/*** Autogenerated by WIDL 10.12 from include/dwrite_1.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dwrite_1_h__
#define __dwrite_1_h__

/* Forward declarations */

#ifndef __IDWriteFactory1_FWD_DEFINED__
#define __IDWriteFactory1_FWD_DEFINED__
typedef interface IDWriteFactory1 IDWriteFactory1;
#ifdef __cplusplus
interface IDWriteFactory1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFace1_FWD_DEFINED__
#define __IDWriteFontFace1_FWD_DEFINED__
typedef interface IDWriteFontFace1 IDWriteFontFace1;
#ifdef __cplusplus
interface IDWriteFontFace1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFont1_FWD_DEFINED__
#define __IDWriteFont1_FWD_DEFINED__
typedef interface IDWriteFont1 IDWriteFont1;
#ifdef __cplusplus
interface IDWriteFont1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteRenderingParams1_FWD_DEFINED__
#define __IDWriteRenderingParams1_FWD_DEFINED__
typedef interface IDWriteRenderingParams1 IDWriteRenderingParams1;
#ifdef __cplusplus
interface IDWriteRenderingParams1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalyzer1_FWD_DEFINED__
#define __IDWriteTextAnalyzer1_FWD_DEFINED__
typedef interface IDWriteTextAnalyzer1 IDWriteTextAnalyzer1;
#ifdef __cplusplus
interface IDWriteTextAnalyzer1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalysisSource1_FWD_DEFINED__
#define __IDWriteTextAnalysisSource1_FWD_DEFINED__
typedef interface IDWriteTextAnalysisSource1 IDWriteTextAnalysisSource1;
#ifdef __cplusplus
interface IDWriteTextAnalysisSource1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalysisSink1_FWD_DEFINED__
#define __IDWriteTextAnalysisSink1_FWD_DEFINED__
typedef interface IDWriteTextAnalysisSink1 IDWriteTextAnalysisSink1;
#ifdef __cplusplus
interface IDWriteTextAnalysisSink1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextLayout1_FWD_DEFINED__
#define __IDWriteTextLayout1_FWD_DEFINED__
typedef interface IDWriteTextLayout1 IDWriteTextLayout1;
#ifdef __cplusplus
interface IDWriteTextLayout1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteBitmapRenderTarget1_FWD_DEFINED__
#define __IDWriteBitmapRenderTarget1_FWD_DEFINED__
typedef interface IDWriteBitmapRenderTarget1 IDWriteBitmapRenderTarget1;
#ifdef __cplusplus
interface IDWriteBitmapRenderTarget1;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dwrite.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum DWRITE_PANOSE_FAMILY {
    DWRITE_PANOSE_FAMILY_ANY = 0,
    DWRITE_PANOSE_FAMILY_NO_FIT = 1,
    DWRITE_PANOSE_FAMILY_TEXT_DISPLAY = 2,
    DWRITE_PANOSE_FAMILY_SCRIPT = 3,
    DWRITE_PANOSE_FAMILY_DECORATIVE = 4,
    DWRITE_PANOSE_FAMILY_SYMBOL = 5,
    DWRITE_PANOSE_FAMILY_PICTORIAL = DWRITE_PANOSE_FAMILY_SYMBOL
} DWRITE_PANOSE_FAMILY;
typedef enum DWRITE_PANOSE_SERIF_STYLE {
    DWRITE_PANOSE_SERIF_STYLE_ANY = 0,
    DWRITE_PANOSE_SERIF_STYLE_NO_FIT = 1,
    DWRITE_PANOSE_SERIF_STYLE_COVE = 2,
    DWRITE_PANOSE_SERIF_STYLE_OBTUSE_COVE = 3,
    DWRITE_PANOSE_SERIF_STYLE_SQUARE_COVE = 4,
    DWRITE_PANOSE_SERIF_STYLE_OBTUSE_SQUARE_COVE = 5,
    DWRITE_PANOSE_SERIF_STYLE_SQUARE = 6,
    DWRITE_PANOSE_SERIF_STYLE_THIN = 7,
    DWRITE_PANOSE_SERIF_STYLE_OVAL = 8,
    DWRITE_PANOSE_SERIF_STYLE_EXAGGERATED = 9,
    DWRITE_PANOSE_SERIF_STYLE_TRIANGLE = 10,
    DWRITE_PANOSE_SERIF_STYLE_NORMAL_SANS = 11,
    DWRITE_PANOSE_SERIF_STYLE_OBTUSE_SANS = 12,
    DWRITE_PANOSE_SERIF_STYLE_PERPENDICULAR_SANS = 13,
    DWRITE_PANOSE_SERIF_STYLE_FLARED = 14,
    DWRITE_PANOSE_SERIF_STYLE_ROUNDED = 15,
    DWRITE_PANOSE_SERIF_STYLE_SCRIPT = 16,
    DWRITE_PANOSE_SERIF_STYLE_PERP_SANS = DWRITE_PANOSE_SERIF_STYLE_PERPENDICULAR_SANS,
    DWRITE_PANOSE_SERIF_STYLE_BONE = DWRITE_PANOSE_SERIF_STYLE_OVAL
} DWRITE_PANOSE_SERIF_STYLE;
typedef enum DWRITE_PANOSE_WEIGHT {
    DWRITE_PANOSE_WEIGHT_ANY = 0,
    DWRITE_PANOSE_WEIGHT_NO_FIT = 1,
    DWRITE_PANOSE_WEIGHT_VERY_LIGHT = 2,
    DWRITE_PANOSE_WEIGHT_LIGHT = 3,
    DWRITE_PANOSE_WEIGHT_THIN = 4,
    DWRITE_PANOSE_WEIGHT_BOOK = 5,
    DWRITE_PANOSE_WEIGHT_MEDIUM = 6,
    DWRITE_PANOSE_WEIGHT_DEMI = 7,
    DWRITE_PANOSE_WEIGHT_BOLD = 8,
    DWRITE_PANOSE_WEIGHT_HEAVY = 9,
    DWRITE_PANOSE_WEIGHT_BLACK = 10,
    DWRITE_PANOSE_WEIGHT_EXTRA_BLACK = 11,
    DWRITE_PANOSE_WEIGHT_NORD = DWRITE_PANOSE_WEIGHT_EXTRA_BLACK
} DWRITE_PANOSE_WEIGHT;
typedef enum DWRITE_PANOSE_PROPORTION {
    DWRITE_PANOSE_PROPORTION_ANY = 0,
    DWRITE_PANOSE_PROPORTION_NO_FIT = 1,
    DWRITE_PANOSE_PROPORTION_OLD_STYLE = 2,
    DWRITE_PANOSE_PROPORTION_MODERN = 3,
    DWRITE_PANOSE_PROPORTION_EVEN_WIDTH = 4,
    DWRITE_PANOSE_PROPORTION_EXPANDED = 5,
    DWRITE_PANOSE_PROPORTION_CONDENSED = 6,
    DWRITE_PANOSE_PROPORTION_VERY_EXPANDED = 7,
    DWRITE_PANOSE_PROPORTION_VERY_CONDENSED = 8,
    DWRITE_PANOSE_PROPORTION_MONOSPACED = 9
} DWRITE_PANOSE_PROPORTION;
typedef enum DWRITE_PANOSE_CONTRAST {
    DWRITE_PANOSE_CONTRAST_ANY = 0,
    DWRITE_PANOSE_CONTRAST_NO_FIT = 1,
    DWRITE_PANOSE_CONTRAST_NONE = 2,
    DWRITE_PANOSE_CONTRAST_VERY_LOW = 3,
    DWRITE_PANOSE_CONTRAST_LOW = 4,
    DWRITE_PANOSE_CONTRAST_MEDIUM_LOW = 5,
    DWRITE_PANOSE_CONTRAST_MEDIUM = 6,
    DWRITE_PANOSE_CONTRAST_MEDIUM_HIGH = 7,
    DWRITE_PANOSE_CONTRAST_HIGH = 8,
    DWRITE_PANOSE_CONTRAST_VERY_HIGH = 9,
    DWRITE_PANOSE_CONTRAST_HORIZONTAL_LOW = 10,
    DWRITE_PANOSE_CONTRAST_HORIZONTAL_MEDIUM = 11,
    DWRITE_PANOSE_CONTRAST_HORIZONTAL_HIGH = 12,
    DWRITE_PANOSE_CONTRAST_BROKEN = 13
} DWRITE_PANOSE_CONTRAST;
typedef enum DWRITE_PANOSE_STROKE_VARIATION {
    DWRITE_PANOSE_STROKE_VARIATION_ANY = 0,
    DWRITE_PANOSE_STROKE_VARIATION_NO_FIT = 1,
    DWRITE_PANOSE_STROKE_VARIATION_NO_VARIATION = 2,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_DIAGONAL = 3,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_TRANSITIONAL = 4,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_VERTICAL = 5,
    DWRITE_PANOSE_STROKE_VARIATION_GRADUAL_HORIZONTAL = 6,
    DWRITE_PANOSE_STROKE_VARIATION_RAPID_VERTICAL = 7,
    DWRITE_PANOSE_STROKE_VARIATION_RAPID_HORIZONTAL = 8,
    DWRITE_PANOSE_STROKE_VARIATION_INSTANT_VERTICAL = 9,
    DWRITE_PANOSE_STROKE_VARIATION_INSTANT_HORIZONTAL = 10
} DWRITE_PANOSE_STROKE_VARIATION;
typedef enum DWRITE_PANOSE_ARM_STYLE {
    DWRITE_PANOSE_ARM_STYLE_ANY = 0,
    DWRITE_PANOSE_ARM_STYLE_NO_FIT = 1,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_HORIZONTAL = 2,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_WEDGE = 3,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_VERTICAL = 4,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_SINGLE_SERIF = 5,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_DOUBLE_SERIF = 6,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_HORIZONTAL = 7,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_WEDGE = 8,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_VERTICAL = 9,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_SINGLE_SERIF = 10,
    DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_DOUBLE_SERIF = 11,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_HORZ = DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_HORIZONTAL,
    DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_VERT = DWRITE_PANOSE_ARM_STYLE_STRAIGHT_ARMS_VERTICAL,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_HORZ = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_HORIZONTAL,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_WEDGE = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_WEDGE,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_VERT = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_VERTICAL,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_SINGLE_SERIF = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_SINGLE_SERIF,
    DWRITE_PANOSE_ARM_STYLE_BENT_ARMS_DOUBLE_SERIF = DWRITE_PANOSE_ARM_STYLE_NONSTRAIGHT_ARMS_DOUBLE_SERIF
} DWRITE_PANOSE_ARM_STYLE;
typedef enum DWRITE_PANOSE_LETTERFORM {
    DWRITE_PANOSE_LETTERFORM_ANY = 0,
    DWRITE_PANOSE_LETTERFORM_NO_FIT = 1,
    DWRITE_PANOSE_LETTERFORM_NORMAL_CONTACT = 2,
    DWRITE_PANOSE_LETTERFORM_NORMAL_WEIGHTED = 3,
    DWRITE_PANOSE_LETTERFORM_NORMAL_BOXED = 4,
    DWRITE_PANOSE_LETTERFORM_NORMAL_FLATTENED = 5,
    DWRITE_PANOSE_LETTERFORM_NORMAL_ROUNDED = 6,
    DWRITE_PANOSE_LETTERFORM_NORMAL_OFF_CENTER = 7,
    DWRITE_PANOSE_LETTERFORM_NORMAL_SQUARE = 8,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_CONTACT = 9,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_WEIGHTED = 10,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_BOXED = 11,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_FLATTENED = 12,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_ROUNDED = 13,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_OFF_CENTER = 14,
    DWRITE_PANOSE_LETTERFORM_OBLIQUE_SQUARE = 15
} DWRITE_PANOSE_LETTERFORM;
typedef enum DWRITE_PANOSE_MIDLINE {
    DWRITE_PANOSE_MIDLINE_ANY = 0,
    DWRITE_PANOSE_MIDLINE_NO_FIT = 1,
    DWRITE_PANOSE_MIDLINE_STANDARD_TRIMMED = 2,
    DWRITE_PANOSE_MIDLINE_STANDARD_POINTED = 3,
    DWRITE_PANOSE_MIDLINE_STANDARD_SERIFED = 4,
    DWRITE_PANOSE_MIDLINE_HIGH_TRIMMED = 5,
    DWRITE_PANOSE_MIDLINE_HIGH_POINTED = 6,
    DWRITE_PANOSE_MIDLINE_HIGH_SERIFED = 7,
    DWRITE_PANOSE_MIDLINE_CONSTANT_TRIMMED = 8,
    DWRITE_PANOSE_MIDLINE_CONSTANT_POINTED = 9,
    DWRITE_PANOSE_MIDLINE_CONSTANT_SERIFED = 10,
    DWRITE_PANOSE_MIDLINE_LOW_TRIMMED = 11,
    DWRITE_PANOSE_MIDLINE_LOW_POINTED = 12,
    DWRITE_PANOSE_MIDLINE_LOW_SERIFED = 13
} DWRITE_PANOSE_MIDLINE;
typedef enum DWRITE_PANOSE_XHEIGHT {
    DWRITE_PANOSE_XHEIGHT_ANY = 0,
    DWRITE_PANOSE_XHEIGHT_NO_FIT = 1,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_SMALL = 2,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_STANDARD = 3,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_LARGE = 4,
    DWRITE_PANOSE_XHEIGHT_DUCKING_SMALL = 5,
    DWRITE_PANOSE_XHEIGHT_DUCKING_STANDARD = 6,
    DWRITE_PANOSE_XHEIGHT_DUCKING_LARGE = 7,
    DWRITE_PANOSE_XHEIGHT_CONSTANT_STD = DWRITE_PANOSE_XHEIGHT_CONSTANT_STANDARD,
    DWRITE_PANOSE_XHEIGHT_DUCKING_STD = DWRITE_PANOSE_XHEIGHT_DUCKING_STANDARD
} DWRITE_PANOSE_XHEIGHT;
typedef enum DWRITE_PANOSE_TOOL_KIND {
    DWRITE_PANOSE_TOOL_KIND_ANY = 0,
    DWRITE_PANOSE_TOOL_KIND_NO_FIT = 1,
    DWRITE_PANOSE_TOOL_KIND_FLAT_NIB = 2,
    DWRITE_PANOSE_TOOL_KIND_PRESSURE_POINT = 3,
    DWRITE_PANOSE_TOOL_KIND_ENGRAVED = 4,
    DWRITE_PANOSE_TOOL_KIND_BALL = 5,
    DWRITE_PANOSE_TOOL_KIND_BRUSH = 6,
    DWRITE_PANOSE_TOOL_KIND_ROUGH = 7,
    DWRITE_PANOSE_TOOL_KIND_FELT_PEN_BRUSH_TIP = 8,
    DWRITE_PANOSE_TOOL_KIND_WILD_BRUSH = 9
} DWRITE_PANOSE_TOOL_KIND;
typedef enum DWRITE_PANOSE_SPACING {
    DWRITE_PANOSE_SPACING_ANY = 0,
    DWRITE_PANOSE_SPACING_NO_FIT = 1,
    DWRITE_PANOSE_SPACING_PROPORTIONAL_SPACED = 2,
    DWRITE_PANOSE_SPACING_MONOSPACED = 3
} DWRITE_PANOSE_SPACING;
typedef enum DWRITE_PANOSE_ASPECT_RATIO {
    DWRITE_PANOSE_ASPECT_RATIO_ANY = 0,
    DWRITE_PANOSE_ASPECT_RATIO_NO_FIT = 1,
    DWRITE_PANOSE_ASPECT_RATIO_VERY_CONDENSED = 2,
    DWRITE_PANOSE_ASPECT_RATIO_CONDENSED = 3,
    DWRITE_PANOSE_ASPECT_RATIO_NORMAL = 4,
    DWRITE_PANOSE_ASPECT_RATIO_EXPANDED = 5,
    DWRITE_PANOSE_ASPECT_RATIO_VERY_EXPANDED = 6
} DWRITE_PANOSE_ASPECT_RATIO;
typedef enum DWRITE_PANOSE_SCRIPT_TOPOLOGY {
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ANY = 0,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_NO_FIT = 1,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ROMAN_DISCONNECTED = 2,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ROMAN_TRAILING = 3,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_ROMAN_CONNECTED = 4,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_CURSIVE_DISCONNECTED = 5,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_CURSIVE_TRAILING = 6,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_CURSIVE_CONNECTED = 7,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_BLACKLETTER_DISCONNECTED = 8,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_BLACKLETTER_TRAILING = 9,
    DWRITE_PANOSE_SCRIPT_TOPOLOGY_BLACKLETTER_CONNECTED = 10
} DWRITE_PANOSE_SCRIPT_TOPOLOGY;
typedef enum DWRITE_PANOSE_SCRIPT_FORM {
    DWRITE_PANOSE_SCRIPT_FORM_ANY = 0,
    DWRITE_PANOSE_SCRIPT_FORM_NO_FIT = 1,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_NO_WRAPPING = 2,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_SOME_WRAPPING = 3,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_MORE_WRAPPING = 4,
    DWRITE_PANOSE_SCRIPT_FORM_UPRIGHT_EXTREME_WRAPPING = 5,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_NO_WRAPPING = 6,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_SOME_WRAPPING = 7,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_MORE_WRAPPING = 8,
    DWRITE_PANOSE_SCRIPT_FORM_OBLIQUE_EXTREME_WRAPPING = 9,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_NO_WRAPPING = 10,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_SOME_WRAPPING = 11,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_MORE_WRAPPING = 12,
    DWRITE_PANOSE_SCRIPT_FORM_EXAGGERATED_EXTREME_WRAPPING = 13
} DWRITE_PANOSE_SCRIPT_FORM;
typedef enum DWRITE_PANOSE_FINIALS {
    DWRITE_PANOSE_FINIALS_ANY = 0,
    DWRITE_PANOSE_FINIALS_NO_FIT = 1,
    DWRITE_PANOSE_FINIALS_NONE_NO_LOOPS = 2,
    DWRITE_PANOSE_FINIALS_NONE_CLOSED_LOOPS = 3,
    DWRITE_PANOSE_FINIALS_NONE_OPEN_LOOPS = 4,
    DWRITE_PANOSE_FINIALS_SHARP_NO_LOOPS = 5,
    DWRITE_PANOSE_FINIALS_SHARP_CLOSED_LOOPS = 6,
    DWRITE_PANOSE_FINIALS_SHARP_OPEN_LOOPS = 7,
    DWRITE_PANOSE_FINIALS_TAPERED_NO_LOOPS = 8,
    DWRITE_PANOSE_FINIALS_TAPERED_CLOSED_LOOPS = 9,
    DWRITE_PANOSE_FINIALS_TAPERED_OPEN_LOOPS = 10,
    DWRITE_PANOSE_FINIALS_ROUND_NO_LOOPS = 11,
    DWRITE_PANOSE_FINIALS_ROUND_CLOSED_LOOPS = 12,
    DWRITE_PANOSE_FINIALS_ROUND_OPEN_LOOPS = 13
} DWRITE_PANOSE_FINIALS;
typedef enum DWRITE_PANOSE_XASCENT {
    DWRITE_PANOSE_XASCENT_ANY = 0,
    DWRITE_PANOSE_XASCENT_NO_FIT = 1,
    DWRITE_PANOSE_XASCENT_VERY_LOW = 2,
    DWRITE_PANOSE_XASCENT_LOW = 3,
    DWRITE_PANOSE_XASCENT_MEDIUM = 4,
    DWRITE_PANOSE_XASCENT_HIGH = 5,
    DWRITE_PANOSE_XASCENT_VERY_HIGH = 6
} DWRITE_PANOSE_XASCENT;
typedef enum DWRITE_PANOSE_DECORATIVE_CLASS {
    DWRITE_PANOSE_DECORATIVE_CLASS_ANY = 0,
    DWRITE_PANOSE_DECORATIVE_CLASS_NO_FIT = 1,
    DWRITE_PANOSE_DECORATIVE_CLASS_DERIVATIVE = 2,
    DWRITE_PANOSE_DECORATIVE_CLASS_NONSTANDARD_TOPOLOGY = 3,
    DWRITE_PANOSE_DECORATIVE_CLASS_NONSTANDARD_ELEMENTS = 4,
    DWRITE_PANOSE_DECORATIVE_CLASS_NONSTANDARD_ASPECT = 5,
    DWRITE_PANOSE_DECORATIVE_CLASS_INITIALS = 6,
    DWRITE_PANOSE_DECORATIVE_CLASS_CARTOON = 7,
    DWRITE_PANOSE_DECORATIVE_CLASS_PICTURE_STEMS = 8,
    DWRITE_PANOSE_DECORATIVE_CLASS_ORNAMENTED = 9,
    DWRITE_PANOSE_DECORATIVE_CLASS_TEXT_AND_BACKGROUND = 10,
    DWRITE_PANOSE_DECORATIVE_CLASS_COLLAGE = 11,
    DWRITE_PANOSE_DECORATIVE_CLASS_MONTAGE = 12
} DWRITE_PANOSE_DECORATIVE_CLASS;
typedef enum DWRITE_PANOSE_ASPECT {
    DWRITE_PANOSE_ASPECT_ANY = 0,
    DWRITE_PANOSE_ASPECT_NO_FIT = 1,
    DWRITE_PANOSE_ASPECT_SUPER_CONDENSED = 2,
    DWRITE_PANOSE_ASPECT_VERY_CONDENSED = 3,
    DWRITE_PANOSE_ASPECT_CONDENSED = 4,
    DWRITE_PANOSE_ASPECT_NORMAL = 5,
    DWRITE_PANOSE_ASPECT_EXTENDED = 6,
    DWRITE_PANOSE_ASPECT_VERY_EXTENDED = 7,
    DWRITE_PANOSE_ASPECT_SUPER_EXTENDED = 8,
    DWRITE_PANOSE_ASPECT_MONOSPACED = 9
} DWRITE_PANOSE_ASPECT;
typedef enum DWRITE_PANOSE_FILL {
    DWRITE_PANOSE_FILL_ANY = 0,
    DWRITE_PANOSE_FILL_NO_FIT = 1,
    DWRITE_PANOSE_FILL_STANDARD_SOLID_FILL = 2,
    DWRITE_PANOSE_FILL_NO_FILL = 3,
    DWRITE_PANOSE_FILL_PATTERNED_FILL = 4,
    DWRITE_PANOSE_FILL_COMPLEX_FILL = 5,
    DWRITE_PANOSE_FILL_SHAPED_FILL = 6,
    DWRITE_PANOSE_FILL_DRAWN_DISTRESSED = 7
} DWRITE_PANOSE_FILL;
typedef enum DWRITE_PANOSE_LINING {
    DWRITE_PANOSE_LINING_ANY = 0,
    DWRITE_PANOSE_LINING_NO_FIT = 1,
    DWRITE_PANOSE_LINING_NONE = 2,
    DWRITE_PANOSE_LINING_INLINE = 3,
    DWRITE_PANOSE_LINING_OUTLINE = 4,
    DWRITE_PANOSE_LINING_ENGRAVED = 5,
    DWRITE_PANOSE_LINING_SHADOW = 6,
    DWRITE_PANOSE_LINING_RELIEF = 7,
    DWRITE_PANOSE_LINING_BACKDROP = 8
} DWRITE_PANOSE_LINING;
typedef enum DWRITE_PANOSE_DECORATIVE_TOPOLOGY {
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_ANY = 0,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_NO_FIT = 1,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_STANDARD = 2,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_SQUARE = 3,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_MULTIPLE_SEGMENT = 4,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_ART_DECO = 5,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_UNEVEN_WEIGHTING = 6,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_DIVERSE_ARMS = 7,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_DIVERSE_FORMS = 8,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_LOMBARDIC_FORMS = 9,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_UPPER_CASE_IN_LOWER_CASE = 10,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_IMPLIED_TOPOLOGY = 11,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_HORSESHOE_E_AND_A = 12,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_CURSIVE = 13,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_BLACKLETTER = 14,
    DWRITE_PANOSE_DECORATIVE_TOPOLOGY_SWASH_VARIANCE = 15
} DWRITE_PANOSE_DECORATIVE_TOPOLOGY;
typedef enum DWRITE_PANOSE_CHARACTER_RANGES {
    DWRITE_PANOSE_CHARACTER_RANGES_ANY = 0,
    DWRITE_PANOSE_CHARACTER_RANGES_NO_FIT = 1,
    DWRITE_PANOSE_CHARACTER_RANGES_EXTENDED_COLLECTION = 2,
    DWRITE_PANOSE_CHARACTER_RANGES_LITERALS = 3,
    DWRITE_PANOSE_CHARACTER_RANGES_NO_LOWER_CASE = 4,
    DWRITE_PANOSE_CHARACTER_RANGES_SMALL_CAPS = 5
} DWRITE_PANOSE_CHARACTER_RANGES;
typedef enum DWRITE_PANOSE_SYMBOL_KIND {
    DWRITE_PANOSE_SYMBOL_KIND_ANY = 0,
    DWRITE_PANOSE_SYMBOL_KIND_NO_FIT = 1,
    DWRITE_PANOSE_SYMBOL_KIND_MONTAGES = 2,
    DWRITE_PANOSE_SYMBOL_KIND_PICTURES = 3,
    DWRITE_PANOSE_SYMBOL_KIND_SHAPES = 4,
    DWRITE_PANOSE_SYMBOL_KIND_SCIENTIFIC = 5,
    DWRITE_PANOSE_SYMBOL_KIND_MUSIC = 6,
    DWRITE_PANOSE_SYMBOL_KIND_EXPERT = 7,
    DWRITE_PANOSE_SYMBOL_KIND_PATTERNS = 8,
    DWRITE_PANOSE_SYMBOL_KIND_BOARDERS = 9,
    DWRITE_PANOSE_SYMBOL_KIND_ICONS = 10,
    DWRITE_PANOSE_SYMBOL_KIND_LOGOS = 11,
    DWRITE_PANOSE_SYMBOL_KIND_INDUSTRY_SPECIFIC = 12
} DWRITE_PANOSE_SYMBOL_KIND;
typedef enum DWRITE_PANOSE_SYMBOL_ASPECT_RATIO {
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_ANY = 0,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NO_FIT = 1,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NO_WIDTH = 2,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_EXCEPTIONALLY_WIDE = 3,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_SUPER_WIDE = 4,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_VERY_WIDE = 5,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_WIDE = 6,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NORMAL = 7,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_NARROW = 8,
    DWRITE_PANOSE_SYMBOL_ASPECT_RATIO_VERY_NARROW = 9
} DWRITE_PANOSE_SYMBOL_ASPECT_RATIO;
typedef enum DWRITE_OUTLINE_THRESHOLD {
    DWRITE_OUTLINE_THRESHOLD_ANTIALIASED = 0,
    DWRITE_OUTLINE_THRESHOLD_ALIASED = 1
} DWRITE_OUTLINE_THRESHOLD;
typedef enum DWRITE_BASELINE {
    DWRITE_BASELINE_DEFAULT = 0,
    DWRITE_BASELINE_ROMAN = 1,
    DWRITE_BASELINE_CENTRAL = 2,
    DWRITE_BASELINE_MATH = 3,
    DWRITE_BASELINE_HANGING = 4,
    DWRITE_BASELINE_IDEOGRAPHIC_BOTTOM = 5,
    DWRITE_BASELINE_IDEOGRAPHIC_TOP = 6,
    DWRITE_BASELINE_MINIMUM = 7,
    DWRITE_BASELINE_MAXIMUM = 8
} DWRITE_BASELINE;
typedef enum DWRITE_VERTICAL_GLYPH_ORIENTATION {
    DWRITE_VERTICAL_GLYPH_ORIENTATION_DEFAULT = 0,
    DWRITE_VERTICAL_GLYPH_ORIENTATION_STACKED = 1
} DWRITE_VERTICAL_GLYPH_ORIENTATION;
typedef enum DWRITE_GLYPH_ORIENTATION_ANGLE {
    DWRITE_GLYPH_ORIENTATION_ANGLE_0_DEGREES = 0,
    DWRITE_GLYPH_ORIENTATION_ANGLE_90_DEGREES = 1,
    DWRITE_GLYPH_ORIENTATION_ANGLE_180_DEGREES = 2,
    DWRITE_GLYPH_ORIENTATION_ANGLE_270_DEGREES = 3
} DWRITE_GLYPH_ORIENTATION_ANGLE;
typedef struct DWRITE_FONT_METRICS1 {
    UINT16 designUnitsPerEm;
    UINT16 ascent;
    UINT16 descent;
    INT16 lineGap;
    UINT16 capHeight;
    UINT16 xHeight;
    INT16 underlinePosition;
    UINT16 underlineThickness;
    INT16 strikethroughPosition;
    UINT16 strikethroughThickness;
    INT16 glyphBoxLeft;
    INT16 glyphBoxTop;
    INT16 glyphBoxRight;
    INT16 glyphBoxBottom;
    INT16 subscriptPositionX;
    INT16 subscriptPositionY;
    INT16 subscriptSizeX;
    INT16 subscriptSizeY;
    INT16 superscriptPositionX;
    INT16 superscriptPositionY;
    INT16 superscriptSizeX;
    INT16 superscriptSizeY;
    WINBOOL hasTypographicMetrics;
} DWRITE_FONT_METRICS1;
typedef struct DWRITE_CARET_METRICS {
    INT16 slopeRise;
    INT16 slopeRun;
    INT16 offset;
} DWRITE_CARET_METRICS;
typedef union DWRITE_PANOSE {
    UINT8 values[10];
    UINT8 familyKind;
    struct {
        UINT8 familyKind;
        UINT8 serifStyle;
        UINT8 weight;
        UINT8 proportion;
        UINT8 contrast;
        UINT8 strokeVariation;
        UINT8 armStyle;
        UINT8 letterform;
        UINT8 midline;
        UINT8 xHeight;
    } text;
    struct {
        UINT8 familyKind;
        UINT8 toolKind;
        UINT8 weight;
        UINT8 spacing;
        UINT8 aspectRatio;
        UINT8 contrast;
        UINT8 scriptTopology;
        UINT8 scriptForm;
        UINT8 finials;
        UINT8 xAscent;
    } script;
    struct {
        UINT8 familyKind;
        UINT8 decorativeClass;
        UINT8 weight;
        UINT8 aspect;
        UINT8 contrast;
        UINT8 serifVariant;
        UINT8 fill;
        UINT8 lining;
        UINT8 decorativeTopology;
        UINT8 characterRange;
    } decorative;
    struct {
        UINT8 familyKind;
        UINT8 symbolKind;
        UINT8 weight;
        UINT8 spacing;
        UINT8 aspectRatioAndContrast;
        UINT8 aspectRatio94;
        UINT8 aspectRatio119;
        UINT8 aspectRatio157;
        UINT8 aspectRatio163;
        UINT8 aspectRatio211;
    } symbol;
} DWRITE_PANOSE;
typedef struct DWRITE_UNICODE_RANGE {
    UINT32 first;
    UINT32 last;
} DWRITE_UNICODE_RANGE;
typedef struct DWRITE_SCRIPT_PROPERTIES {
    UINT32 isoScriptCode;
    UINT32 isoScriptNumber;
    UINT32 clusterLookahead;
    UINT32 justificationCharacter;
    UINT32 restrictCaretToClusters : 1;
    UINT32 usesWordDividers : 1;
    UINT32 isDiscreteWriting : 1;
    UINT32 isBlockWriting : 1;
    UINT32 isDistributedWithinCluster : 1;
    UINT32 isConnectedWriting : 1;
    UINT32 isCursiveWriting : 1;
    UINT32 reserved : 25;
} DWRITE_SCRIPT_PROPERTIES;
typedef struct DWRITE_JUSTIFICATION_OPPORTUNITY {
    FLOAT expansionMinimum;
    FLOAT expansionMaximum;
    FLOAT compressionMaximum;
    UINT32 expansionPriority : 8;
    UINT32 compressionPriority : 8;
    UINT32 allowResidualExpansion : 1;
    UINT32 allowResidualCompression : 1;
    UINT32 applyToLeadingEdge : 1;
    UINT32 applyToTrailingEdge : 1;
    UINT32 reserved : 12;
} DWRITE_JUSTIFICATION_OPPORTUNITY;
#ifndef __IDWriteTextAnalysisSource1_FWD_DEFINED__
#define __IDWriteTextAnalysisSource1_FWD_DEFINED__
typedef interface IDWriteTextAnalysisSource1 IDWriteTextAnalysisSource1;
#ifdef __cplusplus
interface IDWriteTextAnalysisSource1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalysisSink1_FWD_DEFINED__
#define __IDWriteTextAnalysisSink1_FWD_DEFINED__
typedef interface IDWriteTextAnalysisSink1 IDWriteTextAnalysisSink1;
#ifdef __cplusplus
interface IDWriteTextAnalysisSink1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteRenderingParams1_FWD_DEFINED__
#define __IDWriteRenderingParams1_FWD_DEFINED__
typedef interface IDWriteRenderingParams1 IDWriteRenderingParams1;
#ifdef __cplusplus
interface IDWriteRenderingParams1;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IDWriteFactory1 interface
 */
#ifndef __IDWriteFactory1_INTERFACE_DEFINED__
#define __IDWriteFactory1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFactory1, 0x30572f99, 0xdac6, 0x41db, 0xa1,0x6e, 0x04,0x86,0x30,0x7e,0x60,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("30572f99-dac6-41db-a16e-0486307e606a")
IDWriteFactory1 : public IDWriteFactory
{
    virtual HRESULT STDMETHODCALLTYPE GetEudcFontCollection(
        IDWriteFontCollection **collection,
        WINBOOL check_for_updates = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCustomRenderingParams(
        FLOAT gamma,
        FLOAT enhcontrast,
        FLOAT enhcontrast_grayscale,
        FLOAT cleartype_level,
        DWRITE_PIXEL_GEOMETRY geometry,
        DWRITE_RENDERING_MODE mode,
        IDWriteRenderingParams1 **params) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFactory1, 0x30572f99, 0xdac6, 0x41db, 0xa1,0x6e, 0x04,0x86,0x30,0x7e,0x60,0x6a)
#endif
#else
typedef struct IDWriteFactory1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFactory1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFactory1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFactory1 *This);

    /*** IDWriteFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemFontCollection)(
        IDWriteFactory1 *This,
        IDWriteFontCollection **collection,
        WINBOOL check_for_updates);

    HRESULT (STDMETHODCALLTYPE *CreateCustomFontCollection)(
        IDWriteFactory1 *This,
        IDWriteFontCollectionLoader *loader,
        const void *key,
        UINT32 key_size,
        IDWriteFontCollection **collection);

    HRESULT (STDMETHODCALLTYPE *RegisterFontCollectionLoader)(
        IDWriteFactory1 *This,
        IDWriteFontCollectionLoader *loader);

    HRESULT (STDMETHODCALLTYPE *UnregisterFontCollectionLoader)(
        IDWriteFactory1 *This,
        IDWriteFontCollectionLoader *loader);

    HRESULT (STDMETHODCALLTYPE *CreateFontFileReference)(
        IDWriteFactory1 *This,
        const WCHAR *path,
        const FILETIME *writetime,
        IDWriteFontFile **font_file);

    HRESULT (STDMETHODCALLTYPE *CreateCustomFontFileReference)(
        IDWriteFactory1 *This,
        const void *reference_key,
        UINT32 key_size,
        IDWriteFontFileLoader *loader,
        IDWriteFontFile **font_file);

    HRESULT (STDMETHODCALLTYPE *CreateFontFace)(
        IDWriteFactory1 *This,
        DWRITE_FONT_FACE_TYPE facetype,
        UINT32 files_number,
        IDWriteFontFile *const *font_files,
        UINT32 index,
        DWRITE_FONT_SIMULATIONS sim_flags,
        IDWriteFontFace **font_face);

    HRESULT (STDMETHODCALLTYPE *CreateRenderingParams)(
        IDWriteFactory1 *This,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *CreateMonitorRenderingParams)(
        IDWriteFactory1 *This,
        HMONITOR monitor,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *CreateCustomRenderingParams)(
        IDWriteFactory1 *This,
        FLOAT gamma,
        FLOAT enhancedContrast,
        FLOAT cleartype_level,
        DWRITE_PIXEL_GEOMETRY geometry,
        DWRITE_RENDERING_MODE mode,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *RegisterFontFileLoader)(
        IDWriteFactory1 *This,
        IDWriteFontFileLoader *loader);

    HRESULT (STDMETHODCALLTYPE *UnregisterFontFileLoader)(
        IDWriteFactory1 *This,
        IDWriteFontFileLoader *loader);

    HRESULT (STDMETHODCALLTYPE *CreateTextFormat)(
        IDWriteFactory1 *This,
        const WCHAR *family_name,
        IDWriteFontCollection *collection,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STYLE style,
        DWRITE_FONT_STRETCH stretch,
        FLOAT size,
        const WCHAR *locale,
        IDWriteTextFormat **format);

    HRESULT (STDMETHODCALLTYPE *CreateTypography)(
        IDWriteFactory1 *This,
        IDWriteTypography **typography);

    HRESULT (STDMETHODCALLTYPE *GetGdiInterop)(
        IDWriteFactory1 *This,
        IDWriteGdiInterop **gdi_interop);

    HRESULT (STDMETHODCALLTYPE *CreateTextLayout)(
        IDWriteFactory1 *This,
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT max_width,
        FLOAT max_height,
        IDWriteTextLayout **layout);

    HRESULT (STDMETHODCALLTYPE *CreateGdiCompatibleTextLayout)(
        IDWriteFactory1 *This,
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT layout_width,
        FLOAT layout_height,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        IDWriteTextLayout **layout);

    HRESULT (STDMETHODCALLTYPE *CreateEllipsisTrimmingSign)(
        IDWriteFactory1 *This,
        IDWriteTextFormat *format,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *CreateTextAnalyzer)(
        IDWriteFactory1 *This,
        IDWriteTextAnalyzer **analyzer);

    HRESULT (STDMETHODCALLTYPE *CreateNumberSubstitution)(
        IDWriteFactory1 *This,
        DWRITE_NUMBER_SUBSTITUTION_METHOD method,
        const WCHAR *locale,
        WINBOOL ignore_user_override,
        IDWriteNumberSubstitution **substitution);

    HRESULT (STDMETHODCALLTYPE *CreateGlyphRunAnalysis)(
        IDWriteFactory1 *This,
        const DWRITE_GLYPH_RUN *glyph_run,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_RENDERING_MODE rendering_mode,
        DWRITE_MEASURING_MODE measuring_mode,
        FLOAT baseline_x,
        FLOAT baseline_y,
        IDWriteGlyphRunAnalysis **analysis);

    /*** IDWriteFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEudcFontCollection)(
        IDWriteFactory1 *This,
        IDWriteFontCollection **collection,
        WINBOOL check_for_updates);

    HRESULT (STDMETHODCALLTYPE *IDWriteFactory1_CreateCustomRenderingParams)(
        IDWriteFactory1 *This,
        FLOAT gamma,
        FLOAT enhcontrast,
        FLOAT enhcontrast_grayscale,
        FLOAT cleartype_level,
        DWRITE_PIXEL_GEOMETRY geometry,
        DWRITE_RENDERING_MODE mode,
        IDWriteRenderingParams1 **params);

    END_INTERFACE
} IDWriteFactory1Vtbl;

interface IDWriteFactory1 {
    CONST_VTBL IDWriteFactory1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFactory1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFactory1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFactory1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFactory methods ***/
#define IDWriteFactory1_GetSystemFontCollection(This,collection,check_for_updates) (This)->lpVtbl->GetSystemFontCollection(This,collection,check_for_updates)
#define IDWriteFactory1_CreateCustomFontCollection(This,loader,key,key_size,collection) (This)->lpVtbl->CreateCustomFontCollection(This,loader,key,key_size,collection)
#define IDWriteFactory1_RegisterFontCollectionLoader(This,loader) (This)->lpVtbl->RegisterFontCollectionLoader(This,loader)
#define IDWriteFactory1_UnregisterFontCollectionLoader(This,loader) (This)->lpVtbl->UnregisterFontCollectionLoader(This,loader)
#define IDWriteFactory1_CreateFontFileReference(This,path,writetime,font_file) (This)->lpVtbl->CreateFontFileReference(This,path,writetime,font_file)
#define IDWriteFactory1_CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file) (This)->lpVtbl->CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file)
#define IDWriteFactory1_CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face) (This)->lpVtbl->CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face)
#define IDWriteFactory1_CreateRenderingParams(This,params) (This)->lpVtbl->CreateRenderingParams(This,params)
#define IDWriteFactory1_CreateMonitorRenderingParams(This,monitor,params) (This)->lpVtbl->CreateMonitorRenderingParams(This,monitor,params)
#define IDWriteFactory1_RegisterFontFileLoader(This,loader) (This)->lpVtbl->RegisterFontFileLoader(This,loader)
#define IDWriteFactory1_UnregisterFontFileLoader(This,loader) (This)->lpVtbl->UnregisterFontFileLoader(This,loader)
#define IDWriteFactory1_CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format) (This)->lpVtbl->CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format)
#define IDWriteFactory1_CreateTypography(This,typography) (This)->lpVtbl->CreateTypography(This,typography)
#define IDWriteFactory1_GetGdiInterop(This,gdi_interop) (This)->lpVtbl->GetGdiInterop(This,gdi_interop)
#define IDWriteFactory1_CreateTextLayout(This,string,len,format,max_width,max_height,layout) (This)->lpVtbl->CreateTextLayout(This,string,len,format,max_width,max_height,layout)
#define IDWriteFactory1_CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout) (This)->lpVtbl->CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout)
#define IDWriteFactory1_CreateEllipsisTrimmingSign(This,format,trimming_sign) (This)->lpVtbl->CreateEllipsisTrimmingSign(This,format,trimming_sign)
#define IDWriteFactory1_CreateTextAnalyzer(This,analyzer) (This)->lpVtbl->CreateTextAnalyzer(This,analyzer)
#define IDWriteFactory1_CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution) (This)->lpVtbl->CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution)
#define IDWriteFactory1_CreateGlyphRunAnalysis(This,glyph_run,pixels_per_dip,transform,rendering_mode,measuring_mode,baseline_x,baseline_y,analysis) (This)->lpVtbl->CreateGlyphRunAnalysis(This,glyph_run,pixels_per_dip,transform,rendering_mode,measuring_mode,baseline_x,baseline_y,analysis)
/*** IDWriteFactory1 methods ***/
#define IDWriteFactory1_GetEudcFontCollection(This,collection,check_for_updates) (This)->lpVtbl->GetEudcFontCollection(This,collection,check_for_updates)
#define IDWriteFactory1_CreateCustomRenderingParams(This,gamma,enhcontrast,enhcontrast_grayscale,cleartype_level,geometry,mode,params) (This)->lpVtbl->IDWriteFactory1_CreateCustomRenderingParams(This,gamma,enhcontrast,enhcontrast_grayscale,cleartype_level,geometry,mode,params)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFactory1_QueryInterface(IDWriteFactory1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFactory1_AddRef(IDWriteFactory1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFactory1_Release(IDWriteFactory1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFactory methods ***/
static inline HRESULT IDWriteFactory1_GetSystemFontCollection(IDWriteFactory1* This,IDWriteFontCollection **collection,WINBOOL check_for_updates) {
    return This->lpVtbl->GetSystemFontCollection(This,collection,check_for_updates);
}
static inline HRESULT IDWriteFactory1_CreateCustomFontCollection(IDWriteFactory1* This,IDWriteFontCollectionLoader *loader,const void *key,UINT32 key_size,IDWriteFontCollection **collection) {
    return This->lpVtbl->CreateCustomFontCollection(This,loader,key,key_size,collection);
}
static inline HRESULT IDWriteFactory1_RegisterFontCollectionLoader(IDWriteFactory1* This,IDWriteFontCollectionLoader *loader) {
    return This->lpVtbl->RegisterFontCollectionLoader(This,loader);
}
static inline HRESULT IDWriteFactory1_UnregisterFontCollectionLoader(IDWriteFactory1* This,IDWriteFontCollectionLoader *loader) {
    return This->lpVtbl->UnregisterFontCollectionLoader(This,loader);
}
static inline HRESULT IDWriteFactory1_CreateFontFileReference(IDWriteFactory1* This,const WCHAR *path,const FILETIME *writetime,IDWriteFontFile **font_file) {
    return This->lpVtbl->CreateFontFileReference(This,path,writetime,font_file);
}
static inline HRESULT IDWriteFactory1_CreateCustomFontFileReference(IDWriteFactory1* This,const void *reference_key,UINT32 key_size,IDWriteFontFileLoader *loader,IDWriteFontFile **font_file) {
    return This->lpVtbl->CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file);
}
static inline HRESULT IDWriteFactory1_CreateFontFace(IDWriteFactory1* This,DWRITE_FONT_FACE_TYPE facetype,UINT32 files_number,IDWriteFontFile *const *font_files,UINT32 index,DWRITE_FONT_SIMULATIONS sim_flags,IDWriteFontFace **font_face) {
    return This->lpVtbl->CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face);
}
static inline HRESULT IDWriteFactory1_CreateRenderingParams(IDWriteFactory1* This,IDWriteRenderingParams **params) {
    return This->lpVtbl->CreateRenderingParams(This,params);
}
static inline HRESULT IDWriteFactory1_CreateMonitorRenderingParams(IDWriteFactory1* This,HMONITOR monitor,IDWriteRenderingParams **params) {
    return This->lpVtbl->CreateMonitorRenderingParams(This,monitor,params);
}
static inline HRESULT IDWriteFactory1_RegisterFontFileLoader(IDWriteFactory1* This,IDWriteFontFileLoader *loader) {
    return This->lpVtbl->RegisterFontFileLoader(This,loader);
}
static inline HRESULT IDWriteFactory1_UnregisterFontFileLoader(IDWriteFactory1* This,IDWriteFontFileLoader *loader) {
    return This->lpVtbl->UnregisterFontFileLoader(This,loader);
}
static inline HRESULT IDWriteFactory1_CreateTextFormat(IDWriteFactory1* This,const WCHAR *family_name,IDWriteFontCollection *collection,DWRITE_FONT_WEIGHT weight,DWRITE_FONT_STYLE style,DWRITE_FONT_STRETCH stretch,FLOAT size,const WCHAR *locale,IDWriteTextFormat **format) {
    return This->lpVtbl->CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format);
}
static inline HRESULT IDWriteFactory1_CreateTypography(IDWriteFactory1* This,IDWriteTypography **typography) {
    return This->lpVtbl->CreateTypography(This,typography);
}
static inline HRESULT IDWriteFactory1_GetGdiInterop(IDWriteFactory1* This,IDWriteGdiInterop **gdi_interop) {
    return This->lpVtbl->GetGdiInterop(This,gdi_interop);
}
static inline HRESULT IDWriteFactory1_CreateTextLayout(IDWriteFactory1* This,const WCHAR *string,UINT32 len,IDWriteTextFormat *format,FLOAT max_width,FLOAT max_height,IDWriteTextLayout **layout) {
    return This->lpVtbl->CreateTextLayout(This,string,len,format,max_width,max_height,layout);
}
static inline HRESULT IDWriteFactory1_CreateGdiCompatibleTextLayout(IDWriteFactory1* This,const WCHAR *string,UINT32 len,IDWriteTextFormat *format,FLOAT layout_width,FLOAT layout_height,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,IDWriteTextLayout **layout) {
    return This->lpVtbl->CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout);
}
static inline HRESULT IDWriteFactory1_CreateEllipsisTrimmingSign(IDWriteFactory1* This,IDWriteTextFormat *format,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->CreateEllipsisTrimmingSign(This,format,trimming_sign);
}
static inline HRESULT IDWriteFactory1_CreateTextAnalyzer(IDWriteFactory1* This,IDWriteTextAnalyzer **analyzer) {
    return This->lpVtbl->CreateTextAnalyzer(This,analyzer);
}
static inline HRESULT IDWriteFactory1_CreateNumberSubstitution(IDWriteFactory1* This,DWRITE_NUMBER_SUBSTITUTION_METHOD method,const WCHAR *locale,WINBOOL ignore_user_override,IDWriteNumberSubstitution **substitution) {
    return This->lpVtbl->CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution);
}
static inline HRESULT IDWriteFactory1_CreateGlyphRunAnalysis(IDWriteFactory1* This,const DWRITE_GLYPH_RUN *glyph_run,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,DWRITE_RENDERING_MODE rendering_mode,DWRITE_MEASURING_MODE measuring_mode,FLOAT baseline_x,FLOAT baseline_y,IDWriteGlyphRunAnalysis **analysis) {
    return This->lpVtbl->CreateGlyphRunAnalysis(This,glyph_run,pixels_per_dip,transform,rendering_mode,measuring_mode,baseline_x,baseline_y,analysis);
}
/*** IDWriteFactory1 methods ***/
static inline HRESULT IDWriteFactory1_GetEudcFontCollection(IDWriteFactory1* This,IDWriteFontCollection **collection,WINBOOL check_for_updates) {
    return This->lpVtbl->GetEudcFontCollection(This,collection,check_for_updates);
}
static inline HRESULT IDWriteFactory1_CreateCustomRenderingParams(IDWriteFactory1* This,FLOAT gamma,FLOAT enhcontrast,FLOAT enhcontrast_grayscale,FLOAT cleartype_level,DWRITE_PIXEL_GEOMETRY geometry,DWRITE_RENDERING_MODE mode,IDWriteRenderingParams1 **params) {
    return This->lpVtbl->IDWriteFactory1_CreateCustomRenderingParams(This,gamma,enhcontrast,enhcontrast_grayscale,cleartype_level,geometry,mode,params);
}
#endif
#endif

#endif


#endif  /* __IDWriteFactory1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFace1 interface
 */
#ifndef __IDWriteFontFace1_INTERFACE_DEFINED__
#define __IDWriteFontFace1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFace1, 0xa71efdb4, 0x9fdb, 0x4838, 0xad,0x90, 0xcf,0xc3,0xbe,0x8c,0x3d,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a71efdb4-9fdb-4838-ad90-cfc3be8c3daf")
IDWriteFontFace1 : public IDWriteFontFace
{
    virtual void STDMETHODCALLTYPE GetMetrics(
        DWRITE_FONT_METRICS1 *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGdiCompatibleMetrics(
        FLOAT em_size,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_FONT_METRICS1 *metrics) = 0;

    virtual void STDMETHODCALLTYPE GetCaretMetrics(
        DWRITE_CARET_METRICS *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnicodeRanges(
        UINT32 max_count,
        DWRITE_UNICODE_RANGE *ranges,
        UINT32 *count) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsMonospacedFont(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesignGlyphAdvances(
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *advances,
        WINBOOL is_sideways = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGdiCompatibleGlyphAdvances(
        FLOAT em_size,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        WINBOOL is_sideways,
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *advances) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetKerningPairAdjustments(
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *adjustments) = 0;

    virtual WINBOOL STDMETHODCALLTYPE HasKerningPairs(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRecommendedRenderingMode(
        FLOAT font_emsize,
        FLOAT dpiX,
        FLOAT dpiY,
        const DWRITE_MATRIX *transform,
        WINBOOL is_sideways,
        DWRITE_OUTLINE_THRESHOLD threshold,
        DWRITE_MEASURING_MODE measuring_mode,
        DWRITE_RENDERING_MODE *rendering_mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVerticalGlyphVariants(
        UINT32 glyph_count,
        const UINT16 *nominal_indices,
        UINT16 *vertical_indices) = 0;

    virtual WINBOOL STDMETHODCALLTYPE HasVerticalGlyphVariants(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFace1, 0xa71efdb4, 0x9fdb, 0x4838, 0xad,0x90, 0xcf,0xc3,0xbe,0x8c,0x3d,0xaf)
#endif
#else
typedef struct IDWriteFontFace1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFace1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFace1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFace1 *This);

    /*** IDWriteFontFace methods ***/
    DWRITE_FONT_FACE_TYPE (STDMETHODCALLTYPE *GetType)(
        IDWriteFontFace1 *This);

    HRESULT (STDMETHODCALLTYPE *GetFiles)(
        IDWriteFontFace1 *This,
        UINT32 *number_of_files,
        IDWriteFontFile **fontfiles);

    UINT32 (STDMETHODCALLTYPE *GetIndex)(
        IDWriteFontFace1 *This);

    DWRITE_FONT_SIMULATIONS (STDMETHODCALLTYPE *GetSimulations)(
        IDWriteFontFace1 *This);

    WINBOOL (STDMETHODCALLTYPE *IsSymbolFont)(
        IDWriteFontFace1 *This);

    void (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteFontFace1 *This,
        DWRITE_FONT_METRICS *metrics);

    UINT16 (STDMETHODCALLTYPE *GetGlyphCount)(
        IDWriteFontFace1 *This);

    HRESULT (STDMETHODCALLTYPE *GetDesignGlyphMetrics)(
        IDWriteFontFace1 *This,
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways);

    HRESULT (STDMETHODCALLTYPE *GetGlyphIndices)(
        IDWriteFontFace1 *This,
        const UINT32 *codepoints,
        UINT32 count,
        UINT16 *glyph_indices);

    HRESULT (STDMETHODCALLTYPE *TryGetFontTable)(
        IDWriteFontFace1 *This,
        UINT32 table_tag,
        const void **table_data,
        UINT32 *table_size,
        void **context,
        WINBOOL *exists);

    void (STDMETHODCALLTYPE *ReleaseFontTable)(
        IDWriteFontFace1 *This,
        void *table_context);

    HRESULT (STDMETHODCALLTYPE *GetGlyphRunOutline)(
        IDWriteFontFace1 *This,
        FLOAT emSize,
        const UINT16 *glyph_indices,
        const FLOAT *glyph_advances,
        const DWRITE_GLYPH_OFFSET *glyph_offsets,
        UINT32 glyph_count,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IDWriteGeometrySink *geometrysink);

    HRESULT (STDMETHODCALLTYPE *GetRecommendedRenderingMode)(
        IDWriteFontFace1 *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        DWRITE_MEASURING_MODE mode,
        IDWriteRenderingParams *params,
        DWRITE_RENDERING_MODE *rendering_mode);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleMetrics)(
        IDWriteFontFace1 *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_FONT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphMetrics)(
        IDWriteFontFace1 *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways);

    /*** IDWriteFontFace1 methods ***/
    void (STDMETHODCALLTYPE *IDWriteFontFace1_GetMetrics)(
        IDWriteFontFace1 *This,
        DWRITE_FONT_METRICS1 *metrics);

    HRESULT (STDMETHODCALLTYPE *IDWriteFontFace1_GetGdiCompatibleMetrics)(
        IDWriteFontFace1 *This,
        FLOAT em_size,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_FONT_METRICS1 *metrics);

    void (STDMETHODCALLTYPE *GetCaretMetrics)(
        IDWriteFontFace1 *This,
        DWRITE_CARET_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetUnicodeRanges)(
        IDWriteFontFace1 *This,
        UINT32 max_count,
        DWRITE_UNICODE_RANGE *ranges,
        UINT32 *count);

    WINBOOL (STDMETHODCALLTYPE *IsMonospacedFont)(
        IDWriteFontFace1 *This);

    HRESULT (STDMETHODCALLTYPE *GetDesignGlyphAdvances)(
        IDWriteFontFace1 *This,
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *advances,
        WINBOOL is_sideways);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphAdvances)(
        IDWriteFontFace1 *This,
        FLOAT em_size,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        WINBOOL is_sideways,
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *advances);

    HRESULT (STDMETHODCALLTYPE *GetKerningPairAdjustments)(
        IDWriteFontFace1 *This,
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *adjustments);

    WINBOOL (STDMETHODCALLTYPE *HasKerningPairs)(
        IDWriteFontFace1 *This);

    HRESULT (STDMETHODCALLTYPE *IDWriteFontFace1_GetRecommendedRenderingMode)(
        IDWriteFontFace1 *This,
        FLOAT font_emsize,
        FLOAT dpiX,
        FLOAT dpiY,
        const DWRITE_MATRIX *transform,
        WINBOOL is_sideways,
        DWRITE_OUTLINE_THRESHOLD threshold,
        DWRITE_MEASURING_MODE measuring_mode,
        DWRITE_RENDERING_MODE *rendering_mode);

    HRESULT (STDMETHODCALLTYPE *GetVerticalGlyphVariants)(
        IDWriteFontFace1 *This,
        UINT32 glyph_count,
        const UINT16 *nominal_indices,
        UINT16 *vertical_indices);

    WINBOOL (STDMETHODCALLTYPE *HasVerticalGlyphVariants)(
        IDWriteFontFace1 *This);

    END_INTERFACE
} IDWriteFontFace1Vtbl;

interface IDWriteFontFace1 {
    CONST_VTBL IDWriteFontFace1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFace1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFace1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFace1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFace methods ***/
#define IDWriteFontFace1_GetType(This) (This)->lpVtbl->GetType(This)
#define IDWriteFontFace1_GetFiles(This,number_of_files,fontfiles) (This)->lpVtbl->GetFiles(This,number_of_files,fontfiles)
#define IDWriteFontFace1_GetIndex(This) (This)->lpVtbl->GetIndex(This)
#define IDWriteFontFace1_GetSimulations(This) (This)->lpVtbl->GetSimulations(This)
#define IDWriteFontFace1_IsSymbolFont(This) (This)->lpVtbl->IsSymbolFont(This)
#define IDWriteFontFace1_GetGlyphCount(This) (This)->lpVtbl->GetGlyphCount(This)
#define IDWriteFontFace1_GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways) (This)->lpVtbl->GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways)
#define IDWriteFontFace1_GetGlyphIndices(This,codepoints,count,glyph_indices) (This)->lpVtbl->GetGlyphIndices(This,codepoints,count,glyph_indices)
#define IDWriteFontFace1_TryGetFontTable(This,table_tag,table_data,table_size,context,exists) (This)->lpVtbl->TryGetFontTable(This,table_tag,table_data,table_size,context,exists)
#define IDWriteFontFace1_ReleaseFontTable(This,table_context) (This)->lpVtbl->ReleaseFontTable(This,table_context)
#define IDWriteFontFace1_GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink) (This)->lpVtbl->GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink)
#define IDWriteFontFace1_GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways) (This)->lpVtbl->GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways)
/*** IDWriteFontFace1 methods ***/
#define IDWriteFontFace1_GetMetrics(This,metrics) (This)->lpVtbl->IDWriteFontFace1_GetMetrics(This,metrics)
#define IDWriteFontFace1_GetGdiCompatibleMetrics(This,em_size,pixels_per_dip,transform,metrics) (This)->lpVtbl->IDWriteFontFace1_GetGdiCompatibleMetrics(This,em_size,pixels_per_dip,transform,metrics)
#define IDWriteFontFace1_GetCaretMetrics(This,metrics) (This)->lpVtbl->GetCaretMetrics(This,metrics)
#define IDWriteFontFace1_GetUnicodeRanges(This,max_count,ranges,count) (This)->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count)
#define IDWriteFontFace1_IsMonospacedFont(This) (This)->lpVtbl->IsMonospacedFont(This)
#define IDWriteFontFace1_GetDesignGlyphAdvances(This,glyph_count,indices,advances,is_sideways) (This)->lpVtbl->GetDesignGlyphAdvances(This,glyph_count,indices,advances,is_sideways)
#define IDWriteFontFace1_GetGdiCompatibleGlyphAdvances(This,em_size,pixels_per_dip,transform,use_gdi_natural,is_sideways,glyph_count,indices,advances) (This)->lpVtbl->GetGdiCompatibleGlyphAdvances(This,em_size,pixels_per_dip,transform,use_gdi_natural,is_sideways,glyph_count,indices,advances)
#define IDWriteFontFace1_GetKerningPairAdjustments(This,glyph_count,indices,adjustments) (This)->lpVtbl->GetKerningPairAdjustments(This,glyph_count,indices,adjustments)
#define IDWriteFontFace1_HasKerningPairs(This) (This)->lpVtbl->HasKerningPairs(This)
#define IDWriteFontFace1_GetRecommendedRenderingMode(This,font_emsize,dpiX,dpiY,transform,is_sideways,threshold,measuring_mode,rendering_mode) (This)->lpVtbl->IDWriteFontFace1_GetRecommendedRenderingMode(This,font_emsize,dpiX,dpiY,transform,is_sideways,threshold,measuring_mode,rendering_mode)
#define IDWriteFontFace1_GetVerticalGlyphVariants(This,glyph_count,nominal_indices,vertical_indices) (This)->lpVtbl->GetVerticalGlyphVariants(This,glyph_count,nominal_indices,vertical_indices)
#define IDWriteFontFace1_HasVerticalGlyphVariants(This) (This)->lpVtbl->HasVerticalGlyphVariants(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFace1_QueryInterface(IDWriteFontFace1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFace1_AddRef(IDWriteFontFace1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFace1_Release(IDWriteFontFace1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFace methods ***/
static inline DWRITE_FONT_FACE_TYPE IDWriteFontFace1_GetType(IDWriteFontFace1* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT IDWriteFontFace1_GetFiles(IDWriteFontFace1* This,UINT32 *number_of_files,IDWriteFontFile **fontfiles) {
    return This->lpVtbl->GetFiles(This,number_of_files,fontfiles);
}
static inline UINT32 IDWriteFontFace1_GetIndex(IDWriteFontFace1* This) {
    return This->lpVtbl->GetIndex(This);
}
static inline DWRITE_FONT_SIMULATIONS IDWriteFontFace1_GetSimulations(IDWriteFontFace1* This) {
    return This->lpVtbl->GetSimulations(This);
}
static inline WINBOOL IDWriteFontFace1_IsSymbolFont(IDWriteFontFace1* This) {
    return This->lpVtbl->IsSymbolFont(This);
}
static inline UINT16 IDWriteFontFace1_GetGlyphCount(IDWriteFontFace1* This) {
    return This->lpVtbl->GetGlyphCount(This);
}
static inline HRESULT IDWriteFontFace1_GetDesignGlyphMetrics(IDWriteFontFace1* This,const UINT16 *glyph_indices,UINT32 glyph_count,DWRITE_GLYPH_METRICS *metrics,WINBOOL is_sideways) {
    return This->lpVtbl->GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways);
}
static inline HRESULT IDWriteFontFace1_GetGlyphIndices(IDWriteFontFace1* This,const UINT32 *codepoints,UINT32 count,UINT16 *glyph_indices) {
    return This->lpVtbl->GetGlyphIndices(This,codepoints,count,glyph_indices);
}
static inline HRESULT IDWriteFontFace1_TryGetFontTable(IDWriteFontFace1* This,UINT32 table_tag,const void **table_data,UINT32 *table_size,void **context,WINBOOL *exists) {
    return This->lpVtbl->TryGetFontTable(This,table_tag,table_data,table_size,context,exists);
}
static inline void IDWriteFontFace1_ReleaseFontTable(IDWriteFontFace1* This,void *table_context) {
    This->lpVtbl->ReleaseFontTable(This,table_context);
}
static inline HRESULT IDWriteFontFace1_GetGlyphRunOutline(IDWriteFontFace1* This,FLOAT emSize,const UINT16 *glyph_indices,const FLOAT *glyph_advances,const DWRITE_GLYPH_OFFSET *glyph_offsets,UINT32 glyph_count,WINBOOL is_sideways,WINBOOL is_rtl,IDWriteGeometrySink *geometrysink) {
    return This->lpVtbl->GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink);
}
static inline HRESULT IDWriteFontFace1_GetGdiCompatibleGlyphMetrics(IDWriteFontFace1* This,FLOAT emSize,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,const UINT16 *glyph_indices,UINT32 glyph_count,DWRITE_GLYPH_METRICS *metrics,WINBOOL is_sideways) {
    return This->lpVtbl->GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways);
}
/*** IDWriteFontFace1 methods ***/
static inline void IDWriteFontFace1_GetMetrics(IDWriteFontFace1* This,DWRITE_FONT_METRICS1 *metrics) {
    This->lpVtbl->IDWriteFontFace1_GetMetrics(This,metrics);
}
static inline HRESULT IDWriteFontFace1_GetGdiCompatibleMetrics(IDWriteFontFace1* This,FLOAT em_size,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,DWRITE_FONT_METRICS1 *metrics) {
    return This->lpVtbl->IDWriteFontFace1_GetGdiCompatibleMetrics(This,em_size,pixels_per_dip,transform,metrics);
}
static inline void IDWriteFontFace1_GetCaretMetrics(IDWriteFontFace1* This,DWRITE_CARET_METRICS *metrics) {
    This->lpVtbl->GetCaretMetrics(This,metrics);
}
static inline HRESULT IDWriteFontFace1_GetUnicodeRanges(IDWriteFontFace1* This,UINT32 max_count,DWRITE_UNICODE_RANGE *ranges,UINT32 *count) {
    return This->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count);
}
static inline WINBOOL IDWriteFontFace1_IsMonospacedFont(IDWriteFontFace1* This) {
    return This->lpVtbl->IsMonospacedFont(This);
}
static inline HRESULT IDWriteFontFace1_GetDesignGlyphAdvances(IDWriteFontFace1* This,UINT32 glyph_count,const UINT16 *indices,INT32 *advances,WINBOOL is_sideways) {
    return This->lpVtbl->GetDesignGlyphAdvances(This,glyph_count,indices,advances,is_sideways);
}
static inline HRESULT IDWriteFontFace1_GetGdiCompatibleGlyphAdvances(IDWriteFontFace1* This,FLOAT em_size,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,WINBOOL is_sideways,UINT32 glyph_count,const UINT16 *indices,INT32 *advances) {
    return This->lpVtbl->GetGdiCompatibleGlyphAdvances(This,em_size,pixels_per_dip,transform,use_gdi_natural,is_sideways,glyph_count,indices,advances);
}
static inline HRESULT IDWriteFontFace1_GetKerningPairAdjustments(IDWriteFontFace1* This,UINT32 glyph_count,const UINT16 *indices,INT32 *adjustments) {
    return This->lpVtbl->GetKerningPairAdjustments(This,glyph_count,indices,adjustments);
}
static inline WINBOOL IDWriteFontFace1_HasKerningPairs(IDWriteFontFace1* This) {
    return This->lpVtbl->HasKerningPairs(This);
}
static inline HRESULT IDWriteFontFace1_GetRecommendedRenderingMode(IDWriteFontFace1* This,FLOAT font_emsize,FLOAT dpiX,FLOAT dpiY,const DWRITE_MATRIX *transform,WINBOOL is_sideways,DWRITE_OUTLINE_THRESHOLD threshold,DWRITE_MEASURING_MODE measuring_mode,DWRITE_RENDERING_MODE *rendering_mode) {
    return This->lpVtbl->IDWriteFontFace1_GetRecommendedRenderingMode(This,font_emsize,dpiX,dpiY,transform,is_sideways,threshold,measuring_mode,rendering_mode);
}
static inline HRESULT IDWriteFontFace1_GetVerticalGlyphVariants(IDWriteFontFace1* This,UINT32 glyph_count,const UINT16 *nominal_indices,UINT16 *vertical_indices) {
    return This->lpVtbl->GetVerticalGlyphVariants(This,glyph_count,nominal_indices,vertical_indices);
}
static inline WINBOOL IDWriteFontFace1_HasVerticalGlyphVariants(IDWriteFontFace1* This) {
    return This->lpVtbl->HasVerticalGlyphVariants(This);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFace1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFont1 interface
 */
#ifndef __IDWriteFont1_INTERFACE_DEFINED__
#define __IDWriteFont1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFont1, 0xacd16696, 0x8c14, 0x4f5d, 0x87,0x7e, 0xfe,0x3f,0xc1,0xd3,0x27,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("acd16696-8c14-4f5d-877e-fe3fc1d32738")
IDWriteFont1 : public IDWriteFont
{
    virtual void STDMETHODCALLTYPE GetMetrics(
        DWRITE_FONT_METRICS1 *metrics) = 0;

    virtual void STDMETHODCALLTYPE GetPanose(
        DWRITE_PANOSE *panose) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnicodeRanges(
        UINT32 max_count,
        DWRITE_UNICODE_RANGE *ranges,
        UINT32 *count) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsMonospacedFont(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFont1, 0xacd16696, 0x8c14, 0x4f5d, 0x87,0x7e, 0xfe,0x3f,0xc1,0xd3,0x27,0x38)
#endif
#else
typedef struct IDWriteFont1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFont1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFont1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFont1 *This);

    /*** IDWriteFont methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFontFamily)(
        IDWriteFont1 *This,
        IDWriteFontFamily **family);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetWeight)(
        IDWriteFont1 *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetStretch)(
        IDWriteFont1 *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetStyle)(
        IDWriteFont1 *This);

    WINBOOL (STDMETHODCALLTYPE *IsSymbolFont)(
        IDWriteFont1 *This);

    HRESULT (STDMETHODCALLTYPE *GetFaceNames)(
        IDWriteFont1 *This,
        IDWriteLocalizedStrings **names);

    HRESULT (STDMETHODCALLTYPE *GetInformationalStrings)(
        IDWriteFont1 *This,
        DWRITE_INFORMATIONAL_STRING_ID stringid,
        IDWriteLocalizedStrings **strings,
        WINBOOL *exists);

    DWRITE_FONT_SIMULATIONS (STDMETHODCALLTYPE *GetSimulations)(
        IDWriteFont1 *This);

    void (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteFont1 *This,
        DWRITE_FONT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HasCharacter)(
        IDWriteFont1 *This,
        UINT32 value,
        WINBOOL *exists);

    HRESULT (STDMETHODCALLTYPE *CreateFontFace)(
        IDWriteFont1 *This,
        IDWriteFontFace **face);

    /*** IDWriteFont1 methods ***/
    void (STDMETHODCALLTYPE *IDWriteFont1_GetMetrics)(
        IDWriteFont1 *This,
        DWRITE_FONT_METRICS1 *metrics);

    void (STDMETHODCALLTYPE *GetPanose)(
        IDWriteFont1 *This,
        DWRITE_PANOSE *panose);

    HRESULT (STDMETHODCALLTYPE *GetUnicodeRanges)(
        IDWriteFont1 *This,
        UINT32 max_count,
        DWRITE_UNICODE_RANGE *ranges,
        UINT32 *count);

    WINBOOL (STDMETHODCALLTYPE *IsMonospacedFont)(
        IDWriteFont1 *This);

    END_INTERFACE
} IDWriteFont1Vtbl;

interface IDWriteFont1 {
    CONST_VTBL IDWriteFont1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFont1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFont1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFont1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFont methods ***/
#define IDWriteFont1_GetFontFamily(This,family) (This)->lpVtbl->GetFontFamily(This,family)
#define IDWriteFont1_GetWeight(This) (This)->lpVtbl->GetWeight(This)
#define IDWriteFont1_GetStretch(This) (This)->lpVtbl->GetStretch(This)
#define IDWriteFont1_GetStyle(This) (This)->lpVtbl->GetStyle(This)
#define IDWriteFont1_IsSymbolFont(This) (This)->lpVtbl->IsSymbolFont(This)
#define IDWriteFont1_GetFaceNames(This,names) (This)->lpVtbl->GetFaceNames(This,names)
#define IDWriteFont1_GetInformationalStrings(This,stringid,strings,exists) (This)->lpVtbl->GetInformationalStrings(This,stringid,strings,exists)
#define IDWriteFont1_GetSimulations(This) (This)->lpVtbl->GetSimulations(This)
#define IDWriteFont1_HasCharacter(This,value,exists) (This)->lpVtbl->HasCharacter(This,value,exists)
#define IDWriteFont1_CreateFontFace(This,face) (This)->lpVtbl->CreateFontFace(This,face)
/*** IDWriteFont1 methods ***/
#define IDWriteFont1_GetMetrics(This,metrics) (This)->lpVtbl->IDWriteFont1_GetMetrics(This,metrics)
#define IDWriteFont1_GetPanose(This,panose) (This)->lpVtbl->GetPanose(This,panose)
#define IDWriteFont1_GetUnicodeRanges(This,max_count,ranges,count) (This)->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count)
#define IDWriteFont1_IsMonospacedFont(This) (This)->lpVtbl->IsMonospacedFont(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFont1_QueryInterface(IDWriteFont1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFont1_AddRef(IDWriteFont1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFont1_Release(IDWriteFont1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFont methods ***/
static inline HRESULT IDWriteFont1_GetFontFamily(IDWriteFont1* This,IDWriteFontFamily **family) {
    return This->lpVtbl->GetFontFamily(This,family);
}
static inline DWRITE_FONT_WEIGHT IDWriteFont1_GetWeight(IDWriteFont1* This) {
    return This->lpVtbl->GetWeight(This);
}
static inline DWRITE_FONT_STRETCH IDWriteFont1_GetStretch(IDWriteFont1* This) {
    return This->lpVtbl->GetStretch(This);
}
static inline DWRITE_FONT_STYLE IDWriteFont1_GetStyle(IDWriteFont1* This) {
    return This->lpVtbl->GetStyle(This);
}
static inline WINBOOL IDWriteFont1_IsSymbolFont(IDWriteFont1* This) {
    return This->lpVtbl->IsSymbolFont(This);
}
static inline HRESULT IDWriteFont1_GetFaceNames(IDWriteFont1* This,IDWriteLocalizedStrings **names) {
    return This->lpVtbl->GetFaceNames(This,names);
}
static inline HRESULT IDWriteFont1_GetInformationalStrings(IDWriteFont1* This,DWRITE_INFORMATIONAL_STRING_ID stringid,IDWriteLocalizedStrings **strings,WINBOOL *exists) {
    return This->lpVtbl->GetInformationalStrings(This,stringid,strings,exists);
}
static inline DWRITE_FONT_SIMULATIONS IDWriteFont1_GetSimulations(IDWriteFont1* This) {
    return This->lpVtbl->GetSimulations(This);
}
static inline HRESULT IDWriteFont1_HasCharacter(IDWriteFont1* This,UINT32 value,WINBOOL *exists) {
    return This->lpVtbl->HasCharacter(This,value,exists);
}
static inline HRESULT IDWriteFont1_CreateFontFace(IDWriteFont1* This,IDWriteFontFace **face) {
    return This->lpVtbl->CreateFontFace(This,face);
}
/*** IDWriteFont1 methods ***/
static inline void IDWriteFont1_GetMetrics(IDWriteFont1* This,DWRITE_FONT_METRICS1 *metrics) {
    This->lpVtbl->IDWriteFont1_GetMetrics(This,metrics);
}
static inline void IDWriteFont1_GetPanose(IDWriteFont1* This,DWRITE_PANOSE *panose) {
    This->lpVtbl->GetPanose(This,panose);
}
static inline HRESULT IDWriteFont1_GetUnicodeRanges(IDWriteFont1* This,UINT32 max_count,DWRITE_UNICODE_RANGE *ranges,UINT32 *count) {
    return This->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count);
}
static inline WINBOOL IDWriteFont1_IsMonospacedFont(IDWriteFont1* This) {
    return This->lpVtbl->IsMonospacedFont(This);
}
#endif
#endif

#endif


#endif  /* __IDWriteFont1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteRenderingParams1 interface
 */
#ifndef __IDWriteRenderingParams1_INTERFACE_DEFINED__
#define __IDWriteRenderingParams1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteRenderingParams1, 0x94413cf4, 0xa6fc, 0x4248, 0x8b,0x50, 0x66,0x74,0x34,0x8f,0xca,0xd3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94413cf4-a6fc-4248-8b50-6674348fcad3")
IDWriteRenderingParams1 : public IDWriteRenderingParams
{
    virtual FLOAT STDMETHODCALLTYPE GetGrayscaleEnhancedContrast(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteRenderingParams1, 0x94413cf4, 0xa6fc, 0x4248, 0x8b,0x50, 0x66,0x74,0x34,0x8f,0xca,0xd3)
#endif
#else
typedef struct IDWriteRenderingParams1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteRenderingParams1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteRenderingParams1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteRenderingParams1 *This);

    /*** IDWriteRenderingParams methods ***/
    FLOAT (STDMETHODCALLTYPE *GetGamma)(
        IDWriteRenderingParams1 *This);

    FLOAT (STDMETHODCALLTYPE *GetEnhancedContrast)(
        IDWriteRenderingParams1 *This);

    FLOAT (STDMETHODCALLTYPE *GetClearTypeLevel)(
        IDWriteRenderingParams1 *This);

    DWRITE_PIXEL_GEOMETRY (STDMETHODCALLTYPE *GetPixelGeometry)(
        IDWriteRenderingParams1 *This);

    DWRITE_RENDERING_MODE (STDMETHODCALLTYPE *GetRenderingMode)(
        IDWriteRenderingParams1 *This);

    /*** IDWriteRenderingParams1 methods ***/
    FLOAT (STDMETHODCALLTYPE *GetGrayscaleEnhancedContrast)(
        IDWriteRenderingParams1 *This);

    END_INTERFACE
} IDWriteRenderingParams1Vtbl;

interface IDWriteRenderingParams1 {
    CONST_VTBL IDWriteRenderingParams1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteRenderingParams1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteRenderingParams1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteRenderingParams1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteRenderingParams methods ***/
#define IDWriteRenderingParams1_GetGamma(This) (This)->lpVtbl->GetGamma(This)
#define IDWriteRenderingParams1_GetEnhancedContrast(This) (This)->lpVtbl->GetEnhancedContrast(This)
#define IDWriteRenderingParams1_GetClearTypeLevel(This) (This)->lpVtbl->GetClearTypeLevel(This)
#define IDWriteRenderingParams1_GetPixelGeometry(This) (This)->lpVtbl->GetPixelGeometry(This)
#define IDWriteRenderingParams1_GetRenderingMode(This) (This)->lpVtbl->GetRenderingMode(This)
/*** IDWriteRenderingParams1 methods ***/
#define IDWriteRenderingParams1_GetGrayscaleEnhancedContrast(This) (This)->lpVtbl->GetGrayscaleEnhancedContrast(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteRenderingParams1_QueryInterface(IDWriteRenderingParams1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteRenderingParams1_AddRef(IDWriteRenderingParams1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteRenderingParams1_Release(IDWriteRenderingParams1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteRenderingParams methods ***/
static inline FLOAT IDWriteRenderingParams1_GetGamma(IDWriteRenderingParams1* This) {
    return This->lpVtbl->GetGamma(This);
}
static inline FLOAT IDWriteRenderingParams1_GetEnhancedContrast(IDWriteRenderingParams1* This) {
    return This->lpVtbl->GetEnhancedContrast(This);
}
static inline FLOAT IDWriteRenderingParams1_GetClearTypeLevel(IDWriteRenderingParams1* This) {
    return This->lpVtbl->GetClearTypeLevel(This);
}
static inline DWRITE_PIXEL_GEOMETRY IDWriteRenderingParams1_GetPixelGeometry(IDWriteRenderingParams1* This) {
    return This->lpVtbl->GetPixelGeometry(This);
}
static inline DWRITE_RENDERING_MODE IDWriteRenderingParams1_GetRenderingMode(IDWriteRenderingParams1* This) {
    return This->lpVtbl->GetRenderingMode(This);
}
/*** IDWriteRenderingParams1 methods ***/
static inline FLOAT IDWriteRenderingParams1_GetGrayscaleEnhancedContrast(IDWriteRenderingParams1* This) {
    return This->lpVtbl->GetGrayscaleEnhancedContrast(This);
}
#endif
#endif

#endif


#endif  /* __IDWriteRenderingParams1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextAnalyzer1 interface
 */
#ifndef __IDWriteTextAnalyzer1_INTERFACE_DEFINED__
#define __IDWriteTextAnalyzer1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextAnalyzer1, 0x80dad800, 0xe21f, 0x4e83, 0x96,0xce, 0xbf,0xcc,0xe5,0x00,0xdb,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("80dad800-e21f-4e83-96ce-bfcce500db7c")
IDWriteTextAnalyzer1 : public IDWriteTextAnalyzer
{
    virtual HRESULT STDMETHODCALLTYPE ApplyCharacterSpacing(
        FLOAT leading_spacing,
        FLOAT trailing_spacing,
        FLOAT min_advance_width,
        UINT32 len,
        UINT32 glyph_count,
        const UINT16 *clustermap,
        const FLOAT *advances,
        const DWRITE_GLYPH_OFFSET *offsets,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *props,
        FLOAT *modified_advances,
        DWRITE_GLYPH_OFFSET *modified_offsets) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBaseline(
        IDWriteFontFace *face,
        DWRITE_BASELINE baseline,
        WINBOOL vertical,
        WINBOOL is_simulation_allowed,
        DWRITE_SCRIPT_ANALYSIS sa,
        const WCHAR *localeName,
        INT32 *baseline_coord,
        WINBOOL *exists) = 0;

    virtual HRESULT STDMETHODCALLTYPE AnalyzeVerticalGlyphOrientation(
        IDWriteTextAnalysisSource1 *source,
        UINT32 text_pos,
        UINT32 len,
        IDWriteTextAnalysisSink1 *sink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphOrientationTransform(
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        WINBOOL is_sideways,
        DWRITE_MATRIX *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptProperties(
        DWRITE_SCRIPT_ANALYSIS sa,
        DWRITE_SCRIPT_PROPERTIES *props) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTextComplexity(
        const WCHAR *text,
        UINT32 len,
        IDWriteFontFace *face,
        WINBOOL *is_simple,
        UINT32 *len_read,
        UINT16 *indices) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetJustificationOpportunities(
        IDWriteFontFace *face,
        FLOAT font_em_size,
        DWRITE_SCRIPT_ANALYSIS sa,
        UINT32 length,
        UINT32 glyph_count,
        const WCHAR *text,
        const UINT16 *clustermap,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        DWRITE_JUSTIFICATION_OPPORTUNITY *jo) = 0;

    virtual HRESULT STDMETHODCALLTYPE JustifyGlyphAdvances(
        FLOAT width,
        UINT32 glyph_count,
        const DWRITE_JUSTIFICATION_OPPORTUNITY *jo,
        const FLOAT *advances,
        const DWRITE_GLYPH_OFFSET *offsets,
        FLOAT *justifiedadvances,
        DWRITE_GLYPH_OFFSET *justifiedoffsets) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetJustifiedGlyphs(
        IDWriteFontFace *face,
        FLOAT font_em_size,
        DWRITE_SCRIPT_ANALYSIS sa,
        UINT32 length,
        UINT32 glyph_count,
        UINT32 max_glyphcount,
        const UINT16 *clustermap,
        const UINT16 *indices,
        const FLOAT *advances,
        const FLOAT *justifiedadvances,
        const DWRITE_GLYPH_OFFSET *justifiedoffsets,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        UINT32 *actual_count,
        UINT16 *modified_clustermap,
        UINT16 *modified_indices,
        FLOAT *modified_advances,
        DWRITE_GLYPH_OFFSET *modified_offsets) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextAnalyzer1, 0x80dad800, 0xe21f, 0x4e83, 0x96,0xce, 0xbf,0xcc,0xe5,0x00,0xdb,0x7c)
#endif
#else
typedef struct IDWriteTextAnalyzer1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextAnalyzer1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextAnalyzer1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextAnalyzer1 *This);

    /*** IDWriteTextAnalyzer methods ***/
    HRESULT (STDMETHODCALLTYPE *AnalyzeScript)(
        IDWriteTextAnalyzer1 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeBidi)(
        IDWriteTextAnalyzer1 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeNumberSubstitution)(
        IDWriteTextAnalyzer1 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeLineBreakpoints)(
        IDWriteTextAnalyzer1 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *GetGlyphs)(
        IDWriteTextAnalyzer1 *This,
        const WCHAR *text,
        UINT32 length,
        IDWriteFontFace *font_face,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        IDWriteNumberSubstitution *substitution,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        UINT32 max_glyph_count,
        UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *text_props,
        UINT16 *glyph_indices,
        DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 *actual_glyph_count);

    HRESULT (STDMETHODCALLTYPE *GetGlyphPlacements)(
        IDWriteTextAnalyzer1 *This,
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphPlacements)(
        IDWriteTextAnalyzer1 *This,
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_lengths,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets);

    /*** IDWriteTextAnalyzer1 methods ***/
    HRESULT (STDMETHODCALLTYPE *ApplyCharacterSpacing)(
        IDWriteTextAnalyzer1 *This,
        FLOAT leading_spacing,
        FLOAT trailing_spacing,
        FLOAT min_advance_width,
        UINT32 len,
        UINT32 glyph_count,
        const UINT16 *clustermap,
        const FLOAT *advances,
        const DWRITE_GLYPH_OFFSET *offsets,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *props,
        FLOAT *modified_advances,
        DWRITE_GLYPH_OFFSET *modified_offsets);

    HRESULT (STDMETHODCALLTYPE *GetBaseline)(
        IDWriteTextAnalyzer1 *This,
        IDWriteFontFace *face,
        DWRITE_BASELINE baseline,
        WINBOOL vertical,
        WINBOOL is_simulation_allowed,
        DWRITE_SCRIPT_ANALYSIS sa,
        const WCHAR *localeName,
        INT32 *baseline_coord,
        WINBOOL *exists);

    HRESULT (STDMETHODCALLTYPE *AnalyzeVerticalGlyphOrientation)(
        IDWriteTextAnalyzer1 *This,
        IDWriteTextAnalysisSource1 *source,
        UINT32 text_pos,
        UINT32 len,
        IDWriteTextAnalysisSink1 *sink);

    HRESULT (STDMETHODCALLTYPE *GetGlyphOrientationTransform)(
        IDWriteTextAnalyzer1 *This,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        WINBOOL is_sideways,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetScriptProperties)(
        IDWriteTextAnalyzer1 *This,
        DWRITE_SCRIPT_ANALYSIS sa,
        DWRITE_SCRIPT_PROPERTIES *props);

    HRESULT (STDMETHODCALLTYPE *GetTextComplexity)(
        IDWriteTextAnalyzer1 *This,
        const WCHAR *text,
        UINT32 len,
        IDWriteFontFace *face,
        WINBOOL *is_simple,
        UINT32 *len_read,
        UINT16 *indices);

    HRESULT (STDMETHODCALLTYPE *GetJustificationOpportunities)(
        IDWriteTextAnalyzer1 *This,
        IDWriteFontFace *face,
        FLOAT font_em_size,
        DWRITE_SCRIPT_ANALYSIS sa,
        UINT32 length,
        UINT32 glyph_count,
        const WCHAR *text,
        const UINT16 *clustermap,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        DWRITE_JUSTIFICATION_OPPORTUNITY *jo);

    HRESULT (STDMETHODCALLTYPE *JustifyGlyphAdvances)(
        IDWriteTextAnalyzer1 *This,
        FLOAT width,
        UINT32 glyph_count,
        const DWRITE_JUSTIFICATION_OPPORTUNITY *jo,
        const FLOAT *advances,
        const DWRITE_GLYPH_OFFSET *offsets,
        FLOAT *justifiedadvances,
        DWRITE_GLYPH_OFFSET *justifiedoffsets);

    HRESULT (STDMETHODCALLTYPE *GetJustifiedGlyphs)(
        IDWriteTextAnalyzer1 *This,
        IDWriteFontFace *face,
        FLOAT font_em_size,
        DWRITE_SCRIPT_ANALYSIS sa,
        UINT32 length,
        UINT32 glyph_count,
        UINT32 max_glyphcount,
        const UINT16 *clustermap,
        const UINT16 *indices,
        const FLOAT *advances,
        const FLOAT *justifiedadvances,
        const DWRITE_GLYPH_OFFSET *justifiedoffsets,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        UINT32 *actual_count,
        UINT16 *modified_clustermap,
        UINT16 *modified_indices,
        FLOAT *modified_advances,
        DWRITE_GLYPH_OFFSET *modified_offsets);

    END_INTERFACE
} IDWriteTextAnalyzer1Vtbl;

interface IDWriteTextAnalyzer1 {
    CONST_VTBL IDWriteTextAnalyzer1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextAnalyzer1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextAnalyzer1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextAnalyzer1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextAnalyzer methods ***/
#define IDWriteTextAnalyzer1_AnalyzeScript(This,source,position,length,sink) (This)->lpVtbl->AnalyzeScript(This,source,position,length,sink)
#define IDWriteTextAnalyzer1_AnalyzeBidi(This,source,position,length,sink) (This)->lpVtbl->AnalyzeBidi(This,source,position,length,sink)
#define IDWriteTextAnalyzer1_AnalyzeNumberSubstitution(This,source,position,length,sink) (This)->lpVtbl->AnalyzeNumberSubstitution(This,source,position,length,sink)
#define IDWriteTextAnalyzer1_AnalyzeLineBreakpoints(This,source,position,length,sink) (This)->lpVtbl->AnalyzeLineBreakpoints(This,source,position,length,sink)
#define IDWriteTextAnalyzer1_GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count) (This)->lpVtbl->GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count)
#define IDWriteTextAnalyzer1_GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets) (This)->lpVtbl->GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets)
#define IDWriteTextAnalyzer1_GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets) (This)->lpVtbl->GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets)
/*** IDWriteTextAnalyzer1 methods ***/
#define IDWriteTextAnalyzer1_ApplyCharacterSpacing(This,leading_spacing,trailing_spacing,min_advance_width,len,glyph_count,clustermap,advances,offsets,props,modified_advances,modified_offsets) (This)->lpVtbl->ApplyCharacterSpacing(This,leading_spacing,trailing_spacing,min_advance_width,len,glyph_count,clustermap,advances,offsets,props,modified_advances,modified_offsets)
#define IDWriteTextAnalyzer1_GetBaseline(This,face,baseline,vertical,is_simulation_allowed,sa,localeName,baseline_coord,exists) (This)->lpVtbl->GetBaseline(This,face,baseline,vertical,is_simulation_allowed,sa,localeName,baseline_coord,exists)
#define IDWriteTextAnalyzer1_AnalyzeVerticalGlyphOrientation(This,source,text_pos,len,sink) (This)->lpVtbl->AnalyzeVerticalGlyphOrientation(This,source,text_pos,len,sink)
#define IDWriteTextAnalyzer1_GetGlyphOrientationTransform(This,angle,is_sideways,transform) (This)->lpVtbl->GetGlyphOrientationTransform(This,angle,is_sideways,transform)
#define IDWriteTextAnalyzer1_GetScriptProperties(This,sa,props) (This)->lpVtbl->GetScriptProperties(This,sa,props)
#define IDWriteTextAnalyzer1_GetTextComplexity(This,text,len,face,is_simple,len_read,indices) (This)->lpVtbl->GetTextComplexity(This,text,len,face,is_simple,len_read,indices)
#define IDWriteTextAnalyzer1_GetJustificationOpportunities(This,face,font_em_size,sa,length,glyph_count,text,clustermap,prop,jo) (This)->lpVtbl->GetJustificationOpportunities(This,face,font_em_size,sa,length,glyph_count,text,clustermap,prop,jo)
#define IDWriteTextAnalyzer1_JustifyGlyphAdvances(This,width,glyph_count,jo,advances,offsets,justifiedadvances,justifiedoffsets) (This)->lpVtbl->JustifyGlyphAdvances(This,width,glyph_count,jo,advances,offsets,justifiedadvances,justifiedoffsets)
#define IDWriteTextAnalyzer1_GetJustifiedGlyphs(This,face,font_em_size,sa,length,glyph_count,max_glyphcount,clustermap,indices,advances,justifiedadvances,justifiedoffsets,prop,actual_count,modified_clustermap,modified_indices,modified_advances,modified_offsets) (This)->lpVtbl->GetJustifiedGlyphs(This,face,font_em_size,sa,length,glyph_count,max_glyphcount,clustermap,indices,advances,justifiedadvances,justifiedoffsets,prop,actual_count,modified_clustermap,modified_indices,modified_advances,modified_offsets)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextAnalyzer1_QueryInterface(IDWriteTextAnalyzer1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextAnalyzer1_AddRef(IDWriteTextAnalyzer1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextAnalyzer1_Release(IDWriteTextAnalyzer1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextAnalyzer methods ***/
static inline HRESULT IDWriteTextAnalyzer1_AnalyzeScript(IDWriteTextAnalyzer1* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeScript(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer1_AnalyzeBidi(IDWriteTextAnalyzer1* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeBidi(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer1_AnalyzeNumberSubstitution(IDWriteTextAnalyzer1* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeNumberSubstitution(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer1_AnalyzeLineBreakpoints(IDWriteTextAnalyzer1* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeLineBreakpoints(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer1_GetGlyphs(IDWriteTextAnalyzer1* This,const WCHAR *text,UINT32 length,IDWriteFontFace *font_face,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,IDWriteNumberSubstitution *substitution,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_len,UINT32 feature_ranges,UINT32 max_glyph_count,UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *text_props,UINT16 *glyph_indices,DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 *actual_glyph_count) {
    return This->lpVtbl->GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count);
}
static inline HRESULT IDWriteTextAnalyzer1_GetGlyphPlacements(IDWriteTextAnalyzer1* This,const WCHAR *text,const UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *props,UINT32 text_len,const UINT16 *glyph_indices,const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 glyph_count,IDWriteFontFace *font_face,FLOAT fontEmSize,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_len,UINT32 feature_ranges,FLOAT *glyph_advances,DWRITE_GLYPH_OFFSET *glyph_offsets) {
    return This->lpVtbl->GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets);
}
static inline HRESULT IDWriteTextAnalyzer1_GetGdiCompatibleGlyphPlacements(IDWriteTextAnalyzer1* This,const WCHAR *text,const UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *props,UINT32 text_len,const UINT16 *glyph_indices,const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 glyph_count,IDWriteFontFace *font_face,FLOAT fontEmSize,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_lengths,UINT32 feature_ranges,FLOAT *glyph_advances,DWRITE_GLYPH_OFFSET *glyph_offsets) {
    return This->lpVtbl->GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets);
}
/*** IDWriteTextAnalyzer1 methods ***/
static inline HRESULT IDWriteTextAnalyzer1_ApplyCharacterSpacing(IDWriteTextAnalyzer1* This,FLOAT leading_spacing,FLOAT trailing_spacing,FLOAT min_advance_width,UINT32 len,UINT32 glyph_count,const UINT16 *clustermap,const FLOAT *advances,const DWRITE_GLYPH_OFFSET *offsets,const DWRITE_SHAPING_GLYPH_PROPERTIES *props,FLOAT *modified_advances,DWRITE_GLYPH_OFFSET *modified_offsets) {
    return This->lpVtbl->ApplyCharacterSpacing(This,leading_spacing,trailing_spacing,min_advance_width,len,glyph_count,clustermap,advances,offsets,props,modified_advances,modified_offsets);
}
static inline HRESULT IDWriteTextAnalyzer1_GetBaseline(IDWriteTextAnalyzer1* This,IDWriteFontFace *face,DWRITE_BASELINE baseline,WINBOOL vertical,WINBOOL is_simulation_allowed,DWRITE_SCRIPT_ANALYSIS sa,const WCHAR *localeName,INT32 *baseline_coord,WINBOOL *exists) {
    return This->lpVtbl->GetBaseline(This,face,baseline,vertical,is_simulation_allowed,sa,localeName,baseline_coord,exists);
}
static inline HRESULT IDWriteTextAnalyzer1_AnalyzeVerticalGlyphOrientation(IDWriteTextAnalyzer1* This,IDWriteTextAnalysisSource1 *source,UINT32 text_pos,UINT32 len,IDWriteTextAnalysisSink1 *sink) {
    return This->lpVtbl->AnalyzeVerticalGlyphOrientation(This,source,text_pos,len,sink);
}
static inline HRESULT IDWriteTextAnalyzer1_GetGlyphOrientationTransform(IDWriteTextAnalyzer1* This,DWRITE_GLYPH_ORIENTATION_ANGLE angle,WINBOOL is_sideways,DWRITE_MATRIX *transform) {
    return This->lpVtbl->GetGlyphOrientationTransform(This,angle,is_sideways,transform);
}
static inline HRESULT IDWriteTextAnalyzer1_GetScriptProperties(IDWriteTextAnalyzer1* This,DWRITE_SCRIPT_ANALYSIS sa,DWRITE_SCRIPT_PROPERTIES *props) {
    return This->lpVtbl->GetScriptProperties(This,sa,props);
}
static inline HRESULT IDWriteTextAnalyzer1_GetTextComplexity(IDWriteTextAnalyzer1* This,const WCHAR *text,UINT32 len,IDWriteFontFace *face,WINBOOL *is_simple,UINT32 *len_read,UINT16 *indices) {
    return This->lpVtbl->GetTextComplexity(This,text,len,face,is_simple,len_read,indices);
}
static inline HRESULT IDWriteTextAnalyzer1_GetJustificationOpportunities(IDWriteTextAnalyzer1* This,IDWriteFontFace *face,FLOAT font_em_size,DWRITE_SCRIPT_ANALYSIS sa,UINT32 length,UINT32 glyph_count,const WCHAR *text,const UINT16 *clustermap,const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,DWRITE_JUSTIFICATION_OPPORTUNITY *jo) {
    return This->lpVtbl->GetJustificationOpportunities(This,face,font_em_size,sa,length,glyph_count,text,clustermap,prop,jo);
}
static inline HRESULT IDWriteTextAnalyzer1_JustifyGlyphAdvances(IDWriteTextAnalyzer1* This,FLOAT width,UINT32 glyph_count,const DWRITE_JUSTIFICATION_OPPORTUNITY *jo,const FLOAT *advances,const DWRITE_GLYPH_OFFSET *offsets,FLOAT *justifiedadvances,DWRITE_GLYPH_OFFSET *justifiedoffsets) {
    return This->lpVtbl->JustifyGlyphAdvances(This,width,glyph_count,jo,advances,offsets,justifiedadvances,justifiedoffsets);
}
static inline HRESULT IDWriteTextAnalyzer1_GetJustifiedGlyphs(IDWriteTextAnalyzer1* This,IDWriteFontFace *face,FLOAT font_em_size,DWRITE_SCRIPT_ANALYSIS sa,UINT32 length,UINT32 glyph_count,UINT32 max_glyphcount,const UINT16 *clustermap,const UINT16 *indices,const FLOAT *advances,const FLOAT *justifiedadvances,const DWRITE_GLYPH_OFFSET *justifiedoffsets,const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,UINT32 *actual_count,UINT16 *modified_clustermap,UINT16 *modified_indices,FLOAT *modified_advances,DWRITE_GLYPH_OFFSET *modified_offsets) {
    return This->lpVtbl->GetJustifiedGlyphs(This,face,font_em_size,sa,length,glyph_count,max_glyphcount,clustermap,indices,advances,justifiedadvances,justifiedoffsets,prop,actual_count,modified_clustermap,modified_indices,modified_advances,modified_offsets);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextAnalyzer1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextAnalysisSource1 interface
 */
#ifndef __IDWriteTextAnalysisSource1_INTERFACE_DEFINED__
#define __IDWriteTextAnalysisSource1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextAnalysisSource1, 0x639cfad8, 0x0fb4, 0x4b21, 0xa5,0x8a, 0x06,0x79,0x20,0x12,0x00,0x09);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("639cfad8-0fb4-4b21-a58a-067920120009")
IDWriteTextAnalysisSource1 : public IDWriteTextAnalysisSource
{
    virtual HRESULT STDMETHODCALLTYPE GetVerticalGlyphOrientation(
        UINT32 pos,
        UINT32 *length,
        DWRITE_VERTICAL_GLYPH_ORIENTATION *orientation,
        UINT8 *bidi_level) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextAnalysisSource1, 0x639cfad8, 0x0fb4, 0x4b21, 0xa5,0x8a, 0x06,0x79,0x20,0x12,0x00,0x09)
#endif
#else
typedef struct IDWriteTextAnalysisSource1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextAnalysisSource1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextAnalysisSource1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextAnalysisSource1 *This);

    /*** IDWriteTextAnalysisSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTextAtPosition)(
        IDWriteTextAnalysisSource1 *This,
        UINT32 position,
        const WCHAR **text,
        UINT32 *text_len);

    HRESULT (STDMETHODCALLTYPE *GetTextBeforePosition)(
        IDWriteTextAnalysisSource1 *This,
        UINT32 position,
        const WCHAR **text,
        UINT32 *text_len);

    DWRITE_READING_DIRECTION (STDMETHODCALLTYPE *GetParagraphReadingDirection)(
        IDWriteTextAnalysisSource1 *This);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteTextAnalysisSource1 *This,
        UINT32 position,
        UINT32 *text_len,
        const WCHAR **locale);

    HRESULT (STDMETHODCALLTYPE *GetNumberSubstitution)(
        IDWriteTextAnalysisSource1 *This,
        UINT32 position,
        UINT32 *text_len,
        IDWriteNumberSubstitution **substitution);

    /*** IDWriteTextAnalysisSource1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVerticalGlyphOrientation)(
        IDWriteTextAnalysisSource1 *This,
        UINT32 pos,
        UINT32 *length,
        DWRITE_VERTICAL_GLYPH_ORIENTATION *orientation,
        UINT8 *bidi_level);

    END_INTERFACE
} IDWriteTextAnalysisSource1Vtbl;

interface IDWriteTextAnalysisSource1 {
    CONST_VTBL IDWriteTextAnalysisSource1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextAnalysisSource1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextAnalysisSource1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextAnalysisSource1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextAnalysisSource methods ***/
#define IDWriteTextAnalysisSource1_GetTextAtPosition(This,position,text,text_len) (This)->lpVtbl->GetTextAtPosition(This,position,text,text_len)
#define IDWriteTextAnalysisSource1_GetTextBeforePosition(This,position,text,text_len) (This)->lpVtbl->GetTextBeforePosition(This,position,text,text_len)
#define IDWriteTextAnalysisSource1_GetParagraphReadingDirection(This) (This)->lpVtbl->GetParagraphReadingDirection(This)
#define IDWriteTextAnalysisSource1_GetLocaleName(This,position,text_len,locale) (This)->lpVtbl->GetLocaleName(This,position,text_len,locale)
#define IDWriteTextAnalysisSource1_GetNumberSubstitution(This,position,text_len,substitution) (This)->lpVtbl->GetNumberSubstitution(This,position,text_len,substitution)
/*** IDWriteTextAnalysisSource1 methods ***/
#define IDWriteTextAnalysisSource1_GetVerticalGlyphOrientation(This,pos,length,orientation,bidi_level) (This)->lpVtbl->GetVerticalGlyphOrientation(This,pos,length,orientation,bidi_level)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextAnalysisSource1_QueryInterface(IDWriteTextAnalysisSource1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextAnalysisSource1_AddRef(IDWriteTextAnalysisSource1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextAnalysisSource1_Release(IDWriteTextAnalysisSource1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextAnalysisSource methods ***/
static inline HRESULT IDWriteTextAnalysisSource1_GetTextAtPosition(IDWriteTextAnalysisSource1* This,UINT32 position,const WCHAR **text,UINT32 *text_len) {
    return This->lpVtbl->GetTextAtPosition(This,position,text,text_len);
}
static inline HRESULT IDWriteTextAnalysisSource1_GetTextBeforePosition(IDWriteTextAnalysisSource1* This,UINT32 position,const WCHAR **text,UINT32 *text_len) {
    return This->lpVtbl->GetTextBeforePosition(This,position,text,text_len);
}
static inline DWRITE_READING_DIRECTION IDWriteTextAnalysisSource1_GetParagraphReadingDirection(IDWriteTextAnalysisSource1* This) {
    return This->lpVtbl->GetParagraphReadingDirection(This);
}
static inline HRESULT IDWriteTextAnalysisSource1_GetLocaleName(IDWriteTextAnalysisSource1* This,UINT32 position,UINT32 *text_len,const WCHAR **locale) {
    return This->lpVtbl->GetLocaleName(This,position,text_len,locale);
}
static inline HRESULT IDWriteTextAnalysisSource1_GetNumberSubstitution(IDWriteTextAnalysisSource1* This,UINT32 position,UINT32 *text_len,IDWriteNumberSubstitution **substitution) {
    return This->lpVtbl->GetNumberSubstitution(This,position,text_len,substitution);
}
/*** IDWriteTextAnalysisSource1 methods ***/
static inline HRESULT IDWriteTextAnalysisSource1_GetVerticalGlyphOrientation(IDWriteTextAnalysisSource1* This,UINT32 pos,UINT32 *length,DWRITE_VERTICAL_GLYPH_ORIENTATION *orientation,UINT8 *bidi_level) {
    return This->lpVtbl->GetVerticalGlyphOrientation(This,pos,length,orientation,bidi_level);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextAnalysisSource1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextAnalysisSink1 interface
 */
#ifndef __IDWriteTextAnalysisSink1_INTERFACE_DEFINED__
#define __IDWriteTextAnalysisSink1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextAnalysisSink1, 0xb0d941a0, 0x85e7, 0x4d8b, 0x9f,0xd3, 0x5c,0xed,0x99,0x34,0x48,0x2a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b0d941a0-85e7-4d8b-9fd3-5ced9934482a")
IDWriteTextAnalysisSink1 : public IDWriteTextAnalysisSink
{
    virtual HRESULT STDMETHODCALLTYPE SetGlyphOrientation(
        UINT32 pos,
        UINT32 length,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        UINT8 adjusted_bidilevel,
        WINBOOL is_sideways,
        WINBOOL is_rtl) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextAnalysisSink1, 0xb0d941a0, 0x85e7, 0x4d8b, 0x9f,0xd3, 0x5c,0xed,0x99,0x34,0x48,0x2a)
#endif
#else
typedef struct IDWriteTextAnalysisSink1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextAnalysisSink1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextAnalysisSink1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextAnalysisSink1 *This);

    /*** IDWriteTextAnalysisSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetScriptAnalysis)(
        IDWriteTextAnalysisSink1 *This,
        UINT32 position,
        UINT32 length,
        const DWRITE_SCRIPT_ANALYSIS *scriptanalysis);

    HRESULT (STDMETHODCALLTYPE *SetLineBreakpoints)(
        IDWriteTextAnalysisSink1 *This,
        UINT32 position,
        UINT32 length,
        const DWRITE_LINE_BREAKPOINT *breakpoints);

    HRESULT (STDMETHODCALLTYPE *SetBidiLevel)(
        IDWriteTextAnalysisSink1 *This,
        UINT32 position,
        UINT32 length,
        UINT8 explicitLevel,
        UINT8 resolvedLevel);

    HRESULT (STDMETHODCALLTYPE *SetNumberSubstitution)(
        IDWriteTextAnalysisSink1 *This,
        UINT32 position,
        UINT32 length,
        IDWriteNumberSubstitution *substitution);

    /*** IDWriteTextAnalysisSink1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetGlyphOrientation)(
        IDWriteTextAnalysisSink1 *This,
        UINT32 pos,
        UINT32 length,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        UINT8 adjusted_bidilevel,
        WINBOOL is_sideways,
        WINBOOL is_rtl);

    END_INTERFACE
} IDWriteTextAnalysisSink1Vtbl;

interface IDWriteTextAnalysisSink1 {
    CONST_VTBL IDWriteTextAnalysisSink1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextAnalysisSink1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextAnalysisSink1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextAnalysisSink1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextAnalysisSink methods ***/
#define IDWriteTextAnalysisSink1_SetScriptAnalysis(This,position,length,scriptanalysis) (This)->lpVtbl->SetScriptAnalysis(This,position,length,scriptanalysis)
#define IDWriteTextAnalysisSink1_SetLineBreakpoints(This,position,length,breakpoints) (This)->lpVtbl->SetLineBreakpoints(This,position,length,breakpoints)
#define IDWriteTextAnalysisSink1_SetBidiLevel(This,position,length,explicitLevel,resolvedLevel) (This)->lpVtbl->SetBidiLevel(This,position,length,explicitLevel,resolvedLevel)
#define IDWriteTextAnalysisSink1_SetNumberSubstitution(This,position,length,substitution) (This)->lpVtbl->SetNumberSubstitution(This,position,length,substitution)
/*** IDWriteTextAnalysisSink1 methods ***/
#define IDWriteTextAnalysisSink1_SetGlyphOrientation(This,pos,length,angle,adjusted_bidilevel,is_sideways,is_rtl) (This)->lpVtbl->SetGlyphOrientation(This,pos,length,angle,adjusted_bidilevel,is_sideways,is_rtl)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextAnalysisSink1_QueryInterface(IDWriteTextAnalysisSink1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextAnalysisSink1_AddRef(IDWriteTextAnalysisSink1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextAnalysisSink1_Release(IDWriteTextAnalysisSink1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextAnalysisSink methods ***/
static inline HRESULT IDWriteTextAnalysisSink1_SetScriptAnalysis(IDWriteTextAnalysisSink1* This,UINT32 position,UINT32 length,const DWRITE_SCRIPT_ANALYSIS *scriptanalysis) {
    return This->lpVtbl->SetScriptAnalysis(This,position,length,scriptanalysis);
}
static inline HRESULT IDWriteTextAnalysisSink1_SetLineBreakpoints(IDWriteTextAnalysisSink1* This,UINT32 position,UINT32 length,const DWRITE_LINE_BREAKPOINT *breakpoints) {
    return This->lpVtbl->SetLineBreakpoints(This,position,length,breakpoints);
}
static inline HRESULT IDWriteTextAnalysisSink1_SetBidiLevel(IDWriteTextAnalysisSink1* This,UINT32 position,UINT32 length,UINT8 explicitLevel,UINT8 resolvedLevel) {
    return This->lpVtbl->SetBidiLevel(This,position,length,explicitLevel,resolvedLevel);
}
static inline HRESULT IDWriteTextAnalysisSink1_SetNumberSubstitution(IDWriteTextAnalysisSink1* This,UINT32 position,UINT32 length,IDWriteNumberSubstitution *substitution) {
    return This->lpVtbl->SetNumberSubstitution(This,position,length,substitution);
}
/*** IDWriteTextAnalysisSink1 methods ***/
static inline HRESULT IDWriteTextAnalysisSink1_SetGlyphOrientation(IDWriteTextAnalysisSink1* This,UINT32 pos,UINT32 length,DWRITE_GLYPH_ORIENTATION_ANGLE angle,UINT8 adjusted_bidilevel,WINBOOL is_sideways,WINBOOL is_rtl) {
    return This->lpVtbl->SetGlyphOrientation(This,pos,length,angle,adjusted_bidilevel,is_sideways,is_rtl);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextAnalysisSink1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextLayout1 interface
 */
#ifndef __IDWriteTextLayout1_INTERFACE_DEFINED__
#define __IDWriteTextLayout1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextLayout1, 0x9064d822, 0x80a7, 0x465c, 0xa9,0x86, 0xdf,0x65,0xf7,0x8b,0x8f,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9064d822-80a7-465c-a986-df65f78b8feb")
IDWriteTextLayout1 : public IDWriteTextLayout
{
    virtual HRESULT STDMETHODCALLTYPE SetPairKerning(
        WINBOOL is_pairkerning_enabled,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPairKerning(
        UINT32 position,
        WINBOOL *is_pairkerning_enabled,
        DWRITE_TEXT_RANGE *range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCharacterSpacing(
        FLOAT leading_spacing,
        FLOAT trailing_spacing,
        FLOAT minimum_advance_width,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCharacterSpacing(
        UINT32 position,
        FLOAT *leading_spacing,
        FLOAT *trailing_spacing,
        FLOAT *minimum_advance_width,
        DWRITE_TEXT_RANGE *range = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextLayout1, 0x9064d822, 0x80a7, 0x465c, 0xa9,0x86, 0xdf,0x65,0xf7,0x8b,0x8f,0xeb)
#endif
#else
typedef struct IDWriteTextLayout1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextLayout1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextLayout1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextLayout1 *This);

    /*** IDWriteTextFormat methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTextAlignment)(
        IDWriteTextLayout1 *This,
        DWRITE_TEXT_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetParagraphAlignment)(
        IDWriteTextLayout1 *This,
        DWRITE_PARAGRAPH_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetWordWrapping)(
        IDWriteTextLayout1 *This,
        DWRITE_WORD_WRAPPING wrapping);

    HRESULT (STDMETHODCALLTYPE *SetReadingDirection)(
        IDWriteTextLayout1 *This,
        DWRITE_READING_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetFlowDirection)(
        IDWriteTextLayout1 *This,
        DWRITE_FLOW_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetIncrementalTabStop)(
        IDWriteTextLayout1 *This,
        FLOAT tabstop);

    HRESULT (STDMETHODCALLTYPE *SetTrimming)(
        IDWriteTextLayout1 *This,
        const DWRITE_TRIMMING *trimming,
        IDWriteInlineObject *trimming_sign);

    HRESULT (STDMETHODCALLTYPE *SetLineSpacing)(
        IDWriteTextLayout1 *This,
        DWRITE_LINE_SPACING_METHOD spacing,
        FLOAT line_spacing,
        FLOAT baseline);

    DWRITE_TEXT_ALIGNMENT (STDMETHODCALLTYPE *GetTextAlignment)(
        IDWriteTextLayout1 *This);

    DWRITE_PARAGRAPH_ALIGNMENT (STDMETHODCALLTYPE *GetParagraphAlignment)(
        IDWriteTextLayout1 *This);

    DWRITE_WORD_WRAPPING (STDMETHODCALLTYPE *GetWordWrapping)(
        IDWriteTextLayout1 *This);

    DWRITE_READING_DIRECTION (STDMETHODCALLTYPE *GetReadingDirection)(
        IDWriteTextLayout1 *This);

    DWRITE_FLOW_DIRECTION (STDMETHODCALLTYPE *GetFlowDirection)(
        IDWriteTextLayout1 *This);

    FLOAT (STDMETHODCALLTYPE *GetIncrementalTabStop)(
        IDWriteTextLayout1 *This);

    HRESULT (STDMETHODCALLTYPE *GetTrimming)(
        IDWriteTextLayout1 *This,
        DWRITE_TRIMMING *options,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *GetLineSpacing)(
        IDWriteTextLayout1 *This,
        DWRITE_LINE_SPACING_METHOD *method,
        FLOAT *spacing,
        FLOAT *baseline);

    HRESULT (STDMETHODCALLTYPE *GetFontCollection)(
        IDWriteTextLayout1 *This,
        IDWriteFontCollection **collection);

    UINT32 (STDMETHODCALLTYPE *GetFontFamilyNameLength)(
        IDWriteTextLayout1 *This);

    HRESULT (STDMETHODCALLTYPE *GetFontFamilyName)(
        IDWriteTextLayout1 *This,
        WCHAR *name,
        UINT32 size);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetFontWeight)(
        IDWriteTextLayout1 *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetFontStyle)(
        IDWriteTextLayout1 *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetFontStretch)(
        IDWriteTextLayout1 *This);

    FLOAT (STDMETHODCALLTYPE *GetFontSize)(
        IDWriteTextLayout1 *This);

    UINT32 (STDMETHODCALLTYPE *GetLocaleNameLength)(
        IDWriteTextLayout1 *This);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteTextLayout1 *This,
        WCHAR *name,
        UINT32 size);

    /*** IDWriteTextLayout methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMaxWidth)(
        IDWriteTextLayout1 *This,
        FLOAT maxWidth);

    HRESULT (STDMETHODCALLTYPE *SetMaxHeight)(
        IDWriteTextLayout1 *This,
        FLOAT maxHeight);

    HRESULT (STDMETHODCALLTYPE *SetFontCollection)(
        IDWriteTextLayout1 *This,
        IDWriteFontCollection *collection,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontFamilyName)(
        IDWriteTextLayout1 *This,
        const WCHAR *name,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontWeight)(
        IDWriteTextLayout1 *This,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontStyle)(
        IDWriteTextLayout1 *This,
        DWRITE_FONT_STYLE style,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontStretch)(
        IDWriteTextLayout1 *This,
        DWRITE_FONT_STRETCH stretch,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontSize)(
        IDWriteTextLayout1 *This,
        FLOAT size,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetUnderline)(
        IDWriteTextLayout1 *This,
        WINBOOL underline,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetStrikethrough)(
        IDWriteTextLayout1 *This,
        WINBOOL strikethrough,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetDrawingEffect)(
        IDWriteTextLayout1 *This,
        IUnknown *effect,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetInlineObject)(
        IDWriteTextLayout1 *This,
        IDWriteInlineObject *object,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetTypography)(
        IDWriteTextLayout1 *This,
        IDWriteTypography *typography,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetLocaleName)(
        IDWriteTextLayout1 *This,
        const WCHAR *locale,
        DWRITE_TEXT_RANGE range);

    FLOAT (STDMETHODCALLTYPE *GetMaxWidth)(
        IDWriteTextLayout1 *This);

    FLOAT (STDMETHODCALLTYPE *GetMaxHeight)(
        IDWriteTextLayout1 *This);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontCollection)(
        IDWriteTextLayout1 *This,
        UINT32 pos,
        IDWriteFontCollection **collection,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontFamilyNameLength)(
        IDWriteTextLayout1 *This,
        UINT32 pos,
        UINT32 *len,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontFamilyName)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontWeight)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        DWRITE_FONT_WEIGHT *weight,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontStyle)(
        IDWriteTextLayout1 *This,
        UINT32 currentPosition,
        DWRITE_FONT_STYLE *style,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontStretch)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        DWRITE_FONT_STRETCH *stretch,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontSize)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        FLOAT *size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetUnderline)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        WINBOOL *has_underline,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetStrikethrough)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        WINBOOL *has_strikethrough,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetDrawingEffect)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        IUnknown **effect,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetInlineObject)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        IDWriteInlineObject **object,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetTypography)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        IDWriteTypography **typography,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetLocaleNameLength)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        UINT32 *length,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetLocaleName)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *Draw)(
        IDWriteTextLayout1 *This,
        void *context,
        IDWriteTextRenderer *renderer,
        FLOAT originX,
        FLOAT originY);

    HRESULT (STDMETHODCALLTYPE *GetLineMetrics)(
        IDWriteTextLayout1 *This,
        DWRITE_LINE_METRICS *metrics,
        UINT32 max_count,
        UINT32 *actual_count);

    HRESULT (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteTextLayout1 *This,
        DWRITE_TEXT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetOverhangMetrics)(
        IDWriteTextLayout1 *This,
        DWRITE_OVERHANG_METRICS *overhangs);

    HRESULT (STDMETHODCALLTYPE *GetClusterMetrics)(
        IDWriteTextLayout1 *This,
        DWRITE_CLUSTER_METRICS *metrics,
        UINT32 max_count,
        UINT32 *act_count);

    HRESULT (STDMETHODCALLTYPE *DetermineMinWidth)(
        IDWriteTextLayout1 *This,
        FLOAT *min_width);

    HRESULT (STDMETHODCALLTYPE *HitTestPoint)(
        IDWriteTextLayout1 *This,
        FLOAT pointX,
        FLOAT pointY,
        WINBOOL *is_trailinghit,
        WINBOOL *is_inside,
        DWRITE_HIT_TEST_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HitTestTextPosition)(
        IDWriteTextLayout1 *This,
        UINT32 textPosition,
        WINBOOL is_trailinghit,
        FLOAT *pointX,
        FLOAT *pointY,
        DWRITE_HIT_TEST_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HitTestTextRange)(
        IDWriteTextLayout1 *This,
        UINT32 textPosition,
        UINT32 textLength,
        FLOAT originX,
        FLOAT originY,
        DWRITE_HIT_TEST_METRICS *metrics,
        UINT32 max_metricscount,
        UINT32 *actual_metricscount);

    /*** IDWriteTextLayout1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPairKerning)(
        IDWriteTextLayout1 *This,
        WINBOOL is_pairkerning_enabled,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *GetPairKerning)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        WINBOOL *is_pairkerning_enabled,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *SetCharacterSpacing)(
        IDWriteTextLayout1 *This,
        FLOAT leading_spacing,
        FLOAT trailing_spacing,
        FLOAT minimum_advance_width,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *GetCharacterSpacing)(
        IDWriteTextLayout1 *This,
        UINT32 position,
        FLOAT *leading_spacing,
        FLOAT *trailing_spacing,
        FLOAT *minimum_advance_width,
        DWRITE_TEXT_RANGE *range);

    END_INTERFACE
} IDWriteTextLayout1Vtbl;

interface IDWriteTextLayout1 {
    CONST_VTBL IDWriteTextLayout1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextLayout1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextLayout1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextLayout1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextFormat methods ***/
#define IDWriteTextLayout1_SetTextAlignment(This,alignment) (This)->lpVtbl->SetTextAlignment(This,alignment)
#define IDWriteTextLayout1_SetParagraphAlignment(This,alignment) (This)->lpVtbl->SetParagraphAlignment(This,alignment)
#define IDWriteTextLayout1_SetWordWrapping(This,wrapping) (This)->lpVtbl->SetWordWrapping(This,wrapping)
#define IDWriteTextLayout1_SetReadingDirection(This,direction) (This)->lpVtbl->SetReadingDirection(This,direction)
#define IDWriteTextLayout1_SetFlowDirection(This,direction) (This)->lpVtbl->SetFlowDirection(This,direction)
#define IDWriteTextLayout1_SetIncrementalTabStop(This,tabstop) (This)->lpVtbl->SetIncrementalTabStop(This,tabstop)
#define IDWriteTextLayout1_SetTrimming(This,trimming,trimming_sign) (This)->lpVtbl->SetTrimming(This,trimming,trimming_sign)
#define IDWriteTextLayout1_SetLineSpacing(This,spacing,line_spacing,baseline) (This)->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline)
#define IDWriteTextLayout1_GetTextAlignment(This) (This)->lpVtbl->GetTextAlignment(This)
#define IDWriteTextLayout1_GetParagraphAlignment(This) (This)->lpVtbl->GetParagraphAlignment(This)
#define IDWriteTextLayout1_GetWordWrapping(This) (This)->lpVtbl->GetWordWrapping(This)
#define IDWriteTextLayout1_GetReadingDirection(This) (This)->lpVtbl->GetReadingDirection(This)
#define IDWriteTextLayout1_GetFlowDirection(This) (This)->lpVtbl->GetFlowDirection(This)
#define IDWriteTextLayout1_GetIncrementalTabStop(This) (This)->lpVtbl->GetIncrementalTabStop(This)
#define IDWriteTextLayout1_GetTrimming(This,options,trimming_sign) (This)->lpVtbl->GetTrimming(This,options,trimming_sign)
#define IDWriteTextLayout1_GetLineSpacing(This,method,spacing,baseline) (This)->lpVtbl->GetLineSpacing(This,method,spacing,baseline)
/*** IDWriteTextLayout methods ***/
#define IDWriteTextLayout1_SetMaxWidth(This,maxWidth) (This)->lpVtbl->SetMaxWidth(This,maxWidth)
#define IDWriteTextLayout1_SetMaxHeight(This,maxHeight) (This)->lpVtbl->SetMaxHeight(This,maxHeight)
#define IDWriteTextLayout1_SetFontCollection(This,collection,range) (This)->lpVtbl->SetFontCollection(This,collection,range)
#define IDWriteTextLayout1_SetFontFamilyName(This,name,range) (This)->lpVtbl->SetFontFamilyName(This,name,range)
#define IDWriteTextLayout1_SetFontWeight(This,weight,range) (This)->lpVtbl->SetFontWeight(This,weight,range)
#define IDWriteTextLayout1_SetFontStyle(This,style,range) (This)->lpVtbl->SetFontStyle(This,style,range)
#define IDWriteTextLayout1_SetFontStretch(This,stretch,range) (This)->lpVtbl->SetFontStretch(This,stretch,range)
#define IDWriteTextLayout1_SetFontSize(This,size,range) (This)->lpVtbl->SetFontSize(This,size,range)
#define IDWriteTextLayout1_SetUnderline(This,underline,range) (This)->lpVtbl->SetUnderline(This,underline,range)
#define IDWriteTextLayout1_SetStrikethrough(This,strikethrough,range) (This)->lpVtbl->SetStrikethrough(This,strikethrough,range)
#define IDWriteTextLayout1_SetDrawingEffect(This,effect,range) (This)->lpVtbl->SetDrawingEffect(This,effect,range)
#define IDWriteTextLayout1_SetInlineObject(This,object,range) (This)->lpVtbl->SetInlineObject(This,object,range)
#define IDWriteTextLayout1_SetTypography(This,typography,range) (This)->lpVtbl->SetTypography(This,typography,range)
#define IDWriteTextLayout1_SetLocaleName(This,locale,range) (This)->lpVtbl->SetLocaleName(This,locale,range)
#define IDWriteTextLayout1_GetMaxWidth(This) (This)->lpVtbl->GetMaxWidth(This)
#define IDWriteTextLayout1_GetMaxHeight(This) (This)->lpVtbl->GetMaxHeight(This)
#define IDWriteTextLayout1_GetFontCollection(This,pos,collection,range) (This)->lpVtbl->IDWriteTextLayout_GetFontCollection(This,pos,collection,range)
#define IDWriteTextLayout1_GetFontFamilyNameLength(This,pos,len,range) (This)->lpVtbl->IDWriteTextLayout_GetFontFamilyNameLength(This,pos,len,range)
#define IDWriteTextLayout1_GetFontFamilyName(This,position,name,name_size,range) (This)->lpVtbl->IDWriteTextLayout_GetFontFamilyName(This,position,name,name_size,range)
#define IDWriteTextLayout1_GetFontWeight(This,position,weight,range) (This)->lpVtbl->IDWriteTextLayout_GetFontWeight(This,position,weight,range)
#define IDWriteTextLayout1_GetFontStyle(This,currentPosition,style,range) (This)->lpVtbl->IDWriteTextLayout_GetFontStyle(This,currentPosition,style,range)
#define IDWriteTextLayout1_GetFontStretch(This,position,stretch,range) (This)->lpVtbl->IDWriteTextLayout_GetFontStretch(This,position,stretch,range)
#define IDWriteTextLayout1_GetFontSize(This,position,size,range) (This)->lpVtbl->IDWriteTextLayout_GetFontSize(This,position,size,range)
#define IDWriteTextLayout1_GetUnderline(This,position,has_underline,range) (This)->lpVtbl->GetUnderline(This,position,has_underline,range)
#define IDWriteTextLayout1_GetStrikethrough(This,position,has_strikethrough,range) (This)->lpVtbl->GetStrikethrough(This,position,has_strikethrough,range)
#define IDWriteTextLayout1_GetDrawingEffect(This,position,effect,range) (This)->lpVtbl->GetDrawingEffect(This,position,effect,range)
#define IDWriteTextLayout1_GetInlineObject(This,position,object,range) (This)->lpVtbl->GetInlineObject(This,position,object,range)
#define IDWriteTextLayout1_GetTypography(This,position,typography,range) (This)->lpVtbl->GetTypography(This,position,typography,range)
#define IDWriteTextLayout1_GetLocaleNameLength(This,position,length,range) (This)->lpVtbl->IDWriteTextLayout_GetLocaleNameLength(This,position,length,range)
#define IDWriteTextLayout1_GetLocaleName(This,position,name,name_size,range) (This)->lpVtbl->IDWriteTextLayout_GetLocaleName(This,position,name,name_size,range)
#define IDWriteTextLayout1_Draw(This,context,renderer,originX,originY) (This)->lpVtbl->Draw(This,context,renderer,originX,originY)
#define IDWriteTextLayout1_GetLineMetrics(This,metrics,max_count,actual_count) (This)->lpVtbl->GetLineMetrics(This,metrics,max_count,actual_count)
#define IDWriteTextLayout1_GetMetrics(This,metrics) (This)->lpVtbl->GetMetrics(This,metrics)
#define IDWriteTextLayout1_GetOverhangMetrics(This,overhangs) (This)->lpVtbl->GetOverhangMetrics(This,overhangs)
#define IDWriteTextLayout1_GetClusterMetrics(This,metrics,max_count,act_count) (This)->lpVtbl->GetClusterMetrics(This,metrics,max_count,act_count)
#define IDWriteTextLayout1_DetermineMinWidth(This,min_width) (This)->lpVtbl->DetermineMinWidth(This,min_width)
#define IDWriteTextLayout1_HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics) (This)->lpVtbl->HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics)
#define IDWriteTextLayout1_HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics) (This)->lpVtbl->HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics)
#define IDWriteTextLayout1_HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount) (This)->lpVtbl->HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount)
/*** IDWriteTextLayout1 methods ***/
#define IDWriteTextLayout1_SetPairKerning(This,is_pairkerning_enabled,range) (This)->lpVtbl->SetPairKerning(This,is_pairkerning_enabled,range)
#define IDWriteTextLayout1_GetPairKerning(This,position,is_pairkerning_enabled,range) (This)->lpVtbl->GetPairKerning(This,position,is_pairkerning_enabled,range)
#define IDWriteTextLayout1_SetCharacterSpacing(This,leading_spacing,trailing_spacing,minimum_advance_width,range) (This)->lpVtbl->SetCharacterSpacing(This,leading_spacing,trailing_spacing,minimum_advance_width,range)
#define IDWriteTextLayout1_GetCharacterSpacing(This,position,leading_spacing,trailing_spacing,minimum_advance_width,range) (This)->lpVtbl->GetCharacterSpacing(This,position,leading_spacing,trailing_spacing,minimum_advance_width,range)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextLayout1_QueryInterface(IDWriteTextLayout1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextLayout1_AddRef(IDWriteTextLayout1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextLayout1_Release(IDWriteTextLayout1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextFormat methods ***/
static inline HRESULT IDWriteTextLayout1_SetTextAlignment(IDWriteTextLayout1* This,DWRITE_TEXT_ALIGNMENT alignment) {
    return This->lpVtbl->SetTextAlignment(This,alignment);
}
static inline HRESULT IDWriteTextLayout1_SetParagraphAlignment(IDWriteTextLayout1* This,DWRITE_PARAGRAPH_ALIGNMENT alignment) {
    return This->lpVtbl->SetParagraphAlignment(This,alignment);
}
static inline HRESULT IDWriteTextLayout1_SetWordWrapping(IDWriteTextLayout1* This,DWRITE_WORD_WRAPPING wrapping) {
    return This->lpVtbl->SetWordWrapping(This,wrapping);
}
static inline HRESULT IDWriteTextLayout1_SetReadingDirection(IDWriteTextLayout1* This,DWRITE_READING_DIRECTION direction) {
    return This->lpVtbl->SetReadingDirection(This,direction);
}
static inline HRESULT IDWriteTextLayout1_SetFlowDirection(IDWriteTextLayout1* This,DWRITE_FLOW_DIRECTION direction) {
    return This->lpVtbl->SetFlowDirection(This,direction);
}
static inline HRESULT IDWriteTextLayout1_SetIncrementalTabStop(IDWriteTextLayout1* This,FLOAT tabstop) {
    return This->lpVtbl->SetIncrementalTabStop(This,tabstop);
}
static inline HRESULT IDWriteTextLayout1_SetTrimming(IDWriteTextLayout1* This,const DWRITE_TRIMMING *trimming,IDWriteInlineObject *trimming_sign) {
    return This->lpVtbl->SetTrimming(This,trimming,trimming_sign);
}
static inline HRESULT IDWriteTextLayout1_SetLineSpacing(IDWriteTextLayout1* This,DWRITE_LINE_SPACING_METHOD spacing,FLOAT line_spacing,FLOAT baseline) {
    return This->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline);
}
static inline DWRITE_TEXT_ALIGNMENT IDWriteTextLayout1_GetTextAlignment(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetTextAlignment(This);
}
static inline DWRITE_PARAGRAPH_ALIGNMENT IDWriteTextLayout1_GetParagraphAlignment(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetParagraphAlignment(This);
}
static inline DWRITE_WORD_WRAPPING IDWriteTextLayout1_GetWordWrapping(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetWordWrapping(This);
}
static inline DWRITE_READING_DIRECTION IDWriteTextLayout1_GetReadingDirection(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetReadingDirection(This);
}
static inline DWRITE_FLOW_DIRECTION IDWriteTextLayout1_GetFlowDirection(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetFlowDirection(This);
}
static inline FLOAT IDWriteTextLayout1_GetIncrementalTabStop(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetIncrementalTabStop(This);
}
static inline HRESULT IDWriteTextLayout1_GetTrimming(IDWriteTextLayout1* This,DWRITE_TRIMMING *options,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->GetTrimming(This,options,trimming_sign);
}
static inline HRESULT IDWriteTextLayout1_GetLineSpacing(IDWriteTextLayout1* This,DWRITE_LINE_SPACING_METHOD *method,FLOAT *spacing,FLOAT *baseline) {
    return This->lpVtbl->GetLineSpacing(This,method,spacing,baseline);
}
/*** IDWriteTextLayout methods ***/
static inline HRESULT IDWriteTextLayout1_SetMaxWidth(IDWriteTextLayout1* This,FLOAT maxWidth) {
    return This->lpVtbl->SetMaxWidth(This,maxWidth);
}
static inline HRESULT IDWriteTextLayout1_SetMaxHeight(IDWriteTextLayout1* This,FLOAT maxHeight) {
    return This->lpVtbl->SetMaxHeight(This,maxHeight);
}
static inline HRESULT IDWriteTextLayout1_SetFontCollection(IDWriteTextLayout1* This,IDWriteFontCollection *collection,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontCollection(This,collection,range);
}
static inline HRESULT IDWriteTextLayout1_SetFontFamilyName(IDWriteTextLayout1* This,const WCHAR *name,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontFamilyName(This,name,range);
}
static inline HRESULT IDWriteTextLayout1_SetFontWeight(IDWriteTextLayout1* This,DWRITE_FONT_WEIGHT weight,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontWeight(This,weight,range);
}
static inline HRESULT IDWriteTextLayout1_SetFontStyle(IDWriteTextLayout1* This,DWRITE_FONT_STYLE style,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontStyle(This,style,range);
}
static inline HRESULT IDWriteTextLayout1_SetFontStretch(IDWriteTextLayout1* This,DWRITE_FONT_STRETCH stretch,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontStretch(This,stretch,range);
}
static inline HRESULT IDWriteTextLayout1_SetFontSize(IDWriteTextLayout1* This,FLOAT size,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontSize(This,size,range);
}
static inline HRESULT IDWriteTextLayout1_SetUnderline(IDWriteTextLayout1* This,WINBOOL underline,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetUnderline(This,underline,range);
}
static inline HRESULT IDWriteTextLayout1_SetStrikethrough(IDWriteTextLayout1* This,WINBOOL strikethrough,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetStrikethrough(This,strikethrough,range);
}
static inline HRESULT IDWriteTextLayout1_SetDrawingEffect(IDWriteTextLayout1* This,IUnknown *effect,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetDrawingEffect(This,effect,range);
}
static inline HRESULT IDWriteTextLayout1_SetInlineObject(IDWriteTextLayout1* This,IDWriteInlineObject *object,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetInlineObject(This,object,range);
}
static inline HRESULT IDWriteTextLayout1_SetTypography(IDWriteTextLayout1* This,IDWriteTypography *typography,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetTypography(This,typography,range);
}
static inline HRESULT IDWriteTextLayout1_SetLocaleName(IDWriteTextLayout1* This,const WCHAR *locale,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetLocaleName(This,locale,range);
}
static inline FLOAT IDWriteTextLayout1_GetMaxWidth(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetMaxWidth(This);
}
static inline FLOAT IDWriteTextLayout1_GetMaxHeight(IDWriteTextLayout1* This) {
    return This->lpVtbl->GetMaxHeight(This);
}
static inline HRESULT IDWriteTextLayout1_GetFontCollection(IDWriteTextLayout1* This,UINT32 pos,IDWriteFontCollection **collection,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontCollection(This,pos,collection,range);
}
static inline HRESULT IDWriteTextLayout1_GetFontFamilyNameLength(IDWriteTextLayout1* This,UINT32 pos,UINT32 *len,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontFamilyNameLength(This,pos,len,range);
}
static inline HRESULT IDWriteTextLayout1_GetFontFamilyName(IDWriteTextLayout1* This,UINT32 position,WCHAR *name,UINT32 name_size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontFamilyName(This,position,name,name_size,range);
}
static inline HRESULT IDWriteTextLayout1_GetFontWeight(IDWriteTextLayout1* This,UINT32 position,DWRITE_FONT_WEIGHT *weight,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontWeight(This,position,weight,range);
}
static inline HRESULT IDWriteTextLayout1_GetFontStyle(IDWriteTextLayout1* This,UINT32 currentPosition,DWRITE_FONT_STYLE *style,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontStyle(This,currentPosition,style,range);
}
static inline HRESULT IDWriteTextLayout1_GetFontStretch(IDWriteTextLayout1* This,UINT32 position,DWRITE_FONT_STRETCH *stretch,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontStretch(This,position,stretch,range);
}
static inline HRESULT IDWriteTextLayout1_GetFontSize(IDWriteTextLayout1* This,UINT32 position,FLOAT *size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontSize(This,position,size,range);
}
static inline HRESULT IDWriteTextLayout1_GetUnderline(IDWriteTextLayout1* This,UINT32 position,WINBOOL *has_underline,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetUnderline(This,position,has_underline,range);
}
static inline HRESULT IDWriteTextLayout1_GetStrikethrough(IDWriteTextLayout1* This,UINT32 position,WINBOOL *has_strikethrough,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetStrikethrough(This,position,has_strikethrough,range);
}
static inline HRESULT IDWriteTextLayout1_GetDrawingEffect(IDWriteTextLayout1* This,UINT32 position,IUnknown **effect,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetDrawingEffect(This,position,effect,range);
}
static inline HRESULT IDWriteTextLayout1_GetInlineObject(IDWriteTextLayout1* This,UINT32 position,IDWriteInlineObject **object,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetInlineObject(This,position,object,range);
}
static inline HRESULT IDWriteTextLayout1_GetTypography(IDWriteTextLayout1* This,UINT32 position,IDWriteTypography **typography,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetTypography(This,position,typography,range);
}
static inline HRESULT IDWriteTextLayout1_GetLocaleNameLength(IDWriteTextLayout1* This,UINT32 position,UINT32 *length,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetLocaleNameLength(This,position,length,range);
}
static inline HRESULT IDWriteTextLayout1_GetLocaleName(IDWriteTextLayout1* This,UINT32 position,WCHAR *name,UINT32 name_size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetLocaleName(This,position,name,name_size,range);
}
static inline HRESULT IDWriteTextLayout1_Draw(IDWriteTextLayout1* This,void *context,IDWriteTextRenderer *renderer,FLOAT originX,FLOAT originY) {
    return This->lpVtbl->Draw(This,context,renderer,originX,originY);
}
static inline HRESULT IDWriteTextLayout1_GetLineMetrics(IDWriteTextLayout1* This,DWRITE_LINE_METRICS *metrics,UINT32 max_count,UINT32 *actual_count) {
    return This->lpVtbl->GetLineMetrics(This,metrics,max_count,actual_count);
}
static inline HRESULT IDWriteTextLayout1_GetMetrics(IDWriteTextLayout1* This,DWRITE_TEXT_METRICS *metrics) {
    return This->lpVtbl->GetMetrics(This,metrics);
}
static inline HRESULT IDWriteTextLayout1_GetOverhangMetrics(IDWriteTextLayout1* This,DWRITE_OVERHANG_METRICS *overhangs) {
    return This->lpVtbl->GetOverhangMetrics(This,overhangs);
}
static inline HRESULT IDWriteTextLayout1_GetClusterMetrics(IDWriteTextLayout1* This,DWRITE_CLUSTER_METRICS *metrics,UINT32 max_count,UINT32 *act_count) {
    return This->lpVtbl->GetClusterMetrics(This,metrics,max_count,act_count);
}
static inline HRESULT IDWriteTextLayout1_DetermineMinWidth(IDWriteTextLayout1* This,FLOAT *min_width) {
    return This->lpVtbl->DetermineMinWidth(This,min_width);
}
static inline HRESULT IDWriteTextLayout1_HitTestPoint(IDWriteTextLayout1* This,FLOAT pointX,FLOAT pointY,WINBOOL *is_trailinghit,WINBOOL *is_inside,DWRITE_HIT_TEST_METRICS *metrics) {
    return This->lpVtbl->HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics);
}
static inline HRESULT IDWriteTextLayout1_HitTestTextPosition(IDWriteTextLayout1* This,UINT32 textPosition,WINBOOL is_trailinghit,FLOAT *pointX,FLOAT *pointY,DWRITE_HIT_TEST_METRICS *metrics) {
    return This->lpVtbl->HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics);
}
static inline HRESULT IDWriteTextLayout1_HitTestTextRange(IDWriteTextLayout1* This,UINT32 textPosition,UINT32 textLength,FLOAT originX,FLOAT originY,DWRITE_HIT_TEST_METRICS *metrics,UINT32 max_metricscount,UINT32 *actual_metricscount) {
    return This->lpVtbl->HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount);
}
/*** IDWriteTextLayout1 methods ***/
static inline HRESULT IDWriteTextLayout1_SetPairKerning(IDWriteTextLayout1* This,WINBOOL is_pairkerning_enabled,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetPairKerning(This,is_pairkerning_enabled,range);
}
static inline HRESULT IDWriteTextLayout1_GetPairKerning(IDWriteTextLayout1* This,UINT32 position,WINBOOL *is_pairkerning_enabled,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetPairKerning(This,position,is_pairkerning_enabled,range);
}
static inline HRESULT IDWriteTextLayout1_SetCharacterSpacing(IDWriteTextLayout1* This,FLOAT leading_spacing,FLOAT trailing_spacing,FLOAT minimum_advance_width,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetCharacterSpacing(This,leading_spacing,trailing_spacing,minimum_advance_width,range);
}
static inline HRESULT IDWriteTextLayout1_GetCharacterSpacing(IDWriteTextLayout1* This,UINT32 position,FLOAT *leading_spacing,FLOAT *trailing_spacing,FLOAT *minimum_advance_width,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetCharacterSpacing(This,position,leading_spacing,trailing_spacing,minimum_advance_width,range);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextLayout1_INTERFACE_DEFINED__ */

typedef enum DWRITE_TEXT_ANTIALIAS_MODE {
    DWRITE_TEXT_ANTIALIAS_MODE_CLEARTYPE = 0,
    DWRITE_TEXT_ANTIALIAS_MODE_GRAYSCALE = 1
} DWRITE_TEXT_ANTIALIAS_MODE;
/*****************************************************************************
 * IDWriteBitmapRenderTarget1 interface
 */
#ifndef __IDWriteBitmapRenderTarget1_INTERFACE_DEFINED__
#define __IDWriteBitmapRenderTarget1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteBitmapRenderTarget1, 0x791e8298, 0x3ef3, 0x4230, 0x98,0x80, 0xc9,0xbd,0xec,0xc4,0x20,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("791e8298-3ef3-4230-9880-c9bdecc42064")
IDWriteBitmapRenderTarget1 : public IDWriteBitmapRenderTarget
{
    virtual DWRITE_TEXT_ANTIALIAS_MODE STDMETHODCALLTYPE GetTextAntialiasMode(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTextAntialiasMode(
        DWRITE_TEXT_ANTIALIAS_MODE mode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteBitmapRenderTarget1, 0x791e8298, 0x3ef3, 0x4230, 0x98,0x80, 0xc9,0xbd,0xec,0xc4,0x20,0x64)
#endif
#else
typedef struct IDWriteBitmapRenderTarget1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteBitmapRenderTarget1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteBitmapRenderTarget1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteBitmapRenderTarget1 *This);

    /*** IDWriteBitmapRenderTarget methods ***/
    HRESULT (STDMETHODCALLTYPE *DrawGlyphRun)(
        IDWriteBitmapRenderTarget1 *This,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        DWRITE_MEASURING_MODE measuring_mode,
        const DWRITE_GLYPH_RUN *glyph_run,
        IDWriteRenderingParams *params,
        COLORREF textColor,
        RECT *blackbox_rect);

    HDC (STDMETHODCALLTYPE *GetMemoryDC)(
        IDWriteBitmapRenderTarget1 *This);

    FLOAT (STDMETHODCALLTYPE *GetPixelsPerDip)(
        IDWriteBitmapRenderTarget1 *This);

    HRESULT (STDMETHODCALLTYPE *SetPixelsPerDip)(
        IDWriteBitmapRenderTarget1 *This,
        FLOAT pixels_per_dip);

    HRESULT (STDMETHODCALLTYPE *GetCurrentTransform)(
        IDWriteBitmapRenderTarget1 *This,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *SetCurrentTransform)(
        IDWriteBitmapRenderTarget1 *This,
        const DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IDWriteBitmapRenderTarget1 *This,
        SIZE *size);

    HRESULT (STDMETHODCALLTYPE *Resize)(
        IDWriteBitmapRenderTarget1 *This,
        UINT32 width,
        UINT32 height);

    /*** IDWriteBitmapRenderTarget1 methods ***/
    DWRITE_TEXT_ANTIALIAS_MODE (STDMETHODCALLTYPE *GetTextAntialiasMode)(
        IDWriteBitmapRenderTarget1 *This);

    HRESULT (STDMETHODCALLTYPE *SetTextAntialiasMode)(
        IDWriteBitmapRenderTarget1 *This,
        DWRITE_TEXT_ANTIALIAS_MODE mode);

    END_INTERFACE
} IDWriteBitmapRenderTarget1Vtbl;

interface IDWriteBitmapRenderTarget1 {
    CONST_VTBL IDWriteBitmapRenderTarget1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteBitmapRenderTarget1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteBitmapRenderTarget1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteBitmapRenderTarget1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteBitmapRenderTarget methods ***/
#define IDWriteBitmapRenderTarget1_DrawGlyphRun(This,baselineOriginX,baselineOriginY,measuring_mode,glyph_run,params,textColor,blackbox_rect) (This)->lpVtbl->DrawGlyphRun(This,baselineOriginX,baselineOriginY,measuring_mode,glyph_run,params,textColor,blackbox_rect)
#define IDWriteBitmapRenderTarget1_GetMemoryDC(This) (This)->lpVtbl->GetMemoryDC(This)
#define IDWriteBitmapRenderTarget1_GetPixelsPerDip(This) (This)->lpVtbl->GetPixelsPerDip(This)
#define IDWriteBitmapRenderTarget1_SetPixelsPerDip(This,pixels_per_dip) (This)->lpVtbl->SetPixelsPerDip(This,pixels_per_dip)
#define IDWriteBitmapRenderTarget1_GetCurrentTransform(This,transform) (This)->lpVtbl->GetCurrentTransform(This,transform)
#define IDWriteBitmapRenderTarget1_SetCurrentTransform(This,transform) (This)->lpVtbl->SetCurrentTransform(This,transform)
#define IDWriteBitmapRenderTarget1_GetSize(This,size) (This)->lpVtbl->GetSize(This,size)
#define IDWriteBitmapRenderTarget1_Resize(This,width,height) (This)->lpVtbl->Resize(This,width,height)
/*** IDWriteBitmapRenderTarget1 methods ***/
#define IDWriteBitmapRenderTarget1_GetTextAntialiasMode(This) (This)->lpVtbl->GetTextAntialiasMode(This)
#define IDWriteBitmapRenderTarget1_SetTextAntialiasMode(This,mode) (This)->lpVtbl->SetTextAntialiasMode(This,mode)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteBitmapRenderTarget1_QueryInterface(IDWriteBitmapRenderTarget1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteBitmapRenderTarget1_AddRef(IDWriteBitmapRenderTarget1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteBitmapRenderTarget1_Release(IDWriteBitmapRenderTarget1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteBitmapRenderTarget methods ***/
static inline HRESULT IDWriteBitmapRenderTarget1_DrawGlyphRun(IDWriteBitmapRenderTarget1* This,FLOAT baselineOriginX,FLOAT baselineOriginY,DWRITE_MEASURING_MODE measuring_mode,const DWRITE_GLYPH_RUN *glyph_run,IDWriteRenderingParams *params,COLORREF textColor,RECT *blackbox_rect) {
    return This->lpVtbl->DrawGlyphRun(This,baselineOriginX,baselineOriginY,measuring_mode,glyph_run,params,textColor,blackbox_rect);
}
static inline HDC IDWriteBitmapRenderTarget1_GetMemoryDC(IDWriteBitmapRenderTarget1* This) {
    return This->lpVtbl->GetMemoryDC(This);
}
static inline FLOAT IDWriteBitmapRenderTarget1_GetPixelsPerDip(IDWriteBitmapRenderTarget1* This) {
    return This->lpVtbl->GetPixelsPerDip(This);
}
static inline HRESULT IDWriteBitmapRenderTarget1_SetPixelsPerDip(IDWriteBitmapRenderTarget1* This,FLOAT pixels_per_dip) {
    return This->lpVtbl->SetPixelsPerDip(This,pixels_per_dip);
}
static inline HRESULT IDWriteBitmapRenderTarget1_GetCurrentTransform(IDWriteBitmapRenderTarget1* This,DWRITE_MATRIX *transform) {
    return This->lpVtbl->GetCurrentTransform(This,transform);
}
static inline HRESULT IDWriteBitmapRenderTarget1_SetCurrentTransform(IDWriteBitmapRenderTarget1* This,const DWRITE_MATRIX *transform) {
    return This->lpVtbl->SetCurrentTransform(This,transform);
}
static inline HRESULT IDWriteBitmapRenderTarget1_GetSize(IDWriteBitmapRenderTarget1* This,SIZE *size) {
    return This->lpVtbl->GetSize(This,size);
}
static inline HRESULT IDWriteBitmapRenderTarget1_Resize(IDWriteBitmapRenderTarget1* This,UINT32 width,UINT32 height) {
    return This->lpVtbl->Resize(This,width,height);
}
/*** IDWriteBitmapRenderTarget1 methods ***/
static inline DWRITE_TEXT_ANTIALIAS_MODE IDWriteBitmapRenderTarget1_GetTextAntialiasMode(IDWriteBitmapRenderTarget1* This) {
    return This->lpVtbl->GetTextAntialiasMode(This);
}
static inline HRESULT IDWriteBitmapRenderTarget1_SetTextAntialiasMode(IDWriteBitmapRenderTarget1* This,DWRITE_TEXT_ANTIALIAS_MODE mode) {
    return This->lpVtbl->SetTextAntialiasMode(This,mode);
}
#endif
#endif

#endif


#endif  /* __IDWriteBitmapRenderTarget1_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dwrite_1_h__ */
