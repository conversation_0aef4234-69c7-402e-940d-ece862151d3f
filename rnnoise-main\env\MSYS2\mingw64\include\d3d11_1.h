/*** Autogenerated by WIDL 10.12 from include/d3d11_1.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d11_1_h__
#define __d3d11_1_h__

/* Forward declarations */

#ifndef __ID3D11BlendState1_FWD_DEFINED__
#define __ID3D11BlendState1_FWD_DEFINED__
typedef interface ID3D11BlendState1 ID3D11BlendState1;
#ifdef __cplusplus
interface ID3D11BlendState1;
#endif /* __cplusplus */
#endif

#ifndef __ID3DDeviceContextState_FWD_DEFINED__
#define __ID3DDeviceContextState_FWD_DEFINED__
typedef interface ID3DDeviceContextState ID3DDeviceContextState;
#ifdef __cplusplus
interface ID3DDeviceContextState;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11DeviceContext1_FWD_DEFINED__
#define __ID3D11DeviceContext1_FWD_DEFINED__
typedef interface ID3D11DeviceContext1 ID3D11DeviceContext1;
#ifdef __cplusplus
interface ID3D11DeviceContext1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoContext1_FWD_DEFINED__
#define __ID3D11VideoContext1_FWD_DEFINED__
typedef interface ID3D11VideoContext1 ID3D11VideoContext1;
#ifdef __cplusplus
interface ID3D11VideoContext1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoDevice1_FWD_DEFINED__
#define __ID3D11VideoDevice1_FWD_DEFINED__
typedef interface ID3D11VideoDevice1 ID3D11VideoDevice1;
#ifdef __cplusplus
interface ID3D11VideoDevice1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoProcessorEnumerator1_FWD_DEFINED__
#define __ID3D11VideoProcessorEnumerator1_FWD_DEFINED__
typedef interface ID3D11VideoProcessorEnumerator1 ID3D11VideoProcessorEnumerator1;
#ifdef __cplusplus
interface ID3D11VideoProcessorEnumerator1;
#endif /* __cplusplus */
#endif

#ifndef __ID3DUserDefinedAnnotation_FWD_DEFINED__
#define __ID3DUserDefinedAnnotation_FWD_DEFINED__
typedef interface ID3DUserDefinedAnnotation ID3DUserDefinedAnnotation;
#ifdef __cplusplus
interface ID3DUserDefinedAnnotation;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11RasterizerState1_FWD_DEFINED__
#define __ID3D11RasterizerState1_FWD_DEFINED__
typedef interface ID3D11RasterizerState1 ID3D11RasterizerState1;
#ifdef __cplusplus
interface ID3D11RasterizerState1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Device1_FWD_DEFINED__
#define __ID3D11Device1_FWD_DEFINED__
typedef interface ID3D11Device1 ID3D11Device1;
#ifdef __cplusplus
interface ID3D11Device1;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <dxgi1_2.h>
#include <d3dcommon.h>
#include <d3d11.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum D3D11_LOGIC_OP {
    D3D11_LOGIC_OP_CLEAR = 0,
    D3D11_LOGIC_OP_SET = 1,
    D3D11_LOGIC_OP_COPY = 2,
    D3D11_LOGIC_OP_COPY_INVERTED = 3,
    D3D11_LOGIC_OP_NOOP = 4,
    D3D11_LOGIC_OP_INVERT = 5,
    D3D11_LOGIC_OP_AND = 6,
    D3D11_LOGIC_OP_NAND = 7,
    D3D11_LOGIC_OP_OR = 8,
    D3D11_LOGIC_OP_NOR = 9,
    D3D11_LOGIC_OP_XOR = 10,
    D3D11_LOGIC_OP_EQUIV = 11,
    D3D11_LOGIC_OP_AND_REVERSE = 12,
    D3D11_LOGIC_OP_AND_INVERTED = 13,
    D3D11_LOGIC_OP_OR_REVERSE = 14,
    D3D11_LOGIC_OP_OR_INVERTED = 15
} D3D11_LOGIC_OP;
typedef enum D3D11_COPY_FLAGS {
    D3D11_COPY_NO_OVERWRITE = 0x1,
    D3D11_COPY_DISCARD = 0x2
} D3D11_COPY_FLAGS;
typedef enum D3D11_1_CREATE_DEVICE_CONTEXT_STATE_FLAG {
    D3D11_1_CREATE_DEVICE_CONTEXT_STATE_SINGLETHREADED = 0x1
} D3D11_1_CREATE_DEVICE_CONTEXT_STATE_FLAG;
typedef enum D3D11_VIDEO_DECODER_CAPS {
    D3D11_VIDEO_DECODER_CAPS_DOWNSAMPLE = 0x1,
    D3D11_VIDEO_DECODER_CAPS_NON_REAL_TIME = 0x2,
    D3D11_VIDEO_DECODER_CAPS_DOWNSAMPLE_DYNAMIC = 0x4,
    D3D11_VIDEO_DECODER_CAPS_DOWNSAMPLE_REQUIRED = 0x8,
    D3D11_VIDEO_DECODER_CAPS_UNSUPPORTED = 0x10
} D3D11_VIDEO_DECODER_CAPS;
typedef enum D3D11_VIDEO_PROCESSOR_BEHAVIOR_HINTS {
    D3D11_VIDEO_PROCESSOR_BEHAVIOR_HINT_MULTIPLANE_OVERLAY_ROTATION = 0x1,
    D3D11_VIDEO_PROCESSOR_BEHAVIOR_HINT_MULTIPLANE_OVERLAY_RESIZE = 0x2,
    D3D11_VIDEO_PROCESSOR_BEHAVIOR_HINT_MULTIPLANE_OVERLAY_COLOR_SPACE_CONVERSION = 0x4,
    D3D11_VIDEO_PROCESSOR_BEHAVIOR_HINT_TRIPLE_BUFFER_OUTPUT = 0x8
} D3D11_VIDEO_PROCESSOR_BEHAVIOR_HINTS;
typedef enum D3D11_CRYPTO_SESSION_STATUS {
    D3D11_CRYPTO_SESSION_STATUS_OK = 0x0,
    D3D11_CRYPTO_SESSION_STATUS_KEY_LOST = 0x1,
    D3D11_CRYPTO_SESSION_STATUS_KEY_AND_CONTENT_LOST = 0x2
} D3D11_CRYPTO_SESSION_STATUS;
typedef struct D3D11_RENDER_TARGET_BLEND_DESC1 {
    WINBOOL BlendEnable;
    WINBOOL LogicOpEnable;
    D3D11_BLEND SrcBlend;
    D3D11_BLEND DestBlend;
    D3D11_BLEND_OP BlendOp;
    D3D11_BLEND SrcBlendAlpha;
    D3D11_BLEND DestBlendAlpha;
    D3D11_BLEND_OP BlendOpAlpha;
    D3D11_LOGIC_OP LogicOp;
    UINT8 RenderTargetWriteMask;
} D3D11_RENDER_TARGET_BLEND_DESC1;
typedef struct D3D11_BLEND_DESC1 {
    WINBOOL AlphaToCoverageEnable;
    WINBOOL IndependentBlendEnable;
    D3D11_RENDER_TARGET_BLEND_DESC1 RenderTarget[8];
} D3D11_BLEND_DESC1;
typedef struct D3D11_RASTERIZER_DESC1 {
    D3D11_FILL_MODE FillMode;
    D3D11_CULL_MODE CullMode;
    WINBOOL FrontCounterClockwise;
    INT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    WINBOOL DepthClipEnable;
    WINBOOL ScissorEnable;
    WINBOOL MultisampleEnable;
    WINBOOL AntialiasedLineEnable;
    UINT ForcedSampleCount;
} D3D11_RASTERIZER_DESC1;
typedef struct D3D11_VIDEO_DECODER_SUB_SAMPLE_MAPPING_BLOCK {
    UINT ClearSize;
    UINT EncryptedSize;
} D3D11_VIDEO_DECODER_SUB_SAMPLE_MAPPING_BLOCK;
typedef struct D3D11_VIDEO_DECODER_BUFFER_DESC1 {
    D3D11_VIDEO_DECODER_BUFFER_TYPE BufferType;
    UINT DataOffset;
    UINT DataSize;
    void *pIV;
    UINT IVSize;
    D3D11_VIDEO_DECODER_SUB_SAMPLE_MAPPING_BLOCK *pSubSampleMappingBlock;
    UINT SubSampleMappingCount;
} D3D11_VIDEO_DECODER_BUFFER_DESC1;
typedef struct D3D11_VIDEO_DECODER_BEGIN_FRAME_CRYPTO_SESSION {
    ID3D11CryptoSession *pCryptoSession;
    UINT BlobSize;
    void *pBlob;
    GUID *pKeyInfoId;
    UINT PrivateDataSize;
    void *pPrivateData;
} D3D11_VIDEO_DECODER_BEGIN_FRAME_CRYPTO_SESSION;
typedef struct D3D11_VIDEO_PROCESSOR_STREAM_BEHAVIOR_HINT {
    WINBOOL Enable;
    UINT Width;
    UINT Height;
    DXGI_FORMAT Format;
} D3D11_VIDEO_PROCESSOR_STREAM_BEHAVIOR_HINT;
typedef struct D3D11_KEY_EXCHANGE_HW_PROTECTION_INPUT_DATA {
    UINT PrivateDataSize;
    UINT HWProtectionDataSize;
    BYTE pbInput[4];
} D3D11_KEY_EXCHANGE_HW_PROTECTION_INPUT_DATA;
typedef struct D3D11_KEY_EXCHANGE_HW_PROTECTION_OUTPUT_DATA {
    UINT PrivateDataSize;
    UINT MaxHWProtectionDataSize;
    UINT HWProtectionDataSize;
    UINT64 TransportTime;
    UINT64 ExecutionTime;
    BYTE pbOutput[4];
} D3D11_KEY_EXCHANGE_HW_PROTECTION_OUTPUT_DATA;
typedef struct D3D11_KEY_EXCHANGE_HW_PROTECTION_DATA {
    UINT HWProtectionFunctionID;
    D3D11_KEY_EXCHANGE_HW_PROTECTION_INPUT_DATA *pInputData;
    D3D11_KEY_EXCHANGE_HW_PROTECTION_OUTPUT_DATA *pOutputData;
    HRESULT Status;
} D3D11_KEY_EXCHANGE_HW_PROTECTION_DATA;
typedef struct D3D11_VIDEO_SAMPLE_DESC {
    UINT Width;
    UINT Height;
    DXGI_FORMAT Format;
    DXGI_COLOR_SPACE_TYPE ColorSpace;
} D3D11_VIDEO_SAMPLE_DESC;
/*****************************************************************************
 * ID3D11BlendState1 interface
 */
#ifndef __ID3D11BlendState1_INTERFACE_DEFINED__
#define __ID3D11BlendState1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11BlendState1, 0xcc86fabe, 0xda55, 0x401d, 0x85,0xe7, 0xe3,0xc9,0xde,0x28,0x77,0xe9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cc86fabe-da55-401d-85e7-e3c9de2877e9")
ID3D11BlendState1 : public ID3D11BlendState
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_BLEND_DESC1 *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11BlendState1, 0xcc86fabe, 0xda55, 0x401d, 0x85,0xe7, 0xe3,0xc9,0xde,0x28,0x77,0xe9)
#endif
#else
typedef struct ID3D11BlendState1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11BlendState1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11BlendState1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11BlendState1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11BlendState1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11BlendState1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11BlendState1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11BlendState1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11BlendState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11BlendState1 *This,
        D3D11_BLEND_DESC *pDesc);

    /*** ID3D11BlendState1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11BlendState1 *This,
        D3D11_BLEND_DESC1 *pDesc);

    END_INTERFACE
} ID3D11BlendState1Vtbl;

interface ID3D11BlendState1 {
    CONST_VTBL ID3D11BlendState1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11BlendState1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11BlendState1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11BlendState1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11BlendState1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11BlendState1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11BlendState1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11BlendState1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11BlendState methods ***/
#define ID3D11BlendState1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11BlendState1 methods ***/
#define ID3D11BlendState1_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11BlendState1_QueryInterface(ID3D11BlendState1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11BlendState1_AddRef(ID3D11BlendState1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11BlendState1_Release(ID3D11BlendState1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11BlendState1_GetDevice(ID3D11BlendState1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11BlendState1_GetPrivateData(ID3D11BlendState1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11BlendState1_SetPrivateData(ID3D11BlendState1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11BlendState1_SetPrivateDataInterface(ID3D11BlendState1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11BlendState methods ***/
static inline void ID3D11BlendState1_GetDesc(ID3D11BlendState1* This,D3D11_BLEND_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11BlendState1 methods ***/
static inline void ID3D11BlendState1_GetDesc1(ID3D11BlendState1* This,D3D11_BLEND_DESC1 *pDesc) {
    This->lpVtbl->GetDesc1(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11BlendState1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3DDeviceContextState interface
 */
#ifndef __ID3DDeviceContextState_INTERFACE_DEFINED__
#define __ID3DDeviceContextState_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3DDeviceContextState, 0x5c1e0d8a, 0x7c23, 0x48f9, 0x8c,0x59, 0xa9,0x29,0x58,0xce,0xff,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5c1e0d8a-7c23-48f9-8c59-a92958ceff11")
ID3DDeviceContextState : public ID3D11DeviceChild
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3DDeviceContextState, 0x5c1e0d8a, 0x7c23, 0x48f9, 0x8c,0x59, 0xa9,0x29,0x58,0xce,0xff,0x11)
#endif
#else
typedef struct ID3DDeviceContextStateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3DDeviceContextState *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3DDeviceContextState *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3DDeviceContextState *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3DDeviceContextState *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3DDeviceContextState *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3DDeviceContextState *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3DDeviceContextState *This,
        REFGUID guid,
        const IUnknown *pData);

    END_INTERFACE
} ID3DDeviceContextStateVtbl;

interface ID3DDeviceContextState {
    CONST_VTBL ID3DDeviceContextStateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3DDeviceContextState_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3DDeviceContextState_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3DDeviceContextState_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3DDeviceContextState_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3DDeviceContextState_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3DDeviceContextState_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3DDeviceContextState_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3DDeviceContextState_QueryInterface(ID3DDeviceContextState* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3DDeviceContextState_AddRef(ID3DDeviceContextState* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3DDeviceContextState_Release(ID3DDeviceContextState* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3DDeviceContextState_GetDevice(ID3DDeviceContextState* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3DDeviceContextState_GetPrivateData(ID3DDeviceContextState* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3DDeviceContextState_SetPrivateData(ID3DDeviceContextState* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3DDeviceContextState_SetPrivateDataInterface(ID3DDeviceContextState* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
#endif
#endif

#endif


#endif  /* __ID3DDeviceContextState_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11DeviceContext1 interface
 */
#ifndef __ID3D11DeviceContext1_INTERFACE_DEFINED__
#define __ID3D11DeviceContext1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DeviceContext1, 0xbb2c6faa, 0xb5fb, 0x4082, 0x8e,0x6b, 0x38,0x8b,0x8c,0xfa,0x90,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb2c6faa-b5fb-4082-8e6b-388b8cfa90e1")
ID3D11DeviceContext1 : public ID3D11DeviceContext
{
    virtual void STDMETHODCALLTYPE CopySubresourceRegion1(
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox,
        UINT CopyFlags) = 0;

    virtual void STDMETHODCALLTYPE UpdateSubresource1(
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch,
        UINT CopyFlags) = 0;

    virtual void STDMETHODCALLTYPE DiscardResource(
        ID3D11Resource *pResource) = 0;

    virtual void STDMETHODCALLTYPE DiscardView(
        ID3D11View *pResourceView) = 0;

    virtual void STDMETHODCALLTYPE VSSetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE HSSetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE DSSetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE GSSetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE PSSetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE CSSetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE VSGetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE HSGetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE DSGetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE GSGetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE PSGetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE CSGetConstantBuffers1(
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants) = 0;

    virtual void STDMETHODCALLTYPE SwapDeviceContextState(
        ID3DDeviceContextState *pState,
        ID3DDeviceContextState **ppPreviousState) = 0;

    virtual void STDMETHODCALLTYPE ClearView(
        ID3D11View *pView,
        const FLOAT Color[4],
        const D3D11_RECT *pRect,
        UINT NumRects) = 0;

    virtual void STDMETHODCALLTYPE DiscardView1(
        ID3D11View *pResourceView,
        const D3D11_RECT *pRects,
        UINT NumRects) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DeviceContext1, 0xbb2c6faa, 0xb5fb, 0x4082, 0x8e,0x6b, 0x38,0x8b,0x8c,0xfa,0x90,0xe1)
#endif
#else
typedef struct ID3D11DeviceContext1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DeviceContext1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DeviceContext1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DeviceContext1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DeviceContext1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DeviceContext1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DeviceContext1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DeviceContext1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11DeviceContext methods ***/
    void (STDMETHODCALLTYPE *VSSetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSSetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSSetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11PixelShader *pPixelShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *PSSetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *VSSetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11VertexShader *pVertexShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DrawIndexed)(
        ID3D11DeviceContext1 *This,
        UINT IndexCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation);

    void (STDMETHODCALLTYPE *Draw)(
        ID3D11DeviceContext1 *This,
        UINT VertexCount,
        UINT StartVertexLocation);

    HRESULT (STDMETHODCALLTYPE *Map)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pResource,
        UINT Subresource,
        D3D11_MAP MapType,
        UINT MapFlags,
        D3D11_MAPPED_SUBRESOURCE *pMappedResource);

    void (STDMETHODCALLTYPE *Unmap)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pResource,
        UINT Subresource);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *IASetInputLayout)(
        ID3D11DeviceContext1 *This,
        ID3D11InputLayout *pInputLayout);

    void (STDMETHODCALLTYPE *IASetVertexBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppVertexBuffers,
        const UINT *pStrides,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *IASetIndexBuffer)(
        ID3D11DeviceContext1 *This,
        ID3D11Buffer *pIndexBuffer,
        DXGI_FORMAT Format,
        UINT Offset);

    void (STDMETHODCALLTYPE *DrawIndexedInstanced)(
        ID3D11DeviceContext1 *This,
        UINT IndexCountPerInstance,
        UINT InstanceCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *DrawInstanced)(
        ID3D11DeviceContext1 *This,
        UINT VertexCountPerInstance,
        UINT InstanceCount,
        UINT StartVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSSetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11GeometryShader *pShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *IASetPrimitiveTopology)(
        ID3D11DeviceContext1 *This,
        D3D11_PRIMITIVE_TOPOLOGY Topology);

    void (STDMETHODCALLTYPE *VSSetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSSetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *Begin)(
        ID3D11DeviceContext1 *This,
        ID3D11Asynchronous *pAsync);

    void (STDMETHODCALLTYPE *End)(
        ID3D11DeviceContext1 *This,
        ID3D11Asynchronous *pAsync);

    HRESULT (STDMETHODCALLTYPE *GetData)(
        ID3D11DeviceContext1 *This,
        ID3D11Asynchronous *pAsync,
        void *pData,
        UINT DataSize,
        UINT GetDataFlags);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D11DeviceContext1 *This,
        ID3D11Predicate *pPredicate,
        WINBOOL PredicateValue);

    void (STDMETHODCALLTYPE *GSSetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSSetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *OMSetRenderTargets)(
        ID3D11DeviceContext1 *This,
        UINT NumViews,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView);

    void (STDMETHODCALLTYPE *OMSetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext1 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *OMSetBlendState)(
        ID3D11DeviceContext1 *This,
        ID3D11BlendState *pBlendState,
        const FLOAT BlendFactor[4],
        UINT SampleMask);

    void (STDMETHODCALLTYPE *OMSetDepthStencilState)(
        ID3D11DeviceContext1 *This,
        ID3D11DepthStencilState *pDepthStencilState,
        UINT StencilRef);

    void (STDMETHODCALLTYPE *SOSetTargets)(
        ID3D11DeviceContext1 *This,
        UINT NumBuffers,
        ID3D11Buffer *const *ppSOTargets,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *DrawAuto)(
        ID3D11DeviceContext1 *This);

    void (STDMETHODCALLTYPE *DrawIndexedInstancedIndirect)(
        ID3D11DeviceContext1 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *DrawInstancedIndirect)(
        ID3D11DeviceContext1 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *Dispatch)(
        ID3D11DeviceContext1 *This,
        UINT ThreadGroupCountX,
        UINT ThreadGroupCountY,
        UINT ThreadGroupCountZ);

    void (STDMETHODCALLTYPE *DispatchIndirect)(
        ID3D11DeviceContext1 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *RSSetState)(
        ID3D11DeviceContext1 *This,
        ID3D11RasterizerState *pRasterizerState);

    void (STDMETHODCALLTYPE *RSSetViewports)(
        ID3D11DeviceContext1 *This,
        UINT NumViewports,
        const D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSSetScissorRects)(
        ID3D11DeviceContext1 *This,
        UINT NumRects,
        const D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *CopySubresourceRegion)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox);

    void (STDMETHODCALLTYPE *CopyResource)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pDstResource,
        ID3D11Resource *pSrcResource);

    void (STDMETHODCALLTYPE *UpdateSubresource)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch);

    void (STDMETHODCALLTYPE *CopyStructureCount)(
        ID3D11DeviceContext1 *This,
        ID3D11Buffer *pDstBuffer,
        UINT DstAlignedByteOffset,
        ID3D11UnorderedAccessView *pSrcView);

    void (STDMETHODCALLTYPE *ClearRenderTargetView)(
        ID3D11DeviceContext1 *This,
        ID3D11RenderTargetView *pRenderTargetView,
        const FLOAT ColorRGBA[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewUint)(
        ID3D11DeviceContext1 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const UINT Values[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewFloat)(
        ID3D11DeviceContext1 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const FLOAT Values[4]);

    void (STDMETHODCALLTYPE *ClearDepthStencilView)(
        ID3D11DeviceContext1 *This,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT ClearFlags,
        FLOAT Depth,
        UINT8 Stencil);

    void (STDMETHODCALLTYPE *GenerateMips)(
        ID3D11DeviceContext1 *This,
        ID3D11ShaderResourceView *pShaderResourceView);

    void (STDMETHODCALLTYPE *SetResourceMinLOD)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pResource,
        FLOAT MinLOD);

    FLOAT (STDMETHODCALLTYPE *GetResourceMinLOD)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *ResolveSubresource)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        DXGI_FORMAT Format);

    void (STDMETHODCALLTYPE *ExecuteCommandList)(
        ID3D11DeviceContext1 *This,
        ID3D11CommandList *pCommandList,
        WINBOOL RestoreContextState);

    void (STDMETHODCALLTYPE *HSSetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSSetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11HullShader *pHullShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *HSSetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSSetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSSetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11DomainShader *pDomainShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DSSetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSSetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSSetUnorderedAccessViews)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *CSSetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11ComputeShader *pComputeShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *CSSetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSGetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSGetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11PixelShader **ppPixelShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *VSGetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11VertexShader **ppVertexShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *IAGetInputLayout)(
        ID3D11DeviceContext1 *This,
        ID3D11InputLayout **ppInputLayout);

    void (STDMETHODCALLTYPE *IAGetVertexBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppVertexBuffers,
        UINT *pStrides,
        UINT *pOffsets);

    void (STDMETHODCALLTYPE *IAGetIndexBuffer)(
        ID3D11DeviceContext1 *This,
        ID3D11Buffer **pIndexBuffer,
        DXGI_FORMAT *Format,
        UINT *Offset);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSGetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11GeometryShader **ppGeometryShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *IAGetPrimitiveTopology)(
        ID3D11DeviceContext1 *This,
        D3D11_PRIMITIVE_TOPOLOGY *pTopology);

    void (STDMETHODCALLTYPE *VSGetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSGetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *GetPredication)(
        ID3D11DeviceContext1 *This,
        ID3D11Predicate **ppPredicate,
        WINBOOL *pPredicateValue);

    void (STDMETHODCALLTYPE *GSGetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSGetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *OMGetRenderTargets)(
        ID3D11DeviceContext1 *This,
        UINT NumViews,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView);

    void (STDMETHODCALLTYPE *OMGetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext1 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *OMGetBlendState)(
        ID3D11DeviceContext1 *This,
        ID3D11BlendState **ppBlendState,
        FLOAT BlendFactor[4],
        UINT *pSampleMask);

    void (STDMETHODCALLTYPE *OMGetDepthStencilState)(
        ID3D11DeviceContext1 *This,
        ID3D11DepthStencilState **ppDepthStencilState,
        UINT *pStencilRef);

    void (STDMETHODCALLTYPE *SOGetTargets)(
        ID3D11DeviceContext1 *This,
        UINT NumBuffers,
        ID3D11Buffer **ppSOTargets);

    void (STDMETHODCALLTYPE *RSGetState)(
        ID3D11DeviceContext1 *This,
        ID3D11RasterizerState **ppRasterizerState);

    void (STDMETHODCALLTYPE *RSGetViewports)(
        ID3D11DeviceContext1 *This,
        UINT *pNumViewports,
        D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSGetScissorRects)(
        ID3D11DeviceContext1 *This,
        UINT *pNumRects,
        D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *HSGetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSGetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11HullShader **ppHullShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *HSGetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSGetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSGetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11DomainShader **ppDomainShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *DSGetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSGetShaderResources)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSGetUnorderedAccessViews)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *CSGetShader)(
        ID3D11DeviceContext1 *This,
        ID3D11ComputeShader **ppComputeShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *CSGetSamplers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D11DeviceContext1 *This);

    void (STDMETHODCALLTYPE *Flush)(
        ID3D11DeviceContext1 *This);

    D3D11_DEVICE_CONTEXT_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D11DeviceContext1 *This);

    UINT (STDMETHODCALLTYPE *GetContextFlags)(
        ID3D11DeviceContext1 *This);

    HRESULT (STDMETHODCALLTYPE *FinishCommandList)(
        ID3D11DeviceContext1 *This,
        WINBOOL RestoreDeferredContextState,
        ID3D11CommandList **ppCommandList);

    /*** ID3D11DeviceContext1 methods ***/
    void (STDMETHODCALLTYPE *CopySubresourceRegion1)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *UpdateSubresource1)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D11DeviceContext1 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *DiscardView)(
        ID3D11DeviceContext1 *This,
        ID3D11View *pResourceView);

    void (STDMETHODCALLTYPE *VSSetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers1)(
        ID3D11DeviceContext1 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *SwapDeviceContextState)(
        ID3D11DeviceContext1 *This,
        ID3DDeviceContextState *pState,
        ID3DDeviceContextState **ppPreviousState);

    void (STDMETHODCALLTYPE *ClearView)(
        ID3D11DeviceContext1 *This,
        ID3D11View *pView,
        const FLOAT Color[4],
        const D3D11_RECT *pRect,
        UINT NumRects);

    void (STDMETHODCALLTYPE *DiscardView1)(
        ID3D11DeviceContext1 *This,
        ID3D11View *pResourceView,
        const D3D11_RECT *pRects,
        UINT NumRects);

    END_INTERFACE
} ID3D11DeviceContext1Vtbl;

interface ID3D11DeviceContext1 {
    CONST_VTBL ID3D11DeviceContext1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DeviceContext1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DeviceContext1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DeviceContext1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DeviceContext1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DeviceContext1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DeviceContext1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DeviceContext1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11DeviceContext methods ***/
#define ID3D11DeviceContext1_VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext1_PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext1_DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation) (This)->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation)
#define ID3D11DeviceContext1_Draw(This,VertexCount,StartVertexLocation) (This)->lpVtbl->Draw(This,VertexCount,StartVertexLocation)
#define ID3D11DeviceContext1_Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource) (This)->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource)
#define ID3D11DeviceContext1_Unmap(This,pResource,Subresource) (This)->lpVtbl->Unmap(This,pResource,Subresource)
#define ID3D11DeviceContext1_PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_IASetInputLayout(This,pInputLayout) (This)->lpVtbl->IASetInputLayout(This,pInputLayout)
#define ID3D11DeviceContext1_IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext1_IASetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext1_DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext1_DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext1_GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_GSSetShader(This,pShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext1_IASetPrimitiveTopology(This,Topology) (This)->lpVtbl->IASetPrimitiveTopology(This,Topology)
#define ID3D11DeviceContext1_VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_Begin(This,pAsync) (This)->lpVtbl->Begin(This,pAsync)
#define ID3D11DeviceContext1_End(This,pAsync) (This)->lpVtbl->End(This,pAsync)
#define ID3D11DeviceContext1_GetData(This,pAsync,pData,DataSize,GetDataFlags) (This)->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags)
#define ID3D11DeviceContext1_SetPredication(This,pPredicate,PredicateValue) (This)->lpVtbl->SetPredication(This,pPredicate,PredicateValue)
#define ID3D11DeviceContext1_GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView) (This)->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView)
#define ID3D11DeviceContext1_OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext1_OMSetBlendState(This,pBlendState,BlendFactor,SampleMask) (This)->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask)
#define ID3D11DeviceContext1_OMSetDepthStencilState(This,pDepthStencilState,StencilRef) (This)->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef)
#define ID3D11DeviceContext1_SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets) (This)->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets)
#define ID3D11DeviceContext1_DrawAuto(This) (This)->lpVtbl->DrawAuto(This)
#define ID3D11DeviceContext1_DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext1_DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext1_Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ) (This)->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ)
#define ID3D11DeviceContext1_DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext1_RSSetState(This,pRasterizerState) (This)->lpVtbl->RSSetState(This,pRasterizerState)
#define ID3D11DeviceContext1_RSSetViewports(This,NumViewports,pViewports) (This)->lpVtbl->RSSetViewports(This,NumViewports,pViewports)
#define ID3D11DeviceContext1_RSSetScissorRects(This,NumRects,pRects) (This)->lpVtbl->RSSetScissorRects(This,NumRects,pRects)
#define ID3D11DeviceContext1_CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox) (This)->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox)
#define ID3D11DeviceContext1_CopyResource(This,pDstResource,pSrcResource) (This)->lpVtbl->CopyResource(This,pDstResource,pSrcResource)
#define ID3D11DeviceContext1_UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch) (This)->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch)
#define ID3D11DeviceContext1_CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView) (This)->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView)
#define ID3D11DeviceContext1_ClearRenderTargetView(This,pRenderTargetView,ColorRGBA) (This)->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA)
#define ID3D11DeviceContext1_ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext1_ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext1_ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil) (This)->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil)
#define ID3D11DeviceContext1_GenerateMips(This,pShaderResourceView) (This)->lpVtbl->GenerateMips(This,pShaderResourceView)
#define ID3D11DeviceContext1_SetResourceMinLOD(This,pResource,MinLOD) (This)->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD)
#define ID3D11DeviceContext1_GetResourceMinLOD(This,pResource) (This)->lpVtbl->GetResourceMinLOD(This,pResource)
#define ID3D11DeviceContext1_ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format) (This)->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format)
#define ID3D11DeviceContext1_ExecuteCommandList(This,pCommandList,RestoreContextState) (This)->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState)
#define ID3D11DeviceContext1_HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext1_HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext1_DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext1_CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext1_CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext1_PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext1_PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_IAGetInputLayout(This,ppInputLayout) (This)->lpVtbl->IAGetInputLayout(This,ppInputLayout)
#define ID3D11DeviceContext1_IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext1_IAGetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext1_GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext1_IAGetPrimitiveTopology(This,pTopology) (This)->lpVtbl->IAGetPrimitiveTopology(This,pTopology)
#define ID3D11DeviceContext1_VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_GetPredication(This,ppPredicate,pPredicateValue) (This)->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue)
#define ID3D11DeviceContext1_GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView) (This)->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView)
#define ID3D11DeviceContext1_OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext1_OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask) (This)->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask)
#define ID3D11DeviceContext1_OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef) (This)->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef)
#define ID3D11DeviceContext1_SOGetTargets(This,NumBuffers,ppSOTargets) (This)->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets)
#define ID3D11DeviceContext1_RSGetState(This,ppRasterizerState) (This)->lpVtbl->RSGetState(This,ppRasterizerState)
#define ID3D11DeviceContext1_RSGetViewports(This,pNumViewports,pViewports) (This)->lpVtbl->RSGetViewports(This,pNumViewports,pViewports)
#define ID3D11DeviceContext1_RSGetScissorRects(This,pNumRects,pRects) (This)->lpVtbl->RSGetScissorRects(This,pNumRects,pRects)
#define ID3D11DeviceContext1_HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext1_HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext1_DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext1_CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext1_CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext1_CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext1_CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext1_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D11DeviceContext1_Flush(This) (This)->lpVtbl->Flush(This)
#define ID3D11DeviceContext1_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D11DeviceContext1_GetContextFlags(This) (This)->lpVtbl->GetContextFlags(This)
#define ID3D11DeviceContext1_FinishCommandList(This,RestoreDeferredContextState,ppCommandList) (This)->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList)
/*** ID3D11DeviceContext1 methods ***/
#define ID3D11DeviceContext1_CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags) (This)->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags)
#define ID3D11DeviceContext1_UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags) (This)->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags)
#define ID3D11DeviceContext1_DiscardResource(This,pResource) (This)->lpVtbl->DiscardResource(This,pResource)
#define ID3D11DeviceContext1_DiscardView(This,pResourceView) (This)->lpVtbl->DiscardView(This,pResourceView)
#define ID3D11DeviceContext1_VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext1_SwapDeviceContextState(This,pState,ppPreviousState) (This)->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState)
#define ID3D11DeviceContext1_ClearView(This,pView,Color,pRect,NumRects) (This)->lpVtbl->ClearView(This,pView,Color,pRect,NumRects)
#define ID3D11DeviceContext1_DiscardView1(This,pResourceView,pRects,NumRects) (This)->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11DeviceContext1_QueryInterface(ID3D11DeviceContext1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11DeviceContext1_AddRef(ID3D11DeviceContext1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11DeviceContext1_Release(ID3D11DeviceContext1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11DeviceContext1_GetDevice(ID3D11DeviceContext1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11DeviceContext1_GetPrivateData(ID3D11DeviceContext1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11DeviceContext1_SetPrivateData(ID3D11DeviceContext1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11DeviceContext1_SetPrivateDataInterface(ID3D11DeviceContext1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11DeviceContext methods ***/
static inline void ID3D11DeviceContext1_VSSetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_PSSetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_PSSetShader(ID3D11DeviceContext1* This,ID3D11PixelShader *pPixelShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext1_PSSetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_VSSetShader(ID3D11DeviceContext1* This,ID3D11VertexShader *pVertexShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext1_DrawIndexed(ID3D11DeviceContext1* This,UINT IndexCount,UINT StartIndexLocation,INT BaseVertexLocation) {
    This->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation);
}
static inline void ID3D11DeviceContext1_Draw(ID3D11DeviceContext1* This,UINT VertexCount,UINT StartVertexLocation) {
    This->lpVtbl->Draw(This,VertexCount,StartVertexLocation);
}
static inline HRESULT ID3D11DeviceContext1_Map(ID3D11DeviceContext1* This,ID3D11Resource *pResource,UINT Subresource,D3D11_MAP MapType,UINT MapFlags,D3D11_MAPPED_SUBRESOURCE *pMappedResource) {
    return This->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource);
}
static inline void ID3D11DeviceContext1_Unmap(ID3D11DeviceContext1* This,ID3D11Resource *pResource,UINT Subresource) {
    This->lpVtbl->Unmap(This,pResource,Subresource);
}
static inline void ID3D11DeviceContext1_PSSetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_IASetInputLayout(ID3D11DeviceContext1* This,ID3D11InputLayout *pInputLayout) {
    This->lpVtbl->IASetInputLayout(This,pInputLayout);
}
static inline void ID3D11DeviceContext1_IASetVertexBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppVertexBuffers,const UINT *pStrides,const UINT *pOffsets) {
    This->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext1_IASetIndexBuffer(ID3D11DeviceContext1* This,ID3D11Buffer *pIndexBuffer,DXGI_FORMAT Format,UINT Offset) {
    This->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext1_DrawIndexedInstanced(ID3D11DeviceContext1* This,UINT IndexCountPerInstance,UINT InstanceCount,UINT StartIndexLocation,INT BaseVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext1_DrawInstanced(ID3D11DeviceContext1* This,UINT VertexCountPerInstance,UINT InstanceCount,UINT StartVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext1_GSSetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_GSSetShader(ID3D11DeviceContext1* This,ID3D11GeometryShader *pShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext1_IASetPrimitiveTopology(ID3D11DeviceContext1* This,D3D11_PRIMITIVE_TOPOLOGY Topology) {
    This->lpVtbl->IASetPrimitiveTopology(This,Topology);
}
static inline void ID3D11DeviceContext1_VSSetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_VSSetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_Begin(ID3D11DeviceContext1* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->Begin(This,pAsync);
}
static inline void ID3D11DeviceContext1_End(ID3D11DeviceContext1* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->End(This,pAsync);
}
static inline HRESULT ID3D11DeviceContext1_GetData(ID3D11DeviceContext1* This,ID3D11Asynchronous *pAsync,void *pData,UINT DataSize,UINT GetDataFlags) {
    return This->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags);
}
static inline void ID3D11DeviceContext1_SetPredication(ID3D11DeviceContext1* This,ID3D11Predicate *pPredicate,WINBOOL PredicateValue) {
    This->lpVtbl->SetPredication(This,pPredicate,PredicateValue);
}
static inline void ID3D11DeviceContext1_GSSetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_GSSetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_OMSetRenderTargets(ID3D11DeviceContext1* This,UINT NumViews,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView) {
    This->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView);
}
static inline void ID3D11DeviceContext1_OMSetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext1* This,UINT NumRTVs,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext1_OMSetBlendState(ID3D11DeviceContext1* This,ID3D11BlendState *pBlendState,const FLOAT BlendFactor[4],UINT SampleMask) {
    This->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask);
}
static inline void ID3D11DeviceContext1_OMSetDepthStencilState(ID3D11DeviceContext1* This,ID3D11DepthStencilState *pDepthStencilState,UINT StencilRef) {
    This->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef);
}
static inline void ID3D11DeviceContext1_SOSetTargets(ID3D11DeviceContext1* This,UINT NumBuffers,ID3D11Buffer *const *ppSOTargets,const UINT *pOffsets) {
    This->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets);
}
static inline void ID3D11DeviceContext1_DrawAuto(ID3D11DeviceContext1* This) {
    This->lpVtbl->DrawAuto(This);
}
static inline void ID3D11DeviceContext1_DrawIndexedInstancedIndirect(ID3D11DeviceContext1* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext1_DrawInstancedIndirect(ID3D11DeviceContext1* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext1_Dispatch(ID3D11DeviceContext1* This,UINT ThreadGroupCountX,UINT ThreadGroupCountY,UINT ThreadGroupCountZ) {
    This->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ);
}
static inline void ID3D11DeviceContext1_DispatchIndirect(ID3D11DeviceContext1* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext1_RSSetState(ID3D11DeviceContext1* This,ID3D11RasterizerState *pRasterizerState) {
    This->lpVtbl->RSSetState(This,pRasterizerState);
}
static inline void ID3D11DeviceContext1_RSSetViewports(ID3D11DeviceContext1* This,UINT NumViewports,const D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSSetViewports(This,NumViewports,pViewports);
}
static inline void ID3D11DeviceContext1_RSSetScissorRects(ID3D11DeviceContext1* This,UINT NumRects,const D3D11_RECT *pRects) {
    This->lpVtbl->RSSetScissorRects(This,NumRects,pRects);
}
static inline void ID3D11DeviceContext1_CopySubresourceRegion(ID3D11DeviceContext1* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox) {
    This->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox);
}
static inline void ID3D11DeviceContext1_CopyResource(ID3D11DeviceContext1* This,ID3D11Resource *pDstResource,ID3D11Resource *pSrcResource) {
    This->lpVtbl->CopyResource(This,pDstResource,pSrcResource);
}
static inline void ID3D11DeviceContext1_UpdateSubresource(ID3D11DeviceContext1* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch) {
    This->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch);
}
static inline void ID3D11DeviceContext1_CopyStructureCount(ID3D11DeviceContext1* This,ID3D11Buffer *pDstBuffer,UINT DstAlignedByteOffset,ID3D11UnorderedAccessView *pSrcView) {
    This->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView);
}
static inline void ID3D11DeviceContext1_ClearRenderTargetView(ID3D11DeviceContext1* This,ID3D11RenderTargetView *pRenderTargetView,const FLOAT ColorRGBA[4]) {
    This->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA);
}
static inline void ID3D11DeviceContext1_ClearUnorderedAccessViewUint(ID3D11DeviceContext1* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const UINT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext1_ClearUnorderedAccessViewFloat(ID3D11DeviceContext1* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const FLOAT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext1_ClearDepthStencilView(ID3D11DeviceContext1* This,ID3D11DepthStencilView *pDepthStencilView,UINT ClearFlags,FLOAT Depth,UINT8 Stencil) {
    This->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil);
}
static inline void ID3D11DeviceContext1_GenerateMips(ID3D11DeviceContext1* This,ID3D11ShaderResourceView *pShaderResourceView) {
    This->lpVtbl->GenerateMips(This,pShaderResourceView);
}
static inline void ID3D11DeviceContext1_SetResourceMinLOD(ID3D11DeviceContext1* This,ID3D11Resource *pResource,FLOAT MinLOD) {
    This->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD);
}
static inline FLOAT ID3D11DeviceContext1_GetResourceMinLOD(ID3D11DeviceContext1* This,ID3D11Resource *pResource) {
    return This->lpVtbl->GetResourceMinLOD(This,pResource);
}
static inline void ID3D11DeviceContext1_ResolveSubresource(ID3D11DeviceContext1* This,ID3D11Resource *pDstResource,UINT DstSubresource,ID3D11Resource *pSrcResource,UINT SrcSubresource,DXGI_FORMAT Format) {
    This->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format);
}
static inline void ID3D11DeviceContext1_ExecuteCommandList(ID3D11DeviceContext1* This,ID3D11CommandList *pCommandList,WINBOOL RestoreContextState) {
    This->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState);
}
static inline void ID3D11DeviceContext1_HSSetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_HSSetShader(ID3D11DeviceContext1* This,ID3D11HullShader *pHullShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext1_HSSetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_HSSetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_DSSetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_DSSetShader(ID3D11DeviceContext1* This,ID3D11DomainShader *pDomainShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext1_DSSetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_DSSetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_CSSetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_CSSetUnorderedAccessViews(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext1_CSSetShader(ID3D11DeviceContext1* This,ID3D11ComputeShader *pComputeShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext1_CSSetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_CSSetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_VSGetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_PSGetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_PSGetShader(ID3D11DeviceContext1* This,ID3D11PixelShader **ppPixelShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext1_PSGetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_VSGetShader(ID3D11DeviceContext1* This,ID3D11VertexShader **ppVertexShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext1_PSGetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_IAGetInputLayout(ID3D11DeviceContext1* This,ID3D11InputLayout **ppInputLayout) {
    This->lpVtbl->IAGetInputLayout(This,ppInputLayout);
}
static inline void ID3D11DeviceContext1_IAGetVertexBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppVertexBuffers,UINT *pStrides,UINT *pOffsets) {
    This->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext1_IAGetIndexBuffer(ID3D11DeviceContext1* This,ID3D11Buffer **pIndexBuffer,DXGI_FORMAT *Format,UINT *Offset) {
    This->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext1_GSGetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_GSGetShader(ID3D11DeviceContext1* This,ID3D11GeometryShader **ppGeometryShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext1_IAGetPrimitiveTopology(ID3D11DeviceContext1* This,D3D11_PRIMITIVE_TOPOLOGY *pTopology) {
    This->lpVtbl->IAGetPrimitiveTopology(This,pTopology);
}
static inline void ID3D11DeviceContext1_VSGetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_VSGetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_GetPredication(ID3D11DeviceContext1* This,ID3D11Predicate **ppPredicate,WINBOOL *pPredicateValue) {
    This->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue);
}
static inline void ID3D11DeviceContext1_GSGetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_GSGetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_OMGetRenderTargets(ID3D11DeviceContext1* This,UINT NumViews,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView) {
    This->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView);
}
static inline void ID3D11DeviceContext1_OMGetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext1* This,UINT NumRTVs,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext1_OMGetBlendState(ID3D11DeviceContext1* This,ID3D11BlendState **ppBlendState,FLOAT BlendFactor[4],UINT *pSampleMask) {
    This->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask);
}
static inline void ID3D11DeviceContext1_OMGetDepthStencilState(ID3D11DeviceContext1* This,ID3D11DepthStencilState **ppDepthStencilState,UINT *pStencilRef) {
    This->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef);
}
static inline void ID3D11DeviceContext1_SOGetTargets(ID3D11DeviceContext1* This,UINT NumBuffers,ID3D11Buffer **ppSOTargets) {
    This->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets);
}
static inline void ID3D11DeviceContext1_RSGetState(ID3D11DeviceContext1* This,ID3D11RasterizerState **ppRasterizerState) {
    This->lpVtbl->RSGetState(This,ppRasterizerState);
}
static inline void ID3D11DeviceContext1_RSGetViewports(ID3D11DeviceContext1* This,UINT *pNumViewports,D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSGetViewports(This,pNumViewports,pViewports);
}
static inline void ID3D11DeviceContext1_RSGetScissorRects(ID3D11DeviceContext1* This,UINT *pNumRects,D3D11_RECT *pRects) {
    This->lpVtbl->RSGetScissorRects(This,pNumRects,pRects);
}
static inline void ID3D11DeviceContext1_HSGetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_HSGetShader(ID3D11DeviceContext1* This,ID3D11HullShader **ppHullShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext1_HSGetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_HSGetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_DSGetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_DSGetShader(ID3D11DeviceContext1* This,ID3D11DomainShader **ppDomainShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext1_DSGetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_DSGetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_CSGetShaderResources(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext1_CSGetUnorderedAccessViews(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext1_CSGetShader(ID3D11DeviceContext1* This,ID3D11ComputeShader **ppComputeShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext1_CSGetSamplers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext1_CSGetConstantBuffers(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext1_ClearState(ID3D11DeviceContext1* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D11DeviceContext1_Flush(ID3D11DeviceContext1* This) {
    This->lpVtbl->Flush(This);
}
static inline D3D11_DEVICE_CONTEXT_TYPE ID3D11DeviceContext1_GetType(ID3D11DeviceContext1* This) {
    return This->lpVtbl->GetType(This);
}
static inline UINT ID3D11DeviceContext1_GetContextFlags(ID3D11DeviceContext1* This) {
    return This->lpVtbl->GetContextFlags(This);
}
static inline HRESULT ID3D11DeviceContext1_FinishCommandList(ID3D11DeviceContext1* This,WINBOOL RestoreDeferredContextState,ID3D11CommandList **ppCommandList) {
    return This->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList);
}
/*** ID3D11DeviceContext1 methods ***/
static inline void ID3D11DeviceContext1_CopySubresourceRegion1(ID3D11DeviceContext1* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox,UINT CopyFlags) {
    This->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags);
}
static inline void ID3D11DeviceContext1_UpdateSubresource1(ID3D11DeviceContext1* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch,UINT CopyFlags) {
    This->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags);
}
static inline void ID3D11DeviceContext1_DiscardResource(ID3D11DeviceContext1* This,ID3D11Resource *pResource) {
    This->lpVtbl->DiscardResource(This,pResource);
}
static inline void ID3D11DeviceContext1_DiscardView(ID3D11DeviceContext1* This,ID3D11View *pResourceView) {
    This->lpVtbl->DiscardView(This,pResourceView);
}
static inline void ID3D11DeviceContext1_VSSetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_HSSetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_DSSetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_GSSetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_PSSetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_CSSetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_VSGetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_HSGetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_DSGetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_GSGetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_PSGetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_CSGetConstantBuffers1(ID3D11DeviceContext1* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext1_SwapDeviceContextState(ID3D11DeviceContext1* This,ID3DDeviceContextState *pState,ID3DDeviceContextState **ppPreviousState) {
    This->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState);
}
static inline void ID3D11DeviceContext1_ClearView(ID3D11DeviceContext1* This,ID3D11View *pView,const FLOAT Color[4],const D3D11_RECT *pRect,UINT NumRects) {
    This->lpVtbl->ClearView(This,pView,Color,pRect,NumRects);
}
static inline void ID3D11DeviceContext1_DiscardView1(ID3D11DeviceContext1* This,ID3D11View *pResourceView,const D3D11_RECT *pRects,UINT NumRects) {
    This->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects);
}
#endif
#endif

#endif


#endif  /* __ID3D11DeviceContext1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoContext1 interface
 */
#ifndef __ID3D11VideoContext1_INTERFACE_DEFINED__
#define __ID3D11VideoContext1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoContext1, 0xa7f026da, 0xa5f8, 0x4487, 0xa5,0x64, 0x15,0xe3,0x43,0x57,0x65,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a7f026da-a5f8-4487-a564-15e34357651e")
ID3D11VideoContext1 : public ID3D11VideoContext
{
    virtual HRESULT STDMETHODCALLTYPE SubmitDecoderBuffers1(
        ID3D11VideoDecoder *decoder,
        UINT buffer_count,
        const D3D11_VIDEO_DECODER_BUFFER_DESC1 *buffer_desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDataForNewHardwareKey(
        ID3D11CryptoSession *session,
        UINT input_size,
        const void *input_data,
        UINT64 *output_data) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckCryptoSessionStatus(
        ID3D11CryptoSession *session,
        D3D11_CRYPTO_SESSION_STATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE DecoderEnableDownsampling(
        ID3D11VideoDecoder *decoder,
        DXGI_COLOR_SPACE_TYPE colour_space,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc,
        UINT reference_frame_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE DecoderUpdateDownsampling(
        ID3D11VideoDecoder *decoder,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputColorSpace1(
        ID3D11VideoProcessor *processor,
        DXGI_COLOR_SPACE_TYPE colour_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputShaderUsage(
        ID3D11VideoProcessor *processor,
        WINBOOL shader_usage) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputColorSpace1(
        ID3D11VideoProcessor *processor,
        DXGI_COLOR_SPACE_TYPE *colour_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputShaderUsage(
        ID3D11VideoProcessor *processor,
        WINBOOL *shader_usage) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamColorSpace1(
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_COLOR_SPACE_TYPE colour_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamMirror(
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        WINBOOL enable,
        WINBOOL flip_horizontal,
        WINBOOL flip_vertical) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamColorSpace1(
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_COLOR_SPACE_TYPE *colour_space) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamMirror(
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        WINBOOL *enable,
        WINBOOL *flip_horizontal,
        WINBOOL *flip_vertical) = 0;

    virtual HRESULT STDMETHODCALLTYPE VideoProcessorGetBehaviorHints(
        ID3D11VideoProcessor *processor,
        UINT output_width,
        UINT output_height,
        DXGI_FORMAT output_format,
        UINT stream_count,
        const D3D11_VIDEO_PROCESSOR_STREAM_BEHAVIOR_HINT *streams,
        UINT *behaviour_hints) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoContext1, 0xa7f026da, 0xa5f8, 0x4487, 0xa5,0x64, 0x15,0xe3,0x43,0x57,0x65,0x1e)
#endif
#else
typedef struct ID3D11VideoContext1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoContext1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoContext1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoContext1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoContext1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoContext1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoContext1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoContext1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoContext methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDecoderBuffer)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type,
        UINT *buffer_size,
        void **buffer);

    HRESULT (STDMETHODCALLTYPE *ReleaseDecoderBuffer)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type);

    HRESULT (STDMETHODCALLTYPE *DecoderBeginFrame)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        ID3D11VideoDecoderOutputView *view,
        UINT key_size,
        const void *key);

    HRESULT (STDMETHODCALLTYPE *DecoderEndFrame)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder);

    HRESULT (STDMETHODCALLTYPE *SubmitDecoderBuffers)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        UINT buffers_count,
        const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc);

    HRESULT (STDMETHODCALLTYPE *DecoderExtension)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        const D3D11_VIDEO_DECODER_EXTENSION *extension);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputTargetRect)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputBackgroundColor)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL y_cb_cr,
        const D3D11_VIDEO_COLOR *color);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputColorSpace)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputAlphaFillMode)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,
        UINT stream_idx);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputConstriction)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        SIZE size);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputStereoMode)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorSetOutputExtension)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputTargetRect)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputBackgroundColor)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *y_cb_cr,
        D3D11_VIDEO_COLOR *color);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputColorSpace)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputAlphaFillMode)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,
        UINT *stream_idx);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputConstriction)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        SIZE *size);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputStereoMode)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetOutputExtension)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamFrameFormat)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT format);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamColorSpace)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamOutputRate)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,
        WINBOOL repeat,
        const DXGI_RATIONAL *custom_rate);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamSourceRect)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamDestRect)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamAlpha)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float alpha);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamPalette)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        const UINT *entries);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamPixelAspectRatio)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const DXGI_RATIONAL *src_aspect_ratio,
        const DXGI_RATIONAL *dst_aspect_ratio);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamLumaKey)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float lower,
        float upper);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamStereoFormat)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,
        WINBOOL left_view_frame0,
        WINBOOL base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,
        int mono_offset);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamAutoProcessingMode)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamFilter)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL enable,
        int level);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorSetStreamExtension)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamFrameFormat)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT *format);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamColorSpace)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamOutputRate)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,
        WINBOOL *repeat,
        DXGI_RATIONAL *custom_rate);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamSourceRect)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamDestRect)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamAlpha)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *alpha);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamPalette)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        UINT *entries);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamPixelAspectRatio)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        DXGI_RATIONAL *src_aspect_ratio,
        DXGI_RATIONAL *dst_aspect_ratio);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamLumaKey)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *lower,
        float *upper);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamStereoFormat)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,
        WINBOOL *left_view_frame0,
        WINBOOL *base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,
        int *mono_offset);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamAutoProcessingMode)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamFilter)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL *enabled,
        int *level);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetStreamExtension)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorBlt)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        ID3D11VideoProcessorOutputView *view,
        UINT frame_idx,
        UINT stream_count,
        const D3D11_VIDEO_PROCESSOR_STREAM *streams);

    HRESULT (STDMETHODCALLTYPE *NegotiateCryptoSessionKeyExchange)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *EncryptionBlt)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        UINT iv_size,
        void *iv);

    void (STDMETHODCALLTYPE *DecryptionBlt)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        D3D11_ENCRYPTED_BLOCK_INFO *block_info,
        UINT key_size,
        const void *key,
        UINT iv_size,
        void *iv);

    void (STDMETHODCALLTYPE *StartSessionKeyRefresh)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session,
        UINT random_number_size,
        void *random_number);

    void (STDMETHODCALLTYPE *FinishSessionKeyRefresh)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session);

    HRESULT (STDMETHODCALLTYPE *GetEncryptionBltKey)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session,
        UINT key_size,
        void *key);

    HRESULT (STDMETHODCALLTYPE *NegotiateAuthenticatedChannelKeyExchange)(
        ID3D11VideoContext1 *This,
        ID3D11AuthenticatedChannel *channel,
        UINT data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *QueryAuthenticatedChannel)(
        ID3D11VideoContext1 *This,
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        UINT output_size,
        void *output);

    HRESULT (STDMETHODCALLTYPE *ConfigureAuthenticatedChannel)(
        ID3D11VideoContext1 *This,
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamRotation)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_ROTATION rotation);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamRotation)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enable,
        D3D11_VIDEO_PROCESSOR_ROTATION *rotation);

    /*** ID3D11VideoContext1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SubmitDecoderBuffers1)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        UINT buffer_count,
        const D3D11_VIDEO_DECODER_BUFFER_DESC1 *buffer_desc);

    HRESULT (STDMETHODCALLTYPE *GetDataForNewHardwareKey)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session,
        UINT input_size,
        const void *input_data,
        UINT64 *output_data);

    HRESULT (STDMETHODCALLTYPE *CheckCryptoSessionStatus)(
        ID3D11VideoContext1 *This,
        ID3D11CryptoSession *session,
        D3D11_CRYPTO_SESSION_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *DecoderEnableDownsampling)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        DXGI_COLOR_SPACE_TYPE colour_space,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc,
        UINT reference_frame_count);

    HRESULT (STDMETHODCALLTYPE *DecoderUpdateDownsampling)(
        ID3D11VideoContext1 *This,
        ID3D11VideoDecoder *decoder,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputColorSpace1)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        DXGI_COLOR_SPACE_TYPE colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputShaderUsage)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL shader_usage);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputColorSpace1)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        DXGI_COLOR_SPACE_TYPE *colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputShaderUsage)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *shader_usage);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamColorSpace1)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_COLOR_SPACE_TYPE colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamMirror)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        WINBOOL enable,
        WINBOOL flip_horizontal,
        WINBOOL flip_vertical);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamColorSpace1)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_COLOR_SPACE_TYPE *colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamMirror)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        WINBOOL *enable,
        WINBOOL *flip_horizontal,
        WINBOOL *flip_vertical);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetBehaviorHints)(
        ID3D11VideoContext1 *This,
        ID3D11VideoProcessor *processor,
        UINT output_width,
        UINT output_height,
        DXGI_FORMAT output_format,
        UINT stream_count,
        const D3D11_VIDEO_PROCESSOR_STREAM_BEHAVIOR_HINT *streams,
        UINT *behaviour_hints);

    END_INTERFACE
} ID3D11VideoContext1Vtbl;

interface ID3D11VideoContext1 {
    CONST_VTBL ID3D11VideoContext1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoContext1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoContext1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoContext1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoContext1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoContext1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoContext1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoContext1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoContext methods ***/
#define ID3D11VideoContext1_GetDecoderBuffer(This,decoder,type,buffer_size,buffer) (This)->lpVtbl->GetDecoderBuffer(This,decoder,type,buffer_size,buffer)
#define ID3D11VideoContext1_ReleaseDecoderBuffer(This,decoder,type) (This)->lpVtbl->ReleaseDecoderBuffer(This,decoder,type)
#define ID3D11VideoContext1_DecoderBeginFrame(This,decoder,view,key_size,key) (This)->lpVtbl->DecoderBeginFrame(This,decoder,view,key_size,key)
#define ID3D11VideoContext1_DecoderEndFrame(This,decoder) (This)->lpVtbl->DecoderEndFrame(This,decoder)
#define ID3D11VideoContext1_SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc) (This)->lpVtbl->SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc)
#define ID3D11VideoContext1_DecoderExtension(This,decoder,extension) (This)->lpVtbl->DecoderExtension(This,decoder,extension)
#define ID3D11VideoContext1_VideoProcessorSetOutputTargetRect(This,processor,enable,rect) (This)->lpVtbl->VideoProcessorSetOutputTargetRect(This,processor,enable,rect)
#define ID3D11VideoContext1_VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color) (This)->lpVtbl->VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color)
#define ID3D11VideoContext1_VideoProcessorSetOutputColorSpace(This,processor,color_space) (This)->lpVtbl->VideoProcessorSetOutputColorSpace(This,processor,color_space)
#define ID3D11VideoContext1_VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx) (This)->lpVtbl->VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx)
#define ID3D11VideoContext1_VideoProcessorSetOutputConstriction(This,processor,enable,size) (This)->lpVtbl->VideoProcessorSetOutputConstriction(This,processor,enable,size)
#define ID3D11VideoContext1_VideoProcessorSetOutputStereoMode(This,processor,enable) (This)->lpVtbl->VideoProcessorSetOutputStereoMode(This,processor,enable)
#define ID3D11VideoContext1_VideoProcessorSetOutputExtension(This,processor,guid,data_size,data) (This)->lpVtbl->VideoProcessorSetOutputExtension(This,processor,guid,data_size,data)
#define ID3D11VideoContext1_VideoProcessorGetOutputTargetRect(This,processor,enabled,rect) (This)->lpVtbl->VideoProcessorGetOutputTargetRect(This,processor,enabled,rect)
#define ID3D11VideoContext1_VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color) (This)->lpVtbl->VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color)
#define ID3D11VideoContext1_VideoProcessorGetOutputColorSpace(This,processor,color_space) (This)->lpVtbl->VideoProcessorGetOutputColorSpace(This,processor,color_space)
#define ID3D11VideoContext1_VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx) (This)->lpVtbl->VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx)
#define ID3D11VideoContext1_VideoProcessorGetOutputConstriction(This,processor,enabled,size) (This)->lpVtbl->VideoProcessorGetOutputConstriction(This,processor,enabled,size)
#define ID3D11VideoContext1_VideoProcessorGetOutputStereoMode(This,processor,enabled) (This)->lpVtbl->VideoProcessorGetOutputStereoMode(This,processor,enabled)
#define ID3D11VideoContext1_VideoProcessorGetOutputExtension(This,processor,guid,data_size,data) (This)->lpVtbl->VideoProcessorGetOutputExtension(This,processor,guid,data_size,data)
#define ID3D11VideoContext1_VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format) (This)->lpVtbl->VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format)
#define ID3D11VideoContext1_VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space) (This)->lpVtbl->VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space)
#define ID3D11VideoContext1_VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate) (This)->lpVtbl->VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate)
#define ID3D11VideoContext1_VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect) (This)->lpVtbl->VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect)
#define ID3D11VideoContext1_VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect) (This)->lpVtbl->VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect)
#define ID3D11VideoContext1_VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha) (This)->lpVtbl->VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha)
#define ID3D11VideoContext1_VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries) (This)->lpVtbl->VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries)
#define ID3D11VideoContext1_VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio) (This)->lpVtbl->VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio)
#define ID3D11VideoContext1_VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper) (This)->lpVtbl->VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper)
#define ID3D11VideoContext1_VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset) (This)->lpVtbl->VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset)
#define ID3D11VideoContext1_VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable) (This)->lpVtbl->VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable)
#define ID3D11VideoContext1_VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level) (This)->lpVtbl->VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level)
#define ID3D11VideoContext1_VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data) (This)->lpVtbl->VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data)
#define ID3D11VideoContext1_VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format) (This)->lpVtbl->VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format)
#define ID3D11VideoContext1_VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space) (This)->lpVtbl->VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space)
#define ID3D11VideoContext1_VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate) (This)->lpVtbl->VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate)
#define ID3D11VideoContext1_VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect) (This)->lpVtbl->VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect)
#define ID3D11VideoContext1_VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect) (This)->lpVtbl->VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect)
#define ID3D11VideoContext1_VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha) (This)->lpVtbl->VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha)
#define ID3D11VideoContext1_VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries) (This)->lpVtbl->VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries)
#define ID3D11VideoContext1_VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio) (This)->lpVtbl->VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio)
#define ID3D11VideoContext1_VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper) (This)->lpVtbl->VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper)
#define ID3D11VideoContext1_VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset) (This)->lpVtbl->VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset)
#define ID3D11VideoContext1_VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled) (This)->lpVtbl->VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled)
#define ID3D11VideoContext1_VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level) (This)->lpVtbl->VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level)
#define ID3D11VideoContext1_VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data) (This)->lpVtbl->VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data)
#define ID3D11VideoContext1_VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams) (This)->lpVtbl->VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams)
#define ID3D11VideoContext1_NegotiateCryptoSessionKeyExchange(This,session,data_size,data) (This)->lpVtbl->NegotiateCryptoSessionKeyExchange(This,session,data_size,data)
#define ID3D11VideoContext1_EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv) (This)->lpVtbl->EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv)
#define ID3D11VideoContext1_DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv) (This)->lpVtbl->DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv)
#define ID3D11VideoContext1_StartSessionKeyRefresh(This,session,random_number_size,random_number) (This)->lpVtbl->StartSessionKeyRefresh(This,session,random_number_size,random_number)
#define ID3D11VideoContext1_FinishSessionKeyRefresh(This,session) (This)->lpVtbl->FinishSessionKeyRefresh(This,session)
#define ID3D11VideoContext1_GetEncryptionBltKey(This,session,key_size,key) (This)->lpVtbl->GetEncryptionBltKey(This,session,key_size,key)
#define ID3D11VideoContext1_NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data) (This)->lpVtbl->NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data)
#define ID3D11VideoContext1_QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output) (This)->lpVtbl->QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output)
#define ID3D11VideoContext1_ConfigureAuthenticatedChannel(This,channel,input_size,input,output) (This)->lpVtbl->ConfigureAuthenticatedChannel(This,channel,input_size,input,output)
#define ID3D11VideoContext1_VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation) (This)->lpVtbl->VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation)
#define ID3D11VideoContext1_VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation) (This)->lpVtbl->VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation)
/*** ID3D11VideoContext1 methods ***/
#define ID3D11VideoContext1_SubmitDecoderBuffers1(This,decoder,buffer_count,buffer_desc) (This)->lpVtbl->SubmitDecoderBuffers1(This,decoder,buffer_count,buffer_desc)
#define ID3D11VideoContext1_GetDataForNewHardwareKey(This,session,input_size,input_data,output_data) (This)->lpVtbl->GetDataForNewHardwareKey(This,session,input_size,input_data,output_data)
#define ID3D11VideoContext1_CheckCryptoSessionStatus(This,session,status) (This)->lpVtbl->CheckCryptoSessionStatus(This,session,status)
#define ID3D11VideoContext1_DecoderEnableDownsampling(This,decoder,colour_space,output_desc,reference_frame_count) (This)->lpVtbl->DecoderEnableDownsampling(This,decoder,colour_space,output_desc,reference_frame_count)
#define ID3D11VideoContext1_DecoderUpdateDownsampling(This,decoder,output_desc) (This)->lpVtbl->DecoderUpdateDownsampling(This,decoder,output_desc)
#define ID3D11VideoContext1_VideoProcessorSetOutputColorSpace1(This,processor,colour_space) (This)->lpVtbl->VideoProcessorSetOutputColorSpace1(This,processor,colour_space)
#define ID3D11VideoContext1_VideoProcessorSetOutputShaderUsage(This,processor,shader_usage) (This)->lpVtbl->VideoProcessorSetOutputShaderUsage(This,processor,shader_usage)
#define ID3D11VideoContext1_VideoProcessorGetOutputColorSpace1(This,processor,colour_space) (This)->lpVtbl->VideoProcessorGetOutputColorSpace1(This,processor,colour_space)
#define ID3D11VideoContext1_VideoProcessorGetOutputShaderUsage(This,processor,shader_usage) (This)->lpVtbl->VideoProcessorGetOutputShaderUsage(This,processor,shader_usage)
#define ID3D11VideoContext1_VideoProcessorSetStreamColorSpace1(This,processor,stream_index,colour_space) (This)->lpVtbl->VideoProcessorSetStreamColorSpace1(This,processor,stream_index,colour_space)
#define ID3D11VideoContext1_VideoProcessorSetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical) (This)->lpVtbl->VideoProcessorSetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical)
#define ID3D11VideoContext1_VideoProcessorGetStreamColorSpace1(This,processor,stream_index,colour_space) (This)->lpVtbl->VideoProcessorGetStreamColorSpace1(This,processor,stream_index,colour_space)
#define ID3D11VideoContext1_VideoProcessorGetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical) (This)->lpVtbl->VideoProcessorGetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical)
#define ID3D11VideoContext1_VideoProcessorGetBehaviorHints(This,processor,output_width,output_height,output_format,stream_count,streams,behaviour_hints) (This)->lpVtbl->VideoProcessorGetBehaviorHints(This,processor,output_width,output_height,output_format,stream_count,streams,behaviour_hints)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11VideoContext1_QueryInterface(ID3D11VideoContext1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11VideoContext1_AddRef(ID3D11VideoContext1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11VideoContext1_Release(ID3D11VideoContext1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11VideoContext1_GetDevice(ID3D11VideoContext1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11VideoContext1_GetPrivateData(ID3D11VideoContext1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11VideoContext1_SetPrivateData(ID3D11VideoContext1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11VideoContext1_SetPrivateDataInterface(ID3D11VideoContext1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoContext methods ***/
static inline HRESULT ID3D11VideoContext1_GetDecoderBuffer(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,D3D11_VIDEO_DECODER_BUFFER_TYPE type,UINT *buffer_size,void **buffer) {
    return This->lpVtbl->GetDecoderBuffer(This,decoder,type,buffer_size,buffer);
}
static inline HRESULT ID3D11VideoContext1_ReleaseDecoderBuffer(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,D3D11_VIDEO_DECODER_BUFFER_TYPE type) {
    return This->lpVtbl->ReleaseDecoderBuffer(This,decoder,type);
}
static inline HRESULT ID3D11VideoContext1_DecoderBeginFrame(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,ID3D11VideoDecoderOutputView *view,UINT key_size,const void *key) {
    return This->lpVtbl->DecoderBeginFrame(This,decoder,view,key_size,key);
}
static inline HRESULT ID3D11VideoContext1_DecoderEndFrame(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder) {
    return This->lpVtbl->DecoderEndFrame(This,decoder);
}
static inline HRESULT ID3D11VideoContext1_SubmitDecoderBuffers(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,UINT buffers_count,const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc) {
    return This->lpVtbl->SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc);
}
static inline HRESULT ID3D11VideoContext1_DecoderExtension(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,const D3D11_VIDEO_DECODER_EXTENSION *extension) {
    return This->lpVtbl->DecoderExtension(This,decoder,extension);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputTargetRect(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetOutputTargetRect(This,processor,enable,rect);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputBackgroundColor(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL y_cb_cr,const D3D11_VIDEO_COLOR *color) {
    This->lpVtbl->VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputColorSpace(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorSetOutputColorSpace(This,processor,color_space);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputAlphaFillMode(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,UINT stream_idx) {
    This->lpVtbl->VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputConstriction(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL enable,SIZE size) {
    This->lpVtbl->VideoProcessorSetOutputConstriction(This,processor,enable,size);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputStereoMode(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL enable) {
    This->lpVtbl->VideoProcessorSetOutputStereoMode(This,processor,enable);
}
static inline HRESULT ID3D11VideoContext1_VideoProcessorSetOutputExtension(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorSetOutputExtension(This,processor,guid,data_size,data);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputTargetRect(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetOutputTargetRect(This,processor,enabled,rect);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputBackgroundColor(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL *y_cb_cr,D3D11_VIDEO_COLOR *color) {
    This->lpVtbl->VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputColorSpace(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorGetOutputColorSpace(This,processor,color_space);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputAlphaFillMode(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,UINT *stream_idx) {
    This->lpVtbl->VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputConstriction(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL *enabled,SIZE *size) {
    This->lpVtbl->VideoProcessorGetOutputConstriction(This,processor,enabled,size);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputStereoMode(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL *enabled) {
    This->lpVtbl->VideoProcessorGetOutputStereoMode(This,processor,enabled);
}
static inline HRESULT ID3D11VideoContext1_VideoProcessorGetOutputExtension(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorGetOutputExtension(This,processor,guid,data_size,data);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamFrameFormat(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_FRAME_FORMAT format) {
    This->lpVtbl->VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamColorSpace(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamOutputRate(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,WINBOOL repeat,const DXGI_RATIONAL *custom_rate) {
    This->lpVtbl->VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamSourceRect(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamDestRect(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamAlpha(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,float alpha) {
    This->lpVtbl->VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamPalette(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,UINT entry_count,const UINT *entries) {
    This->lpVtbl->VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamPixelAspectRatio(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const DXGI_RATIONAL *src_aspect_ratio,const DXGI_RATIONAL *dst_aspect_ratio) {
    This->lpVtbl->VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamLumaKey(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,float lower,float upper) {
    This->lpVtbl->VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamStereoFormat(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,WINBOOL left_view_frame0,WINBOOL base_view_frame0,D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,int mono_offset) {
    This->lpVtbl->VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamAutoProcessingMode(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable) {
    This->lpVtbl->VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamFilter(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_FILTER filter,WINBOOL enable,int level) {
    This->lpVtbl->VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level);
}
static inline HRESULT ID3D11VideoContext1_VideoProcessorSetStreamExtension(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamFrameFormat(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_FRAME_FORMAT *format) {
    This->lpVtbl->VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamColorSpace(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamOutputRate(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,WINBOOL *repeat,DXGI_RATIONAL *custom_rate) {
    This->lpVtbl->VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamSourceRect(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamDestRect(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamAlpha(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,float *alpha) {
    This->lpVtbl->VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamPalette(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,UINT entry_count,UINT *entries) {
    This->lpVtbl->VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamPixelAspectRatio(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,DXGI_RATIONAL *src_aspect_ratio,DXGI_RATIONAL *dst_aspect_ratio) {
    This->lpVtbl->VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamLumaKey(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,float *lower,float *upper) {
    This->lpVtbl->VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamStereoFormat(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,WINBOOL *left_view_frame0,WINBOOL *base_view_frame0,D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,int *mono_offset) {
    This->lpVtbl->VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamAutoProcessingMode(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled) {
    This->lpVtbl->VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamFilter(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_FILTER filter,WINBOOL *enabled,int *level) {
    This->lpVtbl->VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level);
}
static inline HRESULT ID3D11VideoContext1_VideoProcessorGetStreamExtension(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data);
}
static inline HRESULT ID3D11VideoContext1_VideoProcessorBlt(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,ID3D11VideoProcessorOutputView *view,UINT frame_idx,UINT stream_count,const D3D11_VIDEO_PROCESSOR_STREAM *streams) {
    return This->lpVtbl->VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams);
}
static inline HRESULT ID3D11VideoContext1_NegotiateCryptoSessionKeyExchange(ID3D11VideoContext1* This,ID3D11CryptoSession *session,UINT data_size,void *data) {
    return This->lpVtbl->NegotiateCryptoSessionKeyExchange(This,session,data_size,data);
}
static inline void ID3D11VideoContext1_EncryptionBlt(ID3D11VideoContext1* This,ID3D11CryptoSession *session,ID3D11Texture2D *src_surface,ID3D11Texture2D *dst_surface,UINT iv_size,void *iv) {
    This->lpVtbl->EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv);
}
static inline void ID3D11VideoContext1_DecryptionBlt(ID3D11VideoContext1* This,ID3D11CryptoSession *session,ID3D11Texture2D *src_surface,ID3D11Texture2D *dst_surface,D3D11_ENCRYPTED_BLOCK_INFO *block_info,UINT key_size,const void *key,UINT iv_size,void *iv) {
    This->lpVtbl->DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv);
}
static inline void ID3D11VideoContext1_StartSessionKeyRefresh(ID3D11VideoContext1* This,ID3D11CryptoSession *session,UINT random_number_size,void *random_number) {
    This->lpVtbl->StartSessionKeyRefresh(This,session,random_number_size,random_number);
}
static inline void ID3D11VideoContext1_FinishSessionKeyRefresh(ID3D11VideoContext1* This,ID3D11CryptoSession *session) {
    This->lpVtbl->FinishSessionKeyRefresh(This,session);
}
static inline HRESULT ID3D11VideoContext1_GetEncryptionBltKey(ID3D11VideoContext1* This,ID3D11CryptoSession *session,UINT key_size,void *key) {
    return This->lpVtbl->GetEncryptionBltKey(This,session,key_size,key);
}
static inline HRESULT ID3D11VideoContext1_NegotiateAuthenticatedChannelKeyExchange(ID3D11VideoContext1* This,ID3D11AuthenticatedChannel *channel,UINT data_size,void *data) {
    return This->lpVtbl->NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data);
}
static inline HRESULT ID3D11VideoContext1_QueryAuthenticatedChannel(ID3D11VideoContext1* This,ID3D11AuthenticatedChannel *channel,UINT input_size,const void *input,UINT output_size,void *output) {
    return This->lpVtbl->QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output);
}
static inline HRESULT ID3D11VideoContext1_ConfigureAuthenticatedChannel(ID3D11VideoContext1* This,ID3D11AuthenticatedChannel *channel,UINT input_size,const void *input,D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output) {
    return This->lpVtbl->ConfigureAuthenticatedChannel(This,channel,input_size,input,output);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamRotation(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,D3D11_VIDEO_PROCESSOR_ROTATION rotation) {
    This->lpVtbl->VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamRotation(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enable,D3D11_VIDEO_PROCESSOR_ROTATION *rotation) {
    This->lpVtbl->VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation);
}
/*** ID3D11VideoContext1 methods ***/
static inline HRESULT ID3D11VideoContext1_SubmitDecoderBuffers1(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,UINT buffer_count,const D3D11_VIDEO_DECODER_BUFFER_DESC1 *buffer_desc) {
    return This->lpVtbl->SubmitDecoderBuffers1(This,decoder,buffer_count,buffer_desc);
}
static inline HRESULT ID3D11VideoContext1_GetDataForNewHardwareKey(ID3D11VideoContext1* This,ID3D11CryptoSession *session,UINT input_size,const void *input_data,UINT64 *output_data) {
    return This->lpVtbl->GetDataForNewHardwareKey(This,session,input_size,input_data,output_data);
}
static inline HRESULT ID3D11VideoContext1_CheckCryptoSessionStatus(ID3D11VideoContext1* This,ID3D11CryptoSession *session,D3D11_CRYPTO_SESSION_STATUS *status) {
    return This->lpVtbl->CheckCryptoSessionStatus(This,session,status);
}
static inline HRESULT ID3D11VideoContext1_DecoderEnableDownsampling(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,DXGI_COLOR_SPACE_TYPE colour_space,const D3D11_VIDEO_SAMPLE_DESC *output_desc,UINT reference_frame_count) {
    return This->lpVtbl->DecoderEnableDownsampling(This,decoder,colour_space,output_desc,reference_frame_count);
}
static inline HRESULT ID3D11VideoContext1_DecoderUpdateDownsampling(ID3D11VideoContext1* This,ID3D11VideoDecoder *decoder,const D3D11_VIDEO_SAMPLE_DESC *output_desc) {
    return This->lpVtbl->DecoderUpdateDownsampling(This,decoder,output_desc);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputColorSpace1(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,DXGI_COLOR_SPACE_TYPE colour_space) {
    This->lpVtbl->VideoProcessorSetOutputColorSpace1(This,processor,colour_space);
}
static inline void ID3D11VideoContext1_VideoProcessorSetOutputShaderUsage(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL shader_usage) {
    This->lpVtbl->VideoProcessorSetOutputShaderUsage(This,processor,shader_usage);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputColorSpace1(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,DXGI_COLOR_SPACE_TYPE *colour_space) {
    This->lpVtbl->VideoProcessorGetOutputColorSpace1(This,processor,colour_space);
}
static inline void ID3D11VideoContext1_VideoProcessorGetOutputShaderUsage(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,WINBOOL *shader_usage) {
    This->lpVtbl->VideoProcessorGetOutputShaderUsage(This,processor,shader_usage);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamColorSpace1(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_index,DXGI_COLOR_SPACE_TYPE colour_space) {
    This->lpVtbl->VideoProcessorSetStreamColorSpace1(This,processor,stream_index,colour_space);
}
static inline void ID3D11VideoContext1_VideoProcessorSetStreamMirror(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_index,WINBOOL enable,WINBOOL flip_horizontal,WINBOOL flip_vertical) {
    This->lpVtbl->VideoProcessorSetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamColorSpace1(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_index,DXGI_COLOR_SPACE_TYPE *colour_space) {
    This->lpVtbl->VideoProcessorGetStreamColorSpace1(This,processor,stream_index,colour_space);
}
static inline void ID3D11VideoContext1_VideoProcessorGetStreamMirror(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT stream_index,WINBOOL *enable,WINBOOL *flip_horizontal,WINBOOL *flip_vertical) {
    This->lpVtbl->VideoProcessorGetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical);
}
static inline HRESULT ID3D11VideoContext1_VideoProcessorGetBehaviorHints(ID3D11VideoContext1* This,ID3D11VideoProcessor *processor,UINT output_width,UINT output_height,DXGI_FORMAT output_format,UINT stream_count,const D3D11_VIDEO_PROCESSOR_STREAM_BEHAVIOR_HINT *streams,UINT *behaviour_hints) {
    return This->lpVtbl->VideoProcessorGetBehaviorHints(This,processor,output_width,output_height,output_format,stream_count,streams,behaviour_hints);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoContext1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoDevice1 interface
 */
#ifndef __ID3D11VideoDevice1_INTERFACE_DEFINED__
#define __ID3D11VideoDevice1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoDevice1, 0x29da1d51, 0x1321, 0x4454, 0x80,0x4b, 0xf5,0xfc,0x9f,0x86,0x1f,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("29da1d51-1321-4454-804b-f5fc9f861f0f")
ID3D11VideoDevice1 : public ID3D11VideoDevice
{
    virtual HRESULT STDMETHODCALLTYPE GetCryptoSessionPrivateDataSize(
        const GUID *crypto_type,
        const GUID *decoder_profile,
        const GUID *key_exchange_type,
        UINT *input_size,
        UINT *output_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoDecoderCaps(
        const GUID *decoder_profile,
        UINT sample_width,
        UINT sample_height,
        const DXGI_RATIONAL *framerate,
        UINT bitrate,
        const GUID *crypto_type,
        UINT *decoder_caps) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckVideoDecoderDownsampling(
        const D3D11_VIDEO_DECODER_DESC *input_desc,
        DXGI_COLOR_SPACE_TYPE input_colour_space,
        const D3D11_VIDEO_DECODER_CONFIG *input_config,
        const DXGI_RATIONAL *framerate,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc,
        WINBOOL *supported,
        WINBOOL *real_time_hint) = 0;

    virtual HRESULT STDMETHODCALLTYPE RecommendVideoDecoderDownsampleParameters(
        const D3D11_VIDEO_DECODER_DESC *input_desc,
        DXGI_COLOR_SPACE_TYPE input_colour_space,
        const D3D11_VIDEO_DECODER_CONFIG *input_config,
        const DXGI_RATIONAL *framerate,
        D3D11_VIDEO_SAMPLE_DESC *recommended_output_desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoDevice1, 0x29da1d51, 0x1321, 0x4454, 0x80,0x4b, 0xf5,0xfc,0x9f,0x86,0x1f,0x0f)
#endif
#else
typedef struct ID3D11VideoDevice1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoDevice1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoDevice1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoDevice1 *This);

    /*** ID3D11VideoDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder)(
        ID3D11VideoDevice1 *This,
        const D3D11_VIDEO_DECODER_DESC *pVideoDesc,
        const D3D11_VIDEO_DECODER_CONFIG *pConfig,
        ID3D11VideoDecoder **ppDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor)(
        ID3D11VideoDevice1 *This,
        ID3D11VideoProcessorEnumerator *pEnum,
        UINT RateConversionIndex,
        ID3D11VideoProcessor **ppVideoProcessor);

    HRESULT (STDMETHODCALLTYPE *CreateAuthenticatedChannel)(
        ID3D11VideoDevice1 *This,
        D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType,
        ID3D11AuthenticatedChannel **ppAuthenticatedChannel);

    HRESULT (STDMETHODCALLTYPE *CreateCryptoSession)(
        ID3D11VideoDevice1 *This,
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        const GUID *pKeyExchangeType,
        ID3D11CryptoSession **ppCryptoSession);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderOutputView)(
        ID3D11VideoDevice1 *This,
        ID3D11Resource *pResource,
        const D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoDecoderOutputView **ppVDOVView);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessorInputView)(
        ID3D11VideoDevice1 *This,
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorInputView **ppVPIView);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessorOutputView)(
        ID3D11VideoDevice1 *This,
        ID3D11Resource *pResource,
        ID3D11VideoProcessorEnumerator *pEnum,
        const D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc,
        ID3D11VideoProcessorOutputView **ppVPOView);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessorEnumerator)(
        ID3D11VideoDevice1 *This,
        const D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc,
        ID3D11VideoProcessorEnumerator **ppEnum);

    UINT (STDMETHODCALLTYPE *GetVideoDecoderProfileCount)(
        ID3D11VideoDevice1 *This);

    HRESULT (STDMETHODCALLTYPE *GetVideoDecoderProfile)(
        ID3D11VideoDevice1 *This,
        UINT Index,
        GUID *pDecoderProfile);

    HRESULT (STDMETHODCALLTYPE *CheckVideoDecoderFormat)(
        ID3D11VideoDevice1 *This,
        const GUID *pDecoderProfile,
        DXGI_FORMAT Format,
        WINBOOL *pSupported);

    HRESULT (STDMETHODCALLTYPE *GetVideoDecoderConfigCount)(
        ID3D11VideoDevice1 *This,
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT *pCount);

    HRESULT (STDMETHODCALLTYPE *GetVideoDecoderConfig)(
        ID3D11VideoDevice1 *This,
        const D3D11_VIDEO_DECODER_DESC *pDesc,
        UINT Index,
        D3D11_VIDEO_DECODER_CONFIG *pConfig);

    HRESULT (STDMETHODCALLTYPE *GetContentProtectionCaps)(
        ID3D11VideoDevice1 *This,
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        D3D11_VIDEO_CONTENT_PROTECTION_CAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *CheckCryptoKeyExchange)(
        ID3D11VideoDevice1 *This,
        const GUID *pCryptoType,
        const GUID *pDecoderProfile,
        UINT Index,
        GUID *pKeyExchangeType);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoDevice1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoDevice1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCryptoSessionPrivateDataSize)(
        ID3D11VideoDevice1 *This,
        const GUID *crypto_type,
        const GUID *decoder_profile,
        const GUID *key_exchange_type,
        UINT *input_size,
        UINT *output_size);

    HRESULT (STDMETHODCALLTYPE *GetVideoDecoderCaps)(
        ID3D11VideoDevice1 *This,
        const GUID *decoder_profile,
        UINT sample_width,
        UINT sample_height,
        const DXGI_RATIONAL *framerate,
        UINT bitrate,
        const GUID *crypto_type,
        UINT *decoder_caps);

    HRESULT (STDMETHODCALLTYPE *CheckVideoDecoderDownsampling)(
        ID3D11VideoDevice1 *This,
        const D3D11_VIDEO_DECODER_DESC *input_desc,
        DXGI_COLOR_SPACE_TYPE input_colour_space,
        const D3D11_VIDEO_DECODER_CONFIG *input_config,
        const DXGI_RATIONAL *framerate,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc,
        WINBOOL *supported,
        WINBOOL *real_time_hint);

    HRESULT (STDMETHODCALLTYPE *RecommendVideoDecoderDownsampleParameters)(
        ID3D11VideoDevice1 *This,
        const D3D11_VIDEO_DECODER_DESC *input_desc,
        DXGI_COLOR_SPACE_TYPE input_colour_space,
        const D3D11_VIDEO_DECODER_CONFIG *input_config,
        const DXGI_RATIONAL *framerate,
        D3D11_VIDEO_SAMPLE_DESC *recommended_output_desc);

    END_INTERFACE
} ID3D11VideoDevice1Vtbl;

interface ID3D11VideoDevice1 {
    CONST_VTBL ID3D11VideoDevice1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoDevice1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoDevice1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoDevice1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11VideoDevice methods ***/
#define ID3D11VideoDevice1_CreateVideoDecoder(This,pVideoDesc,pConfig,ppDecoder) (This)->lpVtbl->CreateVideoDecoder(This,pVideoDesc,pConfig,ppDecoder)
#define ID3D11VideoDevice1_CreateVideoProcessor(This,pEnum,RateConversionIndex,ppVideoProcessor) (This)->lpVtbl->CreateVideoProcessor(This,pEnum,RateConversionIndex,ppVideoProcessor)
#define ID3D11VideoDevice1_CreateAuthenticatedChannel(This,ChannelType,ppAuthenticatedChannel) (This)->lpVtbl->CreateAuthenticatedChannel(This,ChannelType,ppAuthenticatedChannel)
#define ID3D11VideoDevice1_CreateCryptoSession(This,pCryptoType,pDecoderProfile,pKeyExchangeType,ppCryptoSession) (This)->lpVtbl->CreateCryptoSession(This,pCryptoType,pDecoderProfile,pKeyExchangeType,ppCryptoSession)
#define ID3D11VideoDevice1_CreateVideoDecoderOutputView(This,pResource,pDesc,ppVDOVView) (This)->lpVtbl->CreateVideoDecoderOutputView(This,pResource,pDesc,ppVDOVView)
#define ID3D11VideoDevice1_CreateVideoProcessorInputView(This,pResource,pEnum,pDesc,ppVPIView) (This)->lpVtbl->CreateVideoProcessorInputView(This,pResource,pEnum,pDesc,ppVPIView)
#define ID3D11VideoDevice1_CreateVideoProcessorOutputView(This,pResource,pEnum,pDesc,ppVPOView) (This)->lpVtbl->CreateVideoProcessorOutputView(This,pResource,pEnum,pDesc,ppVPOView)
#define ID3D11VideoDevice1_CreateVideoProcessorEnumerator(This,pDesc,ppEnum) (This)->lpVtbl->CreateVideoProcessorEnumerator(This,pDesc,ppEnum)
#define ID3D11VideoDevice1_GetVideoDecoderProfileCount(This) (This)->lpVtbl->GetVideoDecoderProfileCount(This)
#define ID3D11VideoDevice1_GetVideoDecoderProfile(This,Index,pDecoderProfile) (This)->lpVtbl->GetVideoDecoderProfile(This,Index,pDecoderProfile)
#define ID3D11VideoDevice1_CheckVideoDecoderFormat(This,pDecoderProfile,Format,pSupported) (This)->lpVtbl->CheckVideoDecoderFormat(This,pDecoderProfile,Format,pSupported)
#define ID3D11VideoDevice1_GetVideoDecoderConfigCount(This,pDesc,pCount) (This)->lpVtbl->GetVideoDecoderConfigCount(This,pDesc,pCount)
#define ID3D11VideoDevice1_GetVideoDecoderConfig(This,pDesc,Index,pConfig) (This)->lpVtbl->GetVideoDecoderConfig(This,pDesc,Index,pConfig)
#define ID3D11VideoDevice1_GetContentProtectionCaps(This,pCryptoType,pDecoderProfile,pCaps) (This)->lpVtbl->GetContentProtectionCaps(This,pCryptoType,pDecoderProfile,pCaps)
#define ID3D11VideoDevice1_CheckCryptoKeyExchange(This,pCryptoType,pDecoderProfile,Index,pKeyExchangeType) (This)->lpVtbl->CheckCryptoKeyExchange(This,pCryptoType,pDecoderProfile,Index,pKeyExchangeType)
#define ID3D11VideoDevice1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoDevice1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoDevice1 methods ***/
#define ID3D11VideoDevice1_GetCryptoSessionPrivateDataSize(This,crypto_type,decoder_profile,key_exchange_type,input_size,output_size) (This)->lpVtbl->GetCryptoSessionPrivateDataSize(This,crypto_type,decoder_profile,key_exchange_type,input_size,output_size)
#define ID3D11VideoDevice1_GetVideoDecoderCaps(This,decoder_profile,sample_width,sample_height,framerate,bitrate,crypto_type,decoder_caps) (This)->lpVtbl->GetVideoDecoderCaps(This,decoder_profile,sample_width,sample_height,framerate,bitrate,crypto_type,decoder_caps)
#define ID3D11VideoDevice1_CheckVideoDecoderDownsampling(This,input_desc,input_colour_space,input_config,framerate,output_desc,supported,real_time_hint) (This)->lpVtbl->CheckVideoDecoderDownsampling(This,input_desc,input_colour_space,input_config,framerate,output_desc,supported,real_time_hint)
#define ID3D11VideoDevice1_RecommendVideoDecoderDownsampleParameters(This,input_desc,input_colour_space,input_config,framerate,recommended_output_desc) (This)->lpVtbl->RecommendVideoDecoderDownsampleParameters(This,input_desc,input_colour_space,input_config,framerate,recommended_output_desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11VideoDevice1_QueryInterface(ID3D11VideoDevice1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11VideoDevice1_AddRef(ID3D11VideoDevice1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11VideoDevice1_Release(ID3D11VideoDevice1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11VideoDevice methods ***/
static inline HRESULT ID3D11VideoDevice1_CreateVideoDecoder(ID3D11VideoDevice1* This,const D3D11_VIDEO_DECODER_DESC *pVideoDesc,const D3D11_VIDEO_DECODER_CONFIG *pConfig,ID3D11VideoDecoder **ppDecoder) {
    return This->lpVtbl->CreateVideoDecoder(This,pVideoDesc,pConfig,ppDecoder);
}
static inline HRESULT ID3D11VideoDevice1_CreateVideoProcessor(ID3D11VideoDevice1* This,ID3D11VideoProcessorEnumerator *pEnum,UINT RateConversionIndex,ID3D11VideoProcessor **ppVideoProcessor) {
    return This->lpVtbl->CreateVideoProcessor(This,pEnum,RateConversionIndex,ppVideoProcessor);
}
static inline HRESULT ID3D11VideoDevice1_CreateAuthenticatedChannel(ID3D11VideoDevice1* This,D3D11_AUTHENTICATED_CHANNEL_TYPE ChannelType,ID3D11AuthenticatedChannel **ppAuthenticatedChannel) {
    return This->lpVtbl->CreateAuthenticatedChannel(This,ChannelType,ppAuthenticatedChannel);
}
static inline HRESULT ID3D11VideoDevice1_CreateCryptoSession(ID3D11VideoDevice1* This,const GUID *pCryptoType,const GUID *pDecoderProfile,const GUID *pKeyExchangeType,ID3D11CryptoSession **ppCryptoSession) {
    return This->lpVtbl->CreateCryptoSession(This,pCryptoType,pDecoderProfile,pKeyExchangeType,ppCryptoSession);
}
static inline HRESULT ID3D11VideoDevice1_CreateVideoDecoderOutputView(ID3D11VideoDevice1* This,ID3D11Resource *pResource,const D3D11_VIDEO_DECODER_OUTPUT_VIEW_DESC *pDesc,ID3D11VideoDecoderOutputView **ppVDOVView) {
    return This->lpVtbl->CreateVideoDecoderOutputView(This,pResource,pDesc,ppVDOVView);
}
static inline HRESULT ID3D11VideoDevice1_CreateVideoProcessorInputView(ID3D11VideoDevice1* This,ID3D11Resource *pResource,ID3D11VideoProcessorEnumerator *pEnum,const D3D11_VIDEO_PROCESSOR_INPUT_VIEW_DESC *pDesc,ID3D11VideoProcessorInputView **ppVPIView) {
    return This->lpVtbl->CreateVideoProcessorInputView(This,pResource,pEnum,pDesc,ppVPIView);
}
static inline HRESULT ID3D11VideoDevice1_CreateVideoProcessorOutputView(ID3D11VideoDevice1* This,ID3D11Resource *pResource,ID3D11VideoProcessorEnumerator *pEnum,const D3D11_VIDEO_PROCESSOR_OUTPUT_VIEW_DESC *pDesc,ID3D11VideoProcessorOutputView **ppVPOView) {
    return This->lpVtbl->CreateVideoProcessorOutputView(This,pResource,pEnum,pDesc,ppVPOView);
}
static inline HRESULT ID3D11VideoDevice1_CreateVideoProcessorEnumerator(ID3D11VideoDevice1* This,const D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pDesc,ID3D11VideoProcessorEnumerator **ppEnum) {
    return This->lpVtbl->CreateVideoProcessorEnumerator(This,pDesc,ppEnum);
}
static inline UINT ID3D11VideoDevice1_GetVideoDecoderProfileCount(ID3D11VideoDevice1* This) {
    return This->lpVtbl->GetVideoDecoderProfileCount(This);
}
static inline HRESULT ID3D11VideoDevice1_GetVideoDecoderProfile(ID3D11VideoDevice1* This,UINT Index,GUID *pDecoderProfile) {
    return This->lpVtbl->GetVideoDecoderProfile(This,Index,pDecoderProfile);
}
static inline HRESULT ID3D11VideoDevice1_CheckVideoDecoderFormat(ID3D11VideoDevice1* This,const GUID *pDecoderProfile,DXGI_FORMAT Format,WINBOOL *pSupported) {
    return This->lpVtbl->CheckVideoDecoderFormat(This,pDecoderProfile,Format,pSupported);
}
static inline HRESULT ID3D11VideoDevice1_GetVideoDecoderConfigCount(ID3D11VideoDevice1* This,const D3D11_VIDEO_DECODER_DESC *pDesc,UINT *pCount) {
    return This->lpVtbl->GetVideoDecoderConfigCount(This,pDesc,pCount);
}
static inline HRESULT ID3D11VideoDevice1_GetVideoDecoderConfig(ID3D11VideoDevice1* This,const D3D11_VIDEO_DECODER_DESC *pDesc,UINT Index,D3D11_VIDEO_DECODER_CONFIG *pConfig) {
    return This->lpVtbl->GetVideoDecoderConfig(This,pDesc,Index,pConfig);
}
static inline HRESULT ID3D11VideoDevice1_GetContentProtectionCaps(ID3D11VideoDevice1* This,const GUID *pCryptoType,const GUID *pDecoderProfile,D3D11_VIDEO_CONTENT_PROTECTION_CAPS *pCaps) {
    return This->lpVtbl->GetContentProtectionCaps(This,pCryptoType,pDecoderProfile,pCaps);
}
static inline HRESULT ID3D11VideoDevice1_CheckCryptoKeyExchange(ID3D11VideoDevice1* This,const GUID *pCryptoType,const GUID *pDecoderProfile,UINT Index,GUID *pKeyExchangeType) {
    return This->lpVtbl->CheckCryptoKeyExchange(This,pCryptoType,pDecoderProfile,Index,pKeyExchangeType);
}
static inline HRESULT ID3D11VideoDevice1_SetPrivateData(ID3D11VideoDevice1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11VideoDevice1_SetPrivateDataInterface(ID3D11VideoDevice1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoDevice1 methods ***/
static inline HRESULT ID3D11VideoDevice1_GetCryptoSessionPrivateDataSize(ID3D11VideoDevice1* This,const GUID *crypto_type,const GUID *decoder_profile,const GUID *key_exchange_type,UINT *input_size,UINT *output_size) {
    return This->lpVtbl->GetCryptoSessionPrivateDataSize(This,crypto_type,decoder_profile,key_exchange_type,input_size,output_size);
}
static inline HRESULT ID3D11VideoDevice1_GetVideoDecoderCaps(ID3D11VideoDevice1* This,const GUID *decoder_profile,UINT sample_width,UINT sample_height,const DXGI_RATIONAL *framerate,UINT bitrate,const GUID *crypto_type,UINT *decoder_caps) {
    return This->lpVtbl->GetVideoDecoderCaps(This,decoder_profile,sample_width,sample_height,framerate,bitrate,crypto_type,decoder_caps);
}
static inline HRESULT ID3D11VideoDevice1_CheckVideoDecoderDownsampling(ID3D11VideoDevice1* This,const D3D11_VIDEO_DECODER_DESC *input_desc,DXGI_COLOR_SPACE_TYPE input_colour_space,const D3D11_VIDEO_DECODER_CONFIG *input_config,const DXGI_RATIONAL *framerate,const D3D11_VIDEO_SAMPLE_DESC *output_desc,WINBOOL *supported,WINBOOL *real_time_hint) {
    return This->lpVtbl->CheckVideoDecoderDownsampling(This,input_desc,input_colour_space,input_config,framerate,output_desc,supported,real_time_hint);
}
static inline HRESULT ID3D11VideoDevice1_RecommendVideoDecoderDownsampleParameters(ID3D11VideoDevice1* This,const D3D11_VIDEO_DECODER_DESC *input_desc,DXGI_COLOR_SPACE_TYPE input_colour_space,const D3D11_VIDEO_DECODER_CONFIG *input_config,const DXGI_RATIONAL *framerate,D3D11_VIDEO_SAMPLE_DESC *recommended_output_desc) {
    return This->lpVtbl->RecommendVideoDecoderDownsampleParameters(This,input_desc,input_colour_space,input_config,framerate,recommended_output_desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoDevice1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoProcessorEnumerator1 interface
 */
#ifndef __ID3D11VideoProcessorEnumerator1_INTERFACE_DEFINED__
#define __ID3D11VideoProcessorEnumerator1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoProcessorEnumerator1, 0x465217f2, 0x5568, 0x43cf, 0xb5,0xb9, 0xf6,0x1d,0x54,0x53,0x1c,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("465217f2-5568-43cf-b5b9-f61d54531ca1")
ID3D11VideoProcessorEnumerator1 : public ID3D11VideoProcessorEnumerator
{
    virtual HRESULT STDMETHODCALLTYPE CheckVideoProcessorFormatConversion(
        DXGI_FORMAT input_format,
        DXGI_COLOR_SPACE_TYPE input_colour_space,
        DXGI_FORMAT output_format,
        DXGI_COLOR_SPACE_TYPE output_colour_space,
        WINBOOL *supported) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoProcessorEnumerator1, 0x465217f2, 0x5568, 0x43cf, 0xb5,0xb9, 0xf6,0x1d,0x54,0x53,0x1c,0xa1)
#endif
#else
typedef struct ID3D11VideoProcessorEnumerator1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoProcessorEnumerator1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoProcessorEnumerator1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoProcessorEnumerator1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoProcessorEnumerator1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoProcessorEnumerator1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoProcessorEnumerator1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoProcessorEnumerator1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoProcessorEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorContentDesc)(
        ID3D11VideoProcessorEnumerator1 *This,
        D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pContentDesc);

    HRESULT (STDMETHODCALLTYPE *CheckVideoProcessorFormat)(
        ID3D11VideoProcessorEnumerator1 *This,
        DXGI_FORMAT Format,
        UINT *pFlags);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorCaps)(
        ID3D11VideoProcessorEnumerator1 *This,
        D3D11_VIDEO_PROCESSOR_CAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorRateConversionCaps)(
        ID3D11VideoProcessorEnumerator1 *This,
        UINT TypeIndex,
        D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorCustomRate)(
        ID3D11VideoProcessorEnumerator1 *This,
        UINT TypeIndex,
        UINT CustomRateIndex,
        D3D11_VIDEO_PROCESSOR_CUSTOM_RATE *pRate);

    HRESULT (STDMETHODCALLTYPE *GetVideoProcessorFilterRange)(
        ID3D11VideoProcessorEnumerator1 *This,
        D3D11_VIDEO_PROCESSOR_FILTER Filter,
        D3D11_VIDEO_PROCESSOR_FILTER_RANGE *pRange);

    /*** ID3D11VideoProcessorEnumerator1 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckVideoProcessorFormatConversion)(
        ID3D11VideoProcessorEnumerator1 *This,
        DXGI_FORMAT input_format,
        DXGI_COLOR_SPACE_TYPE input_colour_space,
        DXGI_FORMAT output_format,
        DXGI_COLOR_SPACE_TYPE output_colour_space,
        WINBOOL *supported);

    END_INTERFACE
} ID3D11VideoProcessorEnumerator1Vtbl;

interface ID3D11VideoProcessorEnumerator1 {
    CONST_VTBL ID3D11VideoProcessorEnumerator1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoProcessorEnumerator1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoProcessorEnumerator1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoProcessorEnumerator1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoProcessorEnumerator1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoProcessorEnumerator1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoProcessorEnumerator1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoProcessorEnumerator1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoProcessorEnumerator methods ***/
#define ID3D11VideoProcessorEnumerator1_GetVideoProcessorContentDesc(This,pContentDesc) (This)->lpVtbl->GetVideoProcessorContentDesc(This,pContentDesc)
#define ID3D11VideoProcessorEnumerator1_CheckVideoProcessorFormat(This,Format,pFlags) (This)->lpVtbl->CheckVideoProcessorFormat(This,Format,pFlags)
#define ID3D11VideoProcessorEnumerator1_GetVideoProcessorCaps(This,pCaps) (This)->lpVtbl->GetVideoProcessorCaps(This,pCaps)
#define ID3D11VideoProcessorEnumerator1_GetVideoProcessorRateConversionCaps(This,TypeIndex,pCaps) (This)->lpVtbl->GetVideoProcessorRateConversionCaps(This,TypeIndex,pCaps)
#define ID3D11VideoProcessorEnumerator1_GetVideoProcessorCustomRate(This,TypeIndex,CustomRateIndex,pRate) (This)->lpVtbl->GetVideoProcessorCustomRate(This,TypeIndex,CustomRateIndex,pRate)
#define ID3D11VideoProcessorEnumerator1_GetVideoProcessorFilterRange(This,Filter,pRange) (This)->lpVtbl->GetVideoProcessorFilterRange(This,Filter,pRange)
/*** ID3D11VideoProcessorEnumerator1 methods ***/
#define ID3D11VideoProcessorEnumerator1_CheckVideoProcessorFormatConversion(This,input_format,input_colour_space,output_format,output_colour_space,supported) (This)->lpVtbl->CheckVideoProcessorFormatConversion(This,input_format,input_colour_space,output_format,output_colour_space,supported)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11VideoProcessorEnumerator1_QueryInterface(ID3D11VideoProcessorEnumerator1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11VideoProcessorEnumerator1_AddRef(ID3D11VideoProcessorEnumerator1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11VideoProcessorEnumerator1_Release(ID3D11VideoProcessorEnumerator1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11VideoProcessorEnumerator1_GetDevice(ID3D11VideoProcessorEnumerator1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_GetPrivateData(ID3D11VideoProcessorEnumerator1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_SetPrivateData(ID3D11VideoProcessorEnumerator1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_SetPrivateDataInterface(ID3D11VideoProcessorEnumerator1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoProcessorEnumerator methods ***/
static inline HRESULT ID3D11VideoProcessorEnumerator1_GetVideoProcessorContentDesc(ID3D11VideoProcessorEnumerator1* This,D3D11_VIDEO_PROCESSOR_CONTENT_DESC *pContentDesc) {
    return This->lpVtbl->GetVideoProcessorContentDesc(This,pContentDesc);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_CheckVideoProcessorFormat(ID3D11VideoProcessorEnumerator1* This,DXGI_FORMAT Format,UINT *pFlags) {
    return This->lpVtbl->CheckVideoProcessorFormat(This,Format,pFlags);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_GetVideoProcessorCaps(ID3D11VideoProcessorEnumerator1* This,D3D11_VIDEO_PROCESSOR_CAPS *pCaps) {
    return This->lpVtbl->GetVideoProcessorCaps(This,pCaps);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_GetVideoProcessorRateConversionCaps(ID3D11VideoProcessorEnumerator1* This,UINT TypeIndex,D3D11_VIDEO_PROCESSOR_RATE_CONVERSION_CAPS *pCaps) {
    return This->lpVtbl->GetVideoProcessorRateConversionCaps(This,TypeIndex,pCaps);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_GetVideoProcessorCustomRate(ID3D11VideoProcessorEnumerator1* This,UINT TypeIndex,UINT CustomRateIndex,D3D11_VIDEO_PROCESSOR_CUSTOM_RATE *pRate) {
    return This->lpVtbl->GetVideoProcessorCustomRate(This,TypeIndex,CustomRateIndex,pRate);
}
static inline HRESULT ID3D11VideoProcessorEnumerator1_GetVideoProcessorFilterRange(ID3D11VideoProcessorEnumerator1* This,D3D11_VIDEO_PROCESSOR_FILTER Filter,D3D11_VIDEO_PROCESSOR_FILTER_RANGE *pRange) {
    return This->lpVtbl->GetVideoProcessorFilterRange(This,Filter,pRange);
}
/*** ID3D11VideoProcessorEnumerator1 methods ***/
static inline HRESULT ID3D11VideoProcessorEnumerator1_CheckVideoProcessorFormatConversion(ID3D11VideoProcessorEnumerator1* This,DXGI_FORMAT input_format,DXGI_COLOR_SPACE_TYPE input_colour_space,DXGI_FORMAT output_format,DXGI_COLOR_SPACE_TYPE output_colour_space,WINBOOL *supported) {
    return This->lpVtbl->CheckVideoProcessorFormatConversion(This,input_format,input_colour_space,output_format,output_colour_space,supported);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoProcessorEnumerator1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3DUserDefinedAnnotation interface
 */
#ifndef __ID3DUserDefinedAnnotation_INTERFACE_DEFINED__
#define __ID3DUserDefinedAnnotation_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3DUserDefinedAnnotation, 0xb2daad8b, 0x03d4, 0x4dbf, 0x95,0xeb, 0x32,0xab,0x4b,0x63,0xd0,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b2daad8b-03d4-4dbf-95eb-32ab4b63d0ab")
ID3DUserDefinedAnnotation : public IUnknown
{
    virtual INT STDMETHODCALLTYPE BeginEvent(
        LPCWSTR Name) = 0;

    virtual INT STDMETHODCALLTYPE EndEvent(
        ) = 0;

    virtual void STDMETHODCALLTYPE SetMarker(
        LPCWSTR Name) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetStatus(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3DUserDefinedAnnotation, 0xb2daad8b, 0x03d4, 0x4dbf, 0x95,0xeb, 0x32,0xab,0x4b,0x63,0xd0,0xab)
#endif
#else
typedef struct ID3DUserDefinedAnnotationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3DUserDefinedAnnotation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3DUserDefinedAnnotation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3DUserDefinedAnnotation *This);

    /*** ID3DUserDefinedAnnotation methods ***/
    INT (STDMETHODCALLTYPE *BeginEvent)(
        ID3DUserDefinedAnnotation *This,
        LPCWSTR Name);

    INT (STDMETHODCALLTYPE *EndEvent)(
        ID3DUserDefinedAnnotation *This);

    void (STDMETHODCALLTYPE *SetMarker)(
        ID3DUserDefinedAnnotation *This,
        LPCWSTR Name);

    WINBOOL (STDMETHODCALLTYPE *GetStatus)(
        ID3DUserDefinedAnnotation *This);

    END_INTERFACE
} ID3DUserDefinedAnnotationVtbl;

interface ID3DUserDefinedAnnotation {
    CONST_VTBL ID3DUserDefinedAnnotationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3DUserDefinedAnnotation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3DUserDefinedAnnotation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3DUserDefinedAnnotation_Release(This) (This)->lpVtbl->Release(This)
/*** ID3DUserDefinedAnnotation methods ***/
#define ID3DUserDefinedAnnotation_BeginEvent(This,Name) (This)->lpVtbl->BeginEvent(This,Name)
#define ID3DUserDefinedAnnotation_EndEvent(This) (This)->lpVtbl->EndEvent(This)
#define ID3DUserDefinedAnnotation_SetMarker(This,Name) (This)->lpVtbl->SetMarker(This,Name)
#define ID3DUserDefinedAnnotation_GetStatus(This) (This)->lpVtbl->GetStatus(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3DUserDefinedAnnotation_QueryInterface(ID3DUserDefinedAnnotation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3DUserDefinedAnnotation_AddRef(ID3DUserDefinedAnnotation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3DUserDefinedAnnotation_Release(ID3DUserDefinedAnnotation* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3DUserDefinedAnnotation methods ***/
static inline INT ID3DUserDefinedAnnotation_BeginEvent(ID3DUserDefinedAnnotation* This,LPCWSTR Name) {
    return This->lpVtbl->BeginEvent(This,Name);
}
static inline INT ID3DUserDefinedAnnotation_EndEvent(ID3DUserDefinedAnnotation* This) {
    return This->lpVtbl->EndEvent(This);
}
static inline void ID3DUserDefinedAnnotation_SetMarker(ID3DUserDefinedAnnotation* This,LPCWSTR Name) {
    This->lpVtbl->SetMarker(This,Name);
}
static inline WINBOOL ID3DUserDefinedAnnotation_GetStatus(ID3DUserDefinedAnnotation* This) {
    return This->lpVtbl->GetStatus(This);
}
#endif
#endif

#endif


#endif  /* __ID3DUserDefinedAnnotation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11RasterizerState1 interface
 */
#ifndef __ID3D11RasterizerState1_INTERFACE_DEFINED__
#define __ID3D11RasterizerState1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11RasterizerState1, 0x1217d7a6, 0x5039, 0x418c, 0xb0,0x42, 0x9c,0xbe,0x25,0x6a,0xfd,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1217d7a6-5039-418c-b042-9cbe256afd6e")
ID3D11RasterizerState1 : public ID3D11RasterizerState
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_RASTERIZER_DESC1 *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11RasterizerState1, 0x1217d7a6, 0x5039, 0x418c, 0xb0,0x42, 0x9c,0xbe,0x25,0x6a,0xfd,0x6e)
#endif
#else
typedef struct ID3D11RasterizerState1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11RasterizerState1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11RasterizerState1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11RasterizerState1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11RasterizerState1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11RasterizerState1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11RasterizerState1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11RasterizerState1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11RasterizerState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11RasterizerState1 *This,
        D3D11_RASTERIZER_DESC *pDesc);

    /*** ID3D11RasterizerState1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11RasterizerState1 *This,
        D3D11_RASTERIZER_DESC1 *pDesc);

    END_INTERFACE
} ID3D11RasterizerState1Vtbl;

interface ID3D11RasterizerState1 {
    CONST_VTBL ID3D11RasterizerState1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11RasterizerState1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11RasterizerState1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11RasterizerState1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11RasterizerState1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11RasterizerState1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11RasterizerState1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11RasterizerState1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11RasterizerState methods ***/
#define ID3D11RasterizerState1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11RasterizerState1 methods ***/
#define ID3D11RasterizerState1_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11RasterizerState1_QueryInterface(ID3D11RasterizerState1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11RasterizerState1_AddRef(ID3D11RasterizerState1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11RasterizerState1_Release(ID3D11RasterizerState1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11RasterizerState1_GetDevice(ID3D11RasterizerState1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11RasterizerState1_GetPrivateData(ID3D11RasterizerState1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11RasterizerState1_SetPrivateData(ID3D11RasterizerState1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11RasterizerState1_SetPrivateDataInterface(ID3D11RasterizerState1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11RasterizerState methods ***/
static inline void ID3D11RasterizerState1_GetDesc(ID3D11RasterizerState1* This,D3D11_RASTERIZER_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11RasterizerState1 methods ***/
static inline void ID3D11RasterizerState1_GetDesc1(ID3D11RasterizerState1* This,D3D11_RASTERIZER_DESC1 *pDesc) {
    This->lpVtbl->GetDesc1(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __ID3D11RasterizerState1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Device1 interface
 */
#ifndef __ID3D11Device1_INTERFACE_DEFINED__
#define __ID3D11Device1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Device1, 0xa04bfb29, 0x08ef, 0x43d6, 0xa4,0x9c, 0xa9,0xbd,0xbd,0xcb,0xe6,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a04bfb29-08ef-43d6-a49c-a9bdbdcbe686")
ID3D11Device1 : public ID3D11Device
{
    virtual void STDMETHODCALLTYPE GetImmediateContext1(
        ID3D11DeviceContext1 **ppImmediateContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDeferredContext1(
        UINT ContextFlags,
        ID3D11DeviceContext1 **ppDeferredContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBlendState1(
        const D3D11_BLEND_DESC1 *pBlendStateDesc,
        ID3D11BlendState1 **ppBlendState) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRasterizerState1(
        const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,
        ID3D11RasterizerState1 **ppRasterizerState) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDeviceContextState(
        UINT Flags,
        const D3D_FEATURE_LEVEL *pFeatureLevels,
        UINT FeatureLevels,
        UINT SDKVersion,
        REFIID EmulatedInterface,
        D3D_FEATURE_LEVEL *pChosenFeatureLevel,
        ID3DDeviceContextState **ppContextState) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenSharedResource1(
        HANDLE hResource,
        REFIID returnedInterface,
        void **ppResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenSharedResourceByName(
        LPCWSTR lpName,
        DWORD dwDesiredAccess,
        REFIID returnedInterface,
        void **ppResource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Device1, 0xa04bfb29, 0x08ef, 0x43d6, 0xa4,0x9c, 0xa9,0xbd,0xbd,0xcb,0xe6,0x86)
#endif
#else
typedef struct ID3D11Device1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Device1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Device1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Device1 *This);

    /*** ID3D11Device methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBuffer)(
        ID3D11Device1 *This,
        const D3D11_BUFFER_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Buffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *CreateTexture1D)(
        ID3D11Device1 *This,
        const D3D11_TEXTURE1D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture1D **ppTexture1D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture2D)(
        ID3D11Device1 *This,
        const D3D11_TEXTURE2D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture2D **ppTexture2D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D)(
        ID3D11Device1 *This,
        const D3D11_TEXTURE3D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture3D **ppTexture3D);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView)(
        ID3D11Device1 *This,
        ID3D11Resource *pResource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D11ShaderResourceView **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView)(
        ID3D11Device1 *This,
        ID3D11Resource *pResource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        ID3D11UnorderedAccessView **ppUAView);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView)(
        ID3D11Device1 *This,
        ID3D11Resource *pResource,
        const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D11RenderTargetView **ppRTView);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilView)(
        ID3D11Device1 *This,
        ID3D11Resource *pResource,
        const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D11DepthStencilView **ppDepthStencilView);

    HRESULT (STDMETHODCALLTYPE *CreateInputLayout)(
        ID3D11Device1 *This,
        const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D11InputLayout **ppInputLayout);

    HRESULT (STDMETHODCALLTYPE *CreateVertexShader)(
        ID3D11Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11VertexShader **ppVertexShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShader)(
        ID3D11Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShaderWithStreamOutput)(
        ID3D11Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        const UINT *pBufferStrides,
        UINT NumStrides,
        UINT RasterizedStream,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreatePixelShader)(
        ID3D11Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11PixelShader **ppPixelShader);

    HRESULT (STDMETHODCALLTYPE *CreateHullShader)(
        ID3D11Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11HullShader **ppHullShader);

    HRESULT (STDMETHODCALLTYPE *CreateDomainShader)(
        ID3D11Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11DomainShader **ppDomainShader);

    HRESULT (STDMETHODCALLTYPE *CreateComputeShader)(
        ID3D11Device1 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11ComputeShader **ppComputeShader);

    HRESULT (STDMETHODCALLTYPE *CreateClassLinkage)(
        ID3D11Device1 *This,
        ID3D11ClassLinkage **ppLinkage);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState)(
        ID3D11Device1 *This,
        const D3D11_BLEND_DESC *pBlendStateDesc,
        ID3D11BlendState **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilState)(
        ID3D11Device1 *This,
        const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D11DepthStencilState **ppDepthStencilState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState)(
        ID3D11Device1 *This,
        const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        ID3D11RasterizerState **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateSamplerState)(
        ID3D11Device1 *This,
        const D3D11_SAMPLER_DESC *pSamplerDesc,
        ID3D11SamplerState **ppSamplerState);

    HRESULT (STDMETHODCALLTYPE *CreateQuery)(
        ID3D11Device1 *This,
        const D3D11_QUERY_DESC *pQueryDesc,
        ID3D11Query **ppQuery);

    HRESULT (STDMETHODCALLTYPE *CreatePredicate)(
        ID3D11Device1 *This,
        const D3D11_QUERY_DESC *pPredicateDesc,
        ID3D11Predicate **ppPredicate);

    HRESULT (STDMETHODCALLTYPE *CreateCounter)(
        ID3D11Device1 *This,
        const D3D11_COUNTER_DESC *pCounterDesc,
        ID3D11Counter **ppCounter);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext)(
        ID3D11Device1 *This,
        UINT ContextFlags,
        ID3D11DeviceContext **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource)(
        ID3D11Device1 *This,
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *CheckFormatSupport)(
        ID3D11Device1 *This,
        DXGI_FORMAT Format,
        UINT *pFormatSupport);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels)(
        ID3D11Device1 *This,
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels);

    void (STDMETHODCALLTYPE *CheckCounterInfo)(
        ID3D11Device1 *This,
        D3D11_COUNTER_INFO *pCounterInfo);

    HRESULT (STDMETHODCALLTYPE *CheckCounter)(
        ID3D11Device1 *This,
        const D3D11_COUNTER_DESC *pDesc,
        D3D11_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        LPSTR szName,
        UINT *pNameLength,
        LPSTR szUnits,
        UINT *pUnitsLength,
        LPSTR szDescription,
        UINT *pDescriptionLength);

    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D11Device1 *This,
        D3D11_FEATURE Feature,
        void *pFeatureSupportData,
        UINT FeatureSupportDataSize);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Device1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Device1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Device1 *This,
        REFGUID guid,
        const IUnknown *pData);

    D3D_FEATURE_LEVEL (STDMETHODCALLTYPE *GetFeatureLevel)(
        ID3D11Device1 *This);

    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        ID3D11Device1 *This);

    HRESULT (STDMETHODCALLTYPE *GetDeviceRemovedReason)(
        ID3D11Device1 *This);

    void (STDMETHODCALLTYPE *GetImmediateContext)(
        ID3D11Device1 *This,
        ID3D11DeviceContext **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *SetExceptionMode)(
        ID3D11Device1 *This,
        UINT RaiseFlags);

    UINT (STDMETHODCALLTYPE *GetExceptionMode)(
        ID3D11Device1 *This);

    /*** ID3D11Device1 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext1)(
        ID3D11Device1 *This,
        ID3D11DeviceContext1 **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext1)(
        ID3D11Device1 *This,
        UINT ContextFlags,
        ID3D11DeviceContext1 **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState1)(
        ID3D11Device1 *This,
        const D3D11_BLEND_DESC1 *pBlendStateDesc,
        ID3D11BlendState1 **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState1)(
        ID3D11Device1 *This,
        const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,
        ID3D11RasterizerState1 **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateDeviceContextState)(
        ID3D11Device1 *This,
        UINT Flags,
        const D3D_FEATURE_LEVEL *pFeatureLevels,
        UINT FeatureLevels,
        UINT SDKVersion,
        REFIID EmulatedInterface,
        D3D_FEATURE_LEVEL *pChosenFeatureLevel,
        ID3DDeviceContextState **ppContextState);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource1)(
        ID3D11Device1 *This,
        HANDLE hResource,
        REFIID returnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResourceByName)(
        ID3D11Device1 *This,
        LPCWSTR lpName,
        DWORD dwDesiredAccess,
        REFIID returnedInterface,
        void **ppResource);

    END_INTERFACE
} ID3D11Device1Vtbl;

interface ID3D11Device1 {
    CONST_VTBL ID3D11Device1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Device1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Device1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Device1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Device methods ***/
#define ID3D11Device1_CreateBuffer(This,pDesc,pInitialData,ppBuffer) (This)->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer)
#define ID3D11Device1_CreateTexture1D(This,pDesc,pInitialData,ppTexture1D) (This)->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D)
#define ID3D11Device1_CreateTexture2D(This,pDesc,pInitialData,ppTexture2D) (This)->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D)
#define ID3D11Device1_CreateTexture3D(This,pDesc,pInitialData,ppTexture3D) (This)->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D)
#define ID3D11Device1_CreateShaderResourceView(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView)
#define ID3D11Device1_CreateUnorderedAccessView(This,pResource,pDesc,ppUAView) (This)->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView)
#define ID3D11Device1_CreateRenderTargetView(This,pResource,pDesc,ppRTView) (This)->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView)
#define ID3D11Device1_CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView) (This)->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView)
#define ID3D11Device1_CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout) (This)->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout)
#define ID3D11Device1_CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader) (This)->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader)
#define ID3D11Device1_CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader)
#define ID3D11Device1_CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader)
#define ID3D11Device1_CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader) (This)->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader)
#define ID3D11Device1_CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader) (This)->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader)
#define ID3D11Device1_CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader) (This)->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader)
#define ID3D11Device1_CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader) (This)->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader)
#define ID3D11Device1_CreateClassLinkage(This,ppLinkage) (This)->lpVtbl->CreateClassLinkage(This,ppLinkage)
#define ID3D11Device1_CreateBlendState(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device1_CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState) (This)->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState)
#define ID3D11Device1_CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device1_CreateSamplerState(This,pSamplerDesc,ppSamplerState) (This)->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState)
#define ID3D11Device1_CreateQuery(This,pQueryDesc,ppQuery) (This)->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery)
#define ID3D11Device1_CreatePredicate(This,pPredicateDesc,ppPredicate) (This)->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate)
#define ID3D11Device1_CreateCounter(This,pCounterDesc,ppCounter) (This)->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter)
#define ID3D11Device1_CreateDeferredContext(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext)
#define ID3D11Device1_OpenSharedResource(This,hResource,ReturnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource)
#define ID3D11Device1_CheckFormatSupport(This,Format,pFormatSupport) (This)->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport)
#define ID3D11Device1_CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels) (This)->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels)
#define ID3D11Device1_CheckCounterInfo(This,pCounterInfo) (This)->lpVtbl->CheckCounterInfo(This,pCounterInfo)
#define ID3D11Device1_CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength) (This)->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength)
#define ID3D11Device1_CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize) (This)->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize)
#define ID3D11Device1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Device1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Device1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#define ID3D11Device1_GetFeatureLevel(This) (This)->lpVtbl->GetFeatureLevel(This)
#define ID3D11Device1_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#define ID3D11Device1_GetDeviceRemovedReason(This) (This)->lpVtbl->GetDeviceRemovedReason(This)
#define ID3D11Device1_GetImmediateContext(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext(This,ppImmediateContext)
#define ID3D11Device1_SetExceptionMode(This,RaiseFlags) (This)->lpVtbl->SetExceptionMode(This,RaiseFlags)
#define ID3D11Device1_GetExceptionMode(This) (This)->lpVtbl->GetExceptionMode(This)
/*** ID3D11Device1 methods ***/
#define ID3D11Device1_GetImmediateContext1(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext1(This,ppImmediateContext)
#define ID3D11Device1_CreateDeferredContext1(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext)
#define ID3D11Device1_CreateBlendState1(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device1_CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device1_CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState) (This)->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState)
#define ID3D11Device1_OpenSharedResource1(This,hResource,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource)
#define ID3D11Device1_OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Device1_QueryInterface(ID3D11Device1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Device1_AddRef(ID3D11Device1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Device1_Release(ID3D11Device1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Device methods ***/
static inline HRESULT ID3D11Device1_CreateBuffer(ID3D11Device1* This,const D3D11_BUFFER_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Buffer **ppBuffer) {
    return This->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer);
}
static inline HRESULT ID3D11Device1_CreateTexture1D(ID3D11Device1* This,const D3D11_TEXTURE1D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture1D **ppTexture1D) {
    return This->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D);
}
static inline HRESULT ID3D11Device1_CreateTexture2D(ID3D11Device1* This,const D3D11_TEXTURE2D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture2D **ppTexture2D) {
    return This->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D);
}
static inline HRESULT ID3D11Device1_CreateTexture3D(ID3D11Device1* This,const D3D11_TEXTURE3D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture3D **ppTexture3D) {
    return This->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D);
}
static inline HRESULT ID3D11Device1_CreateShaderResourceView(ID3D11Device1* This,ID3D11Resource *pResource,const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,ID3D11ShaderResourceView **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView);
}
static inline HRESULT ID3D11Device1_CreateUnorderedAccessView(ID3D11Device1* This,ID3D11Resource *pResource,const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,ID3D11UnorderedAccessView **ppUAView) {
    return This->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView);
}
static inline HRESULT ID3D11Device1_CreateRenderTargetView(ID3D11Device1* This,ID3D11Resource *pResource,const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,ID3D11RenderTargetView **ppRTView) {
    return This->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView);
}
static inline HRESULT ID3D11Device1_CreateDepthStencilView(ID3D11Device1* This,ID3D11Resource *pResource,const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,ID3D11DepthStencilView **ppDepthStencilView) {
    return This->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView);
}
static inline HRESULT ID3D11Device1_CreateInputLayout(ID3D11Device1* This,const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,UINT NumElements,const void *pShaderBytecodeWithInputSignature,SIZE_T BytecodeLength,ID3D11InputLayout **ppInputLayout) {
    return This->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout);
}
static inline HRESULT ID3D11Device1_CreateVertexShader(ID3D11Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11VertexShader **ppVertexShader) {
    return This->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader);
}
static inline HRESULT ID3D11Device1_CreateGeometryShader(ID3D11Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device1_CreateGeometryShaderWithStreamOutput(ID3D11Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,UINT NumEntries,const UINT *pBufferStrides,UINT NumStrides,UINT RasterizedStream,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device1_CreatePixelShader(ID3D11Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11PixelShader **ppPixelShader) {
    return This->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader);
}
static inline HRESULT ID3D11Device1_CreateHullShader(ID3D11Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11HullShader **ppHullShader) {
    return This->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader);
}
static inline HRESULT ID3D11Device1_CreateDomainShader(ID3D11Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11DomainShader **ppDomainShader) {
    return This->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader);
}
static inline HRESULT ID3D11Device1_CreateComputeShader(ID3D11Device1* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11ComputeShader **ppComputeShader) {
    return This->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader);
}
static inline HRESULT ID3D11Device1_CreateClassLinkage(ID3D11Device1* This,ID3D11ClassLinkage **ppLinkage) {
    return This->lpVtbl->CreateClassLinkage(This,ppLinkage);
}
static inline HRESULT ID3D11Device1_CreateBlendState(ID3D11Device1* This,const D3D11_BLEND_DESC *pBlendStateDesc,ID3D11BlendState **ppBlendState) {
    return This->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device1_CreateDepthStencilState(ID3D11Device1* This,const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,ID3D11DepthStencilState **ppDepthStencilState) {
    return This->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState);
}
static inline HRESULT ID3D11Device1_CreateRasterizerState(ID3D11Device1* This,const D3D11_RASTERIZER_DESC *pRasterizerDesc,ID3D11RasterizerState **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device1_CreateSamplerState(ID3D11Device1* This,const D3D11_SAMPLER_DESC *pSamplerDesc,ID3D11SamplerState **ppSamplerState) {
    return This->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState);
}
static inline HRESULT ID3D11Device1_CreateQuery(ID3D11Device1* This,const D3D11_QUERY_DESC *pQueryDesc,ID3D11Query **ppQuery) {
    return This->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery);
}
static inline HRESULT ID3D11Device1_CreatePredicate(ID3D11Device1* This,const D3D11_QUERY_DESC *pPredicateDesc,ID3D11Predicate **ppPredicate) {
    return This->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate);
}
static inline HRESULT ID3D11Device1_CreateCounter(ID3D11Device1* This,const D3D11_COUNTER_DESC *pCounterDesc,ID3D11Counter **ppCounter) {
    return This->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter);
}
static inline HRESULT ID3D11Device1_CreateDeferredContext(ID3D11Device1* This,UINT ContextFlags,ID3D11DeviceContext **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device1_OpenSharedResource(ID3D11Device1* This,HANDLE hResource,REFIID ReturnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource);
}
static inline HRESULT ID3D11Device1_CheckFormatSupport(ID3D11Device1* This,DXGI_FORMAT Format,UINT *pFormatSupport) {
    return This->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport);
}
static inline HRESULT ID3D11Device1_CheckMultisampleQualityLevels(ID3D11Device1* This,DXGI_FORMAT Format,UINT SampleCount,UINT *pNumQualityLevels) {
    return This->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels);
}
static inline void ID3D11Device1_CheckCounterInfo(ID3D11Device1* This,D3D11_COUNTER_INFO *pCounterInfo) {
    This->lpVtbl->CheckCounterInfo(This,pCounterInfo);
}
static inline HRESULT ID3D11Device1_CheckCounter(ID3D11Device1* This,const D3D11_COUNTER_DESC *pDesc,D3D11_COUNTER_TYPE *pType,UINT *pActiveCounters,LPSTR szName,UINT *pNameLength,LPSTR szUnits,UINT *pUnitsLength,LPSTR szDescription,UINT *pDescriptionLength) {
    return This->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength);
}
static inline HRESULT ID3D11Device1_CheckFeatureSupport(ID3D11Device1* This,D3D11_FEATURE Feature,void *pFeatureSupportData,UINT FeatureSupportDataSize) {
    return This->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize);
}
static inline HRESULT ID3D11Device1_GetPrivateData(ID3D11Device1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Device1_SetPrivateData(ID3D11Device1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Device1_SetPrivateDataInterface(ID3D11Device1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
static inline D3D_FEATURE_LEVEL ID3D11Device1_GetFeatureLevel(ID3D11Device1* This) {
    return This->lpVtbl->GetFeatureLevel(This);
}
static inline UINT ID3D11Device1_GetCreationFlags(ID3D11Device1* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
static inline HRESULT ID3D11Device1_GetDeviceRemovedReason(ID3D11Device1* This) {
    return This->lpVtbl->GetDeviceRemovedReason(This);
}
static inline void ID3D11Device1_GetImmediateContext(ID3D11Device1* This,ID3D11DeviceContext **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device1_SetExceptionMode(ID3D11Device1* This,UINT RaiseFlags) {
    return This->lpVtbl->SetExceptionMode(This,RaiseFlags);
}
static inline UINT ID3D11Device1_GetExceptionMode(ID3D11Device1* This) {
    return This->lpVtbl->GetExceptionMode(This);
}
/*** ID3D11Device1 methods ***/
static inline void ID3D11Device1_GetImmediateContext1(ID3D11Device1* This,ID3D11DeviceContext1 **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext1(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device1_CreateDeferredContext1(ID3D11Device1* This,UINT ContextFlags,ID3D11DeviceContext1 **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device1_CreateBlendState1(ID3D11Device1* This,const D3D11_BLEND_DESC1 *pBlendStateDesc,ID3D11BlendState1 **ppBlendState) {
    return This->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device1_CreateRasterizerState1(ID3D11Device1* This,const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,ID3D11RasterizerState1 **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device1_CreateDeviceContextState(ID3D11Device1* This,UINT Flags,const D3D_FEATURE_LEVEL *pFeatureLevels,UINT FeatureLevels,UINT SDKVersion,REFIID EmulatedInterface,D3D_FEATURE_LEVEL *pChosenFeatureLevel,ID3DDeviceContextState **ppContextState) {
    return This->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState);
}
static inline HRESULT ID3D11Device1_OpenSharedResource1(ID3D11Device1* This,HANDLE hResource,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource);
}
static inline HRESULT ID3D11Device1_OpenSharedResourceByName(ID3D11Device1* This,LPCWSTR lpName,DWORD dwDesiredAccess,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource);
}
#endif
#endif

#endif


#endif  /* __ID3D11Device1_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d11_1_h__ */
