/*** Autogenerated by WIDL 10.12 from include/xamlom.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __xamlom_h__
#define __xamlom_h__

/* Forward declarations */

#ifndef __IVisualTreeServiceCallback_FWD_DEFINED__
#define __IVisualTreeServiceCallback_FWD_DEFINED__
typedef interface IVisualTreeServiceCallback IVisualTreeServiceCallback;
#ifdef __cplusplus
interface IVisualTreeServiceCallback;
#endif /* __cplusplus */
#endif

#ifndef __IVisualTreeServiceCallback2_FWD_DEFINED__
#define __IVisualTreeServiceCallback2_FWD_DEFINED__
typedef interface IVisualTreeServiceCallback2 IVisualTreeServiceCallback2;
#ifdef __cplusplus
interface IVisualTreeServiceCallback2;
#endif /* __cplusplus */
#endif

#ifndef __IVisualTreeService_FWD_DEFINED__
#define __IVisualTreeService_FWD_DEFINED__
typedef interface IVisualTreeService IVisualTreeService;
#ifdef __cplusplus
interface IVisualTreeService;
#endif /* __cplusplus */
#endif

#ifndef __IXamlDiagnostics_FWD_DEFINED__
#define __IXamlDiagnostics_FWD_DEFINED__
typedef interface IXamlDiagnostics IXamlDiagnostics;
#ifdef __cplusplus
interface IXamlDiagnostics;
#endif /* __cplusplus */
#endif

#ifndef __IBitmapData_FWD_DEFINED__
#define __IBitmapData_FWD_DEFINED__
typedef interface IBitmapData IBitmapData;
#ifdef __cplusplus
interface IBitmapData;
#endif /* __cplusplus */
#endif

#ifndef __IVisualTreeService2_FWD_DEFINED__
#define __IVisualTreeService2_FWD_DEFINED__
typedef interface IVisualTreeService2 IVisualTreeService2;
#ifdef __cplusplus
interface IVisualTreeService2;
#endif /* __cplusplus */
#endif

#ifndef __IVisualTreeService3_FWD_DEFINED__
#define __IVisualTreeService3_FWD_DEFINED__
typedef interface IVisualTreeService3 IVisualTreeService3;
#ifdef __cplusplus
interface IVisualTreeService3;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <inspectable.h>
#include <dxgi1_2.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef E_NOTFOUND
#define E_NOTFOUND HRESULT_FROM_WIN32(ERROR_NOT_FOUND)
#endif
#define E_UNKNOWNTYPE _HRESULT_TYPEDEF_(0x802b0028)
HRESULT WINAPI InitializeXamlDiagnostic(PCWSTR, DWORD, PCWSTR, PCWSTR, CLSID);
HRESULT WINAPI InitializeXamlDiagnosticsEx(PCWSTR, DWORD, PCWSTR, PCWSTR, CLSID, PCWSTR);
typedef MIDL_uhyper InstanceHandle;
typedef enum VisualMutationType {
    Add = 0,
    Remove = 1
} VisualMutationType;
typedef enum BaseValueSource {
    BaseValueSourceUnknown = 0,
    BaseValueSourceDefault = 1,
    BaseValueSourceBuiltInStyle = 2,
    BaseValueSourceStyle = 3,
    BaseValueSourceLocal = 4,
    Inherited = 5,
    DefaultStyleTrigger = 6,
    TemplateTrigger = 7,
    StyleTrigger = 8,
    ImplicitStyleReference = 9,
    ParentTemplate = 10,
    ParentTemplateTrigger = 11,
    Animation = 12,
    Coercion = 13,
    BaseValueSourceVisualState = 14
} BaseValueSource;
typedef struct SourceInfo {
    BSTR FileName;
    unsigned int LineNumber;
    unsigned int ColumnNumber;
    unsigned int CharPosition;
    BSTR Hash;
} SourceInfo;
typedef struct ParentChildRelation {
    InstanceHandle Parent;
    InstanceHandle Child;
    unsigned int ChildIndex;
} ParentChildRelation;
typedef struct VisualElement {
    InstanceHandle Handle;
    SourceInfo SrcInfo;
    BSTR Type;
    BSTR Name;
    unsigned int NumChildren;
} VisualElement;
typedef struct PropertyChainSource {
    InstanceHandle Handle;
    BSTR TargetType;
    BSTR Name;
    BaseValueSource Source;
    SourceInfo SrcInfo;
} PropertyChainSource;
typedef enum MetadataBit {
    None = 0x0,
    IsValueHandle = 0x1,
    IsPropertyReadOnly = 0x2,
    IsValueCollection = 0x4,
    IsValueCollectionReadOnly = 0x8,
    IsValueBindingExpression = 0x10,
    IsValueNull = 0x20,
    IsValueHandleAndEvaluatedValue = 0x40
} MetadataBit;
typedef struct PropertyChainValue {
    unsigned int Index;
    BSTR Type;
    BSTR DeclaringType;
    BSTR ValueType;
    BSTR ItemType;
    BSTR Value;
    WINBOOL Overridden;
    hyper MetadataBits;
    BSTR PropertyName;
    unsigned int PropertyChainIndex;
} PropertyChainValue;
typedef struct EnumType {
    BSTR Name;
    SAFEARRAY *ValueInts;
    SAFEARRAY *ValueStrings;
} EnumType;
typedef struct CollectionElementValue {
    unsigned int Index;
    BSTR ValueType;
    BSTR Value;
    hyper MetadataBits;
} CollectionElementValue;
typedef enum RenderTargetBitmapOptions {
    RenderTarget = 0,
    RenderTargetAndChildren = 1
} RenderTargetBitmapOptions;
typedef struct BitmapDescription {
    unsigned int Width;
    unsigned int Height;
    DXGI_FORMAT Format;
    DXGI_ALPHA_MODE AlphaMode;
} BitmapDescription;
typedef enum ResourceType {
    ResourceTypeStatic = 0,
    ResourceTypeTheme = 1
} ResourceType;
typedef enum VisualElementState {
    ErrorResolved = 0,
    ErrorResourceNotFound = 1,
    ErrorInvalidResource = 2
} VisualElementState;
/*****************************************************************************
 * IVisualTreeServiceCallback interface
 */
#ifndef __IVisualTreeServiceCallback_INTERFACE_DEFINED__
#define __IVisualTreeServiceCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVisualTreeServiceCallback, 0xaa7a8931, 0x80e4, 0x4fec, 0x8f,0x3b, 0x55,0x3f,0x87,0xb4,0x96,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa7a8931-80e4-4fec-8f3b-553f87b4966e")
IVisualTreeServiceCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnVisualTreeChange(
        ParentChildRelation relation,
        VisualElement element,
        VisualMutationType mutation_type) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVisualTreeServiceCallback, 0xaa7a8931, 0x80e4, 0x4fec, 0x8f,0x3b, 0x55,0x3f,0x87,0xb4,0x96,0x6e)
#endif
#else
typedef struct IVisualTreeServiceCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVisualTreeServiceCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVisualTreeServiceCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVisualTreeServiceCallback *This);

    /*** IVisualTreeServiceCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnVisualTreeChange)(
        IVisualTreeServiceCallback *This,
        ParentChildRelation relation,
        VisualElement element,
        VisualMutationType mutation_type);

    END_INTERFACE
} IVisualTreeServiceCallbackVtbl;

interface IVisualTreeServiceCallback {
    CONST_VTBL IVisualTreeServiceCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVisualTreeServiceCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVisualTreeServiceCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVisualTreeServiceCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IVisualTreeServiceCallback methods ***/
#define IVisualTreeServiceCallback_OnVisualTreeChange(This,relation,element,mutation_type) (This)->lpVtbl->OnVisualTreeChange(This,relation,element,mutation_type)
#else
/*** IUnknown methods ***/
static inline HRESULT IVisualTreeServiceCallback_QueryInterface(IVisualTreeServiceCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVisualTreeServiceCallback_AddRef(IVisualTreeServiceCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVisualTreeServiceCallback_Release(IVisualTreeServiceCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IVisualTreeServiceCallback methods ***/
static inline HRESULT IVisualTreeServiceCallback_OnVisualTreeChange(IVisualTreeServiceCallback* This,ParentChildRelation relation,VisualElement element,VisualMutationType mutation_type) {
    return This->lpVtbl->OnVisualTreeChange(This,relation,element,mutation_type);
}
#endif
#endif

#endif


#endif  /* __IVisualTreeServiceCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVisualTreeServiceCallback2 interface
 */
#ifndef __IVisualTreeServiceCallback2_INTERFACE_DEFINED__
#define __IVisualTreeServiceCallback2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVisualTreeServiceCallback2, 0xbad9eb88, 0xae77, 0x4397, 0xb9,0x48, 0x5f,0xa2,0xdb,0x0a,0x19,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bad9eb88-ae77-4397-b948-5fa2db0a19ea")
IVisualTreeServiceCallback2 : public IVisualTreeServiceCallback
{
    virtual HRESULT STDMETHODCALLTYPE OnElementStateChanged(
        InstanceHandle element,
        VisualElementState element_state,
        LPCWSTR context) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVisualTreeServiceCallback2, 0xbad9eb88, 0xae77, 0x4397, 0xb9,0x48, 0x5f,0xa2,0xdb,0x0a,0x19,0xea)
#endif
#else
typedef struct IVisualTreeServiceCallback2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVisualTreeServiceCallback2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVisualTreeServiceCallback2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVisualTreeServiceCallback2 *This);

    /*** IVisualTreeServiceCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnVisualTreeChange)(
        IVisualTreeServiceCallback2 *This,
        ParentChildRelation relation,
        VisualElement element,
        VisualMutationType mutation_type);

    /*** IVisualTreeServiceCallback2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnElementStateChanged)(
        IVisualTreeServiceCallback2 *This,
        InstanceHandle element,
        VisualElementState element_state,
        LPCWSTR context);

    END_INTERFACE
} IVisualTreeServiceCallback2Vtbl;

interface IVisualTreeServiceCallback2 {
    CONST_VTBL IVisualTreeServiceCallback2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVisualTreeServiceCallback2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVisualTreeServiceCallback2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVisualTreeServiceCallback2_Release(This) (This)->lpVtbl->Release(This)
/*** IVisualTreeServiceCallback methods ***/
#define IVisualTreeServiceCallback2_OnVisualTreeChange(This,relation,element,mutation_type) (This)->lpVtbl->OnVisualTreeChange(This,relation,element,mutation_type)
/*** IVisualTreeServiceCallback2 methods ***/
#define IVisualTreeServiceCallback2_OnElementStateChanged(This,element,element_state,context) (This)->lpVtbl->OnElementStateChanged(This,element,element_state,context)
#else
/*** IUnknown methods ***/
static inline HRESULT IVisualTreeServiceCallback2_QueryInterface(IVisualTreeServiceCallback2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVisualTreeServiceCallback2_AddRef(IVisualTreeServiceCallback2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVisualTreeServiceCallback2_Release(IVisualTreeServiceCallback2* This) {
    return This->lpVtbl->Release(This);
}
/*** IVisualTreeServiceCallback methods ***/
static inline HRESULT IVisualTreeServiceCallback2_OnVisualTreeChange(IVisualTreeServiceCallback2* This,ParentChildRelation relation,VisualElement element,VisualMutationType mutation_type) {
    return This->lpVtbl->OnVisualTreeChange(This,relation,element,mutation_type);
}
/*** IVisualTreeServiceCallback2 methods ***/
static inline HRESULT IVisualTreeServiceCallback2_OnElementStateChanged(IVisualTreeServiceCallback2* This,InstanceHandle element,VisualElementState element_state,LPCWSTR context) {
    return This->lpVtbl->OnElementStateChanged(This,element,element_state,context);
}
#endif
#endif

#endif


#endif  /* __IVisualTreeServiceCallback2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVisualTreeService interface
 */
#ifndef __IVisualTreeService_INTERFACE_DEFINED__
#define __IVisualTreeService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVisualTreeService, 0xa593b11a, 0xd17f, 0x48bb, 0x8f,0x66, 0x83,0x91,0x07,0x31,0xc8,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a593b11a-d17f-48bb-8f66-83910731c8a5")
IVisualTreeService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseVisualTreeChange(
        IVisualTreeServiceCallback *callback) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnadviseVisualTreeChange(
        IVisualTreeServiceCallback *callback) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnums(
        unsigned int *count,
        EnumType **enums) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        BSTR type_name,
        BSTR value,
        InstanceHandle *instance_handle) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyValuesChain(
        InstanceHandle instance_handle,
        unsigned int *source_count,
        PropertyChainSource **property_sources,
        unsigned int *property_count,
        PropertyChainValue **property_values) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProperty(
        InstanceHandle instance_handle,
        InstanceHandle value,
        unsigned int property_index) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearProperty(
        InstanceHandle instance_handle,
        unsigned int property_index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCollectionCount(
        InstanceHandle instance_handle,
        unsigned int *collection_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCollectionElements(
        InstanceHandle instance_handle,
        unsigned int start_index,
        unsigned int *element_count,
        CollectionElementValue **element_values) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddChild(
        InstanceHandle parent,
        InstanceHandle child,
        unsigned int index) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveChild(
        InstanceHandle parent,
        unsigned int index) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearChildren(
        InstanceHandle parent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVisualTreeService, 0xa593b11a, 0xd17f, 0x48bb, 0x8f,0x66, 0x83,0x91,0x07,0x31,0xc8,0xa5)
#endif
#else
typedef struct IVisualTreeServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVisualTreeService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVisualTreeService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVisualTreeService *This);

    /*** IVisualTreeService methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseVisualTreeChange)(
        IVisualTreeService *This,
        IVisualTreeServiceCallback *callback);

    HRESULT (STDMETHODCALLTYPE *UnadviseVisualTreeChange)(
        IVisualTreeService *This,
        IVisualTreeServiceCallback *callback);

    HRESULT (STDMETHODCALLTYPE *GetEnums)(
        IVisualTreeService *This,
        unsigned int *count,
        EnumType **enums);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IVisualTreeService *This,
        BSTR type_name,
        BSTR value,
        InstanceHandle *instance_handle);

    HRESULT (STDMETHODCALLTYPE *GetPropertyValuesChain)(
        IVisualTreeService *This,
        InstanceHandle instance_handle,
        unsigned int *source_count,
        PropertyChainSource **property_sources,
        unsigned int *property_count,
        PropertyChainValue **property_values);

    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IVisualTreeService *This,
        InstanceHandle instance_handle,
        InstanceHandle value,
        unsigned int property_index);

    HRESULT (STDMETHODCALLTYPE *ClearProperty)(
        IVisualTreeService *This,
        InstanceHandle instance_handle,
        unsigned int property_index);

    HRESULT (STDMETHODCALLTYPE *GetCollectionCount)(
        IVisualTreeService *This,
        InstanceHandle instance_handle,
        unsigned int *collection_size);

    HRESULT (STDMETHODCALLTYPE *GetCollectionElements)(
        IVisualTreeService *This,
        InstanceHandle instance_handle,
        unsigned int start_index,
        unsigned int *element_count,
        CollectionElementValue **element_values);

    HRESULT (STDMETHODCALLTYPE *AddChild)(
        IVisualTreeService *This,
        InstanceHandle parent,
        InstanceHandle child,
        unsigned int index);

    HRESULT (STDMETHODCALLTYPE *RemoveChild)(
        IVisualTreeService *This,
        InstanceHandle parent,
        unsigned int index);

    HRESULT (STDMETHODCALLTYPE *ClearChildren)(
        IVisualTreeService *This,
        InstanceHandle parent);

    END_INTERFACE
} IVisualTreeServiceVtbl;

interface IVisualTreeService {
    CONST_VTBL IVisualTreeServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVisualTreeService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVisualTreeService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVisualTreeService_Release(This) (This)->lpVtbl->Release(This)
/*** IVisualTreeService methods ***/
#define IVisualTreeService_AdviseVisualTreeChange(This,callback) (This)->lpVtbl->AdviseVisualTreeChange(This,callback)
#define IVisualTreeService_UnadviseVisualTreeChange(This,callback) (This)->lpVtbl->UnadviseVisualTreeChange(This,callback)
#define IVisualTreeService_GetEnums(This,count,enums) (This)->lpVtbl->GetEnums(This,count,enums)
#define IVisualTreeService_CreateInstance(This,type_name,value,instance_handle) (This)->lpVtbl->CreateInstance(This,type_name,value,instance_handle)
#define IVisualTreeService_GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values) (This)->lpVtbl->GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values)
#define IVisualTreeService_SetProperty(This,instance_handle,value,property_index) (This)->lpVtbl->SetProperty(This,instance_handle,value,property_index)
#define IVisualTreeService_ClearProperty(This,instance_handle,property_index) (This)->lpVtbl->ClearProperty(This,instance_handle,property_index)
#define IVisualTreeService_GetCollectionCount(This,instance_handle,collection_size) (This)->lpVtbl->GetCollectionCount(This,instance_handle,collection_size)
#define IVisualTreeService_GetCollectionElements(This,instance_handle,start_index,element_count,element_values) (This)->lpVtbl->GetCollectionElements(This,instance_handle,start_index,element_count,element_values)
#define IVisualTreeService_AddChild(This,parent,child,index) (This)->lpVtbl->AddChild(This,parent,child,index)
#define IVisualTreeService_RemoveChild(This,parent,index) (This)->lpVtbl->RemoveChild(This,parent,index)
#define IVisualTreeService_ClearChildren(This,parent) (This)->lpVtbl->ClearChildren(This,parent)
#else
/*** IUnknown methods ***/
static inline HRESULT IVisualTreeService_QueryInterface(IVisualTreeService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVisualTreeService_AddRef(IVisualTreeService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVisualTreeService_Release(IVisualTreeService* This) {
    return This->lpVtbl->Release(This);
}
/*** IVisualTreeService methods ***/
static inline HRESULT IVisualTreeService_AdviseVisualTreeChange(IVisualTreeService* This,IVisualTreeServiceCallback *callback) {
    return This->lpVtbl->AdviseVisualTreeChange(This,callback);
}
static inline HRESULT IVisualTreeService_UnadviseVisualTreeChange(IVisualTreeService* This,IVisualTreeServiceCallback *callback) {
    return This->lpVtbl->UnadviseVisualTreeChange(This,callback);
}
static inline HRESULT IVisualTreeService_GetEnums(IVisualTreeService* This,unsigned int *count,EnumType **enums) {
    return This->lpVtbl->GetEnums(This,count,enums);
}
static inline HRESULT IVisualTreeService_CreateInstance(IVisualTreeService* This,BSTR type_name,BSTR value,InstanceHandle *instance_handle) {
    return This->lpVtbl->CreateInstance(This,type_name,value,instance_handle);
}
static inline HRESULT IVisualTreeService_GetPropertyValuesChain(IVisualTreeService* This,InstanceHandle instance_handle,unsigned int *source_count,PropertyChainSource **property_sources,unsigned int *property_count,PropertyChainValue **property_values) {
    return This->lpVtbl->GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values);
}
static inline HRESULT IVisualTreeService_SetProperty(IVisualTreeService* This,InstanceHandle instance_handle,InstanceHandle value,unsigned int property_index) {
    return This->lpVtbl->SetProperty(This,instance_handle,value,property_index);
}
static inline HRESULT IVisualTreeService_ClearProperty(IVisualTreeService* This,InstanceHandle instance_handle,unsigned int property_index) {
    return This->lpVtbl->ClearProperty(This,instance_handle,property_index);
}
static inline HRESULT IVisualTreeService_GetCollectionCount(IVisualTreeService* This,InstanceHandle instance_handle,unsigned int *collection_size) {
    return This->lpVtbl->GetCollectionCount(This,instance_handle,collection_size);
}
static inline HRESULT IVisualTreeService_GetCollectionElements(IVisualTreeService* This,InstanceHandle instance_handle,unsigned int start_index,unsigned int *element_count,CollectionElementValue **element_values) {
    return This->lpVtbl->GetCollectionElements(This,instance_handle,start_index,element_count,element_values);
}
static inline HRESULT IVisualTreeService_AddChild(IVisualTreeService* This,InstanceHandle parent,InstanceHandle child,unsigned int index) {
    return This->lpVtbl->AddChild(This,parent,child,index);
}
static inline HRESULT IVisualTreeService_RemoveChild(IVisualTreeService* This,InstanceHandle parent,unsigned int index) {
    return This->lpVtbl->RemoveChild(This,parent,index);
}
static inline HRESULT IVisualTreeService_ClearChildren(IVisualTreeService* This,InstanceHandle parent) {
    return This->lpVtbl->ClearChildren(This,parent);
}
#endif
#endif

#endif


#endif  /* __IVisualTreeService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXamlDiagnostics interface
 */
#ifndef __IXamlDiagnostics_INTERFACE_DEFINED__
#define __IXamlDiagnostics_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXamlDiagnostics, 0x18c9e2b6, 0x3f43, 0x4116, 0x9f,0x2b, 0xff,0x93,0x5d,0x77,0x70,0xd2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("18c9e2b6-3f43-4116-9f2b-ff935d7770d2")
IXamlDiagnostics : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDispatcher(
        IInspectable **dispatcher) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUiLayer(
        IInspectable **layer) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetApplication(
        IInspectable **application) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIInspectableFromHandle(
        InstanceHandle instance_handle,
        IInspectable **instance) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHandleFromIInspectable(
        IInspectable *instance,
        InstanceHandle *handle) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTest(
        RECT rect,
        unsigned int *count,
        InstanceHandle **instance_handles) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterInstance(
        IInspectable *instance,
        InstanceHandle *instance_handle) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInitializationData(
        BSTR *initialization_data) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXamlDiagnostics, 0x18c9e2b6, 0x3f43, 0x4116, 0x9f,0x2b, 0xff,0x93,0x5d,0x77,0x70,0xd2)
#endif
#else
typedef struct IXamlDiagnosticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXamlDiagnostics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXamlDiagnostics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXamlDiagnostics *This);

    /*** IXamlDiagnostics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDispatcher)(
        IXamlDiagnostics *This,
        IInspectable **dispatcher);

    HRESULT (STDMETHODCALLTYPE *GetUiLayer)(
        IXamlDiagnostics *This,
        IInspectable **layer);

    HRESULT (STDMETHODCALLTYPE *GetApplication)(
        IXamlDiagnostics *This,
        IInspectable **application);

    HRESULT (STDMETHODCALLTYPE *GetIInspectableFromHandle)(
        IXamlDiagnostics *This,
        InstanceHandle instance_handle,
        IInspectable **instance);

    HRESULT (STDMETHODCALLTYPE *GetHandleFromIInspectable)(
        IXamlDiagnostics *This,
        IInspectable *instance,
        InstanceHandle *handle);

    HRESULT (STDMETHODCALLTYPE *HitTest)(
        IXamlDiagnostics *This,
        RECT rect,
        unsigned int *count,
        InstanceHandle **instance_handles);

    HRESULT (STDMETHODCALLTYPE *RegisterInstance)(
        IXamlDiagnostics *This,
        IInspectable *instance,
        InstanceHandle *instance_handle);

    HRESULT (STDMETHODCALLTYPE *GetInitializationData)(
        IXamlDiagnostics *This,
        BSTR *initialization_data);

    END_INTERFACE
} IXamlDiagnosticsVtbl;

interface IXamlDiagnostics {
    CONST_VTBL IXamlDiagnosticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXamlDiagnostics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXamlDiagnostics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXamlDiagnostics_Release(This) (This)->lpVtbl->Release(This)
/*** IXamlDiagnostics methods ***/
#define IXamlDiagnostics_GetDispatcher(This,dispatcher) (This)->lpVtbl->GetDispatcher(This,dispatcher)
#define IXamlDiagnostics_GetUiLayer(This,layer) (This)->lpVtbl->GetUiLayer(This,layer)
#define IXamlDiagnostics_GetApplication(This,application) (This)->lpVtbl->GetApplication(This,application)
#define IXamlDiagnostics_GetIInspectableFromHandle(This,instance_handle,instance) (This)->lpVtbl->GetIInspectableFromHandle(This,instance_handle,instance)
#define IXamlDiagnostics_GetHandleFromIInspectable(This,instance,handle) (This)->lpVtbl->GetHandleFromIInspectable(This,instance,handle)
#define IXamlDiagnostics_HitTest(This,rect,count,instance_handles) (This)->lpVtbl->HitTest(This,rect,count,instance_handles)
#define IXamlDiagnostics_RegisterInstance(This,instance,instance_handle) (This)->lpVtbl->RegisterInstance(This,instance,instance_handle)
#define IXamlDiagnostics_GetInitializationData(This,initialization_data) (This)->lpVtbl->GetInitializationData(This,initialization_data)
#else
/*** IUnknown methods ***/
static inline HRESULT IXamlDiagnostics_QueryInterface(IXamlDiagnostics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXamlDiagnostics_AddRef(IXamlDiagnostics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXamlDiagnostics_Release(IXamlDiagnostics* This) {
    return This->lpVtbl->Release(This);
}
/*** IXamlDiagnostics methods ***/
static inline HRESULT IXamlDiagnostics_GetDispatcher(IXamlDiagnostics* This,IInspectable **dispatcher) {
    return This->lpVtbl->GetDispatcher(This,dispatcher);
}
static inline HRESULT IXamlDiagnostics_GetUiLayer(IXamlDiagnostics* This,IInspectable **layer) {
    return This->lpVtbl->GetUiLayer(This,layer);
}
static inline HRESULT IXamlDiagnostics_GetApplication(IXamlDiagnostics* This,IInspectable **application) {
    return This->lpVtbl->GetApplication(This,application);
}
static inline HRESULT IXamlDiagnostics_GetIInspectableFromHandle(IXamlDiagnostics* This,InstanceHandle instance_handle,IInspectable **instance) {
    return This->lpVtbl->GetIInspectableFromHandle(This,instance_handle,instance);
}
static inline HRESULT IXamlDiagnostics_GetHandleFromIInspectable(IXamlDiagnostics* This,IInspectable *instance,InstanceHandle *handle) {
    return This->lpVtbl->GetHandleFromIInspectable(This,instance,handle);
}
static inline HRESULT IXamlDiagnostics_HitTest(IXamlDiagnostics* This,RECT rect,unsigned int *count,InstanceHandle **instance_handles) {
    return This->lpVtbl->HitTest(This,rect,count,instance_handles);
}
static inline HRESULT IXamlDiagnostics_RegisterInstance(IXamlDiagnostics* This,IInspectable *instance,InstanceHandle *instance_handle) {
    return This->lpVtbl->RegisterInstance(This,instance,instance_handle);
}
static inline HRESULT IXamlDiagnostics_GetInitializationData(IXamlDiagnostics* This,BSTR *initialization_data) {
    return This->lpVtbl->GetInitializationData(This,initialization_data);
}
#endif
#endif

#endif


#endif  /* __IXamlDiagnostics_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBitmapData interface
 */
#ifndef __IBitmapData_INTERFACE_DEFINED__
#define __IBitmapData_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBitmapData, 0xd1a34ef2, 0xcad8, 0x4635, 0xa3,0xd2, 0xfc,0xda,0x8d,0x3f,0x3c,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d1a34ef2-cad8-4635-a3d2-fcda8d3f3caf")
IBitmapData : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CopyBytesTo(
        unsigned int source_offset_in_bytes,
        unsigned int max_bytes_to_copy,
        byte *bytes,
        unsigned int *number_of_bytes_copied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStride(
        unsigned int *stride) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBitmapDescription(
        BitmapDescription *bitmap_description) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceBitmapDescription(
        BitmapDescription *bitmap_description) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBitmapData, 0xd1a34ef2, 0xcad8, 0x4635, 0xa3,0xd2, 0xfc,0xda,0x8d,0x3f,0x3c,0xaf)
#endif
#else
typedef struct IBitmapDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBitmapData *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBitmapData *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBitmapData *This);

    /*** IBitmapData methods ***/
    HRESULT (STDMETHODCALLTYPE *CopyBytesTo)(
        IBitmapData *This,
        unsigned int source_offset_in_bytes,
        unsigned int max_bytes_to_copy,
        byte *bytes,
        unsigned int *number_of_bytes_copied);

    HRESULT (STDMETHODCALLTYPE *GetStride)(
        IBitmapData *This,
        unsigned int *stride);

    HRESULT (STDMETHODCALLTYPE *GetBitmapDescription)(
        IBitmapData *This,
        BitmapDescription *bitmap_description);

    HRESULT (STDMETHODCALLTYPE *GetSourceBitmapDescription)(
        IBitmapData *This,
        BitmapDescription *bitmap_description);

    END_INTERFACE
} IBitmapDataVtbl;

interface IBitmapData {
    CONST_VTBL IBitmapDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBitmapData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBitmapData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBitmapData_Release(This) (This)->lpVtbl->Release(This)
/*** IBitmapData methods ***/
#define IBitmapData_CopyBytesTo(This,source_offset_in_bytes,max_bytes_to_copy,bytes,number_of_bytes_copied) (This)->lpVtbl->CopyBytesTo(This,source_offset_in_bytes,max_bytes_to_copy,bytes,number_of_bytes_copied)
#define IBitmapData_GetStride(This,stride) (This)->lpVtbl->GetStride(This,stride)
#define IBitmapData_GetBitmapDescription(This,bitmap_description) (This)->lpVtbl->GetBitmapDescription(This,bitmap_description)
#define IBitmapData_GetSourceBitmapDescription(This,bitmap_description) (This)->lpVtbl->GetSourceBitmapDescription(This,bitmap_description)
#else
/*** IUnknown methods ***/
static inline HRESULT IBitmapData_QueryInterface(IBitmapData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBitmapData_AddRef(IBitmapData* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBitmapData_Release(IBitmapData* This) {
    return This->lpVtbl->Release(This);
}
/*** IBitmapData methods ***/
static inline HRESULT IBitmapData_CopyBytesTo(IBitmapData* This,unsigned int source_offset_in_bytes,unsigned int max_bytes_to_copy,byte *bytes,unsigned int *number_of_bytes_copied) {
    return This->lpVtbl->CopyBytesTo(This,source_offset_in_bytes,max_bytes_to_copy,bytes,number_of_bytes_copied);
}
static inline HRESULT IBitmapData_GetStride(IBitmapData* This,unsigned int *stride) {
    return This->lpVtbl->GetStride(This,stride);
}
static inline HRESULT IBitmapData_GetBitmapDescription(IBitmapData* This,BitmapDescription *bitmap_description) {
    return This->lpVtbl->GetBitmapDescription(This,bitmap_description);
}
static inline HRESULT IBitmapData_GetSourceBitmapDescription(IBitmapData* This,BitmapDescription *bitmap_description) {
    return This->lpVtbl->GetSourceBitmapDescription(This,bitmap_description);
}
#endif
#endif

#endif


#endif  /* __IBitmapData_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVisualTreeService2 interface
 */
#ifndef __IVisualTreeService2_INTERFACE_DEFINED__
#define __IVisualTreeService2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVisualTreeService2, 0x130f5136, 0xec43, 0x4f61, 0x89,0xc7, 0x98,0x01,0xa3,0x6d,0x2e,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("130f5136-ec43-4f61-89c7-9801a36d2e95")
IVisualTreeService2 : public IVisualTreeService
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyIndex(
        InstanceHandle object,
        LPCWSTR property_name,
        unsigned int *property_index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        InstanceHandle object,
        unsigned int property_index,
        InstanceHandle *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReplaceResource(
        InstanceHandle resource_dictionary,
        InstanceHandle key,
        InstanceHandle new_value) = 0;

    virtual HRESULT STDMETHODCALLTYPE RenderTargetBitmap(
        InstanceHandle handle,
        RenderTargetBitmapOptions options,
        unsigned int max_pixel_width,
        unsigned int max_pixel_height,
        IBitmapData **bitmap_data) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVisualTreeService2, 0x130f5136, 0xec43, 0x4f61, 0x89,0xc7, 0x98,0x01,0xa3,0x6d,0x2e,0x95)
#endif
#else
typedef struct IVisualTreeService2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVisualTreeService2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVisualTreeService2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVisualTreeService2 *This);

    /*** IVisualTreeService methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseVisualTreeChange)(
        IVisualTreeService2 *This,
        IVisualTreeServiceCallback *callback);

    HRESULT (STDMETHODCALLTYPE *UnadviseVisualTreeChange)(
        IVisualTreeService2 *This,
        IVisualTreeServiceCallback *callback);

    HRESULT (STDMETHODCALLTYPE *GetEnums)(
        IVisualTreeService2 *This,
        unsigned int *count,
        EnumType **enums);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IVisualTreeService2 *This,
        BSTR type_name,
        BSTR value,
        InstanceHandle *instance_handle);

    HRESULT (STDMETHODCALLTYPE *GetPropertyValuesChain)(
        IVisualTreeService2 *This,
        InstanceHandle instance_handle,
        unsigned int *source_count,
        PropertyChainSource **property_sources,
        unsigned int *property_count,
        PropertyChainValue **property_values);

    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IVisualTreeService2 *This,
        InstanceHandle instance_handle,
        InstanceHandle value,
        unsigned int property_index);

    HRESULT (STDMETHODCALLTYPE *ClearProperty)(
        IVisualTreeService2 *This,
        InstanceHandle instance_handle,
        unsigned int property_index);

    HRESULT (STDMETHODCALLTYPE *GetCollectionCount)(
        IVisualTreeService2 *This,
        InstanceHandle instance_handle,
        unsigned int *collection_size);

    HRESULT (STDMETHODCALLTYPE *GetCollectionElements)(
        IVisualTreeService2 *This,
        InstanceHandle instance_handle,
        unsigned int start_index,
        unsigned int *element_count,
        CollectionElementValue **element_values);

    HRESULT (STDMETHODCALLTYPE *AddChild)(
        IVisualTreeService2 *This,
        InstanceHandle parent,
        InstanceHandle child,
        unsigned int index);

    HRESULT (STDMETHODCALLTYPE *RemoveChild)(
        IVisualTreeService2 *This,
        InstanceHandle parent,
        unsigned int index);

    HRESULT (STDMETHODCALLTYPE *ClearChildren)(
        IVisualTreeService2 *This,
        InstanceHandle parent);

    /*** IVisualTreeService2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyIndex)(
        IVisualTreeService2 *This,
        InstanceHandle object,
        LPCWSTR property_name,
        unsigned int *property_index);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IVisualTreeService2 *This,
        InstanceHandle object,
        unsigned int property_index,
        InstanceHandle *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceResource)(
        IVisualTreeService2 *This,
        InstanceHandle resource_dictionary,
        InstanceHandle key,
        InstanceHandle new_value);

    HRESULT (STDMETHODCALLTYPE *RenderTargetBitmap)(
        IVisualTreeService2 *This,
        InstanceHandle handle,
        RenderTargetBitmapOptions options,
        unsigned int max_pixel_width,
        unsigned int max_pixel_height,
        IBitmapData **bitmap_data);

    END_INTERFACE
} IVisualTreeService2Vtbl;

interface IVisualTreeService2 {
    CONST_VTBL IVisualTreeService2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVisualTreeService2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVisualTreeService2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVisualTreeService2_Release(This) (This)->lpVtbl->Release(This)
/*** IVisualTreeService methods ***/
#define IVisualTreeService2_AdviseVisualTreeChange(This,callback) (This)->lpVtbl->AdviseVisualTreeChange(This,callback)
#define IVisualTreeService2_UnadviseVisualTreeChange(This,callback) (This)->lpVtbl->UnadviseVisualTreeChange(This,callback)
#define IVisualTreeService2_GetEnums(This,count,enums) (This)->lpVtbl->GetEnums(This,count,enums)
#define IVisualTreeService2_CreateInstance(This,type_name,value,instance_handle) (This)->lpVtbl->CreateInstance(This,type_name,value,instance_handle)
#define IVisualTreeService2_GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values) (This)->lpVtbl->GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values)
#define IVisualTreeService2_SetProperty(This,instance_handle,value,property_index) (This)->lpVtbl->SetProperty(This,instance_handle,value,property_index)
#define IVisualTreeService2_ClearProperty(This,instance_handle,property_index) (This)->lpVtbl->ClearProperty(This,instance_handle,property_index)
#define IVisualTreeService2_GetCollectionCount(This,instance_handle,collection_size) (This)->lpVtbl->GetCollectionCount(This,instance_handle,collection_size)
#define IVisualTreeService2_GetCollectionElements(This,instance_handle,start_index,element_count,element_values) (This)->lpVtbl->GetCollectionElements(This,instance_handle,start_index,element_count,element_values)
#define IVisualTreeService2_AddChild(This,parent,child,index) (This)->lpVtbl->AddChild(This,parent,child,index)
#define IVisualTreeService2_RemoveChild(This,parent,index) (This)->lpVtbl->RemoveChild(This,parent,index)
#define IVisualTreeService2_ClearChildren(This,parent) (This)->lpVtbl->ClearChildren(This,parent)
/*** IVisualTreeService2 methods ***/
#define IVisualTreeService2_GetPropertyIndex(This,object,property_name,property_index) (This)->lpVtbl->GetPropertyIndex(This,object,property_name,property_index)
#define IVisualTreeService2_GetProperty(This,object,property_index,value) (This)->lpVtbl->GetProperty(This,object,property_index,value)
#define IVisualTreeService2_ReplaceResource(This,resource_dictionary,key,new_value) (This)->lpVtbl->ReplaceResource(This,resource_dictionary,key,new_value)
#define IVisualTreeService2_RenderTargetBitmap(This,handle,options,max_pixel_width,max_pixel_height,bitmap_data) (This)->lpVtbl->RenderTargetBitmap(This,handle,options,max_pixel_width,max_pixel_height,bitmap_data)
#else
/*** IUnknown methods ***/
static inline HRESULT IVisualTreeService2_QueryInterface(IVisualTreeService2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVisualTreeService2_AddRef(IVisualTreeService2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVisualTreeService2_Release(IVisualTreeService2* This) {
    return This->lpVtbl->Release(This);
}
/*** IVisualTreeService methods ***/
static inline HRESULT IVisualTreeService2_AdviseVisualTreeChange(IVisualTreeService2* This,IVisualTreeServiceCallback *callback) {
    return This->lpVtbl->AdviseVisualTreeChange(This,callback);
}
static inline HRESULT IVisualTreeService2_UnadviseVisualTreeChange(IVisualTreeService2* This,IVisualTreeServiceCallback *callback) {
    return This->lpVtbl->UnadviseVisualTreeChange(This,callback);
}
static inline HRESULT IVisualTreeService2_GetEnums(IVisualTreeService2* This,unsigned int *count,EnumType **enums) {
    return This->lpVtbl->GetEnums(This,count,enums);
}
static inline HRESULT IVisualTreeService2_CreateInstance(IVisualTreeService2* This,BSTR type_name,BSTR value,InstanceHandle *instance_handle) {
    return This->lpVtbl->CreateInstance(This,type_name,value,instance_handle);
}
static inline HRESULT IVisualTreeService2_GetPropertyValuesChain(IVisualTreeService2* This,InstanceHandle instance_handle,unsigned int *source_count,PropertyChainSource **property_sources,unsigned int *property_count,PropertyChainValue **property_values) {
    return This->lpVtbl->GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values);
}
static inline HRESULT IVisualTreeService2_SetProperty(IVisualTreeService2* This,InstanceHandle instance_handle,InstanceHandle value,unsigned int property_index) {
    return This->lpVtbl->SetProperty(This,instance_handle,value,property_index);
}
static inline HRESULT IVisualTreeService2_ClearProperty(IVisualTreeService2* This,InstanceHandle instance_handle,unsigned int property_index) {
    return This->lpVtbl->ClearProperty(This,instance_handle,property_index);
}
static inline HRESULT IVisualTreeService2_GetCollectionCount(IVisualTreeService2* This,InstanceHandle instance_handle,unsigned int *collection_size) {
    return This->lpVtbl->GetCollectionCount(This,instance_handle,collection_size);
}
static inline HRESULT IVisualTreeService2_GetCollectionElements(IVisualTreeService2* This,InstanceHandle instance_handle,unsigned int start_index,unsigned int *element_count,CollectionElementValue **element_values) {
    return This->lpVtbl->GetCollectionElements(This,instance_handle,start_index,element_count,element_values);
}
static inline HRESULT IVisualTreeService2_AddChild(IVisualTreeService2* This,InstanceHandle parent,InstanceHandle child,unsigned int index) {
    return This->lpVtbl->AddChild(This,parent,child,index);
}
static inline HRESULT IVisualTreeService2_RemoveChild(IVisualTreeService2* This,InstanceHandle parent,unsigned int index) {
    return This->lpVtbl->RemoveChild(This,parent,index);
}
static inline HRESULT IVisualTreeService2_ClearChildren(IVisualTreeService2* This,InstanceHandle parent) {
    return This->lpVtbl->ClearChildren(This,parent);
}
/*** IVisualTreeService2 methods ***/
static inline HRESULT IVisualTreeService2_GetPropertyIndex(IVisualTreeService2* This,InstanceHandle object,LPCWSTR property_name,unsigned int *property_index) {
    return This->lpVtbl->GetPropertyIndex(This,object,property_name,property_index);
}
static inline HRESULT IVisualTreeService2_GetProperty(IVisualTreeService2* This,InstanceHandle object,unsigned int property_index,InstanceHandle *value) {
    return This->lpVtbl->GetProperty(This,object,property_index,value);
}
static inline HRESULT IVisualTreeService2_ReplaceResource(IVisualTreeService2* This,InstanceHandle resource_dictionary,InstanceHandle key,InstanceHandle new_value) {
    return This->lpVtbl->ReplaceResource(This,resource_dictionary,key,new_value);
}
static inline HRESULT IVisualTreeService2_RenderTargetBitmap(IVisualTreeService2* This,InstanceHandle handle,RenderTargetBitmapOptions options,unsigned int max_pixel_width,unsigned int max_pixel_height,IBitmapData **bitmap_data) {
    return This->lpVtbl->RenderTargetBitmap(This,handle,options,max_pixel_width,max_pixel_height,bitmap_data);
}
#endif
#endif

#endif


#endif  /* __IVisualTreeService2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVisualTreeService3 interface
 */
#ifndef __IVisualTreeService3_INTERFACE_DEFINED__
#define __IVisualTreeService3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVisualTreeService3, 0x0e79c6e0, 0x85a0, 0x4be8, 0xb4,0x1a, 0x65,0x5c,0xf1,0xfd,0x19,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0e79c6e0-85a0-4be8-b41a-655cf1fd19bd")
IVisualTreeService3 : public IVisualTreeService2
{
    virtual HRESULT STDMETHODCALLTYPE ResolveResource(
        InstanceHandle resource_context,
        LPCWSTR resource_name,
        ResourceType resource_type,
        unsigned int property_index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDictionaryItem(
        InstanceHandle dictionary_handle,
        LPCWSTR resource_name,
        WINBOOL resource_is_implicit_style,
        InstanceHandle *resource_handle) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDictionaryItem(
        InstanceHandle dictionary_handle,
        InstanceHandle resource_key,
        InstanceHandle resource_handle) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveDictionaryItem(
        InstanceHandle dictionary_handle,
        InstanceHandle resource_key) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVisualTreeService3, 0x0e79c6e0, 0x85a0, 0x4be8, 0xb4,0x1a, 0x65,0x5c,0xf1,0xfd,0x19,0xbd)
#endif
#else
typedef struct IVisualTreeService3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVisualTreeService3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVisualTreeService3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVisualTreeService3 *This);

    /*** IVisualTreeService methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseVisualTreeChange)(
        IVisualTreeService3 *This,
        IVisualTreeServiceCallback *callback);

    HRESULT (STDMETHODCALLTYPE *UnadviseVisualTreeChange)(
        IVisualTreeService3 *This,
        IVisualTreeServiceCallback *callback);

    HRESULT (STDMETHODCALLTYPE *GetEnums)(
        IVisualTreeService3 *This,
        unsigned int *count,
        EnumType **enums);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IVisualTreeService3 *This,
        BSTR type_name,
        BSTR value,
        InstanceHandle *instance_handle);

    HRESULT (STDMETHODCALLTYPE *GetPropertyValuesChain)(
        IVisualTreeService3 *This,
        InstanceHandle instance_handle,
        unsigned int *source_count,
        PropertyChainSource **property_sources,
        unsigned int *property_count,
        PropertyChainValue **property_values);

    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IVisualTreeService3 *This,
        InstanceHandle instance_handle,
        InstanceHandle value,
        unsigned int property_index);

    HRESULT (STDMETHODCALLTYPE *ClearProperty)(
        IVisualTreeService3 *This,
        InstanceHandle instance_handle,
        unsigned int property_index);

    HRESULT (STDMETHODCALLTYPE *GetCollectionCount)(
        IVisualTreeService3 *This,
        InstanceHandle instance_handle,
        unsigned int *collection_size);

    HRESULT (STDMETHODCALLTYPE *GetCollectionElements)(
        IVisualTreeService3 *This,
        InstanceHandle instance_handle,
        unsigned int start_index,
        unsigned int *element_count,
        CollectionElementValue **element_values);

    HRESULT (STDMETHODCALLTYPE *AddChild)(
        IVisualTreeService3 *This,
        InstanceHandle parent,
        InstanceHandle child,
        unsigned int index);

    HRESULT (STDMETHODCALLTYPE *RemoveChild)(
        IVisualTreeService3 *This,
        InstanceHandle parent,
        unsigned int index);

    HRESULT (STDMETHODCALLTYPE *ClearChildren)(
        IVisualTreeService3 *This,
        InstanceHandle parent);

    /*** IVisualTreeService2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyIndex)(
        IVisualTreeService3 *This,
        InstanceHandle object,
        LPCWSTR property_name,
        unsigned int *property_index);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IVisualTreeService3 *This,
        InstanceHandle object,
        unsigned int property_index,
        InstanceHandle *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceResource)(
        IVisualTreeService3 *This,
        InstanceHandle resource_dictionary,
        InstanceHandle key,
        InstanceHandle new_value);

    HRESULT (STDMETHODCALLTYPE *RenderTargetBitmap)(
        IVisualTreeService3 *This,
        InstanceHandle handle,
        RenderTargetBitmapOptions options,
        unsigned int max_pixel_width,
        unsigned int max_pixel_height,
        IBitmapData **bitmap_data);

    /*** IVisualTreeService3 methods ***/
    HRESULT (STDMETHODCALLTYPE *ResolveResource)(
        IVisualTreeService3 *This,
        InstanceHandle resource_context,
        LPCWSTR resource_name,
        ResourceType resource_type,
        unsigned int property_index);

    HRESULT (STDMETHODCALLTYPE *GetDictionaryItem)(
        IVisualTreeService3 *This,
        InstanceHandle dictionary_handle,
        LPCWSTR resource_name,
        WINBOOL resource_is_implicit_style,
        InstanceHandle *resource_handle);

    HRESULT (STDMETHODCALLTYPE *AddDictionaryItem)(
        IVisualTreeService3 *This,
        InstanceHandle dictionary_handle,
        InstanceHandle resource_key,
        InstanceHandle resource_handle);

    HRESULT (STDMETHODCALLTYPE *RemoveDictionaryItem)(
        IVisualTreeService3 *This,
        InstanceHandle dictionary_handle,
        InstanceHandle resource_key);

    END_INTERFACE
} IVisualTreeService3Vtbl;

interface IVisualTreeService3 {
    CONST_VTBL IVisualTreeService3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVisualTreeService3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVisualTreeService3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVisualTreeService3_Release(This) (This)->lpVtbl->Release(This)
/*** IVisualTreeService methods ***/
#define IVisualTreeService3_AdviseVisualTreeChange(This,callback) (This)->lpVtbl->AdviseVisualTreeChange(This,callback)
#define IVisualTreeService3_UnadviseVisualTreeChange(This,callback) (This)->lpVtbl->UnadviseVisualTreeChange(This,callback)
#define IVisualTreeService3_GetEnums(This,count,enums) (This)->lpVtbl->GetEnums(This,count,enums)
#define IVisualTreeService3_CreateInstance(This,type_name,value,instance_handle) (This)->lpVtbl->CreateInstance(This,type_name,value,instance_handle)
#define IVisualTreeService3_GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values) (This)->lpVtbl->GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values)
#define IVisualTreeService3_SetProperty(This,instance_handle,value,property_index) (This)->lpVtbl->SetProperty(This,instance_handle,value,property_index)
#define IVisualTreeService3_ClearProperty(This,instance_handle,property_index) (This)->lpVtbl->ClearProperty(This,instance_handle,property_index)
#define IVisualTreeService3_GetCollectionCount(This,instance_handle,collection_size) (This)->lpVtbl->GetCollectionCount(This,instance_handle,collection_size)
#define IVisualTreeService3_GetCollectionElements(This,instance_handle,start_index,element_count,element_values) (This)->lpVtbl->GetCollectionElements(This,instance_handle,start_index,element_count,element_values)
#define IVisualTreeService3_AddChild(This,parent,child,index) (This)->lpVtbl->AddChild(This,parent,child,index)
#define IVisualTreeService3_RemoveChild(This,parent,index) (This)->lpVtbl->RemoveChild(This,parent,index)
#define IVisualTreeService3_ClearChildren(This,parent) (This)->lpVtbl->ClearChildren(This,parent)
/*** IVisualTreeService2 methods ***/
#define IVisualTreeService3_GetPropertyIndex(This,object,property_name,property_index) (This)->lpVtbl->GetPropertyIndex(This,object,property_name,property_index)
#define IVisualTreeService3_GetProperty(This,object,property_index,value) (This)->lpVtbl->GetProperty(This,object,property_index,value)
#define IVisualTreeService3_ReplaceResource(This,resource_dictionary,key,new_value) (This)->lpVtbl->ReplaceResource(This,resource_dictionary,key,new_value)
#define IVisualTreeService3_RenderTargetBitmap(This,handle,options,max_pixel_width,max_pixel_height,bitmap_data) (This)->lpVtbl->RenderTargetBitmap(This,handle,options,max_pixel_width,max_pixel_height,bitmap_data)
/*** IVisualTreeService3 methods ***/
#define IVisualTreeService3_ResolveResource(This,resource_context,resource_name,resource_type,property_index) (This)->lpVtbl->ResolveResource(This,resource_context,resource_name,resource_type,property_index)
#define IVisualTreeService3_GetDictionaryItem(This,dictionary_handle,resource_name,resource_is_implicit_style,resource_handle) (This)->lpVtbl->GetDictionaryItem(This,dictionary_handle,resource_name,resource_is_implicit_style,resource_handle)
#define IVisualTreeService3_AddDictionaryItem(This,dictionary_handle,resource_key,resource_handle) (This)->lpVtbl->AddDictionaryItem(This,dictionary_handle,resource_key,resource_handle)
#define IVisualTreeService3_RemoveDictionaryItem(This,dictionary_handle,resource_key) (This)->lpVtbl->RemoveDictionaryItem(This,dictionary_handle,resource_key)
#else
/*** IUnknown methods ***/
static inline HRESULT IVisualTreeService3_QueryInterface(IVisualTreeService3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVisualTreeService3_AddRef(IVisualTreeService3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVisualTreeService3_Release(IVisualTreeService3* This) {
    return This->lpVtbl->Release(This);
}
/*** IVisualTreeService methods ***/
static inline HRESULT IVisualTreeService3_AdviseVisualTreeChange(IVisualTreeService3* This,IVisualTreeServiceCallback *callback) {
    return This->lpVtbl->AdviseVisualTreeChange(This,callback);
}
static inline HRESULT IVisualTreeService3_UnadviseVisualTreeChange(IVisualTreeService3* This,IVisualTreeServiceCallback *callback) {
    return This->lpVtbl->UnadviseVisualTreeChange(This,callback);
}
static inline HRESULT IVisualTreeService3_GetEnums(IVisualTreeService3* This,unsigned int *count,EnumType **enums) {
    return This->lpVtbl->GetEnums(This,count,enums);
}
static inline HRESULT IVisualTreeService3_CreateInstance(IVisualTreeService3* This,BSTR type_name,BSTR value,InstanceHandle *instance_handle) {
    return This->lpVtbl->CreateInstance(This,type_name,value,instance_handle);
}
static inline HRESULT IVisualTreeService3_GetPropertyValuesChain(IVisualTreeService3* This,InstanceHandle instance_handle,unsigned int *source_count,PropertyChainSource **property_sources,unsigned int *property_count,PropertyChainValue **property_values) {
    return This->lpVtbl->GetPropertyValuesChain(This,instance_handle,source_count,property_sources,property_count,property_values);
}
static inline HRESULT IVisualTreeService3_SetProperty(IVisualTreeService3* This,InstanceHandle instance_handle,InstanceHandle value,unsigned int property_index) {
    return This->lpVtbl->SetProperty(This,instance_handle,value,property_index);
}
static inline HRESULT IVisualTreeService3_ClearProperty(IVisualTreeService3* This,InstanceHandle instance_handle,unsigned int property_index) {
    return This->lpVtbl->ClearProperty(This,instance_handle,property_index);
}
static inline HRESULT IVisualTreeService3_GetCollectionCount(IVisualTreeService3* This,InstanceHandle instance_handle,unsigned int *collection_size) {
    return This->lpVtbl->GetCollectionCount(This,instance_handle,collection_size);
}
static inline HRESULT IVisualTreeService3_GetCollectionElements(IVisualTreeService3* This,InstanceHandle instance_handle,unsigned int start_index,unsigned int *element_count,CollectionElementValue **element_values) {
    return This->lpVtbl->GetCollectionElements(This,instance_handle,start_index,element_count,element_values);
}
static inline HRESULT IVisualTreeService3_AddChild(IVisualTreeService3* This,InstanceHandle parent,InstanceHandle child,unsigned int index) {
    return This->lpVtbl->AddChild(This,parent,child,index);
}
static inline HRESULT IVisualTreeService3_RemoveChild(IVisualTreeService3* This,InstanceHandle parent,unsigned int index) {
    return This->lpVtbl->RemoveChild(This,parent,index);
}
static inline HRESULT IVisualTreeService3_ClearChildren(IVisualTreeService3* This,InstanceHandle parent) {
    return This->lpVtbl->ClearChildren(This,parent);
}
/*** IVisualTreeService2 methods ***/
static inline HRESULT IVisualTreeService3_GetPropertyIndex(IVisualTreeService3* This,InstanceHandle object,LPCWSTR property_name,unsigned int *property_index) {
    return This->lpVtbl->GetPropertyIndex(This,object,property_name,property_index);
}
static inline HRESULT IVisualTreeService3_GetProperty(IVisualTreeService3* This,InstanceHandle object,unsigned int property_index,InstanceHandle *value) {
    return This->lpVtbl->GetProperty(This,object,property_index,value);
}
static inline HRESULT IVisualTreeService3_ReplaceResource(IVisualTreeService3* This,InstanceHandle resource_dictionary,InstanceHandle key,InstanceHandle new_value) {
    return This->lpVtbl->ReplaceResource(This,resource_dictionary,key,new_value);
}
static inline HRESULT IVisualTreeService3_RenderTargetBitmap(IVisualTreeService3* This,InstanceHandle handle,RenderTargetBitmapOptions options,unsigned int max_pixel_width,unsigned int max_pixel_height,IBitmapData **bitmap_data) {
    return This->lpVtbl->RenderTargetBitmap(This,handle,options,max_pixel_width,max_pixel_height,bitmap_data);
}
/*** IVisualTreeService3 methods ***/
static inline HRESULT IVisualTreeService3_ResolveResource(IVisualTreeService3* This,InstanceHandle resource_context,LPCWSTR resource_name,ResourceType resource_type,unsigned int property_index) {
    return This->lpVtbl->ResolveResource(This,resource_context,resource_name,resource_type,property_index);
}
static inline HRESULT IVisualTreeService3_GetDictionaryItem(IVisualTreeService3* This,InstanceHandle dictionary_handle,LPCWSTR resource_name,WINBOOL resource_is_implicit_style,InstanceHandle *resource_handle) {
    return This->lpVtbl->GetDictionaryItem(This,dictionary_handle,resource_name,resource_is_implicit_style,resource_handle);
}
static inline HRESULT IVisualTreeService3_AddDictionaryItem(IVisualTreeService3* This,InstanceHandle dictionary_handle,InstanceHandle resource_key,InstanceHandle resource_handle) {
    return This->lpVtbl->AddDictionaryItem(This,dictionary_handle,resource_key,resource_handle);
}
static inline HRESULT IVisualTreeService3_RemoveDictionaryItem(IVisualTreeService3* This,InstanceHandle dictionary_handle,InstanceHandle resource_key) {
    return This->lpVtbl->RemoveDictionaryItem(This,dictionary_handle,resource_key);
}
#endif
#endif

#endif


#endif  /* __IVisualTreeService3_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __xamlom_h__ */
