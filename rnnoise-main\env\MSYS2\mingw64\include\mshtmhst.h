/*** Autogenerated by WIDL 10.12 from include/mshtmhst.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mshtmhst_h__
#define __mshtmhst_h__

/* Forward declarations */

#ifndef __IHostDialogHelper_FWD_DEFINED__
#define __IHostDialogHelper_FWD_DEFINED__
typedef interface IHostDialogHelper IHostDialogHelper;
#ifdef __cplusplus
interface IHostDialogHelper;
#endif /* __cplusplus */
#endif

#ifndef __HostDialogHelper_FWD_DEFINED__
#define __HostDialogHelper_FWD_DEFINED__
#ifdef __cplusplus
typedef class HostDialogHelper HostDialogHelper;
#else
typedef struct HostDialogHelper HostDialogHelper;
#endif /* defined __cplusplus */
#endif /* defined __HostDialogHelper_FWD_DEFINED__ */

#ifndef __IDocHostUIHandler_FWD_DEFINED__
#define __IDocHostUIHandler_FWD_DEFINED__
typedef interface IDocHostUIHandler IDocHostUIHandler;
#ifdef __cplusplus
interface IDocHostUIHandler;
#endif /* __cplusplus */
#endif

#ifndef __IDocHostUIHandler2_FWD_DEFINED__
#define __IDocHostUIHandler2_FWD_DEFINED__
typedef interface IDocHostUIHandler2 IDocHostUIHandler2;
#ifdef __cplusplus
interface IDocHostUIHandler2;
#endif /* __cplusplus */
#endif

#ifndef __ICustomDoc_FWD_DEFINED__
#define __ICustomDoc_FWD_DEFINED__
typedef interface ICustomDoc ICustomDoc;
#ifdef __cplusplus
interface ICustomDoc;
#endif /* __cplusplus */
#endif

#ifndef __IDocHostShowUI_FWD_DEFINED__
#define __IDocHostShowUI_FWD_DEFINED__
typedef interface IDocHostShowUI IDocHostShowUI;
#ifdef __cplusplus
interface IDocHostShowUI;
#endif /* __cplusplus */
#endif

#ifndef __IClassFactoryEx_FWD_DEFINED__
#define __IClassFactoryEx_FWD_DEFINED__
typedef interface IClassFactoryEx IClassFactoryEx;
#ifdef __cplusplus
interface IClassFactoryEx;
#endif /* __cplusplus */
#endif

#ifndef __IHTMLOMWindowServices_FWD_DEFINED__
#define __IHTMLOMWindowServices_FWD_DEFINED__
typedef interface IHTMLOMWindowServices IHTMLOMWindowServices;
#ifdef __cplusplus
interface IHTMLOMWindowServices;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <ocidl.h>
#include <objidl.h>
#include <oleidl.h>
#include <oaidl.h>
#include <docobj.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef MSHTMHST_H
#define MSHTMHST_H
#define CONTEXT_MENU_DEFAULT 0
#define CONTEXT_MENU_IMAGE 1
#define CONTEXT_MENU_CONTROL 2
#define CONTEXT_MENU_TABLE 3
#define CONTEXT_MENU_TEXTSELECT 4
#define CONTEXT_MENU_ANCHOR 5
#define CONTEXT_MENU_UNKNOWN 6
#define CONTEXT_MENU_IMGDYNSRC 7
#define CONTEXT_MENU_DEBUG 8
#define CONTEXT_MENU_VSCROLL 9
#define CONTEXT_MENU_HSCROLL 10
#define CONTEXT_MENU_MEDIA 11

#define MENUEXT_SHOWDIALOG 0x1

#define CMDID_SCRIPTSITE_URL 0
#define CMDID_SCRIPTSITE_HTMLDLGTRUST 1
#define CMDID_SCRIPTSITE_SECSTATE 2
#define CMDID_SCRIPTSITE_SID 3
#define CMDID_SCRIPTSITE_TRUSTEDDOC 4
#define CMDID_SCRIPTSITE_SECURITY_WINDOW 5
#define CMDID_SCRIPTSITE_NAMESPACE 6
#define CMDID_SCRIPTSITE_IURI 7
#define CMDID_HOSTCONTEXT_URL 8
#define CMDID_SCRIPTSITE_ALLOWRECOVERY 9

#define HTMLDLG_NOUI 0x10
#define HTMLDLG_MODAL 0x20
#define HTMLDLG_MODELESS 0x40
#define HTMLDLG_PRINT_TEMPLATE 0x80
#define HTMLDLG_VERIFY 0x100
#define HTMLDLG_ALLOW_UNKNOWN_THREAD 0x200

#define PRINT_DONTBOTHERUSER 0x1
#define PRINT_WAITFORCOMPLETION 0x2
#define CMDSETID_Forms3 CGID_MSHTML
#define SZ_HTML_CLIENTSITE_OBJECTPARAM L"{d4db6850-5385-11d0-89e9-00a0c90a90ac}"
EXTERN_C const GUID CGID_ScriptSite;
EXTERN_C const GUID CGID_MSHTML;
EXTERN_C const GUID CLSID_HostDialogHelper;
DEFINE_GUID(CGID_DocHostCommandHandler,0xf38bc242,0xb950,0x11d1,0x89,0x18,0x00,0xc0,0x4f,0xc2,0xc8,0x36);
#ifndef __IHTMLWindow2_FWD_DEFINED__
#define __IHTMLWindow2_FWD_DEFINED__
typedef interface IHTMLWindow2 IHTMLWindow2;
#endif

typedef HRESULT STDAPICALLTYPE SHOWHTMLDIALOGFN (HWND hwndParent, IMoniker *pmk, VARIANT *pvarArgIn, WCHAR *pchOptions, VARIANT *pvArgOut);
typedef HRESULT STDAPICALLTYPE SHOWHTMLDIALOGEXFN (HWND hwndParent, IMoniker *pmk, DWORD dwDialogFlags, VARIANT *pvarArgIn, WCHAR *pchOptions, VARIANT *pvArgOut);
typedef HRESULT STDAPICALLTYPE SHOWMODELESSHTMLDIALOGFN (HWND hwndParent, IMoniker *pmk, VARIANT *pvarArgIn, VARIANT *pvarOptions, IHTMLWindow2 **ppWindow);
typedef HRESULT STDAPICALLTYPE IEREGISTERXMLNSFN (LPCWSTR lpszURI, GUID clsid, BOOL fMachine);
typedef HRESULT STDAPICALLTYPE IEISXMLNSREGISTEREDFN (LPCWSTR lpszURI, GUID *pCLSID);

STDAPI ShowHTMLDialog (HWND hwndParent, IMoniker *pMk, VARIANT *pvarArgIn, LPWSTR pchOptions, VARIANT *pvarArgOut);
STDAPI ShowHTMLDialogEx (HWND hwndParent, IMoniker *pMk, DWORD dwDialogFlags, VARIANT *pvarArgIn, LPWSTR pchOptions, VARIANT *pvarArgOut);
STDAPI ShowModelessHTMLDialog (HWND hwndParent, IMoniker *pMk, VARIANT *pvarArgIn, VARIANT *pvarOptions, IHTMLWindow2 **ppWindow);
#if !defined (_ARM_) && !defined (__arm__)
STDAPI RunHTMLApplication (HINSTANCE hinst, HINSTANCE hPrevInst, LPSTR szCmdLine, int nCmdShow);
#endif
STDAPI CreateHTMLPropertyPage (IMoniker *pmk, IPropertyPage **ppPP);
STDAPI EarlyStartDisplaySystem (void);
STDAPI IERegisterXMLNS (LPCWSTR lpszURI, GUID clsid, BOOL fMachine);
STDAPI IEIsXMLNSRegistered (LPCWSTR lpszURI, GUID *pCLSID);
STDAPI GetColorValueFromString (LPCWSTR lpszColor, BOOL fStrictCSS1, BOOL fIsStandardsCSS, COLORREF *pColor);
typedef enum tagDOCHOSTUIDBLCLK {
    DOCHOSTUIDBLCLK_DEFAULT = 0,
    DOCHOSTUIDBLCLK_SHOWPROPERTIES = 1,
    DOCHOSTUIDBLCLK_SHOWCODE = 2
} DOCHOSTUIDBLCLK;
typedef enum tagDOCHOSTUIFLAG {
    DOCHOSTUIFLAG_DIALOG = 0x1,
    DOCHOSTUIFLAG_DISABLE_HELP_MENU = 0x2,
    DOCHOSTUIFLAG_NO3DBORDER = 0x4,
    DOCHOSTUIFLAG_SCROLL_NO = 0x8,
    DOCHOSTUIFLAG_DISABLE_SCRIPT_INACTIVE = 0x10,
    DOCHOSTUIFLAG_OPENNEWWIN = 0x20,
    DOCHOSTUIFLAG_DISABLE_OFFSCREEN = 0x40,
    DOCHOSTUIFLAG_FLAT_SCROLLBAR = 0x80,
    DOCHOSTUIFLAG_DIV_BLOCKDEFAULT = 0x100,
    DOCHOSTUIFLAG_ACTIVATE_CLIENTHIT_ONLY = 0x200,
    DOCHOSTUIFLAG_OVERRIDEBEHAVIORFACTORY = 0x400,
    DOCHOSTUIFLAG_CODEPAGELINKEDFONTS = 0x800,
    DOCHOSTUIFLAG_URL_ENCODING_DISABLE_UTF8 = 0x1000,
    DOCHOSTUIFLAG_URL_ENCODING_ENABLE_UTF8 = 0x2000,
    DOCHOSTUIFLAG_ENABLE_FORMS_AUTOCOMPLETE = 0x4000,
    DOCHOSTUIFLAG_ENABLE_INPLACE_NAVIGATION = 0x10000,
    DOCHOSTUIFLAG_IME_ENABLE_RECONVERSION = 0x20000,
    DOCHOSTUIFLAG_THEME = 0x40000,
    DOCHOSTUIFLAG_NOTHEME = 0x80000,
    DOCHOSTUIFLAG_NOPICS = 0x100000,
    DOCHOSTUIFLAG_NO3DOUTERBORDER = 0x200000,
    DOCHOSTUIFLAG_DISABLE_EDIT_NS_FIXUP = 0x400000,
    DOCHOSTUIFLAG_LOCAL_MACHINE_ACCESS_CHECK = 0x800000,
    DOCHOSTUIFLAG_DISABLE_UNTRUSTEDPROTOCOL = 0x1000000,
    DOCHOSTUIFLAG_HOST_NAVIGATES = 0x2000000,
    DOCHOSTUIFLAG_ENABLE_REDIRECT_NOTIFICATION = 0x4000000,
    DOCHOSTUIFLAG_USE_WINDOWLESS_SELECTCONTROL = 0x8000000,
    DOCHOSTUIFLAG_USE_WINDOWED_SELECTCONTROL = 0x10000000,
    DOCHOSTUIFLAG_ENABLE_ACTIVEX_INACTIVATE_MODE = 0x20000000,
    DOCHOSTUIFLAG_DPI_AWARE = 0x40000000
} DOCHOSTUIFLAG;
#define DOCHOSTUIFLAG_BROWSER (DOCHOSTUIFLAG_DISABLE_HELP_MENU | DOCHOSTUIFLAG_DISABLE_SCRIPT_INACTIVE)
typedef enum tagDOCHOSTUITYPE {
    DOCHOSTUITYPE_BROWSE = 0,
    DOCHOSTUITYPE_AUTHOR = 1
} DOCHOSTUITYPE;
typedef struct _DOCHOSTUIINFO {
    ULONG cbSize;
    DWORD dwFlags;
    DWORD dwDoubleClick;
    OLECHAR *pchHostCss;
    OLECHAR *pchHostNS;
} DOCHOSTUIINFO;
/*****************************************************************************
 * IHostDialogHelper interface
 */
#ifndef __IHostDialogHelper_INTERFACE_DEFINED__
#define __IHostDialogHelper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IHostDialogHelper, 0x53dec138, 0xa51e, 0x11d2, 0x86,0x1e, 0x00,0xc0,0x4f,0xa3,0x5c,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("53dec138-a51e-11d2-861e-00c04fa35c89")
IHostDialogHelper : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ShowHTMLDialog(
        HWND hwndParent,
        IMoniker *pMk,
        VARIANT *pvarArgIn,
        WCHAR *pchOptions,
        VARIANT *pvarArgOut,
        IUnknown *punkHost) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IHostDialogHelper, 0x53dec138, 0xa51e, 0x11d2, 0x86,0x1e, 0x00,0xc0,0x4f,0xa3,0x5c,0x89)
#endif
#else
typedef struct IHostDialogHelperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IHostDialogHelper *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IHostDialogHelper *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IHostDialogHelper *This);

    /*** IHostDialogHelper methods ***/
    HRESULT (STDMETHODCALLTYPE *ShowHTMLDialog)(
        IHostDialogHelper *This,
        HWND hwndParent,
        IMoniker *pMk,
        VARIANT *pvarArgIn,
        WCHAR *pchOptions,
        VARIANT *pvarArgOut,
        IUnknown *punkHost);

    END_INTERFACE
} IHostDialogHelperVtbl;

interface IHostDialogHelper {
    CONST_VTBL IHostDialogHelperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IHostDialogHelper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHostDialogHelper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHostDialogHelper_Release(This) (This)->lpVtbl->Release(This)
/*** IHostDialogHelper methods ***/
#define IHostDialogHelper_ShowHTMLDialog(This,hwndParent,pMk,pvarArgIn,pchOptions,pvarArgOut,punkHost) (This)->lpVtbl->ShowHTMLDialog(This,hwndParent,pMk,pvarArgIn,pchOptions,pvarArgOut,punkHost)
#else
/*** IUnknown methods ***/
static inline HRESULT IHostDialogHelper_QueryInterface(IHostDialogHelper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IHostDialogHelper_AddRef(IHostDialogHelper* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IHostDialogHelper_Release(IHostDialogHelper* This) {
    return This->lpVtbl->Release(This);
}
/*** IHostDialogHelper methods ***/
static inline HRESULT IHostDialogHelper_ShowHTMLDialog(IHostDialogHelper* This,HWND hwndParent,IMoniker *pMk,VARIANT *pvarArgIn,WCHAR *pchOptions,VARIANT *pvarArgOut,IUnknown *punkHost) {
    return This->lpVtbl->ShowHTMLDialog(This,hwndParent,pMk,pvarArgIn,pchOptions,pvarArgOut,punkHost);
}
#endif
#endif

#endif


#endif  /* __IHostDialogHelper_INTERFACE_DEFINED__ */

/*****************************************************************************
 * HostDialogHelper coclass
 */

DEFINE_GUID(CLSID_HostDialogHelper, 0x429af92c, 0xa51f, 0x11d2, 0x86,0x1e, 0x00,0xc0,0x4f,0xa3,0x5c,0x89);

#ifdef __cplusplus
class DECLSPEC_UUID("429af92c-a51f-11d2-861e-00c04fa35c89") HostDialogHelper;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(HostDialogHelper, 0x429af92c, 0xa51f, 0x11d2, 0x86,0x1e, 0x00,0xc0,0x4f,0xa3,0x5c,0x89)
#endif
#endif

/*****************************************************************************
 * IDocHostUIHandler interface
 */
#ifndef __IDocHostUIHandler_INTERFACE_DEFINED__
#define __IDocHostUIHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDocHostUIHandler, 0xbd3f23c0, 0xd43e, 0x11cf, 0x89,0x3b, 0x00,0xaa,0x00,0xbd,0xce,0x1a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bd3f23c0-d43e-11cf-893b-00aa00bdce1a")
IDocHostUIHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ShowContextMenu(
        DWORD dwID,
        POINT *ppt,
        IUnknown *pcmdtReserved,
        IDispatch *pdispReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHostInfo(
        DOCHOSTUIINFO *pInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowUI(
        DWORD dwID,
        IOleInPlaceActiveObject *pActiveObject,
        IOleCommandTarget *pCommandTarget,
        IOleInPlaceFrame *pFrame,
        IOleInPlaceUIWindow *pDoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE HideUI(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateUI(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableModeless(
        WINBOOL fEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDocWindowActivate(
        WINBOOL fActivate) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFrameWindowActivate(
        WINBOOL fActivate) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResizeBorder(
        LPCRECT prcBorder,
        IOleInPlaceUIWindow *pUIWindow,
        WINBOOL fRameWindow) = 0;

    virtual HRESULT STDMETHODCALLTYPE TranslateAccelerator(
        LPMSG lpMsg,
        const GUID *pguidCmdGroup,
        DWORD nCmdID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOptionKeyPath(
        LPOLESTR *pchKey,
        DWORD dw) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDropTarget(
        IDropTarget *pDropTarget,
        IDropTarget **ppDropTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExternal(
        IDispatch **ppDispatch) = 0;

    virtual HRESULT STDMETHODCALLTYPE TranslateUrl(
        DWORD dwTranslate,
        LPWSTR pchURLIn,
        LPWSTR *ppchURLOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE FilterDataObject(
        IDataObject *pDO,
        IDataObject **ppDORet) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDocHostUIHandler, 0xbd3f23c0, 0xd43e, 0x11cf, 0x89,0x3b, 0x00,0xaa,0x00,0xbd,0xce,0x1a)
#endif
#else
typedef struct IDocHostUIHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDocHostUIHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDocHostUIHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDocHostUIHandler *This);

    /*** IDocHostUIHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *ShowContextMenu)(
        IDocHostUIHandler *This,
        DWORD dwID,
        POINT *ppt,
        IUnknown *pcmdtReserved,
        IDispatch *pdispReserved);

    HRESULT (STDMETHODCALLTYPE *GetHostInfo)(
        IDocHostUIHandler *This,
        DOCHOSTUIINFO *pInfo);

    HRESULT (STDMETHODCALLTYPE *ShowUI)(
        IDocHostUIHandler *This,
        DWORD dwID,
        IOleInPlaceActiveObject *pActiveObject,
        IOleCommandTarget *pCommandTarget,
        IOleInPlaceFrame *pFrame,
        IOleInPlaceUIWindow *pDoc);

    HRESULT (STDMETHODCALLTYPE *HideUI)(
        IDocHostUIHandler *This);

    HRESULT (STDMETHODCALLTYPE *UpdateUI)(
        IDocHostUIHandler *This);

    HRESULT (STDMETHODCALLTYPE *EnableModeless)(
        IDocHostUIHandler *This,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *OnDocWindowActivate)(
        IDocHostUIHandler *This,
        WINBOOL fActivate);

    HRESULT (STDMETHODCALLTYPE *OnFrameWindowActivate)(
        IDocHostUIHandler *This,
        WINBOOL fActivate);

    HRESULT (STDMETHODCALLTYPE *ResizeBorder)(
        IDocHostUIHandler *This,
        LPCRECT prcBorder,
        IOleInPlaceUIWindow *pUIWindow,
        WINBOOL fRameWindow);

    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IDocHostUIHandler *This,
        LPMSG lpMsg,
        const GUID *pguidCmdGroup,
        DWORD nCmdID);

    HRESULT (STDMETHODCALLTYPE *GetOptionKeyPath)(
        IDocHostUIHandler *This,
        LPOLESTR *pchKey,
        DWORD dw);

    HRESULT (STDMETHODCALLTYPE *GetDropTarget)(
        IDocHostUIHandler *This,
        IDropTarget *pDropTarget,
        IDropTarget **ppDropTarget);

    HRESULT (STDMETHODCALLTYPE *GetExternal)(
        IDocHostUIHandler *This,
        IDispatch **ppDispatch);

    HRESULT (STDMETHODCALLTYPE *TranslateUrl)(
        IDocHostUIHandler *This,
        DWORD dwTranslate,
        LPWSTR pchURLIn,
        LPWSTR *ppchURLOut);

    HRESULT (STDMETHODCALLTYPE *FilterDataObject)(
        IDocHostUIHandler *This,
        IDataObject *pDO,
        IDataObject **ppDORet);

    END_INTERFACE
} IDocHostUIHandlerVtbl;

interface IDocHostUIHandler {
    CONST_VTBL IDocHostUIHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDocHostUIHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDocHostUIHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDocHostUIHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IDocHostUIHandler methods ***/
#define IDocHostUIHandler_ShowContextMenu(This,dwID,ppt,pcmdtReserved,pdispReserved) (This)->lpVtbl->ShowContextMenu(This,dwID,ppt,pcmdtReserved,pdispReserved)
#define IDocHostUIHandler_GetHostInfo(This,pInfo) (This)->lpVtbl->GetHostInfo(This,pInfo)
#define IDocHostUIHandler_ShowUI(This,dwID,pActiveObject,pCommandTarget,pFrame,pDoc) (This)->lpVtbl->ShowUI(This,dwID,pActiveObject,pCommandTarget,pFrame,pDoc)
#define IDocHostUIHandler_HideUI(This) (This)->lpVtbl->HideUI(This)
#define IDocHostUIHandler_UpdateUI(This) (This)->lpVtbl->UpdateUI(This)
#define IDocHostUIHandler_EnableModeless(This,fEnable) (This)->lpVtbl->EnableModeless(This,fEnable)
#define IDocHostUIHandler_OnDocWindowActivate(This,fActivate) (This)->lpVtbl->OnDocWindowActivate(This,fActivate)
#define IDocHostUIHandler_OnFrameWindowActivate(This,fActivate) (This)->lpVtbl->OnFrameWindowActivate(This,fActivate)
#define IDocHostUIHandler_ResizeBorder(This,prcBorder,pUIWindow,fRameWindow) (This)->lpVtbl->ResizeBorder(This,prcBorder,pUIWindow,fRameWindow)
#define IDocHostUIHandler_TranslateAccelerator(This,lpMsg,pguidCmdGroup,nCmdID) (This)->lpVtbl->TranslateAccelerator(This,lpMsg,pguidCmdGroup,nCmdID)
#define IDocHostUIHandler_GetOptionKeyPath(This,pchKey,dw) (This)->lpVtbl->GetOptionKeyPath(This,pchKey,dw)
#define IDocHostUIHandler_GetDropTarget(This,pDropTarget,ppDropTarget) (This)->lpVtbl->GetDropTarget(This,pDropTarget,ppDropTarget)
#define IDocHostUIHandler_GetExternal(This,ppDispatch) (This)->lpVtbl->GetExternal(This,ppDispatch)
#define IDocHostUIHandler_TranslateUrl(This,dwTranslate,pchURLIn,ppchURLOut) (This)->lpVtbl->TranslateUrl(This,dwTranslate,pchURLIn,ppchURLOut)
#define IDocHostUIHandler_FilterDataObject(This,pDO,ppDORet) (This)->lpVtbl->FilterDataObject(This,pDO,ppDORet)
#else
/*** IUnknown methods ***/
static inline HRESULT IDocHostUIHandler_QueryInterface(IDocHostUIHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDocHostUIHandler_AddRef(IDocHostUIHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDocHostUIHandler_Release(IDocHostUIHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IDocHostUIHandler methods ***/
static inline HRESULT IDocHostUIHandler_ShowContextMenu(IDocHostUIHandler* This,DWORD dwID,POINT *ppt,IUnknown *pcmdtReserved,IDispatch *pdispReserved) {
    return This->lpVtbl->ShowContextMenu(This,dwID,ppt,pcmdtReserved,pdispReserved);
}
static inline HRESULT IDocHostUIHandler_GetHostInfo(IDocHostUIHandler* This,DOCHOSTUIINFO *pInfo) {
    return This->lpVtbl->GetHostInfo(This,pInfo);
}
static inline HRESULT IDocHostUIHandler_ShowUI(IDocHostUIHandler* This,DWORD dwID,IOleInPlaceActiveObject *pActiveObject,IOleCommandTarget *pCommandTarget,IOleInPlaceFrame *pFrame,IOleInPlaceUIWindow *pDoc) {
    return This->lpVtbl->ShowUI(This,dwID,pActiveObject,pCommandTarget,pFrame,pDoc);
}
static inline HRESULT IDocHostUIHandler_HideUI(IDocHostUIHandler* This) {
    return This->lpVtbl->HideUI(This);
}
static inline HRESULT IDocHostUIHandler_UpdateUI(IDocHostUIHandler* This) {
    return This->lpVtbl->UpdateUI(This);
}
static inline HRESULT IDocHostUIHandler_EnableModeless(IDocHostUIHandler* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableModeless(This,fEnable);
}
static inline HRESULT IDocHostUIHandler_OnDocWindowActivate(IDocHostUIHandler* This,WINBOOL fActivate) {
    return This->lpVtbl->OnDocWindowActivate(This,fActivate);
}
static inline HRESULT IDocHostUIHandler_OnFrameWindowActivate(IDocHostUIHandler* This,WINBOOL fActivate) {
    return This->lpVtbl->OnFrameWindowActivate(This,fActivate);
}
static inline HRESULT IDocHostUIHandler_ResizeBorder(IDocHostUIHandler* This,LPCRECT prcBorder,IOleInPlaceUIWindow *pUIWindow,WINBOOL fRameWindow) {
    return This->lpVtbl->ResizeBorder(This,prcBorder,pUIWindow,fRameWindow);
}
static inline HRESULT IDocHostUIHandler_TranslateAccelerator(IDocHostUIHandler* This,LPMSG lpMsg,const GUID *pguidCmdGroup,DWORD nCmdID) {
    return This->lpVtbl->TranslateAccelerator(This,lpMsg,pguidCmdGroup,nCmdID);
}
static inline HRESULT IDocHostUIHandler_GetOptionKeyPath(IDocHostUIHandler* This,LPOLESTR *pchKey,DWORD dw) {
    return This->lpVtbl->GetOptionKeyPath(This,pchKey,dw);
}
static inline HRESULT IDocHostUIHandler_GetDropTarget(IDocHostUIHandler* This,IDropTarget *pDropTarget,IDropTarget **ppDropTarget) {
    return This->lpVtbl->GetDropTarget(This,pDropTarget,ppDropTarget);
}
static inline HRESULT IDocHostUIHandler_GetExternal(IDocHostUIHandler* This,IDispatch **ppDispatch) {
    return This->lpVtbl->GetExternal(This,ppDispatch);
}
static inline HRESULT IDocHostUIHandler_TranslateUrl(IDocHostUIHandler* This,DWORD dwTranslate,LPWSTR pchURLIn,LPWSTR *ppchURLOut) {
    return This->lpVtbl->TranslateUrl(This,dwTranslate,pchURLIn,ppchURLOut);
}
static inline HRESULT IDocHostUIHandler_FilterDataObject(IDocHostUIHandler* This,IDataObject *pDO,IDataObject **ppDORet) {
    return This->lpVtbl->FilterDataObject(This,pDO,ppDORet);
}
#endif
#endif

#endif


#endif  /* __IDocHostUIHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDocHostUIHandler2 interface
 */
#ifndef __IDocHostUIHandler2_INTERFACE_DEFINED__
#define __IDocHostUIHandler2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDocHostUIHandler2, 0x3050f6d0, 0x98b5, 0x11cf, 0xbb,0x82, 0x00,0xaa,0x00,0xbd,0xce,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3050f6d0-98b5-11cf-bb82-00aa00bdce0b")
IDocHostUIHandler2 : public IDocHostUIHandler
{
    virtual HRESULT STDMETHODCALLTYPE GetOverrideKeyPath(
        LPOLESTR *pchKey,
        DWORD dw) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDocHostUIHandler2, 0x3050f6d0, 0x98b5, 0x11cf, 0xbb,0x82, 0x00,0xaa,0x00,0xbd,0xce,0x0b)
#endif
#else
typedef struct IDocHostUIHandler2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDocHostUIHandler2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDocHostUIHandler2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDocHostUIHandler2 *This);

    /*** IDocHostUIHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *ShowContextMenu)(
        IDocHostUIHandler2 *This,
        DWORD dwID,
        POINT *ppt,
        IUnknown *pcmdtReserved,
        IDispatch *pdispReserved);

    HRESULT (STDMETHODCALLTYPE *GetHostInfo)(
        IDocHostUIHandler2 *This,
        DOCHOSTUIINFO *pInfo);

    HRESULT (STDMETHODCALLTYPE *ShowUI)(
        IDocHostUIHandler2 *This,
        DWORD dwID,
        IOleInPlaceActiveObject *pActiveObject,
        IOleCommandTarget *pCommandTarget,
        IOleInPlaceFrame *pFrame,
        IOleInPlaceUIWindow *pDoc);

    HRESULT (STDMETHODCALLTYPE *HideUI)(
        IDocHostUIHandler2 *This);

    HRESULT (STDMETHODCALLTYPE *UpdateUI)(
        IDocHostUIHandler2 *This);

    HRESULT (STDMETHODCALLTYPE *EnableModeless)(
        IDocHostUIHandler2 *This,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *OnDocWindowActivate)(
        IDocHostUIHandler2 *This,
        WINBOOL fActivate);

    HRESULT (STDMETHODCALLTYPE *OnFrameWindowActivate)(
        IDocHostUIHandler2 *This,
        WINBOOL fActivate);

    HRESULT (STDMETHODCALLTYPE *ResizeBorder)(
        IDocHostUIHandler2 *This,
        LPCRECT prcBorder,
        IOleInPlaceUIWindow *pUIWindow,
        WINBOOL fRameWindow);

    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IDocHostUIHandler2 *This,
        LPMSG lpMsg,
        const GUID *pguidCmdGroup,
        DWORD nCmdID);

    HRESULT (STDMETHODCALLTYPE *GetOptionKeyPath)(
        IDocHostUIHandler2 *This,
        LPOLESTR *pchKey,
        DWORD dw);

    HRESULT (STDMETHODCALLTYPE *GetDropTarget)(
        IDocHostUIHandler2 *This,
        IDropTarget *pDropTarget,
        IDropTarget **ppDropTarget);

    HRESULT (STDMETHODCALLTYPE *GetExternal)(
        IDocHostUIHandler2 *This,
        IDispatch **ppDispatch);

    HRESULT (STDMETHODCALLTYPE *TranslateUrl)(
        IDocHostUIHandler2 *This,
        DWORD dwTranslate,
        LPWSTR pchURLIn,
        LPWSTR *ppchURLOut);

    HRESULT (STDMETHODCALLTYPE *FilterDataObject)(
        IDocHostUIHandler2 *This,
        IDataObject *pDO,
        IDataObject **ppDORet);

    /*** IDocHostUIHandler2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOverrideKeyPath)(
        IDocHostUIHandler2 *This,
        LPOLESTR *pchKey,
        DWORD dw);

    END_INTERFACE
} IDocHostUIHandler2Vtbl;

interface IDocHostUIHandler2 {
    CONST_VTBL IDocHostUIHandler2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDocHostUIHandler2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDocHostUIHandler2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDocHostUIHandler2_Release(This) (This)->lpVtbl->Release(This)
/*** IDocHostUIHandler methods ***/
#define IDocHostUIHandler2_ShowContextMenu(This,dwID,ppt,pcmdtReserved,pdispReserved) (This)->lpVtbl->ShowContextMenu(This,dwID,ppt,pcmdtReserved,pdispReserved)
#define IDocHostUIHandler2_GetHostInfo(This,pInfo) (This)->lpVtbl->GetHostInfo(This,pInfo)
#define IDocHostUIHandler2_ShowUI(This,dwID,pActiveObject,pCommandTarget,pFrame,pDoc) (This)->lpVtbl->ShowUI(This,dwID,pActiveObject,pCommandTarget,pFrame,pDoc)
#define IDocHostUIHandler2_HideUI(This) (This)->lpVtbl->HideUI(This)
#define IDocHostUIHandler2_UpdateUI(This) (This)->lpVtbl->UpdateUI(This)
#define IDocHostUIHandler2_EnableModeless(This,fEnable) (This)->lpVtbl->EnableModeless(This,fEnable)
#define IDocHostUIHandler2_OnDocWindowActivate(This,fActivate) (This)->lpVtbl->OnDocWindowActivate(This,fActivate)
#define IDocHostUIHandler2_OnFrameWindowActivate(This,fActivate) (This)->lpVtbl->OnFrameWindowActivate(This,fActivate)
#define IDocHostUIHandler2_ResizeBorder(This,prcBorder,pUIWindow,fRameWindow) (This)->lpVtbl->ResizeBorder(This,prcBorder,pUIWindow,fRameWindow)
#define IDocHostUIHandler2_TranslateAccelerator(This,lpMsg,pguidCmdGroup,nCmdID) (This)->lpVtbl->TranslateAccelerator(This,lpMsg,pguidCmdGroup,nCmdID)
#define IDocHostUIHandler2_GetOptionKeyPath(This,pchKey,dw) (This)->lpVtbl->GetOptionKeyPath(This,pchKey,dw)
#define IDocHostUIHandler2_GetDropTarget(This,pDropTarget,ppDropTarget) (This)->lpVtbl->GetDropTarget(This,pDropTarget,ppDropTarget)
#define IDocHostUIHandler2_GetExternal(This,ppDispatch) (This)->lpVtbl->GetExternal(This,ppDispatch)
#define IDocHostUIHandler2_TranslateUrl(This,dwTranslate,pchURLIn,ppchURLOut) (This)->lpVtbl->TranslateUrl(This,dwTranslate,pchURLIn,ppchURLOut)
#define IDocHostUIHandler2_FilterDataObject(This,pDO,ppDORet) (This)->lpVtbl->FilterDataObject(This,pDO,ppDORet)
/*** IDocHostUIHandler2 methods ***/
#define IDocHostUIHandler2_GetOverrideKeyPath(This,pchKey,dw) (This)->lpVtbl->GetOverrideKeyPath(This,pchKey,dw)
#else
/*** IUnknown methods ***/
static inline HRESULT IDocHostUIHandler2_QueryInterface(IDocHostUIHandler2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDocHostUIHandler2_AddRef(IDocHostUIHandler2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDocHostUIHandler2_Release(IDocHostUIHandler2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDocHostUIHandler methods ***/
static inline HRESULT IDocHostUIHandler2_ShowContextMenu(IDocHostUIHandler2* This,DWORD dwID,POINT *ppt,IUnknown *pcmdtReserved,IDispatch *pdispReserved) {
    return This->lpVtbl->ShowContextMenu(This,dwID,ppt,pcmdtReserved,pdispReserved);
}
static inline HRESULT IDocHostUIHandler2_GetHostInfo(IDocHostUIHandler2* This,DOCHOSTUIINFO *pInfo) {
    return This->lpVtbl->GetHostInfo(This,pInfo);
}
static inline HRESULT IDocHostUIHandler2_ShowUI(IDocHostUIHandler2* This,DWORD dwID,IOleInPlaceActiveObject *pActiveObject,IOleCommandTarget *pCommandTarget,IOleInPlaceFrame *pFrame,IOleInPlaceUIWindow *pDoc) {
    return This->lpVtbl->ShowUI(This,dwID,pActiveObject,pCommandTarget,pFrame,pDoc);
}
static inline HRESULT IDocHostUIHandler2_HideUI(IDocHostUIHandler2* This) {
    return This->lpVtbl->HideUI(This);
}
static inline HRESULT IDocHostUIHandler2_UpdateUI(IDocHostUIHandler2* This) {
    return This->lpVtbl->UpdateUI(This);
}
static inline HRESULT IDocHostUIHandler2_EnableModeless(IDocHostUIHandler2* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableModeless(This,fEnable);
}
static inline HRESULT IDocHostUIHandler2_OnDocWindowActivate(IDocHostUIHandler2* This,WINBOOL fActivate) {
    return This->lpVtbl->OnDocWindowActivate(This,fActivate);
}
static inline HRESULT IDocHostUIHandler2_OnFrameWindowActivate(IDocHostUIHandler2* This,WINBOOL fActivate) {
    return This->lpVtbl->OnFrameWindowActivate(This,fActivate);
}
static inline HRESULT IDocHostUIHandler2_ResizeBorder(IDocHostUIHandler2* This,LPCRECT prcBorder,IOleInPlaceUIWindow *pUIWindow,WINBOOL fRameWindow) {
    return This->lpVtbl->ResizeBorder(This,prcBorder,pUIWindow,fRameWindow);
}
static inline HRESULT IDocHostUIHandler2_TranslateAccelerator(IDocHostUIHandler2* This,LPMSG lpMsg,const GUID *pguidCmdGroup,DWORD nCmdID) {
    return This->lpVtbl->TranslateAccelerator(This,lpMsg,pguidCmdGroup,nCmdID);
}
static inline HRESULT IDocHostUIHandler2_GetOptionKeyPath(IDocHostUIHandler2* This,LPOLESTR *pchKey,DWORD dw) {
    return This->lpVtbl->GetOptionKeyPath(This,pchKey,dw);
}
static inline HRESULT IDocHostUIHandler2_GetDropTarget(IDocHostUIHandler2* This,IDropTarget *pDropTarget,IDropTarget **ppDropTarget) {
    return This->lpVtbl->GetDropTarget(This,pDropTarget,ppDropTarget);
}
static inline HRESULT IDocHostUIHandler2_GetExternal(IDocHostUIHandler2* This,IDispatch **ppDispatch) {
    return This->lpVtbl->GetExternal(This,ppDispatch);
}
static inline HRESULT IDocHostUIHandler2_TranslateUrl(IDocHostUIHandler2* This,DWORD dwTranslate,LPWSTR pchURLIn,LPWSTR *ppchURLOut) {
    return This->lpVtbl->TranslateUrl(This,dwTranslate,pchURLIn,ppchURLOut);
}
static inline HRESULT IDocHostUIHandler2_FilterDataObject(IDocHostUIHandler2* This,IDataObject *pDO,IDataObject **ppDORet) {
    return This->lpVtbl->FilterDataObject(This,pDO,ppDORet);
}
/*** IDocHostUIHandler2 methods ***/
static inline HRESULT IDocHostUIHandler2_GetOverrideKeyPath(IDocHostUIHandler2* This,LPOLESTR *pchKey,DWORD dw) {
    return This->lpVtbl->GetOverrideKeyPath(This,pchKey,dw);
}
#endif
#endif

#endif


#endif  /* __IDocHostUIHandler2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICustomDoc interface
 */
#ifndef __ICustomDoc_INTERFACE_DEFINED__
#define __ICustomDoc_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICustomDoc, 0x3050f3f0, 0x98b5, 0x11cf, 0xbb,0x82, 0x00,0xaa,0x00,0xbd,0xce,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3050f3f0-98b5-11cf-bb82-00aa00bdce0b")
ICustomDoc : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetUIHandler(
        IDocHostUIHandler *pUIHandler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICustomDoc, 0x3050f3f0, 0x98b5, 0x11cf, 0xbb,0x82, 0x00,0xaa,0x00,0xbd,0xce,0x0b)
#endif
#else
typedef struct ICustomDocVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICustomDoc *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICustomDoc *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICustomDoc *This);

    /*** ICustomDoc methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUIHandler)(
        ICustomDoc *This,
        IDocHostUIHandler *pUIHandler);

    END_INTERFACE
} ICustomDocVtbl;

interface ICustomDoc {
    CONST_VTBL ICustomDocVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICustomDoc_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICustomDoc_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICustomDoc_Release(This) (This)->lpVtbl->Release(This)
/*** ICustomDoc methods ***/
#define ICustomDoc_SetUIHandler(This,pUIHandler) (This)->lpVtbl->SetUIHandler(This,pUIHandler)
#else
/*** IUnknown methods ***/
static inline HRESULT ICustomDoc_QueryInterface(ICustomDoc* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICustomDoc_AddRef(ICustomDoc* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICustomDoc_Release(ICustomDoc* This) {
    return This->lpVtbl->Release(This);
}
/*** ICustomDoc methods ***/
static inline HRESULT ICustomDoc_SetUIHandler(ICustomDoc* This,IDocHostUIHandler *pUIHandler) {
    return This->lpVtbl->SetUIHandler(This,pUIHandler);
}
#endif
#endif

#endif


#endif  /* __ICustomDoc_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDocHostShowUI interface
 */
#ifndef __IDocHostShowUI_INTERFACE_DEFINED__
#define __IDocHostShowUI_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDocHostShowUI, 0xc4d244b0, 0xd43e, 0x11cf, 0x89,0x3b, 0x00,0xaa,0x00,0xbd,0xce,0x1a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c4d244b0-d43e-11cf-893b-00aa00bdce1a")
IDocHostShowUI : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ShowMessage(
        HWND hwnd,
        LPOLESTR lpstrText,
        LPOLESTR lpstrCaption,
        DWORD dwType,
        LPOLESTR lpstrHelpFile,
        DWORD dwHelpContext,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowHelp(
        HWND hwnd,
        LPOLESTR pszHelpFile,
        UINT uCommand,
        DWORD dwData,
        POINT ptMouse,
        IDispatch *pDispatchObjectHit) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDocHostShowUI, 0xc4d244b0, 0xd43e, 0x11cf, 0x89,0x3b, 0x00,0xaa,0x00,0xbd,0xce,0x1a)
#endif
#else
typedef struct IDocHostShowUIVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDocHostShowUI *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDocHostShowUI *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDocHostShowUI *This);

    /*** IDocHostShowUI methods ***/
    HRESULT (STDMETHODCALLTYPE *ShowMessage)(
        IDocHostShowUI *This,
        HWND hwnd,
        LPOLESTR lpstrText,
        LPOLESTR lpstrCaption,
        DWORD dwType,
        LPOLESTR lpstrHelpFile,
        DWORD dwHelpContext,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *ShowHelp)(
        IDocHostShowUI *This,
        HWND hwnd,
        LPOLESTR pszHelpFile,
        UINT uCommand,
        DWORD dwData,
        POINT ptMouse,
        IDispatch *pDispatchObjectHit);

    END_INTERFACE
} IDocHostShowUIVtbl;

interface IDocHostShowUI {
    CONST_VTBL IDocHostShowUIVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDocHostShowUI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDocHostShowUI_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDocHostShowUI_Release(This) (This)->lpVtbl->Release(This)
/*** IDocHostShowUI methods ***/
#define IDocHostShowUI_ShowMessage(This,hwnd,lpstrText,lpstrCaption,dwType,lpstrHelpFile,dwHelpContext,plResult) (This)->lpVtbl->ShowMessage(This,hwnd,lpstrText,lpstrCaption,dwType,lpstrHelpFile,dwHelpContext,plResult)
#define IDocHostShowUI_ShowHelp(This,hwnd,pszHelpFile,uCommand,dwData,ptMouse,pDispatchObjectHit) (This)->lpVtbl->ShowHelp(This,hwnd,pszHelpFile,uCommand,dwData,ptMouse,pDispatchObjectHit)
#else
/*** IUnknown methods ***/
static inline HRESULT IDocHostShowUI_QueryInterface(IDocHostShowUI* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDocHostShowUI_AddRef(IDocHostShowUI* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDocHostShowUI_Release(IDocHostShowUI* This) {
    return This->lpVtbl->Release(This);
}
/*** IDocHostShowUI methods ***/
static inline HRESULT IDocHostShowUI_ShowMessage(IDocHostShowUI* This,HWND hwnd,LPOLESTR lpstrText,LPOLESTR lpstrCaption,DWORD dwType,LPOLESTR lpstrHelpFile,DWORD dwHelpContext,LRESULT *plResult) {
    return This->lpVtbl->ShowMessage(This,hwnd,lpstrText,lpstrCaption,dwType,lpstrHelpFile,dwHelpContext,plResult);
}
static inline HRESULT IDocHostShowUI_ShowHelp(IDocHostShowUI* This,HWND hwnd,LPOLESTR pszHelpFile,UINT uCommand,DWORD dwData,POINT ptMouse,IDispatch *pDispatchObjectHit) {
    return This->lpVtbl->ShowHelp(This,hwnd,pszHelpFile,uCommand,dwData,ptMouse,pDispatchObjectHit);
}
#endif
#endif

#endif


#endif  /* __IDocHostShowUI_INTERFACE_DEFINED__ */

#define IClassFactory3 IClassFactoryEx
#define IID_IClassFactory3 IID_IClassFactoryEx
#define SID_SHTMLOMWindowServices IID_IHTMLOMWindowServices
/*****************************************************************************
 * IClassFactoryEx interface
 */
#ifndef __IClassFactoryEx_INTERFACE_DEFINED__
#define __IClassFactoryEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IClassFactoryEx, 0x342d1ea0, 0xae25, 0x11d1, 0x89,0xc5, 0x00,0x60,0x08,0xc3,0xfb,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("342d1ea0-ae25-11d1-89c5-006008c3fbfc")
IClassFactoryEx : public IClassFactory
{
    virtual HRESULT STDMETHODCALLTYPE CreateInstanceWithContext(
        IUnknown *punkContext,
        IUnknown *punkOuter,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IClassFactoryEx, 0x342d1ea0, 0xae25, 0x11d1, 0x89,0xc5, 0x00,0x60,0x08,0xc3,0xfb,0xfc)
#endif
#else
typedef struct IClassFactoryExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IClassFactoryEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IClassFactoryEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IClassFactoryEx *This);

    /*** IClassFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IClassFactoryEx *This,
        IUnknown *pUnkOuter,
        REFIID riid,
        void **ppvObject);

    HRESULT (STDMETHODCALLTYPE *LockServer)(
        IClassFactoryEx *This,
        WINBOOL fLock);

    /*** IClassFactoryEx methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstanceWithContext)(
        IClassFactoryEx *This,
        IUnknown *punkContext,
        IUnknown *punkOuter,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IClassFactoryExVtbl;

interface IClassFactoryEx {
    CONST_VTBL IClassFactoryExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IClassFactoryEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IClassFactoryEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IClassFactoryEx_Release(This) (This)->lpVtbl->Release(This)
/*** IClassFactory methods ***/
#define IClassFactoryEx_CreateInstance(This,pUnkOuter,riid,ppvObject) (This)->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObject)
#define IClassFactoryEx_LockServer(This,fLock) (This)->lpVtbl->LockServer(This,fLock)
/*** IClassFactoryEx methods ***/
#define IClassFactoryEx_CreateInstanceWithContext(This,punkContext,punkOuter,riid,ppv) (This)->lpVtbl->CreateInstanceWithContext(This,punkContext,punkOuter,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IClassFactoryEx_QueryInterface(IClassFactoryEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IClassFactoryEx_AddRef(IClassFactoryEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IClassFactoryEx_Release(IClassFactoryEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IClassFactory methods ***/
static inline HRESULT IClassFactoryEx_CreateInstance(IClassFactoryEx* This,IUnknown *pUnkOuter,REFIID riid,void **ppvObject) {
    return This->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObject);
}
static inline HRESULT IClassFactoryEx_LockServer(IClassFactoryEx* This,WINBOOL fLock) {
    return This->lpVtbl->LockServer(This,fLock);
}
/*** IClassFactoryEx methods ***/
static inline HRESULT IClassFactoryEx_CreateInstanceWithContext(IClassFactoryEx* This,IUnknown *punkContext,IUnknown *punkOuter,REFIID riid,void **ppv) {
    return This->lpVtbl->CreateInstanceWithContext(This,punkContext,punkOuter,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IClassFactoryEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IHTMLOMWindowServices interface
 */
#ifndef __IHTMLOMWindowServices_INTERFACE_DEFINED__
#define __IHTMLOMWindowServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_IHTMLOMWindowServices, 0x3050f5fc, 0x98b5, 0x11cf, 0xbb,0x82, 0x00,0xaa,0x00,0xbd,0xce,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3050f5fc-98b5-11cf-bb82-00aa00bdce0b")
IHTMLOMWindowServices : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE moveTo(
        LONG x,
        LONG y) = 0;

    virtual HRESULT STDMETHODCALLTYPE moveBy(
        LONG x,
        LONG y) = 0;

    virtual HRESULT STDMETHODCALLTYPE resizeTo(
        LONG x,
        LONG y) = 0;

    virtual HRESULT STDMETHODCALLTYPE resizeBy(
        LONG x,
        LONG y) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IHTMLOMWindowServices, 0x3050f5fc, 0x98b5, 0x11cf, 0xbb,0x82, 0x00,0xaa,0x00,0xbd,0xce,0x0b)
#endif
#else
typedef struct IHTMLOMWindowServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IHTMLOMWindowServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IHTMLOMWindowServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IHTMLOMWindowServices *This);

    /*** IHTMLOMWindowServices methods ***/
    HRESULT (STDMETHODCALLTYPE *moveTo)(
        IHTMLOMWindowServices *This,
        LONG x,
        LONG y);

    HRESULT (STDMETHODCALLTYPE *moveBy)(
        IHTMLOMWindowServices *This,
        LONG x,
        LONG y);

    HRESULT (STDMETHODCALLTYPE *resizeTo)(
        IHTMLOMWindowServices *This,
        LONG x,
        LONG y);

    HRESULT (STDMETHODCALLTYPE *resizeBy)(
        IHTMLOMWindowServices *This,
        LONG x,
        LONG y);

    END_INTERFACE
} IHTMLOMWindowServicesVtbl;

interface IHTMLOMWindowServices {
    CONST_VTBL IHTMLOMWindowServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IHTMLOMWindowServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHTMLOMWindowServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHTMLOMWindowServices_Release(This) (This)->lpVtbl->Release(This)
/*** IHTMLOMWindowServices methods ***/
#define IHTMLOMWindowServices_moveTo(This,x,y) (This)->lpVtbl->moveTo(This,x,y)
#define IHTMLOMWindowServices_moveBy(This,x,y) (This)->lpVtbl->moveBy(This,x,y)
#define IHTMLOMWindowServices_resizeTo(This,x,y) (This)->lpVtbl->resizeTo(This,x,y)
#define IHTMLOMWindowServices_resizeBy(This,x,y) (This)->lpVtbl->resizeBy(This,x,y)
#else
/*** IUnknown methods ***/
static inline HRESULT IHTMLOMWindowServices_QueryInterface(IHTMLOMWindowServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IHTMLOMWindowServices_AddRef(IHTMLOMWindowServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IHTMLOMWindowServices_Release(IHTMLOMWindowServices* This) {
    return This->lpVtbl->Release(This);
}
/*** IHTMLOMWindowServices methods ***/
static inline HRESULT IHTMLOMWindowServices_moveTo(IHTMLOMWindowServices* This,LONG x,LONG y) {
    return This->lpVtbl->moveTo(This,x,y);
}
static inline HRESULT IHTMLOMWindowServices_moveBy(IHTMLOMWindowServices* This,LONG x,LONG y) {
    return This->lpVtbl->moveBy(This,x,y);
}
static inline HRESULT IHTMLOMWindowServices_resizeTo(IHTMLOMWindowServices* This,LONG x,LONG y) {
    return This->lpVtbl->resizeTo(This,x,y);
}
static inline HRESULT IHTMLOMWindowServices_resizeBy(IHTMLOMWindowServices* This,LONG x,LONG y) {
    return This->lpVtbl->resizeBy(This,x,y);
}
#endif
#endif

#endif


#endif  /* __IHTMLOMWindowServices_INTERFACE_DEFINED__ */

#endif
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mshtmhst_h__ */
