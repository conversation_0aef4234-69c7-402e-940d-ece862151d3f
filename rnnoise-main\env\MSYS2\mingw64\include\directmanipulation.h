/*** Autogenerated by WIDL 10.12 from include/directmanipulation.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __directmanipulation_h__
#define __directmanipulation_h__

/* Forward declarations */

#ifndef __IDirectManipulationFrameInfoProvider_FWD_DEFINED__
#define __IDirectManipulationFrameInfoProvider_FWD_DEFINED__
typedef interface IDirectManipulationFrameInfoProvider IDirectManipulationFrameInfoProvider;
#ifdef __cplusplus
interface IDirectManipulationFrameInfoProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationManager_FWD_DEFINED__
#define __IDirectManipulationManager_FWD_DEFINED__
typedef interface IDirectManipulationManager IDirectManipulationManager;
#ifdef __cplusplus
interface IDirectManipulationManager;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationContent_FWD_DEFINED__
#define __IDirectManipulationContent_FWD_DEFINED__
typedef interface IDirectManipulationContent IDirectManipulationContent;
#ifdef __cplusplus
interface IDirectManipulationContent;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationUpdateHandler_FWD_DEFINED__
#define __IDirectManipulationUpdateHandler_FWD_DEFINED__
typedef interface IDirectManipulationUpdateHandler IDirectManipulationUpdateHandler;
#ifdef __cplusplus
interface IDirectManipulationUpdateHandler;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationUpdateManager_FWD_DEFINED__
#define __IDirectManipulationUpdateManager_FWD_DEFINED__
typedef interface IDirectManipulationUpdateManager IDirectManipulationUpdateManager;
#ifdef __cplusplus
interface IDirectManipulationUpdateManager;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationCompositor_FWD_DEFINED__
#define __IDirectManipulationCompositor_FWD_DEFINED__
typedef interface IDirectManipulationCompositor IDirectManipulationCompositor;
#ifdef __cplusplus
interface IDirectManipulationCompositor;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationViewport_FWD_DEFINED__
#define __IDirectManipulationViewport_FWD_DEFINED__
typedef interface IDirectManipulationViewport IDirectManipulationViewport;
#ifdef __cplusplus
interface IDirectManipulationViewport;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationViewport2_FWD_DEFINED__
#define __IDirectManipulationViewport2_FWD_DEFINED__
typedef interface IDirectManipulationViewport2 IDirectManipulationViewport2;
#ifdef __cplusplus
interface IDirectManipulationViewport2;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationViewportEventHandler_FWD_DEFINED__
#define __IDirectManipulationViewportEventHandler_FWD_DEFINED__
typedef interface IDirectManipulationViewportEventHandler IDirectManipulationViewportEventHandler;
#ifdef __cplusplus
interface IDirectManipulationViewportEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationManager2_FWD_DEFINED__
#define __IDirectManipulationManager2_FWD_DEFINED__
typedef interface IDirectManipulationManager2 IDirectManipulationManager2;
#ifdef __cplusplus
interface IDirectManipulationManager2;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationPrimaryContent_FWD_DEFINED__
#define __IDirectManipulationPrimaryContent_FWD_DEFINED__
typedef interface IDirectManipulationPrimaryContent IDirectManipulationPrimaryContent;
#ifdef __cplusplus
interface IDirectManipulationPrimaryContent;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationCompositor2_FWD_DEFINED__
#define __IDirectManipulationCompositor2_FWD_DEFINED__
typedef interface IDirectManipulationCompositor2 IDirectManipulationCompositor2;
#ifdef __cplusplus
interface IDirectManipulationCompositor2;
#endif /* __cplusplus */
#endif

#ifndef __IDirectManipulationInteractionEventHandler_FWD_DEFINED__
#define __IDirectManipulationInteractionEventHandler_FWD_DEFINED__
typedef interface IDirectManipulationInteractionEventHandler IDirectManipulationInteractionEventHandler;
#ifdef __cplusplus
interface IDirectManipulationInteractionEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __DirectManipulationManager_FWD_DEFINED__
#define __DirectManipulationManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class DirectManipulationManager DirectManipulationManager;
#else
typedef struct DirectManipulationManager DirectManipulationManager;
#endif /* defined __cplusplus */
#endif /* defined __DirectManipulationManager_FWD_DEFINED__ */

#ifndef __DirectManipulationSharedManager_FWD_DEFINED__
#define __DirectManipulationSharedManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class DirectManipulationSharedManager DirectManipulationSharedManager;
#else
typedef struct DirectManipulationSharedManager DirectManipulationSharedManager;
#endif /* defined __cplusplus */
#endif /* defined __DirectManipulationSharedManager_FWD_DEFINED__ */

#ifndef __DCompManipulationCompositor_FWD_DEFINED__
#define __DCompManipulationCompositor_FWD_DEFINED__
#ifdef __cplusplus
typedef class DCompManipulationCompositor DCompManipulationCompositor;
#else
typedef struct DCompManipulationCompositor DCompManipulationCompositor;
#endif /* defined __cplusplus */
#endif /* defined __DCompManipulationCompositor_FWD_DEFINED__ */

#ifndef __DirectManipulationViewport_FWD_DEFINED__
#define __DirectManipulationViewport_FWD_DEFINED__
#ifdef __cplusplus
typedef class DirectManipulationViewport DirectManipulationViewport;
#else
typedef struct DirectManipulationViewport DirectManipulationViewport;
#endif /* defined __cplusplus */
#endif /* defined __DirectManipulationViewport_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#if 0
typedef void *HWND;
#endif
#ifndef __IDirectManipulationViewportEventHandler_FWD_DEFINED__
#define __IDirectManipulationViewportEventHandler_FWD_DEFINED__
typedef interface IDirectManipulationViewportEventHandler IDirectManipulationViewportEventHandler;
#ifdef __cplusplus
interface IDirectManipulationViewportEventHandler;
#endif /* __cplusplus */
#endif

typedef enum DIRECTMANIPULATION_HITTEST_TYPE {
    DIRECTMANIPULATION_HITTEST_TYPE_ASYNCHRONOUS = 0x0,
    DIRECTMANIPULATION_HITTEST_TYPE_SYNCHRONOUS = 0x1,
    DIRECTMANIPULATION_HITTEST_TYPE_AUTO_SYNCHRONOUS = 0x2
} DIRECTMANIPULATION_HITTEST_TYPE;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_HITTEST_TYPE)
typedef enum DIRECTMANIPULATION_STATUS {
    DIRECTMANIPULATION_BUILDING = 0,
    DIRECTMANIPULATION_ENABLED = 1,
    DIRECTMANIPULATION_DISABLED = 2,
    DIRECTMANIPULATION_RUNNING = 3,
    DIRECTMANIPULATION_INERTIA = 4,
    DIRECTMANIPULATION_READY = 5,
    DIRECTMANIPULATION_SUSPENDED = 6
} DIRECTMANIPULATION_STATUS;
typedef enum DIRECTMANIPULATION_VIEWPORT_OPTIONS {
    DIRECTMANIPULATION_VIEWPORT_OPTIONS_DEFAULT = 0x0,
    DIRECTMANIPULATION_VIEWPORT_OPTIONS_AUTODISABLE = 0x1,
    DIRECTMANIPULATION_VIEWPORT_OPTIONS_MANUALUPDATE = 0x2,
    DIRECTMANIPULATION_VIEWPORT_OPTIONS_INPUT = 0x4,
    DIRECTMANIPULATION_VIEWPORT_OPTIONS_EXPLICITHITTEST = 0x8,
    DIRECTMANIPULATION_VIEWPORT_OPTIONS_DISABLEPIXELSNAPPING = 0x10
} DIRECTMANIPULATION_VIEWPORT_OPTIONS;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_VIEWPORT_OPTIONS)
typedef enum DIRECTMANIPULATION_CONFIGURATION {
    DIRECTMANIPULATION_CONFIGURATION_NONE = 0x0,
    DIRECTMANIPULATION_CONFIGURATION_INTERACTION = 0x1,
    DIRECTMANIPULATION_CONFIGURATION_TRANSLATION_X = 0x2,
    DIRECTMANIPULATION_CONFIGURATION_TRANSLATION_Y = 0x4,
    DIRECTMANIPULATION_CONFIGURATION_SCALING = 0x10,
    DIRECTMANIPULATION_CONFIGURATION_TRANSLATION_INERTIA = 0x20,
    DIRECTMANIPULATION_CONFIGURATION_SCALING_INERTIA = 0x80,
    DIRECTMANIPULATION_CONFIGURATION_RAILS_X = 0x100,
    DIRECTMANIPULATION_CONFIGURATION_RAILS_Y = 0x200
} DIRECTMANIPULATION_CONFIGURATION;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_CONFIGURATION)
typedef enum DIRECTMANIPULATION_GESTURE_CONFIGURATION {
    DIRECTMANIPULATION_GESTURE_NONE = 0x0,
    DIRECTMANIPULATION_GESTURE_DEFAULT = 0x0,
    DIRECTMANIPULATION_GESTURE_CROSS_SLIDE_VERTICAL = 0x8,
    DIRECTMANIPULATION_GESTURE_CROSS_SLIDE_HORIZONTAL = 0x10,
    DIRECTMANIPULATION_GESTURE_PINCH_ZOOM = 0x20
} DIRECTMANIPULATION_GESTURE_CONFIGURATION;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_GESTURE_CONFIGURATION)
typedef enum DIRECTMANIPULATION_MOTION_TYPES {
    DIRECTMANIPULATION_MOTION_NONE = 0x0,
    DIRECTMANIPULATION_MOTION_TRANSLATEX = 0x1,
    DIRECTMANIPULATION_MOTION_TRANSLATEY = 0x2,
    DIRECTMANIPULATION_MOTION_ZOOM = 0x4,
    DIRECTMANIPULATION_MOTION_CENTERX = 0x10,
    DIRECTMANIPULATION_MOTION_CENTERY = 0x20,
    DIRECTMANIPULATION_MOTION_ALL = (((DIRECTMANIPULATION_MOTION_TRANSLATEX | DIRECTMANIPULATION_MOTION_TRANSLATEY) | DIRECTMANIPULATION_MOTION_ZOOM) | DIRECTMANIPULATION_MOTION_CENTERX) | DIRECTMANIPULATION_MOTION_CENTERY
} DIRECTMANIPULATION_MOTION_TYPES;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_MOTION_TYPES)
typedef enum DIRECTMANIPULATION_SNAPPOINT_TYPE {
    DIRECTMANIPULATION_SNAPPOINT_MANDATORY = 0,
    DIRECTMANIPULATION_SNAPPOINT_OPTIONAL = 1,
    DIRECTMANIPULATION_SNAPPOINT_MANDATORY_SINGLE = 2,
    DIRECTMANIPULATION_SNAPPOINT_OPTIONAL_SINGLE = 3
} DIRECTMANIPULATION_SNAPPOINT_TYPE;
typedef enum DIRECTMANIPULATION_SNAPPOINT_COORDINATE {
    DIRECTMANIPULATION_COORDINATE_BOUNDARY = 0x0,
    DIRECTMANIPULATION_COORDINATE_ORIGIN = 0x1,
    DIRECTMANIPULATION_COORDINATE_MIRRORED = 0x10
} DIRECTMANIPULATION_SNAPPOINT_COORDINATE;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_SNAPPOINT_COORDINATE)
typedef enum DIRECTMANIPULATION_HORIZONTALALIGNMENT {
    DIRECTMANIPULATION_HORIZONTALALIGNMENT_NONE = 0x0,
    DIRECTMANIPULATION_HORIZONTALALIGNMENT_LEFT = 0x1,
    DIRECTMANIPULATION_HORIZONTALALIGNMENT_CENTER = 0x2,
    DIRECTMANIPULATION_HORIZONTALALIGNMENT_RIGHT = 0x4,
    DIRECTMANIPULATION_HORIZONTALALIGNMENT_UNLOCKCENTER = 0x8
} DIRECTMANIPULATION_HORIZONTALALIGNMENT;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_HORIZONTALALIGNMENT)
typedef enum DIRECTMANIPULATION_VERTICALALIGNMENT {
    DIRECTMANIPULATION_VERTICALALIGNMENT_NONE = 0x0,
    DIRECTMANIPULATION_VERTICALALIGNMENT_TOP = 0x1,
    DIRECTMANIPULATION_VERTICALALIGNMENT_CENTER = 0x2,
    DIRECTMANIPULATION_VERTICALALIGNMENT_BOTTOM = 0x4,
    DIRECTMANIPULATION_VERTICALALIGNMENT_UNLOCKCENTER = 0x8
} DIRECTMANIPULATION_VERTICALALIGNMENT;
DEFINE_ENUM_FLAG_OPERATORS(DIRECTMANIPULATION_VERTICALALIGNMENT)
typedef enum DIRECTMANIPULATION_INPUT_MODE {
    DIRECTMANIPULATION_INPUT_MODE_AUTOMATIC = 0,
    DIRECTMANIPULATION_INPUT_MODE_MANUAL = 1
} DIRECTMANIPULATION_INPUT_MODE;
typedef enum DIRECTMANIPULATION_INTERACTION_TYPE {
    DIRECTMANIPULATION_INTERACTION_BEGIN = 0,
    DIRECTMANIPULATION_INTERACTION_TYPE_MANIPULATION = 1,
    DIRECTMANIPULATION_INTERACTION_TYPE_GESTURE_TAP = 2,
    DIRECTMANIPULATION_INTERACTION_TYPE_GESTURE_HOLD = 3,
    DIRECTMANIPULATION_INTERACTION_TYPE_GESTURE_CROSS_SLIDE = 4,
    DIRECTMANIPULATION_INTERACTION_TYPE_GESTURE_PINCH_ZOOM = 5,
    DIRECTMANIPULATION_INTERACTION_END = 100
} DIRECTMANIPULATION_INTERACTION_TYPE;
/*****************************************************************************
 * IDirectManipulationFrameInfoProvider interface
 */
#ifndef __IDirectManipulationFrameInfoProvider_INTERFACE_DEFINED__
#define __IDirectManipulationFrameInfoProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationFrameInfoProvider, 0xfb759dba, 0x6f4c, 0x4c01, 0x87,0x4e, 0x19,0xc8,0xa0,0x59,0x07,0xf9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fb759dba-6f4c-4c01-874e-19c8a05907f9")
IDirectManipulationFrameInfoProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNextFrameInfo(
        ULONGLONG *time,
        ULONGLONG *process,
        ULONGLONG *composition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationFrameInfoProvider, 0xfb759dba, 0x6f4c, 0x4c01, 0x87,0x4e, 0x19,0xc8,0xa0,0x59,0x07,0xf9)
#endif
#else
typedef struct IDirectManipulationFrameInfoProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationFrameInfoProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationFrameInfoProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationFrameInfoProvider *This);

    /*** IDirectManipulationFrameInfoProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNextFrameInfo)(
        IDirectManipulationFrameInfoProvider *This,
        ULONGLONG *time,
        ULONGLONG *process,
        ULONGLONG *composition);

    END_INTERFACE
} IDirectManipulationFrameInfoProviderVtbl;

interface IDirectManipulationFrameInfoProvider {
    CONST_VTBL IDirectManipulationFrameInfoProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationFrameInfoProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationFrameInfoProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationFrameInfoProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationFrameInfoProvider methods ***/
#define IDirectManipulationFrameInfoProvider_GetNextFrameInfo(This,time,process,composition) (This)->lpVtbl->GetNextFrameInfo(This,time,process,composition)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationFrameInfoProvider_QueryInterface(IDirectManipulationFrameInfoProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationFrameInfoProvider_AddRef(IDirectManipulationFrameInfoProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationFrameInfoProvider_Release(IDirectManipulationFrameInfoProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationFrameInfoProvider methods ***/
static inline HRESULT IDirectManipulationFrameInfoProvider_GetNextFrameInfo(IDirectManipulationFrameInfoProvider* This,ULONGLONG *time,ULONGLONG *process,ULONGLONG *composition) {
    return This->lpVtbl->GetNextFrameInfo(This,time,process,composition);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationFrameInfoProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationManager interface
 */
#ifndef __IDirectManipulationManager_INTERFACE_DEFINED__
#define __IDirectManipulationManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationManager, 0xfbf5d3b4, 0x70c7, 0x4163, 0x93,0x22, 0x5a,0x6f,0x66,0x0d,0x6f,0xbc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fbf5d3b4-70c7-4163-9322-5a6f660d6fbc")
IDirectManipulationManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Activate(
        HWND window) = 0;

    virtual HRESULT STDMETHODCALLTYPE Deactivate(
        HWND window) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterHitTestTarget(
        HWND window,
        HWND hittest,
        DIRECTMANIPULATION_HITTEST_TYPE type) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessInput(
        const MSG *msg,
        WINBOOL *handled) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUpdateManager(
        REFIID riid,
        void **obj) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateViewport(
        IDirectManipulationFrameInfoProvider *frame,
        HWND window,
        REFIID riid,
        void **obj) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateContent(
        IDirectManipulationFrameInfoProvider *frame,
        REFCLSID clsid,
        REFIID riid,
        void **obj) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationManager, 0xfbf5d3b4, 0x70c7, 0x4163, 0x93,0x22, 0x5a,0x6f,0x66,0x0d,0x6f,0xbc)
#endif
#else
typedef struct IDirectManipulationManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationManager *This);

    /*** IDirectManipulationManager methods ***/
    HRESULT (STDMETHODCALLTYPE *Activate)(
        IDirectManipulationManager *This,
        HWND window);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        IDirectManipulationManager *This,
        HWND window);

    HRESULT (STDMETHODCALLTYPE *RegisterHitTestTarget)(
        IDirectManipulationManager *This,
        HWND window,
        HWND hittest,
        DIRECTMANIPULATION_HITTEST_TYPE type);

    HRESULT (STDMETHODCALLTYPE *ProcessInput)(
        IDirectManipulationManager *This,
        const MSG *msg,
        WINBOOL *handled);

    HRESULT (STDMETHODCALLTYPE *GetUpdateManager)(
        IDirectManipulationManager *This,
        REFIID riid,
        void **obj);

    HRESULT (STDMETHODCALLTYPE *CreateViewport)(
        IDirectManipulationManager *This,
        IDirectManipulationFrameInfoProvider *frame,
        HWND window,
        REFIID riid,
        void **obj);

    HRESULT (STDMETHODCALLTYPE *CreateContent)(
        IDirectManipulationManager *This,
        IDirectManipulationFrameInfoProvider *frame,
        REFCLSID clsid,
        REFIID riid,
        void **obj);

    END_INTERFACE
} IDirectManipulationManagerVtbl;

interface IDirectManipulationManager {
    CONST_VTBL IDirectManipulationManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationManager methods ***/
#define IDirectManipulationManager_Activate(This,window) (This)->lpVtbl->Activate(This,window)
#define IDirectManipulationManager_Deactivate(This,window) (This)->lpVtbl->Deactivate(This,window)
#define IDirectManipulationManager_RegisterHitTestTarget(This,window,hittest,type) (This)->lpVtbl->RegisterHitTestTarget(This,window,hittest,type)
#define IDirectManipulationManager_ProcessInput(This,msg,handled) (This)->lpVtbl->ProcessInput(This,msg,handled)
#define IDirectManipulationManager_GetUpdateManager(This,riid,obj) (This)->lpVtbl->GetUpdateManager(This,riid,obj)
#define IDirectManipulationManager_CreateViewport(This,frame,window,riid,obj) (This)->lpVtbl->CreateViewport(This,frame,window,riid,obj)
#define IDirectManipulationManager_CreateContent(This,frame,clsid,riid,obj) (This)->lpVtbl->CreateContent(This,frame,clsid,riid,obj)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationManager_QueryInterface(IDirectManipulationManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationManager_AddRef(IDirectManipulationManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationManager_Release(IDirectManipulationManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationManager methods ***/
static inline HRESULT IDirectManipulationManager_Activate(IDirectManipulationManager* This,HWND window) {
    return This->lpVtbl->Activate(This,window);
}
static inline HRESULT IDirectManipulationManager_Deactivate(IDirectManipulationManager* This,HWND window) {
    return This->lpVtbl->Deactivate(This,window);
}
static inline HRESULT IDirectManipulationManager_RegisterHitTestTarget(IDirectManipulationManager* This,HWND window,HWND hittest,DIRECTMANIPULATION_HITTEST_TYPE type) {
    return This->lpVtbl->RegisterHitTestTarget(This,window,hittest,type);
}
static inline HRESULT IDirectManipulationManager_ProcessInput(IDirectManipulationManager* This,const MSG *msg,WINBOOL *handled) {
    return This->lpVtbl->ProcessInput(This,msg,handled);
}
static inline HRESULT IDirectManipulationManager_GetUpdateManager(IDirectManipulationManager* This,REFIID riid,void **obj) {
    return This->lpVtbl->GetUpdateManager(This,riid,obj);
}
static inline HRESULT IDirectManipulationManager_CreateViewport(IDirectManipulationManager* This,IDirectManipulationFrameInfoProvider *frame,HWND window,REFIID riid,void **obj) {
    return This->lpVtbl->CreateViewport(This,frame,window,riid,obj);
}
static inline HRESULT IDirectManipulationManager_CreateContent(IDirectManipulationManager* This,IDirectManipulationFrameInfoProvider *frame,REFCLSID clsid,REFIID riid,void **obj) {
    return This->lpVtbl->CreateContent(This,frame,clsid,riid,obj);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationContent interface
 */
#ifndef __IDirectManipulationContent_INTERFACE_DEFINED__
#define __IDirectManipulationContent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationContent, 0xb89962cb, 0x3d89, 0x442b, 0xbb,0x58, 0x50,0x98,0xfa,0x0f,0x9f,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b89962cb-3d89-442b-bb58-5098fa0f9f16")
IDirectManipulationContent : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetContentRect(
        RECT *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContentRect(
        const RECT *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetViewport(
        REFIID riid,
        void **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTag(
        REFIID riid,
        void **object,
        UINT32 *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTag(
        IUnknown *object,
        UINT32 id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputTransform(
        float *matrix,
        DWORD count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentTransform(
        float *matrix,
        DWORD count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SyncContentTransform(
        const float *matrix,
        DWORD count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationContent, 0xb89962cb, 0x3d89, 0x442b, 0xbb,0x58, 0x50,0x98,0xfa,0x0f,0x9f,0x16)
#endif
#else
typedef struct IDirectManipulationContentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationContent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationContent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationContent *This);

    /*** IDirectManipulationContent methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContentRect)(
        IDirectManipulationContent *This,
        RECT *size);

    HRESULT (STDMETHODCALLTYPE *SetContentRect)(
        IDirectManipulationContent *This,
        const RECT *size);

    HRESULT (STDMETHODCALLTYPE *GetViewport)(
        IDirectManipulationContent *This,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *GetTag)(
        IDirectManipulationContent *This,
        REFIID riid,
        void **object,
        UINT32 *id);

    HRESULT (STDMETHODCALLTYPE *SetTag)(
        IDirectManipulationContent *This,
        IUnknown *object,
        UINT32 id);

    HRESULT (STDMETHODCALLTYPE *GetOutputTransform)(
        IDirectManipulationContent *This,
        float *matrix,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *GetContentTransform)(
        IDirectManipulationContent *This,
        float *matrix,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *SyncContentTransform)(
        IDirectManipulationContent *This,
        const float *matrix,
        DWORD count);

    END_INTERFACE
} IDirectManipulationContentVtbl;

interface IDirectManipulationContent {
    CONST_VTBL IDirectManipulationContentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationContent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationContent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationContent_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationContent methods ***/
#define IDirectManipulationContent_GetContentRect(This,size) (This)->lpVtbl->GetContentRect(This,size)
#define IDirectManipulationContent_SetContentRect(This,size) (This)->lpVtbl->SetContentRect(This,size)
#define IDirectManipulationContent_GetViewport(This,riid,object) (This)->lpVtbl->GetViewport(This,riid,object)
#define IDirectManipulationContent_GetTag(This,riid,object,id) (This)->lpVtbl->GetTag(This,riid,object,id)
#define IDirectManipulationContent_SetTag(This,object,id) (This)->lpVtbl->SetTag(This,object,id)
#define IDirectManipulationContent_GetOutputTransform(This,matrix,count) (This)->lpVtbl->GetOutputTransform(This,matrix,count)
#define IDirectManipulationContent_GetContentTransform(This,matrix,count) (This)->lpVtbl->GetContentTransform(This,matrix,count)
#define IDirectManipulationContent_SyncContentTransform(This,matrix,count) (This)->lpVtbl->SyncContentTransform(This,matrix,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationContent_QueryInterface(IDirectManipulationContent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationContent_AddRef(IDirectManipulationContent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationContent_Release(IDirectManipulationContent* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationContent methods ***/
static inline HRESULT IDirectManipulationContent_GetContentRect(IDirectManipulationContent* This,RECT *size) {
    return This->lpVtbl->GetContentRect(This,size);
}
static inline HRESULT IDirectManipulationContent_SetContentRect(IDirectManipulationContent* This,const RECT *size) {
    return This->lpVtbl->SetContentRect(This,size);
}
static inline HRESULT IDirectManipulationContent_GetViewport(IDirectManipulationContent* This,REFIID riid,void **object) {
    return This->lpVtbl->GetViewport(This,riid,object);
}
static inline HRESULT IDirectManipulationContent_GetTag(IDirectManipulationContent* This,REFIID riid,void **object,UINT32 *id) {
    return This->lpVtbl->GetTag(This,riid,object,id);
}
static inline HRESULT IDirectManipulationContent_SetTag(IDirectManipulationContent* This,IUnknown *object,UINT32 id) {
    return This->lpVtbl->SetTag(This,object,id);
}
static inline HRESULT IDirectManipulationContent_GetOutputTransform(IDirectManipulationContent* This,float *matrix,DWORD count) {
    return This->lpVtbl->GetOutputTransform(This,matrix,count);
}
static inline HRESULT IDirectManipulationContent_GetContentTransform(IDirectManipulationContent* This,float *matrix,DWORD count) {
    return This->lpVtbl->GetContentTransform(This,matrix,count);
}
static inline HRESULT IDirectManipulationContent_SyncContentTransform(IDirectManipulationContent* This,const float *matrix,DWORD count) {
    return This->lpVtbl->SyncContentTransform(This,matrix,count);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationContent_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationUpdateHandler interface
 */
#ifndef __IDirectManipulationUpdateHandler_INTERFACE_DEFINED__
#define __IDirectManipulationUpdateHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationUpdateHandler, 0x790b6337, 0x64f8, 0x4ff5, 0xa2,0x69, 0xb3,0x2b,0xc2,0xaf,0x27,0xa7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("790b6337-64f8-4ff5-a269-b32bc2af27a7")
IDirectManipulationUpdateHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Update(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationUpdateHandler, 0x790b6337, 0x64f8, 0x4ff5, 0xa2,0x69, 0xb3,0x2b,0xc2,0xaf,0x27,0xa7)
#endif
#else
typedef struct IDirectManipulationUpdateHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationUpdateHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationUpdateHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationUpdateHandler *This);

    /*** IDirectManipulationUpdateHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Update)(
        IDirectManipulationUpdateHandler *This);

    END_INTERFACE
} IDirectManipulationUpdateHandlerVtbl;

interface IDirectManipulationUpdateHandler {
    CONST_VTBL IDirectManipulationUpdateHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationUpdateHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationUpdateHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationUpdateHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationUpdateHandler methods ***/
#define IDirectManipulationUpdateHandler_Update(This) (This)->lpVtbl->Update(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationUpdateHandler_QueryInterface(IDirectManipulationUpdateHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationUpdateHandler_AddRef(IDirectManipulationUpdateHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationUpdateHandler_Release(IDirectManipulationUpdateHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationUpdateHandler methods ***/
static inline HRESULT IDirectManipulationUpdateHandler_Update(IDirectManipulationUpdateHandler* This) {
    return This->lpVtbl->Update(This);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationUpdateHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationUpdateManager interface
 */
#ifndef __IDirectManipulationUpdateManager_INTERFACE_DEFINED__
#define __IDirectManipulationUpdateManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationUpdateManager, 0xb0ae62fd, 0xbe34, 0x46e7, 0x9c,0xaa, 0xd3,0x61,0xfa,0xcb,0xb9,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b0ae62fd-be34-46e7-9caa-d361facbb9cc")
IDirectManipulationUpdateManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterWaitHandleCallback(
        HANDLE handle,
        IDirectManipulationUpdateHandler *handler,
        DWORD *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterWaitHandleCallback(
        DWORD cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Update(
        IDirectManipulationFrameInfoProvider *provider) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationUpdateManager, 0xb0ae62fd, 0xbe34, 0x46e7, 0x9c,0xaa, 0xd3,0x61,0xfa,0xcb,0xb9,0xcc)
#endif
#else
typedef struct IDirectManipulationUpdateManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationUpdateManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationUpdateManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationUpdateManager *This);

    /*** IDirectManipulationUpdateManager methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterWaitHandleCallback)(
        IDirectManipulationUpdateManager *This,
        HANDLE handle,
        IDirectManipulationUpdateHandler *handler,
        DWORD *cookie);

    HRESULT (STDMETHODCALLTYPE *UnregisterWaitHandleCallback)(
        IDirectManipulationUpdateManager *This,
        DWORD cookie);

    HRESULT (STDMETHODCALLTYPE *Update)(
        IDirectManipulationUpdateManager *This,
        IDirectManipulationFrameInfoProvider *provider);

    END_INTERFACE
} IDirectManipulationUpdateManagerVtbl;

interface IDirectManipulationUpdateManager {
    CONST_VTBL IDirectManipulationUpdateManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationUpdateManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationUpdateManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationUpdateManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationUpdateManager methods ***/
#define IDirectManipulationUpdateManager_RegisterWaitHandleCallback(This,handle,handler,cookie) (This)->lpVtbl->RegisterWaitHandleCallback(This,handle,handler,cookie)
#define IDirectManipulationUpdateManager_UnregisterWaitHandleCallback(This,cookie) (This)->lpVtbl->UnregisterWaitHandleCallback(This,cookie)
#define IDirectManipulationUpdateManager_Update(This,provider) (This)->lpVtbl->Update(This,provider)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationUpdateManager_QueryInterface(IDirectManipulationUpdateManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationUpdateManager_AddRef(IDirectManipulationUpdateManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationUpdateManager_Release(IDirectManipulationUpdateManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationUpdateManager methods ***/
static inline HRESULT IDirectManipulationUpdateManager_RegisterWaitHandleCallback(IDirectManipulationUpdateManager* This,HANDLE handle,IDirectManipulationUpdateHandler *handler,DWORD *cookie) {
    return This->lpVtbl->RegisterWaitHandleCallback(This,handle,handler,cookie);
}
static inline HRESULT IDirectManipulationUpdateManager_UnregisterWaitHandleCallback(IDirectManipulationUpdateManager* This,DWORD cookie) {
    return This->lpVtbl->UnregisterWaitHandleCallback(This,cookie);
}
static inline HRESULT IDirectManipulationUpdateManager_Update(IDirectManipulationUpdateManager* This,IDirectManipulationFrameInfoProvider *provider) {
    return This->lpVtbl->Update(This,provider);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationUpdateManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationCompositor interface
 */
#ifndef __IDirectManipulationCompositor_INTERFACE_DEFINED__
#define __IDirectManipulationCompositor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationCompositor, 0x537a0825, 0x0387, 0x4efa, 0xb6,0x2f, 0x71,0xeb,0x1f,0x08,0x5a,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("537a0825-0387-4efa-b62f-71eb1f085a7e")
IDirectManipulationCompositor : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddContent(
        IDirectManipulationContent *content,
        IUnknown *device,
        IUnknown *parent,
        IUnknown *child) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveContent(
        IDirectManipulationContent *content) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUpdateManager(
        IDirectManipulationUpdateManager *manager) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationCompositor, 0x537a0825, 0x0387, 0x4efa, 0xb6,0x2f, 0x71,0xeb,0x1f,0x08,0x5a,0x7e)
#endif
#else
typedef struct IDirectManipulationCompositorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationCompositor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationCompositor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationCompositor *This);

    /*** IDirectManipulationCompositor methods ***/
    HRESULT (STDMETHODCALLTYPE *AddContent)(
        IDirectManipulationCompositor *This,
        IDirectManipulationContent *content,
        IUnknown *device,
        IUnknown *parent,
        IUnknown *child);

    HRESULT (STDMETHODCALLTYPE *RemoveContent)(
        IDirectManipulationCompositor *This,
        IDirectManipulationContent *content);

    HRESULT (STDMETHODCALLTYPE *SetUpdateManager)(
        IDirectManipulationCompositor *This,
        IDirectManipulationUpdateManager *manager);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IDirectManipulationCompositor *This);

    END_INTERFACE
} IDirectManipulationCompositorVtbl;

interface IDirectManipulationCompositor {
    CONST_VTBL IDirectManipulationCompositorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationCompositor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationCompositor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationCompositor_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationCompositor methods ***/
#define IDirectManipulationCompositor_AddContent(This,content,device,parent,child) (This)->lpVtbl->AddContent(This,content,device,parent,child)
#define IDirectManipulationCompositor_RemoveContent(This,content) (This)->lpVtbl->RemoveContent(This,content)
#define IDirectManipulationCompositor_SetUpdateManager(This,manager) (This)->lpVtbl->SetUpdateManager(This,manager)
#define IDirectManipulationCompositor_Flush(This) (This)->lpVtbl->Flush(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationCompositor_QueryInterface(IDirectManipulationCompositor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationCompositor_AddRef(IDirectManipulationCompositor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationCompositor_Release(IDirectManipulationCompositor* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationCompositor methods ***/
static inline HRESULT IDirectManipulationCompositor_AddContent(IDirectManipulationCompositor* This,IDirectManipulationContent *content,IUnknown *device,IUnknown *parent,IUnknown *child) {
    return This->lpVtbl->AddContent(This,content,device,parent,child);
}
static inline HRESULT IDirectManipulationCompositor_RemoveContent(IDirectManipulationCompositor* This,IDirectManipulationContent *content) {
    return This->lpVtbl->RemoveContent(This,content);
}
static inline HRESULT IDirectManipulationCompositor_SetUpdateManager(IDirectManipulationCompositor* This,IDirectManipulationUpdateManager *manager) {
    return This->lpVtbl->SetUpdateManager(This,manager);
}
static inline HRESULT IDirectManipulationCompositor_Flush(IDirectManipulationCompositor* This) {
    return This->lpVtbl->Flush(This);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationCompositor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationViewport interface
 */
#ifndef __IDirectManipulationViewport_INTERFACE_DEFINED__
#define __IDirectManipulationViewport_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationViewport, 0x28b85a3d, 0x60a0, 0x48bd, 0x9b,0xa1, 0x5c,0xe8,0xd9,0xea,0x3a,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("28b85a3d-60a0-48bd-9ba1-5ce8d9ea3a6d")
IDirectManipulationViewport : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Enable(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Disable(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContact(
        UINT32 id) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseContact(
        UINT32 id) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseAllContacts(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        DIRECTMANIPULATION_STATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTag(
        REFIID riid,
        void **object,
        UINT32 *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTag(
        IUnknown *object,
        UINT32 id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetViewportRect(
        RECT *viewport) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetViewportRect(
        const RECT *viewport) = 0;

    virtual HRESULT STDMETHODCALLTYPE ZoomToRect(
        const float left,
        const float top,
        const float right,
        const float bottom,
        WINBOOL animate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetViewportTransform(
        const float *matrix,
        DWORD count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SyncDisplayTransform(
        const float *matrix,
        DWORD count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrimaryContent(
        REFIID riid,
        void **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddContent(
        IDirectManipulationContent *content) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveContent(
        IDirectManipulationContent *content) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetViewportOptions(
        DIRECTMANIPULATION_VIEWPORT_OPTIONS options) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddConfiguration(
        DIRECTMANIPULATION_CONFIGURATION configuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveConfiguration(
        DIRECTMANIPULATION_CONFIGURATION configuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE ActivateConfiguration(
        DIRECTMANIPULATION_CONFIGURATION configuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetManualGesture(
        DIRECTMANIPULATION_GESTURE_CONFIGURATION configuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetChaining(
        DIRECTMANIPULATION_MOTION_TYPES enabledTypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddEventHandler(
        HWND window,
        IDirectManipulationViewportEventHandler *eventHandler,
        DWORD *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveEventHandler(
        DWORD cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInputMode(
        DIRECTMANIPULATION_INPUT_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUpdateMode(
        DIRECTMANIPULATION_INPUT_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Abandon(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationViewport, 0x28b85a3d, 0x60a0, 0x48bd, 0x9b,0xa1, 0x5c,0xe8,0xd9,0xea,0x3a,0x6d)
#endif
#else
typedef struct IDirectManipulationViewportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationViewport *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationViewport *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationViewport *This);

    /*** IDirectManipulationViewport methods ***/
    HRESULT (STDMETHODCALLTYPE *Enable)(
        IDirectManipulationViewport *This);

    HRESULT (STDMETHODCALLTYPE *Disable)(
        IDirectManipulationViewport *This);

    HRESULT (STDMETHODCALLTYPE *SetContact)(
        IDirectManipulationViewport *This,
        UINT32 id);

    HRESULT (STDMETHODCALLTYPE *ReleaseContact)(
        IDirectManipulationViewport *This,
        UINT32 id);

    HRESULT (STDMETHODCALLTYPE *ReleaseAllContacts)(
        IDirectManipulationViewport *This);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *GetTag)(
        IDirectManipulationViewport *This,
        REFIID riid,
        void **object,
        UINT32 *id);

    HRESULT (STDMETHODCALLTYPE *SetTag)(
        IDirectManipulationViewport *This,
        IUnknown *object,
        UINT32 id);

    HRESULT (STDMETHODCALLTYPE *GetViewportRect)(
        IDirectManipulationViewport *This,
        RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *SetViewportRect)(
        IDirectManipulationViewport *This,
        const RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *ZoomToRect)(
        IDirectManipulationViewport *This,
        const float left,
        const float top,
        const float right,
        const float bottom,
        WINBOOL animate);

    HRESULT (STDMETHODCALLTYPE *SetViewportTransform)(
        IDirectManipulationViewport *This,
        const float *matrix,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *SyncDisplayTransform)(
        IDirectManipulationViewport *This,
        const float *matrix,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *GetPrimaryContent)(
        IDirectManipulationViewport *This,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *AddContent)(
        IDirectManipulationViewport *This,
        IDirectManipulationContent *content);

    HRESULT (STDMETHODCALLTYPE *RemoveContent)(
        IDirectManipulationViewport *This,
        IDirectManipulationContent *content);

    HRESULT (STDMETHODCALLTYPE *SetViewportOptions)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_VIEWPORT_OPTIONS options);

    HRESULT (STDMETHODCALLTYPE *AddConfiguration)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *RemoveConfiguration)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *ActivateConfiguration)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *SetManualGesture)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_GESTURE_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *SetChaining)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_MOTION_TYPES enabledTypes);

    HRESULT (STDMETHODCALLTYPE *AddEventHandler)(
        IDirectManipulationViewport *This,
        HWND window,
        IDirectManipulationViewportEventHandler *eventHandler,
        DWORD *cookie);

    HRESULT (STDMETHODCALLTYPE *RemoveEventHandler)(
        IDirectManipulationViewport *This,
        DWORD cookie);

    HRESULT (STDMETHODCALLTYPE *SetInputMode)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_INPUT_MODE mode);

    HRESULT (STDMETHODCALLTYPE *SetUpdateMode)(
        IDirectManipulationViewport *This,
        DIRECTMANIPULATION_INPUT_MODE mode);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IDirectManipulationViewport *This);

    HRESULT (STDMETHODCALLTYPE *Abandon)(
        IDirectManipulationViewport *This);

    END_INTERFACE
} IDirectManipulationViewportVtbl;

interface IDirectManipulationViewport {
    CONST_VTBL IDirectManipulationViewportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationViewport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationViewport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationViewport_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationViewport methods ***/
#define IDirectManipulationViewport_Enable(This) (This)->lpVtbl->Enable(This)
#define IDirectManipulationViewport_Disable(This) (This)->lpVtbl->Disable(This)
#define IDirectManipulationViewport_SetContact(This,id) (This)->lpVtbl->SetContact(This,id)
#define IDirectManipulationViewport_ReleaseContact(This,id) (This)->lpVtbl->ReleaseContact(This,id)
#define IDirectManipulationViewport_ReleaseAllContacts(This) (This)->lpVtbl->ReleaseAllContacts(This)
#define IDirectManipulationViewport_GetStatus(This,status) (This)->lpVtbl->GetStatus(This,status)
#define IDirectManipulationViewport_GetTag(This,riid,object,id) (This)->lpVtbl->GetTag(This,riid,object,id)
#define IDirectManipulationViewport_SetTag(This,object,id) (This)->lpVtbl->SetTag(This,object,id)
#define IDirectManipulationViewport_GetViewportRect(This,viewport) (This)->lpVtbl->GetViewportRect(This,viewport)
#define IDirectManipulationViewport_SetViewportRect(This,viewport) (This)->lpVtbl->SetViewportRect(This,viewport)
#define IDirectManipulationViewport_ZoomToRect(This,left,top,right,bottom,animate) (This)->lpVtbl->ZoomToRect(This,left,top,right,bottom,animate)
#define IDirectManipulationViewport_SetViewportTransform(This,matrix,count) (This)->lpVtbl->SetViewportTransform(This,matrix,count)
#define IDirectManipulationViewport_SyncDisplayTransform(This,matrix,count) (This)->lpVtbl->SyncDisplayTransform(This,matrix,count)
#define IDirectManipulationViewport_GetPrimaryContent(This,riid,object) (This)->lpVtbl->GetPrimaryContent(This,riid,object)
#define IDirectManipulationViewport_AddContent(This,content) (This)->lpVtbl->AddContent(This,content)
#define IDirectManipulationViewport_RemoveContent(This,content) (This)->lpVtbl->RemoveContent(This,content)
#define IDirectManipulationViewport_SetViewportOptions(This,options) (This)->lpVtbl->SetViewportOptions(This,options)
#define IDirectManipulationViewport_AddConfiguration(This,configuration) (This)->lpVtbl->AddConfiguration(This,configuration)
#define IDirectManipulationViewport_RemoveConfiguration(This,configuration) (This)->lpVtbl->RemoveConfiguration(This,configuration)
#define IDirectManipulationViewport_ActivateConfiguration(This,configuration) (This)->lpVtbl->ActivateConfiguration(This,configuration)
#define IDirectManipulationViewport_SetManualGesture(This,configuration) (This)->lpVtbl->SetManualGesture(This,configuration)
#define IDirectManipulationViewport_SetChaining(This,enabledTypes) (This)->lpVtbl->SetChaining(This,enabledTypes)
#define IDirectManipulationViewport_AddEventHandler(This,window,eventHandler,cookie) (This)->lpVtbl->AddEventHandler(This,window,eventHandler,cookie)
#define IDirectManipulationViewport_RemoveEventHandler(This,cookie) (This)->lpVtbl->RemoveEventHandler(This,cookie)
#define IDirectManipulationViewport_SetInputMode(This,mode) (This)->lpVtbl->SetInputMode(This,mode)
#define IDirectManipulationViewport_SetUpdateMode(This,mode) (This)->lpVtbl->SetUpdateMode(This,mode)
#define IDirectManipulationViewport_Stop(This) (This)->lpVtbl->Stop(This)
#define IDirectManipulationViewport_Abandon(This) (This)->lpVtbl->Abandon(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationViewport_QueryInterface(IDirectManipulationViewport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationViewport_AddRef(IDirectManipulationViewport* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationViewport_Release(IDirectManipulationViewport* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationViewport methods ***/
static inline HRESULT IDirectManipulationViewport_Enable(IDirectManipulationViewport* This) {
    return This->lpVtbl->Enable(This);
}
static inline HRESULT IDirectManipulationViewport_Disable(IDirectManipulationViewport* This) {
    return This->lpVtbl->Disable(This);
}
static inline HRESULT IDirectManipulationViewport_SetContact(IDirectManipulationViewport* This,UINT32 id) {
    return This->lpVtbl->SetContact(This,id);
}
static inline HRESULT IDirectManipulationViewport_ReleaseContact(IDirectManipulationViewport* This,UINT32 id) {
    return This->lpVtbl->ReleaseContact(This,id);
}
static inline HRESULT IDirectManipulationViewport_ReleaseAllContacts(IDirectManipulationViewport* This) {
    return This->lpVtbl->ReleaseAllContacts(This);
}
static inline HRESULT IDirectManipulationViewport_GetStatus(IDirectManipulationViewport* This,DIRECTMANIPULATION_STATUS *status) {
    return This->lpVtbl->GetStatus(This,status);
}
static inline HRESULT IDirectManipulationViewport_GetTag(IDirectManipulationViewport* This,REFIID riid,void **object,UINT32 *id) {
    return This->lpVtbl->GetTag(This,riid,object,id);
}
static inline HRESULT IDirectManipulationViewport_SetTag(IDirectManipulationViewport* This,IUnknown *object,UINT32 id) {
    return This->lpVtbl->SetTag(This,object,id);
}
static inline HRESULT IDirectManipulationViewport_GetViewportRect(IDirectManipulationViewport* This,RECT *viewport) {
    return This->lpVtbl->GetViewportRect(This,viewport);
}
static inline HRESULT IDirectManipulationViewport_SetViewportRect(IDirectManipulationViewport* This,const RECT *viewport) {
    return This->lpVtbl->SetViewportRect(This,viewport);
}
static inline HRESULT IDirectManipulationViewport_ZoomToRect(IDirectManipulationViewport* This,const float left,const float top,const float right,const float bottom,WINBOOL animate) {
    return This->lpVtbl->ZoomToRect(This,left,top,right,bottom,animate);
}
static inline HRESULT IDirectManipulationViewport_SetViewportTransform(IDirectManipulationViewport* This,const float *matrix,DWORD count) {
    return This->lpVtbl->SetViewportTransform(This,matrix,count);
}
static inline HRESULT IDirectManipulationViewport_SyncDisplayTransform(IDirectManipulationViewport* This,const float *matrix,DWORD count) {
    return This->lpVtbl->SyncDisplayTransform(This,matrix,count);
}
static inline HRESULT IDirectManipulationViewport_GetPrimaryContent(IDirectManipulationViewport* This,REFIID riid,void **object) {
    return This->lpVtbl->GetPrimaryContent(This,riid,object);
}
static inline HRESULT IDirectManipulationViewport_AddContent(IDirectManipulationViewport* This,IDirectManipulationContent *content) {
    return This->lpVtbl->AddContent(This,content);
}
static inline HRESULT IDirectManipulationViewport_RemoveContent(IDirectManipulationViewport* This,IDirectManipulationContent *content) {
    return This->lpVtbl->RemoveContent(This,content);
}
static inline HRESULT IDirectManipulationViewport_SetViewportOptions(IDirectManipulationViewport* This,DIRECTMANIPULATION_VIEWPORT_OPTIONS options) {
    return This->lpVtbl->SetViewportOptions(This,options);
}
static inline HRESULT IDirectManipulationViewport_AddConfiguration(IDirectManipulationViewport* This,DIRECTMANIPULATION_CONFIGURATION configuration) {
    return This->lpVtbl->AddConfiguration(This,configuration);
}
static inline HRESULT IDirectManipulationViewport_RemoveConfiguration(IDirectManipulationViewport* This,DIRECTMANIPULATION_CONFIGURATION configuration) {
    return This->lpVtbl->RemoveConfiguration(This,configuration);
}
static inline HRESULT IDirectManipulationViewport_ActivateConfiguration(IDirectManipulationViewport* This,DIRECTMANIPULATION_CONFIGURATION configuration) {
    return This->lpVtbl->ActivateConfiguration(This,configuration);
}
static inline HRESULT IDirectManipulationViewport_SetManualGesture(IDirectManipulationViewport* This,DIRECTMANIPULATION_GESTURE_CONFIGURATION configuration) {
    return This->lpVtbl->SetManualGesture(This,configuration);
}
static inline HRESULT IDirectManipulationViewport_SetChaining(IDirectManipulationViewport* This,DIRECTMANIPULATION_MOTION_TYPES enabledTypes) {
    return This->lpVtbl->SetChaining(This,enabledTypes);
}
static inline HRESULT IDirectManipulationViewport_AddEventHandler(IDirectManipulationViewport* This,HWND window,IDirectManipulationViewportEventHandler *eventHandler,DWORD *cookie) {
    return This->lpVtbl->AddEventHandler(This,window,eventHandler,cookie);
}
static inline HRESULT IDirectManipulationViewport_RemoveEventHandler(IDirectManipulationViewport* This,DWORD cookie) {
    return This->lpVtbl->RemoveEventHandler(This,cookie);
}
static inline HRESULT IDirectManipulationViewport_SetInputMode(IDirectManipulationViewport* This,DIRECTMANIPULATION_INPUT_MODE mode) {
    return This->lpVtbl->SetInputMode(This,mode);
}
static inline HRESULT IDirectManipulationViewport_SetUpdateMode(IDirectManipulationViewport* This,DIRECTMANIPULATION_INPUT_MODE mode) {
    return This->lpVtbl->SetUpdateMode(This,mode);
}
static inline HRESULT IDirectManipulationViewport_Stop(IDirectManipulationViewport* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IDirectManipulationViewport_Abandon(IDirectManipulationViewport* This) {
    return This->lpVtbl->Abandon(This);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationViewport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationViewport2 interface
 */
#ifndef __IDirectManipulationViewport2_INTERFACE_DEFINED__
#define __IDirectManipulationViewport2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationViewport2, 0x923ccaac, 0x61e1, 0x4385, 0xb7,0x26, 0x01,0x7a,0xf1,0x89,0x88,0x2a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("923ccaac-61e1-4385-b726-017af189882a")
IDirectManipulationViewport2 : public IDirectManipulationViewport
{
    virtual HRESULT STDMETHODCALLTYPE AddBehavior(
        IUnknown *behavior,
        DWORD *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveBehavior(
        DWORD cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllBehaviors(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationViewport2, 0x923ccaac, 0x61e1, 0x4385, 0xb7,0x26, 0x01,0x7a,0xf1,0x89,0x88,0x2a)
#endif
#else
typedef struct IDirectManipulationViewport2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationViewport2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationViewport2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationViewport2 *This);

    /*** IDirectManipulationViewport methods ***/
    HRESULT (STDMETHODCALLTYPE *Enable)(
        IDirectManipulationViewport2 *This);

    HRESULT (STDMETHODCALLTYPE *Disable)(
        IDirectManipulationViewport2 *This);

    HRESULT (STDMETHODCALLTYPE *SetContact)(
        IDirectManipulationViewport2 *This,
        UINT32 id);

    HRESULT (STDMETHODCALLTYPE *ReleaseContact)(
        IDirectManipulationViewport2 *This,
        UINT32 id);

    HRESULT (STDMETHODCALLTYPE *ReleaseAllContacts)(
        IDirectManipulationViewport2 *This);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *GetTag)(
        IDirectManipulationViewport2 *This,
        REFIID riid,
        void **object,
        UINT32 *id);

    HRESULT (STDMETHODCALLTYPE *SetTag)(
        IDirectManipulationViewport2 *This,
        IUnknown *object,
        UINT32 id);

    HRESULT (STDMETHODCALLTYPE *GetViewportRect)(
        IDirectManipulationViewport2 *This,
        RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *SetViewportRect)(
        IDirectManipulationViewport2 *This,
        const RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *ZoomToRect)(
        IDirectManipulationViewport2 *This,
        const float left,
        const float top,
        const float right,
        const float bottom,
        WINBOOL animate);

    HRESULT (STDMETHODCALLTYPE *SetViewportTransform)(
        IDirectManipulationViewport2 *This,
        const float *matrix,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *SyncDisplayTransform)(
        IDirectManipulationViewport2 *This,
        const float *matrix,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *GetPrimaryContent)(
        IDirectManipulationViewport2 *This,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *AddContent)(
        IDirectManipulationViewport2 *This,
        IDirectManipulationContent *content);

    HRESULT (STDMETHODCALLTYPE *RemoveContent)(
        IDirectManipulationViewport2 *This,
        IDirectManipulationContent *content);

    HRESULT (STDMETHODCALLTYPE *SetViewportOptions)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_VIEWPORT_OPTIONS options);

    HRESULT (STDMETHODCALLTYPE *AddConfiguration)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *RemoveConfiguration)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *ActivateConfiguration)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *SetManualGesture)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_GESTURE_CONFIGURATION configuration);

    HRESULT (STDMETHODCALLTYPE *SetChaining)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_MOTION_TYPES enabledTypes);

    HRESULT (STDMETHODCALLTYPE *AddEventHandler)(
        IDirectManipulationViewport2 *This,
        HWND window,
        IDirectManipulationViewportEventHandler *eventHandler,
        DWORD *cookie);

    HRESULT (STDMETHODCALLTYPE *RemoveEventHandler)(
        IDirectManipulationViewport2 *This,
        DWORD cookie);

    HRESULT (STDMETHODCALLTYPE *SetInputMode)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_INPUT_MODE mode);

    HRESULT (STDMETHODCALLTYPE *SetUpdateMode)(
        IDirectManipulationViewport2 *This,
        DIRECTMANIPULATION_INPUT_MODE mode);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IDirectManipulationViewport2 *This);

    HRESULT (STDMETHODCALLTYPE *Abandon)(
        IDirectManipulationViewport2 *This);

    /*** IDirectManipulationViewport2 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddBehavior)(
        IDirectManipulationViewport2 *This,
        IUnknown *behavior,
        DWORD *cookie);

    HRESULT (STDMETHODCALLTYPE *RemoveBehavior)(
        IDirectManipulationViewport2 *This,
        DWORD cookie);

    HRESULT (STDMETHODCALLTYPE *RemoveAllBehaviors)(
        IDirectManipulationViewport2 *This);

    END_INTERFACE
} IDirectManipulationViewport2Vtbl;

interface IDirectManipulationViewport2 {
    CONST_VTBL IDirectManipulationViewport2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationViewport2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationViewport2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationViewport2_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationViewport methods ***/
#define IDirectManipulationViewport2_Enable(This) (This)->lpVtbl->Enable(This)
#define IDirectManipulationViewport2_Disable(This) (This)->lpVtbl->Disable(This)
#define IDirectManipulationViewport2_SetContact(This,id) (This)->lpVtbl->SetContact(This,id)
#define IDirectManipulationViewport2_ReleaseContact(This,id) (This)->lpVtbl->ReleaseContact(This,id)
#define IDirectManipulationViewport2_ReleaseAllContacts(This) (This)->lpVtbl->ReleaseAllContacts(This)
#define IDirectManipulationViewport2_GetStatus(This,status) (This)->lpVtbl->GetStatus(This,status)
#define IDirectManipulationViewport2_GetTag(This,riid,object,id) (This)->lpVtbl->GetTag(This,riid,object,id)
#define IDirectManipulationViewport2_SetTag(This,object,id) (This)->lpVtbl->SetTag(This,object,id)
#define IDirectManipulationViewport2_GetViewportRect(This,viewport) (This)->lpVtbl->GetViewportRect(This,viewport)
#define IDirectManipulationViewport2_SetViewportRect(This,viewport) (This)->lpVtbl->SetViewportRect(This,viewport)
#define IDirectManipulationViewport2_ZoomToRect(This,left,top,right,bottom,animate) (This)->lpVtbl->ZoomToRect(This,left,top,right,bottom,animate)
#define IDirectManipulationViewport2_SetViewportTransform(This,matrix,count) (This)->lpVtbl->SetViewportTransform(This,matrix,count)
#define IDirectManipulationViewport2_SyncDisplayTransform(This,matrix,count) (This)->lpVtbl->SyncDisplayTransform(This,matrix,count)
#define IDirectManipulationViewport2_GetPrimaryContent(This,riid,object) (This)->lpVtbl->GetPrimaryContent(This,riid,object)
#define IDirectManipulationViewport2_AddContent(This,content) (This)->lpVtbl->AddContent(This,content)
#define IDirectManipulationViewport2_RemoveContent(This,content) (This)->lpVtbl->RemoveContent(This,content)
#define IDirectManipulationViewport2_SetViewportOptions(This,options) (This)->lpVtbl->SetViewportOptions(This,options)
#define IDirectManipulationViewport2_AddConfiguration(This,configuration) (This)->lpVtbl->AddConfiguration(This,configuration)
#define IDirectManipulationViewport2_RemoveConfiguration(This,configuration) (This)->lpVtbl->RemoveConfiguration(This,configuration)
#define IDirectManipulationViewport2_ActivateConfiguration(This,configuration) (This)->lpVtbl->ActivateConfiguration(This,configuration)
#define IDirectManipulationViewport2_SetManualGesture(This,configuration) (This)->lpVtbl->SetManualGesture(This,configuration)
#define IDirectManipulationViewport2_SetChaining(This,enabledTypes) (This)->lpVtbl->SetChaining(This,enabledTypes)
#define IDirectManipulationViewport2_AddEventHandler(This,window,eventHandler,cookie) (This)->lpVtbl->AddEventHandler(This,window,eventHandler,cookie)
#define IDirectManipulationViewport2_RemoveEventHandler(This,cookie) (This)->lpVtbl->RemoveEventHandler(This,cookie)
#define IDirectManipulationViewport2_SetInputMode(This,mode) (This)->lpVtbl->SetInputMode(This,mode)
#define IDirectManipulationViewport2_SetUpdateMode(This,mode) (This)->lpVtbl->SetUpdateMode(This,mode)
#define IDirectManipulationViewport2_Stop(This) (This)->lpVtbl->Stop(This)
#define IDirectManipulationViewport2_Abandon(This) (This)->lpVtbl->Abandon(This)
/*** IDirectManipulationViewport2 methods ***/
#define IDirectManipulationViewport2_AddBehavior(This,behavior,cookie) (This)->lpVtbl->AddBehavior(This,behavior,cookie)
#define IDirectManipulationViewport2_RemoveBehavior(This,cookie) (This)->lpVtbl->RemoveBehavior(This,cookie)
#define IDirectManipulationViewport2_RemoveAllBehaviors(This) (This)->lpVtbl->RemoveAllBehaviors(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationViewport2_QueryInterface(IDirectManipulationViewport2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationViewport2_AddRef(IDirectManipulationViewport2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationViewport2_Release(IDirectManipulationViewport2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationViewport methods ***/
static inline HRESULT IDirectManipulationViewport2_Enable(IDirectManipulationViewport2* This) {
    return This->lpVtbl->Enable(This);
}
static inline HRESULT IDirectManipulationViewport2_Disable(IDirectManipulationViewport2* This) {
    return This->lpVtbl->Disable(This);
}
static inline HRESULT IDirectManipulationViewport2_SetContact(IDirectManipulationViewport2* This,UINT32 id) {
    return This->lpVtbl->SetContact(This,id);
}
static inline HRESULT IDirectManipulationViewport2_ReleaseContact(IDirectManipulationViewport2* This,UINT32 id) {
    return This->lpVtbl->ReleaseContact(This,id);
}
static inline HRESULT IDirectManipulationViewport2_ReleaseAllContacts(IDirectManipulationViewport2* This) {
    return This->lpVtbl->ReleaseAllContacts(This);
}
static inline HRESULT IDirectManipulationViewport2_GetStatus(IDirectManipulationViewport2* This,DIRECTMANIPULATION_STATUS *status) {
    return This->lpVtbl->GetStatus(This,status);
}
static inline HRESULT IDirectManipulationViewport2_GetTag(IDirectManipulationViewport2* This,REFIID riid,void **object,UINT32 *id) {
    return This->lpVtbl->GetTag(This,riid,object,id);
}
static inline HRESULT IDirectManipulationViewport2_SetTag(IDirectManipulationViewport2* This,IUnknown *object,UINT32 id) {
    return This->lpVtbl->SetTag(This,object,id);
}
static inline HRESULT IDirectManipulationViewport2_GetViewportRect(IDirectManipulationViewport2* This,RECT *viewport) {
    return This->lpVtbl->GetViewportRect(This,viewport);
}
static inline HRESULT IDirectManipulationViewport2_SetViewportRect(IDirectManipulationViewport2* This,const RECT *viewport) {
    return This->lpVtbl->SetViewportRect(This,viewport);
}
static inline HRESULT IDirectManipulationViewport2_ZoomToRect(IDirectManipulationViewport2* This,const float left,const float top,const float right,const float bottom,WINBOOL animate) {
    return This->lpVtbl->ZoomToRect(This,left,top,right,bottom,animate);
}
static inline HRESULT IDirectManipulationViewport2_SetViewportTransform(IDirectManipulationViewport2* This,const float *matrix,DWORD count) {
    return This->lpVtbl->SetViewportTransform(This,matrix,count);
}
static inline HRESULT IDirectManipulationViewport2_SyncDisplayTransform(IDirectManipulationViewport2* This,const float *matrix,DWORD count) {
    return This->lpVtbl->SyncDisplayTransform(This,matrix,count);
}
static inline HRESULT IDirectManipulationViewport2_GetPrimaryContent(IDirectManipulationViewport2* This,REFIID riid,void **object) {
    return This->lpVtbl->GetPrimaryContent(This,riid,object);
}
static inline HRESULT IDirectManipulationViewport2_AddContent(IDirectManipulationViewport2* This,IDirectManipulationContent *content) {
    return This->lpVtbl->AddContent(This,content);
}
static inline HRESULT IDirectManipulationViewport2_RemoveContent(IDirectManipulationViewport2* This,IDirectManipulationContent *content) {
    return This->lpVtbl->RemoveContent(This,content);
}
static inline HRESULT IDirectManipulationViewport2_SetViewportOptions(IDirectManipulationViewport2* This,DIRECTMANIPULATION_VIEWPORT_OPTIONS options) {
    return This->lpVtbl->SetViewportOptions(This,options);
}
static inline HRESULT IDirectManipulationViewport2_AddConfiguration(IDirectManipulationViewport2* This,DIRECTMANIPULATION_CONFIGURATION configuration) {
    return This->lpVtbl->AddConfiguration(This,configuration);
}
static inline HRESULT IDirectManipulationViewport2_RemoveConfiguration(IDirectManipulationViewport2* This,DIRECTMANIPULATION_CONFIGURATION configuration) {
    return This->lpVtbl->RemoveConfiguration(This,configuration);
}
static inline HRESULT IDirectManipulationViewport2_ActivateConfiguration(IDirectManipulationViewport2* This,DIRECTMANIPULATION_CONFIGURATION configuration) {
    return This->lpVtbl->ActivateConfiguration(This,configuration);
}
static inline HRESULT IDirectManipulationViewport2_SetManualGesture(IDirectManipulationViewport2* This,DIRECTMANIPULATION_GESTURE_CONFIGURATION configuration) {
    return This->lpVtbl->SetManualGesture(This,configuration);
}
static inline HRESULT IDirectManipulationViewport2_SetChaining(IDirectManipulationViewport2* This,DIRECTMANIPULATION_MOTION_TYPES enabledTypes) {
    return This->lpVtbl->SetChaining(This,enabledTypes);
}
static inline HRESULT IDirectManipulationViewport2_AddEventHandler(IDirectManipulationViewport2* This,HWND window,IDirectManipulationViewportEventHandler *eventHandler,DWORD *cookie) {
    return This->lpVtbl->AddEventHandler(This,window,eventHandler,cookie);
}
static inline HRESULT IDirectManipulationViewport2_RemoveEventHandler(IDirectManipulationViewport2* This,DWORD cookie) {
    return This->lpVtbl->RemoveEventHandler(This,cookie);
}
static inline HRESULT IDirectManipulationViewport2_SetInputMode(IDirectManipulationViewport2* This,DIRECTMANIPULATION_INPUT_MODE mode) {
    return This->lpVtbl->SetInputMode(This,mode);
}
static inline HRESULT IDirectManipulationViewport2_SetUpdateMode(IDirectManipulationViewport2* This,DIRECTMANIPULATION_INPUT_MODE mode) {
    return This->lpVtbl->SetUpdateMode(This,mode);
}
static inline HRESULT IDirectManipulationViewport2_Stop(IDirectManipulationViewport2* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IDirectManipulationViewport2_Abandon(IDirectManipulationViewport2* This) {
    return This->lpVtbl->Abandon(This);
}
/*** IDirectManipulationViewport2 methods ***/
static inline HRESULT IDirectManipulationViewport2_AddBehavior(IDirectManipulationViewport2* This,IUnknown *behavior,DWORD *cookie) {
    return This->lpVtbl->AddBehavior(This,behavior,cookie);
}
static inline HRESULT IDirectManipulationViewport2_RemoveBehavior(IDirectManipulationViewport2* This,DWORD cookie) {
    return This->lpVtbl->RemoveBehavior(This,cookie);
}
static inline HRESULT IDirectManipulationViewport2_RemoveAllBehaviors(IDirectManipulationViewport2* This) {
    return This->lpVtbl->RemoveAllBehaviors(This);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationViewport2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationViewportEventHandler interface
 */
#ifndef __IDirectManipulationViewportEventHandler_INTERFACE_DEFINED__
#define __IDirectManipulationViewportEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationViewportEventHandler, 0x952121da, 0xd69f, 0x45f9, 0xb0,0xf9, 0xf2,0x39,0x44,0x32,0x1a,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("952121da-d69f-45f9-b0f9-f23944321a6d")
IDirectManipulationViewportEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnViewportStatusChanged(
        IDirectManipulationViewport *viewport,
        DIRECTMANIPULATION_STATUS current,
        DIRECTMANIPULATION_STATUS previous) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnViewportUpdated(
        IDirectManipulationViewport *viewport) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnContentUpdated(
        IDirectManipulationViewport *viewport,
        IDirectManipulationContent *content) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationViewportEventHandler, 0x952121da, 0xd69f, 0x45f9, 0xb0,0xf9, 0xf2,0x39,0x44,0x32,0x1a,0x6d)
#endif
#else
typedef struct IDirectManipulationViewportEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationViewportEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationViewportEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationViewportEventHandler *This);

    /*** IDirectManipulationViewportEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnViewportStatusChanged)(
        IDirectManipulationViewportEventHandler *This,
        IDirectManipulationViewport *viewport,
        DIRECTMANIPULATION_STATUS current,
        DIRECTMANIPULATION_STATUS previous);

    HRESULT (STDMETHODCALLTYPE *OnViewportUpdated)(
        IDirectManipulationViewportEventHandler *This,
        IDirectManipulationViewport *viewport);

    HRESULT (STDMETHODCALLTYPE *OnContentUpdated)(
        IDirectManipulationViewportEventHandler *This,
        IDirectManipulationViewport *viewport,
        IDirectManipulationContent *content);

    END_INTERFACE
} IDirectManipulationViewportEventHandlerVtbl;

interface IDirectManipulationViewportEventHandler {
    CONST_VTBL IDirectManipulationViewportEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationViewportEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationViewportEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationViewportEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationViewportEventHandler methods ***/
#define IDirectManipulationViewportEventHandler_OnViewportStatusChanged(This,viewport,current,previous) (This)->lpVtbl->OnViewportStatusChanged(This,viewport,current,previous)
#define IDirectManipulationViewportEventHandler_OnViewportUpdated(This,viewport) (This)->lpVtbl->OnViewportUpdated(This,viewport)
#define IDirectManipulationViewportEventHandler_OnContentUpdated(This,viewport,content) (This)->lpVtbl->OnContentUpdated(This,viewport,content)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationViewportEventHandler_QueryInterface(IDirectManipulationViewportEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationViewportEventHandler_AddRef(IDirectManipulationViewportEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationViewportEventHandler_Release(IDirectManipulationViewportEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationViewportEventHandler methods ***/
static inline HRESULT IDirectManipulationViewportEventHandler_OnViewportStatusChanged(IDirectManipulationViewportEventHandler* This,IDirectManipulationViewport *viewport,DIRECTMANIPULATION_STATUS current,DIRECTMANIPULATION_STATUS previous) {
    return This->lpVtbl->OnViewportStatusChanged(This,viewport,current,previous);
}
static inline HRESULT IDirectManipulationViewportEventHandler_OnViewportUpdated(IDirectManipulationViewportEventHandler* This,IDirectManipulationViewport *viewport) {
    return This->lpVtbl->OnViewportUpdated(This,viewport);
}
static inline HRESULT IDirectManipulationViewportEventHandler_OnContentUpdated(IDirectManipulationViewportEventHandler* This,IDirectManipulationViewport *viewport,IDirectManipulationContent *content) {
    return This->lpVtbl->OnContentUpdated(This,viewport,content);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationViewportEventHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationManager2 interface
 */
#ifndef __IDirectManipulationManager2_INTERFACE_DEFINED__
#define __IDirectManipulationManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationManager2, 0xfa1005e9, 0x3d16, 0x484c, 0xbf,0xc9, 0x62,0xb6,0x1e,0x56,0xec,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa1005e9-3d16-484c-bfc9-62b61e56ec4e")
IDirectManipulationManager2 : public IDirectManipulationManager
{
    virtual HRESULT STDMETHODCALLTYPE CreateBehavior(
        REFCLSID clsid,
        REFIID riid,
        void **obj) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationManager2, 0xfa1005e9, 0x3d16, 0x484c, 0xbf,0xc9, 0x62,0xb6,0x1e,0x56,0xec,0x4e)
#endif
#else
typedef struct IDirectManipulationManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationManager2 *This);

    /*** IDirectManipulationManager methods ***/
    HRESULT (STDMETHODCALLTYPE *Activate)(
        IDirectManipulationManager2 *This,
        HWND window);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        IDirectManipulationManager2 *This,
        HWND window);

    HRESULT (STDMETHODCALLTYPE *RegisterHitTestTarget)(
        IDirectManipulationManager2 *This,
        HWND window,
        HWND hittest,
        DIRECTMANIPULATION_HITTEST_TYPE type);

    HRESULT (STDMETHODCALLTYPE *ProcessInput)(
        IDirectManipulationManager2 *This,
        const MSG *msg,
        WINBOOL *handled);

    HRESULT (STDMETHODCALLTYPE *GetUpdateManager)(
        IDirectManipulationManager2 *This,
        REFIID riid,
        void **obj);

    HRESULT (STDMETHODCALLTYPE *CreateViewport)(
        IDirectManipulationManager2 *This,
        IDirectManipulationFrameInfoProvider *frame,
        HWND window,
        REFIID riid,
        void **obj);

    HRESULT (STDMETHODCALLTYPE *CreateContent)(
        IDirectManipulationManager2 *This,
        IDirectManipulationFrameInfoProvider *frame,
        REFCLSID clsid,
        REFIID riid,
        void **obj);

    /*** IDirectManipulationManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBehavior)(
        IDirectManipulationManager2 *This,
        REFCLSID clsid,
        REFIID riid,
        void **obj);

    END_INTERFACE
} IDirectManipulationManager2Vtbl;

interface IDirectManipulationManager2 {
    CONST_VTBL IDirectManipulationManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationManager methods ***/
#define IDirectManipulationManager2_Activate(This,window) (This)->lpVtbl->Activate(This,window)
#define IDirectManipulationManager2_Deactivate(This,window) (This)->lpVtbl->Deactivate(This,window)
#define IDirectManipulationManager2_RegisterHitTestTarget(This,window,hittest,type) (This)->lpVtbl->RegisterHitTestTarget(This,window,hittest,type)
#define IDirectManipulationManager2_ProcessInput(This,msg,handled) (This)->lpVtbl->ProcessInput(This,msg,handled)
#define IDirectManipulationManager2_GetUpdateManager(This,riid,obj) (This)->lpVtbl->GetUpdateManager(This,riid,obj)
#define IDirectManipulationManager2_CreateViewport(This,frame,window,riid,obj) (This)->lpVtbl->CreateViewport(This,frame,window,riid,obj)
#define IDirectManipulationManager2_CreateContent(This,frame,clsid,riid,obj) (This)->lpVtbl->CreateContent(This,frame,clsid,riid,obj)
/*** IDirectManipulationManager2 methods ***/
#define IDirectManipulationManager2_CreateBehavior(This,clsid,riid,obj) (This)->lpVtbl->CreateBehavior(This,clsid,riid,obj)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationManager2_QueryInterface(IDirectManipulationManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationManager2_AddRef(IDirectManipulationManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationManager2_Release(IDirectManipulationManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationManager methods ***/
static inline HRESULT IDirectManipulationManager2_Activate(IDirectManipulationManager2* This,HWND window) {
    return This->lpVtbl->Activate(This,window);
}
static inline HRESULT IDirectManipulationManager2_Deactivate(IDirectManipulationManager2* This,HWND window) {
    return This->lpVtbl->Deactivate(This,window);
}
static inline HRESULT IDirectManipulationManager2_RegisterHitTestTarget(IDirectManipulationManager2* This,HWND window,HWND hittest,DIRECTMANIPULATION_HITTEST_TYPE type) {
    return This->lpVtbl->RegisterHitTestTarget(This,window,hittest,type);
}
static inline HRESULT IDirectManipulationManager2_ProcessInput(IDirectManipulationManager2* This,const MSG *msg,WINBOOL *handled) {
    return This->lpVtbl->ProcessInput(This,msg,handled);
}
static inline HRESULT IDirectManipulationManager2_GetUpdateManager(IDirectManipulationManager2* This,REFIID riid,void **obj) {
    return This->lpVtbl->GetUpdateManager(This,riid,obj);
}
static inline HRESULT IDirectManipulationManager2_CreateViewport(IDirectManipulationManager2* This,IDirectManipulationFrameInfoProvider *frame,HWND window,REFIID riid,void **obj) {
    return This->lpVtbl->CreateViewport(This,frame,window,riid,obj);
}
static inline HRESULT IDirectManipulationManager2_CreateContent(IDirectManipulationManager2* This,IDirectManipulationFrameInfoProvider *frame,REFCLSID clsid,REFIID riid,void **obj) {
    return This->lpVtbl->CreateContent(This,frame,clsid,riid,obj);
}
/*** IDirectManipulationManager2 methods ***/
static inline HRESULT IDirectManipulationManager2_CreateBehavior(IDirectManipulationManager2* This,REFCLSID clsid,REFIID riid,void **obj) {
    return This->lpVtbl->CreateBehavior(This,clsid,riid,obj);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationManager2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationPrimaryContent interface
 */
#ifndef __IDirectManipulationPrimaryContent_INTERFACE_DEFINED__
#define __IDirectManipulationPrimaryContent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationPrimaryContent, 0xc12851e4, 0x1698, 0x4625, 0xb9,0xb1, 0x7c,0xa3,0xec,0x18,0x63,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c12851e4-1698-4625-b9b1-7ca3ec18630b")
IDirectManipulationPrimaryContent : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetSnapInterval(
        DIRECTMANIPULATION_MOTION_TYPES motion,
        float interval,
        float offset) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSnapPoints(
        DIRECTMANIPULATION_MOTION_TYPES motion,
        const float *points,
        DWORD count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSnapType(
        DIRECTMANIPULATION_MOTION_TYPES motion,
        DIRECTMANIPULATION_SNAPPOINT_TYPE type) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSnapCoordinate(
        DIRECTMANIPULATION_MOTION_TYPES motion,
        DIRECTMANIPULATION_SNAPPOINT_COORDINATE coordinate,
        float origin) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetZoomBoundaries(
        float minimum,
        float maximum) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHorizontalAlignment(
        DIRECTMANIPULATION_HORIZONTALALIGNMENT alignment) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVerticalAlignment(
        DIRECTMANIPULATION_VERTICALALIGNMENT alignment) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInertiaEndTransform(
        float *matrix,
        DWORD count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCenterPoint(
        float *x,
        float *y) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationPrimaryContent, 0xc12851e4, 0x1698, 0x4625, 0xb9,0xb1, 0x7c,0xa3,0xec,0x18,0x63,0x0b)
#endif
#else
typedef struct IDirectManipulationPrimaryContentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationPrimaryContent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationPrimaryContent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationPrimaryContent *This);

    /*** IDirectManipulationPrimaryContent methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSnapInterval)(
        IDirectManipulationPrimaryContent *This,
        DIRECTMANIPULATION_MOTION_TYPES motion,
        float interval,
        float offset);

    HRESULT (STDMETHODCALLTYPE *SetSnapPoints)(
        IDirectManipulationPrimaryContent *This,
        DIRECTMANIPULATION_MOTION_TYPES motion,
        const float *points,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *SetSnapType)(
        IDirectManipulationPrimaryContent *This,
        DIRECTMANIPULATION_MOTION_TYPES motion,
        DIRECTMANIPULATION_SNAPPOINT_TYPE type);

    HRESULT (STDMETHODCALLTYPE *SetSnapCoordinate)(
        IDirectManipulationPrimaryContent *This,
        DIRECTMANIPULATION_MOTION_TYPES motion,
        DIRECTMANIPULATION_SNAPPOINT_COORDINATE coordinate,
        float origin);

    HRESULT (STDMETHODCALLTYPE *SetZoomBoundaries)(
        IDirectManipulationPrimaryContent *This,
        float minimum,
        float maximum);

    HRESULT (STDMETHODCALLTYPE *SetHorizontalAlignment)(
        IDirectManipulationPrimaryContent *This,
        DIRECTMANIPULATION_HORIZONTALALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetVerticalAlignment)(
        IDirectManipulationPrimaryContent *This,
        DIRECTMANIPULATION_VERTICALALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *GetInertiaEndTransform)(
        IDirectManipulationPrimaryContent *This,
        float *matrix,
        DWORD count);

    HRESULT (STDMETHODCALLTYPE *GetCenterPoint)(
        IDirectManipulationPrimaryContent *This,
        float *x,
        float *y);

    END_INTERFACE
} IDirectManipulationPrimaryContentVtbl;

interface IDirectManipulationPrimaryContent {
    CONST_VTBL IDirectManipulationPrimaryContentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationPrimaryContent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationPrimaryContent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationPrimaryContent_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationPrimaryContent methods ***/
#define IDirectManipulationPrimaryContent_SetSnapInterval(This,motion,interval,offset) (This)->lpVtbl->SetSnapInterval(This,motion,interval,offset)
#define IDirectManipulationPrimaryContent_SetSnapPoints(This,motion,points,count) (This)->lpVtbl->SetSnapPoints(This,motion,points,count)
#define IDirectManipulationPrimaryContent_SetSnapType(This,motion,type) (This)->lpVtbl->SetSnapType(This,motion,type)
#define IDirectManipulationPrimaryContent_SetSnapCoordinate(This,motion,coordinate,origin) (This)->lpVtbl->SetSnapCoordinate(This,motion,coordinate,origin)
#define IDirectManipulationPrimaryContent_SetZoomBoundaries(This,minimum,maximum) (This)->lpVtbl->SetZoomBoundaries(This,minimum,maximum)
#define IDirectManipulationPrimaryContent_SetHorizontalAlignment(This,alignment) (This)->lpVtbl->SetHorizontalAlignment(This,alignment)
#define IDirectManipulationPrimaryContent_SetVerticalAlignment(This,alignment) (This)->lpVtbl->SetVerticalAlignment(This,alignment)
#define IDirectManipulationPrimaryContent_GetInertiaEndTransform(This,matrix,count) (This)->lpVtbl->GetInertiaEndTransform(This,matrix,count)
#define IDirectManipulationPrimaryContent_GetCenterPoint(This,x,y) (This)->lpVtbl->GetCenterPoint(This,x,y)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationPrimaryContent_QueryInterface(IDirectManipulationPrimaryContent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationPrimaryContent_AddRef(IDirectManipulationPrimaryContent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationPrimaryContent_Release(IDirectManipulationPrimaryContent* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationPrimaryContent methods ***/
static inline HRESULT IDirectManipulationPrimaryContent_SetSnapInterval(IDirectManipulationPrimaryContent* This,DIRECTMANIPULATION_MOTION_TYPES motion,float interval,float offset) {
    return This->lpVtbl->SetSnapInterval(This,motion,interval,offset);
}
static inline HRESULT IDirectManipulationPrimaryContent_SetSnapPoints(IDirectManipulationPrimaryContent* This,DIRECTMANIPULATION_MOTION_TYPES motion,const float *points,DWORD count) {
    return This->lpVtbl->SetSnapPoints(This,motion,points,count);
}
static inline HRESULT IDirectManipulationPrimaryContent_SetSnapType(IDirectManipulationPrimaryContent* This,DIRECTMANIPULATION_MOTION_TYPES motion,DIRECTMANIPULATION_SNAPPOINT_TYPE type) {
    return This->lpVtbl->SetSnapType(This,motion,type);
}
static inline HRESULT IDirectManipulationPrimaryContent_SetSnapCoordinate(IDirectManipulationPrimaryContent* This,DIRECTMANIPULATION_MOTION_TYPES motion,DIRECTMANIPULATION_SNAPPOINT_COORDINATE coordinate,float origin) {
    return This->lpVtbl->SetSnapCoordinate(This,motion,coordinate,origin);
}
static inline HRESULT IDirectManipulationPrimaryContent_SetZoomBoundaries(IDirectManipulationPrimaryContent* This,float minimum,float maximum) {
    return This->lpVtbl->SetZoomBoundaries(This,minimum,maximum);
}
static inline HRESULT IDirectManipulationPrimaryContent_SetHorizontalAlignment(IDirectManipulationPrimaryContent* This,DIRECTMANIPULATION_HORIZONTALALIGNMENT alignment) {
    return This->lpVtbl->SetHorizontalAlignment(This,alignment);
}
static inline HRESULT IDirectManipulationPrimaryContent_SetVerticalAlignment(IDirectManipulationPrimaryContent* This,DIRECTMANIPULATION_VERTICALALIGNMENT alignment) {
    return This->lpVtbl->SetVerticalAlignment(This,alignment);
}
static inline HRESULT IDirectManipulationPrimaryContent_GetInertiaEndTransform(IDirectManipulationPrimaryContent* This,float *matrix,DWORD count) {
    return This->lpVtbl->GetInertiaEndTransform(This,matrix,count);
}
static inline HRESULT IDirectManipulationPrimaryContent_GetCenterPoint(IDirectManipulationPrimaryContent* This,float *x,float *y) {
    return This->lpVtbl->GetCenterPoint(This,x,y);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationPrimaryContent_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationCompositor2 interface
 */
#ifndef __IDirectManipulationCompositor2_INTERFACE_DEFINED__
#define __IDirectManipulationCompositor2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationCompositor2, 0xd38c7822, 0xf1cb, 0x43cb, 0xb4,0xb9, 0xac,0x0c,0x76,0x7a,0x41,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d38c7822-f1cb-43cb-b4b9-ac0c767a412e")
IDirectManipulationCompositor2 : public IDirectManipulationCompositor
{
    virtual HRESULT STDMETHODCALLTYPE AddContentWithCrossProcessChaining(
        IDirectManipulationPrimaryContent *content,
        IUnknown *device,
        IUnknown *parentVisual,
        IUnknown *childVisual) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationCompositor2, 0xd38c7822, 0xf1cb, 0x43cb, 0xb4,0xb9, 0xac,0x0c,0x76,0x7a,0x41,0x2e)
#endif
#else
typedef struct IDirectManipulationCompositor2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationCompositor2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationCompositor2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationCompositor2 *This);

    /*** IDirectManipulationCompositor methods ***/
    HRESULT (STDMETHODCALLTYPE *AddContent)(
        IDirectManipulationCompositor2 *This,
        IDirectManipulationContent *content,
        IUnknown *device,
        IUnknown *parent,
        IUnknown *child);

    HRESULT (STDMETHODCALLTYPE *RemoveContent)(
        IDirectManipulationCompositor2 *This,
        IDirectManipulationContent *content);

    HRESULT (STDMETHODCALLTYPE *SetUpdateManager)(
        IDirectManipulationCompositor2 *This,
        IDirectManipulationUpdateManager *manager);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IDirectManipulationCompositor2 *This);

    /*** IDirectManipulationCompositor2 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddContentWithCrossProcessChaining)(
        IDirectManipulationCompositor2 *This,
        IDirectManipulationPrimaryContent *content,
        IUnknown *device,
        IUnknown *parentVisual,
        IUnknown *childVisual);

    END_INTERFACE
} IDirectManipulationCompositor2Vtbl;

interface IDirectManipulationCompositor2 {
    CONST_VTBL IDirectManipulationCompositor2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationCompositor2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationCompositor2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationCompositor2_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationCompositor methods ***/
#define IDirectManipulationCompositor2_AddContent(This,content,device,parent,child) (This)->lpVtbl->AddContent(This,content,device,parent,child)
#define IDirectManipulationCompositor2_RemoveContent(This,content) (This)->lpVtbl->RemoveContent(This,content)
#define IDirectManipulationCompositor2_SetUpdateManager(This,manager) (This)->lpVtbl->SetUpdateManager(This,manager)
#define IDirectManipulationCompositor2_Flush(This) (This)->lpVtbl->Flush(This)
/*** IDirectManipulationCompositor2 methods ***/
#define IDirectManipulationCompositor2_AddContentWithCrossProcessChaining(This,content,device,parentVisual,childVisual) (This)->lpVtbl->AddContentWithCrossProcessChaining(This,content,device,parentVisual,childVisual)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationCompositor2_QueryInterface(IDirectManipulationCompositor2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationCompositor2_AddRef(IDirectManipulationCompositor2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationCompositor2_Release(IDirectManipulationCompositor2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationCompositor methods ***/
static inline HRESULT IDirectManipulationCompositor2_AddContent(IDirectManipulationCompositor2* This,IDirectManipulationContent *content,IUnknown *device,IUnknown *parent,IUnknown *child) {
    return This->lpVtbl->AddContent(This,content,device,parent,child);
}
static inline HRESULT IDirectManipulationCompositor2_RemoveContent(IDirectManipulationCompositor2* This,IDirectManipulationContent *content) {
    return This->lpVtbl->RemoveContent(This,content);
}
static inline HRESULT IDirectManipulationCompositor2_SetUpdateManager(IDirectManipulationCompositor2* This,IDirectManipulationUpdateManager *manager) {
    return This->lpVtbl->SetUpdateManager(This,manager);
}
static inline HRESULT IDirectManipulationCompositor2_Flush(IDirectManipulationCompositor2* This) {
    return This->lpVtbl->Flush(This);
}
/*** IDirectManipulationCompositor2 methods ***/
static inline HRESULT IDirectManipulationCompositor2_AddContentWithCrossProcessChaining(IDirectManipulationCompositor2* This,IDirectManipulationPrimaryContent *content,IUnknown *device,IUnknown *parentVisual,IUnknown *childVisual) {
    return This->lpVtbl->AddContentWithCrossProcessChaining(This,content,device,parentVisual,childVisual);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationCompositor2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDirectManipulationInteractionEventHandler interface
 */
#ifndef __IDirectManipulationInteractionEventHandler_INTERFACE_DEFINED__
#define __IDirectManipulationInteractionEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectManipulationInteractionEventHandler, 0xe43f45b8, 0x42b4, 0x403e, 0xb1,0xf2, 0x27,0x3b,0x8f,0x51,0x08,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e43f45b8-42b4-403e-b1f2-273b8f510830")
IDirectManipulationInteractionEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnInteraction(
        IDirectManipulationViewport2 *viewport,
        DIRECTMANIPULATION_INTERACTION_TYPE interaction) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectManipulationInteractionEventHandler, 0xe43f45b8, 0x42b4, 0x403e, 0xb1,0xf2, 0x27,0x3b,0x8f,0x51,0x08,0x30)
#endif
#else
typedef struct IDirectManipulationInteractionEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectManipulationInteractionEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectManipulationInteractionEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectManipulationInteractionEventHandler *This);

    /*** IDirectManipulationInteractionEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnInteraction)(
        IDirectManipulationInteractionEventHandler *This,
        IDirectManipulationViewport2 *viewport,
        DIRECTMANIPULATION_INTERACTION_TYPE interaction);

    END_INTERFACE
} IDirectManipulationInteractionEventHandlerVtbl;

interface IDirectManipulationInteractionEventHandler {
    CONST_VTBL IDirectManipulationInteractionEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectManipulationInteractionEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectManipulationInteractionEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectManipulationInteractionEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectManipulationInteractionEventHandler methods ***/
#define IDirectManipulationInteractionEventHandler_OnInteraction(This,viewport,interaction) (This)->lpVtbl->OnInteraction(This,viewport,interaction)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectManipulationInteractionEventHandler_QueryInterface(IDirectManipulationInteractionEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectManipulationInteractionEventHandler_AddRef(IDirectManipulationInteractionEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectManipulationInteractionEventHandler_Release(IDirectManipulationInteractionEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectManipulationInteractionEventHandler methods ***/
static inline HRESULT IDirectManipulationInteractionEventHandler_OnInteraction(IDirectManipulationInteractionEventHandler* This,IDirectManipulationViewport2 *viewport,DIRECTMANIPULATION_INTERACTION_TYPE interaction) {
    return This->lpVtbl->OnInteraction(This,viewport,interaction);
}
#endif
#endif

#endif


#endif  /* __IDirectManipulationInteractionEventHandler_INTERFACE_DEFINED__ */

#ifndef __DirectManipulation_LIBRARY_DEFINED__
#define __DirectManipulation_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_DirectManipulation, 0x9fbedf98, 0xf6d8, 0x4e3b, 0xb4,0x88, 0xfa,0x66,0xdb,0xf5,0xe9,0xf3);

/*****************************************************************************
 * DirectManipulationManager coclass
 */

DEFINE_GUID(CLSID_DirectManipulationManager, 0x54e211b6, 0x3650, 0x4f75, 0x83,0x34, 0xfa,0x35,0x95,0x98,0xe1,0xc5);

#ifdef __cplusplus
class DECLSPEC_UUID("54e211b6-3650-4f75-8334-fa359598e1c5") DirectManipulationManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DirectManipulationManager, 0x54e211b6, 0x3650, 0x4f75, 0x83,0x34, 0xfa,0x35,0x95,0x98,0xe1,0xc5)
#endif
#endif

/*****************************************************************************
 * DirectManipulationSharedManager coclass
 */

DEFINE_GUID(CLSID_DirectManipulationSharedManager, 0x99793286, 0x77cc, 0x4b57, 0x96,0xdb, 0x3b,0x35,0x4f,0x6f,0x9f,0xb5);

#ifdef __cplusplus
class DECLSPEC_UUID("99793286-77cc-4b57-96db-3b354f6f9fb5") DirectManipulationSharedManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DirectManipulationSharedManager, 0x99793286, 0x77cc, 0x4b57, 0x96,0xdb, 0x3b,0x35,0x4f,0x6f,0x9f,0xb5)
#endif
#endif

/*****************************************************************************
 * DCompManipulationCompositor coclass
 */

DEFINE_GUID(CLSID_DCompManipulationCompositor, 0x79dea627, 0xa08a, 0x43ac, 0x8e,0xf5, 0x69,0x00,0xb9,0x29,0x91,0x26);

#ifdef __cplusplus
class DECLSPEC_UUID("79dea627-a08a-43ac-8ef5-6900b9299126") DCompManipulationCompositor;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DCompManipulationCompositor, 0x79dea627, 0xa08a, 0x43ac, 0x8e,0xf5, 0x69,0x00,0xb9,0x29,0x91,0x26)
#endif
#endif

/*****************************************************************************
 * DirectManipulationViewport coclass
 */

DEFINE_GUID(CLSID_DirectManipulationViewport, 0x34e211b6, 0x3650, 0x4f75, 0x83,0x34, 0xfa,0x35,0x95,0x98,0xe1,0xc5);

#ifdef __cplusplus
class DECLSPEC_UUID("34e211b6-3650-4f75-8334-fa359598e1c5") DirectManipulationViewport;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DirectManipulationViewport, 0x34e211b6, 0x3650, 0x4f75, 0x83,0x34, 0xfa,0x35,0x95,0x98,0xe1,0xc5)
#endif
#endif

#endif /* __DirectManipulation_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __directmanipulation_h__ */
