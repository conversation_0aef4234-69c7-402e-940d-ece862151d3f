/*** Autogenerated by WIDL 10.12 from include/vidcap.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __vidcap_h__
#define __vidcap_h__

/* Forward declarations */

#ifndef __IKsTopologyInfo_FWD_DEFINED__
#define __IKsTopologyInfo_FWD_DEFINED__
typedef interface IKsTopologyInfo IKsTopologyInfo;
#ifdef __cplusplus
interface IKsTopologyInfo;
#endif /* __cplusplus */
#endif

#ifndef __ISelector_FWD_DEFINED__
#define __ISelector_FWD_DEFINED__
typedef interface ISelector ISelector;
#ifdef __cplusplus
interface ISelector;
#endif /* __cplusplus */
#endif

#ifndef __ICameraControl_FWD_DEFINED__
#define __ICameraControl_FWD_DEFINED__
typedef interface ICameraControl ICameraControl;
#ifdef __cplusplus
interface ICameraControl;
#endif /* __cplusplus */
#endif

#ifndef __IVideoProcAmp_FWD_DEFINED__
#define __IVideoProcAmp_FWD_DEFINED__
typedef interface IVideoProcAmp IVideoProcAmp;
#ifdef __cplusplus
interface IVideoProcAmp;
#endif /* __cplusplus */
#endif

#ifndef __IKsNodeControl_FWD_DEFINED__
#define __IKsNodeControl_FWD_DEFINED__
typedef interface IKsNodeControl IKsNodeControl;
#ifdef __cplusplus
interface IKsNodeControl;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <strmif.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#include "ks.h"
#ifndef _KS_
typedef struct __WIDL_vidcap_generated_name_00000016 {
    ULONG FromNode;
    ULONG FromNodePin;
    ULONG ToNode;
    ULONG ToNodePin;
} KSTOPOLOGY_CONNECTION;
typedef struct __WIDL_vidcap_generated_name_00000016 *PKSTOPOLOGY_CONNECTION;
#endif
/*****************************************************************************
 * IKsTopologyInfo interface
 */
#ifndef __IKsTopologyInfo_INTERFACE_DEFINED__
#define __IKsTopologyInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IKsTopologyInfo, 0x720d4ac0, 0x7533, 0x11d0, 0xa5,0xd6, 0x28,0xdb,0x04,0xc1,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("720d4ac0-7533-11d0-a5d6-28db04c10000")
IKsTopologyInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_NumCategories(
        DWORD *pdwNumCategories) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Category(
        DWORD dwIndex,
        GUID *pCategory) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NumConnections(
        DWORD *pdwNumConnections) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ConnectionInfo(
        DWORD dwIndex,
        KSTOPOLOGY_CONNECTION *pConnectionInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NodeName(
        DWORD dwNodeId,
        WCHAR *pwchNodeName,
        DWORD dwBufSize,
        DWORD *pdwNameLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NumNodes(
        DWORD *pdwNumNodes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NodeType(
        DWORD dwNodeId,
        GUID *pNodeType) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNodeInstance(
        DWORD dwNodeId,
        REFIID iid,
        void **ppvObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IKsTopologyInfo, 0x720d4ac0, 0x7533, 0x11d0, 0xa5,0xd6, 0x28,0xdb,0x04,0xc1,0x00,0x00)
#endif
#else
typedef struct IKsTopologyInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IKsTopologyInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IKsTopologyInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IKsTopologyInfo *This);

    /*** IKsTopologyInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NumCategories)(
        IKsTopologyInfo *This,
        DWORD *pdwNumCategories);

    HRESULT (STDMETHODCALLTYPE *get_Category)(
        IKsTopologyInfo *This,
        DWORD dwIndex,
        GUID *pCategory);

    HRESULT (STDMETHODCALLTYPE *get_NumConnections)(
        IKsTopologyInfo *This,
        DWORD *pdwNumConnections);

    HRESULT (STDMETHODCALLTYPE *get_ConnectionInfo)(
        IKsTopologyInfo *This,
        DWORD dwIndex,
        KSTOPOLOGY_CONNECTION *pConnectionInfo);

    HRESULT (STDMETHODCALLTYPE *get_NodeName)(
        IKsTopologyInfo *This,
        DWORD dwNodeId,
        WCHAR *pwchNodeName,
        DWORD dwBufSize,
        DWORD *pdwNameLen);

    HRESULT (STDMETHODCALLTYPE *get_NumNodes)(
        IKsTopologyInfo *This,
        DWORD *pdwNumNodes);

    HRESULT (STDMETHODCALLTYPE *get_NodeType)(
        IKsTopologyInfo *This,
        DWORD dwNodeId,
        GUID *pNodeType);

    HRESULT (STDMETHODCALLTYPE *CreateNodeInstance)(
        IKsTopologyInfo *This,
        DWORD dwNodeId,
        REFIID iid,
        void **ppvObject);

    END_INTERFACE
} IKsTopologyInfoVtbl;

interface IKsTopologyInfo {
    CONST_VTBL IKsTopologyInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IKsTopologyInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IKsTopologyInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IKsTopologyInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IKsTopologyInfo methods ***/
#define IKsTopologyInfo_get_NumCategories(This,pdwNumCategories) (This)->lpVtbl->get_NumCategories(This,pdwNumCategories)
#define IKsTopologyInfo_get_Category(This,dwIndex,pCategory) (This)->lpVtbl->get_Category(This,dwIndex,pCategory)
#define IKsTopologyInfo_get_NumConnections(This,pdwNumConnections) (This)->lpVtbl->get_NumConnections(This,pdwNumConnections)
#define IKsTopologyInfo_get_ConnectionInfo(This,dwIndex,pConnectionInfo) (This)->lpVtbl->get_ConnectionInfo(This,dwIndex,pConnectionInfo)
#define IKsTopologyInfo_get_NodeName(This,dwNodeId,pwchNodeName,dwBufSize,pdwNameLen) (This)->lpVtbl->get_NodeName(This,dwNodeId,pwchNodeName,dwBufSize,pdwNameLen)
#define IKsTopologyInfo_get_NumNodes(This,pdwNumNodes) (This)->lpVtbl->get_NumNodes(This,pdwNumNodes)
#define IKsTopologyInfo_get_NodeType(This,dwNodeId,pNodeType) (This)->lpVtbl->get_NodeType(This,dwNodeId,pNodeType)
#define IKsTopologyInfo_CreateNodeInstance(This,dwNodeId,iid,ppvObject) (This)->lpVtbl->CreateNodeInstance(This,dwNodeId,iid,ppvObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IKsTopologyInfo_QueryInterface(IKsTopologyInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IKsTopologyInfo_AddRef(IKsTopologyInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IKsTopologyInfo_Release(IKsTopologyInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IKsTopologyInfo methods ***/
static inline HRESULT IKsTopologyInfo_get_NumCategories(IKsTopologyInfo* This,DWORD *pdwNumCategories) {
    return This->lpVtbl->get_NumCategories(This,pdwNumCategories);
}
static inline HRESULT IKsTopologyInfo_get_Category(IKsTopologyInfo* This,DWORD dwIndex,GUID *pCategory) {
    return This->lpVtbl->get_Category(This,dwIndex,pCategory);
}
static inline HRESULT IKsTopologyInfo_get_NumConnections(IKsTopologyInfo* This,DWORD *pdwNumConnections) {
    return This->lpVtbl->get_NumConnections(This,pdwNumConnections);
}
static inline HRESULT IKsTopologyInfo_get_ConnectionInfo(IKsTopologyInfo* This,DWORD dwIndex,KSTOPOLOGY_CONNECTION *pConnectionInfo) {
    return This->lpVtbl->get_ConnectionInfo(This,dwIndex,pConnectionInfo);
}
static inline HRESULT IKsTopologyInfo_get_NodeName(IKsTopologyInfo* This,DWORD dwNodeId,WCHAR *pwchNodeName,DWORD dwBufSize,DWORD *pdwNameLen) {
    return This->lpVtbl->get_NodeName(This,dwNodeId,pwchNodeName,dwBufSize,pdwNameLen);
}
static inline HRESULT IKsTopologyInfo_get_NumNodes(IKsTopologyInfo* This,DWORD *pdwNumNodes) {
    return This->lpVtbl->get_NumNodes(This,pdwNumNodes);
}
static inline HRESULT IKsTopologyInfo_get_NodeType(IKsTopologyInfo* This,DWORD dwNodeId,GUID *pNodeType) {
    return This->lpVtbl->get_NodeType(This,dwNodeId,pNodeType);
}
static inline HRESULT IKsTopologyInfo_CreateNodeInstance(IKsTopologyInfo* This,DWORD dwNodeId,REFIID iid,void **ppvObject) {
    return This->lpVtbl->CreateNodeInstance(This,dwNodeId,iid,ppvObject);
}
#endif
#endif

#endif


#endif  /* __IKsTopologyInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISelector interface
 */
#ifndef __ISelector_INTERFACE_DEFINED__
#define __ISelector_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISelector, 0x1abdaeca, 0x68b6, 0x4f83, 0x93,0x71, 0xb4,0x13,0x90,0x7c,0x7b,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1abdaeca-68b6-4f83-9371-b413907c7b9f")
ISelector : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_NumSources(
        DWORD *pdwNumSources) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SourceNodeId(
        DWORD *pdwPinId) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SourceNodeId(
        DWORD dwPinId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISelector, 0x1abdaeca, 0x68b6, 0x4f83, 0x93,0x71, 0xb4,0x13,0x90,0x7c,0x7b,0x9f)
#endif
#else
typedef struct ISelectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISelector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISelector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISelector *This);

    /*** ISelector methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NumSources)(
        ISelector *This,
        DWORD *pdwNumSources);

    HRESULT (STDMETHODCALLTYPE *get_SourceNodeId)(
        ISelector *This,
        DWORD *pdwPinId);

    HRESULT (STDMETHODCALLTYPE *put_SourceNodeId)(
        ISelector *This,
        DWORD dwPinId);

    END_INTERFACE
} ISelectorVtbl;

interface ISelector {
    CONST_VTBL ISelectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISelector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISelector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISelector_Release(This) (This)->lpVtbl->Release(This)
/*** ISelector methods ***/
#define ISelector_get_NumSources(This,pdwNumSources) (This)->lpVtbl->get_NumSources(This,pdwNumSources)
#define ISelector_get_SourceNodeId(This,pdwPinId) (This)->lpVtbl->get_SourceNodeId(This,pdwPinId)
#define ISelector_put_SourceNodeId(This,dwPinId) (This)->lpVtbl->put_SourceNodeId(This,dwPinId)
#else
/*** IUnknown methods ***/
static inline HRESULT ISelector_QueryInterface(ISelector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISelector_AddRef(ISelector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISelector_Release(ISelector* This) {
    return This->lpVtbl->Release(This);
}
/*** ISelector methods ***/
static inline HRESULT ISelector_get_NumSources(ISelector* This,DWORD *pdwNumSources) {
    return This->lpVtbl->get_NumSources(This,pdwNumSources);
}
static inline HRESULT ISelector_get_SourceNodeId(ISelector* This,DWORD *pdwPinId) {
    return This->lpVtbl->get_SourceNodeId(This,pdwPinId);
}
static inline HRESULT ISelector_put_SourceNodeId(ISelector* This,DWORD dwPinId) {
    return This->lpVtbl->put_SourceNodeId(This,dwPinId);
}
#endif
#endif

#endif


#endif  /* __ISelector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICameraControl interface
 */
#ifndef __ICameraControl_INTERFACE_DEFINED__
#define __ICameraControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICameraControl, 0x2ba1785d, 0x4d1b, 0x44ef, 0x85,0xe8, 0xc7,0xf1,0xd3,0xf2,0x01,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2ba1785d-4d1b-44ef-85e8-c7f1d3f20184")
ICameraControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Exposure(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Exposure(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Exposure(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Focus(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Focus(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Focus(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Iris(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Iris(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Iris(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Zoom(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Zoom(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Zoom(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FocalLengths(
        LONG *plOcularFocalLength,
        LONG *plObjectiveFocalLengthMin,
        LONG *plObjectiveFocalLengthMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Pan(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Pan(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Pan(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Tilt(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Tilt(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Tilt(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PanTilt(
        LONG *pPanValue,
        LONG *pTiltValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PanTilt(
        LONG PanValue,
        LONG TiltValue,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Roll(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Roll(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Roll(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExposureRelative(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExposureRelative(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_ExposureRelative(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FocusRelative(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FocusRelative(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_FocusRelative(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IrisRelative(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IrisRelative(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_IrisRelative(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ZoomRelative(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ZoomRelative(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_ZoomRelative(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PanRelative(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PanRelative(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TiltRelative(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_TiltRelative(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_TiltRelative(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PanTiltRelative(
        LONG *pPanValue,
        LONG *pTiltValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PanTiltRelative(
        LONG PanValue,
        LONG TiltValue,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_PanRelative(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RollRelative(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RollRelative(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_RollRelative(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ScanMode(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ScanMode(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PrivacyMode(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PrivacyMode(
        LONG Value,
        LONG Flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICameraControl, 0x2ba1785d, 0x4d1b, 0x44ef, 0x85,0xe8, 0xc7,0xf1,0xd3,0xf2,0x01,0x84)
#endif
#else
typedef struct ICameraControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICameraControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICameraControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICameraControl *This);

    /*** ICameraControl methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Exposure)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Exposure)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Exposure)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Focus)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Focus)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Focus)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Iris)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Iris)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Iris)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Zoom)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Zoom)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Zoom)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_FocalLengths)(
        ICameraControl *This,
        LONG *plOcularFocalLength,
        LONG *plObjectiveFocalLengthMin,
        LONG *plObjectiveFocalLengthMax);

    HRESULT (STDMETHODCALLTYPE *get_Pan)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Pan)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Pan)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Tilt)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Tilt)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Tilt)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_PanTilt)(
        ICameraControl *This,
        LONG *pPanValue,
        LONG *pTiltValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_PanTilt)(
        ICameraControl *This,
        LONG PanValue,
        LONG TiltValue,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *get_Roll)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Roll)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Roll)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_ExposureRelative)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_ExposureRelative)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_ExposureRelative)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_FocusRelative)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_FocusRelative)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_FocusRelative)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_IrisRelative)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_IrisRelative)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_IrisRelative)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_ZoomRelative)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_ZoomRelative)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_ZoomRelative)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_PanRelative)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_PanRelative)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *get_TiltRelative)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_TiltRelative)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_TiltRelative)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_PanTiltRelative)(
        ICameraControl *This,
        LONG *pPanValue,
        LONG *pTiltValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_PanTiltRelative)(
        ICameraControl *This,
        LONG PanValue,
        LONG TiltValue,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_PanRelative)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_RollRelative)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_RollRelative)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_RollRelative)(
        ICameraControl *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_ScanMode)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_ScanMode)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *get_PrivacyMode)(
        ICameraControl *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_PrivacyMode)(
        ICameraControl *This,
        LONG Value,
        LONG Flags);

    END_INTERFACE
} ICameraControlVtbl;

interface ICameraControl {
    CONST_VTBL ICameraControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICameraControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICameraControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICameraControl_Release(This) (This)->lpVtbl->Release(This)
/*** ICameraControl methods ***/
#define ICameraControl_get_Exposure(This,pValue,pFlags) (This)->lpVtbl->get_Exposure(This,pValue,pFlags)
#define ICameraControl_put_Exposure(This,Value,Flags) (This)->lpVtbl->put_Exposure(This,Value,Flags)
#define ICameraControl_getRange_Exposure(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Exposure(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_Focus(This,pValue,pFlags) (This)->lpVtbl->get_Focus(This,pValue,pFlags)
#define ICameraControl_put_Focus(This,Value,Flags) (This)->lpVtbl->put_Focus(This,Value,Flags)
#define ICameraControl_getRange_Focus(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Focus(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_Iris(This,pValue,pFlags) (This)->lpVtbl->get_Iris(This,pValue,pFlags)
#define ICameraControl_put_Iris(This,Value,Flags) (This)->lpVtbl->put_Iris(This,Value,Flags)
#define ICameraControl_getRange_Iris(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Iris(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_Zoom(This,pValue,pFlags) (This)->lpVtbl->get_Zoom(This,pValue,pFlags)
#define ICameraControl_put_Zoom(This,Value,Flags) (This)->lpVtbl->put_Zoom(This,Value,Flags)
#define ICameraControl_getRange_Zoom(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Zoom(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_FocalLengths(This,plOcularFocalLength,plObjectiveFocalLengthMin,plObjectiveFocalLengthMax) (This)->lpVtbl->get_FocalLengths(This,plOcularFocalLength,plObjectiveFocalLengthMin,plObjectiveFocalLengthMax)
#define ICameraControl_get_Pan(This,pValue,pFlags) (This)->lpVtbl->get_Pan(This,pValue,pFlags)
#define ICameraControl_put_Pan(This,Value,Flags) (This)->lpVtbl->put_Pan(This,Value,Flags)
#define ICameraControl_getRange_Pan(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Pan(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_Tilt(This,pValue,pFlags) (This)->lpVtbl->get_Tilt(This,pValue,pFlags)
#define ICameraControl_put_Tilt(This,Value,Flags) (This)->lpVtbl->put_Tilt(This,Value,Flags)
#define ICameraControl_getRange_Tilt(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Tilt(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_PanTilt(This,pPanValue,pTiltValue,pFlags) (This)->lpVtbl->get_PanTilt(This,pPanValue,pTiltValue,pFlags)
#define ICameraControl_put_PanTilt(This,PanValue,TiltValue,Flags) (This)->lpVtbl->put_PanTilt(This,PanValue,TiltValue,Flags)
#define ICameraControl_get_Roll(This,pValue,pFlags) (This)->lpVtbl->get_Roll(This,pValue,pFlags)
#define ICameraControl_put_Roll(This,Value,Flags) (This)->lpVtbl->put_Roll(This,Value,Flags)
#define ICameraControl_getRange_Roll(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Roll(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_ExposureRelative(This,pValue,pFlags) (This)->lpVtbl->get_ExposureRelative(This,pValue,pFlags)
#define ICameraControl_put_ExposureRelative(This,Value,Flags) (This)->lpVtbl->put_ExposureRelative(This,Value,Flags)
#define ICameraControl_getRange_ExposureRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_ExposureRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_FocusRelative(This,pValue,pFlags) (This)->lpVtbl->get_FocusRelative(This,pValue,pFlags)
#define ICameraControl_put_FocusRelative(This,Value,Flags) (This)->lpVtbl->put_FocusRelative(This,Value,Flags)
#define ICameraControl_getRange_FocusRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_FocusRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_IrisRelative(This,pValue,pFlags) (This)->lpVtbl->get_IrisRelative(This,pValue,pFlags)
#define ICameraControl_put_IrisRelative(This,Value,Flags) (This)->lpVtbl->put_IrisRelative(This,Value,Flags)
#define ICameraControl_getRange_IrisRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_IrisRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_ZoomRelative(This,pValue,pFlags) (This)->lpVtbl->get_ZoomRelative(This,pValue,pFlags)
#define ICameraControl_put_ZoomRelative(This,Value,Flags) (This)->lpVtbl->put_ZoomRelative(This,Value,Flags)
#define ICameraControl_getRange_ZoomRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_ZoomRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_PanRelative(This,pValue,pFlags) (This)->lpVtbl->get_PanRelative(This,pValue,pFlags)
#define ICameraControl_put_PanRelative(This,Value,Flags) (This)->lpVtbl->put_PanRelative(This,Value,Flags)
#define ICameraControl_get_TiltRelative(This,pValue,pFlags) (This)->lpVtbl->get_TiltRelative(This,pValue,pFlags)
#define ICameraControl_put_TiltRelative(This,Value,Flags) (This)->lpVtbl->put_TiltRelative(This,Value,Flags)
#define ICameraControl_getRange_TiltRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_TiltRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_PanTiltRelative(This,pPanValue,pTiltValue,pFlags) (This)->lpVtbl->get_PanTiltRelative(This,pPanValue,pTiltValue,pFlags)
#define ICameraControl_put_PanTiltRelative(This,PanValue,TiltValue,Flags) (This)->lpVtbl->put_PanTiltRelative(This,PanValue,TiltValue,Flags)
#define ICameraControl_getRange_PanRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_PanRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_RollRelative(This,pValue,pFlags) (This)->lpVtbl->get_RollRelative(This,pValue,pFlags)
#define ICameraControl_put_RollRelative(This,Value,Flags) (This)->lpVtbl->put_RollRelative(This,Value,Flags)
#define ICameraControl_getRange_RollRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_RollRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define ICameraControl_get_ScanMode(This,pValue,pFlags) (This)->lpVtbl->get_ScanMode(This,pValue,pFlags)
#define ICameraControl_put_ScanMode(This,Value,Flags) (This)->lpVtbl->put_ScanMode(This,Value,Flags)
#define ICameraControl_get_PrivacyMode(This,pValue,pFlags) (This)->lpVtbl->get_PrivacyMode(This,pValue,pFlags)
#define ICameraControl_put_PrivacyMode(This,Value,Flags) (This)->lpVtbl->put_PrivacyMode(This,Value,Flags)
#else
/*** IUnknown methods ***/
static inline HRESULT ICameraControl_QueryInterface(ICameraControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICameraControl_AddRef(ICameraControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICameraControl_Release(ICameraControl* This) {
    return This->lpVtbl->Release(This);
}
/*** ICameraControl methods ***/
static inline HRESULT ICameraControl_get_Exposure(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Exposure(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_Exposure(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Exposure(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_Exposure(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Exposure(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_Focus(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Focus(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_Focus(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Focus(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_Focus(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Focus(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_Iris(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Iris(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_Iris(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Iris(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_Iris(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Iris(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_Zoom(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Zoom(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_Zoom(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Zoom(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_Zoom(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Zoom(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_FocalLengths(ICameraControl* This,LONG *plOcularFocalLength,LONG *plObjectiveFocalLengthMin,LONG *plObjectiveFocalLengthMax) {
    return This->lpVtbl->get_FocalLengths(This,plOcularFocalLength,plObjectiveFocalLengthMin,plObjectiveFocalLengthMax);
}
static inline HRESULT ICameraControl_get_Pan(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Pan(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_Pan(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Pan(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_Pan(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Pan(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_Tilt(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Tilt(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_Tilt(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Tilt(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_Tilt(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Tilt(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_PanTilt(ICameraControl* This,LONG *pPanValue,LONG *pTiltValue,LONG *pFlags) {
    return This->lpVtbl->get_PanTilt(This,pPanValue,pTiltValue,pFlags);
}
static inline HRESULT ICameraControl_put_PanTilt(ICameraControl* This,LONG PanValue,LONG TiltValue,LONG Flags) {
    return This->lpVtbl->put_PanTilt(This,PanValue,TiltValue,Flags);
}
static inline HRESULT ICameraControl_get_Roll(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Roll(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_Roll(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Roll(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_Roll(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Roll(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_ExposureRelative(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_ExposureRelative(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_ExposureRelative(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_ExposureRelative(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_ExposureRelative(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_ExposureRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_FocusRelative(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_FocusRelative(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_FocusRelative(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_FocusRelative(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_FocusRelative(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_FocusRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_IrisRelative(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_IrisRelative(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_IrisRelative(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_IrisRelative(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_IrisRelative(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_IrisRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_ZoomRelative(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_ZoomRelative(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_ZoomRelative(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_ZoomRelative(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_ZoomRelative(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_ZoomRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_PanRelative(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_PanRelative(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_PanRelative(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_PanRelative(This,Value,Flags);
}
static inline HRESULT ICameraControl_get_TiltRelative(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_TiltRelative(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_TiltRelative(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_TiltRelative(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_TiltRelative(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_TiltRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_PanTiltRelative(ICameraControl* This,LONG *pPanValue,LONG *pTiltValue,LONG *pFlags) {
    return This->lpVtbl->get_PanTiltRelative(This,pPanValue,pTiltValue,pFlags);
}
static inline HRESULT ICameraControl_put_PanTiltRelative(ICameraControl* This,LONG PanValue,LONG TiltValue,LONG Flags) {
    return This->lpVtbl->put_PanTiltRelative(This,PanValue,TiltValue,Flags);
}
static inline HRESULT ICameraControl_getRange_PanRelative(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_PanRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_RollRelative(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_RollRelative(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_RollRelative(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_RollRelative(This,Value,Flags);
}
static inline HRESULT ICameraControl_getRange_RollRelative(ICameraControl* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_RollRelative(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT ICameraControl_get_ScanMode(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_ScanMode(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_ScanMode(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_ScanMode(This,Value,Flags);
}
static inline HRESULT ICameraControl_get_PrivacyMode(ICameraControl* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_PrivacyMode(This,pValue,pFlags);
}
static inline HRESULT ICameraControl_put_PrivacyMode(ICameraControl* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_PrivacyMode(This,Value,Flags);
}
#endif
#endif

#endif


#endif  /* __ICameraControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVideoProcAmp interface
 */
#ifndef __IVideoProcAmp_INTERFACE_DEFINED__
#define __IVideoProcAmp_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVideoProcAmp, 0x4050560e, 0x42a7, 0x413a, 0x85,0xc2, 0x09,0x26,0x9a,0x2d,0x0f,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4050560e-42a7-413a-85c2-09269a2d0f44")
IVideoProcAmp : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_BacklightCompensation(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_BacklightCompensation(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_BacklightCompensation(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Brightness(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Brightness(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Brightness(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ColorEnable(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ColorEnable(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_ColorEnable(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Contrast(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Contrast(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Contrast(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Gamma(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Gamma(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Gamma(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Saturation(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Saturation(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Saturation(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Sharpness(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Sharpness(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Sharpness(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WhiteBalance(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WhiteBalance(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_WhiteBalance(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Gain(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Gain(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Gain(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Hue(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Hue(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_Hue(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DigitalMultiplier(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DigitalMultiplier(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_DigitalMultiplier(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PowerlineFrequency(
        LONG *pValue,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PowerlineFrequency(
        LONG Value,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_PowerlineFrequency(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WhiteBalanceComponent(
        LONG *pValue1,
        LONG *pValue2,
        LONG *pFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WhiteBalanceComponent(
        LONG Value1,
        LONG Value2,
        LONG Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE getRange_WhiteBalanceComponent(
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVideoProcAmp, 0x4050560e, 0x42a7, 0x413a, 0x85,0xc2, 0x09,0x26,0x9a,0x2d,0x0f,0x44)
#endif
#else
typedef struct IVideoProcAmpVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVideoProcAmp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVideoProcAmp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVideoProcAmp *This);

    /*** IVideoProcAmp methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BacklightCompensation)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_BacklightCompensation)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_BacklightCompensation)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Brightness)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Brightness)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Brightness)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_ColorEnable)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_ColorEnable)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_ColorEnable)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Contrast)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Contrast)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Contrast)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Gamma)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Gamma)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Gamma)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Saturation)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Saturation)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Saturation)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Sharpness)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Sharpness)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Sharpness)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_WhiteBalance)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_WhiteBalance)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_WhiteBalance)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Gain)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Gain)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Gain)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_Hue)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_Hue)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_Hue)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_DigitalMultiplier)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_DigitalMultiplier)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_DigitalMultiplier)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_PowerlineFrequency)(
        IVideoProcAmp *This,
        LONG *pValue,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_PowerlineFrequency)(
        IVideoProcAmp *This,
        LONG Value,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_PowerlineFrequency)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    HRESULT (STDMETHODCALLTYPE *get_WhiteBalanceComponent)(
        IVideoProcAmp *This,
        LONG *pValue1,
        LONG *pValue2,
        LONG *pFlags);

    HRESULT (STDMETHODCALLTYPE *put_WhiteBalanceComponent)(
        IVideoProcAmp *This,
        LONG Value1,
        LONG Value2,
        LONG Flags);

    HRESULT (STDMETHODCALLTYPE *getRange_WhiteBalanceComponent)(
        IVideoProcAmp *This,
        LONG *pMin,
        LONG *pMax,
        LONG *pSteppingDelta,
        LONG *pDefault,
        LONG *pCapsFlag);

    END_INTERFACE
} IVideoProcAmpVtbl;

interface IVideoProcAmp {
    CONST_VTBL IVideoProcAmpVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVideoProcAmp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVideoProcAmp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVideoProcAmp_Release(This) (This)->lpVtbl->Release(This)
/*** IVideoProcAmp methods ***/
#define IVideoProcAmp_get_BacklightCompensation(This,pValue,pFlags) (This)->lpVtbl->get_BacklightCompensation(This,pValue,pFlags)
#define IVideoProcAmp_put_BacklightCompensation(This,Value,Flags) (This)->lpVtbl->put_BacklightCompensation(This,Value,Flags)
#define IVideoProcAmp_getRange_BacklightCompensation(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_BacklightCompensation(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_Brightness(This,pValue,pFlags) (This)->lpVtbl->get_Brightness(This,pValue,pFlags)
#define IVideoProcAmp_put_Brightness(This,Value,Flags) (This)->lpVtbl->put_Brightness(This,Value,Flags)
#define IVideoProcAmp_getRange_Brightness(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Brightness(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_ColorEnable(This,pValue,pFlags) (This)->lpVtbl->get_ColorEnable(This,pValue,pFlags)
#define IVideoProcAmp_put_ColorEnable(This,Value,Flags) (This)->lpVtbl->put_ColorEnable(This,Value,Flags)
#define IVideoProcAmp_getRange_ColorEnable(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_ColorEnable(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_Contrast(This,pValue,pFlags) (This)->lpVtbl->get_Contrast(This,pValue,pFlags)
#define IVideoProcAmp_put_Contrast(This,Value,Flags) (This)->lpVtbl->put_Contrast(This,Value,Flags)
#define IVideoProcAmp_getRange_Contrast(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Contrast(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_Gamma(This,pValue,pFlags) (This)->lpVtbl->get_Gamma(This,pValue,pFlags)
#define IVideoProcAmp_put_Gamma(This,Value,Flags) (This)->lpVtbl->put_Gamma(This,Value,Flags)
#define IVideoProcAmp_getRange_Gamma(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Gamma(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_Saturation(This,pValue,pFlags) (This)->lpVtbl->get_Saturation(This,pValue,pFlags)
#define IVideoProcAmp_put_Saturation(This,Value,Flags) (This)->lpVtbl->put_Saturation(This,Value,Flags)
#define IVideoProcAmp_getRange_Saturation(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Saturation(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_Sharpness(This,pValue,pFlags) (This)->lpVtbl->get_Sharpness(This,pValue,pFlags)
#define IVideoProcAmp_put_Sharpness(This,Value,Flags) (This)->lpVtbl->put_Sharpness(This,Value,Flags)
#define IVideoProcAmp_getRange_Sharpness(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Sharpness(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_WhiteBalance(This,pValue,pFlags) (This)->lpVtbl->get_WhiteBalance(This,pValue,pFlags)
#define IVideoProcAmp_put_WhiteBalance(This,Value,Flags) (This)->lpVtbl->put_WhiteBalance(This,Value,Flags)
#define IVideoProcAmp_getRange_WhiteBalance(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_WhiteBalance(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_Gain(This,pValue,pFlags) (This)->lpVtbl->get_Gain(This,pValue,pFlags)
#define IVideoProcAmp_put_Gain(This,Value,Flags) (This)->lpVtbl->put_Gain(This,Value,Flags)
#define IVideoProcAmp_getRange_Gain(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Gain(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_Hue(This,pValue,pFlags) (This)->lpVtbl->get_Hue(This,pValue,pFlags)
#define IVideoProcAmp_put_Hue(This,Value,Flags) (This)->lpVtbl->put_Hue(This,Value,Flags)
#define IVideoProcAmp_getRange_Hue(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_Hue(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_DigitalMultiplier(This,pValue,pFlags) (This)->lpVtbl->get_DigitalMultiplier(This,pValue,pFlags)
#define IVideoProcAmp_put_DigitalMultiplier(This,Value,Flags) (This)->lpVtbl->put_DigitalMultiplier(This,Value,Flags)
#define IVideoProcAmp_getRange_DigitalMultiplier(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_DigitalMultiplier(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_PowerlineFrequency(This,pValue,pFlags) (This)->lpVtbl->get_PowerlineFrequency(This,pValue,pFlags)
#define IVideoProcAmp_put_PowerlineFrequency(This,Value,Flags) (This)->lpVtbl->put_PowerlineFrequency(This,Value,Flags)
#define IVideoProcAmp_getRange_PowerlineFrequency(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_PowerlineFrequency(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#define IVideoProcAmp_get_WhiteBalanceComponent(This,pValue1,pValue2,pFlags) (This)->lpVtbl->get_WhiteBalanceComponent(This,pValue1,pValue2,pFlags)
#define IVideoProcAmp_put_WhiteBalanceComponent(This,Value1,Value2,Flags) (This)->lpVtbl->put_WhiteBalanceComponent(This,Value1,Value2,Flags)
#define IVideoProcAmp_getRange_WhiteBalanceComponent(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag) (This)->lpVtbl->getRange_WhiteBalanceComponent(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag)
#else
/*** IUnknown methods ***/
static inline HRESULT IVideoProcAmp_QueryInterface(IVideoProcAmp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVideoProcAmp_AddRef(IVideoProcAmp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVideoProcAmp_Release(IVideoProcAmp* This) {
    return This->lpVtbl->Release(This);
}
/*** IVideoProcAmp methods ***/
static inline HRESULT IVideoProcAmp_get_BacklightCompensation(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_BacklightCompensation(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_BacklightCompensation(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_BacklightCompensation(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_BacklightCompensation(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_BacklightCompensation(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_Brightness(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Brightness(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_Brightness(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Brightness(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_Brightness(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Brightness(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_ColorEnable(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_ColorEnable(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_ColorEnable(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_ColorEnable(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_ColorEnable(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_ColorEnable(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_Contrast(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Contrast(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_Contrast(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Contrast(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_Contrast(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Contrast(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_Gamma(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Gamma(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_Gamma(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Gamma(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_Gamma(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Gamma(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_Saturation(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Saturation(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_Saturation(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Saturation(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_Saturation(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Saturation(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_Sharpness(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Sharpness(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_Sharpness(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Sharpness(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_Sharpness(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Sharpness(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_WhiteBalance(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_WhiteBalance(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_WhiteBalance(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_WhiteBalance(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_WhiteBalance(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_WhiteBalance(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_Gain(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Gain(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_Gain(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Gain(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_Gain(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Gain(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_Hue(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_Hue(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_Hue(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_Hue(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_Hue(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_Hue(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_DigitalMultiplier(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_DigitalMultiplier(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_DigitalMultiplier(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_DigitalMultiplier(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_DigitalMultiplier(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_DigitalMultiplier(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_PowerlineFrequency(IVideoProcAmp* This,LONG *pValue,LONG *pFlags) {
    return This->lpVtbl->get_PowerlineFrequency(This,pValue,pFlags);
}
static inline HRESULT IVideoProcAmp_put_PowerlineFrequency(IVideoProcAmp* This,LONG Value,LONG Flags) {
    return This->lpVtbl->put_PowerlineFrequency(This,Value,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_PowerlineFrequency(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_PowerlineFrequency(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
static inline HRESULT IVideoProcAmp_get_WhiteBalanceComponent(IVideoProcAmp* This,LONG *pValue1,LONG *pValue2,LONG *pFlags) {
    return This->lpVtbl->get_WhiteBalanceComponent(This,pValue1,pValue2,pFlags);
}
static inline HRESULT IVideoProcAmp_put_WhiteBalanceComponent(IVideoProcAmp* This,LONG Value1,LONG Value2,LONG Flags) {
    return This->lpVtbl->put_WhiteBalanceComponent(This,Value1,Value2,Flags);
}
static inline HRESULT IVideoProcAmp_getRange_WhiteBalanceComponent(IVideoProcAmp* This,LONG *pMin,LONG *pMax,LONG *pSteppingDelta,LONG *pDefault,LONG *pCapsFlag) {
    return This->lpVtbl->getRange_WhiteBalanceComponent(This,pMin,pMax,pSteppingDelta,pDefault,pCapsFlag);
}
#endif
#endif

#endif


#endif  /* __IVideoProcAmp_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IKsNodeControl interface
 */
#ifndef __IKsNodeControl_INTERFACE_DEFINED__
#define __IKsNodeControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IKsNodeControl, 0x11737c14, 0x24a7, 0x4bb5, 0x81,0xa0, 0x0d,0x00,0x38,0x13,0xb0,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("11737c14-24a7-4bb5-81a0-0d003813b0c4")
IKsNodeControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_NodeId(
        DWORD dwNodeId) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_KsControl(
        PVOID pKsControl) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IKsNodeControl, 0x11737c14, 0x24a7, 0x4bb5, 0x81,0xa0, 0x0d,0x00,0x38,0x13,0xb0,0xc4)
#endif
#else
typedef struct IKsNodeControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IKsNodeControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IKsNodeControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IKsNodeControl *This);

    /*** IKsNodeControl methods ***/
    HRESULT (STDMETHODCALLTYPE *put_NodeId)(
        IKsNodeControl *This,
        DWORD dwNodeId);

    HRESULT (STDMETHODCALLTYPE *put_KsControl)(
        IKsNodeControl *This,
        PVOID pKsControl);

    END_INTERFACE
} IKsNodeControlVtbl;

interface IKsNodeControl {
    CONST_VTBL IKsNodeControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IKsNodeControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IKsNodeControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IKsNodeControl_Release(This) (This)->lpVtbl->Release(This)
/*** IKsNodeControl methods ***/
#define IKsNodeControl_put_NodeId(This,dwNodeId) (This)->lpVtbl->put_NodeId(This,dwNodeId)
#define IKsNodeControl_put_KsControl(This,pKsControl) (This)->lpVtbl->put_KsControl(This,pKsControl)
#else
/*** IUnknown methods ***/
static inline HRESULT IKsNodeControl_QueryInterface(IKsNodeControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IKsNodeControl_AddRef(IKsNodeControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IKsNodeControl_Release(IKsNodeControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IKsNodeControl methods ***/
static inline HRESULT IKsNodeControl_put_NodeId(IKsNodeControl* This,DWORD dwNodeId) {
    return This->lpVtbl->put_NodeId(This,dwNodeId);
}
static inline HRESULT IKsNodeControl_put_KsControl(IKsNodeControl* This,PVOID pKsControl) {
    return This->lpVtbl->put_KsControl(This,pKsControl);
}
#endif
#endif

#endif


#endif  /* __IKsNodeControl_INTERFACE_DEFINED__ */

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __vidcap_h__ */
