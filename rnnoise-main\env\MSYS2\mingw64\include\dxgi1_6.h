/*** Autogenerated by WIDL 10.12 from include/dxgi1_6.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dxgi1_6_h__
#define __dxgi1_6_h__

/* Forward declarations */

#ifndef __IDXGIAdapter4_FWD_DEFINED__
#define __IDXGIAdapter4_FWD_DEFINED__
typedef interface IDXGIAdapter4 IDXGIAdapter4;
#ifdef __cplusplus
interface IDXGIAdapter4;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIOutput6_FWD_DEFINED__
#define __IDXGIOutput6_FWD_DEFINED__
typedef interface IDXGIOutput6 IDXGIOutput6;
#ifdef __cplusplus
interface IDXGIOutput6;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory6_FWD_DEFINED__
#define __IDXGIFactory6_FWD_DEFINED__
typedef interface IDXGIFactory6 IDXGIFactory6;
#ifdef __cplusplus
interface IDXGIFactory6;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory7_FWD_DEFINED__
#define __IDXGIFactory7_FWD_DEFINED__
typedef interface IDXGIFactory7 IDXGIFactory7;
#ifdef __cplusplus
interface IDXGIFactory7;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dxgi1_5.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum DXGI_ADAPTER_FLAG3 {
    DXGI_ADAPTER_FLAG3_NONE = 0x0,
    DXGI_ADAPTER_FLAG3_REMOTE = 0x1,
    DXGI_ADAPTER_FLAG3_SOFTWARE = 0x2,
    DXGI_ADAPTER_FLAG3_ACG_COMPATIBLE = 0x4,
    DXGI_ADAPTER_FLAG3_SUPPORT_MONITORED_FENCES = 0x8,
    DXGI_ADAPTER_FLAG3_SUPPORT_NON_MONITORED_FENCES = 0x10,
    DXGI_ADAPTER_FLAG3_KEYED_MUTEX_CONFORMANCE = 0x20,
    DXGI_ADAPTER_FLAG3_FORCE_DWORD = 0xffffffff
} DXGI_ADAPTER_FLAG3;
typedef enum DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAGS {
    DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAG_FULLSCREEN = 0x1,
    DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAG_WINDOWED = 0x2,
    DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAG_CURSOR_STRETCHED = 0x4
} DXGI_HARDWARE_COMPOSITION_SUPPORT_FLAGS;
typedef enum DXGI_GPU_PREFERENCE {
    DXGI_GPU_PREFERENCE_UNSPECIFIED = 0x0,
    DXGI_GPU_PREFERENCE_MINIMUM_POWER = 0x1,
    DXGI_GPU_PREFERENCE_HIGH_PERFORMANCE = 0x2
} DXGI_GPU_PREFERENCE;
typedef struct DXGI_ADAPTER_DESC3 {
    WCHAR Description[128];
    UINT VendorId;
    UINT DeviceId;
    UINT SubSysId;
    UINT Revision;
    SIZE_T DedicatedVideoMemory;
    SIZE_T DedicatedSystemMemory;
    SIZE_T SharedSystemMemory;
    LUID AdapterLuid;
    DXGI_ADAPTER_FLAG3 Flags;
    DXGI_GRAPHICS_PREEMPTION_GRANULARITY GraphicsPreemptionGranularity;
    DXGI_COMPUTE_PREEMPTION_GRANULARITY ComputePreemptionGranularity;
} DXGI_ADAPTER_DESC3;
typedef struct DXGI_OUTPUT_DESC1 {
    WCHAR DeviceName[32];
    RECT DesktopCoordinates;
    WINBOOL AttachedToDesktop;
    DXGI_MODE_ROTATION Rotation;
    HMONITOR Monitor;
    UINT BitsPerColor;
    DXGI_COLOR_SPACE_TYPE ColorSpace;
    FLOAT RedPrimary[2];
    FLOAT GreenPrimary[2];
    FLOAT BluePrimary[2];
    FLOAT WhitePoint[2];
    FLOAT MinLuminance;
    FLOAT MaxLuminance;
    FLOAT MaxFullFrameLuminance;
} DXGI_OUTPUT_DESC1;
/*****************************************************************************
 * IDXGIAdapter4 interface
 */
#ifndef __IDXGIAdapter4_INTERFACE_DEFINED__
#define __IDXGIAdapter4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIAdapter4, 0x3c8d99d1, 0x4fbf, 0x4181, 0xa8,0x2c, 0xaf,0x66,0xbf,0x7b,0xd2,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3c8d99d1-4fbf-4181-a82c-af66bf7bd24e")
IDXGIAdapter4 : public IDXGIAdapter3
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc3(
        DXGI_ADAPTER_DESC3 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIAdapter4, 0x3c8d99d1, 0x4fbf, 0x4181, 0xa8,0x2c, 0xaf,0x66,0xbf,0x7b,0xd2,0x4e)
#endif
#else
typedef struct IDXGIAdapter4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIAdapter4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIAdapter4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIAdapter4 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIAdapter4 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIAdapter4 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIAdapter4 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIAdapter4 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumOutputs)(
        IDXGIAdapter4 *This,
        UINT output_idx,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIAdapter4 *This,
        DXGI_ADAPTER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *CheckInterfaceSupport)(
        IDXGIAdapter4 *This,
        REFGUID guid,
        LARGE_INTEGER *umd_version);

    /*** IDXGIAdapter1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGIAdapter4 *This,
        DXGI_ADAPTER_DESC1 *pDesc);

    /*** IDXGIAdapter2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc2)(
        IDXGIAdapter4 *This,
        DXGI_ADAPTER_DESC2 *pDesc);

    /*** IDXGIAdapter3 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterHardwareContentProtectionTeardownStatusEvent)(
        IDXGIAdapter4 *This,
        HANDLE event,
        DWORD *cookie);

    void (STDMETHODCALLTYPE *UnregisterHardwareContentProtectionTeardownStatus)(
        IDXGIAdapter4 *This,
        DWORD cookie);

    HRESULT (STDMETHODCALLTYPE *QueryVideoMemoryInfo)(
        IDXGIAdapter4 *This,
        UINT node_index,
        DXGI_MEMORY_SEGMENT_GROUP segment_group,
        DXGI_QUERY_VIDEO_MEMORY_INFO *memory_info);

    HRESULT (STDMETHODCALLTYPE *SetVideoMemoryReservation)(
        IDXGIAdapter4 *This,
        UINT node_index,
        DXGI_MEMORY_SEGMENT_GROUP segment_group,
        UINT64 reservation);

    HRESULT (STDMETHODCALLTYPE *RegisterVideoMemoryBudgetChangeNotificationEvent)(
        IDXGIAdapter4 *This,
        HANDLE event,
        DWORD *cookie);

    void (STDMETHODCALLTYPE *UnregisterVideoMemoryBudgetChangeNotification)(
        IDXGIAdapter4 *This,
        DWORD cookie);

    /*** IDXGIAdapter4 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc3)(
        IDXGIAdapter4 *This,
        DXGI_ADAPTER_DESC3 *desc);

    END_INTERFACE
} IDXGIAdapter4Vtbl;

interface IDXGIAdapter4 {
    CONST_VTBL IDXGIAdapter4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIAdapter4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIAdapter4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIAdapter4_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIAdapter4_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter4_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIAdapter4_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter4_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIAdapter methods ***/
#define IDXGIAdapter4_EnumOutputs(This,output_idx,output) (This)->lpVtbl->EnumOutputs(This,output_idx,output)
#define IDXGIAdapter4_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIAdapter4_CheckInterfaceSupport(This,guid,umd_version) (This)->lpVtbl->CheckInterfaceSupport(This,guid,umd_version)
/*** IDXGIAdapter1 methods ***/
#define IDXGIAdapter4_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
/*** IDXGIAdapter2 methods ***/
#define IDXGIAdapter4_GetDesc2(This,pDesc) (This)->lpVtbl->GetDesc2(This,pDesc)
/*** IDXGIAdapter3 methods ***/
#define IDXGIAdapter4_RegisterHardwareContentProtectionTeardownStatusEvent(This,event,cookie) (This)->lpVtbl->RegisterHardwareContentProtectionTeardownStatusEvent(This,event,cookie)
#define IDXGIAdapter4_UnregisterHardwareContentProtectionTeardownStatus(This,cookie) (This)->lpVtbl->UnregisterHardwareContentProtectionTeardownStatus(This,cookie)
#define IDXGIAdapter4_QueryVideoMemoryInfo(This,node_index,segment_group,memory_info) (This)->lpVtbl->QueryVideoMemoryInfo(This,node_index,segment_group,memory_info)
#define IDXGIAdapter4_SetVideoMemoryReservation(This,node_index,segment_group,reservation) (This)->lpVtbl->SetVideoMemoryReservation(This,node_index,segment_group,reservation)
#define IDXGIAdapter4_RegisterVideoMemoryBudgetChangeNotificationEvent(This,event,cookie) (This)->lpVtbl->RegisterVideoMemoryBudgetChangeNotificationEvent(This,event,cookie)
#define IDXGIAdapter4_UnregisterVideoMemoryBudgetChangeNotification(This,cookie) (This)->lpVtbl->UnregisterVideoMemoryBudgetChangeNotification(This,cookie)
/*** IDXGIAdapter4 methods ***/
#define IDXGIAdapter4_GetDesc3(This,desc) (This)->lpVtbl->GetDesc3(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIAdapter4_QueryInterface(IDXGIAdapter4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIAdapter4_AddRef(IDXGIAdapter4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIAdapter4_Release(IDXGIAdapter4* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIAdapter4_SetPrivateData(IDXGIAdapter4* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter4_SetPrivateDataInterface(IDXGIAdapter4* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIAdapter4_GetPrivateData(IDXGIAdapter4* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter4_GetParent(IDXGIAdapter4* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIAdapter methods ***/
static inline HRESULT IDXGIAdapter4_EnumOutputs(IDXGIAdapter4* This,UINT output_idx,IDXGIOutput **output) {
    return This->lpVtbl->EnumOutputs(This,output_idx,output);
}
static inline HRESULT IDXGIAdapter4_GetDesc(IDXGIAdapter4* This,DXGI_ADAPTER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIAdapter4_CheckInterfaceSupport(IDXGIAdapter4* This,REFGUID guid,LARGE_INTEGER *umd_version) {
    return This->lpVtbl->CheckInterfaceSupport(This,guid,umd_version);
}
/*** IDXGIAdapter1 methods ***/
static inline HRESULT IDXGIAdapter4_GetDesc1(IDXGIAdapter4* This,DXGI_ADAPTER_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
/*** IDXGIAdapter2 methods ***/
static inline HRESULT IDXGIAdapter4_GetDesc2(IDXGIAdapter4* This,DXGI_ADAPTER_DESC2 *pDesc) {
    return This->lpVtbl->GetDesc2(This,pDesc);
}
/*** IDXGIAdapter3 methods ***/
static inline HRESULT IDXGIAdapter4_RegisterHardwareContentProtectionTeardownStatusEvent(IDXGIAdapter4* This,HANDLE event,DWORD *cookie) {
    return This->lpVtbl->RegisterHardwareContentProtectionTeardownStatusEvent(This,event,cookie);
}
static inline void IDXGIAdapter4_UnregisterHardwareContentProtectionTeardownStatus(IDXGIAdapter4* This,DWORD cookie) {
    This->lpVtbl->UnregisterHardwareContentProtectionTeardownStatus(This,cookie);
}
static inline HRESULT IDXGIAdapter4_QueryVideoMemoryInfo(IDXGIAdapter4* This,UINT node_index,DXGI_MEMORY_SEGMENT_GROUP segment_group,DXGI_QUERY_VIDEO_MEMORY_INFO *memory_info) {
    return This->lpVtbl->QueryVideoMemoryInfo(This,node_index,segment_group,memory_info);
}
static inline HRESULT IDXGIAdapter4_SetVideoMemoryReservation(IDXGIAdapter4* This,UINT node_index,DXGI_MEMORY_SEGMENT_GROUP segment_group,UINT64 reservation) {
    return This->lpVtbl->SetVideoMemoryReservation(This,node_index,segment_group,reservation);
}
static inline HRESULT IDXGIAdapter4_RegisterVideoMemoryBudgetChangeNotificationEvent(IDXGIAdapter4* This,HANDLE event,DWORD *cookie) {
    return This->lpVtbl->RegisterVideoMemoryBudgetChangeNotificationEvent(This,event,cookie);
}
static inline void IDXGIAdapter4_UnregisterVideoMemoryBudgetChangeNotification(IDXGIAdapter4* This,DWORD cookie) {
    This->lpVtbl->UnregisterVideoMemoryBudgetChangeNotification(This,cookie);
}
/*** IDXGIAdapter4 methods ***/
static inline HRESULT IDXGIAdapter4_GetDesc3(IDXGIAdapter4* This,DXGI_ADAPTER_DESC3 *desc) {
    return This->lpVtbl->GetDesc3(This,desc);
}
#endif
#endif

#endif


#endif  /* __IDXGIAdapter4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIOutput6 interface
 */
#ifndef __IDXGIOutput6_INTERFACE_DEFINED__
#define __IDXGIOutput6_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutput6, 0x068346e8, 0xaaec, 0x4b84, 0xad,0xd7, 0x13,0x7f,0x51,0x3f,0x77,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("068346e8-aaec-4b84-add7-137f513f77a1")
IDXGIOutput6 : public IDXGIOutput5
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc1(
        DXGI_OUTPUT_DESC1 *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckHardwareCompositionSupport(
        UINT *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutput6, 0x068346e8, 0xaaec, 0x4b84, 0xad,0xd7, 0x13,0x7f,0x51,0x3f,0x77,0xa1)
#endif
#else
typedef struct IDXGIOutput6Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutput6 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutput6 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutput6 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutput6 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutput6 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutput6 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutput6 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutput methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutput6 *This,
        DXGI_OUTPUT_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList)(
        IDXGIOutput6 *This,
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode)(
        IDXGIOutput6 *This,
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device);

    HRESULT (STDMETHODCALLTYPE *WaitForVBlank)(
        IDXGIOutput6 *This);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IDXGIOutput6 *This,
        IUnknown *device,
        WINBOOL exclusive);

    void (STDMETHODCALLTYPE *ReleaseOwnership)(
        IDXGIOutput6 *This);

    HRESULT (STDMETHODCALLTYPE *GetGammaControlCapabilities)(
        IDXGIOutput6 *This,
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps);

    HRESULT (STDMETHODCALLTYPE *SetGammaControl)(
        IDXGIOutput6 *This,
        const DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *GetGammaControl)(
        IDXGIOutput6 *This,
        DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *SetDisplaySurface)(
        IDXGIOutput6 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData)(
        IDXGIOutput6 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGIOutput6 *This,
        DXGI_FRAME_STATISTICS *stats);

    /*** IDXGIOutput1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList1)(
        IDXGIOutput6 *This,
        DXGI_FORMAT enum_format,
        UINT flags,
        UINT *num_modes,
        DXGI_MODE_DESC1 *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode1)(
        IDXGIOutput6 *This,
        const DXGI_MODE_DESC1 *mode_to_match,
        DXGI_MODE_DESC1 *closest_match,
        IUnknown *concerned_device);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData1)(
        IDXGIOutput6 *This,
        IDXGIResource *destination);

    HRESULT (STDMETHODCALLTYPE *DuplicateOutput)(
        IDXGIOutput6 *This,
        IUnknown *device,
        IDXGIOutputDuplication **output_duplication);

    /*** IDXGIOutput2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *SupportsOverlays)(
        IDXGIOutput6 *This);

    /*** IDXGIOutput3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckOverlaySupport)(
        IDXGIOutput6 *This,
        DXGI_FORMAT enum_format,
        IUnknown *concerned_device,
        UINT *flags);

    /*** IDXGIOutput4 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckOverlayColorSpaceSupport)(
        IDXGIOutput6 *This,
        DXGI_FORMAT format,
        DXGI_COLOR_SPACE_TYPE colour_space,
        IUnknown *device,
        UINT *flags);

    /*** IDXGIOutput5 methods ***/
    HRESULT (STDMETHODCALLTYPE *DuplicateOutput1)(
        IDXGIOutput6 *This,
        IUnknown *device,
        UINT flags,
        UINT format_count,
        const DXGI_FORMAT *formats,
        IDXGIOutputDuplication **duplication);

    /*** IDXGIOutput6 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGIOutput6 *This,
        DXGI_OUTPUT_DESC1 *desc);

    HRESULT (STDMETHODCALLTYPE *CheckHardwareCompositionSupport)(
        IDXGIOutput6 *This,
        UINT *flags);

    END_INTERFACE
} IDXGIOutput6Vtbl;

interface IDXGIOutput6 {
    CONST_VTBL IDXGIOutput6Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutput6_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutput6_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutput6_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutput6_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutput6_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutput6_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutput6_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutput methods ***/
#define IDXGIOutput6_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutput6_GetDisplayModeList(This,format,flags,mode_count,desc) (This)->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc)
#define IDXGIOutput6_FindClosestMatchingMode(This,mode,closest_match,device) (This)->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device)
#define IDXGIOutput6_WaitForVBlank(This) (This)->lpVtbl->WaitForVBlank(This)
#define IDXGIOutput6_TakeOwnership(This,device,exclusive) (This)->lpVtbl->TakeOwnership(This,device,exclusive)
#define IDXGIOutput6_ReleaseOwnership(This) (This)->lpVtbl->ReleaseOwnership(This)
#define IDXGIOutput6_GetGammaControlCapabilities(This,gamma_caps) (This)->lpVtbl->GetGammaControlCapabilities(This,gamma_caps)
#define IDXGIOutput6_SetGammaControl(This,gamma_control) (This)->lpVtbl->SetGammaControl(This,gamma_control)
#define IDXGIOutput6_GetGammaControl(This,gamma_control) (This)->lpVtbl->GetGammaControl(This,gamma_control)
#define IDXGIOutput6_SetDisplaySurface(This,surface) (This)->lpVtbl->SetDisplaySurface(This,surface)
#define IDXGIOutput6_GetDisplaySurfaceData(This,surface) (This)->lpVtbl->GetDisplaySurfaceData(This,surface)
#define IDXGIOutput6_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
/*** IDXGIOutput1 methods ***/
#define IDXGIOutput6_GetDisplayModeList1(This,enum_format,flags,num_modes,desc) (This)->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc)
#define IDXGIOutput6_FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device) (This)->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device)
#define IDXGIOutput6_GetDisplaySurfaceData1(This,destination) (This)->lpVtbl->GetDisplaySurfaceData1(This,destination)
#define IDXGIOutput6_DuplicateOutput(This,device,output_duplication) (This)->lpVtbl->DuplicateOutput(This,device,output_duplication)
/*** IDXGIOutput2 methods ***/
#define IDXGIOutput6_SupportsOverlays(This) (This)->lpVtbl->SupportsOverlays(This)
/*** IDXGIOutput3 methods ***/
#define IDXGIOutput6_CheckOverlaySupport(This,enum_format,concerned_device,flags) (This)->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags)
/*** IDXGIOutput4 methods ***/
#define IDXGIOutput6_CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags) (This)->lpVtbl->CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags)
/*** IDXGIOutput5 methods ***/
#define IDXGIOutput6_DuplicateOutput1(This,device,flags,format_count,formats,duplication) (This)->lpVtbl->DuplicateOutput1(This,device,flags,format_count,formats,duplication)
/*** IDXGIOutput6 methods ***/
#define IDXGIOutput6_GetDesc1(This,desc) (This)->lpVtbl->GetDesc1(This,desc)
#define IDXGIOutput6_CheckHardwareCompositionSupport(This,flags) (This)->lpVtbl->CheckHardwareCompositionSupport(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIOutput6_QueryInterface(IDXGIOutput6* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIOutput6_AddRef(IDXGIOutput6* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIOutput6_Release(IDXGIOutput6* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIOutput6_SetPrivateData(IDXGIOutput6* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput6_SetPrivateDataInterface(IDXGIOutput6* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIOutput6_GetPrivateData(IDXGIOutput6* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput6_GetParent(IDXGIOutput6* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutput methods ***/
static inline HRESULT IDXGIOutput6_GetDesc(IDXGIOutput6* This,DXGI_OUTPUT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIOutput6_GetDisplayModeList(IDXGIOutput6* This,DXGI_FORMAT format,UINT flags,UINT *mode_count,DXGI_MODE_DESC *desc) {
    return This->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc);
}
static inline HRESULT IDXGIOutput6_FindClosestMatchingMode(IDXGIOutput6* This,const DXGI_MODE_DESC *mode,DXGI_MODE_DESC *closest_match,IUnknown *device) {
    return This->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device);
}
static inline HRESULT IDXGIOutput6_WaitForVBlank(IDXGIOutput6* This) {
    return This->lpVtbl->WaitForVBlank(This);
}
static inline HRESULT IDXGIOutput6_TakeOwnership(IDXGIOutput6* This,IUnknown *device,WINBOOL exclusive) {
    return This->lpVtbl->TakeOwnership(This,device,exclusive);
}
static inline void IDXGIOutput6_ReleaseOwnership(IDXGIOutput6* This) {
    This->lpVtbl->ReleaseOwnership(This);
}
static inline HRESULT IDXGIOutput6_GetGammaControlCapabilities(IDXGIOutput6* This,DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) {
    return This->lpVtbl->GetGammaControlCapabilities(This,gamma_caps);
}
static inline HRESULT IDXGIOutput6_SetGammaControl(IDXGIOutput6* This,const DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->SetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput6_GetGammaControl(IDXGIOutput6* This,DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->GetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput6_SetDisplaySurface(IDXGIOutput6* This,IDXGISurface *surface) {
    return This->lpVtbl->SetDisplaySurface(This,surface);
}
static inline HRESULT IDXGIOutput6_GetDisplaySurfaceData(IDXGIOutput6* This,IDXGISurface *surface) {
    return This->lpVtbl->GetDisplaySurfaceData(This,surface);
}
static inline HRESULT IDXGIOutput6_GetFrameStatistics(IDXGIOutput6* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
/*** IDXGIOutput1 methods ***/
static inline HRESULT IDXGIOutput6_GetDisplayModeList1(IDXGIOutput6* This,DXGI_FORMAT enum_format,UINT flags,UINT *num_modes,DXGI_MODE_DESC1 *desc) {
    return This->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc);
}
static inline HRESULT IDXGIOutput6_FindClosestMatchingMode1(IDXGIOutput6* This,const DXGI_MODE_DESC1 *mode_to_match,DXGI_MODE_DESC1 *closest_match,IUnknown *concerned_device) {
    return This->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device);
}
static inline HRESULT IDXGIOutput6_GetDisplaySurfaceData1(IDXGIOutput6* This,IDXGIResource *destination) {
    return This->lpVtbl->GetDisplaySurfaceData1(This,destination);
}
static inline HRESULT IDXGIOutput6_DuplicateOutput(IDXGIOutput6* This,IUnknown *device,IDXGIOutputDuplication **output_duplication) {
    return This->lpVtbl->DuplicateOutput(This,device,output_duplication);
}
/*** IDXGIOutput2 methods ***/
static inline WINBOOL IDXGIOutput6_SupportsOverlays(IDXGIOutput6* This) {
    return This->lpVtbl->SupportsOverlays(This);
}
/*** IDXGIOutput3 methods ***/
static inline HRESULT IDXGIOutput6_CheckOverlaySupport(IDXGIOutput6* This,DXGI_FORMAT enum_format,IUnknown *concerned_device,UINT *flags) {
    return This->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags);
}
/*** IDXGIOutput4 methods ***/
static inline HRESULT IDXGIOutput6_CheckOverlayColorSpaceSupport(IDXGIOutput6* This,DXGI_FORMAT format,DXGI_COLOR_SPACE_TYPE colour_space,IUnknown *device,UINT *flags) {
    return This->lpVtbl->CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags);
}
/*** IDXGIOutput5 methods ***/
static inline HRESULT IDXGIOutput6_DuplicateOutput1(IDXGIOutput6* This,IUnknown *device,UINT flags,UINT format_count,const DXGI_FORMAT *formats,IDXGIOutputDuplication **duplication) {
    return This->lpVtbl->DuplicateOutput1(This,device,flags,format_count,formats,duplication);
}
/*** IDXGIOutput6 methods ***/
static inline HRESULT IDXGIOutput6_GetDesc1(IDXGIOutput6* This,DXGI_OUTPUT_DESC1 *desc) {
    return This->lpVtbl->GetDesc1(This,desc);
}
static inline HRESULT IDXGIOutput6_CheckHardwareCompositionSupport(IDXGIOutput6* This,UINT *flags) {
    return This->lpVtbl->CheckHardwareCompositionSupport(This,flags);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutput6_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactory6 interface
 */
#ifndef __IDXGIFactory6_INTERFACE_DEFINED__
#define __IDXGIFactory6_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory6, 0xc1b6694f, 0xff09, 0x44a9, 0xb0,0x3c, 0x77,0x90,0x0a,0x0a,0x1d,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c1b6694f-ff09-44a9-b03c-77900a0a1d17")
IDXGIFactory6 : public IDXGIFactory5
{
    virtual HRESULT STDMETHODCALLTYPE EnumAdapterByGpuPreference(
        UINT adapter_idx,
        DXGI_GPU_PREFERENCE gpu_preference,
        REFIID iid,
        void **adapter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory6, 0xc1b6694f, 0xff09, 0x44a9, 0xb0,0x3c, 0x77,0x90,0x0a,0x0a,0x1d,0x17)
#endif
#else
typedef struct IDXGIFactory6Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory6 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory6 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory6 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory6 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory6 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory6 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory6 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory6 *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory6 *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory6 *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory6 *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory6 *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    /*** IDXGIFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters1)(
        IDXGIFactory6 *This,
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter);

    WINBOOL (STDMETHODCALLTYPE *IsCurrent)(
        IDXGIFactory6 *This);

    /*** IDXGIFactory2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsWindowedStereoEnabled)(
        IDXGIFactory6 *This);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForHwnd)(
        IDXGIFactory6 *This,
        IUnknown *pDevice,
        HWND hWnd,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForCoreWindow)(
        IDXGIFactory6 *This,
        IUnknown *pDevice,
        IUnknown *pWindow,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *GetSharedResourceAdapterLuid)(
        IDXGIFactory6 *This,
        HANDLE hResource,
        LUID *pLuid);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusWindow)(
        IDXGIFactory6 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusEvent)(
        IDXGIFactory6 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterStereoStatus)(
        IDXGIFactory6 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusWindow)(
        IDXGIFactory6 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusEvent)(
        IDXGIFactory6 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterOcclusionStatus)(
        IDXGIFactory6 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForComposition)(
        IDXGIFactory6 *This,
        IUnknown *pDevice,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    /*** IDXGIFactory3 methods ***/
    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        IDXGIFactory6 *This);

    /*** IDXGIFactory4 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapterByLuid)(
        IDXGIFactory6 *This,
        LUID luid,
        REFIID iid,
        void **adapter);

    HRESULT (STDMETHODCALLTYPE *EnumWarpAdapter)(
        IDXGIFactory6 *This,
        REFIID iid,
        void **adapter);

    /*** IDXGIFactory5 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        IDXGIFactory6 *This,
        DXGI_FEATURE feature,
        void *support_data,
        UINT support_data_size);

    /*** IDXGIFactory6 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapterByGpuPreference)(
        IDXGIFactory6 *This,
        UINT adapter_idx,
        DXGI_GPU_PREFERENCE gpu_preference,
        REFIID iid,
        void **adapter);

    END_INTERFACE
} IDXGIFactory6Vtbl;

interface IDXGIFactory6 {
    CONST_VTBL IDXGIFactory6Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory6_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory6_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory6_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory6_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory6_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory6_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory6_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory6_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory6_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory6_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory6_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory6_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
/*** IDXGIFactory1 methods ***/
#define IDXGIFactory6_EnumAdapters1(This,Adapter,ppAdapter) (This)->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter)
#define IDXGIFactory6_IsCurrent(This) (This)->lpVtbl->IsCurrent(This)
/*** IDXGIFactory2 methods ***/
#define IDXGIFactory6_IsWindowedStereoEnabled(This) (This)->lpVtbl->IsWindowedStereoEnabled(This)
#define IDXGIFactory6_CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory6_CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory6_GetSharedResourceAdapterLuid(This,hResource,pLuid) (This)->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid)
#define IDXGIFactory6_RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory6_RegisterStereoStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory6_UnregisterStereoStatus(This,dwCookie) (This)->lpVtbl->UnregisterStereoStatus(This,dwCookie)
#define IDXGIFactory6_RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory6_RegisterOcclusionStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory6_UnregisterOcclusionStatus(This,dwCookie) (This)->lpVtbl->UnregisterOcclusionStatus(This,dwCookie)
#define IDXGIFactory6_CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain)
/*** IDXGIFactory3 methods ***/
#define IDXGIFactory6_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
/*** IDXGIFactory4 methods ***/
#define IDXGIFactory6_EnumAdapterByLuid(This,luid,iid,adapter) (This)->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter)
#define IDXGIFactory6_EnumWarpAdapter(This,iid,adapter) (This)->lpVtbl->EnumWarpAdapter(This,iid,adapter)
/*** IDXGIFactory5 methods ***/
#define IDXGIFactory6_CheckFeatureSupport(This,feature,support_data,support_data_size) (This)->lpVtbl->CheckFeatureSupport(This,feature,support_data,support_data_size)
/*** IDXGIFactory6 methods ***/
#define IDXGIFactory6_EnumAdapterByGpuPreference(This,adapter_idx,gpu_preference,iid,adapter) (This)->lpVtbl->EnumAdapterByGpuPreference(This,adapter_idx,gpu_preference,iid,adapter)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactory6_QueryInterface(IDXGIFactory6* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactory6_AddRef(IDXGIFactory6* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactory6_Release(IDXGIFactory6* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIFactory6_SetPrivateData(IDXGIFactory6* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory6_SetPrivateDataInterface(IDXGIFactory6* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIFactory6_GetPrivateData(IDXGIFactory6* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory6_GetParent(IDXGIFactory6* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static inline HRESULT IDXGIFactory6_EnumAdapters(IDXGIFactory6* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static inline HRESULT IDXGIFactory6_MakeWindowAssociation(IDXGIFactory6* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static inline HRESULT IDXGIFactory6_GetWindowAssociation(IDXGIFactory6* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static inline HRESULT IDXGIFactory6_CreateSwapChain(IDXGIFactory6* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static inline HRESULT IDXGIFactory6_CreateSoftwareAdapter(IDXGIFactory6* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
/*** IDXGIFactory1 methods ***/
static inline HRESULT IDXGIFactory6_EnumAdapters1(IDXGIFactory6* This,UINT Adapter,IDXGIAdapter1 **ppAdapter) {
    return This->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter);
}
static inline WINBOOL IDXGIFactory6_IsCurrent(IDXGIFactory6* This) {
    return This->lpVtbl->IsCurrent(This);
}
/*** IDXGIFactory2 methods ***/
static inline WINBOOL IDXGIFactory6_IsWindowedStereoEnabled(IDXGIFactory6* This) {
    return This->lpVtbl->IsWindowedStereoEnabled(This);
}
static inline HRESULT IDXGIFactory6_CreateSwapChainForHwnd(IDXGIFactory6* This,IUnknown *pDevice,HWND hWnd,const DXGI_SWAP_CHAIN_DESC1 *pDesc,const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory6_CreateSwapChainForCoreWindow(IDXGIFactory6* This,IUnknown *pDevice,IUnknown *pWindow,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory6_GetSharedResourceAdapterLuid(IDXGIFactory6* This,HANDLE hResource,LUID *pLuid) {
    return This->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid);
}
static inline HRESULT IDXGIFactory6_RegisterStereoStatusWindow(IDXGIFactory6* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory6_RegisterStereoStatusEvent(IDXGIFactory6* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory6_UnregisterStereoStatus(IDXGIFactory6* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterStereoStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory6_RegisterOcclusionStatusWindow(IDXGIFactory6* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory6_RegisterOcclusionStatusEvent(IDXGIFactory6* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory6_UnregisterOcclusionStatus(IDXGIFactory6* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterOcclusionStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory6_CreateSwapChainForComposition(IDXGIFactory6* This,IUnknown *pDevice,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain);
}
/*** IDXGIFactory3 methods ***/
static inline UINT IDXGIFactory6_GetCreationFlags(IDXGIFactory6* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
/*** IDXGIFactory4 methods ***/
static inline HRESULT IDXGIFactory6_EnumAdapterByLuid(IDXGIFactory6* This,LUID luid,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter);
}
static inline HRESULT IDXGIFactory6_EnumWarpAdapter(IDXGIFactory6* This,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumWarpAdapter(This,iid,adapter);
}
/*** IDXGIFactory5 methods ***/
static inline HRESULT IDXGIFactory6_CheckFeatureSupport(IDXGIFactory6* This,DXGI_FEATURE feature,void *support_data,UINT support_data_size) {
    return This->lpVtbl->CheckFeatureSupport(This,feature,support_data,support_data_size);
}
/*** IDXGIFactory6 methods ***/
static inline HRESULT IDXGIFactory6_EnumAdapterByGpuPreference(IDXGIFactory6* This,UINT adapter_idx,DXGI_GPU_PREFERENCE gpu_preference,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumAdapterByGpuPreference(This,adapter_idx,gpu_preference,iid,adapter);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory6_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactory7 interface
 */
#ifndef __IDXGIFactory7_INTERFACE_DEFINED__
#define __IDXGIFactory7_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory7, 0xa4966eed, 0x76db, 0x44da, 0x84,0xc1, 0xee,0x9a,0x7a,0xfb,0x20,0xa8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a4966eed-76db-44da-84c1-ee9a7afb20a8")
IDXGIFactory7 : public IDXGIFactory6
{
    virtual HRESULT STDMETHODCALLTYPE RegisterAdaptersChangedEvent(
        HANDLE event,
        DWORD *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterAdaptersChangedEvent(
        DWORD cookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory7, 0xa4966eed, 0x76db, 0x44da, 0x84,0xc1, 0xee,0x9a,0x7a,0xfb,0x20,0xa8)
#endif
#else
typedef struct IDXGIFactory7Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory7 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory7 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory7 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory7 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory7 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory7 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory7 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory7 *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory7 *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory7 *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory7 *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory7 *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    /*** IDXGIFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters1)(
        IDXGIFactory7 *This,
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter);

    WINBOOL (STDMETHODCALLTYPE *IsCurrent)(
        IDXGIFactory7 *This);

    /*** IDXGIFactory2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsWindowedStereoEnabled)(
        IDXGIFactory7 *This);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForHwnd)(
        IDXGIFactory7 *This,
        IUnknown *pDevice,
        HWND hWnd,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForCoreWindow)(
        IDXGIFactory7 *This,
        IUnknown *pDevice,
        IUnknown *pWindow,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *GetSharedResourceAdapterLuid)(
        IDXGIFactory7 *This,
        HANDLE hResource,
        LUID *pLuid);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusWindow)(
        IDXGIFactory7 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusEvent)(
        IDXGIFactory7 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterStereoStatus)(
        IDXGIFactory7 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusWindow)(
        IDXGIFactory7 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusEvent)(
        IDXGIFactory7 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterOcclusionStatus)(
        IDXGIFactory7 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForComposition)(
        IDXGIFactory7 *This,
        IUnknown *pDevice,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    /*** IDXGIFactory3 methods ***/
    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        IDXGIFactory7 *This);

    /*** IDXGIFactory4 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapterByLuid)(
        IDXGIFactory7 *This,
        LUID luid,
        REFIID iid,
        void **adapter);

    HRESULT (STDMETHODCALLTYPE *EnumWarpAdapter)(
        IDXGIFactory7 *This,
        REFIID iid,
        void **adapter);

    /*** IDXGIFactory5 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        IDXGIFactory7 *This,
        DXGI_FEATURE feature,
        void *support_data,
        UINT support_data_size);

    /*** IDXGIFactory6 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapterByGpuPreference)(
        IDXGIFactory7 *This,
        UINT adapter_idx,
        DXGI_GPU_PREFERENCE gpu_preference,
        REFIID iid,
        void **adapter);

    /*** IDXGIFactory7 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterAdaptersChangedEvent)(
        IDXGIFactory7 *This,
        HANDLE event,
        DWORD *cookie);

    HRESULT (STDMETHODCALLTYPE *UnregisterAdaptersChangedEvent)(
        IDXGIFactory7 *This,
        DWORD cookie);

    END_INTERFACE
} IDXGIFactory7Vtbl;

interface IDXGIFactory7 {
    CONST_VTBL IDXGIFactory7Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory7_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory7_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory7_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory7_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory7_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory7_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory7_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory7_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory7_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory7_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory7_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory7_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
/*** IDXGIFactory1 methods ***/
#define IDXGIFactory7_EnumAdapters1(This,Adapter,ppAdapter) (This)->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter)
#define IDXGIFactory7_IsCurrent(This) (This)->lpVtbl->IsCurrent(This)
/*** IDXGIFactory2 methods ***/
#define IDXGIFactory7_IsWindowedStereoEnabled(This) (This)->lpVtbl->IsWindowedStereoEnabled(This)
#define IDXGIFactory7_CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory7_CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory7_GetSharedResourceAdapterLuid(This,hResource,pLuid) (This)->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid)
#define IDXGIFactory7_RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory7_RegisterStereoStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory7_UnregisterStereoStatus(This,dwCookie) (This)->lpVtbl->UnregisterStereoStatus(This,dwCookie)
#define IDXGIFactory7_RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory7_RegisterOcclusionStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory7_UnregisterOcclusionStatus(This,dwCookie) (This)->lpVtbl->UnregisterOcclusionStatus(This,dwCookie)
#define IDXGIFactory7_CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain)
/*** IDXGIFactory3 methods ***/
#define IDXGIFactory7_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
/*** IDXGIFactory4 methods ***/
#define IDXGIFactory7_EnumAdapterByLuid(This,luid,iid,adapter) (This)->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter)
#define IDXGIFactory7_EnumWarpAdapter(This,iid,adapter) (This)->lpVtbl->EnumWarpAdapter(This,iid,adapter)
/*** IDXGIFactory5 methods ***/
#define IDXGIFactory7_CheckFeatureSupport(This,feature,support_data,support_data_size) (This)->lpVtbl->CheckFeatureSupport(This,feature,support_data,support_data_size)
/*** IDXGIFactory6 methods ***/
#define IDXGIFactory7_EnumAdapterByGpuPreference(This,adapter_idx,gpu_preference,iid,adapter) (This)->lpVtbl->EnumAdapterByGpuPreference(This,adapter_idx,gpu_preference,iid,adapter)
/*** IDXGIFactory7 methods ***/
#define IDXGIFactory7_RegisterAdaptersChangedEvent(This,event,cookie) (This)->lpVtbl->RegisterAdaptersChangedEvent(This,event,cookie)
#define IDXGIFactory7_UnregisterAdaptersChangedEvent(This,cookie) (This)->lpVtbl->UnregisterAdaptersChangedEvent(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactory7_QueryInterface(IDXGIFactory7* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactory7_AddRef(IDXGIFactory7* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactory7_Release(IDXGIFactory7* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIFactory7_SetPrivateData(IDXGIFactory7* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory7_SetPrivateDataInterface(IDXGIFactory7* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIFactory7_GetPrivateData(IDXGIFactory7* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory7_GetParent(IDXGIFactory7* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static inline HRESULT IDXGIFactory7_EnumAdapters(IDXGIFactory7* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static inline HRESULT IDXGIFactory7_MakeWindowAssociation(IDXGIFactory7* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static inline HRESULT IDXGIFactory7_GetWindowAssociation(IDXGIFactory7* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static inline HRESULT IDXGIFactory7_CreateSwapChain(IDXGIFactory7* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static inline HRESULT IDXGIFactory7_CreateSoftwareAdapter(IDXGIFactory7* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
/*** IDXGIFactory1 methods ***/
static inline HRESULT IDXGIFactory7_EnumAdapters1(IDXGIFactory7* This,UINT Adapter,IDXGIAdapter1 **ppAdapter) {
    return This->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter);
}
static inline WINBOOL IDXGIFactory7_IsCurrent(IDXGIFactory7* This) {
    return This->lpVtbl->IsCurrent(This);
}
/*** IDXGIFactory2 methods ***/
static inline WINBOOL IDXGIFactory7_IsWindowedStereoEnabled(IDXGIFactory7* This) {
    return This->lpVtbl->IsWindowedStereoEnabled(This);
}
static inline HRESULT IDXGIFactory7_CreateSwapChainForHwnd(IDXGIFactory7* This,IUnknown *pDevice,HWND hWnd,const DXGI_SWAP_CHAIN_DESC1 *pDesc,const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory7_CreateSwapChainForCoreWindow(IDXGIFactory7* This,IUnknown *pDevice,IUnknown *pWindow,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory7_GetSharedResourceAdapterLuid(IDXGIFactory7* This,HANDLE hResource,LUID *pLuid) {
    return This->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid);
}
static inline HRESULT IDXGIFactory7_RegisterStereoStatusWindow(IDXGIFactory7* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory7_RegisterStereoStatusEvent(IDXGIFactory7* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory7_UnregisterStereoStatus(IDXGIFactory7* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterStereoStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory7_RegisterOcclusionStatusWindow(IDXGIFactory7* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory7_RegisterOcclusionStatusEvent(IDXGIFactory7* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory7_UnregisterOcclusionStatus(IDXGIFactory7* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterOcclusionStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory7_CreateSwapChainForComposition(IDXGIFactory7* This,IUnknown *pDevice,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain);
}
/*** IDXGIFactory3 methods ***/
static inline UINT IDXGIFactory7_GetCreationFlags(IDXGIFactory7* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
/*** IDXGIFactory4 methods ***/
static inline HRESULT IDXGIFactory7_EnumAdapterByLuid(IDXGIFactory7* This,LUID luid,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter);
}
static inline HRESULT IDXGIFactory7_EnumWarpAdapter(IDXGIFactory7* This,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumWarpAdapter(This,iid,adapter);
}
/*** IDXGIFactory5 methods ***/
static inline HRESULT IDXGIFactory7_CheckFeatureSupport(IDXGIFactory7* This,DXGI_FEATURE feature,void *support_data,UINT support_data_size) {
    return This->lpVtbl->CheckFeatureSupport(This,feature,support_data,support_data_size);
}
/*** IDXGIFactory6 methods ***/
static inline HRESULT IDXGIFactory7_EnumAdapterByGpuPreference(IDXGIFactory7* This,UINT adapter_idx,DXGI_GPU_PREFERENCE gpu_preference,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumAdapterByGpuPreference(This,adapter_idx,gpu_preference,iid,adapter);
}
/*** IDXGIFactory7 methods ***/
static inline HRESULT IDXGIFactory7_RegisterAdaptersChangedEvent(IDXGIFactory7* This,HANDLE event,DWORD *cookie) {
    return This->lpVtbl->RegisterAdaptersChangedEvent(This,event,cookie);
}
static inline HRESULT IDXGIFactory7_UnregisterAdaptersChangedEvent(IDXGIFactory7* This,DWORD cookie) {
    return This->lpVtbl->UnregisterAdaptersChangedEvent(This,cookie);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory7_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dxgi1_6_h__ */
