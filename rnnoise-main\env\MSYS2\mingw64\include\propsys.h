/*** Autogenerated by WIDL 10.12 from include/propsys.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __propsys_h__
#define __propsys_h__

/* Forward declarations */

#ifndef __IInitializeWithFile_FWD_DEFINED__
#define __IInitializeWithFile_FWD_DEFINED__
typedef interface IInitializeWithFile IInitializeWithFile;
#ifdef __cplusplus
interface IInitializeWithFile;
#endif /* __cplusplus */
#endif

#ifndef __IInitializeWithStream_FWD_DEFINED__
#define __IInitializeWithStream_FWD_DEFINED__
typedef interface IInitializeWithStream IInitializeWithStream;
#ifdef __cplusplus
interface IInitializeWithStream;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyStore_FWD_DEFINED__
#define __IPropertyStore_FWD_DEFINED__
typedef interface IPropertyStore IPropertyStore;
#ifdef __cplusplus
interface IPropertyStore;
#endif /* __cplusplus */
#endif

#ifndef __INamedPropertyStore_FWD_DEFINED__
#define __INamedPropertyStore_FWD_DEFINED__
typedef interface INamedPropertyStore INamedPropertyStore;
#ifdef __cplusplus
interface INamedPropertyStore;
#endif /* __cplusplus */
#endif

#ifndef __IObjectWithPropertyKey_FWD_DEFINED__
#define __IObjectWithPropertyKey_FWD_DEFINED__
typedef interface IObjectWithPropertyKey IObjectWithPropertyKey;
#ifdef __cplusplus
interface IObjectWithPropertyKey;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyChange_FWD_DEFINED__
#define __IPropertyChange_FWD_DEFINED__
typedef interface IPropertyChange IPropertyChange;
#ifdef __cplusplus
interface IPropertyChange;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyChangeArray_FWD_DEFINED__
#define __IPropertyChangeArray_FWD_DEFINED__
typedef interface IPropertyChangeArray IPropertyChangeArray;
#ifdef __cplusplus
interface IPropertyChangeArray;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyStoreCapabilities_FWD_DEFINED__
#define __IPropertyStoreCapabilities_FWD_DEFINED__
typedef interface IPropertyStoreCapabilities IPropertyStoreCapabilities;
#ifdef __cplusplus
interface IPropertyStoreCapabilities;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyStoreCache_FWD_DEFINED__
#define __IPropertyStoreCache_FWD_DEFINED__
typedef interface IPropertyStoreCache IPropertyStoreCache;
#ifdef __cplusplus
interface IPropertyStoreCache;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyEnumType_FWD_DEFINED__
#define __IPropertyEnumType_FWD_DEFINED__
typedef interface IPropertyEnumType IPropertyEnumType;
#ifdef __cplusplus
interface IPropertyEnumType;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyEnumType2_FWD_DEFINED__
#define __IPropertyEnumType2_FWD_DEFINED__
typedef interface IPropertyEnumType2 IPropertyEnumType2;
#ifdef __cplusplus
interface IPropertyEnumType2;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyEnumTypeList_FWD_DEFINED__
#define __IPropertyEnumTypeList_FWD_DEFINED__
typedef interface IPropertyEnumTypeList IPropertyEnumTypeList;
#ifdef __cplusplus
interface IPropertyEnumTypeList;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyDescription_FWD_DEFINED__
#define __IPropertyDescription_FWD_DEFINED__
typedef interface IPropertyDescription IPropertyDescription;
#ifdef __cplusplus
interface IPropertyDescription;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyDescription2_FWD_DEFINED__
#define __IPropertyDescription2_FWD_DEFINED__
typedef interface IPropertyDescription2 IPropertyDescription2;
#ifdef __cplusplus
interface IPropertyDescription2;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyDescriptionAliasInfo_FWD_DEFINED__
#define __IPropertyDescriptionAliasInfo_FWD_DEFINED__
typedef interface IPropertyDescriptionAliasInfo IPropertyDescriptionAliasInfo;
#ifdef __cplusplus
interface IPropertyDescriptionAliasInfo;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyDescriptionSearchInfo_FWD_DEFINED__
#define __IPropertyDescriptionSearchInfo_FWD_DEFINED__
typedef interface IPropertyDescriptionSearchInfo IPropertyDescriptionSearchInfo;
#ifdef __cplusplus
interface IPropertyDescriptionSearchInfo;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyDescriptionRelatedPropertyInfo_FWD_DEFINED__
#define __IPropertyDescriptionRelatedPropertyInfo_FWD_DEFINED__
typedef interface IPropertyDescriptionRelatedPropertyInfo IPropertyDescriptionRelatedPropertyInfo;
#ifdef __cplusplus
interface IPropertyDescriptionRelatedPropertyInfo;
#endif /* __cplusplus */
#endif

#ifndef __IPropertySystem_FWD_DEFINED__
#define __IPropertySystem_FWD_DEFINED__
typedef interface IPropertySystem IPropertySystem;
#ifdef __cplusplus
interface IPropertySystem;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyDescriptionList_FWD_DEFINED__
#define __IPropertyDescriptionList_FWD_DEFINED__
typedef interface IPropertyDescriptionList IPropertyDescriptionList;
#ifdef __cplusplus
interface IPropertyDescriptionList;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyStoreFactory_FWD_DEFINED__
#define __IPropertyStoreFactory_FWD_DEFINED__
typedef interface IPropertyStoreFactory IPropertyStoreFactory;
#ifdef __cplusplus
interface IPropertyStoreFactory;
#endif /* __cplusplus */
#endif

#ifndef __IDelayedPropertyStoreFactory_FWD_DEFINED__
#define __IDelayedPropertyStoreFactory_FWD_DEFINED__
typedef interface IDelayedPropertyStoreFactory IDelayedPropertyStoreFactory;
#ifdef __cplusplus
interface IDelayedPropertyStoreFactory;
#endif /* __cplusplus */
#endif

#ifndef __IPersistSerializedPropStorage_FWD_DEFINED__
#define __IPersistSerializedPropStorage_FWD_DEFINED__
typedef interface IPersistSerializedPropStorage IPersistSerializedPropStorage;
#ifdef __cplusplus
interface IPersistSerializedPropStorage;
#endif /* __cplusplus */
#endif

#ifndef __IPersistSerializedPropStorage2_FWD_DEFINED__
#define __IPersistSerializedPropStorage2_FWD_DEFINED__
typedef interface IPersistSerializedPropStorage2 IPersistSerializedPropStorage2;
#ifdef __cplusplus
interface IPersistSerializedPropStorage2;
#endif /* __cplusplus */
#endif

#ifndef __IPropertySystemChangeNotify_FWD_DEFINED__
#define __IPropertySystemChangeNotify_FWD_DEFINED__
typedef interface IPropertySystemChangeNotify IPropertySystemChangeNotify;
#ifdef __cplusplus
interface IPropertySystemChangeNotify;
#endif /* __cplusplus */
#endif

#ifndef __ICreateObject_FWD_DEFINED__
#define __ICreateObject_FWD_DEFINED__
typedef interface ICreateObject ICreateObject;
#ifdef __cplusplus
interface ICreateObject;
#endif /* __cplusplus */
#endif

#ifndef __InMemoryPropertyStore_FWD_DEFINED__
#define __InMemoryPropertyStore_FWD_DEFINED__
#ifdef __cplusplus
typedef class InMemoryPropertyStore InMemoryPropertyStore;
#else
typedef struct InMemoryPropertyStore InMemoryPropertyStore;
#endif /* defined __cplusplus */
#endif /* defined __InMemoryPropertyStore_FWD_DEFINED__ */

#ifndef __PropertySystem_FWD_DEFINED__
#define __PropertySystem_FWD_DEFINED__
#ifdef __cplusplus
typedef class PropertySystem PropertySystem;
#else
typedef struct PropertySystem PropertySystem;
#endif /* defined __cplusplus */
#endif /* defined __PropertySystem_FWD_DEFINED__ */

/* Headers for imported files */

#include <objidl.h>
#include <oleidl.h>
#include <ocidl.h>
#include <shtypes.h>
#include <structuredquerycondition.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>


#ifndef PSSTDAPI
#ifdef _PROPSYS_
#define PSSTDAPI STDAPI
#define PSSTDAPI_(type)   STDAPI_(type)
#else
#define PSSTDAPI EXTERN_C DECLSPEC_IMPORT HRESULT STDAPICALLTYPE
#define PSSTDAPI_(type) EXTERN_C DECLSPEC_IMPORT type STDAPICALLTYPE
#endif
#endif

#if 0
typedef PROPERTYKEY *REFPROPERTYKEY;
#endif

#include <propkeydef.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IInitializeWithFile interface
 */
#ifndef __IInitializeWithFile_INTERFACE_DEFINED__
#define __IInitializeWithFile_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInitializeWithFile, 0xb7d14566, 0x0509, 0x4cce, 0xa7,0x1f, 0x0a,0x55,0x42,0x33,0xbd,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b7d14566-0509-4cce-a71f-0a554233bd9b")
IInitializeWithFile : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        LPCWSTR pszFilePath,
        DWORD grfMode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInitializeWithFile, 0xb7d14566, 0x0509, 0x4cce, 0xa7,0x1f, 0x0a,0x55,0x42,0x33,0xbd,0x9b)
#endif
#else
typedef struct IInitializeWithFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInitializeWithFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInitializeWithFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInitializeWithFile *This);

    /*** IInitializeWithFile methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IInitializeWithFile *This,
        LPCWSTR pszFilePath,
        DWORD grfMode);

    END_INTERFACE
} IInitializeWithFileVtbl;

interface IInitializeWithFile {
    CONST_VTBL IInitializeWithFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInitializeWithFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInitializeWithFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInitializeWithFile_Release(This) (This)->lpVtbl->Release(This)
/*** IInitializeWithFile methods ***/
#define IInitializeWithFile_Initialize(This,pszFilePath,grfMode) (This)->lpVtbl->Initialize(This,pszFilePath,grfMode)
#else
/*** IUnknown methods ***/
static inline HRESULT IInitializeWithFile_QueryInterface(IInitializeWithFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInitializeWithFile_AddRef(IInitializeWithFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInitializeWithFile_Release(IInitializeWithFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInitializeWithFile methods ***/
static inline HRESULT IInitializeWithFile_Initialize(IInitializeWithFile* This,LPCWSTR pszFilePath,DWORD grfMode) {
    return This->lpVtbl->Initialize(This,pszFilePath,grfMode);
}
#endif
#endif

#endif


#endif  /* __IInitializeWithFile_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IInitializeWithStream interface
 */
#ifndef __IInitializeWithStream_INTERFACE_DEFINED__
#define __IInitializeWithStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInitializeWithStream, 0xb824b49d, 0x22ac, 0x4161, 0xac,0x8a, 0x99,0x16,0xe8,0xfa,0x3f,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b824b49d-22ac-4161-ac8a-9916e8fa3f7f")
IInitializeWithStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IStream *pstream,
        DWORD grfMode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInitializeWithStream, 0xb824b49d, 0x22ac, 0x4161, 0xac,0x8a, 0x99,0x16,0xe8,0xfa,0x3f,0x7f)
#endif
#else
typedef struct IInitializeWithStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInitializeWithStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInitializeWithStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInitializeWithStream *This);

    /*** IInitializeWithStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IInitializeWithStream *This,
        IStream *pstream,
        DWORD grfMode);

    END_INTERFACE
} IInitializeWithStreamVtbl;

interface IInitializeWithStream {
    CONST_VTBL IInitializeWithStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInitializeWithStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInitializeWithStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInitializeWithStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInitializeWithStream methods ***/
#define IInitializeWithStream_Initialize(This,pstream,grfMode) (This)->lpVtbl->Initialize(This,pstream,grfMode)
#else
/*** IUnknown methods ***/
static inline HRESULT IInitializeWithStream_QueryInterface(IInitializeWithStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInitializeWithStream_AddRef(IInitializeWithStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInitializeWithStream_Release(IInitializeWithStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInitializeWithStream methods ***/
static inline HRESULT IInitializeWithStream_Initialize(IInitializeWithStream* This,IStream *pstream,DWORD grfMode) {
    return This->lpVtbl->Initialize(This,pstream,grfMode);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IInitializeWithStream_RemoteInitialize_Proxy(
    IInitializeWithStream* This,
    IStream *pstream,
    DWORD grfMode);
void __RPC_STUB IInitializeWithStream_RemoteInitialize_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IInitializeWithStream_Initialize_Proxy(
    IInitializeWithStream* This,
    IStream *pstream,
    DWORD grfMode);
HRESULT __RPC_STUB IInitializeWithStream_Initialize_Stub(
    IInitializeWithStream* This,
    IStream *pstream,
    DWORD grfMode);

#endif  /* __IInitializeWithStream_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IPropertyStore interface
 */
#ifndef __IPropertyStore_INTERFACE_DEFINED__
#define __IPropertyStore_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyStore, 0x886d8eeb, 0x8cf2, 0x4446, 0x8d,0x02, 0xcd,0xba,0x1d,0xbd,0xcf,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("886d8eeb-8cf2-4446-8d02-cdba1dbdcf99")
IPropertyStore : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *cProps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        DWORD iProp,
        PROPERTYKEY *pkey) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        REFPROPERTYKEY key,
        PROPVARIANT *pv) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        REFPROPERTYKEY key,
        REFPROPVARIANT propvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyStore, 0x886d8eeb, 0x8cf2, 0x4446, 0x8d,0x02, 0xcd,0xba,0x1d,0xbd,0xcf,0x99)
#endif
#else
typedef struct IPropertyStoreVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyStore *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyStore *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyStore *This);

    /*** IPropertyStore methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPropertyStore *This,
        DWORD *cProps);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPropertyStore *This,
        DWORD iProp,
        PROPERTYKEY *pkey);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IPropertyStore *This,
        REFPROPERTYKEY key,
        PROPVARIANT *pv);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IPropertyStore *This,
        REFPROPERTYKEY key,
        REFPROPVARIANT propvar);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IPropertyStore *This);

    END_INTERFACE
} IPropertyStoreVtbl;

interface IPropertyStore {
    CONST_VTBL IPropertyStoreVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyStore_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyStore_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyStore_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyStore methods ***/
#define IPropertyStore_GetCount(This,cProps) (This)->lpVtbl->GetCount(This,cProps)
#define IPropertyStore_GetAt(This,iProp,pkey) (This)->lpVtbl->GetAt(This,iProp,pkey)
#define IPropertyStore_GetValue(This,key,pv) (This)->lpVtbl->GetValue(This,key,pv)
#define IPropertyStore_SetValue(This,key,propvar) (This)->lpVtbl->SetValue(This,key,propvar)
#define IPropertyStore_Commit(This) (This)->lpVtbl->Commit(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyStore_QueryInterface(IPropertyStore* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyStore_AddRef(IPropertyStore* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyStore_Release(IPropertyStore* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyStore methods ***/
static inline HRESULT IPropertyStore_GetCount(IPropertyStore* This,DWORD *cProps) {
    return This->lpVtbl->GetCount(This,cProps);
}
static inline HRESULT IPropertyStore_GetAt(IPropertyStore* This,DWORD iProp,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetAt(This,iProp,pkey);
}
static inline HRESULT IPropertyStore_GetValue(IPropertyStore* This,REFPROPERTYKEY key,PROPVARIANT *pv) {
    return This->lpVtbl->GetValue(This,key,pv);
}
static inline HRESULT IPropertyStore_SetValue(IPropertyStore* This,REFPROPERTYKEY key,REFPROPVARIANT propvar) {
    return This->lpVtbl->SetValue(This,key,propvar);
}
static inline HRESULT IPropertyStore_Commit(IPropertyStore* This) {
    return This->lpVtbl->Commit(This);
}
#endif
#endif

#endif


#endif  /* __IPropertyStore_INTERFACE_DEFINED__ */


typedef IPropertyStore *LPPROPERTYSTORE;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * INamedPropertyStore interface
 */
#ifndef __INamedPropertyStore_INTERFACE_DEFINED__
#define __INamedPropertyStore_INTERFACE_DEFINED__

DEFINE_GUID(IID_INamedPropertyStore, 0x71604b0f, 0x97b0, 0x4764, 0x85,0x77, 0x2f,0x13,0xe9,0x8a,0x14,0x22);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71604b0f-97b0-4764-8577-2f13e98a1422")
INamedPropertyStore : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNamedValue(
        LPCWSTR pszName,
        PROPVARIANT *ppropvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNamedValue(
        LPCWSTR pszName,
        REFPROPVARIANT propvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNameCount(
        DWORD *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNameAt(
        DWORD iProp,
        BSTR *pbstrName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INamedPropertyStore, 0x71604b0f, 0x97b0, 0x4764, 0x85,0x77, 0x2f,0x13,0xe9,0x8a,0x14,0x22)
#endif
#else
typedef struct INamedPropertyStoreVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INamedPropertyStore *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INamedPropertyStore *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INamedPropertyStore *This);

    /*** INamedPropertyStore methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNamedValue)(
        INamedPropertyStore *This,
        LPCWSTR pszName,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *SetNamedValue)(
        INamedPropertyStore *This,
        LPCWSTR pszName,
        REFPROPVARIANT propvar);

    HRESULT (STDMETHODCALLTYPE *GetNameCount)(
        INamedPropertyStore *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *GetNameAt)(
        INamedPropertyStore *This,
        DWORD iProp,
        BSTR *pbstrName);

    END_INTERFACE
} INamedPropertyStoreVtbl;

interface INamedPropertyStore {
    CONST_VTBL INamedPropertyStoreVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INamedPropertyStore_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INamedPropertyStore_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INamedPropertyStore_Release(This) (This)->lpVtbl->Release(This)
/*** INamedPropertyStore methods ***/
#define INamedPropertyStore_GetNamedValue(This,pszName,ppropvar) (This)->lpVtbl->GetNamedValue(This,pszName,ppropvar)
#define INamedPropertyStore_SetNamedValue(This,pszName,propvar) (This)->lpVtbl->SetNamedValue(This,pszName,propvar)
#define INamedPropertyStore_GetNameCount(This,pdwCount) (This)->lpVtbl->GetNameCount(This,pdwCount)
#define INamedPropertyStore_GetNameAt(This,iProp,pbstrName) (This)->lpVtbl->GetNameAt(This,iProp,pbstrName)
#else
/*** IUnknown methods ***/
static inline HRESULT INamedPropertyStore_QueryInterface(INamedPropertyStore* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INamedPropertyStore_AddRef(INamedPropertyStore* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INamedPropertyStore_Release(INamedPropertyStore* This) {
    return This->lpVtbl->Release(This);
}
/*** INamedPropertyStore methods ***/
static inline HRESULT INamedPropertyStore_GetNamedValue(INamedPropertyStore* This,LPCWSTR pszName,PROPVARIANT *ppropvar) {
    return This->lpVtbl->GetNamedValue(This,pszName,ppropvar);
}
static inline HRESULT INamedPropertyStore_SetNamedValue(INamedPropertyStore* This,LPCWSTR pszName,REFPROPVARIANT propvar) {
    return This->lpVtbl->SetNamedValue(This,pszName,propvar);
}
static inline HRESULT INamedPropertyStore_GetNameCount(INamedPropertyStore* This,DWORD *pdwCount) {
    return This->lpVtbl->GetNameCount(This,pdwCount);
}
static inline HRESULT INamedPropertyStore_GetNameAt(INamedPropertyStore* This,DWORD iProp,BSTR *pbstrName) {
    return This->lpVtbl->GetNameAt(This,iProp,pbstrName);
}
#endif
#endif

#endif


#endif  /* __INamedPropertyStore_INTERFACE_DEFINED__ */


typedef enum GETPROPERTYSTOREFLAGS {
    GPS_DEFAULT = 0x0,
    GPS_HANDLERPROPERTIESONLY = 0x1,
    GPS_READWRITE = 0x2,
    GPS_TEMPORARY = 0x4,
    GPS_FASTPROPERTIESONLY = 0x8,
    GPS_OPENSLOWITEM = 0x10,
    GPS_DELAYCREATION = 0x20,
    GPS_BESTEFFORT = 0x40,
    GPS_NO_OPLOCK = 0x80,
    GPS_PREFERQUERYPROPERTIES = 0x100,
    GPS_MASK_VALID = 0x1ff
} GETPROPERTYSTOREFLAGS;

DEFINE_ENUM_FLAG_OPERATORS(GETPROPERTYSTOREFLAGS)

/*****************************************************************************
 * IObjectWithPropertyKey interface
 */
#ifndef __IObjectWithPropertyKey_INTERFACE_DEFINED__
#define __IObjectWithPropertyKey_INTERFACE_DEFINED__

DEFINE_GUID(IID_IObjectWithPropertyKey, 0xfc0ca0a7, 0xc316, 0x4fd2, 0x90,0x31, 0x3e,0x62,0x8e,0x6d,0x4f,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fc0ca0a7-c316-4fd2-9031-3e628e6d4f23")
IObjectWithPropertyKey : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetPropertyKey(
        REFPROPERTYKEY key) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyKey(
        PROPERTYKEY *pkey) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IObjectWithPropertyKey, 0xfc0ca0a7, 0xc316, 0x4fd2, 0x90,0x31, 0x3e,0x62,0x8e,0x6d,0x4f,0x23)
#endif
#else
typedef struct IObjectWithPropertyKeyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IObjectWithPropertyKey *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IObjectWithPropertyKey *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IObjectWithPropertyKey *This);

    /*** IObjectWithPropertyKey methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPropertyKey)(
        IObjectWithPropertyKey *This,
        REFPROPERTYKEY key);

    HRESULT (STDMETHODCALLTYPE *GetPropertyKey)(
        IObjectWithPropertyKey *This,
        PROPERTYKEY *pkey);

    END_INTERFACE
} IObjectWithPropertyKeyVtbl;

interface IObjectWithPropertyKey {
    CONST_VTBL IObjectWithPropertyKeyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IObjectWithPropertyKey_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IObjectWithPropertyKey_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IObjectWithPropertyKey_Release(This) (This)->lpVtbl->Release(This)
/*** IObjectWithPropertyKey methods ***/
#define IObjectWithPropertyKey_SetPropertyKey(This,key) (This)->lpVtbl->SetPropertyKey(This,key)
#define IObjectWithPropertyKey_GetPropertyKey(This,pkey) (This)->lpVtbl->GetPropertyKey(This,pkey)
#else
/*** IUnknown methods ***/
static inline HRESULT IObjectWithPropertyKey_QueryInterface(IObjectWithPropertyKey* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IObjectWithPropertyKey_AddRef(IObjectWithPropertyKey* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IObjectWithPropertyKey_Release(IObjectWithPropertyKey* This) {
    return This->lpVtbl->Release(This);
}
/*** IObjectWithPropertyKey methods ***/
static inline HRESULT IObjectWithPropertyKey_SetPropertyKey(IObjectWithPropertyKey* This,REFPROPERTYKEY key) {
    return This->lpVtbl->SetPropertyKey(This,key);
}
static inline HRESULT IObjectWithPropertyKey_GetPropertyKey(IObjectWithPropertyKey* This,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetPropertyKey(This,pkey);
}
#endif
#endif

#endif


#endif  /* __IObjectWithPropertyKey_INTERFACE_DEFINED__ */


typedef enum PKA_FLAGS {
    PKA_SET = 0,
    PKA_APPEND = 1,
    PKA_DELETE = 2
} PKA_FLAGS;

/*****************************************************************************
 * IPropertyChange interface
 */
#ifndef __IPropertyChange_INTERFACE_DEFINED__
#define __IPropertyChange_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyChange, 0xf917bc8a, 0x1bba, 0x4478, 0xa2,0x45, 0x1b,0xde,0x03,0xeb,0x94,0x31);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f917bc8a-1bba-4478-a245-1bde03eb9431")
IPropertyChange : public IObjectWithPropertyKey
{
    virtual HRESULT STDMETHODCALLTYPE ApplyToPropVariant(
        REFPROPVARIANT propvarIn,
        PROPVARIANT *ppropvarOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyChange, 0xf917bc8a, 0x1bba, 0x4478, 0xa2,0x45, 0x1b,0xde,0x03,0xeb,0x94,0x31)
#endif
#else
typedef struct IPropertyChangeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyChange *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyChange *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyChange *This);

    /*** IObjectWithPropertyKey methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPropertyKey)(
        IPropertyChange *This,
        REFPROPERTYKEY key);

    HRESULT (STDMETHODCALLTYPE *GetPropertyKey)(
        IPropertyChange *This,
        PROPERTYKEY *pkey);

    /*** IPropertyChange methods ***/
    HRESULT (STDMETHODCALLTYPE *ApplyToPropVariant)(
        IPropertyChange *This,
        REFPROPVARIANT propvarIn,
        PROPVARIANT *ppropvarOut);

    END_INTERFACE
} IPropertyChangeVtbl;

interface IPropertyChange {
    CONST_VTBL IPropertyChangeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyChange_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyChange_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyChange_Release(This) (This)->lpVtbl->Release(This)
/*** IObjectWithPropertyKey methods ***/
#define IPropertyChange_SetPropertyKey(This,key) (This)->lpVtbl->SetPropertyKey(This,key)
#define IPropertyChange_GetPropertyKey(This,pkey) (This)->lpVtbl->GetPropertyKey(This,pkey)
/*** IPropertyChange methods ***/
#define IPropertyChange_ApplyToPropVariant(This,propvarIn,ppropvarOut) (This)->lpVtbl->ApplyToPropVariant(This,propvarIn,ppropvarOut)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyChange_QueryInterface(IPropertyChange* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyChange_AddRef(IPropertyChange* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyChange_Release(IPropertyChange* This) {
    return This->lpVtbl->Release(This);
}
/*** IObjectWithPropertyKey methods ***/
static inline HRESULT IPropertyChange_SetPropertyKey(IPropertyChange* This,REFPROPERTYKEY key) {
    return This->lpVtbl->SetPropertyKey(This,key);
}
static inline HRESULT IPropertyChange_GetPropertyKey(IPropertyChange* This,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetPropertyKey(This,pkey);
}
/*** IPropertyChange methods ***/
static inline HRESULT IPropertyChange_ApplyToPropVariant(IPropertyChange* This,REFPROPVARIANT propvarIn,PROPVARIANT *ppropvarOut) {
    return This->lpVtbl->ApplyToPropVariant(This,propvarIn,ppropvarOut);
}
#endif
#endif

#endif


#endif  /* __IPropertyChange_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyChangeArray interface
 */
#ifndef __IPropertyChangeArray_INTERFACE_DEFINED__
#define __IPropertyChangeArray_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyChangeArray, 0x380f5cad, 0x1b5e, 0x42f2, 0x80,0x5d, 0x63,0x7f,0xd3,0x92,0xd3,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("380f5cad-1b5e-42f2-805d-637fd392d31e")
IPropertyChangeArray : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT *pcOperations) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT iIndex,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT iIndex,
        IPropertyChange *ppropChange) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IPropertyChange *ppropChange) = 0;

    virtual HRESULT STDMETHODCALLTYPE AppendOrReplace(
        IPropertyChange *ppropChange) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT iIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsKeyInArray(
        REFPROPERTYKEY key) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyChangeArray, 0x380f5cad, 0x1b5e, 0x42f2, 0x80,0x5d, 0x63,0x7f,0xd3,0x92,0xd3,0x1e)
#endif
#else
typedef struct IPropertyChangeArrayVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyChangeArray *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyChangeArray *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyChangeArray *This);

    /*** IPropertyChangeArray methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPropertyChangeArray *This,
        UINT *pcOperations);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPropertyChangeArray *This,
        UINT iIndex,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IPropertyChangeArray *This,
        UINT iIndex,
        IPropertyChange *ppropChange);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IPropertyChangeArray *This,
        IPropertyChange *ppropChange);

    HRESULT (STDMETHODCALLTYPE *AppendOrReplace)(
        IPropertyChangeArray *This,
        IPropertyChange *ppropChange);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IPropertyChangeArray *This,
        UINT iIndex);

    HRESULT (STDMETHODCALLTYPE *IsKeyInArray)(
        IPropertyChangeArray *This,
        REFPROPERTYKEY key);

    END_INTERFACE
} IPropertyChangeArrayVtbl;

interface IPropertyChangeArray {
    CONST_VTBL IPropertyChangeArrayVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyChangeArray_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyChangeArray_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyChangeArray_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyChangeArray methods ***/
#define IPropertyChangeArray_GetCount(This,pcOperations) (This)->lpVtbl->GetCount(This,pcOperations)
#define IPropertyChangeArray_GetAt(This,iIndex,riid,ppv) (This)->lpVtbl->GetAt(This,iIndex,riid,ppv)
#define IPropertyChangeArray_InsertAt(This,iIndex,ppropChange) (This)->lpVtbl->InsertAt(This,iIndex,ppropChange)
#define IPropertyChangeArray_Append(This,ppropChange) (This)->lpVtbl->Append(This,ppropChange)
#define IPropertyChangeArray_AppendOrReplace(This,ppropChange) (This)->lpVtbl->AppendOrReplace(This,ppropChange)
#define IPropertyChangeArray_RemoveAt(This,iIndex) (This)->lpVtbl->RemoveAt(This,iIndex)
#define IPropertyChangeArray_IsKeyInArray(This,key) (This)->lpVtbl->IsKeyInArray(This,key)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyChangeArray_QueryInterface(IPropertyChangeArray* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyChangeArray_AddRef(IPropertyChangeArray* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyChangeArray_Release(IPropertyChangeArray* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyChangeArray methods ***/
static inline HRESULT IPropertyChangeArray_GetCount(IPropertyChangeArray* This,UINT *pcOperations) {
    return This->lpVtbl->GetCount(This,pcOperations);
}
static inline HRESULT IPropertyChangeArray_GetAt(IPropertyChangeArray* This,UINT iIndex,REFIID riid,void **ppv) {
    return This->lpVtbl->GetAt(This,iIndex,riid,ppv);
}
static inline HRESULT IPropertyChangeArray_InsertAt(IPropertyChangeArray* This,UINT iIndex,IPropertyChange *ppropChange) {
    return This->lpVtbl->InsertAt(This,iIndex,ppropChange);
}
static inline HRESULT IPropertyChangeArray_Append(IPropertyChangeArray* This,IPropertyChange *ppropChange) {
    return This->lpVtbl->Append(This,ppropChange);
}
static inline HRESULT IPropertyChangeArray_AppendOrReplace(IPropertyChangeArray* This,IPropertyChange *ppropChange) {
    return This->lpVtbl->AppendOrReplace(This,ppropChange);
}
static inline HRESULT IPropertyChangeArray_RemoveAt(IPropertyChangeArray* This,UINT iIndex) {
    return This->lpVtbl->RemoveAt(This,iIndex);
}
static inline HRESULT IPropertyChangeArray_IsKeyInArray(IPropertyChangeArray* This,REFPROPERTYKEY key) {
    return This->lpVtbl->IsKeyInArray(This,key);
}
#endif
#endif

#endif


#endif  /* __IPropertyChangeArray_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyStoreCapabilities interface
 */
#ifndef __IPropertyStoreCapabilities_INTERFACE_DEFINED__
#define __IPropertyStoreCapabilities_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyStoreCapabilities, 0xc8e2d566, 0x186e, 0x4d49, 0xbf,0x41, 0x69,0x09,0xea,0xd5,0x6a,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c8e2d566-186e-4d49-bf41-6909ead56acc")
IPropertyStoreCapabilities : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsPropertyWritable(
        REFPROPERTYKEY key) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyStoreCapabilities, 0xc8e2d566, 0x186e, 0x4d49, 0xbf,0x41, 0x69,0x09,0xea,0xd5,0x6a,0xcc)
#endif
#else
typedef struct IPropertyStoreCapabilitiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyStoreCapabilities *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyStoreCapabilities *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyStoreCapabilities *This);

    /*** IPropertyStoreCapabilities methods ***/
    HRESULT (STDMETHODCALLTYPE *IsPropertyWritable)(
        IPropertyStoreCapabilities *This,
        REFPROPERTYKEY key);

    END_INTERFACE
} IPropertyStoreCapabilitiesVtbl;

interface IPropertyStoreCapabilities {
    CONST_VTBL IPropertyStoreCapabilitiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyStoreCapabilities_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyStoreCapabilities_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyStoreCapabilities_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyStoreCapabilities methods ***/
#define IPropertyStoreCapabilities_IsPropertyWritable(This,key) (This)->lpVtbl->IsPropertyWritable(This,key)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyStoreCapabilities_QueryInterface(IPropertyStoreCapabilities* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyStoreCapabilities_AddRef(IPropertyStoreCapabilities* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyStoreCapabilities_Release(IPropertyStoreCapabilities* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyStoreCapabilities methods ***/
static inline HRESULT IPropertyStoreCapabilities_IsPropertyWritable(IPropertyStoreCapabilities* This,REFPROPERTYKEY key) {
    return This->lpVtbl->IsPropertyWritable(This,key);
}
#endif
#endif

#endif


#endif  /* __IPropertyStoreCapabilities_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyStoreCache interface
 */
#ifndef __IPropertyStoreCache_INTERFACE_DEFINED__
#define __IPropertyStoreCache_INTERFACE_DEFINED__

typedef enum PSC_STATE {
    PSC_NORMAL = 0,
    PSC_NOTINSOURCE = 1,
    PSC_DIRTY = 2,
    PSC_READONLY = 3
} PSC_STATE;

DEFINE_GUID(IID_IPropertyStoreCache, 0x3017056d, 0x9a91, 0x4e90, 0x93,0x7d, 0x74,0x6c,0x72,0xab,0xbf,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3017056d-9a91-4e90-937d-746c72abbf4f")
IPropertyStoreCache : public IPropertyStore
{
    virtual HRESULT STDMETHODCALLTYPE GetState(
        REFPROPERTYKEY key,
        PSC_STATE *pstate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValueAndState(
        REFPROPERTYKEY key,
        PROPVARIANT *ppropvar,
        PSC_STATE *pstate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetState(
        REFPROPERTYKEY key,
        PSC_STATE state) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValueAndState(
        REFPROPERTYKEY key,
        const PROPVARIANT *ppropvar,
        PSC_STATE state) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyStoreCache, 0x3017056d, 0x9a91, 0x4e90, 0x93,0x7d, 0x74,0x6c,0x72,0xab,0xbf,0x4f)
#endif
#else
typedef struct IPropertyStoreCacheVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyStoreCache *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyStoreCache *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyStoreCache *This);

    /*** IPropertyStore methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPropertyStoreCache *This,
        DWORD *cProps);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPropertyStoreCache *This,
        DWORD iProp,
        PROPERTYKEY *pkey);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IPropertyStoreCache *This,
        REFPROPERTYKEY key,
        PROPVARIANT *pv);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IPropertyStoreCache *This,
        REFPROPERTYKEY key,
        REFPROPVARIANT propvar);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IPropertyStoreCache *This);

    /*** IPropertyStoreCache methods ***/
    HRESULT (STDMETHODCALLTYPE *GetState)(
        IPropertyStoreCache *This,
        REFPROPERTYKEY key,
        PSC_STATE *pstate);

    HRESULT (STDMETHODCALLTYPE *GetValueAndState)(
        IPropertyStoreCache *This,
        REFPROPERTYKEY key,
        PROPVARIANT *ppropvar,
        PSC_STATE *pstate);

    HRESULT (STDMETHODCALLTYPE *SetState)(
        IPropertyStoreCache *This,
        REFPROPERTYKEY key,
        PSC_STATE state);

    HRESULT (STDMETHODCALLTYPE *SetValueAndState)(
        IPropertyStoreCache *This,
        REFPROPERTYKEY key,
        const PROPVARIANT *ppropvar,
        PSC_STATE state);

    END_INTERFACE
} IPropertyStoreCacheVtbl;

interface IPropertyStoreCache {
    CONST_VTBL IPropertyStoreCacheVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyStoreCache_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyStoreCache_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyStoreCache_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyStore methods ***/
#define IPropertyStoreCache_GetCount(This,cProps) (This)->lpVtbl->GetCount(This,cProps)
#define IPropertyStoreCache_GetAt(This,iProp,pkey) (This)->lpVtbl->GetAt(This,iProp,pkey)
#define IPropertyStoreCache_GetValue(This,key,pv) (This)->lpVtbl->GetValue(This,key,pv)
#define IPropertyStoreCache_SetValue(This,key,propvar) (This)->lpVtbl->SetValue(This,key,propvar)
#define IPropertyStoreCache_Commit(This) (This)->lpVtbl->Commit(This)
/*** IPropertyStoreCache methods ***/
#define IPropertyStoreCache_GetState(This,key,pstate) (This)->lpVtbl->GetState(This,key,pstate)
#define IPropertyStoreCache_GetValueAndState(This,key,ppropvar,pstate) (This)->lpVtbl->GetValueAndState(This,key,ppropvar,pstate)
#define IPropertyStoreCache_SetState(This,key,state) (This)->lpVtbl->SetState(This,key,state)
#define IPropertyStoreCache_SetValueAndState(This,key,ppropvar,state) (This)->lpVtbl->SetValueAndState(This,key,ppropvar,state)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyStoreCache_QueryInterface(IPropertyStoreCache* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyStoreCache_AddRef(IPropertyStoreCache* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyStoreCache_Release(IPropertyStoreCache* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyStore methods ***/
static inline HRESULT IPropertyStoreCache_GetCount(IPropertyStoreCache* This,DWORD *cProps) {
    return This->lpVtbl->GetCount(This,cProps);
}
static inline HRESULT IPropertyStoreCache_GetAt(IPropertyStoreCache* This,DWORD iProp,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetAt(This,iProp,pkey);
}
static inline HRESULT IPropertyStoreCache_GetValue(IPropertyStoreCache* This,REFPROPERTYKEY key,PROPVARIANT *pv) {
    return This->lpVtbl->GetValue(This,key,pv);
}
static inline HRESULT IPropertyStoreCache_SetValue(IPropertyStoreCache* This,REFPROPERTYKEY key,REFPROPVARIANT propvar) {
    return This->lpVtbl->SetValue(This,key,propvar);
}
static inline HRESULT IPropertyStoreCache_Commit(IPropertyStoreCache* This) {
    return This->lpVtbl->Commit(This);
}
/*** IPropertyStoreCache methods ***/
static inline HRESULT IPropertyStoreCache_GetState(IPropertyStoreCache* This,REFPROPERTYKEY key,PSC_STATE *pstate) {
    return This->lpVtbl->GetState(This,key,pstate);
}
static inline HRESULT IPropertyStoreCache_GetValueAndState(IPropertyStoreCache* This,REFPROPERTYKEY key,PROPVARIANT *ppropvar,PSC_STATE *pstate) {
    return This->lpVtbl->GetValueAndState(This,key,ppropvar,pstate);
}
static inline HRESULT IPropertyStoreCache_SetState(IPropertyStoreCache* This,REFPROPERTYKEY key,PSC_STATE state) {
    return This->lpVtbl->SetState(This,key,state);
}
static inline HRESULT IPropertyStoreCache_SetValueAndState(IPropertyStoreCache* This,REFPROPERTYKEY key,const PROPVARIANT *ppropvar,PSC_STATE state) {
    return This->lpVtbl->SetValueAndState(This,key,ppropvar,state);
}
#endif
#endif

#endif


#endif  /* __IPropertyStoreCache_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyEnumType interface
 */
#ifndef __IPropertyEnumType_INTERFACE_DEFINED__
#define __IPropertyEnumType_INTERFACE_DEFINED__

typedef enum PROPENUMTYPE {
    PET_DISCRETEVALUE = 0,
    PET_RANGEDVALUE = 1,
    PET_DEFAULTVALUE = 2,
    PET_ENDRANGE = 3
} PROPENUMTYPE;

DEFINE_GUID(IID_IPropertyEnumType, 0x11e1fbf9, 0x2d56, 0x4a6b, 0x8d,0xb3, 0x7c,0xd1,0x93,0xa4,0x71,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("11e1fbf9-2d56-4a6b-8db3-7cd193a471f2")
IPropertyEnumType : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEnumType(
        PROPENUMTYPE *penumtype) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        PROPVARIANT *ppropvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRangeMinValue(
        PROPVARIANT *ppropvarMin) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRangeSetValue(
        PROPVARIANT *ppropvarSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayText(
        LPWSTR *ppszDisplay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyEnumType, 0x11e1fbf9, 0x2d56, 0x4a6b, 0x8d,0xb3, 0x7c,0xd1,0x93,0xa4,0x71,0xf2)
#endif
#else
typedef struct IPropertyEnumTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyEnumType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyEnumType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyEnumType *This);

    /*** IPropertyEnumType methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEnumType)(
        IPropertyEnumType *This,
        PROPENUMTYPE *penumtype);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IPropertyEnumType *This,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *GetRangeMinValue)(
        IPropertyEnumType *This,
        PROPVARIANT *ppropvarMin);

    HRESULT (STDMETHODCALLTYPE *GetRangeSetValue)(
        IPropertyEnumType *This,
        PROPVARIANT *ppropvarSet);

    HRESULT (STDMETHODCALLTYPE *GetDisplayText)(
        IPropertyEnumType *This,
        LPWSTR *ppszDisplay);

    END_INTERFACE
} IPropertyEnumTypeVtbl;

interface IPropertyEnumType {
    CONST_VTBL IPropertyEnumTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyEnumType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyEnumType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyEnumType_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyEnumType methods ***/
#define IPropertyEnumType_GetEnumType(This,penumtype) (This)->lpVtbl->GetEnumType(This,penumtype)
#define IPropertyEnumType_GetValue(This,ppropvar) (This)->lpVtbl->GetValue(This,ppropvar)
#define IPropertyEnumType_GetRangeMinValue(This,ppropvarMin) (This)->lpVtbl->GetRangeMinValue(This,ppropvarMin)
#define IPropertyEnumType_GetRangeSetValue(This,ppropvarSet) (This)->lpVtbl->GetRangeSetValue(This,ppropvarSet)
#define IPropertyEnumType_GetDisplayText(This,ppszDisplay) (This)->lpVtbl->GetDisplayText(This,ppszDisplay)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyEnumType_QueryInterface(IPropertyEnumType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyEnumType_AddRef(IPropertyEnumType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyEnumType_Release(IPropertyEnumType* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyEnumType methods ***/
static inline HRESULT IPropertyEnumType_GetEnumType(IPropertyEnumType* This,PROPENUMTYPE *penumtype) {
    return This->lpVtbl->GetEnumType(This,penumtype);
}
static inline HRESULT IPropertyEnumType_GetValue(IPropertyEnumType* This,PROPVARIANT *ppropvar) {
    return This->lpVtbl->GetValue(This,ppropvar);
}
static inline HRESULT IPropertyEnumType_GetRangeMinValue(IPropertyEnumType* This,PROPVARIANT *ppropvarMin) {
    return This->lpVtbl->GetRangeMinValue(This,ppropvarMin);
}
static inline HRESULT IPropertyEnumType_GetRangeSetValue(IPropertyEnumType* This,PROPVARIANT *ppropvarSet) {
    return This->lpVtbl->GetRangeSetValue(This,ppropvarSet);
}
static inline HRESULT IPropertyEnumType_GetDisplayText(IPropertyEnumType* This,LPWSTR *ppszDisplay) {
    return This->lpVtbl->GetDisplayText(This,ppszDisplay);
}
#endif
#endif

#endif


#endif  /* __IPropertyEnumType_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyEnumType2 interface
 */
#ifndef __IPropertyEnumType2_INTERFACE_DEFINED__
#define __IPropertyEnumType2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyEnumType2, 0x9b6e051c, 0x5ddd, 0x4321, 0x90,0x70, 0xfe,0x2a,0xcb,0x55,0xe7,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9b6e051c-5ddd-4321-9070-fe2acb55e794")
IPropertyEnumType2 : public IPropertyEnumType
{
    virtual HRESULT STDMETHODCALLTYPE GetImageReference(
        LPWSTR *ppszImageRes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyEnumType2, 0x9b6e051c, 0x5ddd, 0x4321, 0x90,0x70, 0xfe,0x2a,0xcb,0x55,0xe7,0x94)
#endif
#else
typedef struct IPropertyEnumType2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyEnumType2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyEnumType2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyEnumType2 *This);

    /*** IPropertyEnumType methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEnumType)(
        IPropertyEnumType2 *This,
        PROPENUMTYPE *penumtype);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IPropertyEnumType2 *This,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *GetRangeMinValue)(
        IPropertyEnumType2 *This,
        PROPVARIANT *ppropvarMin);

    HRESULT (STDMETHODCALLTYPE *GetRangeSetValue)(
        IPropertyEnumType2 *This,
        PROPVARIANT *ppropvarSet);

    HRESULT (STDMETHODCALLTYPE *GetDisplayText)(
        IPropertyEnumType2 *This,
        LPWSTR *ppszDisplay);

    /*** IPropertyEnumType2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetImageReference)(
        IPropertyEnumType2 *This,
        LPWSTR *ppszImageRes);

    END_INTERFACE
} IPropertyEnumType2Vtbl;

interface IPropertyEnumType2 {
    CONST_VTBL IPropertyEnumType2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyEnumType2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyEnumType2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyEnumType2_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyEnumType methods ***/
#define IPropertyEnumType2_GetEnumType(This,penumtype) (This)->lpVtbl->GetEnumType(This,penumtype)
#define IPropertyEnumType2_GetValue(This,ppropvar) (This)->lpVtbl->GetValue(This,ppropvar)
#define IPropertyEnumType2_GetRangeMinValue(This,ppropvarMin) (This)->lpVtbl->GetRangeMinValue(This,ppropvarMin)
#define IPropertyEnumType2_GetRangeSetValue(This,ppropvarSet) (This)->lpVtbl->GetRangeSetValue(This,ppropvarSet)
#define IPropertyEnumType2_GetDisplayText(This,ppszDisplay) (This)->lpVtbl->GetDisplayText(This,ppszDisplay)
/*** IPropertyEnumType2 methods ***/
#define IPropertyEnumType2_GetImageReference(This,ppszImageRes) (This)->lpVtbl->GetImageReference(This,ppszImageRes)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyEnumType2_QueryInterface(IPropertyEnumType2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyEnumType2_AddRef(IPropertyEnumType2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyEnumType2_Release(IPropertyEnumType2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyEnumType methods ***/
static inline HRESULT IPropertyEnumType2_GetEnumType(IPropertyEnumType2* This,PROPENUMTYPE *penumtype) {
    return This->lpVtbl->GetEnumType(This,penumtype);
}
static inline HRESULT IPropertyEnumType2_GetValue(IPropertyEnumType2* This,PROPVARIANT *ppropvar) {
    return This->lpVtbl->GetValue(This,ppropvar);
}
static inline HRESULT IPropertyEnumType2_GetRangeMinValue(IPropertyEnumType2* This,PROPVARIANT *ppropvarMin) {
    return This->lpVtbl->GetRangeMinValue(This,ppropvarMin);
}
static inline HRESULT IPropertyEnumType2_GetRangeSetValue(IPropertyEnumType2* This,PROPVARIANT *ppropvarSet) {
    return This->lpVtbl->GetRangeSetValue(This,ppropvarSet);
}
static inline HRESULT IPropertyEnumType2_GetDisplayText(IPropertyEnumType2* This,LPWSTR *ppszDisplay) {
    return This->lpVtbl->GetDisplayText(This,ppszDisplay);
}
/*** IPropertyEnumType2 methods ***/
static inline HRESULT IPropertyEnumType2_GetImageReference(IPropertyEnumType2* This,LPWSTR *ppszImageRes) {
    return This->lpVtbl->GetImageReference(This,ppszImageRes);
}
#endif
#endif

#endif


#endif  /* __IPropertyEnumType2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyEnumTypeList interface
 */
#ifndef __IPropertyEnumTypeList_INTERFACE_DEFINED__
#define __IPropertyEnumTypeList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyEnumTypeList, 0xa99400f4, 0x3d84, 0x4557, 0x94,0xba, 0x12,0x42,0xfb,0x2c,0xc9,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a99400f4-3d84-4557-94ba-1242fb2cc9a6")
IPropertyEnumTypeList : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT *pctypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT itype,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConditionAt(
        UINT nIndex,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindMatchingIndex(
        REFPROPVARIANT propvarCmp,
        UINT *pnIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyEnumTypeList, 0xa99400f4, 0x3d84, 0x4557, 0x94,0xba, 0x12,0x42,0xfb,0x2c,0xc9,0xa6)
#endif
#else
typedef struct IPropertyEnumTypeListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyEnumTypeList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyEnumTypeList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyEnumTypeList *This);

    /*** IPropertyEnumTypeList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPropertyEnumTypeList *This,
        UINT *pctypes);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPropertyEnumTypeList *This,
        UINT itype,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *GetConditionAt)(
        IPropertyEnumTypeList *This,
        UINT nIndex,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *FindMatchingIndex)(
        IPropertyEnumTypeList *This,
        REFPROPVARIANT propvarCmp,
        UINT *pnIndex);

    END_INTERFACE
} IPropertyEnumTypeListVtbl;

interface IPropertyEnumTypeList {
    CONST_VTBL IPropertyEnumTypeListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyEnumTypeList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyEnumTypeList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyEnumTypeList_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyEnumTypeList methods ***/
#define IPropertyEnumTypeList_GetCount(This,pctypes) (This)->lpVtbl->GetCount(This,pctypes)
#define IPropertyEnumTypeList_GetAt(This,itype,riid,ppv) (This)->lpVtbl->GetAt(This,itype,riid,ppv)
#define IPropertyEnumTypeList_GetConditionAt(This,nIndex,riid,ppv) (This)->lpVtbl->GetConditionAt(This,nIndex,riid,ppv)
#define IPropertyEnumTypeList_FindMatchingIndex(This,propvarCmp,pnIndex) (This)->lpVtbl->FindMatchingIndex(This,propvarCmp,pnIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyEnumTypeList_QueryInterface(IPropertyEnumTypeList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyEnumTypeList_AddRef(IPropertyEnumTypeList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyEnumTypeList_Release(IPropertyEnumTypeList* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyEnumTypeList methods ***/
static inline HRESULT IPropertyEnumTypeList_GetCount(IPropertyEnumTypeList* This,UINT *pctypes) {
    return This->lpVtbl->GetCount(This,pctypes);
}
static inline HRESULT IPropertyEnumTypeList_GetAt(IPropertyEnumTypeList* This,UINT itype,REFIID riid,void **ppv) {
    return This->lpVtbl->GetAt(This,itype,riid,ppv);
}
static inline HRESULT IPropertyEnumTypeList_GetConditionAt(IPropertyEnumTypeList* This,UINT nIndex,REFIID riid,void **ppv) {
    return This->lpVtbl->GetConditionAt(This,nIndex,riid,ppv);
}
static inline HRESULT IPropertyEnumTypeList_FindMatchingIndex(IPropertyEnumTypeList* This,REFPROPVARIANT propvarCmp,UINT *pnIndex) {
    return This->lpVtbl->FindMatchingIndex(This,propvarCmp,pnIndex);
}
#endif
#endif

#endif


#endif  /* __IPropertyEnumTypeList_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyDescription interface
 */
#ifndef __IPropertyDescription_INTERFACE_DEFINED__
#define __IPropertyDescription_INTERFACE_DEFINED__

typedef enum PROPDESC_TYPE_FLAGS {
    PDTF_DEFAULT = 0x0,
    PDTF_MULTIPLEVALUES = 0x1,
    PDTF_ISINNATE = 0x2,
    PDTF_ISGROUP = 0x4,
    PDTF_CANGROUPBY = 0x8,
    PDTF_CANSTACKBY = 0x10,
    PDTF_ISTREEPROPERTY = 0x20,
    PDTF_INCLUDEINFULLTEXTQUERY = 0x40,
    PDTF_ISVIEWABLE = 0x80,
    PDTF_ISQUERYABLE = 0x100,
    PDTF_CANBEPURGED = 0x200,
    PDTF_SEARCHRAWVALUE = 0x400,
    PDTF_ISSYSTEMPROPERTY = 0x80000000,
    PDTF_MASK_ALL = 0x800007ff
} PROPDESC_TYPE_FLAGS;

DEFINE_ENUM_FLAG_OPERATORS(PROPDESC_TYPE_FLAGS)

typedef enum PROPDESC_VIEW_FLAGS {
    PDVF_DEFAULT = 0x0,
    PDVF_CENTERALIGN = 0x1,
    PDVF_RIGHTALIGN = 0x2,
    PDVF_BEGINNEWGROUP = 0x4,
    PDVF_FILLAREA = 0x8,
    PDVF_SORTDESCENDING = 0x10,
    PDVF_SHOWONLYIFPRESENT = 0x20,
    PDVF_SHOWBYDEFAULT = 0x40,
    PDVF_SHOWINPRIMARYLIST = 0x80,
    PDVF_SHOWINSECONDARYLIST = 0x100,
    PDVF_HIDELABEL = 0x200,
    PDVF_HIDDEN = 0x800,
    PDVF_CANWRAP = 0x1000,
    PDVF_MASK_ALL = 0x1bff
} PROPDESC_VIEW_FLAGS;

DEFINE_ENUM_FLAG_OPERATORS(PROPDESC_VIEW_FLAGS)

typedef enum PROPDESC_DISPLAYTYPE {
    PDDT_STRING = 0,
    PDDT_NUMBER = 1,
    PDDT_BOOLEAN = 2,
    PDDT_DATETIME = 3,
    PDDT_ENUMERATED = 4
} PROPDESC_DISPLAYTYPE;

typedef enum PROPDESC_GROUPING_RANGE {
    PDGR_DISCRETE = 0,
    PDGR_ALPHANUMERIC = 1,
    PDGR_SIZE = 2,
    PDGR_DYNAMIC = 3,
    PDGR_DATE = 4,
    PDGR_PERCENT = 5,
    PDGR_ENUMERATED = 6
} PROPDESC_GROUPING_RANGE;

typedef enum PROPDESC_FORMAT_FLAGS {
    PDFF_DEFAULT = 0x0,
    PDFF_PREFIXNAME = 0x1,
    PDFF_FILENAME = 0x2,
    PDFF_ALWAYSKB = 0x4,
    PDFF_RESERVED_RIGHTTOLEFT = 0x8,
    PDFF_SHORTTIME = 0x10,
    PDFF_LONGTIME = 0x20,
    PDFF_HIDETIME = 0x40,
    PDFF_SHORTDATE = 0x80,
    PDFF_LONGDATE = 0x100,
    PDFF_HIDEDATE = 0x200,
    PDFF_RELATIVEDATE = 0x400,
    PDFF_USEEDITINVITATION = 0x800,
    PDFF_READONLY = 0x1000,
    PDFF_NOAUTOREADINGORDER = 0x2000
} PROPDESC_FORMAT_FLAGS;

DEFINE_ENUM_FLAG_OPERATORS(PROPDESC_FORMAT_FLAGS)

typedef enum PROPDESC_SORTDESCRIPTION {
    PDSD_GENERAL = 0,
    PDSD_A_Z = 1,
    PDSD_LOWEST_HIGHEST = 2,
    PDSD_SMALLEST_BIGGEST = 3,
    PDSD_OLDEST_NEWEST = 4
} PROPDESC_SORTDESCRIPTION;

typedef enum PROPDESC_RELATIVEDESCRIPTION_TYPE {
    PDRDT_GENERAL = 0,
    PDRDT_DATE = 1,
    PDRDT_SIZE = 2,
    PDRDT_COUNT = 3,
    PDRDT_REVISION = 4,
    PDRDT_LENGTH = 5,
    PDRDT_DURATION = 6,
    PDRDT_SPEED = 7,
    PDRDT_RATE = 8,
    PDRDT_RATING = 9,
    PDRDT_PRIORITY = 10
} PROPDESC_RELATIVEDESCRIPTION_TYPE;

typedef enum PROPDESC_AGGREGATION_TYPE {
    PDAT_DEFAULT = 0,
    PDAT_FIRST = 1,
    PDAT_SUM = 2,
    PDAT_AVERAGE = 3,
    PDAT_DATERANGE = 4,
    PDAT_UNION = 5,
    PDAT_MAX = 6,
    PDAT_MIN = 7
} PROPDESC_AGGREGATION_TYPE;

typedef enum PROPDESC_CONDITION_TYPE {
    PDCOT_NONE = 0,
    PDCOT_STRING = 1,
    PDCOT_SIZE = 2,
    PDCOT_DATETIME = 3,
    PDCOT_BOOLEAN = 4,
    PDCOT_NUMBER = 5
} PROPDESC_CONDITION_TYPE;

DEFINE_GUID(IID_IPropertyDescription, 0x6f79d558, 0x3e96, 0x4549, 0xa1,0xd1, 0x7d,0x75,0xd2,0x28,0x88,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6f79d558-3e96-4549-a1d1-7d75d2288814")
IPropertyDescription : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyKey(
        PROPERTYKEY *pkey) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCanonicalName(
        LPWSTR *ppszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyType(
        VARTYPE *pvartype) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayName(
        LPWSTR *ppszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEditInvitation(
        LPWSTR *ppszInvite) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeFlags(
        PROPDESC_TYPE_FLAGS mask,
        PROPDESC_TYPE_FLAGS *ppdtFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetViewFlags(
        PROPDESC_VIEW_FLAGS *ppdvFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultColumnWidth(
        UINT *pcxChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayType(
        PROPDESC_DISPLAYTYPE *pdisplaytype) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColumnState(
        SHCOLSTATEF *pcsFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGroupingRange(
        PROPDESC_GROUPING_RANGE *pgr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelativeDescriptionType(
        PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelativeDescription(
        REFPROPVARIANT propvar1,
        REFPROPVARIANT propvar2,
        LPWSTR *ppszDesc1,
        LPWSTR *ppszDesc2) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSortDescription(
        PROPDESC_SORTDESCRIPTION *psd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSortDescriptionLabel(
        WINBOOL fDescending,
        LPWSTR *ppszDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAggregationType(
        PROPDESC_AGGREGATION_TYPE *paggtype) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConditionType(
        PROPDESC_CONDITION_TYPE *pcontype,
        CONDITION_OPERATION *popDefault) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumTypeList(
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE CoerceToCanonicalValue(
        PROPVARIANT *ppropvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE FormatForDisplay(
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdfFlags,
        LPWSTR *ppszDisplay) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsValueCanonical(
        REFPROPVARIANT propvar) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyDescription, 0x6f79d558, 0x3e96, 0x4549, 0xa1,0xd1, 0x7d,0x75,0xd2,0x28,0x88,0x14)
#endif
#else
typedef struct IPropertyDescriptionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyDescription *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyDescription *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyDescription *This);

    /*** IPropertyDescription methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyKey)(
        IPropertyDescription *This,
        PROPERTYKEY *pkey);

    HRESULT (STDMETHODCALLTYPE *GetCanonicalName)(
        IPropertyDescription *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetPropertyType)(
        IPropertyDescription *This,
        VARTYPE *pvartype);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IPropertyDescription *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetEditInvitation)(
        IPropertyDescription *This,
        LPWSTR *ppszInvite);

    HRESULT (STDMETHODCALLTYPE *GetTypeFlags)(
        IPropertyDescription *This,
        PROPDESC_TYPE_FLAGS mask,
        PROPDESC_TYPE_FLAGS *ppdtFlags);

    HRESULT (STDMETHODCALLTYPE *GetViewFlags)(
        IPropertyDescription *This,
        PROPDESC_VIEW_FLAGS *ppdvFlags);

    HRESULT (STDMETHODCALLTYPE *GetDefaultColumnWidth)(
        IPropertyDescription *This,
        UINT *pcxChars);

    HRESULT (STDMETHODCALLTYPE *GetDisplayType)(
        IPropertyDescription *This,
        PROPDESC_DISPLAYTYPE *pdisplaytype);

    HRESULT (STDMETHODCALLTYPE *GetColumnState)(
        IPropertyDescription *This,
        SHCOLSTATEF *pcsFlags);

    HRESULT (STDMETHODCALLTYPE *GetGroupingRange)(
        IPropertyDescription *This,
        PROPDESC_GROUPING_RANGE *pgr);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescriptionType)(
        IPropertyDescription *This,
        PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescription)(
        IPropertyDescription *This,
        REFPROPVARIANT propvar1,
        REFPROPVARIANT propvar2,
        LPWSTR *ppszDesc1,
        LPWSTR *ppszDesc2);

    HRESULT (STDMETHODCALLTYPE *GetSortDescription)(
        IPropertyDescription *This,
        PROPDESC_SORTDESCRIPTION *psd);

    HRESULT (STDMETHODCALLTYPE *GetSortDescriptionLabel)(
        IPropertyDescription *This,
        WINBOOL fDescending,
        LPWSTR *ppszDescription);

    HRESULT (STDMETHODCALLTYPE *GetAggregationType)(
        IPropertyDescription *This,
        PROPDESC_AGGREGATION_TYPE *paggtype);

    HRESULT (STDMETHODCALLTYPE *GetConditionType)(
        IPropertyDescription *This,
        PROPDESC_CONDITION_TYPE *pcontype,
        CONDITION_OPERATION *popDefault);

    HRESULT (STDMETHODCALLTYPE *GetEnumTypeList)(
        IPropertyDescription *This,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *CoerceToCanonicalValue)(
        IPropertyDescription *This,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *FormatForDisplay)(
        IPropertyDescription *This,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdfFlags,
        LPWSTR *ppszDisplay);

    HRESULT (STDMETHODCALLTYPE *IsValueCanonical)(
        IPropertyDescription *This,
        REFPROPVARIANT propvar);

    END_INTERFACE
} IPropertyDescriptionVtbl;

interface IPropertyDescription {
    CONST_VTBL IPropertyDescriptionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyDescription_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyDescription_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyDescription_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyDescription methods ***/
#define IPropertyDescription_GetPropertyKey(This,pkey) (This)->lpVtbl->GetPropertyKey(This,pkey)
#define IPropertyDescription_GetCanonicalName(This,ppszName) (This)->lpVtbl->GetCanonicalName(This,ppszName)
#define IPropertyDescription_GetPropertyType(This,pvartype) (This)->lpVtbl->GetPropertyType(This,pvartype)
#define IPropertyDescription_GetDisplayName(This,ppszName) (This)->lpVtbl->GetDisplayName(This,ppszName)
#define IPropertyDescription_GetEditInvitation(This,ppszInvite) (This)->lpVtbl->GetEditInvitation(This,ppszInvite)
#define IPropertyDescription_GetTypeFlags(This,mask,ppdtFlags) (This)->lpVtbl->GetTypeFlags(This,mask,ppdtFlags)
#define IPropertyDescription_GetViewFlags(This,ppdvFlags) (This)->lpVtbl->GetViewFlags(This,ppdvFlags)
#define IPropertyDescription_GetDefaultColumnWidth(This,pcxChars) (This)->lpVtbl->GetDefaultColumnWidth(This,pcxChars)
#define IPropertyDescription_GetDisplayType(This,pdisplaytype) (This)->lpVtbl->GetDisplayType(This,pdisplaytype)
#define IPropertyDescription_GetColumnState(This,pcsFlags) (This)->lpVtbl->GetColumnState(This,pcsFlags)
#define IPropertyDescription_GetGroupingRange(This,pgr) (This)->lpVtbl->GetGroupingRange(This,pgr)
#define IPropertyDescription_GetRelativeDescriptionType(This,prdt) (This)->lpVtbl->GetRelativeDescriptionType(This,prdt)
#define IPropertyDescription_GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2) (This)->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2)
#define IPropertyDescription_GetSortDescription(This,psd) (This)->lpVtbl->GetSortDescription(This,psd)
#define IPropertyDescription_GetSortDescriptionLabel(This,fDescending,ppszDescription) (This)->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription)
#define IPropertyDescription_GetAggregationType(This,paggtype) (This)->lpVtbl->GetAggregationType(This,paggtype)
#define IPropertyDescription_GetConditionType(This,pcontype,popDefault) (This)->lpVtbl->GetConditionType(This,pcontype,popDefault)
#define IPropertyDescription_GetEnumTypeList(This,riid,ppv) (This)->lpVtbl->GetEnumTypeList(This,riid,ppv)
#define IPropertyDescription_CoerceToCanonicalValue(This,ppropvar) (This)->lpVtbl->CoerceToCanonicalValue(This,ppropvar)
#define IPropertyDescription_FormatForDisplay(This,propvar,pdfFlags,ppszDisplay) (This)->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay)
#define IPropertyDescription_IsValueCanonical(This,propvar) (This)->lpVtbl->IsValueCanonical(This,propvar)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyDescription_QueryInterface(IPropertyDescription* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyDescription_AddRef(IPropertyDescription* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyDescription_Release(IPropertyDescription* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyDescription methods ***/
static inline HRESULT IPropertyDescription_GetPropertyKey(IPropertyDescription* This,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetPropertyKey(This,pkey);
}
static inline HRESULT IPropertyDescription_GetCanonicalName(IPropertyDescription* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetCanonicalName(This,ppszName);
}
static inline HRESULT IPropertyDescription_GetPropertyType(IPropertyDescription* This,VARTYPE *pvartype) {
    return This->lpVtbl->GetPropertyType(This,pvartype);
}
static inline HRESULT IPropertyDescription_GetDisplayName(IPropertyDescription* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetDisplayName(This,ppszName);
}
static inline HRESULT IPropertyDescription_GetEditInvitation(IPropertyDescription* This,LPWSTR *ppszInvite) {
    return This->lpVtbl->GetEditInvitation(This,ppszInvite);
}
static inline HRESULT IPropertyDescription_GetTypeFlags(IPropertyDescription* This,PROPDESC_TYPE_FLAGS mask,PROPDESC_TYPE_FLAGS *ppdtFlags) {
    return This->lpVtbl->GetTypeFlags(This,mask,ppdtFlags);
}
static inline HRESULT IPropertyDescription_GetViewFlags(IPropertyDescription* This,PROPDESC_VIEW_FLAGS *ppdvFlags) {
    return This->lpVtbl->GetViewFlags(This,ppdvFlags);
}
static inline HRESULT IPropertyDescription_GetDefaultColumnWidth(IPropertyDescription* This,UINT *pcxChars) {
    return This->lpVtbl->GetDefaultColumnWidth(This,pcxChars);
}
static inline HRESULT IPropertyDescription_GetDisplayType(IPropertyDescription* This,PROPDESC_DISPLAYTYPE *pdisplaytype) {
    return This->lpVtbl->GetDisplayType(This,pdisplaytype);
}
static inline HRESULT IPropertyDescription_GetColumnState(IPropertyDescription* This,SHCOLSTATEF *pcsFlags) {
    return This->lpVtbl->GetColumnState(This,pcsFlags);
}
static inline HRESULT IPropertyDescription_GetGroupingRange(IPropertyDescription* This,PROPDESC_GROUPING_RANGE *pgr) {
    return This->lpVtbl->GetGroupingRange(This,pgr);
}
static inline HRESULT IPropertyDescription_GetRelativeDescriptionType(IPropertyDescription* This,PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt) {
    return This->lpVtbl->GetRelativeDescriptionType(This,prdt);
}
static inline HRESULT IPropertyDescription_GetRelativeDescription(IPropertyDescription* This,REFPROPVARIANT propvar1,REFPROPVARIANT propvar2,LPWSTR *ppszDesc1,LPWSTR *ppszDesc2) {
    return This->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2);
}
static inline HRESULT IPropertyDescription_GetSortDescription(IPropertyDescription* This,PROPDESC_SORTDESCRIPTION *psd) {
    return This->lpVtbl->GetSortDescription(This,psd);
}
static inline HRESULT IPropertyDescription_GetSortDescriptionLabel(IPropertyDescription* This,WINBOOL fDescending,LPWSTR *ppszDescription) {
    return This->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription);
}
static inline HRESULT IPropertyDescription_GetAggregationType(IPropertyDescription* This,PROPDESC_AGGREGATION_TYPE *paggtype) {
    return This->lpVtbl->GetAggregationType(This,paggtype);
}
static inline HRESULT IPropertyDescription_GetConditionType(IPropertyDescription* This,PROPDESC_CONDITION_TYPE *pcontype,CONDITION_OPERATION *popDefault) {
    return This->lpVtbl->GetConditionType(This,pcontype,popDefault);
}
static inline HRESULT IPropertyDescription_GetEnumTypeList(IPropertyDescription* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetEnumTypeList(This,riid,ppv);
}
static inline HRESULT IPropertyDescription_CoerceToCanonicalValue(IPropertyDescription* This,PROPVARIANT *ppropvar) {
    return This->lpVtbl->CoerceToCanonicalValue(This,ppropvar);
}
static inline HRESULT IPropertyDescription_FormatForDisplay(IPropertyDescription* This,REFPROPVARIANT propvar,PROPDESC_FORMAT_FLAGS pdfFlags,LPWSTR *ppszDisplay) {
    return This->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay);
}
static inline HRESULT IPropertyDescription_IsValueCanonical(IPropertyDescription* This,REFPROPVARIANT propvar) {
    return This->lpVtbl->IsValueCanonical(This,propvar);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPropertyDescription_RemoteCoerceToCanonicalValue_Proxy(
    IPropertyDescription* This,
    REFPROPVARIANT propvar,
    PROPVARIANT *ppropvar);
void __RPC_STUB IPropertyDescription_RemoteCoerceToCanonicalValue_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IPropertyDescription_CoerceToCanonicalValue_Proxy(
    IPropertyDescription* This,
    PROPVARIANT *ppropvar);
HRESULT __RPC_STUB IPropertyDescription_CoerceToCanonicalValue_Stub(
    IPropertyDescription* This,
    REFPROPVARIANT propvar,
    PROPVARIANT *ppropvar);

#endif  /* __IPropertyDescription_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyDescription2 interface
 */
#ifndef __IPropertyDescription2_INTERFACE_DEFINED__
#define __IPropertyDescription2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyDescription2, 0x57d2eded, 0x5062, 0x400e, 0xb1,0x07, 0x5d,0xae,0x79,0xfe,0x57,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("57d2eded-5062-400e-b107-5dae79fe57a6")
IPropertyDescription2 : public IPropertyDescription
{
    virtual HRESULT STDMETHODCALLTYPE GetImageReferenceForValue(
        REFPROPVARIANT propvar,
        LPWSTR *ppszImageRes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyDescription2, 0x57d2eded, 0x5062, 0x400e, 0xb1,0x07, 0x5d,0xae,0x79,0xfe,0x57,0xa6)
#endif
#else
typedef struct IPropertyDescription2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyDescription2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyDescription2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyDescription2 *This);

    /*** IPropertyDescription methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyKey)(
        IPropertyDescription2 *This,
        PROPERTYKEY *pkey);

    HRESULT (STDMETHODCALLTYPE *GetCanonicalName)(
        IPropertyDescription2 *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetPropertyType)(
        IPropertyDescription2 *This,
        VARTYPE *pvartype);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IPropertyDescription2 *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetEditInvitation)(
        IPropertyDescription2 *This,
        LPWSTR *ppszInvite);

    HRESULT (STDMETHODCALLTYPE *GetTypeFlags)(
        IPropertyDescription2 *This,
        PROPDESC_TYPE_FLAGS mask,
        PROPDESC_TYPE_FLAGS *ppdtFlags);

    HRESULT (STDMETHODCALLTYPE *GetViewFlags)(
        IPropertyDescription2 *This,
        PROPDESC_VIEW_FLAGS *ppdvFlags);

    HRESULT (STDMETHODCALLTYPE *GetDefaultColumnWidth)(
        IPropertyDescription2 *This,
        UINT *pcxChars);

    HRESULT (STDMETHODCALLTYPE *GetDisplayType)(
        IPropertyDescription2 *This,
        PROPDESC_DISPLAYTYPE *pdisplaytype);

    HRESULT (STDMETHODCALLTYPE *GetColumnState)(
        IPropertyDescription2 *This,
        SHCOLSTATEF *pcsFlags);

    HRESULT (STDMETHODCALLTYPE *GetGroupingRange)(
        IPropertyDescription2 *This,
        PROPDESC_GROUPING_RANGE *pgr);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescriptionType)(
        IPropertyDescription2 *This,
        PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescription)(
        IPropertyDescription2 *This,
        REFPROPVARIANT propvar1,
        REFPROPVARIANT propvar2,
        LPWSTR *ppszDesc1,
        LPWSTR *ppszDesc2);

    HRESULT (STDMETHODCALLTYPE *GetSortDescription)(
        IPropertyDescription2 *This,
        PROPDESC_SORTDESCRIPTION *psd);

    HRESULT (STDMETHODCALLTYPE *GetSortDescriptionLabel)(
        IPropertyDescription2 *This,
        WINBOOL fDescending,
        LPWSTR *ppszDescription);

    HRESULT (STDMETHODCALLTYPE *GetAggregationType)(
        IPropertyDescription2 *This,
        PROPDESC_AGGREGATION_TYPE *paggtype);

    HRESULT (STDMETHODCALLTYPE *GetConditionType)(
        IPropertyDescription2 *This,
        PROPDESC_CONDITION_TYPE *pcontype,
        CONDITION_OPERATION *popDefault);

    HRESULT (STDMETHODCALLTYPE *GetEnumTypeList)(
        IPropertyDescription2 *This,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *CoerceToCanonicalValue)(
        IPropertyDescription2 *This,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *FormatForDisplay)(
        IPropertyDescription2 *This,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdfFlags,
        LPWSTR *ppszDisplay);

    HRESULT (STDMETHODCALLTYPE *IsValueCanonical)(
        IPropertyDescription2 *This,
        REFPROPVARIANT propvar);

    /*** IPropertyDescription2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetImageReferenceForValue)(
        IPropertyDescription2 *This,
        REFPROPVARIANT propvar,
        LPWSTR *ppszImageRes);

    END_INTERFACE
} IPropertyDescription2Vtbl;

interface IPropertyDescription2 {
    CONST_VTBL IPropertyDescription2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyDescription2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyDescription2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyDescription2_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyDescription methods ***/
#define IPropertyDescription2_GetPropertyKey(This,pkey) (This)->lpVtbl->GetPropertyKey(This,pkey)
#define IPropertyDescription2_GetCanonicalName(This,ppszName) (This)->lpVtbl->GetCanonicalName(This,ppszName)
#define IPropertyDescription2_GetPropertyType(This,pvartype) (This)->lpVtbl->GetPropertyType(This,pvartype)
#define IPropertyDescription2_GetDisplayName(This,ppszName) (This)->lpVtbl->GetDisplayName(This,ppszName)
#define IPropertyDescription2_GetEditInvitation(This,ppszInvite) (This)->lpVtbl->GetEditInvitation(This,ppszInvite)
#define IPropertyDescription2_GetTypeFlags(This,mask,ppdtFlags) (This)->lpVtbl->GetTypeFlags(This,mask,ppdtFlags)
#define IPropertyDescription2_GetViewFlags(This,ppdvFlags) (This)->lpVtbl->GetViewFlags(This,ppdvFlags)
#define IPropertyDescription2_GetDefaultColumnWidth(This,pcxChars) (This)->lpVtbl->GetDefaultColumnWidth(This,pcxChars)
#define IPropertyDescription2_GetDisplayType(This,pdisplaytype) (This)->lpVtbl->GetDisplayType(This,pdisplaytype)
#define IPropertyDescription2_GetColumnState(This,pcsFlags) (This)->lpVtbl->GetColumnState(This,pcsFlags)
#define IPropertyDescription2_GetGroupingRange(This,pgr) (This)->lpVtbl->GetGroupingRange(This,pgr)
#define IPropertyDescription2_GetRelativeDescriptionType(This,prdt) (This)->lpVtbl->GetRelativeDescriptionType(This,prdt)
#define IPropertyDescription2_GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2) (This)->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2)
#define IPropertyDescription2_GetSortDescription(This,psd) (This)->lpVtbl->GetSortDescription(This,psd)
#define IPropertyDescription2_GetSortDescriptionLabel(This,fDescending,ppszDescription) (This)->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription)
#define IPropertyDescription2_GetAggregationType(This,paggtype) (This)->lpVtbl->GetAggregationType(This,paggtype)
#define IPropertyDescription2_GetConditionType(This,pcontype,popDefault) (This)->lpVtbl->GetConditionType(This,pcontype,popDefault)
#define IPropertyDescription2_GetEnumTypeList(This,riid,ppv) (This)->lpVtbl->GetEnumTypeList(This,riid,ppv)
#define IPropertyDescription2_CoerceToCanonicalValue(This,ppropvar) (This)->lpVtbl->CoerceToCanonicalValue(This,ppropvar)
#define IPropertyDescription2_FormatForDisplay(This,propvar,pdfFlags,ppszDisplay) (This)->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay)
#define IPropertyDescription2_IsValueCanonical(This,propvar) (This)->lpVtbl->IsValueCanonical(This,propvar)
/*** IPropertyDescription2 methods ***/
#define IPropertyDescription2_GetImageReferenceForValue(This,propvar,ppszImageRes) (This)->lpVtbl->GetImageReferenceForValue(This,propvar,ppszImageRes)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyDescription2_QueryInterface(IPropertyDescription2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyDescription2_AddRef(IPropertyDescription2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyDescription2_Release(IPropertyDescription2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyDescription methods ***/
static inline HRESULT IPropertyDescription2_GetPropertyKey(IPropertyDescription2* This,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetPropertyKey(This,pkey);
}
static inline HRESULT IPropertyDescription2_GetCanonicalName(IPropertyDescription2* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetCanonicalName(This,ppszName);
}
static inline HRESULT IPropertyDescription2_GetPropertyType(IPropertyDescription2* This,VARTYPE *pvartype) {
    return This->lpVtbl->GetPropertyType(This,pvartype);
}
static inline HRESULT IPropertyDescription2_GetDisplayName(IPropertyDescription2* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetDisplayName(This,ppszName);
}
static inline HRESULT IPropertyDescription2_GetEditInvitation(IPropertyDescription2* This,LPWSTR *ppszInvite) {
    return This->lpVtbl->GetEditInvitation(This,ppszInvite);
}
static inline HRESULT IPropertyDescription2_GetTypeFlags(IPropertyDescription2* This,PROPDESC_TYPE_FLAGS mask,PROPDESC_TYPE_FLAGS *ppdtFlags) {
    return This->lpVtbl->GetTypeFlags(This,mask,ppdtFlags);
}
static inline HRESULT IPropertyDescription2_GetViewFlags(IPropertyDescription2* This,PROPDESC_VIEW_FLAGS *ppdvFlags) {
    return This->lpVtbl->GetViewFlags(This,ppdvFlags);
}
static inline HRESULT IPropertyDescription2_GetDefaultColumnWidth(IPropertyDescription2* This,UINT *pcxChars) {
    return This->lpVtbl->GetDefaultColumnWidth(This,pcxChars);
}
static inline HRESULT IPropertyDescription2_GetDisplayType(IPropertyDescription2* This,PROPDESC_DISPLAYTYPE *pdisplaytype) {
    return This->lpVtbl->GetDisplayType(This,pdisplaytype);
}
static inline HRESULT IPropertyDescription2_GetColumnState(IPropertyDescription2* This,SHCOLSTATEF *pcsFlags) {
    return This->lpVtbl->GetColumnState(This,pcsFlags);
}
static inline HRESULT IPropertyDescription2_GetGroupingRange(IPropertyDescription2* This,PROPDESC_GROUPING_RANGE *pgr) {
    return This->lpVtbl->GetGroupingRange(This,pgr);
}
static inline HRESULT IPropertyDescription2_GetRelativeDescriptionType(IPropertyDescription2* This,PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt) {
    return This->lpVtbl->GetRelativeDescriptionType(This,prdt);
}
static inline HRESULT IPropertyDescription2_GetRelativeDescription(IPropertyDescription2* This,REFPROPVARIANT propvar1,REFPROPVARIANT propvar2,LPWSTR *ppszDesc1,LPWSTR *ppszDesc2) {
    return This->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2);
}
static inline HRESULT IPropertyDescription2_GetSortDescription(IPropertyDescription2* This,PROPDESC_SORTDESCRIPTION *psd) {
    return This->lpVtbl->GetSortDescription(This,psd);
}
static inline HRESULT IPropertyDescription2_GetSortDescriptionLabel(IPropertyDescription2* This,WINBOOL fDescending,LPWSTR *ppszDescription) {
    return This->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription);
}
static inline HRESULT IPropertyDescription2_GetAggregationType(IPropertyDescription2* This,PROPDESC_AGGREGATION_TYPE *paggtype) {
    return This->lpVtbl->GetAggregationType(This,paggtype);
}
static inline HRESULT IPropertyDescription2_GetConditionType(IPropertyDescription2* This,PROPDESC_CONDITION_TYPE *pcontype,CONDITION_OPERATION *popDefault) {
    return This->lpVtbl->GetConditionType(This,pcontype,popDefault);
}
static inline HRESULT IPropertyDescription2_GetEnumTypeList(IPropertyDescription2* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetEnumTypeList(This,riid,ppv);
}
static inline HRESULT IPropertyDescription2_CoerceToCanonicalValue(IPropertyDescription2* This,PROPVARIANT *ppropvar) {
    return This->lpVtbl->CoerceToCanonicalValue(This,ppropvar);
}
static inline HRESULT IPropertyDescription2_FormatForDisplay(IPropertyDescription2* This,REFPROPVARIANT propvar,PROPDESC_FORMAT_FLAGS pdfFlags,LPWSTR *ppszDisplay) {
    return This->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay);
}
static inline HRESULT IPropertyDescription2_IsValueCanonical(IPropertyDescription2* This,REFPROPVARIANT propvar) {
    return This->lpVtbl->IsValueCanonical(This,propvar);
}
/*** IPropertyDescription2 methods ***/
static inline HRESULT IPropertyDescription2_GetImageReferenceForValue(IPropertyDescription2* This,REFPROPVARIANT propvar,LPWSTR *ppszImageRes) {
    return This->lpVtbl->GetImageReferenceForValue(This,propvar,ppszImageRes);
}
#endif
#endif

#endif


#endif  /* __IPropertyDescription2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyDescriptionAliasInfo interface
 */
#ifndef __IPropertyDescriptionAliasInfo_INTERFACE_DEFINED__
#define __IPropertyDescriptionAliasInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyDescriptionAliasInfo, 0xf67104fc, 0x2af9, 0x46fd, 0xb3,0x2d, 0x24,0x3c,0x14,0x04,0xf3,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f67104fc-2af9-46fd-b32d-243c1404f3d1")
IPropertyDescriptionAliasInfo : public IPropertyDescription
{
    virtual HRESULT STDMETHODCALLTYPE GetSortByAlias(
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdditionalSortByAliases(
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyDescriptionAliasInfo, 0xf67104fc, 0x2af9, 0x46fd, 0xb3,0x2d, 0x24,0x3c,0x14,0x04,0xf3,0xd1)
#endif
#else
typedef struct IPropertyDescriptionAliasInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyDescriptionAliasInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyDescriptionAliasInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyDescriptionAliasInfo *This);

    /*** IPropertyDescription methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyKey)(
        IPropertyDescriptionAliasInfo *This,
        PROPERTYKEY *pkey);

    HRESULT (STDMETHODCALLTYPE *GetCanonicalName)(
        IPropertyDescriptionAliasInfo *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetPropertyType)(
        IPropertyDescriptionAliasInfo *This,
        VARTYPE *pvartype);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IPropertyDescriptionAliasInfo *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetEditInvitation)(
        IPropertyDescriptionAliasInfo *This,
        LPWSTR *ppszInvite);

    HRESULT (STDMETHODCALLTYPE *GetTypeFlags)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_TYPE_FLAGS mask,
        PROPDESC_TYPE_FLAGS *ppdtFlags);

    HRESULT (STDMETHODCALLTYPE *GetViewFlags)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_VIEW_FLAGS *ppdvFlags);

    HRESULT (STDMETHODCALLTYPE *GetDefaultColumnWidth)(
        IPropertyDescriptionAliasInfo *This,
        UINT *pcxChars);

    HRESULT (STDMETHODCALLTYPE *GetDisplayType)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_DISPLAYTYPE *pdisplaytype);

    HRESULT (STDMETHODCALLTYPE *GetColumnState)(
        IPropertyDescriptionAliasInfo *This,
        SHCOLSTATEF *pcsFlags);

    HRESULT (STDMETHODCALLTYPE *GetGroupingRange)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_GROUPING_RANGE *pgr);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescriptionType)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescription)(
        IPropertyDescriptionAliasInfo *This,
        REFPROPVARIANT propvar1,
        REFPROPVARIANT propvar2,
        LPWSTR *ppszDesc1,
        LPWSTR *ppszDesc2);

    HRESULT (STDMETHODCALLTYPE *GetSortDescription)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_SORTDESCRIPTION *psd);

    HRESULT (STDMETHODCALLTYPE *GetSortDescriptionLabel)(
        IPropertyDescriptionAliasInfo *This,
        WINBOOL fDescending,
        LPWSTR *ppszDescription);

    HRESULT (STDMETHODCALLTYPE *GetAggregationType)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_AGGREGATION_TYPE *paggtype);

    HRESULT (STDMETHODCALLTYPE *GetConditionType)(
        IPropertyDescriptionAliasInfo *This,
        PROPDESC_CONDITION_TYPE *pcontype,
        CONDITION_OPERATION *popDefault);

    HRESULT (STDMETHODCALLTYPE *GetEnumTypeList)(
        IPropertyDescriptionAliasInfo *This,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *CoerceToCanonicalValue)(
        IPropertyDescriptionAliasInfo *This,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *FormatForDisplay)(
        IPropertyDescriptionAliasInfo *This,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdfFlags,
        LPWSTR *ppszDisplay);

    HRESULT (STDMETHODCALLTYPE *IsValueCanonical)(
        IPropertyDescriptionAliasInfo *This,
        REFPROPVARIANT propvar);

    /*** IPropertyDescriptionAliasInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSortByAlias)(
        IPropertyDescriptionAliasInfo *This,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *GetAdditionalSortByAliases)(
        IPropertyDescriptionAliasInfo *This,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IPropertyDescriptionAliasInfoVtbl;

interface IPropertyDescriptionAliasInfo {
    CONST_VTBL IPropertyDescriptionAliasInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyDescriptionAliasInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyDescriptionAliasInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyDescriptionAliasInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyDescription methods ***/
#define IPropertyDescriptionAliasInfo_GetPropertyKey(This,pkey) (This)->lpVtbl->GetPropertyKey(This,pkey)
#define IPropertyDescriptionAliasInfo_GetCanonicalName(This,ppszName) (This)->lpVtbl->GetCanonicalName(This,ppszName)
#define IPropertyDescriptionAliasInfo_GetPropertyType(This,pvartype) (This)->lpVtbl->GetPropertyType(This,pvartype)
#define IPropertyDescriptionAliasInfo_GetDisplayName(This,ppszName) (This)->lpVtbl->GetDisplayName(This,ppszName)
#define IPropertyDescriptionAliasInfo_GetEditInvitation(This,ppszInvite) (This)->lpVtbl->GetEditInvitation(This,ppszInvite)
#define IPropertyDescriptionAliasInfo_GetTypeFlags(This,mask,ppdtFlags) (This)->lpVtbl->GetTypeFlags(This,mask,ppdtFlags)
#define IPropertyDescriptionAliasInfo_GetViewFlags(This,ppdvFlags) (This)->lpVtbl->GetViewFlags(This,ppdvFlags)
#define IPropertyDescriptionAliasInfo_GetDefaultColumnWidth(This,pcxChars) (This)->lpVtbl->GetDefaultColumnWidth(This,pcxChars)
#define IPropertyDescriptionAliasInfo_GetDisplayType(This,pdisplaytype) (This)->lpVtbl->GetDisplayType(This,pdisplaytype)
#define IPropertyDescriptionAliasInfo_GetColumnState(This,pcsFlags) (This)->lpVtbl->GetColumnState(This,pcsFlags)
#define IPropertyDescriptionAliasInfo_GetGroupingRange(This,pgr) (This)->lpVtbl->GetGroupingRange(This,pgr)
#define IPropertyDescriptionAliasInfo_GetRelativeDescriptionType(This,prdt) (This)->lpVtbl->GetRelativeDescriptionType(This,prdt)
#define IPropertyDescriptionAliasInfo_GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2) (This)->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2)
#define IPropertyDescriptionAliasInfo_GetSortDescription(This,psd) (This)->lpVtbl->GetSortDescription(This,psd)
#define IPropertyDescriptionAliasInfo_GetSortDescriptionLabel(This,fDescending,ppszDescription) (This)->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription)
#define IPropertyDescriptionAliasInfo_GetAggregationType(This,paggtype) (This)->lpVtbl->GetAggregationType(This,paggtype)
#define IPropertyDescriptionAliasInfo_GetConditionType(This,pcontype,popDefault) (This)->lpVtbl->GetConditionType(This,pcontype,popDefault)
#define IPropertyDescriptionAliasInfo_GetEnumTypeList(This,riid,ppv) (This)->lpVtbl->GetEnumTypeList(This,riid,ppv)
#define IPropertyDescriptionAliasInfo_CoerceToCanonicalValue(This,ppropvar) (This)->lpVtbl->CoerceToCanonicalValue(This,ppropvar)
#define IPropertyDescriptionAliasInfo_FormatForDisplay(This,propvar,pdfFlags,ppszDisplay) (This)->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay)
#define IPropertyDescriptionAliasInfo_IsValueCanonical(This,propvar) (This)->lpVtbl->IsValueCanonical(This,propvar)
/*** IPropertyDescriptionAliasInfo methods ***/
#define IPropertyDescriptionAliasInfo_GetSortByAlias(This,riid,ppv) (This)->lpVtbl->GetSortByAlias(This,riid,ppv)
#define IPropertyDescriptionAliasInfo_GetAdditionalSortByAliases(This,riid,ppv) (This)->lpVtbl->GetAdditionalSortByAliases(This,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyDescriptionAliasInfo_QueryInterface(IPropertyDescriptionAliasInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyDescriptionAliasInfo_AddRef(IPropertyDescriptionAliasInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyDescriptionAliasInfo_Release(IPropertyDescriptionAliasInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyDescription methods ***/
static inline HRESULT IPropertyDescriptionAliasInfo_GetPropertyKey(IPropertyDescriptionAliasInfo* This,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetPropertyKey(This,pkey);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetCanonicalName(IPropertyDescriptionAliasInfo* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetCanonicalName(This,ppszName);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetPropertyType(IPropertyDescriptionAliasInfo* This,VARTYPE *pvartype) {
    return This->lpVtbl->GetPropertyType(This,pvartype);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetDisplayName(IPropertyDescriptionAliasInfo* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetDisplayName(This,ppszName);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetEditInvitation(IPropertyDescriptionAliasInfo* This,LPWSTR *ppszInvite) {
    return This->lpVtbl->GetEditInvitation(This,ppszInvite);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetTypeFlags(IPropertyDescriptionAliasInfo* This,PROPDESC_TYPE_FLAGS mask,PROPDESC_TYPE_FLAGS *ppdtFlags) {
    return This->lpVtbl->GetTypeFlags(This,mask,ppdtFlags);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetViewFlags(IPropertyDescriptionAliasInfo* This,PROPDESC_VIEW_FLAGS *ppdvFlags) {
    return This->lpVtbl->GetViewFlags(This,ppdvFlags);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetDefaultColumnWidth(IPropertyDescriptionAliasInfo* This,UINT *pcxChars) {
    return This->lpVtbl->GetDefaultColumnWidth(This,pcxChars);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetDisplayType(IPropertyDescriptionAliasInfo* This,PROPDESC_DISPLAYTYPE *pdisplaytype) {
    return This->lpVtbl->GetDisplayType(This,pdisplaytype);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetColumnState(IPropertyDescriptionAliasInfo* This,SHCOLSTATEF *pcsFlags) {
    return This->lpVtbl->GetColumnState(This,pcsFlags);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetGroupingRange(IPropertyDescriptionAliasInfo* This,PROPDESC_GROUPING_RANGE *pgr) {
    return This->lpVtbl->GetGroupingRange(This,pgr);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetRelativeDescriptionType(IPropertyDescriptionAliasInfo* This,PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt) {
    return This->lpVtbl->GetRelativeDescriptionType(This,prdt);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetRelativeDescription(IPropertyDescriptionAliasInfo* This,REFPROPVARIANT propvar1,REFPROPVARIANT propvar2,LPWSTR *ppszDesc1,LPWSTR *ppszDesc2) {
    return This->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetSortDescription(IPropertyDescriptionAliasInfo* This,PROPDESC_SORTDESCRIPTION *psd) {
    return This->lpVtbl->GetSortDescription(This,psd);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetSortDescriptionLabel(IPropertyDescriptionAliasInfo* This,WINBOOL fDescending,LPWSTR *ppszDescription) {
    return This->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetAggregationType(IPropertyDescriptionAliasInfo* This,PROPDESC_AGGREGATION_TYPE *paggtype) {
    return This->lpVtbl->GetAggregationType(This,paggtype);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetConditionType(IPropertyDescriptionAliasInfo* This,PROPDESC_CONDITION_TYPE *pcontype,CONDITION_OPERATION *popDefault) {
    return This->lpVtbl->GetConditionType(This,pcontype,popDefault);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetEnumTypeList(IPropertyDescriptionAliasInfo* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetEnumTypeList(This,riid,ppv);
}
static inline HRESULT IPropertyDescriptionAliasInfo_CoerceToCanonicalValue(IPropertyDescriptionAliasInfo* This,PROPVARIANT *ppropvar) {
    return This->lpVtbl->CoerceToCanonicalValue(This,ppropvar);
}
static inline HRESULT IPropertyDescriptionAliasInfo_FormatForDisplay(IPropertyDescriptionAliasInfo* This,REFPROPVARIANT propvar,PROPDESC_FORMAT_FLAGS pdfFlags,LPWSTR *ppszDisplay) {
    return This->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay);
}
static inline HRESULT IPropertyDescriptionAliasInfo_IsValueCanonical(IPropertyDescriptionAliasInfo* This,REFPROPVARIANT propvar) {
    return This->lpVtbl->IsValueCanonical(This,propvar);
}
/*** IPropertyDescriptionAliasInfo methods ***/
static inline HRESULT IPropertyDescriptionAliasInfo_GetSortByAlias(IPropertyDescriptionAliasInfo* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetSortByAlias(This,riid,ppv);
}
static inline HRESULT IPropertyDescriptionAliasInfo_GetAdditionalSortByAliases(IPropertyDescriptionAliasInfo* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetAdditionalSortByAliases(This,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IPropertyDescriptionAliasInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyDescriptionSearchInfo interface
 */
#ifndef __IPropertyDescriptionSearchInfo_INTERFACE_DEFINED__
#define __IPropertyDescriptionSearchInfo_INTERFACE_DEFINED__

typedef enum PROPDESC_SEARCHINFO_FLAGS {
    PDSIF_DEFAULT = 0x0,
    PDSIF_ININVERTEDINDEX = 0x1,
    PDSIF_ISCOLUMN = 0x2,
    PDSIF_ISCOLUMNSPARSE = 0x4,
    PDSIF_ALWAYSINCLUDE = 0x8,
    PDSIF_USEFORTYPEAHEAD = 0x10
} PROPDESC_SEARCHINFO_FLAGS;

DEFINE_ENUM_FLAG_OPERATORS(PROPDESC_SEARCHINFO_FLAGS)

typedef enum PROPDESC_COLUMNINDEX_TYPE {
    PDCIT_NONE = 0,
    PDCIT_ONDISK = 1,
    PDCIT_INMEMORY = 2,
    PDCIT_ONDEMAND = 3,
    PDCIT_ONDISKALL = 4,
    PDCIT_ONDISKVECTOR = 5
} PROPDESC_COLUMNINDEX_TYPE;

DEFINE_GUID(IID_IPropertyDescriptionSearchInfo, 0x078f91bd, 0x29a2, 0x440f, 0x92,0x4e, 0x46,0xa2,0x91,0x52,0x45,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("078f91bd-29a2-440f-924e-46a291524520")
IPropertyDescriptionSearchInfo : public IPropertyDescription
{
    virtual HRESULT STDMETHODCALLTYPE GetSearchInfoFlags(
        PROPDESC_SEARCHINFO_FLAGS *ppdsiFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColumnIndexType(
        PROPDESC_COLUMNINDEX_TYPE *ppdciType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProjectionString(
        LPWSTR *ppszProjection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxSize(
        UINT *pcbMaxSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyDescriptionSearchInfo, 0x078f91bd, 0x29a2, 0x440f, 0x92,0x4e, 0x46,0xa2,0x91,0x52,0x45,0x20)
#endif
#else
typedef struct IPropertyDescriptionSearchInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyDescriptionSearchInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyDescriptionSearchInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyDescriptionSearchInfo *This);

    /*** IPropertyDescription methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyKey)(
        IPropertyDescriptionSearchInfo *This,
        PROPERTYKEY *pkey);

    HRESULT (STDMETHODCALLTYPE *GetCanonicalName)(
        IPropertyDescriptionSearchInfo *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetPropertyType)(
        IPropertyDescriptionSearchInfo *This,
        VARTYPE *pvartype);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IPropertyDescriptionSearchInfo *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetEditInvitation)(
        IPropertyDescriptionSearchInfo *This,
        LPWSTR *ppszInvite);

    HRESULT (STDMETHODCALLTYPE *GetTypeFlags)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_TYPE_FLAGS mask,
        PROPDESC_TYPE_FLAGS *ppdtFlags);

    HRESULT (STDMETHODCALLTYPE *GetViewFlags)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_VIEW_FLAGS *ppdvFlags);

    HRESULT (STDMETHODCALLTYPE *GetDefaultColumnWidth)(
        IPropertyDescriptionSearchInfo *This,
        UINT *pcxChars);

    HRESULT (STDMETHODCALLTYPE *GetDisplayType)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_DISPLAYTYPE *pdisplaytype);

    HRESULT (STDMETHODCALLTYPE *GetColumnState)(
        IPropertyDescriptionSearchInfo *This,
        SHCOLSTATEF *pcsFlags);

    HRESULT (STDMETHODCALLTYPE *GetGroupingRange)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_GROUPING_RANGE *pgr);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescriptionType)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescription)(
        IPropertyDescriptionSearchInfo *This,
        REFPROPVARIANT propvar1,
        REFPROPVARIANT propvar2,
        LPWSTR *ppszDesc1,
        LPWSTR *ppszDesc2);

    HRESULT (STDMETHODCALLTYPE *GetSortDescription)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_SORTDESCRIPTION *psd);

    HRESULT (STDMETHODCALLTYPE *GetSortDescriptionLabel)(
        IPropertyDescriptionSearchInfo *This,
        WINBOOL fDescending,
        LPWSTR *ppszDescription);

    HRESULT (STDMETHODCALLTYPE *GetAggregationType)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_AGGREGATION_TYPE *paggtype);

    HRESULT (STDMETHODCALLTYPE *GetConditionType)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_CONDITION_TYPE *pcontype,
        CONDITION_OPERATION *popDefault);

    HRESULT (STDMETHODCALLTYPE *GetEnumTypeList)(
        IPropertyDescriptionSearchInfo *This,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *CoerceToCanonicalValue)(
        IPropertyDescriptionSearchInfo *This,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *FormatForDisplay)(
        IPropertyDescriptionSearchInfo *This,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdfFlags,
        LPWSTR *ppszDisplay);

    HRESULT (STDMETHODCALLTYPE *IsValueCanonical)(
        IPropertyDescriptionSearchInfo *This,
        REFPROPVARIANT propvar);

    /*** IPropertyDescriptionSearchInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSearchInfoFlags)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_SEARCHINFO_FLAGS *ppdsiFlags);

    HRESULT (STDMETHODCALLTYPE *GetColumnIndexType)(
        IPropertyDescriptionSearchInfo *This,
        PROPDESC_COLUMNINDEX_TYPE *ppdciType);

    HRESULT (STDMETHODCALLTYPE *GetProjectionString)(
        IPropertyDescriptionSearchInfo *This,
        LPWSTR *ppszProjection);

    HRESULT (STDMETHODCALLTYPE *GetMaxSize)(
        IPropertyDescriptionSearchInfo *This,
        UINT *pcbMaxSize);

    END_INTERFACE
} IPropertyDescriptionSearchInfoVtbl;

interface IPropertyDescriptionSearchInfo {
    CONST_VTBL IPropertyDescriptionSearchInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyDescriptionSearchInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyDescriptionSearchInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyDescriptionSearchInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyDescription methods ***/
#define IPropertyDescriptionSearchInfo_GetPropertyKey(This,pkey) (This)->lpVtbl->GetPropertyKey(This,pkey)
#define IPropertyDescriptionSearchInfo_GetCanonicalName(This,ppszName) (This)->lpVtbl->GetCanonicalName(This,ppszName)
#define IPropertyDescriptionSearchInfo_GetPropertyType(This,pvartype) (This)->lpVtbl->GetPropertyType(This,pvartype)
#define IPropertyDescriptionSearchInfo_GetDisplayName(This,ppszName) (This)->lpVtbl->GetDisplayName(This,ppszName)
#define IPropertyDescriptionSearchInfo_GetEditInvitation(This,ppszInvite) (This)->lpVtbl->GetEditInvitation(This,ppszInvite)
#define IPropertyDescriptionSearchInfo_GetTypeFlags(This,mask,ppdtFlags) (This)->lpVtbl->GetTypeFlags(This,mask,ppdtFlags)
#define IPropertyDescriptionSearchInfo_GetViewFlags(This,ppdvFlags) (This)->lpVtbl->GetViewFlags(This,ppdvFlags)
#define IPropertyDescriptionSearchInfo_GetDefaultColumnWidth(This,pcxChars) (This)->lpVtbl->GetDefaultColumnWidth(This,pcxChars)
#define IPropertyDescriptionSearchInfo_GetDisplayType(This,pdisplaytype) (This)->lpVtbl->GetDisplayType(This,pdisplaytype)
#define IPropertyDescriptionSearchInfo_GetColumnState(This,pcsFlags) (This)->lpVtbl->GetColumnState(This,pcsFlags)
#define IPropertyDescriptionSearchInfo_GetGroupingRange(This,pgr) (This)->lpVtbl->GetGroupingRange(This,pgr)
#define IPropertyDescriptionSearchInfo_GetRelativeDescriptionType(This,prdt) (This)->lpVtbl->GetRelativeDescriptionType(This,prdt)
#define IPropertyDescriptionSearchInfo_GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2) (This)->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2)
#define IPropertyDescriptionSearchInfo_GetSortDescription(This,psd) (This)->lpVtbl->GetSortDescription(This,psd)
#define IPropertyDescriptionSearchInfo_GetSortDescriptionLabel(This,fDescending,ppszDescription) (This)->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription)
#define IPropertyDescriptionSearchInfo_GetAggregationType(This,paggtype) (This)->lpVtbl->GetAggregationType(This,paggtype)
#define IPropertyDescriptionSearchInfo_GetConditionType(This,pcontype,popDefault) (This)->lpVtbl->GetConditionType(This,pcontype,popDefault)
#define IPropertyDescriptionSearchInfo_GetEnumTypeList(This,riid,ppv) (This)->lpVtbl->GetEnumTypeList(This,riid,ppv)
#define IPropertyDescriptionSearchInfo_CoerceToCanonicalValue(This,ppropvar) (This)->lpVtbl->CoerceToCanonicalValue(This,ppropvar)
#define IPropertyDescriptionSearchInfo_FormatForDisplay(This,propvar,pdfFlags,ppszDisplay) (This)->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay)
#define IPropertyDescriptionSearchInfo_IsValueCanonical(This,propvar) (This)->lpVtbl->IsValueCanonical(This,propvar)
/*** IPropertyDescriptionSearchInfo methods ***/
#define IPropertyDescriptionSearchInfo_GetSearchInfoFlags(This,ppdsiFlags) (This)->lpVtbl->GetSearchInfoFlags(This,ppdsiFlags)
#define IPropertyDescriptionSearchInfo_GetColumnIndexType(This,ppdciType) (This)->lpVtbl->GetColumnIndexType(This,ppdciType)
#define IPropertyDescriptionSearchInfo_GetProjectionString(This,ppszProjection) (This)->lpVtbl->GetProjectionString(This,ppszProjection)
#define IPropertyDescriptionSearchInfo_GetMaxSize(This,pcbMaxSize) (This)->lpVtbl->GetMaxSize(This,pcbMaxSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyDescriptionSearchInfo_QueryInterface(IPropertyDescriptionSearchInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyDescriptionSearchInfo_AddRef(IPropertyDescriptionSearchInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyDescriptionSearchInfo_Release(IPropertyDescriptionSearchInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyDescription methods ***/
static inline HRESULT IPropertyDescriptionSearchInfo_GetPropertyKey(IPropertyDescriptionSearchInfo* This,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetPropertyKey(This,pkey);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetCanonicalName(IPropertyDescriptionSearchInfo* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetCanonicalName(This,ppszName);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetPropertyType(IPropertyDescriptionSearchInfo* This,VARTYPE *pvartype) {
    return This->lpVtbl->GetPropertyType(This,pvartype);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetDisplayName(IPropertyDescriptionSearchInfo* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetDisplayName(This,ppszName);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetEditInvitation(IPropertyDescriptionSearchInfo* This,LPWSTR *ppszInvite) {
    return This->lpVtbl->GetEditInvitation(This,ppszInvite);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetTypeFlags(IPropertyDescriptionSearchInfo* This,PROPDESC_TYPE_FLAGS mask,PROPDESC_TYPE_FLAGS *ppdtFlags) {
    return This->lpVtbl->GetTypeFlags(This,mask,ppdtFlags);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetViewFlags(IPropertyDescriptionSearchInfo* This,PROPDESC_VIEW_FLAGS *ppdvFlags) {
    return This->lpVtbl->GetViewFlags(This,ppdvFlags);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetDefaultColumnWidth(IPropertyDescriptionSearchInfo* This,UINT *pcxChars) {
    return This->lpVtbl->GetDefaultColumnWidth(This,pcxChars);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetDisplayType(IPropertyDescriptionSearchInfo* This,PROPDESC_DISPLAYTYPE *pdisplaytype) {
    return This->lpVtbl->GetDisplayType(This,pdisplaytype);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetColumnState(IPropertyDescriptionSearchInfo* This,SHCOLSTATEF *pcsFlags) {
    return This->lpVtbl->GetColumnState(This,pcsFlags);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetGroupingRange(IPropertyDescriptionSearchInfo* This,PROPDESC_GROUPING_RANGE *pgr) {
    return This->lpVtbl->GetGroupingRange(This,pgr);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetRelativeDescriptionType(IPropertyDescriptionSearchInfo* This,PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt) {
    return This->lpVtbl->GetRelativeDescriptionType(This,prdt);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetRelativeDescription(IPropertyDescriptionSearchInfo* This,REFPROPVARIANT propvar1,REFPROPVARIANT propvar2,LPWSTR *ppszDesc1,LPWSTR *ppszDesc2) {
    return This->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetSortDescription(IPropertyDescriptionSearchInfo* This,PROPDESC_SORTDESCRIPTION *psd) {
    return This->lpVtbl->GetSortDescription(This,psd);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetSortDescriptionLabel(IPropertyDescriptionSearchInfo* This,WINBOOL fDescending,LPWSTR *ppszDescription) {
    return This->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetAggregationType(IPropertyDescriptionSearchInfo* This,PROPDESC_AGGREGATION_TYPE *paggtype) {
    return This->lpVtbl->GetAggregationType(This,paggtype);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetConditionType(IPropertyDescriptionSearchInfo* This,PROPDESC_CONDITION_TYPE *pcontype,CONDITION_OPERATION *popDefault) {
    return This->lpVtbl->GetConditionType(This,pcontype,popDefault);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetEnumTypeList(IPropertyDescriptionSearchInfo* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetEnumTypeList(This,riid,ppv);
}
static inline HRESULT IPropertyDescriptionSearchInfo_CoerceToCanonicalValue(IPropertyDescriptionSearchInfo* This,PROPVARIANT *ppropvar) {
    return This->lpVtbl->CoerceToCanonicalValue(This,ppropvar);
}
static inline HRESULT IPropertyDescriptionSearchInfo_FormatForDisplay(IPropertyDescriptionSearchInfo* This,REFPROPVARIANT propvar,PROPDESC_FORMAT_FLAGS pdfFlags,LPWSTR *ppszDisplay) {
    return This->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay);
}
static inline HRESULT IPropertyDescriptionSearchInfo_IsValueCanonical(IPropertyDescriptionSearchInfo* This,REFPROPVARIANT propvar) {
    return This->lpVtbl->IsValueCanonical(This,propvar);
}
/*** IPropertyDescriptionSearchInfo methods ***/
static inline HRESULT IPropertyDescriptionSearchInfo_GetSearchInfoFlags(IPropertyDescriptionSearchInfo* This,PROPDESC_SEARCHINFO_FLAGS *ppdsiFlags) {
    return This->lpVtbl->GetSearchInfoFlags(This,ppdsiFlags);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetColumnIndexType(IPropertyDescriptionSearchInfo* This,PROPDESC_COLUMNINDEX_TYPE *ppdciType) {
    return This->lpVtbl->GetColumnIndexType(This,ppdciType);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetProjectionString(IPropertyDescriptionSearchInfo* This,LPWSTR *ppszProjection) {
    return This->lpVtbl->GetProjectionString(This,ppszProjection);
}
static inline HRESULT IPropertyDescriptionSearchInfo_GetMaxSize(IPropertyDescriptionSearchInfo* This,UINT *pcbMaxSize) {
    return This->lpVtbl->GetMaxSize(This,pcbMaxSize);
}
#endif
#endif

#endif


#endif  /* __IPropertyDescriptionSearchInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyDescriptionRelatedPropertyInfo interface
 */
#ifndef __IPropertyDescriptionRelatedPropertyInfo_INTERFACE_DEFINED__
#define __IPropertyDescriptionRelatedPropertyInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyDescriptionRelatedPropertyInfo, 0x507393f4, 0x2a3d, 0x4a60, 0xb5,0x9e, 0xd9,0xc7,0x57,0x16,0xc2,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("507393f4-2a3d-4a60-b59e-d9c75716c2dd")
IPropertyDescriptionRelatedPropertyInfo : public IPropertyDescription
{
    virtual HRESULT STDMETHODCALLTYPE GetRelatedProperty(
        LPCWSTR pszRelationshipName,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyDescriptionRelatedPropertyInfo, 0x507393f4, 0x2a3d, 0x4a60, 0xb5,0x9e, 0xd9,0xc7,0x57,0x16,0xc2,0xdd)
#endif
#else
typedef struct IPropertyDescriptionRelatedPropertyInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyDescriptionRelatedPropertyInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyDescriptionRelatedPropertyInfo *This);

    /*** IPropertyDescription methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyKey)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPERTYKEY *pkey);

    HRESULT (STDMETHODCALLTYPE *GetCanonicalName)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetPropertyType)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        VARTYPE *pvartype);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *GetEditInvitation)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        LPWSTR *ppszInvite);

    HRESULT (STDMETHODCALLTYPE *GetTypeFlags)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_TYPE_FLAGS mask,
        PROPDESC_TYPE_FLAGS *ppdtFlags);

    HRESULT (STDMETHODCALLTYPE *GetViewFlags)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_VIEW_FLAGS *ppdvFlags);

    HRESULT (STDMETHODCALLTYPE *GetDefaultColumnWidth)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        UINT *pcxChars);

    HRESULT (STDMETHODCALLTYPE *GetDisplayType)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_DISPLAYTYPE *pdisplaytype);

    HRESULT (STDMETHODCALLTYPE *GetColumnState)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        SHCOLSTATEF *pcsFlags);

    HRESULT (STDMETHODCALLTYPE *GetGroupingRange)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_GROUPING_RANGE *pgr);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescriptionType)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt);

    HRESULT (STDMETHODCALLTYPE *GetRelativeDescription)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        REFPROPVARIANT propvar1,
        REFPROPVARIANT propvar2,
        LPWSTR *ppszDesc1,
        LPWSTR *ppszDesc2);

    HRESULT (STDMETHODCALLTYPE *GetSortDescription)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_SORTDESCRIPTION *psd);

    HRESULT (STDMETHODCALLTYPE *GetSortDescriptionLabel)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        WINBOOL fDescending,
        LPWSTR *ppszDescription);

    HRESULT (STDMETHODCALLTYPE *GetAggregationType)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_AGGREGATION_TYPE *paggtype);

    HRESULT (STDMETHODCALLTYPE *GetConditionType)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPDESC_CONDITION_TYPE *pcontype,
        CONDITION_OPERATION *popDefault);

    HRESULT (STDMETHODCALLTYPE *GetEnumTypeList)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *CoerceToCanonicalValue)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        PROPVARIANT *ppropvar);

    HRESULT (STDMETHODCALLTYPE *FormatForDisplay)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdfFlags,
        LPWSTR *ppszDisplay);

    HRESULT (STDMETHODCALLTYPE *IsValueCanonical)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        REFPROPVARIANT propvar);

    /*** IPropertyDescriptionRelatedPropertyInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRelatedProperty)(
        IPropertyDescriptionRelatedPropertyInfo *This,
        LPCWSTR pszRelationshipName,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IPropertyDescriptionRelatedPropertyInfoVtbl;

interface IPropertyDescriptionRelatedPropertyInfo {
    CONST_VTBL IPropertyDescriptionRelatedPropertyInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyDescriptionRelatedPropertyInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyDescriptionRelatedPropertyInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyDescriptionRelatedPropertyInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyDescription methods ***/
#define IPropertyDescriptionRelatedPropertyInfo_GetPropertyKey(This,pkey) (This)->lpVtbl->GetPropertyKey(This,pkey)
#define IPropertyDescriptionRelatedPropertyInfo_GetCanonicalName(This,ppszName) (This)->lpVtbl->GetCanonicalName(This,ppszName)
#define IPropertyDescriptionRelatedPropertyInfo_GetPropertyType(This,pvartype) (This)->lpVtbl->GetPropertyType(This,pvartype)
#define IPropertyDescriptionRelatedPropertyInfo_GetDisplayName(This,ppszName) (This)->lpVtbl->GetDisplayName(This,ppszName)
#define IPropertyDescriptionRelatedPropertyInfo_GetEditInvitation(This,ppszInvite) (This)->lpVtbl->GetEditInvitation(This,ppszInvite)
#define IPropertyDescriptionRelatedPropertyInfo_GetTypeFlags(This,mask,ppdtFlags) (This)->lpVtbl->GetTypeFlags(This,mask,ppdtFlags)
#define IPropertyDescriptionRelatedPropertyInfo_GetViewFlags(This,ppdvFlags) (This)->lpVtbl->GetViewFlags(This,ppdvFlags)
#define IPropertyDescriptionRelatedPropertyInfo_GetDefaultColumnWidth(This,pcxChars) (This)->lpVtbl->GetDefaultColumnWidth(This,pcxChars)
#define IPropertyDescriptionRelatedPropertyInfo_GetDisplayType(This,pdisplaytype) (This)->lpVtbl->GetDisplayType(This,pdisplaytype)
#define IPropertyDescriptionRelatedPropertyInfo_GetColumnState(This,pcsFlags) (This)->lpVtbl->GetColumnState(This,pcsFlags)
#define IPropertyDescriptionRelatedPropertyInfo_GetGroupingRange(This,pgr) (This)->lpVtbl->GetGroupingRange(This,pgr)
#define IPropertyDescriptionRelatedPropertyInfo_GetRelativeDescriptionType(This,prdt) (This)->lpVtbl->GetRelativeDescriptionType(This,prdt)
#define IPropertyDescriptionRelatedPropertyInfo_GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2) (This)->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2)
#define IPropertyDescriptionRelatedPropertyInfo_GetSortDescription(This,psd) (This)->lpVtbl->GetSortDescription(This,psd)
#define IPropertyDescriptionRelatedPropertyInfo_GetSortDescriptionLabel(This,fDescending,ppszDescription) (This)->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription)
#define IPropertyDescriptionRelatedPropertyInfo_GetAggregationType(This,paggtype) (This)->lpVtbl->GetAggregationType(This,paggtype)
#define IPropertyDescriptionRelatedPropertyInfo_GetConditionType(This,pcontype,popDefault) (This)->lpVtbl->GetConditionType(This,pcontype,popDefault)
#define IPropertyDescriptionRelatedPropertyInfo_GetEnumTypeList(This,riid,ppv) (This)->lpVtbl->GetEnumTypeList(This,riid,ppv)
#define IPropertyDescriptionRelatedPropertyInfo_CoerceToCanonicalValue(This,ppropvar) (This)->lpVtbl->CoerceToCanonicalValue(This,ppropvar)
#define IPropertyDescriptionRelatedPropertyInfo_FormatForDisplay(This,propvar,pdfFlags,ppszDisplay) (This)->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay)
#define IPropertyDescriptionRelatedPropertyInfo_IsValueCanonical(This,propvar) (This)->lpVtbl->IsValueCanonical(This,propvar)
/*** IPropertyDescriptionRelatedPropertyInfo methods ***/
#define IPropertyDescriptionRelatedPropertyInfo_GetRelatedProperty(This,pszRelationshipName,riid,ppv) (This)->lpVtbl->GetRelatedProperty(This,pszRelationshipName,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_QueryInterface(IPropertyDescriptionRelatedPropertyInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyDescriptionRelatedPropertyInfo_AddRef(IPropertyDescriptionRelatedPropertyInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyDescriptionRelatedPropertyInfo_Release(IPropertyDescriptionRelatedPropertyInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyDescription methods ***/
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetPropertyKey(IPropertyDescriptionRelatedPropertyInfo* This,PROPERTYKEY *pkey) {
    return This->lpVtbl->GetPropertyKey(This,pkey);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetCanonicalName(IPropertyDescriptionRelatedPropertyInfo* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetCanonicalName(This,ppszName);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetPropertyType(IPropertyDescriptionRelatedPropertyInfo* This,VARTYPE *pvartype) {
    return This->lpVtbl->GetPropertyType(This,pvartype);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetDisplayName(IPropertyDescriptionRelatedPropertyInfo* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetDisplayName(This,ppszName);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetEditInvitation(IPropertyDescriptionRelatedPropertyInfo* This,LPWSTR *ppszInvite) {
    return This->lpVtbl->GetEditInvitation(This,ppszInvite);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetTypeFlags(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_TYPE_FLAGS mask,PROPDESC_TYPE_FLAGS *ppdtFlags) {
    return This->lpVtbl->GetTypeFlags(This,mask,ppdtFlags);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetViewFlags(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_VIEW_FLAGS *ppdvFlags) {
    return This->lpVtbl->GetViewFlags(This,ppdvFlags);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetDefaultColumnWidth(IPropertyDescriptionRelatedPropertyInfo* This,UINT *pcxChars) {
    return This->lpVtbl->GetDefaultColumnWidth(This,pcxChars);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetDisplayType(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_DISPLAYTYPE *pdisplaytype) {
    return This->lpVtbl->GetDisplayType(This,pdisplaytype);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetColumnState(IPropertyDescriptionRelatedPropertyInfo* This,SHCOLSTATEF *pcsFlags) {
    return This->lpVtbl->GetColumnState(This,pcsFlags);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetGroupingRange(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_GROUPING_RANGE *pgr) {
    return This->lpVtbl->GetGroupingRange(This,pgr);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetRelativeDescriptionType(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_RELATIVEDESCRIPTION_TYPE *prdt) {
    return This->lpVtbl->GetRelativeDescriptionType(This,prdt);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetRelativeDescription(IPropertyDescriptionRelatedPropertyInfo* This,REFPROPVARIANT propvar1,REFPROPVARIANT propvar2,LPWSTR *ppszDesc1,LPWSTR *ppszDesc2) {
    return This->lpVtbl->GetRelativeDescription(This,propvar1,propvar2,ppszDesc1,ppszDesc2);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetSortDescription(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_SORTDESCRIPTION *psd) {
    return This->lpVtbl->GetSortDescription(This,psd);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetSortDescriptionLabel(IPropertyDescriptionRelatedPropertyInfo* This,WINBOOL fDescending,LPWSTR *ppszDescription) {
    return This->lpVtbl->GetSortDescriptionLabel(This,fDescending,ppszDescription);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetAggregationType(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_AGGREGATION_TYPE *paggtype) {
    return This->lpVtbl->GetAggregationType(This,paggtype);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetConditionType(IPropertyDescriptionRelatedPropertyInfo* This,PROPDESC_CONDITION_TYPE *pcontype,CONDITION_OPERATION *popDefault) {
    return This->lpVtbl->GetConditionType(This,pcontype,popDefault);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetEnumTypeList(IPropertyDescriptionRelatedPropertyInfo* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetEnumTypeList(This,riid,ppv);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_CoerceToCanonicalValue(IPropertyDescriptionRelatedPropertyInfo* This,PROPVARIANT *ppropvar) {
    return This->lpVtbl->CoerceToCanonicalValue(This,ppropvar);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_FormatForDisplay(IPropertyDescriptionRelatedPropertyInfo* This,REFPROPVARIANT propvar,PROPDESC_FORMAT_FLAGS pdfFlags,LPWSTR *ppszDisplay) {
    return This->lpVtbl->FormatForDisplay(This,propvar,pdfFlags,ppszDisplay);
}
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_IsValueCanonical(IPropertyDescriptionRelatedPropertyInfo* This,REFPROPVARIANT propvar) {
    return This->lpVtbl->IsValueCanonical(This,propvar);
}
/*** IPropertyDescriptionRelatedPropertyInfo methods ***/
static inline HRESULT IPropertyDescriptionRelatedPropertyInfo_GetRelatedProperty(IPropertyDescriptionRelatedPropertyInfo* This,LPCWSTR pszRelationshipName,REFIID riid,void **ppv) {
    return This->lpVtbl->GetRelatedProperty(This,pszRelationshipName,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IPropertyDescriptionRelatedPropertyInfo_INTERFACE_DEFINED__ */


typedef enum PROPDESC_ENUMFILTER {
    PDEF_ALL = 0,
    PDEF_SYSTEM = 1,
    PDEF_NONSYSTEM = 2,
    PDEF_VIEWABLE = 3,
    PDEF_QUERYABLE = 4,
    PDEF_INFULLTEXTQUERY = 5,
    PDEF_COLUMN = 6
} PROPDESC_ENUMFILTER;

/*****************************************************************************
 * IPropertySystem interface
 */
#ifndef __IPropertySystem_INTERFACE_DEFINED__
#define __IPropertySystem_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertySystem, 0xca724e8a, 0xc3e6, 0x442b, 0x88,0xa4, 0x6f,0xb0,0xdb,0x80,0x35,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ca724e8a-c3e6-442b-88a4-6fb0db8035a3")
IPropertySystem : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyDescription(
        REFPROPERTYKEY propkey,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyDescriptionByName(
        LPCWSTR pszCanonicalName,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyDescriptionListFromString(
        LPCWSTR pszPropList,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumeratePropertyDescriptions(
        PROPDESC_ENUMFILTER filterOn,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE FormatForDisplay(
        REFPROPERTYKEY key,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdff,
        LPWSTR pszText,
        DWORD cchText) = 0;

    virtual HRESULT STDMETHODCALLTYPE FormatForDisplayAlloc(
        REFPROPERTYKEY key,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdff,
        LPWSTR *ppszDisplay) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterPropertySchema(
        LPCWSTR pszPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterPropertySchema(
        LPCWSTR pszPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshPropertySchema(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertySystem, 0xca724e8a, 0xc3e6, 0x442b, 0x88,0xa4, 0x6f,0xb0,0xdb,0x80,0x35,0xa3)
#endif
#else
typedef struct IPropertySystemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertySystem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertySystem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertySystem *This);

    /*** IPropertySystem methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyDescription)(
        IPropertySystem *This,
        REFPROPERTYKEY propkey,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *GetPropertyDescriptionByName)(
        IPropertySystem *This,
        LPCWSTR pszCanonicalName,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *GetPropertyDescriptionListFromString)(
        IPropertySystem *This,
        LPCWSTR pszPropList,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *EnumeratePropertyDescriptions)(
        IPropertySystem *This,
        PROPDESC_ENUMFILTER filterOn,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *FormatForDisplay)(
        IPropertySystem *This,
        REFPROPERTYKEY key,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdff,
        LPWSTR pszText,
        DWORD cchText);

    HRESULT (STDMETHODCALLTYPE *FormatForDisplayAlloc)(
        IPropertySystem *This,
        REFPROPERTYKEY key,
        REFPROPVARIANT propvar,
        PROPDESC_FORMAT_FLAGS pdff,
        LPWSTR *ppszDisplay);

    HRESULT (STDMETHODCALLTYPE *RegisterPropertySchema)(
        IPropertySystem *This,
        LPCWSTR pszPath);

    HRESULT (STDMETHODCALLTYPE *UnregisterPropertySchema)(
        IPropertySystem *This,
        LPCWSTR pszPath);

    HRESULT (STDMETHODCALLTYPE *RefreshPropertySchema)(
        IPropertySystem *This);

    END_INTERFACE
} IPropertySystemVtbl;

interface IPropertySystem {
    CONST_VTBL IPropertySystemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertySystem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertySystem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertySystem_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertySystem methods ***/
#define IPropertySystem_GetPropertyDescription(This,propkey,riid,ppv) (This)->lpVtbl->GetPropertyDescription(This,propkey,riid,ppv)
#define IPropertySystem_GetPropertyDescriptionByName(This,pszCanonicalName,riid,ppv) (This)->lpVtbl->GetPropertyDescriptionByName(This,pszCanonicalName,riid,ppv)
#define IPropertySystem_GetPropertyDescriptionListFromString(This,pszPropList,riid,ppv) (This)->lpVtbl->GetPropertyDescriptionListFromString(This,pszPropList,riid,ppv)
#define IPropertySystem_EnumeratePropertyDescriptions(This,filterOn,riid,ppv) (This)->lpVtbl->EnumeratePropertyDescriptions(This,filterOn,riid,ppv)
#define IPropertySystem_FormatForDisplay(This,key,propvar,pdff,pszText,cchText) (This)->lpVtbl->FormatForDisplay(This,key,propvar,pdff,pszText,cchText)
#define IPropertySystem_FormatForDisplayAlloc(This,key,propvar,pdff,ppszDisplay) (This)->lpVtbl->FormatForDisplayAlloc(This,key,propvar,pdff,ppszDisplay)
#define IPropertySystem_RegisterPropertySchema(This,pszPath) (This)->lpVtbl->RegisterPropertySchema(This,pszPath)
#define IPropertySystem_UnregisterPropertySchema(This,pszPath) (This)->lpVtbl->UnregisterPropertySchema(This,pszPath)
#define IPropertySystem_RefreshPropertySchema(This) (This)->lpVtbl->RefreshPropertySchema(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertySystem_QueryInterface(IPropertySystem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertySystem_AddRef(IPropertySystem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertySystem_Release(IPropertySystem* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertySystem methods ***/
static inline HRESULT IPropertySystem_GetPropertyDescription(IPropertySystem* This,REFPROPERTYKEY propkey,REFIID riid,void **ppv) {
    return This->lpVtbl->GetPropertyDescription(This,propkey,riid,ppv);
}
static inline HRESULT IPropertySystem_GetPropertyDescriptionByName(IPropertySystem* This,LPCWSTR pszCanonicalName,REFIID riid,void **ppv) {
    return This->lpVtbl->GetPropertyDescriptionByName(This,pszCanonicalName,riid,ppv);
}
static inline HRESULT IPropertySystem_GetPropertyDescriptionListFromString(IPropertySystem* This,LPCWSTR pszPropList,REFIID riid,void **ppv) {
    return This->lpVtbl->GetPropertyDescriptionListFromString(This,pszPropList,riid,ppv);
}
static inline HRESULT IPropertySystem_EnumeratePropertyDescriptions(IPropertySystem* This,PROPDESC_ENUMFILTER filterOn,REFIID riid,void **ppv) {
    return This->lpVtbl->EnumeratePropertyDescriptions(This,filterOn,riid,ppv);
}
static inline HRESULT IPropertySystem_FormatForDisplay(IPropertySystem* This,REFPROPERTYKEY key,REFPROPVARIANT propvar,PROPDESC_FORMAT_FLAGS pdff,LPWSTR pszText,DWORD cchText) {
    return This->lpVtbl->FormatForDisplay(This,key,propvar,pdff,pszText,cchText);
}
static inline HRESULT IPropertySystem_FormatForDisplayAlloc(IPropertySystem* This,REFPROPERTYKEY key,REFPROPVARIANT propvar,PROPDESC_FORMAT_FLAGS pdff,LPWSTR *ppszDisplay) {
    return This->lpVtbl->FormatForDisplayAlloc(This,key,propvar,pdff,ppszDisplay);
}
static inline HRESULT IPropertySystem_RegisterPropertySchema(IPropertySystem* This,LPCWSTR pszPath) {
    return This->lpVtbl->RegisterPropertySchema(This,pszPath);
}
static inline HRESULT IPropertySystem_UnregisterPropertySchema(IPropertySystem* This,LPCWSTR pszPath) {
    return This->lpVtbl->UnregisterPropertySchema(This,pszPath);
}
static inline HRESULT IPropertySystem_RefreshPropertySchema(IPropertySystem* This) {
    return This->lpVtbl->RefreshPropertySchema(This);
}
#endif
#endif

#endif


#endif  /* __IPropertySystem_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyDescriptionList interface
 */
#ifndef __IPropertyDescriptionList_INTERFACE_DEFINED__
#define __IPropertyDescriptionList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyDescriptionList, 0x1f9fc1d0, 0xc39b, 0x4b26, 0x81,0x7f, 0x01,0x19,0x67,0xd3,0x44,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1f9fc1d0-c39b-4b26-817f-011967d3440e")
IPropertyDescriptionList : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT *pcElem) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT iElem,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyDescriptionList, 0x1f9fc1d0, 0xc39b, 0x4b26, 0x81,0x7f, 0x01,0x19,0x67,0xd3,0x44,0x0e)
#endif
#else
typedef struct IPropertyDescriptionListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyDescriptionList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyDescriptionList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyDescriptionList *This);

    /*** IPropertyDescriptionList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPropertyDescriptionList *This,
        UINT *pcElem);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPropertyDescriptionList *This,
        UINT iElem,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IPropertyDescriptionListVtbl;

interface IPropertyDescriptionList {
    CONST_VTBL IPropertyDescriptionListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyDescriptionList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyDescriptionList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyDescriptionList_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyDescriptionList methods ***/
#define IPropertyDescriptionList_GetCount(This,pcElem) (This)->lpVtbl->GetCount(This,pcElem)
#define IPropertyDescriptionList_GetAt(This,iElem,riid,ppv) (This)->lpVtbl->GetAt(This,iElem,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyDescriptionList_QueryInterface(IPropertyDescriptionList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyDescriptionList_AddRef(IPropertyDescriptionList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyDescriptionList_Release(IPropertyDescriptionList* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyDescriptionList methods ***/
static inline HRESULT IPropertyDescriptionList_GetCount(IPropertyDescriptionList* This,UINT *pcElem) {
    return This->lpVtbl->GetCount(This,pcElem);
}
static inline HRESULT IPropertyDescriptionList_GetAt(IPropertyDescriptionList* This,UINT iElem,REFIID riid,void **ppv) {
    return This->lpVtbl->GetAt(This,iElem,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IPropertyDescriptionList_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyStoreFactory interface
 */
#ifndef __IPropertyStoreFactory_INTERFACE_DEFINED__
#define __IPropertyStoreFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertyStoreFactory, 0xbc110b6d, 0x57e8, 0x4148, 0xa9,0xc6, 0x91,0x01,0x5a,0xb2,0xf3,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bc110b6d-57e8-4148-a9c6-91015ab2f3a5")
IPropertyStoreFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyStore(
        GETPROPERTYSTOREFLAGS flags,
        IUnknown *pUnkFactory,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyStoreForKeys(
        const PROPERTYKEY *rgKeys,
        UINT cKeys,
        GETPROPERTYSTOREFLAGS flags,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyStoreFactory, 0xbc110b6d, 0x57e8, 0x4148, 0xa9,0xc6, 0x91,0x01,0x5a,0xb2,0xf3,0xa5)
#endif
#else
typedef struct IPropertyStoreFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyStoreFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyStoreFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyStoreFactory *This);

    /*** IPropertyStoreFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyStore)(
        IPropertyStoreFactory *This,
        GETPROPERTYSTOREFLAGS flags,
        IUnknown *pUnkFactory,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *GetPropertyStoreForKeys)(
        IPropertyStoreFactory *This,
        const PROPERTYKEY *rgKeys,
        UINT cKeys,
        GETPROPERTYSTOREFLAGS flags,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IPropertyStoreFactoryVtbl;

interface IPropertyStoreFactory {
    CONST_VTBL IPropertyStoreFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyStoreFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyStoreFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyStoreFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyStoreFactory methods ***/
#define IPropertyStoreFactory_GetPropertyStore(This,flags,pUnkFactory,riid,ppv) (This)->lpVtbl->GetPropertyStore(This,flags,pUnkFactory,riid,ppv)
#define IPropertyStoreFactory_GetPropertyStoreForKeys(This,rgKeys,cKeys,flags,riid,ppv) (This)->lpVtbl->GetPropertyStoreForKeys(This,rgKeys,cKeys,flags,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyStoreFactory_QueryInterface(IPropertyStoreFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyStoreFactory_AddRef(IPropertyStoreFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyStoreFactory_Release(IPropertyStoreFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyStoreFactory methods ***/
static inline HRESULT IPropertyStoreFactory_GetPropertyStore(IPropertyStoreFactory* This,GETPROPERTYSTOREFLAGS flags,IUnknown *pUnkFactory,REFIID riid,void **ppv) {
    return This->lpVtbl->GetPropertyStore(This,flags,pUnkFactory,riid,ppv);
}
static inline HRESULT IPropertyStoreFactory_GetPropertyStoreForKeys(IPropertyStoreFactory* This,const PROPERTYKEY *rgKeys,UINT cKeys,GETPROPERTYSTOREFLAGS flags,REFIID riid,void **ppv) {
    return This->lpVtbl->GetPropertyStoreForKeys(This,rgKeys,cKeys,flags,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IPropertyStoreFactory_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDelayedPropertyStoreFactory interface
 */
#ifndef __IDelayedPropertyStoreFactory_INTERFACE_DEFINED__
#define __IDelayedPropertyStoreFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDelayedPropertyStoreFactory, 0x40d4577f, 0xe237, 0x4bdb, 0xbd,0x69, 0x58,0xf0,0x89,0x43,0x1b,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("40d4577f-e237-4bdb-bd69-58f089431b6a")
IDelayedPropertyStoreFactory : public IPropertyStoreFactory
{
    virtual HRESULT STDMETHODCALLTYPE GetDelayedPropertyStore(
        GETPROPERTYSTOREFLAGS flags,
        DWORD dwStoreId,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDelayedPropertyStoreFactory, 0x40d4577f, 0xe237, 0x4bdb, 0xbd,0x69, 0x58,0xf0,0x89,0x43,0x1b,0x6a)
#endif
#else
typedef struct IDelayedPropertyStoreFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDelayedPropertyStoreFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDelayedPropertyStoreFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDelayedPropertyStoreFactory *This);

    /*** IPropertyStoreFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyStore)(
        IDelayedPropertyStoreFactory *This,
        GETPROPERTYSTOREFLAGS flags,
        IUnknown *pUnkFactory,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *GetPropertyStoreForKeys)(
        IDelayedPropertyStoreFactory *This,
        const PROPERTYKEY *rgKeys,
        UINT cKeys,
        GETPROPERTYSTOREFLAGS flags,
        REFIID riid,
        void **ppv);

    /*** IDelayedPropertyStoreFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDelayedPropertyStore)(
        IDelayedPropertyStoreFactory *This,
        GETPROPERTYSTOREFLAGS flags,
        DWORD dwStoreId,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IDelayedPropertyStoreFactoryVtbl;

interface IDelayedPropertyStoreFactory {
    CONST_VTBL IDelayedPropertyStoreFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDelayedPropertyStoreFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDelayedPropertyStoreFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDelayedPropertyStoreFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyStoreFactory methods ***/
#define IDelayedPropertyStoreFactory_GetPropertyStore(This,flags,pUnkFactory,riid,ppv) (This)->lpVtbl->GetPropertyStore(This,flags,pUnkFactory,riid,ppv)
#define IDelayedPropertyStoreFactory_GetPropertyStoreForKeys(This,rgKeys,cKeys,flags,riid,ppv) (This)->lpVtbl->GetPropertyStoreForKeys(This,rgKeys,cKeys,flags,riid,ppv)
/*** IDelayedPropertyStoreFactory methods ***/
#define IDelayedPropertyStoreFactory_GetDelayedPropertyStore(This,flags,dwStoreId,riid,ppv) (This)->lpVtbl->GetDelayedPropertyStore(This,flags,dwStoreId,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IDelayedPropertyStoreFactory_QueryInterface(IDelayedPropertyStoreFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDelayedPropertyStoreFactory_AddRef(IDelayedPropertyStoreFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDelayedPropertyStoreFactory_Release(IDelayedPropertyStoreFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyStoreFactory methods ***/
static inline HRESULT IDelayedPropertyStoreFactory_GetPropertyStore(IDelayedPropertyStoreFactory* This,GETPROPERTYSTOREFLAGS flags,IUnknown *pUnkFactory,REFIID riid,void **ppv) {
    return This->lpVtbl->GetPropertyStore(This,flags,pUnkFactory,riid,ppv);
}
static inline HRESULT IDelayedPropertyStoreFactory_GetPropertyStoreForKeys(IDelayedPropertyStoreFactory* This,const PROPERTYKEY *rgKeys,UINT cKeys,GETPROPERTYSTOREFLAGS flags,REFIID riid,void **ppv) {
    return This->lpVtbl->GetPropertyStoreForKeys(This,rgKeys,cKeys,flags,riid,ppv);
}
/*** IDelayedPropertyStoreFactory methods ***/
static inline HRESULT IDelayedPropertyStoreFactory_GetDelayedPropertyStore(IDelayedPropertyStoreFactory* This,GETPROPERTYSTOREFLAGS flags,DWORD dwStoreId,REFIID riid,void **ppv) {
    return This->lpVtbl->GetDelayedPropertyStore(This,flags,dwStoreId,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IDelayedPropertyStoreFactory_INTERFACE_DEFINED__ */


enum _PERSIST_SPROPSTORE_FLAGS {
    FPSPS_DEFAULT = 0x0,
    FPSPS_READONLY = 0x1,
    FPSPS_TREAT_NEW_VALUES_AS_DIRTY = 0x2
};

typedef int PERSIST_SPROPSTORE_FLAGS;
typedef struct tagSERIALIZEDPROPSTORAGE SERIALIZEDPROPSTORAGE;
typedef SERIALIZEDPROPSTORAGE *PUSERIALIZEDPROPSTORAGE;
typedef const SERIALIZEDPROPSTORAGE *PCUSERIALIZEDPROPSTORAGE;

/*****************************************************************************
 * IPersistSerializedPropStorage interface
 */
#ifndef __IPersistSerializedPropStorage_INTERFACE_DEFINED__
#define __IPersistSerializedPropStorage_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPersistSerializedPropStorage, 0xe318ad57, 0x0aa0, 0x450f, 0xac,0xa5, 0x6f,0xab,0x71,0x03,0xd9,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e318ad57-0aa0-450f-aca5-6fab7103d917")
IPersistSerializedPropStorage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetFlags(
        PERSIST_SPROPSTORE_FLAGS flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPropertyStorage(
        PCUSERIALIZEDPROPSTORAGE psps,
        DWORD cb) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyStorage(
        SERIALIZEDPROPSTORAGE **ppsps,
        DWORD *pcb) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistSerializedPropStorage, 0xe318ad57, 0x0aa0, 0x450f, 0xac,0xa5, 0x6f,0xab,0x71,0x03,0xd9,0x17)
#endif
#else
typedef struct IPersistSerializedPropStorageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistSerializedPropStorage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistSerializedPropStorage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistSerializedPropStorage *This);

    /*** IPersistSerializedPropStorage methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFlags)(
        IPersistSerializedPropStorage *This,
        PERSIST_SPROPSTORE_FLAGS flags);

    HRESULT (STDMETHODCALLTYPE *SetPropertyStorage)(
        IPersistSerializedPropStorage *This,
        PCUSERIALIZEDPROPSTORAGE psps,
        DWORD cb);

    HRESULT (STDMETHODCALLTYPE *GetPropertyStorage)(
        IPersistSerializedPropStorage *This,
        SERIALIZEDPROPSTORAGE **ppsps,
        DWORD *pcb);

    END_INTERFACE
} IPersistSerializedPropStorageVtbl;

interface IPersistSerializedPropStorage {
    CONST_VTBL IPersistSerializedPropStorageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistSerializedPropStorage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistSerializedPropStorage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistSerializedPropStorage_Release(This) (This)->lpVtbl->Release(This)
/*** IPersistSerializedPropStorage methods ***/
#define IPersistSerializedPropStorage_SetFlags(This,flags) (This)->lpVtbl->SetFlags(This,flags)
#define IPersistSerializedPropStorage_SetPropertyStorage(This,psps,cb) (This)->lpVtbl->SetPropertyStorage(This,psps,cb)
#define IPersistSerializedPropStorage_GetPropertyStorage(This,ppsps,pcb) (This)->lpVtbl->GetPropertyStorage(This,ppsps,pcb)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistSerializedPropStorage_QueryInterface(IPersistSerializedPropStorage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistSerializedPropStorage_AddRef(IPersistSerializedPropStorage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistSerializedPropStorage_Release(IPersistSerializedPropStorage* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersistSerializedPropStorage methods ***/
static inline HRESULT IPersistSerializedPropStorage_SetFlags(IPersistSerializedPropStorage* This,PERSIST_SPROPSTORE_FLAGS flags) {
    return This->lpVtbl->SetFlags(This,flags);
}
static inline HRESULT IPersistSerializedPropStorage_SetPropertyStorage(IPersistSerializedPropStorage* This,PCUSERIALIZEDPROPSTORAGE psps,DWORD cb) {
    return This->lpVtbl->SetPropertyStorage(This,psps,cb);
}
static inline HRESULT IPersistSerializedPropStorage_GetPropertyStorage(IPersistSerializedPropStorage* This,SERIALIZEDPROPSTORAGE **ppsps,DWORD *pcb) {
    return This->lpVtbl->GetPropertyStorage(This,ppsps,pcb);
}
#endif
#endif

#endif


#endif  /* __IPersistSerializedPropStorage_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPersistSerializedPropStorage2 interface
 */
#ifndef __IPersistSerializedPropStorage2_INTERFACE_DEFINED__
#define __IPersistSerializedPropStorage2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPersistSerializedPropStorage2, 0x77effa68, 0x4f98, 0x4366, 0xba,0x72, 0x57,0x3b,0x3d,0x88,0x05,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("77effa68-4f98-4366-ba72-573b3d880571")
IPersistSerializedPropStorage2 : public IPersistSerializedPropStorage
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyStorageSize(
        DWORD *pcb) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyStorageBuffer(
        SERIALIZEDPROPSTORAGE *psps,
        DWORD cb,
        DWORD *pcbWritten) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistSerializedPropStorage2, 0x77effa68, 0x4f98, 0x4366, 0xba,0x72, 0x57,0x3b,0x3d,0x88,0x05,0x71)
#endif
#else
typedef struct IPersistSerializedPropStorage2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistSerializedPropStorage2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistSerializedPropStorage2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistSerializedPropStorage2 *This);

    /*** IPersistSerializedPropStorage methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFlags)(
        IPersistSerializedPropStorage2 *This,
        PERSIST_SPROPSTORE_FLAGS flags);

    HRESULT (STDMETHODCALLTYPE *SetPropertyStorage)(
        IPersistSerializedPropStorage2 *This,
        PCUSERIALIZEDPROPSTORAGE psps,
        DWORD cb);

    HRESULT (STDMETHODCALLTYPE *GetPropertyStorage)(
        IPersistSerializedPropStorage2 *This,
        SERIALIZEDPROPSTORAGE **ppsps,
        DWORD *pcb);

    /*** IPersistSerializedPropStorage2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyStorageSize)(
        IPersistSerializedPropStorage2 *This,
        DWORD *pcb);

    HRESULT (STDMETHODCALLTYPE *GetPropertyStorageBuffer)(
        IPersistSerializedPropStorage2 *This,
        SERIALIZEDPROPSTORAGE *psps,
        DWORD cb,
        DWORD *pcbWritten);

    END_INTERFACE
} IPersistSerializedPropStorage2Vtbl;

interface IPersistSerializedPropStorage2 {
    CONST_VTBL IPersistSerializedPropStorage2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistSerializedPropStorage2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistSerializedPropStorage2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistSerializedPropStorage2_Release(This) (This)->lpVtbl->Release(This)
/*** IPersistSerializedPropStorage methods ***/
#define IPersistSerializedPropStorage2_SetFlags(This,flags) (This)->lpVtbl->SetFlags(This,flags)
#define IPersistSerializedPropStorage2_SetPropertyStorage(This,psps,cb) (This)->lpVtbl->SetPropertyStorage(This,psps,cb)
#define IPersistSerializedPropStorage2_GetPropertyStorage(This,ppsps,pcb) (This)->lpVtbl->GetPropertyStorage(This,ppsps,pcb)
/*** IPersistSerializedPropStorage2 methods ***/
#define IPersistSerializedPropStorage2_GetPropertyStorageSize(This,pcb) (This)->lpVtbl->GetPropertyStorageSize(This,pcb)
#define IPersistSerializedPropStorage2_GetPropertyStorageBuffer(This,psps,cb,pcbWritten) (This)->lpVtbl->GetPropertyStorageBuffer(This,psps,cb,pcbWritten)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistSerializedPropStorage2_QueryInterface(IPersistSerializedPropStorage2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistSerializedPropStorage2_AddRef(IPersistSerializedPropStorage2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistSerializedPropStorage2_Release(IPersistSerializedPropStorage2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersistSerializedPropStorage methods ***/
static inline HRESULT IPersistSerializedPropStorage2_SetFlags(IPersistSerializedPropStorage2* This,PERSIST_SPROPSTORE_FLAGS flags) {
    return This->lpVtbl->SetFlags(This,flags);
}
static inline HRESULT IPersistSerializedPropStorage2_SetPropertyStorage(IPersistSerializedPropStorage2* This,PCUSERIALIZEDPROPSTORAGE psps,DWORD cb) {
    return This->lpVtbl->SetPropertyStorage(This,psps,cb);
}
static inline HRESULT IPersistSerializedPropStorage2_GetPropertyStorage(IPersistSerializedPropStorage2* This,SERIALIZEDPROPSTORAGE **ppsps,DWORD *pcb) {
    return This->lpVtbl->GetPropertyStorage(This,ppsps,pcb);
}
/*** IPersistSerializedPropStorage2 methods ***/
static inline HRESULT IPersistSerializedPropStorage2_GetPropertyStorageSize(IPersistSerializedPropStorage2* This,DWORD *pcb) {
    return This->lpVtbl->GetPropertyStorageSize(This,pcb);
}
static inline HRESULT IPersistSerializedPropStorage2_GetPropertyStorageBuffer(IPersistSerializedPropStorage2* This,SERIALIZEDPROPSTORAGE *psps,DWORD cb,DWORD *pcbWritten) {
    return This->lpVtbl->GetPropertyStorageBuffer(This,psps,cb,pcbWritten);
}
#endif
#endif

#endif


#endif  /* __IPersistSerializedPropStorage2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertySystemChangeNotify interface
 */
#ifndef __IPropertySystemChangeNotify_INTERFACE_DEFINED__
#define __IPropertySystemChangeNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertySystemChangeNotify, 0xfa955fd9, 0x38be, 0x4879, 0xa6,0xce, 0x82,0x4c,0xf5,0x2d,0x60,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa955fd9-38be-4879-a6ce-824cf52d609f")
IPropertySystemChangeNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SchemaRefreshed(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertySystemChangeNotify, 0xfa955fd9, 0x38be, 0x4879, 0xa6,0xce, 0x82,0x4c,0xf5,0x2d,0x60,0x9f)
#endif
#else
typedef struct IPropertySystemChangeNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertySystemChangeNotify *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertySystemChangeNotify *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertySystemChangeNotify *This);

    /*** IPropertySystemChangeNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *SchemaRefreshed)(
        IPropertySystemChangeNotify *This);

    END_INTERFACE
} IPropertySystemChangeNotifyVtbl;

interface IPropertySystemChangeNotify {
    CONST_VTBL IPropertySystemChangeNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertySystemChangeNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertySystemChangeNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertySystemChangeNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertySystemChangeNotify methods ***/
#define IPropertySystemChangeNotify_SchemaRefreshed(This) (This)->lpVtbl->SchemaRefreshed(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertySystemChangeNotify_QueryInterface(IPropertySystemChangeNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertySystemChangeNotify_AddRef(IPropertySystemChangeNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertySystemChangeNotify_Release(IPropertySystemChangeNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertySystemChangeNotify methods ***/
static inline HRESULT IPropertySystemChangeNotify_SchemaRefreshed(IPropertySystemChangeNotify* This) {
    return This->lpVtbl->SchemaRefreshed(This);
}
#endif
#endif

#endif


#endif  /* __IPropertySystemChangeNotify_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICreateObject interface
 */
#ifndef __ICreateObject_INTERFACE_DEFINED__
#define __ICreateObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICreateObject, 0x75121952, 0xe0d0, 0x43e5, 0x93,0x80, 0x1d,0x80,0x48,0x3a,0xcf,0x72);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("75121952-e0d0-43e5-9380-1d80483acf72")
ICreateObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateObject(
        REFCLSID clsid,
        IUnknown *pUnkOuter,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICreateObject, 0x75121952, 0xe0d0, 0x43e5, 0x93,0x80, 0x1d,0x80,0x48,0x3a,0xcf,0x72)
#endif
#else
typedef struct ICreateObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICreateObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICreateObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICreateObject *This);

    /*** ICreateObject methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateObject)(
        ICreateObject *This,
        REFCLSID clsid,
        IUnknown *pUnkOuter,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} ICreateObjectVtbl;

interface ICreateObject {
    CONST_VTBL ICreateObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICreateObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICreateObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICreateObject_Release(This) (This)->lpVtbl->Release(This)
/*** ICreateObject methods ***/
#define ICreateObject_CreateObject(This,clsid,pUnkOuter,riid,ppv) (This)->lpVtbl->CreateObject(This,clsid,pUnkOuter,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT ICreateObject_QueryInterface(ICreateObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICreateObject_AddRef(ICreateObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICreateObject_Release(ICreateObject* This) {
    return This->lpVtbl->Release(This);
}
/*** ICreateObject methods ***/
static inline HRESULT ICreateObject_CreateObject(ICreateObject* This,REFCLSID clsid,IUnknown *pUnkOuter,REFIID riid,void **ppv) {
    return This->lpVtbl->CreateObject(This,clsid,pUnkOuter,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __ICreateObject_INTERFACE_DEFINED__ */


#define PKEY_PIDSTR_MAX 10
#define GUIDSTRING_MAX (39)
#define PKEYSTR_MAX (GUIDSTRING_MAX + 1 + PKEY_PIDSTR_MAX)

PSSTDAPI PSCoerceToCanonicalValue(REFPROPERTYKEY key, PROPVARIANT *ppropvar);
PSSTDAPI PSCreateAdapterFromPropertyStore(IPropertyStore *pps, REFIID riid, void **ppv);
PSSTDAPI PSCreateDelayedMultiplexPropertyStore(GETPROPERTYSTOREFLAGS flags, IDelayedPropertyStoreFactory *pdpsf, const DWORD *rgStoreIds, DWORD cStores, REFIID riid, void **ppv);
PSSTDAPI PSCreateMemoryPropertyStore(REFIID riid, void **ppv);
PSSTDAPI PSCreateMultiplexPropertyStore(IUnknown **prgpunkStores, DWORD cStores, REFIID riid, void **ppv);
PSSTDAPI PSCreatePropertyChangeArray(const PROPERTYKEY *rgpropkey, const PKA_FLAGS *rgflags, const PROPVARIANT *rgpropvar, UINT cChanges, REFIID riid, void **ppv);
PSSTDAPI PSCreatePropertyStoreFromObject(IUnknown *punk, DWORD grfMode, REFIID riid, void **ppv);
PSSTDAPI PSCreatePropertyStoreFromPropertySetStorage(IPropertySetStorage *ppss, DWORD grfMode, REFIID riid, void **ppv);
PSSTDAPI PSCreateSimplePropertyChange(PKA_FLAGS flags, REFPROPERTYKEY key, REFPROPVARIANT propvar, REFIID riid, void **ppv);
PSSTDAPI PSEnumeratePropertyDescriptions(PROPDESC_ENUMFILTER filterOn, REFIID riid, void **ppv);
PSSTDAPI PSFormatForDisplay(REFPROPERTYKEY propkey, REFPROPVARIANT propvar, PROPDESC_FORMAT_FLAGS pdfFlags, LPWSTR pwszText, DWORD cchText);
PSSTDAPI PSFormatForDisplayAlloc(REFPROPERTYKEY key, REFPROPVARIANT propvar, PROPDESC_FORMAT_FLAGS pdff, PWSTR *ppszDisplay);
PSSTDAPI PSFormatPropertyValue(IPropertyStore *pps, IPropertyDescription *ppd, PROPDESC_FORMAT_FLAGS pdff, LPWSTR *ppszDisplay);
PSSTDAPI PSGetImageReferenceForValue(REFPROPERTYKEY propkey, REFPROPVARIANT propvar, PWSTR *ppszImageRes);
PSSTDAPI PSGetItemPropertyHandler(IUnknown *punkItem, BOOL fReadWrite, REFIID riid, void **ppv);
PSSTDAPI PSGetItemPropertyHandlerWithCreateObject(IUnknown *punkItem, BOOL fReadWrite, IUnknown *punkCreateObject, REFIID riid, void **ppv);
PSSTDAPI PSGetNamedPropertyFromPropertyStorage(PCUSERIALIZEDPROPSTORAGE psps, DWORD cb, LPCWSTR pszName, PROPVARIANT *ppropvar);
PSSTDAPI PSGetNameFromPropertyKey(REFPROPERTYKEY propkey, PWSTR *ppszCanonicalName);
PSSTDAPI PSGetPropertyDescription(REFPROPERTYKEY propkey, REFIID riid, void **ppv);
PSSTDAPI PSGetPropertyDescriptionByName(LPCWSTR pszCanonicalName, REFIID riid, void **ppv);
PSSTDAPI PSGetPropertyDescriptionListFromString(LPCWSTR pszPropList, REFIID riid, void **ppv);
PSSTDAPI PSGetPropertyFromPropertyStorage(PCUSERIALIZEDPROPSTORAGE psps, DWORD cb, REFPROPERTYKEY rpkey, PROPVARIANT *ppropvar);
PSSTDAPI PSGetPropertyKeyFromName(PCWSTR pszName, PROPERTYKEY *ppropkey);
PSSTDAPI PSGetPropertySystem(REFIID riid, void **ppv);
PSSTDAPI PSGetPropertyValue(IPropertyStore *pps, IPropertyDescription *ppd, PROPVARIANT *ppropvar);
PSSTDAPI PSLookupPropertyHandlerCLSID(PCWSTR pszFilePath, CLSID *pclsid);
PSSTDAPI PSPropertyBag_Delete(IPropertyBag *propBag, LPCWSTR propName);
PSSTDAPI PSPropertyBag_ReadBOOL(IPropertyBag *propBag, LPCWSTR propName, BOOL *value);
PSSTDAPI PSPropertyBag_ReadBSTR(IPropertyBag *propBag, LPCWSTR propName, BSTR *value);
PSSTDAPI PSPropertyBag_ReadDWORD(IPropertyBag *propBag, LPCWSTR propName, DWORD *value);
PSSTDAPI PSPropertyBag_ReadGUID(IPropertyBag *propBag, LPCWSTR propName, GUID *value);
PSSTDAPI PSPropertyBag_ReadInt(IPropertyBag *propBag, LPCWSTR propName, INT *value);
PSSTDAPI PSPropertyBag_ReadLONG(IPropertyBag *propBag, LPCWSTR propName, LONG *value);
PSSTDAPI PSPropertyBag_ReadPOINTL(IPropertyBag *propBag, LPCWSTR propName, POINTL *value);
PSSTDAPI PSPropertyBag_ReadPOINTS(IPropertyBag *propBag, LPCWSTR propName, POINTS *value);
PSSTDAPI PSPropertyBag_ReadPropertyKey(IPropertyBag *propBag, LPCWSTR propName, PROPERTYKEY *value);
PSSTDAPI PSPropertyBag_ReadRECTL(IPropertyBag *propBag, LPCWSTR propName, RECTL *value);
PSSTDAPI PSPropertyBag_ReadSHORT(IPropertyBag *propBag, LPCWSTR propName, SHORT *value);
PSSTDAPI PSPropertyBag_ReadStr(IPropertyBag *propBag, LPCWSTR propName, LPWSTR value, int characterCount);
PSSTDAPI PSPropertyBag_ReadStrAlloc(IPropertyBag *propBag, LPCWSTR propName, PWSTR *value);
PSSTDAPI PSPropertyBag_ReadStream(IPropertyBag *propBag, LPCWSTR propName, IStream **value);
PSSTDAPI PSPropertyBag_ReadType(IPropertyBag *propBag, LPCWSTR propName, VARIANT *var, VARTYPE type);
PSSTDAPI PSPropertyBag_ReadULONGLONG(IPropertyBag *propBag, LPCWSTR propName, ULONGLONG *value);
PSSTDAPI PSPropertyBag_ReadUnknown(IPropertyBag *propBag, LPCWSTR propName, REFIID riid, void **ppv);
PSSTDAPI PSPropertyBag_WriteBOOL(IPropertyBag *propBag, LPCWSTR propName, BOOL value);
PSSTDAPI PSPropertyBag_WriteBSTR(IPropertyBag *propBag, LPCWSTR propName, BSTR value);
PSSTDAPI PSPropertyBag_WriteDWORD(IPropertyBag *propBag, LPCWSTR propName, DWORD value);
PSSTDAPI PSPropertyBag_WriteGUID(IPropertyBag *propBag, LPCWSTR propName, const GUID *value);
PSSTDAPI PSPropertyBag_WriteInt(IPropertyBag *propBag, LPCWSTR propName, INT value);
PSSTDAPI PSPropertyBag_WriteLONG(IPropertyBag *propBag, LPCWSTR propName, LONG value);
PSSTDAPI PSPropertyBag_WritePOINTL(IPropertyBag *propBag, LPCWSTR propName, const POINTL *value);
PSSTDAPI PSPropertyBag_WritePOINTS(IPropertyBag *propBag, LPCWSTR propName, const POINTS *value);
PSSTDAPI PSPropertyBag_WritePropertyKey(IPropertyBag *propBag, LPCWSTR propName, REFPROPERTYKEY value);
PSSTDAPI PSPropertyBag_WriteRECTL(IPropertyBag *propBag, LPCWSTR propName, const RECTL *value);
PSSTDAPI PSPropertyBag_WriteSHORT(IPropertyBag *propBag, LPCWSTR propName, SHORT value);
PSSTDAPI PSPropertyBag_WriteStr(IPropertyBag *propBag, LPCWSTR propName, LPCWSTR value);
PSSTDAPI PSPropertyBag_WriteStream(IPropertyBag *propBag, LPCWSTR propName, IStream *value);
PSSTDAPI PSPropertyBag_WriteULONGLONG(IPropertyBag *propBag, LPCWSTR propName, ULONGLONG value);
PSSTDAPI PSPropertyBag_WriteUnknown(IPropertyBag *propBag, LPCWSTR propName, IUnknown *punk);
PSSTDAPI PSPropertyKeyFromString(LPCWSTR pszString, PROPERTYKEY *pkey);
PSSTDAPI PSRefreshPropertySchema(void);
PSSTDAPI PSRegisterPropertySchema(PCWSTR pszPath);
PSSTDAPI PSSetPropertyValue(IPropertyStore *pps, IPropertyDescription *ppd, REFPROPVARIANT propvar);
PSSTDAPI PSStringFromPropertyKey(REFPROPERTYKEY pkey, LPWSTR psz, UINT cch);
PSSTDAPI PSUnregisterPropertySchema(PCWSTR pszPath);

#ifndef __PropSysObjects_LIBRARY_DEFINED__
#define __PropSysObjects_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_PropSysObjects, 0x2cda3294, 0x6c4f, 0x4020, 0xb1,0x61, 0x27,0xc5,0x30,0xc8,0x1f,0xa6);

/*****************************************************************************
 * InMemoryPropertyStore coclass
 */

DEFINE_GUID(CLSID_InMemoryPropertyStore, 0x9a02e012, 0x6303, 0x4e1e, 0xb9,0xa1, 0x63,0x0f,0x80,0x25,0x92,0xc5);

#ifdef __cplusplus
class DECLSPEC_UUID("9a02e012-6303-4e1e-b9a1-630f802592c5") InMemoryPropertyStore;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(InMemoryPropertyStore, 0x9a02e012, 0x6303, 0x4e1e, 0xb9,0xa1, 0x63,0x0f,0x80,0x25,0x92,0xc5)
#endif
#endif

/*****************************************************************************
 * PropertySystem coclass
 */

DEFINE_GUID(CLSID_PropertySystem, 0xb8967f85, 0x58ae, 0x4f46, 0x9f,0xb2, 0x5d,0x79,0x04,0x79,0x8f,0x4b);

#ifdef __cplusplus
class DECLSPEC_UUID("b8967f85-58ae-4f46-9fb2-5d7904798f4b") PropertySystem;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PropertySystem, 0xb8967f85, 0x58ae, 0x4f46, 0x9f,0xb2, 0x5d,0x79,0x04,0x79,0x8f,0x4b)
#endif
#endif

#endif /* __PropSysObjects_LIBRARY_DEFINED__ */
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __propsys_h__ */
