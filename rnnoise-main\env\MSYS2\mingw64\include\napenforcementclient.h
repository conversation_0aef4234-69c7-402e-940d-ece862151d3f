/*** Autogenerated by WIDL 10.12 from include/napenforcementclient.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __napenforcementclient_h__
#define __napenforcementclient_h__

/* Forward declarations */

#ifndef __INapEnforcementClientBinding_FWD_DEFINED__
#define __INapEnforcementClientBinding_FWD_DEFINED__
typedef interface INapEnforcementClientBinding INapEnforcementClientBinding;
#ifdef __cplusplus
interface INapEnforcementClientBinding;
#endif /* __cplusplus */
#endif

#ifndef __INapEnforcementClientCallback_FWD_DEFINED__
#define __INapEnforcementClientCallback_FWD_DEFINED__
typedef interface INapEnforcementClientCallback INapEnforcementClientCallback;
#ifdef __cplusplus
interface INapEnforcementClientCallback;
#endif /* __cplusplus */
#endif

#ifndef __INapEnforcementClientConnection_FWD_DEFINED__
#define __INapEnforcementClientConnection_FWD_DEFINED__
typedef interface INapEnforcementClientConnection INapEnforcementClientConnection;
#ifdef __cplusplus
interface INapEnforcementClientConnection;
#endif /* __cplusplus */
#endif

#ifndef __INapEnforcementClientConnection2_FWD_DEFINED__
#define __INapEnforcementClientConnection2_FWD_DEFINED__
typedef interface INapEnforcementClientConnection2 INapEnforcementClientConnection2;
#ifdef __cplusplus
interface INapEnforcementClientConnection2;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <naptypes.h>
#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __INapEnforcementClientBinding_FWD_DEFINED__
#define __INapEnforcementClientBinding_FWD_DEFINED__
typedef interface INapEnforcementClientBinding INapEnforcementClientBinding;
#ifdef __cplusplus
interface INapEnforcementClientBinding;
#endif /* __cplusplus */
#endif

#ifndef __INapEnforcementClientCallback_FWD_DEFINED__
#define __INapEnforcementClientCallback_FWD_DEFINED__
typedef interface INapEnforcementClientCallback INapEnforcementClientCallback;
#ifdef __cplusplus
interface INapEnforcementClientCallback;
#endif /* __cplusplus */
#endif

#ifndef __INapEnforcementClientConnection_FWD_DEFINED__
#define __INapEnforcementClientConnection_FWD_DEFINED__
typedef interface INapEnforcementClientConnection INapEnforcementClientConnection;
#ifdef __cplusplus
interface INapEnforcementClientConnection;
#endif /* __cplusplus */
#endif

#ifndef __INapEnforcementClientConnection2_FWD_DEFINED__
#define __INapEnforcementClientConnection2_FWD_DEFINED__
typedef interface INapEnforcementClientConnection2 INapEnforcementClientConnection2;
#ifdef __cplusplus
interface INapEnforcementClientConnection2;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * INapEnforcementClientBinding interface
 */
#ifndef __INapEnforcementClientBinding_INTERFACE_DEFINED__
#define __INapEnforcementClientBinding_INTERFACE_DEFINED__

DEFINE_GUID(IID_INapEnforcementClientBinding, 0x92b93223, 0x7487, 0x42d9, 0x9a,0x91, 0x5b,0x85,0x07,0x72,0x03,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("92b93223-7487-42d9-9a91-5b8507720384")
INapEnforcementClientBinding : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        EnforcementEntityId id,
        INapEnforcementClientCallback *callback) = 0;

    virtual HRESULT STDMETHODCALLTYPE Uninitialize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateConnection(
        INapEnforcementClientConnection **connection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSoHRequest(
        INapEnforcementClientConnection *connection,
        WINBOOL *retriggerHint) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessSoHResponse(
        INapEnforcementClientConnection *connection) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyConnectionStateDown(
        INapEnforcementClientConnection *downCxn) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifySoHChangeFailure(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INapEnforcementClientBinding, 0x92b93223, 0x7487, 0x42d9, 0x9a,0x91, 0x5b,0x85,0x07,0x72,0x03,0x84)
#endif
#else
typedef struct INapEnforcementClientBindingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INapEnforcementClientBinding *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INapEnforcementClientBinding *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INapEnforcementClientBinding *This);

    /*** INapEnforcementClientBinding methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        INapEnforcementClientBinding *This,
        EnforcementEntityId id,
        INapEnforcementClientCallback *callback);

    HRESULT (STDMETHODCALLTYPE *Uninitialize)(
        INapEnforcementClientBinding *This);

    HRESULT (STDMETHODCALLTYPE *CreateConnection)(
        INapEnforcementClientBinding *This,
        INapEnforcementClientConnection **connection);

    HRESULT (STDMETHODCALLTYPE *GetSoHRequest)(
        INapEnforcementClientBinding *This,
        INapEnforcementClientConnection *connection,
        WINBOOL *retriggerHint);

    HRESULT (STDMETHODCALLTYPE *ProcessSoHResponse)(
        INapEnforcementClientBinding *This,
        INapEnforcementClientConnection *connection);

    HRESULT (STDMETHODCALLTYPE *NotifyConnectionStateDown)(
        INapEnforcementClientBinding *This,
        INapEnforcementClientConnection *downCxn);

    HRESULT (STDMETHODCALLTYPE *NotifySoHChangeFailure)(
        INapEnforcementClientBinding *This);

    END_INTERFACE
} INapEnforcementClientBindingVtbl;

interface INapEnforcementClientBinding {
    CONST_VTBL INapEnforcementClientBindingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INapEnforcementClientBinding_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INapEnforcementClientBinding_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INapEnforcementClientBinding_Release(This) (This)->lpVtbl->Release(This)
/*** INapEnforcementClientBinding methods ***/
#define INapEnforcementClientBinding_Initialize(This,id,callback) (This)->lpVtbl->Initialize(This,id,callback)
#define INapEnforcementClientBinding_Uninitialize(This) (This)->lpVtbl->Uninitialize(This)
#define INapEnforcementClientBinding_CreateConnection(This,connection) (This)->lpVtbl->CreateConnection(This,connection)
#define INapEnforcementClientBinding_GetSoHRequest(This,connection,retriggerHint) (This)->lpVtbl->GetSoHRequest(This,connection,retriggerHint)
#define INapEnforcementClientBinding_ProcessSoHResponse(This,connection) (This)->lpVtbl->ProcessSoHResponse(This,connection)
#define INapEnforcementClientBinding_NotifyConnectionStateDown(This,downCxn) (This)->lpVtbl->NotifyConnectionStateDown(This,downCxn)
#define INapEnforcementClientBinding_NotifySoHChangeFailure(This) (This)->lpVtbl->NotifySoHChangeFailure(This)
#else
/*** IUnknown methods ***/
static inline HRESULT INapEnforcementClientBinding_QueryInterface(INapEnforcementClientBinding* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INapEnforcementClientBinding_AddRef(INapEnforcementClientBinding* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INapEnforcementClientBinding_Release(INapEnforcementClientBinding* This) {
    return This->lpVtbl->Release(This);
}
/*** INapEnforcementClientBinding methods ***/
static inline HRESULT INapEnforcementClientBinding_Initialize(INapEnforcementClientBinding* This,EnforcementEntityId id,INapEnforcementClientCallback *callback) {
    return This->lpVtbl->Initialize(This,id,callback);
}
static inline HRESULT INapEnforcementClientBinding_Uninitialize(INapEnforcementClientBinding* This) {
    return This->lpVtbl->Uninitialize(This);
}
static inline HRESULT INapEnforcementClientBinding_CreateConnection(INapEnforcementClientBinding* This,INapEnforcementClientConnection **connection) {
    return This->lpVtbl->CreateConnection(This,connection);
}
static inline HRESULT INapEnforcementClientBinding_GetSoHRequest(INapEnforcementClientBinding* This,INapEnforcementClientConnection *connection,WINBOOL *retriggerHint) {
    return This->lpVtbl->GetSoHRequest(This,connection,retriggerHint);
}
static inline HRESULT INapEnforcementClientBinding_ProcessSoHResponse(INapEnforcementClientBinding* This,INapEnforcementClientConnection *connection) {
    return This->lpVtbl->ProcessSoHResponse(This,connection);
}
static inline HRESULT INapEnforcementClientBinding_NotifyConnectionStateDown(INapEnforcementClientBinding* This,INapEnforcementClientConnection *downCxn) {
    return This->lpVtbl->NotifyConnectionStateDown(This,downCxn);
}
static inline HRESULT INapEnforcementClientBinding_NotifySoHChangeFailure(INapEnforcementClientBinding* This) {
    return This->lpVtbl->NotifySoHChangeFailure(This);
}
#endif
#endif

#endif


#endif  /* __INapEnforcementClientBinding_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INapEnforcementClientCallback interface
 */
#ifndef __INapEnforcementClientCallback_INTERFACE_DEFINED__
#define __INapEnforcementClientCallback_INTERFACE_DEFINED__

typedef struct tagConnections {
    UINT16 count;
    INapEnforcementClientConnection **connections;
} Connections;
DEFINE_GUID(IID_INapEnforcementClientCallback, 0xf5a0b90a, 0x83a1, 0x4f76, 0xba,0x3f, 0x02,0x54,0x18,0x68,0x28,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f5a0b90a-83a1-4f76-ba3f-025418682814")
INapEnforcementClientCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NotifySoHChange(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnections(
        Connections **connections) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INapEnforcementClientCallback, 0xf5a0b90a, 0x83a1, 0x4f76, 0xba,0x3f, 0x02,0x54,0x18,0x68,0x28,0x14)
#endif
#else
typedef struct INapEnforcementClientCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INapEnforcementClientCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INapEnforcementClientCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INapEnforcementClientCallback *This);

    /*** INapEnforcementClientCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *NotifySoHChange)(
        INapEnforcementClientCallback *This);

    HRESULT (STDMETHODCALLTYPE *GetConnections)(
        INapEnforcementClientCallback *This,
        Connections **connections);

    END_INTERFACE
} INapEnforcementClientCallbackVtbl;

interface INapEnforcementClientCallback {
    CONST_VTBL INapEnforcementClientCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INapEnforcementClientCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INapEnforcementClientCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INapEnforcementClientCallback_Release(This) (This)->lpVtbl->Release(This)
/*** INapEnforcementClientCallback methods ***/
#define INapEnforcementClientCallback_NotifySoHChange(This) (This)->lpVtbl->NotifySoHChange(This)
#define INapEnforcementClientCallback_GetConnections(This,connections) (This)->lpVtbl->GetConnections(This,connections)
#else
/*** IUnknown methods ***/
static inline HRESULT INapEnforcementClientCallback_QueryInterface(INapEnforcementClientCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INapEnforcementClientCallback_AddRef(INapEnforcementClientCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INapEnforcementClientCallback_Release(INapEnforcementClientCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** INapEnforcementClientCallback methods ***/
static inline HRESULT INapEnforcementClientCallback_NotifySoHChange(INapEnforcementClientCallback* This) {
    return This->lpVtbl->NotifySoHChange(This);
}
static inline HRESULT INapEnforcementClientCallback_GetConnections(INapEnforcementClientCallback* This,Connections **connections) {
    return This->lpVtbl->GetConnections(This,connections);
}
#endif
#endif

#endif


#endif  /* __INapEnforcementClientCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INapEnforcementClientConnection interface
 */
#ifndef __INapEnforcementClientConnection_INTERFACE_DEFINED__
#define __INapEnforcementClientConnection_INTERFACE_DEFINED__

#define defaultProtocolMaxSize (4000)

#define minProtocolMaxSize (300)

#define maxProtocolMaxSize (0xffff)

typedef UINT32 ProtocolMaxSize;
DEFINE_GUID(IID_INapEnforcementClientConnection, 0xfb3a3505, 0xddb1, 0x468a, 0xb3,0x07, 0xf3,0x28,0xa5,0x74,0x19,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fb3a3505-ddb1-468a-b307-f328a57419d8")
INapEnforcementClientConnection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        EnforcementEntityId id) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMaxSize(
        ProtocolMaxSize maxSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxSize(
        ProtocolMaxSize *maxSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFlags(
        UINT8 flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFlags(
        UINT8 *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConnectionId(
        const ConnectionId *connectionId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectionId(
        ConnectionId **connectionId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCorrelationId(
        CorrelationId *correlationId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringCorrelationId(
        StringCorrelationId **correlationId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCorrelationId(
        CorrelationId correlationId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSoHRequest(
        const NetworkSoHRequest *sohRequest) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSoHRequest(
        NetworkSoHRequest **sohRequest) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSoHResponse(
        const NetworkSoHResponse *sohResponse) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSoHResponse(
        NetworkSoHResponse **sohResponse) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIsolationInfo(
        const IsolationInfo *isolationInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsolationInfo(
        IsolationInfo **isolationInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateData(
        const PrivateData *privateData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrivateData(
        PrivateData **privateData) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnforcerPrivateData(
        const PrivateData *privateData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnforcerPrivateData(
        PrivateData **privateData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INapEnforcementClientConnection, 0xfb3a3505, 0xddb1, 0x468a, 0xb3,0x07, 0xf3,0x28,0xa5,0x74,0x19,0xd8)
#endif
#else
typedef struct INapEnforcementClientConnectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INapEnforcementClientConnection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INapEnforcementClientConnection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INapEnforcementClientConnection *This);

    /*** INapEnforcementClientConnection methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        INapEnforcementClientConnection *This,
        EnforcementEntityId id);

    HRESULT (STDMETHODCALLTYPE *SetMaxSize)(
        INapEnforcementClientConnection *This,
        ProtocolMaxSize maxSize);

    HRESULT (STDMETHODCALLTYPE *GetMaxSize)(
        INapEnforcementClientConnection *This,
        ProtocolMaxSize *maxSize);

    HRESULT (STDMETHODCALLTYPE *SetFlags)(
        INapEnforcementClientConnection *This,
        UINT8 flags);

    HRESULT (STDMETHODCALLTYPE *GetFlags)(
        INapEnforcementClientConnection *This,
        UINT8 *flags);

    HRESULT (STDMETHODCALLTYPE *SetConnectionId)(
        INapEnforcementClientConnection *This,
        const ConnectionId *connectionId);

    HRESULT (STDMETHODCALLTYPE *GetConnectionId)(
        INapEnforcementClientConnection *This,
        ConnectionId **connectionId);

    HRESULT (STDMETHODCALLTYPE *GetCorrelationId)(
        INapEnforcementClientConnection *This,
        CorrelationId *correlationId);

    HRESULT (STDMETHODCALLTYPE *GetStringCorrelationId)(
        INapEnforcementClientConnection *This,
        StringCorrelationId **correlationId);

    HRESULT (STDMETHODCALLTYPE *SetCorrelationId)(
        INapEnforcementClientConnection *This,
        CorrelationId correlationId);

    HRESULT (STDMETHODCALLTYPE *SetSoHRequest)(
        INapEnforcementClientConnection *This,
        const NetworkSoHRequest *sohRequest);

    HRESULT (STDMETHODCALLTYPE *GetSoHRequest)(
        INapEnforcementClientConnection *This,
        NetworkSoHRequest **sohRequest);

    HRESULT (STDMETHODCALLTYPE *SetSoHResponse)(
        INapEnforcementClientConnection *This,
        const NetworkSoHResponse *sohResponse);

    HRESULT (STDMETHODCALLTYPE *GetSoHResponse)(
        INapEnforcementClientConnection *This,
        NetworkSoHResponse **sohResponse);

    HRESULT (STDMETHODCALLTYPE *SetIsolationInfo)(
        INapEnforcementClientConnection *This,
        const IsolationInfo *isolationInfo);

    HRESULT (STDMETHODCALLTYPE *GetIsolationInfo)(
        INapEnforcementClientConnection *This,
        IsolationInfo **isolationInfo);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        INapEnforcementClientConnection *This,
        const PrivateData *privateData);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        INapEnforcementClientConnection *This,
        PrivateData **privateData);

    HRESULT (STDMETHODCALLTYPE *SetEnforcerPrivateData)(
        INapEnforcementClientConnection *This,
        const PrivateData *privateData);

    HRESULT (STDMETHODCALLTYPE *GetEnforcerPrivateData)(
        INapEnforcementClientConnection *This,
        PrivateData **privateData);

    END_INTERFACE
} INapEnforcementClientConnectionVtbl;

interface INapEnforcementClientConnection {
    CONST_VTBL INapEnforcementClientConnectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INapEnforcementClientConnection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INapEnforcementClientConnection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INapEnforcementClientConnection_Release(This) (This)->lpVtbl->Release(This)
/*** INapEnforcementClientConnection methods ***/
#define INapEnforcementClientConnection_Initialize(This,id) (This)->lpVtbl->Initialize(This,id)
#define INapEnforcementClientConnection_SetMaxSize(This,maxSize) (This)->lpVtbl->SetMaxSize(This,maxSize)
#define INapEnforcementClientConnection_GetMaxSize(This,maxSize) (This)->lpVtbl->GetMaxSize(This,maxSize)
#define INapEnforcementClientConnection_SetFlags(This,flags) (This)->lpVtbl->SetFlags(This,flags)
#define INapEnforcementClientConnection_GetFlags(This,flags) (This)->lpVtbl->GetFlags(This,flags)
#define INapEnforcementClientConnection_SetConnectionId(This,connectionId) (This)->lpVtbl->SetConnectionId(This,connectionId)
#define INapEnforcementClientConnection_GetConnectionId(This,connectionId) (This)->lpVtbl->GetConnectionId(This,connectionId)
#define INapEnforcementClientConnection_GetCorrelationId(This,correlationId) (This)->lpVtbl->GetCorrelationId(This,correlationId)
#define INapEnforcementClientConnection_GetStringCorrelationId(This,correlationId) (This)->lpVtbl->GetStringCorrelationId(This,correlationId)
#define INapEnforcementClientConnection_SetCorrelationId(This,correlationId) (This)->lpVtbl->SetCorrelationId(This,correlationId)
#define INapEnforcementClientConnection_SetSoHRequest(This,sohRequest) (This)->lpVtbl->SetSoHRequest(This,sohRequest)
#define INapEnforcementClientConnection_GetSoHRequest(This,sohRequest) (This)->lpVtbl->GetSoHRequest(This,sohRequest)
#define INapEnforcementClientConnection_SetSoHResponse(This,sohResponse) (This)->lpVtbl->SetSoHResponse(This,sohResponse)
#define INapEnforcementClientConnection_GetSoHResponse(This,sohResponse) (This)->lpVtbl->GetSoHResponse(This,sohResponse)
#define INapEnforcementClientConnection_SetIsolationInfo(This,isolationInfo) (This)->lpVtbl->SetIsolationInfo(This,isolationInfo)
#define INapEnforcementClientConnection_GetIsolationInfo(This,isolationInfo) (This)->lpVtbl->GetIsolationInfo(This,isolationInfo)
#define INapEnforcementClientConnection_SetPrivateData(This,privateData) (This)->lpVtbl->SetPrivateData(This,privateData)
#define INapEnforcementClientConnection_GetPrivateData(This,privateData) (This)->lpVtbl->GetPrivateData(This,privateData)
#define INapEnforcementClientConnection_SetEnforcerPrivateData(This,privateData) (This)->lpVtbl->SetEnforcerPrivateData(This,privateData)
#define INapEnforcementClientConnection_GetEnforcerPrivateData(This,privateData) (This)->lpVtbl->GetEnforcerPrivateData(This,privateData)
#else
/*** IUnknown methods ***/
static inline HRESULT INapEnforcementClientConnection_QueryInterface(INapEnforcementClientConnection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INapEnforcementClientConnection_AddRef(INapEnforcementClientConnection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INapEnforcementClientConnection_Release(INapEnforcementClientConnection* This) {
    return This->lpVtbl->Release(This);
}
/*** INapEnforcementClientConnection methods ***/
static inline HRESULT INapEnforcementClientConnection_Initialize(INapEnforcementClientConnection* This,EnforcementEntityId id) {
    return This->lpVtbl->Initialize(This,id);
}
static inline HRESULT INapEnforcementClientConnection_SetMaxSize(INapEnforcementClientConnection* This,ProtocolMaxSize maxSize) {
    return This->lpVtbl->SetMaxSize(This,maxSize);
}
static inline HRESULT INapEnforcementClientConnection_GetMaxSize(INapEnforcementClientConnection* This,ProtocolMaxSize *maxSize) {
    return This->lpVtbl->GetMaxSize(This,maxSize);
}
static inline HRESULT INapEnforcementClientConnection_SetFlags(INapEnforcementClientConnection* This,UINT8 flags) {
    return This->lpVtbl->SetFlags(This,flags);
}
static inline HRESULT INapEnforcementClientConnection_GetFlags(INapEnforcementClientConnection* This,UINT8 *flags) {
    return This->lpVtbl->GetFlags(This,flags);
}
static inline HRESULT INapEnforcementClientConnection_SetConnectionId(INapEnforcementClientConnection* This,const ConnectionId *connectionId) {
    return This->lpVtbl->SetConnectionId(This,connectionId);
}
static inline HRESULT INapEnforcementClientConnection_GetConnectionId(INapEnforcementClientConnection* This,ConnectionId **connectionId) {
    return This->lpVtbl->GetConnectionId(This,connectionId);
}
static inline HRESULT INapEnforcementClientConnection_GetCorrelationId(INapEnforcementClientConnection* This,CorrelationId *correlationId) {
    return This->lpVtbl->GetCorrelationId(This,correlationId);
}
static inline HRESULT INapEnforcementClientConnection_GetStringCorrelationId(INapEnforcementClientConnection* This,StringCorrelationId **correlationId) {
    return This->lpVtbl->GetStringCorrelationId(This,correlationId);
}
static inline HRESULT INapEnforcementClientConnection_SetCorrelationId(INapEnforcementClientConnection* This,CorrelationId correlationId) {
    return This->lpVtbl->SetCorrelationId(This,correlationId);
}
static inline HRESULT INapEnforcementClientConnection_SetSoHRequest(INapEnforcementClientConnection* This,const NetworkSoHRequest *sohRequest) {
    return This->lpVtbl->SetSoHRequest(This,sohRequest);
}
static inline HRESULT INapEnforcementClientConnection_GetSoHRequest(INapEnforcementClientConnection* This,NetworkSoHRequest **sohRequest) {
    return This->lpVtbl->GetSoHRequest(This,sohRequest);
}
static inline HRESULT INapEnforcementClientConnection_SetSoHResponse(INapEnforcementClientConnection* This,const NetworkSoHResponse *sohResponse) {
    return This->lpVtbl->SetSoHResponse(This,sohResponse);
}
static inline HRESULT INapEnforcementClientConnection_GetSoHResponse(INapEnforcementClientConnection* This,NetworkSoHResponse **sohResponse) {
    return This->lpVtbl->GetSoHResponse(This,sohResponse);
}
static inline HRESULT INapEnforcementClientConnection_SetIsolationInfo(INapEnforcementClientConnection* This,const IsolationInfo *isolationInfo) {
    return This->lpVtbl->SetIsolationInfo(This,isolationInfo);
}
static inline HRESULT INapEnforcementClientConnection_GetIsolationInfo(INapEnforcementClientConnection* This,IsolationInfo **isolationInfo) {
    return This->lpVtbl->GetIsolationInfo(This,isolationInfo);
}
static inline HRESULT INapEnforcementClientConnection_SetPrivateData(INapEnforcementClientConnection* This,const PrivateData *privateData) {
    return This->lpVtbl->SetPrivateData(This,privateData);
}
static inline HRESULT INapEnforcementClientConnection_GetPrivateData(INapEnforcementClientConnection* This,PrivateData **privateData) {
    return This->lpVtbl->GetPrivateData(This,privateData);
}
static inline HRESULT INapEnforcementClientConnection_SetEnforcerPrivateData(INapEnforcementClientConnection* This,const PrivateData *privateData) {
    return This->lpVtbl->SetEnforcerPrivateData(This,privateData);
}
static inline HRESULT INapEnforcementClientConnection_GetEnforcerPrivateData(INapEnforcementClientConnection* This,PrivateData **privateData) {
    return This->lpVtbl->GetEnforcerPrivateData(This,privateData);
}
#endif
#endif

#endif


#endif  /* __INapEnforcementClientConnection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INapEnforcementClientConnection2 interface
 */
#ifndef __INapEnforcementClientConnection2_INTERFACE_DEFINED__
#define __INapEnforcementClientConnection2_INTERFACE_DEFINED__

DEFINE_GUID(IID_INapEnforcementClientConnection2, 0xbd244906, 0x70dd, 0x4690, 0xbe,0xea, 0x64,0x86,0x53,0x39,0x35,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bd244906-70dd-4690-beea-************")
INapEnforcementClientConnection2 : public INapEnforcementClientConnection
{
    virtual HRESULT STDMETHODCALLTYPE SetIsolationInfoEx(
        const IsolationInfoEx *isolationInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsolationInfoEx(
        IsolationInfoEx **isolationInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInstalledShvs(
        SystemHealthEntityCount *count,
        SystemHealthEntityId **ids) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInstalledShvs(
        SystemHealthEntityCount count,
        SystemHealthEntityId *ids) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INapEnforcementClientConnection2, 0xbd244906, 0x70dd, 0x4690, 0xbe,0xea, 0x64,0x86,0x53,0x39,0x35,0x00)
#endif
#else
typedef struct INapEnforcementClientConnection2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INapEnforcementClientConnection2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INapEnforcementClientConnection2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INapEnforcementClientConnection2 *This);

    /*** INapEnforcementClientConnection methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        INapEnforcementClientConnection2 *This,
        EnforcementEntityId id);

    HRESULT (STDMETHODCALLTYPE *SetMaxSize)(
        INapEnforcementClientConnection2 *This,
        ProtocolMaxSize maxSize);

    HRESULT (STDMETHODCALLTYPE *GetMaxSize)(
        INapEnforcementClientConnection2 *This,
        ProtocolMaxSize *maxSize);

    HRESULT (STDMETHODCALLTYPE *SetFlags)(
        INapEnforcementClientConnection2 *This,
        UINT8 flags);

    HRESULT (STDMETHODCALLTYPE *GetFlags)(
        INapEnforcementClientConnection2 *This,
        UINT8 *flags);

    HRESULT (STDMETHODCALLTYPE *SetConnectionId)(
        INapEnforcementClientConnection2 *This,
        const ConnectionId *connectionId);

    HRESULT (STDMETHODCALLTYPE *GetConnectionId)(
        INapEnforcementClientConnection2 *This,
        ConnectionId **connectionId);

    HRESULT (STDMETHODCALLTYPE *GetCorrelationId)(
        INapEnforcementClientConnection2 *This,
        CorrelationId *correlationId);

    HRESULT (STDMETHODCALLTYPE *GetStringCorrelationId)(
        INapEnforcementClientConnection2 *This,
        StringCorrelationId **correlationId);

    HRESULT (STDMETHODCALLTYPE *SetCorrelationId)(
        INapEnforcementClientConnection2 *This,
        CorrelationId correlationId);

    HRESULT (STDMETHODCALLTYPE *SetSoHRequest)(
        INapEnforcementClientConnection2 *This,
        const NetworkSoHRequest *sohRequest);

    HRESULT (STDMETHODCALLTYPE *GetSoHRequest)(
        INapEnforcementClientConnection2 *This,
        NetworkSoHRequest **sohRequest);

    HRESULT (STDMETHODCALLTYPE *SetSoHResponse)(
        INapEnforcementClientConnection2 *This,
        const NetworkSoHResponse *sohResponse);

    HRESULT (STDMETHODCALLTYPE *GetSoHResponse)(
        INapEnforcementClientConnection2 *This,
        NetworkSoHResponse **sohResponse);

    HRESULT (STDMETHODCALLTYPE *SetIsolationInfo)(
        INapEnforcementClientConnection2 *This,
        const IsolationInfo *isolationInfo);

    HRESULT (STDMETHODCALLTYPE *GetIsolationInfo)(
        INapEnforcementClientConnection2 *This,
        IsolationInfo **isolationInfo);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        INapEnforcementClientConnection2 *This,
        const PrivateData *privateData);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        INapEnforcementClientConnection2 *This,
        PrivateData **privateData);

    HRESULT (STDMETHODCALLTYPE *SetEnforcerPrivateData)(
        INapEnforcementClientConnection2 *This,
        const PrivateData *privateData);

    HRESULT (STDMETHODCALLTYPE *GetEnforcerPrivateData)(
        INapEnforcementClientConnection2 *This,
        PrivateData **privateData);

    /*** INapEnforcementClientConnection2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetIsolationInfoEx)(
        INapEnforcementClientConnection2 *This,
        const IsolationInfoEx *isolationInfo);

    HRESULT (STDMETHODCALLTYPE *GetIsolationInfoEx)(
        INapEnforcementClientConnection2 *This,
        IsolationInfoEx **isolationInfo);

    HRESULT (STDMETHODCALLTYPE *GetInstalledShvs)(
        INapEnforcementClientConnection2 *This,
        SystemHealthEntityCount *count,
        SystemHealthEntityId **ids);

    HRESULT (STDMETHODCALLTYPE *SetInstalledShvs)(
        INapEnforcementClientConnection2 *This,
        SystemHealthEntityCount count,
        SystemHealthEntityId *ids);

    END_INTERFACE
} INapEnforcementClientConnection2Vtbl;

interface INapEnforcementClientConnection2 {
    CONST_VTBL INapEnforcementClientConnection2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INapEnforcementClientConnection2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INapEnforcementClientConnection2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INapEnforcementClientConnection2_Release(This) (This)->lpVtbl->Release(This)
/*** INapEnforcementClientConnection methods ***/
#define INapEnforcementClientConnection2_Initialize(This,id) (This)->lpVtbl->Initialize(This,id)
#define INapEnforcementClientConnection2_SetMaxSize(This,maxSize) (This)->lpVtbl->SetMaxSize(This,maxSize)
#define INapEnforcementClientConnection2_GetMaxSize(This,maxSize) (This)->lpVtbl->GetMaxSize(This,maxSize)
#define INapEnforcementClientConnection2_SetFlags(This,flags) (This)->lpVtbl->SetFlags(This,flags)
#define INapEnforcementClientConnection2_GetFlags(This,flags) (This)->lpVtbl->GetFlags(This,flags)
#define INapEnforcementClientConnection2_SetConnectionId(This,connectionId) (This)->lpVtbl->SetConnectionId(This,connectionId)
#define INapEnforcementClientConnection2_GetConnectionId(This,connectionId) (This)->lpVtbl->GetConnectionId(This,connectionId)
#define INapEnforcementClientConnection2_GetCorrelationId(This,correlationId) (This)->lpVtbl->GetCorrelationId(This,correlationId)
#define INapEnforcementClientConnection2_GetStringCorrelationId(This,correlationId) (This)->lpVtbl->GetStringCorrelationId(This,correlationId)
#define INapEnforcementClientConnection2_SetCorrelationId(This,correlationId) (This)->lpVtbl->SetCorrelationId(This,correlationId)
#define INapEnforcementClientConnection2_SetSoHRequest(This,sohRequest) (This)->lpVtbl->SetSoHRequest(This,sohRequest)
#define INapEnforcementClientConnection2_GetSoHRequest(This,sohRequest) (This)->lpVtbl->GetSoHRequest(This,sohRequest)
#define INapEnforcementClientConnection2_SetSoHResponse(This,sohResponse) (This)->lpVtbl->SetSoHResponse(This,sohResponse)
#define INapEnforcementClientConnection2_GetSoHResponse(This,sohResponse) (This)->lpVtbl->GetSoHResponse(This,sohResponse)
#define INapEnforcementClientConnection2_SetIsolationInfo(This,isolationInfo) (This)->lpVtbl->SetIsolationInfo(This,isolationInfo)
#define INapEnforcementClientConnection2_GetIsolationInfo(This,isolationInfo) (This)->lpVtbl->GetIsolationInfo(This,isolationInfo)
#define INapEnforcementClientConnection2_SetPrivateData(This,privateData) (This)->lpVtbl->SetPrivateData(This,privateData)
#define INapEnforcementClientConnection2_GetPrivateData(This,privateData) (This)->lpVtbl->GetPrivateData(This,privateData)
#define INapEnforcementClientConnection2_SetEnforcerPrivateData(This,privateData) (This)->lpVtbl->SetEnforcerPrivateData(This,privateData)
#define INapEnforcementClientConnection2_GetEnforcerPrivateData(This,privateData) (This)->lpVtbl->GetEnforcerPrivateData(This,privateData)
/*** INapEnforcementClientConnection2 methods ***/
#define INapEnforcementClientConnection2_SetIsolationInfoEx(This,isolationInfo) (This)->lpVtbl->SetIsolationInfoEx(This,isolationInfo)
#define INapEnforcementClientConnection2_GetIsolationInfoEx(This,isolationInfo) (This)->lpVtbl->GetIsolationInfoEx(This,isolationInfo)
#define INapEnforcementClientConnection2_GetInstalledShvs(This,count,ids) (This)->lpVtbl->GetInstalledShvs(This,count,ids)
#define INapEnforcementClientConnection2_SetInstalledShvs(This,count,ids) (This)->lpVtbl->SetInstalledShvs(This,count,ids)
#else
/*** IUnknown methods ***/
static inline HRESULT INapEnforcementClientConnection2_QueryInterface(INapEnforcementClientConnection2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INapEnforcementClientConnection2_AddRef(INapEnforcementClientConnection2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INapEnforcementClientConnection2_Release(INapEnforcementClientConnection2* This) {
    return This->lpVtbl->Release(This);
}
/*** INapEnforcementClientConnection methods ***/
static inline HRESULT INapEnforcementClientConnection2_Initialize(INapEnforcementClientConnection2* This,EnforcementEntityId id) {
    return This->lpVtbl->Initialize(This,id);
}
static inline HRESULT INapEnforcementClientConnection2_SetMaxSize(INapEnforcementClientConnection2* This,ProtocolMaxSize maxSize) {
    return This->lpVtbl->SetMaxSize(This,maxSize);
}
static inline HRESULT INapEnforcementClientConnection2_GetMaxSize(INapEnforcementClientConnection2* This,ProtocolMaxSize *maxSize) {
    return This->lpVtbl->GetMaxSize(This,maxSize);
}
static inline HRESULT INapEnforcementClientConnection2_SetFlags(INapEnforcementClientConnection2* This,UINT8 flags) {
    return This->lpVtbl->SetFlags(This,flags);
}
static inline HRESULT INapEnforcementClientConnection2_GetFlags(INapEnforcementClientConnection2* This,UINT8 *flags) {
    return This->lpVtbl->GetFlags(This,flags);
}
static inline HRESULT INapEnforcementClientConnection2_SetConnectionId(INapEnforcementClientConnection2* This,const ConnectionId *connectionId) {
    return This->lpVtbl->SetConnectionId(This,connectionId);
}
static inline HRESULT INapEnforcementClientConnection2_GetConnectionId(INapEnforcementClientConnection2* This,ConnectionId **connectionId) {
    return This->lpVtbl->GetConnectionId(This,connectionId);
}
static inline HRESULT INapEnforcementClientConnection2_GetCorrelationId(INapEnforcementClientConnection2* This,CorrelationId *correlationId) {
    return This->lpVtbl->GetCorrelationId(This,correlationId);
}
static inline HRESULT INapEnforcementClientConnection2_GetStringCorrelationId(INapEnforcementClientConnection2* This,StringCorrelationId **correlationId) {
    return This->lpVtbl->GetStringCorrelationId(This,correlationId);
}
static inline HRESULT INapEnforcementClientConnection2_SetCorrelationId(INapEnforcementClientConnection2* This,CorrelationId correlationId) {
    return This->lpVtbl->SetCorrelationId(This,correlationId);
}
static inline HRESULT INapEnforcementClientConnection2_SetSoHRequest(INapEnforcementClientConnection2* This,const NetworkSoHRequest *sohRequest) {
    return This->lpVtbl->SetSoHRequest(This,sohRequest);
}
static inline HRESULT INapEnforcementClientConnection2_GetSoHRequest(INapEnforcementClientConnection2* This,NetworkSoHRequest **sohRequest) {
    return This->lpVtbl->GetSoHRequest(This,sohRequest);
}
static inline HRESULT INapEnforcementClientConnection2_SetSoHResponse(INapEnforcementClientConnection2* This,const NetworkSoHResponse *sohResponse) {
    return This->lpVtbl->SetSoHResponse(This,sohResponse);
}
static inline HRESULT INapEnforcementClientConnection2_GetSoHResponse(INapEnforcementClientConnection2* This,NetworkSoHResponse **sohResponse) {
    return This->lpVtbl->GetSoHResponse(This,sohResponse);
}
static inline HRESULT INapEnforcementClientConnection2_SetIsolationInfo(INapEnforcementClientConnection2* This,const IsolationInfo *isolationInfo) {
    return This->lpVtbl->SetIsolationInfo(This,isolationInfo);
}
static inline HRESULT INapEnforcementClientConnection2_GetIsolationInfo(INapEnforcementClientConnection2* This,IsolationInfo **isolationInfo) {
    return This->lpVtbl->GetIsolationInfo(This,isolationInfo);
}
static inline HRESULT INapEnforcementClientConnection2_SetPrivateData(INapEnforcementClientConnection2* This,const PrivateData *privateData) {
    return This->lpVtbl->SetPrivateData(This,privateData);
}
static inline HRESULT INapEnforcementClientConnection2_GetPrivateData(INapEnforcementClientConnection2* This,PrivateData **privateData) {
    return This->lpVtbl->GetPrivateData(This,privateData);
}
static inline HRESULT INapEnforcementClientConnection2_SetEnforcerPrivateData(INapEnforcementClientConnection2* This,const PrivateData *privateData) {
    return This->lpVtbl->SetEnforcerPrivateData(This,privateData);
}
static inline HRESULT INapEnforcementClientConnection2_GetEnforcerPrivateData(INapEnforcementClientConnection2* This,PrivateData **privateData) {
    return This->lpVtbl->GetEnforcerPrivateData(This,privateData);
}
/*** INapEnforcementClientConnection2 methods ***/
static inline HRESULT INapEnforcementClientConnection2_SetIsolationInfoEx(INapEnforcementClientConnection2* This,const IsolationInfoEx *isolationInfo) {
    return This->lpVtbl->SetIsolationInfoEx(This,isolationInfo);
}
static inline HRESULT INapEnforcementClientConnection2_GetIsolationInfoEx(INapEnforcementClientConnection2* This,IsolationInfoEx **isolationInfo) {
    return This->lpVtbl->GetIsolationInfoEx(This,isolationInfo);
}
static inline HRESULT INapEnforcementClientConnection2_GetInstalledShvs(INapEnforcementClientConnection2* This,SystemHealthEntityCount *count,SystemHealthEntityId **ids) {
    return This->lpVtbl->GetInstalledShvs(This,count,ids);
}
static inline HRESULT INapEnforcementClientConnection2_SetInstalledShvs(INapEnforcementClientConnection2* This,SystemHealthEntityCount count,SystemHealthEntityId *ids) {
    return This->lpVtbl->SetInstalledShvs(This,count,ids);
}
#endif
#endif

#endif


#endif  /* __INapEnforcementClientConnection2_INTERFACE_DEFINED__ */

EXTERN_C const CLSID CLSID_NapEnforcementClientBinding;
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __napenforcementclient_h__ */
