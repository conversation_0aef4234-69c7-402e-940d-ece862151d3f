/*** Autogenerated by WIDL 10.12 from include/windows.media.devices.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_media_devices_h__
#define __windows_media_devices_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs ABI::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                interface IDefaultAudioDeviceChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics ABI::Windows::Media::Devices::IMediaDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                interface IMediaDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CMediaDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CMediaDevice_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                class MediaDevice;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CDevices_CMediaDevice __x_ABI_CWindows_CMedia_CDevices_CMediaDevice;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CDevices_CMediaDevice_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CDefaultAudioRenderDeviceChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CDefaultAudioRenderDeviceChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                class DefaultAudioRenderDeviceChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CDevices_CDefaultAudioRenderDeviceChangedEventArgs __x_ABI_CWindows_CMedia_CDevices_CDefaultAudioRenderDeviceChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CDevices_CDefaultAudioRenderDeviceChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CDefaultAudioCaptureDeviceChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CDefaultAudioCaptureDeviceChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                class DefaultAudioCaptureDeviceChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CDevices_CDefaultAudioCaptureDeviceChangedEventArgs __x_ABI_CWindows_CMedia_CDevices_CDefaultAudioCaptureDeviceChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CDevices_CDefaultAudioCaptureDeviceChangedEventArgs_FWD_DEFINED__ */

#ifndef ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <eventtoken.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                enum AudioDeviceRole {
                    AudioDeviceRole_Default = 0,
                    AudioDeviceRole_Communications = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole {
    AudioDeviceRole_Default = 0,
    AudioDeviceRole_Communications = 1
};
#ifdef WIDL_using_Windows_Media_Devices
#define AudioDeviceRole __x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole
#endif /* WIDL_using_Windows_Media_Devices */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole_ENUM_DEFINED__ */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole __x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs ABI::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                interface IDefaultAudioDeviceChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics ABI::Windows::Media::Devices::IMediaDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                interface IMediaDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IDefaultAudioDeviceChangedEventArgs interface
 */
#ifndef ____x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs, 0x110f882f, 0x1c05, 0x4657, 0xa1,0x8e, 0x47,0xc9,0xb6,0x9f,0x07,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                MIDL_INTERFACE("110f882f-1c05-4657-a18e-47c9b69f07ab")
                IDefaultAudioDeviceChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Role(
                        ABI::Windows::Media::Devices::AudioDeviceRole *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs, 0x110f882f, 0x1c05, 0x4657, 0xa1,0x8e, 0x47,0xc9,0xb6,0x9f,0x07,0xab)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IDefaultAudioDeviceChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Role)(
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *This,
        __x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDefaultAudioDeviceChangedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_get_Role(This,value) (This)->lpVtbl->get_Role(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_Release(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDefaultAudioDeviceChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_get_Id(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_get_Role(__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs* This,__x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole *value) {
    return This->lpVtbl->get_Role(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_Devices
#define IID_IDefaultAudioDeviceChangedEventArgs IID___x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs
#define IDefaultAudioDeviceChangedEventArgsVtbl __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgsVtbl
#define IDefaultAudioDeviceChangedEventArgs __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs
#define IDefaultAudioDeviceChangedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_QueryInterface
#define IDefaultAudioDeviceChangedEventArgs_AddRef __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_AddRef
#define IDefaultAudioDeviceChangedEventArgs_Release __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_Release
#define IDefaultAudioDeviceChangedEventArgs_GetIids __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetIids
#define IDefaultAudioDeviceChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetRuntimeClassName
#define IDefaultAudioDeviceChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_GetTrustLevel
#define IDefaultAudioDeviceChangedEventArgs_get_Id __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_get_Id
#define IDefaultAudioDeviceChangedEventArgs_get_Role __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_get_Role
#endif /* WIDL_using_Windows_Media_Devices */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMediaDeviceStatics interface
 */
#ifndef ____x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics, 0xaa2d9a40, 0x909f, 0x4bba, 0xbf,0x8b, 0x0c,0x0d,0x29,0x6f,0x14,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Devices {
                MIDL_INTERFACE("aa2d9a40-909f-4bba-bf8b-0c0d296f14f0")
                IMediaDeviceStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetAudioCaptureSelector(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetAudioRenderSelector(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetVideoCaptureSelector(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDefaultAudioCaptureId(
                        ABI::Windows::Media::Devices::AudioDeviceRole role,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDefaultAudioRenderId(
                        ABI::Windows::Media::Devices::AudioDeviceRole role,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_DefaultAudioCaptureDeviceChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_DefaultAudioCaptureDeviceChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_DefaultAudioRenderDeviceChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_DefaultAudioRenderDeviceChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics, 0xaa2d9a40, 0x909f, 0x4bba, 0xbf,0x8b, 0x0c,0x0d,0x29,0x6f,0x14,0xf0)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        TrustLevel *trustLevel);

    /*** IMediaDeviceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAudioCaptureSelector)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetAudioRenderSelector)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetVideoCaptureSelector)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetDefaultAudioCaptureId)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        __x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole role,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetDefaultAudioRenderId)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        __x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole role,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *add_DefaultAudioCaptureDeviceChanged)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_DefaultAudioCaptureDeviceChanged)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_DefaultAudioRenderDeviceChanged)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_DefaultAudioRenderDeviceChanged)(
        __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStaticsVtbl;

interface __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics {
    CONST_VTBL __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMediaDeviceStatics methods ***/
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetAudioCaptureSelector(This,value) (This)->lpVtbl->GetAudioCaptureSelector(This,value)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetAudioRenderSelector(This,value) (This)->lpVtbl->GetAudioRenderSelector(This,value)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetVideoCaptureSelector(This,value) (This)->lpVtbl->GetVideoCaptureSelector(This,value)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetDefaultAudioCaptureId(This,role,value) (This)->lpVtbl->GetDefaultAudioCaptureId(This,role,value)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetDefaultAudioRenderId(This,role,value) (This)->lpVtbl->GetDefaultAudioRenderId(This,role,value)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_add_DefaultAudioCaptureDeviceChanged(This,handler,token) (This)->lpVtbl->add_DefaultAudioCaptureDeviceChanged(This,handler,token)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_remove_DefaultAudioCaptureDeviceChanged(This,token) (This)->lpVtbl->remove_DefaultAudioCaptureDeviceChanged(This,token)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_add_DefaultAudioRenderDeviceChanged(This,handler,token) (This)->lpVtbl->add_DefaultAudioRenderDeviceChanged(This,handler,token)
#define __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_remove_DefaultAudioRenderDeviceChanged(This,token) (This)->lpVtbl->remove_DefaultAudioRenderDeviceChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_QueryInterface(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_AddRef(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_Release(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetIids(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetTrustLevel(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMediaDeviceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetAudioCaptureSelector(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,HSTRING *value) {
    return This->lpVtbl->GetAudioCaptureSelector(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetAudioRenderSelector(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,HSTRING *value) {
    return This->lpVtbl->GetAudioRenderSelector(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetVideoCaptureSelector(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,HSTRING *value) {
    return This->lpVtbl->GetVideoCaptureSelector(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetDefaultAudioCaptureId(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,__x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole role,HSTRING *value) {
    return This->lpVtbl->GetDefaultAudioCaptureId(This,role,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetDefaultAudioRenderId(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,__x_ABI_CWindows_CMedia_CDevices_CAudioDeviceRole role,HSTRING *value) {
    return This->lpVtbl->GetDefaultAudioRenderId(This,role,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_add_DefaultAudioCaptureDeviceChanged(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_DefaultAudioCaptureDeviceChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_remove_DefaultAudioCaptureDeviceChanged(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_DefaultAudioCaptureDeviceChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_add_DefaultAudioRenderDeviceChanged(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_DefaultAudioRenderDeviceChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_remove_DefaultAudioRenderDeviceChanged(__x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_DefaultAudioRenderDeviceChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Media_Devices
#define IID_IMediaDeviceStatics IID___x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics
#define IMediaDeviceStaticsVtbl __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStaticsVtbl
#define IMediaDeviceStatics __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics
#define IMediaDeviceStatics_QueryInterface __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_QueryInterface
#define IMediaDeviceStatics_AddRef __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_AddRef
#define IMediaDeviceStatics_Release __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_Release
#define IMediaDeviceStatics_GetIids __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetIids
#define IMediaDeviceStatics_GetRuntimeClassName __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetRuntimeClassName
#define IMediaDeviceStatics_GetTrustLevel __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetTrustLevel
#define IMediaDeviceStatics_GetAudioCaptureSelector __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetAudioCaptureSelector
#define IMediaDeviceStatics_GetAudioRenderSelector __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetAudioRenderSelector
#define IMediaDeviceStatics_GetVideoCaptureSelector __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetVideoCaptureSelector
#define IMediaDeviceStatics_GetDefaultAudioCaptureId __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetDefaultAudioCaptureId
#define IMediaDeviceStatics_GetDefaultAudioRenderId __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_GetDefaultAudioRenderId
#define IMediaDeviceStatics_add_DefaultAudioCaptureDeviceChanged __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_add_DefaultAudioCaptureDeviceChanged
#define IMediaDeviceStatics_remove_DefaultAudioCaptureDeviceChanged __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_remove_DefaultAudioCaptureDeviceChanged
#define IMediaDeviceStatics_add_DefaultAudioRenderDeviceChanged __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_add_DefaultAudioRenderDeviceChanged
#define IMediaDeviceStatics_remove_DefaultAudioRenderDeviceChanged __x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_remove_DefaultAudioRenderDeviceChanged
#endif /* WIDL_using_Windows_Media_Devices */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CDevices_CIMediaDeviceStatics_INTERFACE_DEFINED__ */

/*
 * Class Windows.Media.Devices.MediaDevice
 */
#ifndef RUNTIMECLASS_Windows_Media_Devices_MediaDevice_DEFINED
#define RUNTIMECLASS_Windows_Media_Devices_MediaDevice_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_Devices_MediaDevice[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','D','e','v','i','c','e','s','.','M','e','d','i','a','D','e','v','i','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Devices_MediaDevice[] = L"Windows.Media.Devices.MediaDevice";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Devices_MediaDevice[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','D','e','v','i','c','e','s','.','M','e','d','i','a','D','e','v','i','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_Devices_MediaDevice_DEFINED */

/*
 * Class Windows.Media.Devices.DefaultAudioRenderDeviceChangedEventArgs
 */
#ifndef RUNTIMECLASS_Windows_Media_Devices_DefaultAudioRenderDeviceChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_Devices_DefaultAudioRenderDeviceChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_Devices_DefaultAudioRenderDeviceChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','D','e','v','i','c','e','s','.','D','e','f','a','u','l','t','A','u','d','i','o','R','e','n','d','e','r','D','e','v','i','c','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Devices_DefaultAudioRenderDeviceChangedEventArgs[] = L"Windows.Media.Devices.DefaultAudioRenderDeviceChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Devices_DefaultAudioRenderDeviceChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','D','e','v','i','c','e','s','.','D','e','f','a','u','l','t','A','u','d','i','o','R','e','n','d','e','r','D','e','v','i','c','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_Devices_DefaultAudioRenderDeviceChangedEventArgs_DEFINED */

/*
 * Class Windows.Media.Devices.DefaultAudioCaptureDeviceChangedEventArgs
 */
#ifndef RUNTIMECLASS_Windows_Media_Devices_DefaultAudioCaptureDeviceChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_Devices_DefaultAudioCaptureDeviceChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_Devices_DefaultAudioCaptureDeviceChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','D','e','v','i','c','e','s','.','D','e','f','a','u','l','t','A','u','d','i','o','C','a','p','t','u','r','e','D','e','v','i','c','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Devices_DefaultAudioCaptureDeviceChangedEventArgs[] = L"Windows.Media.Devices.DefaultAudioCaptureDeviceChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Devices_DefaultAudioCaptureDeviceChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','D','e','v','i','c','e','s','.','D','e','f','a','u','l','t','A','u','d','i','o','C','a','p','t','u','r','e','D','e','v','i','c','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_Devices_DefaultAudioCaptureDeviceChangedEventArgs_DEFINED */

/*****************************************************************************
 * ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs, 0x97d07327, 0x2c78, 0x57bc, 0x98,0xe6, 0xa2,0x4c,0xd0,0x24,0xcf,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("97d07327-2c78-57bc-98e6-a24cd024cf5b")
            ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs* > : ITypedEventHandler_impl<IInspectable*, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs*, ABI::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs, 0x97d07327, 0x2c78, 0x57bc, 0x98,0xe6, 0xa2,0x4c,0xd0,0x24,0xcf,0x5b)
#endif
#else
typedef struct __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs *This);

    /*** ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgsVtbl;

interface __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_QueryInterface(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_AddRef(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_Release(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioCaptureDeviceChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_Invoke(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_IInspectable_DefaultAudioCaptureDeviceChangedEventArgs IID___FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs
#define ITypedEventHandler_IInspectable_DefaultAudioCaptureDeviceChangedEventArgsVtbl __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgsVtbl
#define ITypedEventHandler_IInspectable_DefaultAudioCaptureDeviceChangedEventArgs __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs
#define ITypedEventHandler_IInspectable_DefaultAudioCaptureDeviceChangedEventArgs_QueryInterface __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_QueryInterface
#define ITypedEventHandler_IInspectable_DefaultAudioCaptureDeviceChangedEventArgs_AddRef __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_AddRef
#define ITypedEventHandler_IInspectable_DefaultAudioCaptureDeviceChangedEventArgs_Release __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_Release
#define ITypedEventHandler_IInspectable_DefaultAudioCaptureDeviceChangedEventArgs_Invoke __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioCaptureDeviceChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs, 0xfd732aca, 0xdafc, 0x5b7d, 0xbf,0x72, 0xb5,0x60,0xb7,0x8d,0x26,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("fd732aca-dafc-5b7d-bf72-b560b78d260c")
            ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs* > : ITypedEventHandler_impl<IInspectable*, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs*, ABI::Windows::Media::Devices::IDefaultAudioDeviceChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs, 0xfd732aca, 0xdafc, 0x5b7d, 0xbf,0x72, 0xb5,0x60,0xb7,0x8d,0x26,0x0c)
#endif
#else
typedef struct __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs *This);

    /*** ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgsVtbl;

interface __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_QueryInterface(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_AddRef(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_Release(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<IInspectable*,ABI::Windows::Media::Devices::DefaultAudioRenderDeviceChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_Invoke(__FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CMedia_CDevices_CIDefaultAudioDeviceChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_IInspectable_DefaultAudioRenderDeviceChangedEventArgs IID___FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs
#define ITypedEventHandler_IInspectable_DefaultAudioRenderDeviceChangedEventArgsVtbl __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgsVtbl
#define ITypedEventHandler_IInspectable_DefaultAudioRenderDeviceChangedEventArgs __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs
#define ITypedEventHandler_IInspectable_DefaultAudioRenderDeviceChangedEventArgs_QueryInterface __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_QueryInterface
#define ITypedEventHandler_IInspectable_DefaultAudioRenderDeviceChangedEventArgs_AddRef __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_AddRef
#define ITypedEventHandler_IInspectable_DefaultAudioRenderDeviceChangedEventArgs_Release __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_Release
#define ITypedEventHandler_IInspectable_DefaultAudioRenderDeviceChangedEventArgs_Invoke __FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_IInspectable_Windows__CMedia__CDevices__CDefaultAudioRenderDeviceChangedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_media_devices_h__ */
