/*** Autogenerated by WIDL 10.12 from include/urlmon.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __urlmon_h__
#define __urlmon_h__

/* Forward declarations */

#ifndef __IPersistMoniker_FWD_DEFINED__
#define __IPersistMoniker_FWD_DEFINED__
typedef interface IPersistMoniker IPersistMoniker;
#ifdef __cplusplus
interface IPersistMoniker;
#endif /* __cplusplus */
#endif

#ifndef __IMonikerProp_FWD_DEFINED__
#define __IMonikerProp_FWD_DEFINED__
typedef interface IMonikerProp IMonikerProp;
#ifdef __cplusplus
interface IMonikerProp;
#endif /* __cplusplus */
#endif

#ifndef __IBindProtocol_FWD_DEFINED__
#define __IBindProtocol_FWD_DEFINED__
typedef interface IBindProtocol IBindProtocol;
#ifdef __cplusplus
interface IBindProtocol;
#endif /* __cplusplus */
#endif

#ifndef __IBinding_FWD_DEFINED__
#define __IBinding_FWD_DEFINED__
typedef interface IBinding IBinding;
#ifdef __cplusplus
interface IBinding;
#endif /* __cplusplus */
#endif

#ifndef __IBindStatusCallback_FWD_DEFINED__
#define __IBindStatusCallback_FWD_DEFINED__
typedef interface IBindStatusCallback IBindStatusCallback;
#ifdef __cplusplus
interface IBindStatusCallback;
#endif /* __cplusplus */
#endif

#ifndef __IBindStatusCallbackEx_FWD_DEFINED__
#define __IBindStatusCallbackEx_FWD_DEFINED__
typedef interface IBindStatusCallbackEx IBindStatusCallbackEx;
#ifdef __cplusplus
interface IBindStatusCallbackEx;
#endif /* __cplusplus */
#endif

#ifndef __IAuthenticate_FWD_DEFINED__
#define __IAuthenticate_FWD_DEFINED__
typedef interface IAuthenticate IAuthenticate;
#ifdef __cplusplus
interface IAuthenticate;
#endif /* __cplusplus */
#endif

#ifndef __IAuthenticateEx_FWD_DEFINED__
#define __IAuthenticateEx_FWD_DEFINED__
typedef interface IAuthenticateEx IAuthenticateEx;
#ifdef __cplusplus
interface IAuthenticateEx;
#endif /* __cplusplus */
#endif

#ifndef __IHttpNegotiate_FWD_DEFINED__
#define __IHttpNegotiate_FWD_DEFINED__
typedef interface IHttpNegotiate IHttpNegotiate;
#ifdef __cplusplus
interface IHttpNegotiate;
#endif /* __cplusplus */
#endif

#ifndef __IHttpNegotiate2_FWD_DEFINED__
#define __IHttpNegotiate2_FWD_DEFINED__
typedef interface IHttpNegotiate2 IHttpNegotiate2;
#ifdef __cplusplus
interface IHttpNegotiate2;
#endif /* __cplusplus */
#endif

#ifndef __IHttpNegotiate3_FWD_DEFINED__
#define __IHttpNegotiate3_FWD_DEFINED__
typedef interface IHttpNegotiate3 IHttpNegotiate3;
#ifdef __cplusplus
interface IHttpNegotiate3;
#endif /* __cplusplus */
#endif

#ifndef __IWinInetFileStream_FWD_DEFINED__
#define __IWinInetFileStream_FWD_DEFINED__
typedef interface IWinInetFileStream IWinInetFileStream;
#ifdef __cplusplus
interface IWinInetFileStream;
#endif /* __cplusplus */
#endif

#ifndef __IWindowForBindingUI_FWD_DEFINED__
#define __IWindowForBindingUI_FWD_DEFINED__
typedef interface IWindowForBindingUI IWindowForBindingUI;
#ifdef __cplusplus
interface IWindowForBindingUI;
#endif /* __cplusplus */
#endif

#ifndef __ICodeInstall_FWD_DEFINED__
#define __ICodeInstall_FWD_DEFINED__
typedef interface ICodeInstall ICodeInstall;
#ifdef __cplusplus
interface ICodeInstall;
#endif /* __cplusplus */
#endif

#ifndef __IUri_FWD_DEFINED__
#define __IUri_FWD_DEFINED__
typedef interface IUri IUri;
#ifdef __cplusplus
interface IUri;
#endif /* __cplusplus */
#endif

#ifndef __IUriContainer_FWD_DEFINED__
#define __IUriContainer_FWD_DEFINED__
typedef interface IUriContainer IUriContainer;
#ifdef __cplusplus
interface IUriContainer;
#endif /* __cplusplus */
#endif

#ifndef __IUriBuilder_FWD_DEFINED__
#define __IUriBuilder_FWD_DEFINED__
typedef interface IUriBuilder IUriBuilder;
#ifdef __cplusplus
interface IUriBuilder;
#endif /* __cplusplus */
#endif

#ifndef __IUriBuilderFactory_FWD_DEFINED__
#define __IUriBuilderFactory_FWD_DEFINED__
typedef interface IUriBuilderFactory IUriBuilderFactory;
#ifdef __cplusplus
interface IUriBuilderFactory;
#endif /* __cplusplus */
#endif

#ifndef __IWinInetInfo_FWD_DEFINED__
#define __IWinInetInfo_FWD_DEFINED__
typedef interface IWinInetInfo IWinInetInfo;
#ifdef __cplusplus
interface IWinInetInfo;
#endif /* __cplusplus */
#endif

#ifndef __IHttpSecurity_FWD_DEFINED__
#define __IHttpSecurity_FWD_DEFINED__
typedef interface IHttpSecurity IHttpSecurity;
#ifdef __cplusplus
interface IHttpSecurity;
#endif /* __cplusplus */
#endif

#ifndef __IWinInetHttpInfo_FWD_DEFINED__
#define __IWinInetHttpInfo_FWD_DEFINED__
typedef interface IWinInetHttpInfo IWinInetHttpInfo;
#ifdef __cplusplus
interface IWinInetHttpInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWinInetHttpTimeouts_FWD_DEFINED__
#define __IWinInetHttpTimeouts_FWD_DEFINED__
typedef interface IWinInetHttpTimeouts IWinInetHttpTimeouts;
#ifdef __cplusplus
interface IWinInetHttpTimeouts;
#endif /* __cplusplus */
#endif

#ifndef __IWinInetCacheHints_FWD_DEFINED__
#define __IWinInetCacheHints_FWD_DEFINED__
typedef interface IWinInetCacheHints IWinInetCacheHints;
#ifdef __cplusplus
interface IWinInetCacheHints;
#endif /* __cplusplus */
#endif

#ifndef __IWinInetCacheHints2_FWD_DEFINED__
#define __IWinInetCacheHints2_FWD_DEFINED__
typedef interface IWinInetCacheHints2 IWinInetCacheHints2;
#ifdef __cplusplus
interface IWinInetCacheHints2;
#endif /* __cplusplus */
#endif

#ifndef __IBindHost_FWD_DEFINED__
#define __IBindHost_FWD_DEFINED__
typedef interface IBindHost IBindHost;
#ifdef __cplusplus
interface IBindHost;
#endif /* __cplusplus */
#endif

#ifndef __IInternet_FWD_DEFINED__
#define __IInternet_FWD_DEFINED__
typedef interface IInternet IInternet;
#ifdef __cplusplus
interface IInternet;
#endif /* __cplusplus */
#endif

#ifndef __IInternetBindInfo_FWD_DEFINED__
#define __IInternetBindInfo_FWD_DEFINED__
typedef interface IInternetBindInfo IInternetBindInfo;
#ifdef __cplusplus
interface IInternetBindInfo;
#endif /* __cplusplus */
#endif

#ifndef __IInternetBindInfoEx_FWD_DEFINED__
#define __IInternetBindInfoEx_FWD_DEFINED__
typedef interface IInternetBindInfoEx IInternetBindInfoEx;
#ifdef __cplusplus
interface IInternetBindInfoEx;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolRoot_FWD_DEFINED__
#define __IInternetProtocolRoot_FWD_DEFINED__
typedef interface IInternetProtocolRoot IInternetProtocolRoot;
#ifdef __cplusplus
interface IInternetProtocolRoot;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocol_FWD_DEFINED__
#define __IInternetProtocol_FWD_DEFINED__
typedef interface IInternetProtocol IInternetProtocol;
#ifdef __cplusplus
interface IInternetProtocol;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolEx_FWD_DEFINED__
#define __IInternetProtocolEx_FWD_DEFINED__
typedef interface IInternetProtocolEx IInternetProtocolEx;
#ifdef __cplusplus
interface IInternetProtocolEx;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolSink_FWD_DEFINED__
#define __IInternetProtocolSink_FWD_DEFINED__
typedef interface IInternetProtocolSink IInternetProtocolSink;
#ifdef __cplusplus
interface IInternetProtocolSink;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolSinkStackable_FWD_DEFINED__
#define __IInternetProtocolSinkStackable_FWD_DEFINED__
typedef interface IInternetProtocolSinkStackable IInternetProtocolSinkStackable;
#ifdef __cplusplus
interface IInternetProtocolSinkStackable;
#endif /* __cplusplus */
#endif

#ifndef __IInternetSession_FWD_DEFINED__
#define __IInternetSession_FWD_DEFINED__
typedef interface IInternetSession IInternetSession;
#ifdef __cplusplus
interface IInternetSession;
#endif /* __cplusplus */
#endif

#ifndef __IInternetThreadSwitch_FWD_DEFINED__
#define __IInternetThreadSwitch_FWD_DEFINED__
typedef interface IInternetThreadSwitch IInternetThreadSwitch;
#ifdef __cplusplus
interface IInternetThreadSwitch;
#endif /* __cplusplus */
#endif

#ifndef __IInternetPriority_FWD_DEFINED__
#define __IInternetPriority_FWD_DEFINED__
typedef interface IInternetPriority IInternetPriority;
#ifdef __cplusplus
interface IInternetPriority;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolInfo_FWD_DEFINED__
#define __IInternetProtocolInfo_FWD_DEFINED__
typedef interface IInternetProtocolInfo IInternetProtocolInfo;
#ifdef __cplusplus
interface IInternetProtocolInfo;
#endif /* __cplusplus */
#endif

#ifndef __IInternetSecurityMgrSite_FWD_DEFINED__
#define __IInternetSecurityMgrSite_FWD_DEFINED__
typedef interface IInternetSecurityMgrSite IInternetSecurityMgrSite;
#ifdef __cplusplus
interface IInternetSecurityMgrSite;
#endif /* __cplusplus */
#endif

#ifndef __IInternetSecurityManager_FWD_DEFINED__
#define __IInternetSecurityManager_FWD_DEFINED__
typedef interface IInternetSecurityManager IInternetSecurityManager;
#ifdef __cplusplus
interface IInternetSecurityManager;
#endif /* __cplusplus */
#endif

#ifndef __IInternetSecurityManagerEx_FWD_DEFINED__
#define __IInternetSecurityManagerEx_FWD_DEFINED__
typedef interface IInternetSecurityManagerEx IInternetSecurityManagerEx;
#ifdef __cplusplus
interface IInternetSecurityManagerEx;
#endif /* __cplusplus */
#endif

#ifndef __IInternetSecurityManagerEx2_FWD_DEFINED__
#define __IInternetSecurityManagerEx2_FWD_DEFINED__
typedef interface IInternetSecurityManagerEx2 IInternetSecurityManagerEx2;
#ifdef __cplusplus
interface IInternetSecurityManagerEx2;
#endif /* __cplusplus */
#endif

#ifndef __IZoneIdentifier_FWD_DEFINED__
#define __IZoneIdentifier_FWD_DEFINED__
typedef interface IZoneIdentifier IZoneIdentifier;
#ifdef __cplusplus
interface IZoneIdentifier;
#endif /* __cplusplus */
#endif

#ifndef __IInternetHostSecurityManager_FWD_DEFINED__
#define __IInternetHostSecurityManager_FWD_DEFINED__
typedef interface IInternetHostSecurityManager IInternetHostSecurityManager;
#ifdef __cplusplus
interface IInternetHostSecurityManager;
#endif /* __cplusplus */
#endif

#ifndef __IInternetZoneManager_FWD_DEFINED__
#define __IInternetZoneManager_FWD_DEFINED__
typedef interface IInternetZoneManager IInternetZoneManager;
#ifdef __cplusplus
interface IInternetZoneManager;
#endif /* __cplusplus */
#endif

#ifndef __IInternetZoneManagerEx_FWD_DEFINED__
#define __IInternetZoneManagerEx_FWD_DEFINED__
typedef interface IInternetZoneManagerEx IInternetZoneManagerEx;
#ifdef __cplusplus
interface IInternetZoneManagerEx;
#endif /* __cplusplus */
#endif

#ifndef __IInternetZoneManagerEx2_FWD_DEFINED__
#define __IInternetZoneManagerEx2_FWD_DEFINED__
typedef interface IInternetZoneManagerEx2 IInternetZoneManagerEx2;
#ifdef __cplusplus
interface IInternetZoneManagerEx2;
#endif /* __cplusplus */
#endif

#ifndef __ISoftDistExt_FWD_DEFINED__
#define __ISoftDistExt_FWD_DEFINED__
typedef interface ISoftDistExt ISoftDistExt;
#ifdef __cplusplus
interface ISoftDistExt;
#endif /* __cplusplus */
#endif

#ifndef __ICatalogFileInfo_FWD_DEFINED__
#define __ICatalogFileInfo_FWD_DEFINED__
typedef interface ICatalogFileInfo ICatalogFileInfo;
#ifdef __cplusplus
interface ICatalogFileInfo;
#endif /* __cplusplus */
#endif

#ifndef __IDataFilter_FWD_DEFINED__
#define __IDataFilter_FWD_DEFINED__
typedef interface IDataFilter IDataFilter;
#ifdef __cplusplus
interface IDataFilter;
#endif /* __cplusplus */
#endif

#ifndef __IEncodingFilterFactory_FWD_DEFINED__
#define __IEncodingFilterFactory_FWD_DEFINED__
typedef interface IEncodingFilterFactory IEncodingFilterFactory;
#ifdef __cplusplus
interface IEncodingFilterFactory;
#endif /* __cplusplus */
#endif

#ifndef __IWrappedProtocol_FWD_DEFINED__
#define __IWrappedProtocol_FWD_DEFINED__
typedef interface IWrappedProtocol IWrappedProtocol;
#ifdef __cplusplus
interface IWrappedProtocol;
#endif /* __cplusplus */
#endif

#ifndef __IGetBindHandle_FWD_DEFINED__
#define __IGetBindHandle_FWD_DEFINED__
typedef interface IGetBindHandle IGetBindHandle;
#ifdef __cplusplus
interface IGetBindHandle;
#endif /* __cplusplus */
#endif

#ifndef __IBindCallbackRedirect_FWD_DEFINED__
#define __IBindCallbackRedirect_FWD_DEFINED__
typedef interface IBindCallbackRedirect IBindCallbackRedirect;
#ifdef __cplusplus
interface IBindCallbackRedirect;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <objidl.h>
#include <oleidl.h>
#include <servprov.h>
#include <msxml.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)


#ifndef __IPersistMoniker_FWD_DEFINED__
#define __IPersistMoniker_FWD_DEFINED__
typedef interface IPersistMoniker IPersistMoniker;
#ifdef __cplusplus
interface IPersistMoniker;
#endif /* __cplusplus */
#endif

#ifndef __IBindProtocol_FWD_DEFINED__
#define __IBindProtocol_FWD_DEFINED__
typedef interface IBindProtocol IBindProtocol;
#ifdef __cplusplus
interface IBindProtocol;
#endif /* __cplusplus */
#endif

#ifndef __IBinding_FWD_DEFINED__
#define __IBinding_FWD_DEFINED__
typedef interface IBinding IBinding;
#ifdef __cplusplus
interface IBinding;
#endif /* __cplusplus */
#endif

#ifndef __IBindStatusCallback_FWD_DEFINED__
#define __IBindStatusCallback_FWD_DEFINED__
typedef interface IBindStatusCallback IBindStatusCallback;
#ifdef __cplusplus
interface IBindStatusCallback;
#endif /* __cplusplus */
#endif

#ifndef __IBindStatusCallbackEx_FWD_DEFINED__
#define __IBindStatusCallbackEx_FWD_DEFINED__
typedef interface IBindStatusCallbackEx IBindStatusCallbackEx;
#ifdef __cplusplus
interface IBindStatusCallbackEx;
#endif /* __cplusplus */
#endif

#ifndef __IBindStatusCallbackMsg_FWD_DEFINED__
#define __IBindStatusCallbackMsg_FWD_DEFINED__
typedef interface IBindStatusCallbackMsg IBindStatusCallbackMsg;
#ifdef __cplusplus
interface IBindStatusCallbackMsg;
#endif /* __cplusplus */
#endif

#ifndef __IAuthenticate_FWD_DEFINED__
#define __IAuthenticate_FWD_DEFINED__
typedef interface IAuthenticate IAuthenticate;
#ifdef __cplusplus
interface IAuthenticate;
#endif /* __cplusplus */
#endif

#ifndef __IAuthenticateEx_FWD_DEFINED__
#define __IAuthenticateEx_FWD_DEFINED__
typedef interface IAuthenticateEx IAuthenticateEx;
#ifdef __cplusplus
interface IAuthenticateEx;
#endif /* __cplusplus */
#endif

#ifndef __IWindowForBindingUI_FWD_DEFINED__
#define __IWindowForBindingUI_FWD_DEFINED__
typedef interface IWindowForBindingUI IWindowForBindingUI;
#ifdef __cplusplus
interface IWindowForBindingUI;
#endif /* __cplusplus */
#endif

#ifndef __ICodeInstall_FWD_DEFINED__
#define __ICodeInstall_FWD_DEFINED__
typedef interface ICodeInstall ICodeInstall;
#ifdef __cplusplus
interface ICodeInstall;
#endif /* __cplusplus */
#endif

#ifndef __IHttpNegotiate_FWD_DEFINED__
#define __IHttpNegotiate_FWD_DEFINED__
typedef interface IHttpNegotiate IHttpNegotiate;
#ifdef __cplusplus
interface IHttpNegotiate;
#endif /* __cplusplus */
#endif

#ifndef __IHttpNegotiate2_FWD_DEFINED__
#define __IHttpNegotiate2_FWD_DEFINED__
typedef interface IHttpNegotiate2 IHttpNegotiate2;
#ifdef __cplusplus
interface IHttpNegotiate2;
#endif /* __cplusplus */
#endif

#ifndef __IHttpNegotiate3_FWD_DEFINED__
#define __IHttpNegotiate3_FWD_DEFINED__
typedef interface IHttpNegotiate3 IHttpNegotiate3;
#ifdef __cplusplus
interface IHttpNegotiate3;
#endif /* __cplusplus */
#endif

#ifndef __IWinInetFileStream_FWD_DEFINED__
#define __IWinInetFileStream_FWD_DEFINED__
typedef interface IWinInetFileStream IWinInetFileStream;
#ifdef __cplusplus
interface IWinInetFileStream;
#endif /* __cplusplus */
#endif

#ifndef __IXMLElement_FWD_DEFINED__
#define __IXMLElement_FWD_DEFINED__
typedef interface IXMLElement IXMLElement;
#ifdef __cplusplus
interface IXMLElement;
#endif /* __cplusplus */
#endif


EXTERN_C const IID CLSID_SBS_StdURLMoniker;
EXTERN_C const IID CLSID_SBS_HttpProtocol;
EXTERN_C const IID CLSID_SBS_FtpProtocol;
EXTERN_C const IID CLSID_SBS_GopherProtocol;
EXTERN_C const IID CLSID_SBS_HttpSProtocol;
EXTERN_C const IID CLSID_SBS_FileProtocol;
EXTERN_C const IID CLSID_SBS_MkProtocol;
EXTERN_C const IID CLSID_SBS_UrlMkBindCtx;
EXTERN_C const IID CLSID_SBS_SoftDistExt;
EXTERN_C const IID CLSID_SBS_CdlProtocol;
EXTERN_C const IID CLSID_SBS_ClassInstallFilter;
EXTERN_C const IID CLSID_SBS_InternetSecurityManager;
EXTERN_C const IID CLSID_SBS_InternetZoneManager;

#define BINDF_DONTUSECACHE BINDF_GETNEWESTVERSION
#define BINDF_DONTPUTINCACHE BINDF_NOWRITECACHE
#define BINDF_NOCOPYDATA BINDF_PULLDATA
#define INVALID_P_ROOT_SECURITY_ID ((BYTE*)-1)

#define PI_DOCFILECLSIDLOOKUP PI_CLSIDLOOKUP

EXTERN_C const IID IID_IAsyncMoniker;
EXTERN_C const IID CLSID_StdURLMoniker;
EXTERN_C const IID CLSID_HttpProtocol;
EXTERN_C const IID CLSID_FtpProtocol;
EXTERN_C const IID CLSID_GopherProtocol;
EXTERN_C const IID CLSID_HttpSProtocol;
EXTERN_C const IID CLSID_FileProtocol;
EXTERN_C const IID CLSID_MkProtocol;
EXTERN_C const IID CLSID_StdURLProtocol;
EXTERN_C const IID CLSID_UrlMkBindCtx;
EXTERN_C const IID CLSID_CdlProtocol;
EXTERN_C const IID CLSID_ClassInstallFilter;
EXTERN_C const IID IID_IAsyncBindCtx;

#define SZ_URLCONTEXT           OLESTR("URL Context")
#define SZ_ASYNC_CALLEE         OLESTR("AsyncCallee")

#define MKSYS_URLMONIKER         6
#define URL_MK_LEGACY            0
#define URL_MK_UNIFORM           1
#define URL_MK_NO_CANONICALIZE   2

STDAPI CreateURLMoniker(LPMONIKER pMkCtx, LPCWSTR szURL, LPMONIKER *ppmk);
STDAPI CreateURLMonikerEx(LPMONIKER pMkCtx, LPCWSTR szURL, LPMONIKER *ppmk, DWORD dwFlags);
STDAPI GetClassURL(LPCWSTR szURL, CLSID *pClsID);
STDAPI CreateAsyncBindCtx(DWORD reserved, IBindStatusCallback *pBSCb, IEnumFORMATETC *pEFetc, IBindCtx **ppBC);
#if (_WIN32_IE >= _WIN32_IE_IE70)
STDAPI CreateURLMonikerEx2(LPMONIKER pMkCtx, IUri *pUri, LPMONIKER *ppmk, DWORD dwFlags);
#endif
STDAPI CreateAsyncBindCtxEx(IBindCtx *pbc, DWORD dwOptions, IBindStatusCallback *pBSCb, IEnumFORMATETC *pEnum, IBindCtx **ppBC, DWORD reserved);
STDAPI MkParseDisplayNameEx(IBindCtx *pbc, LPCWSTR szDisplayName, ULONG *pchEaten, LPMONIKER *ppmk);
STDAPI RegisterBindStatusCallback(LPBC pBC, IBindStatusCallback *pBSCb, IBindStatusCallback **ppBSCBPrev, DWORD dwReserved);
STDAPI RevokeBindStatusCallback(LPBC pBC, IBindStatusCallback *pBSCb);
STDAPI GetClassFileOrMime(LPBC pBC, LPCWSTR szFilename, LPVOID pBuffer, DWORD cbSize, LPCWSTR szMime, DWORD dwReserved, CLSID *pclsid);
STDAPI IsValidURL(LPBC pBC, LPCWSTR szURL, DWORD dwReserved);
STDAPI CoGetClassObjectFromURL(REFCLSID rCLASSID, LPCWSTR szCODE, DWORD dwFileVersionMS, DWORD dwFileVersionLS, LPCWSTR szTYPE, LPBINDCTX pBindCtx, DWORD dwClsContext, LPVOID pvReserved, REFIID riid, LPVOID *ppv);
STDAPI IEInstallScope(LPDWORD pdwScope);
STDAPI FaultInIEFeature(HWND hWnd, uCLSSPEC *pClassSpec, QUERYCONTEXT *pQuery, DWORD dwFlags);
STDAPI GetComponentIDFromCLSSPEC(uCLSSPEC *pClassspec, LPSTR *ppszComponentID);

#define FIEF_FLAG_FORCE_JITUI 0x1
#define FIEF_FLAG_PEEK 0x2
#define FIEF_FLAG_SKIP_INSTALLED_VERSION_CHECK 0x4

STDAPI IsAsyncMoniker(IMoniker *pmk);
STDAPI CreateURLBinding(LPCWSTR lpszUrl, IBindCtx *pbc, IBinding **ppBdg);
STDAPI RegisterMediaTypes(UINT ctypes, const LPCSTR *rgszTypes, CLIPFORMAT *rgcfTypes);
STDAPI FindMediaType(LPCSTR rgszTypes, CLIPFORMAT *rgcfTypes);
STDAPI CreateFormatEnumerator(UINT cfmtetc, FORMATETC *rgfmtetc, IEnumFORMATETC **ppenumfmtetc);
STDAPI RegisterFormatEnumerator(LPBC pBC, IEnumFORMATETC *pEFetc, DWORD reserved);
STDAPI RevokeFormatEnumerator(LPBC pBC, IEnumFORMATETC *pEFetc);
STDAPI RegisterMediaTypeClass(LPBC pBC,UINT ctypes, const LPCSTR *rgszTypes, CLSID *rgclsID, DWORD reserved);
STDAPI FindMediaTypeClass(LPBC pBC, LPCSTR szType, CLSID *pclsID, DWORD reserved);
STDAPI UrlMkSetSessionOption(DWORD dwOption, LPVOID pBuffer, DWORD dwBufferLength, DWORD dwReserved);
STDAPI UrlMkGetSessionOption(DWORD dwOption, LPVOID pBuffer, DWORD dwBufferLength, DWORD *pdwBufferLengthOut, DWORD dwReserved);
STDAPI FindMimeFromData(LPBC pBC, LPCWSTR pwzUrl, LPVOID pBuffer, DWORD cbSize, LPCWSTR pwzMimeProposed, DWORD dwMimeFlags, LPWSTR *ppwzMimeOut, DWORD dwReserved);

#define FMFD_DEFAULT 0x0
#define FMFD_URLASFILENAME 0x1
#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#define FMFD_ENABLEMIMESNIFFING 0x2
#define FMFD_IGNOREMIMETEXTPLAIN 0x4
#endif

#define FMFD_SERVERMIME 0x8
#define FMFD_RESPECTTEXTPLAIN 0x10
#define FMFD_RETURNUPDATEDIMGMIMES 0x20
#define UAS_EXACTLEGACY 0x1000

STDAPI ObtainUserAgentString(DWORD dwOption, LPSTR pszUAOut, DWORD *cbSize);
STDAPI CompareSecurityIds(BYTE *pbSecurityId1, DWORD dwLen1, BYTE *pbSecurityId2, DWORD dwLen2, DWORD dwReserved);
STDAPI CompatFlagsFromClsid(CLSID *pclsid, LPDWORD pdwCompatFlags, LPDWORD pdwMiscStatusFlags);

#define URLMON_OPTION_USERAGENT 0x10000001
#define URLMON_OPTION_USERAGENT_REFRESH 0x10000002
#define URLMON_OPTION_URL_ENCODING 0x10000004
#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#define URLMON_OPTION_USE_BINDSTRINGCREDS 0x10000008
#endif
#if (_WIN32_IE >= _WIN32_IE_IE70)
#define URLMON_OPTION_USE_BROWSERAPPSDOCUMENTS 0x10000010
#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#define CF_NULL                 0
#define CFSTR_MIME_NULL         NULL

#define CFSTR_MIME_TEXT (TEXT("text/plain"))
#define CFSTR_MIME_RICHTEXT (TEXT("text/richtext"))
#define CFSTR_MIME_MANIFEST (TEXT("text/cache-manifest"))
#define CFSTR_MIME_WEBVTT (TEXT("text/vtt"))
#define CFSTR_MIME_X_BITMAP (TEXT("image/x-xbitmap"))
#define CFSTR_MIME_POSTSCRIPT (TEXT("application/postscript"))
#define CFSTR_MIME_AIFF (TEXT("audio/aiff"))
#define CFSTR_MIME_BASICAUDIO (TEXT("audio/basic"))
#define CFSTR_MIME_WAV (TEXT("audio/wav"))
#define CFSTR_MIME_X_WAV (TEXT("audio/x-wav"))
#define CFSTR_MIME_GIF (TEXT("image/gif"))
#define CFSTR_MIME_PJPEG (TEXT("image/pjpeg"))
#define CFSTR_MIME_JPEG (TEXT("image/jpeg"))
#define CFSTR_MIME_TIFF (TEXT("image/tiff"))
#define CFSTR_MIME_JPEG_XR (TEXT("image/vnd.ms-photo"))
#define CFSTR_MIME_PNG (TEXT("image/png"))
#define CFSTR_MIME_X_PNG (TEXT("image/x-png"))
#define CFSTR_MIME_X_ICON (TEXT("image/x-icon"))
#define CFSTR_MIME_SVG_XML (TEXT("image/svg+xml"))
#define CFSTR_MIME_BMP (TEXT("image/bmp"))
#define CFSTR_MIME_X_EMF (TEXT("image/x-emf"))
#define CFSTR_MIME_X_WMF (TEXT("image/x-wmf"))
#define CFSTR_MIME_AVI (TEXT("video/avi"))
#define CFSTR_MIME_MPEG (TEXT("video/mpeg"))
#define CFSTR_MIME_FRACTALS (TEXT("application/fractals"))
#define CFSTR_MIME_RAWDATA (TEXT("application/octet-stream"))
#define CFSTR_MIME_RAWDATASTRM (TEXT("application/octet-stream"))
#define CFSTR_MIME_PDF (TEXT("application/pdf"))
#define CFSTR_MIME_HTA (TEXT("application/hta"))
#define CFSTR_MIME_APP_XML (TEXT("application/xml"))
#define CFSTR_MIME_XHTML (TEXT("application/xhtml+xml"))
#define CFSTR_MIME_X_AIFF (TEXT("audio/x-aiff"))
#define CFSTR_MIME_X_REALAUDIO (TEXT("audio/x-pn-realaudio"))
#define CFSTR_MIME_XBM (TEXT("image/xbm"))
#define CFSTR_MIME_QUICKTIME (TEXT("video/quicktime"))
#define CFSTR_MIME_X_MSVIDEO (TEXT("video/x-msvideo"))
#define CFSTR_MIME_X_SGI_MOVIE (TEXT("video/x-sgi-movie"))
#define CFSTR_MIME_HTML (TEXT("text/html"))
#define CFSTR_MIME_XML (TEXT("text/xml"))
#define CFSTR_MIME_TTML (TEXT("application/ttml+xml"))
#define CFSTR_MIME_TTAF (TEXT("application/ttaf+xml"))

#define MK_S_ASYNCHRONOUS    _HRESULT_TYPEDEF_(0x401E8L)
#ifndef S_ASYNCHRONOUS
#define S_ASYNCHRONOUS MK_S_ASYNCHRONOUS
#endif

#ifndef E_PENDING
#define E_PENDING _HRESULT_TYPEDEF_(__MSABI_LONG(0x8000000a))
#endif
#define INET_E_INVALID_URL _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0002))
#define INET_E_NO_SESSION _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0003))
#define INET_E_CANNOT_CONNECT            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0004))
#define INET_E_RESOURCE_NOT_FOUND        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0005))
#define INET_E_OBJECT_NOT_FOUND          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0006))
#define INET_E_DATA_NOT_AVAILABLE        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0007))
#define INET_E_DOWNLOAD_FAILURE          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0008))
#define INET_E_AUTHENTICATION_REQUIRED   _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0009))
#define INET_E_NO_VALID_MEDIA            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000A))
#define INET_E_CONNECTION_TIMEOUT        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000B))
#define INET_E_INVALID_REQUEST           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000C))
#define INET_E_UNKNOWN_PROTOCOL          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000D))
#define INET_E_SECURITY_PROBLEM          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000E))
#define INET_E_CANNOT_LOAD_DATA          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C000F))
#define INET_E_CANNOT_INSTANTIATE_OBJECT _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0010))
#define INET_E_INVALID_CERTIFICATE       _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0019))
#define INET_E_REDIRECT_FAILED           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0014))
#define INET_E_REDIRECT_TO_DIR           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0015))
#define INET_E_CANNOT_LOCK_REQUEST                   _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0016))
#define INET_E_USE_EXTEND_BINDING                    _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0017))
#define INET_E_TERMINATED_BIND                       _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0018))
#define INET_E_RESERVED_1                            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C001A))
#define INET_E_BLOCKED_REDIRECT_XSECURITYID          _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C001B))
#define INET_E_DOMINJECTIONVALIDATION                _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C001C))
#define INET_E_ERROR_FIRST                           _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0002))
#define INET_E_CODE_DOWNLOAD_DECLINED                _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0100))
#define INET_E_RESULT_DISPATCHED                     _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0200))
#define INET_E_CANNOT_REPLACE_SFP_FILE               _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0300))
#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#define INET_E_CODE_INSTALL_SUPPRESSED               _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0400))
#endif
#define INET_E_CODE_INSTALL_BLOCKED_BY_HASH_POLICY   _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0500))
#define INET_E_DOWNLOAD_BLOCKED_BY_INPRIVATE         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0501))
#define INET_E_CODE_INSTALL_BLOCKED_IMMERSIVE        _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0502))
#define INET_E_FORBIDFRAMING                         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0503))
#define INET_E_CODE_INSTALL_BLOCKED_ARM              _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0504))
#define INET_E_BLOCKED_PLUGGABLE_PROTOCOL            _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0505))
#define INET_E_ERROR_LAST INET_E_BLOCKED_PLUGGABLE_PROTOCOL
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef _LPPERSISTMONIKER_DEFINED
#define _LPPERSISTMONIKER_DEFINED
/*****************************************************************************
 * IPersistMoniker interface
 */
#ifndef __IPersistMoniker_INTERFACE_DEFINED__
#define __IPersistMoniker_INTERFACE_DEFINED__

typedef IPersistMoniker *LPPERSISTMONIKER;

DEFINE_GUID(IID_IPersistMoniker, 0x79eac9c9, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9c9-baf9-11ce-8c82-00aa004ba90b")
IPersistMoniker : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClassID(
        CLSID *pClassID) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        WINBOOL fFullyAvailable,
        IMoniker *pimkName,
        LPBC pibc,
        DWORD grfMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        IMoniker *pimkName,
        LPBC pbc,
        WINBOOL fRemember) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveCompleted(
        IMoniker *pimkName,
        LPBC pibc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurMoniker(
        IMoniker **ppimkName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistMoniker, 0x79eac9c9, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IPersistMonikerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistMoniker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistMoniker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistMoniker *This);

    /*** IPersistMoniker methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistMoniker *This,
        CLSID *pClassID);

    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IPersistMoniker *This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistMoniker *This,
        WINBOOL fFullyAvailable,
        IMoniker *pimkName,
        LPBC pibc,
        DWORD grfMode);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistMoniker *This,
        IMoniker *pimkName,
        LPBC pbc,
        WINBOOL fRemember);

    HRESULT (STDMETHODCALLTYPE *SaveCompleted)(
        IPersistMoniker *This,
        IMoniker *pimkName,
        LPBC pibc);

    HRESULT (STDMETHODCALLTYPE *GetCurMoniker)(
        IPersistMoniker *This,
        IMoniker **ppimkName);

    END_INTERFACE
} IPersistMonikerVtbl;

interface IPersistMoniker {
    CONST_VTBL IPersistMonikerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistMoniker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistMoniker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistMoniker_Release(This) (This)->lpVtbl->Release(This)
/*** IPersistMoniker methods ***/
#define IPersistMoniker_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
#define IPersistMoniker_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IPersistMoniker_Load(This,fFullyAvailable,pimkName,pibc,grfMode) (This)->lpVtbl->Load(This,fFullyAvailable,pimkName,pibc,grfMode)
#define IPersistMoniker_Save(This,pimkName,pbc,fRemember) (This)->lpVtbl->Save(This,pimkName,pbc,fRemember)
#define IPersistMoniker_SaveCompleted(This,pimkName,pibc) (This)->lpVtbl->SaveCompleted(This,pimkName,pibc)
#define IPersistMoniker_GetCurMoniker(This,ppimkName) (This)->lpVtbl->GetCurMoniker(This,ppimkName)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistMoniker_QueryInterface(IPersistMoniker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistMoniker_AddRef(IPersistMoniker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistMoniker_Release(IPersistMoniker* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersistMoniker methods ***/
static inline HRESULT IPersistMoniker_GetClassID(IPersistMoniker* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
static inline HRESULT IPersistMoniker_IsDirty(IPersistMoniker* This) {
    return This->lpVtbl->IsDirty(This);
}
static inline HRESULT IPersistMoniker_Load(IPersistMoniker* This,WINBOOL fFullyAvailable,IMoniker *pimkName,LPBC pibc,DWORD grfMode) {
    return This->lpVtbl->Load(This,fFullyAvailable,pimkName,pibc,grfMode);
}
static inline HRESULT IPersistMoniker_Save(IPersistMoniker* This,IMoniker *pimkName,LPBC pbc,WINBOOL fRemember) {
    return This->lpVtbl->Save(This,pimkName,pbc,fRemember);
}
static inline HRESULT IPersistMoniker_SaveCompleted(IPersistMoniker* This,IMoniker *pimkName,LPBC pibc) {
    return This->lpVtbl->SaveCompleted(This,pimkName,pibc);
}
static inline HRESULT IPersistMoniker_GetCurMoniker(IPersistMoniker* This,IMoniker **ppimkName) {
    return This->lpVtbl->GetCurMoniker(This,ppimkName);
}
#endif
#endif

#endif


#endif  /* __IPersistMoniker_INTERFACE_DEFINED__ */

#endif

#ifndef _LPMONIKERPROP_DEFINED
#define _LPMONIKERPROP_DEFINED
/*****************************************************************************
 * IMonikerProp interface
 */
#ifndef __IMonikerProp_INTERFACE_DEFINED__
#define __IMonikerProp_INTERFACE_DEFINED__

typedef IMonikerProp *LPMONIKERPROP;

typedef enum __WIDL_urlmon_generated_name_0000000F {
    MIMETYPEPROP = 0x0,
    USE_SRC_URL = 0x1,
    CLASSIDPROP = 0x2,
    TRUSTEDDOWNLOADPROP = 0x3,
    POPUPLEVELPROP = 0x4
} MONIKERPROPERTY;

DEFINE_GUID(IID_IMonikerProp, 0xa5ca5f7f, 0x1847, 0x4d87, 0x9c,0x5b, 0x91,0x85,0x09,0xf7,0x51,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a5ca5f7f-1847-4d87-9c5b-918509f7511d")
IMonikerProp : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PutProperty(
        MONIKERPROPERTY mkp,
        LPCWSTR val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMonikerProp, 0xa5ca5f7f, 0x1847, 0x4d87, 0x9c,0x5b, 0x91,0x85,0x09,0xf7,0x51,0x1d)
#endif
#else
typedef struct IMonikerPropVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMonikerProp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMonikerProp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMonikerProp *This);

    /*** IMonikerProp methods ***/
    HRESULT (STDMETHODCALLTYPE *PutProperty)(
        IMonikerProp *This,
        MONIKERPROPERTY mkp,
        LPCWSTR val);

    END_INTERFACE
} IMonikerPropVtbl;

interface IMonikerProp {
    CONST_VTBL IMonikerPropVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMonikerProp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMonikerProp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMonikerProp_Release(This) (This)->lpVtbl->Release(This)
/*** IMonikerProp methods ***/
#define IMonikerProp_PutProperty(This,mkp,val) (This)->lpVtbl->PutProperty(This,mkp,val)
#else
/*** IUnknown methods ***/
static inline HRESULT IMonikerProp_QueryInterface(IMonikerProp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMonikerProp_AddRef(IMonikerProp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMonikerProp_Release(IMonikerProp* This) {
    return This->lpVtbl->Release(This);
}
/*** IMonikerProp methods ***/
static inline HRESULT IMonikerProp_PutProperty(IMonikerProp* This,MONIKERPROPERTY mkp,LPCWSTR val) {
    return This->lpVtbl->PutProperty(This,mkp,val);
}
#endif
#endif

#endif


#endif  /* __IMonikerProp_INTERFACE_DEFINED__ */

#endif

#ifndef _LPBINDPROTOCOL_DEFINED
#define _LPBINDPROTOCOL_DEFINED

/*****************************************************************************
 * IBindProtocol interface
 */
#ifndef __IBindProtocol_INTERFACE_DEFINED__
#define __IBindProtocol_INTERFACE_DEFINED__

typedef IBindProtocol *LPBINDPROTOCOL;

DEFINE_GUID(IID_IBindProtocol, 0x79eac9cd, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9cd-baf9-11ce-8c82-00aa004ba90b")
IBindProtocol : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateBinding(
        LPCWSTR szUrl,
        IBindCtx *pbc,
        IBinding **ppb) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBindProtocol, 0x79eac9cd, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IBindProtocolVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBindProtocol *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBindProtocol *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBindProtocol *This);

    /*** IBindProtocol methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBinding)(
        IBindProtocol *This,
        LPCWSTR szUrl,
        IBindCtx *pbc,
        IBinding **ppb);

    END_INTERFACE
} IBindProtocolVtbl;

interface IBindProtocol {
    CONST_VTBL IBindProtocolVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBindProtocol_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBindProtocol_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBindProtocol_Release(This) (This)->lpVtbl->Release(This)
/*** IBindProtocol methods ***/
#define IBindProtocol_CreateBinding(This,szUrl,pbc,ppb) (This)->lpVtbl->CreateBinding(This,szUrl,pbc,ppb)
#else
/*** IUnknown methods ***/
static inline HRESULT IBindProtocol_QueryInterface(IBindProtocol* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBindProtocol_AddRef(IBindProtocol* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBindProtocol_Release(IBindProtocol* This) {
    return This->lpVtbl->Release(This);
}
/*** IBindProtocol methods ***/
static inline HRESULT IBindProtocol_CreateBinding(IBindProtocol* This,LPCWSTR szUrl,IBindCtx *pbc,IBinding **ppb) {
    return This->lpVtbl->CreateBinding(This,szUrl,pbc,ppb);
}
#endif
#endif

#endif


#endif  /* __IBindProtocol_INTERFACE_DEFINED__ */

#endif

#ifndef _LPBINDING_DEFINED
#define _LPBINDING_DEFINED

/*****************************************************************************
 * IBinding interface
 */
#ifndef __IBinding_INTERFACE_DEFINED__
#define __IBinding_INTERFACE_DEFINED__

typedef IBinding *LPBINDING;

DEFINE_GUID(IID_IBinding, 0x79eac9c0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9c0-baf9-11ce-8c82-00aa004ba90b")
IBinding : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Abort(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Suspend(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPriority(
        LONG nPriority) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPriority(
        LONG *pnPriority) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBindResult(
        CLSID *pclsidProtocol,
        DWORD *pdwResult,
        LPOLESTR *pszResult,
        DWORD *pdwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBinding, 0x79eac9c0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IBindingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBinding *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBinding *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBinding *This);

    /*** IBinding methods ***/
    HRESULT (STDMETHODCALLTYPE *Abort)(
        IBinding *This);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IBinding *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IBinding *This);

    HRESULT (STDMETHODCALLTYPE *SetPriority)(
        IBinding *This,
        LONG nPriority);

    HRESULT (STDMETHODCALLTYPE *GetPriority)(
        IBinding *This,
        LONG *pnPriority);

    HRESULT (STDMETHODCALLTYPE *GetBindResult)(
        IBinding *This,
        CLSID *pclsidProtocol,
        DWORD *pdwResult,
        LPOLESTR *pszResult,
        DWORD *pdwReserved);

    END_INTERFACE
} IBindingVtbl;

interface IBinding {
    CONST_VTBL IBindingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBinding_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBinding_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBinding_Release(This) (This)->lpVtbl->Release(This)
/*** IBinding methods ***/
#define IBinding_Abort(This) (This)->lpVtbl->Abort(This)
#define IBinding_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IBinding_Resume(This) (This)->lpVtbl->Resume(This)
#define IBinding_SetPriority(This,nPriority) (This)->lpVtbl->SetPriority(This,nPriority)
#define IBinding_GetPriority(This,pnPriority) (This)->lpVtbl->GetPriority(This,pnPriority)
#define IBinding_GetBindResult(This,pclsidProtocol,pdwResult,pszResult,pdwReserved) (This)->lpVtbl->GetBindResult(This,pclsidProtocol,pdwResult,pszResult,pdwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IBinding_QueryInterface(IBinding* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBinding_AddRef(IBinding* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBinding_Release(IBinding* This) {
    return This->lpVtbl->Release(This);
}
/*** IBinding methods ***/
static inline HRESULT IBinding_Abort(IBinding* This) {
    return This->lpVtbl->Abort(This);
}
static inline HRESULT IBinding_Suspend(IBinding* This) {
    return This->lpVtbl->Suspend(This);
}
static inline HRESULT IBinding_Resume(IBinding* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IBinding_SetPriority(IBinding* This,LONG nPriority) {
    return This->lpVtbl->SetPriority(This,nPriority);
}
static inline HRESULT IBinding_GetPriority(IBinding* This,LONG *pnPriority) {
    return This->lpVtbl->GetPriority(This,pnPriority);
}
static inline HRESULT IBinding_GetBindResult(IBinding* This,CLSID *pclsidProtocol,DWORD *pdwResult,LPOLESTR *pszResult,DWORD *pdwReserved) {
    return This->lpVtbl->GetBindResult(This,pclsidProtocol,pdwResult,pszResult,pdwReserved);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IBinding_RemoteGetBindResult_Proxy(
    IBinding* This,
    CLSID *pclsidProtocol,
    DWORD *pdwResult,
    LPOLESTR *pszResult,
    DWORD dwReserved);
void __RPC_STUB IBinding_RemoteGetBindResult_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IBinding_GetBindResult_Proxy(
    IBinding* This,
    CLSID *pclsidProtocol,
    DWORD *pdwResult,
    LPOLESTR *pszResult,
    DWORD *pdwReserved);
HRESULT __RPC_STUB IBinding_GetBindResult_Stub(
    IBinding* This,
    CLSID *pclsidProtocol,
    DWORD *pdwResult,
    LPOLESTR *pszResult,
    DWORD dwReserved);

#endif  /* __IBinding_INTERFACE_DEFINED__ */

#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#ifndef _LPBINDSTATUSCALLBACK_DEFINED
#define _LPBINDSTATUSCALLBACK_DEFINED

/*****************************************************************************
 * IBindStatusCallback interface
 */
#ifndef __IBindStatusCallback_INTERFACE_DEFINED__
#define __IBindStatusCallback_INTERFACE_DEFINED__

typedef IBindStatusCallback *LPBINDSTATUSCALLBACK;

typedef enum __WIDL_urlmon_generated_name_00000010 {
    BINDVERB_GET = 0x0,
    BINDVERB_POST = 0x1,
    BINDVERB_PUT = 0x2,
    BINDVERB_CUSTOM = 0x3,
    BINDVERB_RESERVED1 = 0x4
} BINDVERB;

typedef enum __WIDL_urlmon_generated_name_00000011 {
    BINDINFOF_URLENCODESTGMEDDATA = 0x1,
    BINDINFOF_URLENCODEDEXTRAINFO = 0x2
} BINDINFOF;

typedef enum __WIDL_urlmon_generated_name_00000012 {
    BINDF_ASYNCHRONOUS = 0x1,
    BINDF_ASYNCSTORAGE = 0x2,
    BINDF_NOPROGRESSIVERENDERING = 0x4,
    BINDF_OFFLINEOPERATION = 0x8,
    BINDF_GETNEWESTVERSION = 0x10,
    BINDF_NOWRITECACHE = 0x20,
    BINDF_NEEDFILE = 0x40,
    BINDF_PULLDATA = 0x80,
    BINDF_IGNORESECURITYPROBLEM = 0x100,
    BINDF_RESYNCHRONIZE = 0x200,
    BINDF_HYPERLINK = 0x400,
    BINDF_NO_UI = 0x800,
    BINDF_SILENTOPERATION = 0x1000,
    BINDF_PRAGMA_NO_CACHE = 0x2000,
    BINDF_GETCLASSOBJECT = 0x4000,
    BINDF_RESERVED_1 = 0x8000,
    BINDF_FREE_THREADED = 0x10000,
    BINDF_DIRECT_READ = 0x20000,
    BINDF_FORMS_SUBMIT = 0x40000,
    BINDF_GETFROMCACHE_IF_NET_FAIL = 0x80000,
    BINDF_FROMURLMON = 0x100000,
    BINDF_FWD_BACK = 0x200000,
    BINDF_PREFERDEFAULTHANDLER = 0x400000,
    BINDF_ENFORCERESTRICTED = 0x800000,
    BINDF_RESERVED_2 = 0x80000000,
    BINDF_RESERVED_3 = 0x1000000,
    BINDF_RESERVED_4 = 0x2000000,
    BINDF_RESERVED_5 = 0x4000000,
    BINDF_RESERVED_6 = 0x8000000,
    BINDF_RESERVED_7 = 0x40000000,
    BINDF_RESERVED_8 = 0x20000000
} BINDF;

typedef enum __WIDL_urlmon_generated_name_00000013 {
    URL_ENCODING_NONE = 0x0,
    URL_ENCODING_ENABLE_UTF8 = 0x10000000,
    URL_ENCODING_DISABLE_UTF8 = 0x20000000
} URL_ENCODING;

typedef struct _tagBINDINFO {
    ULONG cbSize;
    LPWSTR szExtraInfo;
    STGMEDIUM stgmedData;
    DWORD grfBindInfoF;
    DWORD dwBindVerb;
    LPWSTR szCustomVerb;
    DWORD cbstgmedData;
    DWORD dwOptions;
    DWORD dwOptionsFlags;
    DWORD dwCodePage;
    SECURITY_ATTRIBUTES securityAttributes;
    IID iid;
    IUnknown *pUnk;
    DWORD dwReserved;
} BINDINFO;

typedef struct _REMSECURITY_ATTRIBUTES {
    DWORD nLength;
    DWORD lpSecurityDescriptor;
    WINBOOL bInheritHandle;
} REMSECURITY_ATTRIBUTES;
typedef struct _REMSECURITY_ATTRIBUTES *PREMSECURITY_ATTRIBUTES;
typedef struct _REMSECURITY_ATTRIBUTES *LPREMSECURITY_ATTRIBUTES;

typedef struct _tagRemBINDINFO {
    ULONG cbSize;
    LPWSTR szExtraInfo;
    DWORD grfBindInfoF;
    DWORD dwBindVerb;
    LPWSTR szCustomVerb;
    DWORD cbstgmedData;
    DWORD dwOptions;
    DWORD dwOptionsFlags;
    DWORD dwCodePage;
    REMSECURITY_ATTRIBUTES securityAttributes;
    IID iid;
    IUnknown *pUnk;
    DWORD dwReserved;
} RemBINDINFO;

typedef struct tagRemFORMATETC {
    DWORD cfFormat;
    DWORD ptd;
    DWORD dwAspect;
    LONG lindex;
    DWORD tymed;
} RemFORMATETC;
typedef struct tagRemFORMATETC *LPREMFORMATETC;

typedef enum __WIDL_urlmon_generated_name_00000014 {
    BINDINFO_OPTIONS_WININETFLAG = 0x10000,
    BINDINFO_OPTIONS_ENABLE_UTF8 = 0x20000,
    BINDINFO_OPTIONS_DISABLE_UTF8 = 0x40000,
    BINDINFO_OPTIONS_USE_IE_ENCODING = 0x80000,
    BINDINFO_OPTIONS_BINDTOOBJECT = 0x100000,
    BINDINFO_OPTIONS_SECURITYOPTOUT = 0x200000,
    BINDINFO_OPTIONS_IGNOREMIMETEXTPLAIN = 0x400000,
    BINDINFO_OPTIONS_USEBINDSTRINGCREDS = 0x800000,
    BINDINFO_OPTIONS_IGNOREHTTPHTTPSREDIRECTS = 0x1000000,
    BINDINFO_OPTIONS_IGNORE_SSLERRORS_ONCE = 0x2000000,
    BINDINFO_WPC_DOWNLOADBLOCKED = 0x8000000,
    BINDINFO_WPC_LOGGING_ENABLED = 0x10000000,
    BINDINFO_OPTIONS_ALLOWCONNECTDATA = 0x20000000,
    BINDINFO_OPTIONS_DISABLEAUTOREDIRECTS = 0x40000000,
    BINDINFO_OPTIONS_SHDOCVW_NAVIGATE = (int)0x80000000
} BINDINFO_OPTIONS;

typedef enum __WIDL_urlmon_generated_name_00000015 {
    BSCF_FIRSTDATANOTIFICATION = 0x1,
    BSCF_INTERMEDIATEDATANOTIFICATION = 0x2,
    BSCF_LASTDATANOTIFICATION = 0x4,
    BSCF_DATAFULLYAVAILABLE = 0x8,
    BSCF_AVAILABLEDATASIZEUNKNOWN = 0x10,
    BSCF_SKIPDRAINDATAFORFILEURLS = 0x20,
    BSCF_64BITLENGTHDOWNLOAD = 0x40
} BSCF;

typedef enum tagBINDSTATUS {
    BINDSTATUS_FINDINGRESOURCE = 1,
    BINDSTATUS_CONNECTING = 2,
    BINDSTATUS_REDIRECTING = 3,
    BINDSTATUS_BEGINDOWNLOADDATA = 4,
    BINDSTATUS_DOWNLOADINGDATA = 5,
    BINDSTATUS_ENDDOWNLOADDATA = 6,
    BINDSTATUS_BEGINDOWNLOADCOMPONENTS = 7,
    BINDSTATUS_INSTALLINGCOMPONENTS = 8,
    BINDSTATUS_ENDDOWNLOADCOMPONENTS = 9,
    BINDSTATUS_USINGCACHEDCOPY = 10,
    BINDSTATUS_SENDINGREQUEST = 11,
    BINDSTATUS_CLASSIDAVAILABLE = 12,
    BINDSTATUS_MIMETYPEAVAILABLE = 13,
    BINDSTATUS_CACHEFILENAMEAVAILABLE = 14,
    BINDSTATUS_BEGINSYNCOPERATION = 15,
    BINDSTATUS_ENDSYNCOPERATION = 16,
    BINDSTATUS_BEGINUPLOADDATA = 17,
    BINDSTATUS_UPLOADINGDATA = 18,
    BINDSTATUS_ENDUPLOADDATA = 19,
    BINDSTATUS_PROTOCOLCLASSID = 20,
    BINDSTATUS_ENCODING = 21,
    BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE = 22,
    BINDSTATUS_CLASSINSTALLLOCATION = 23,
    BINDSTATUS_DECODING = 24,
    BINDSTATUS_LOADINGMIMEHANDLER = 25,
    BINDSTATUS_CONTENTDISPOSITIONATTACH = 26,
    BINDSTATUS_FILTERREPORTMIMETYPE = 27,
    BINDSTATUS_CLSIDCANINSTANTIATE = 28,
    BINDSTATUS_IUNKNOWNAVAILABLE = 29,
    BINDSTATUS_DIRECTBIND = 30,
    BINDSTATUS_RAWMIMETYPE = 31,
    BINDSTATUS_PROXYDETECTING = 32,
    BINDSTATUS_ACCEPTRANGES = 33,
    BINDSTATUS_COOKIE_SENT = 34,
    BINDSTATUS_COMPACT_POLICY_RECEIVED = 35,
    BINDSTATUS_COOKIE_SUPPRESSED = 36,
    BINDSTATUS_COOKIE_STATE_UNKNOWN = 37,
    BINDSTATUS_COOKIE_STATE_ACCEPT = 38,
    BINDSTATUS_COOKIE_STATE_REJECT = 39,
    BINDSTATUS_COOKIE_STATE_PROMPT = 40,
    BINDSTATUS_COOKIE_STATE_LEASH = 41,
    BINDSTATUS_COOKIE_STATE_DOWNGRADE = 42,
    BINDSTATUS_POLICY_HREF = 43,
    BINDSTATUS_P3P_HEADER = 44,
    BINDSTATUS_SESSION_COOKIE_RECEIVED = 45,
    BINDSTATUS_PERSISTENT_COOKIE_RECEIVED = 46,
    BINDSTATUS_SESSION_COOKIES_ALLOWED = 47,
    BINDSTATUS_CACHECONTROL = 48,
    BINDSTATUS_CONTENTDISPOSITIONFILENAME = 49,
    BINDSTATUS_MIMETEXTPLAINMISMATCH = 50,
    BINDSTATUS_PUBLISHERAVAILABLE = 51,
    BINDSTATUS_DISPLAYNAMEAVAILABLE = 52,
    BINDSTATUS_SSLUX_NAVBLOCKED = 53,
    BINDSTATUS_SERVER_MIMETYPEAVAILABLE = 54,
    BINDSTATUS_SNIFFED_CLASSIDAVAILABLE = 55,
    BINDSTATUS_64BIT_PROGRESS = 56,
    BINDSTATUS_LAST = BINDSTATUS_64BIT_PROGRESS,
    BINDSTATUS_RESERVED_0 = 57,
    BINDSTATUS_RESERVED_1 = 58,
    BINDSTATUS_RESERVED_2 = 59,
    BINDSTATUS_RESERVED_3 = 60,
    BINDSTATUS_RESERVED_4 = 61,
    BINDSTATUS_RESERVED_5 = 62,
    BINDSTATUS_RESERVED_6 = 63,
    BINDSTATUS_RESERVED_7 = 64,
    BINDSTATUS_RESERVED_8 = 65,
    BINDSTATUS_RESERVED_9 = 66,
    BINDSTATUS_LAST_PRIVATE = BINDSTATUS_RESERVED_9
} BINDSTATUS;

DEFINE_GUID(IID_IBindStatusCallback, 0x79eac9c1, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9c1-baf9-11ce-8c82-00aa004ba90b")
IBindStatusCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStartBinding(
        DWORD dwReserved,
        IBinding *pib) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPriority(
        LONG *pnPriority) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnLowResource(
        DWORD reserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnProgress(
        ULONG ulProgress,
        ULONG ulProgressMax,
        ULONG ulStatusCode,
        LPCWSTR szStatusText) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStopBinding(
        HRESULT hresult,
        LPCWSTR szError) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBindInfo(
        DWORD *grfBINDF,
        BINDINFO *pbindinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDataAvailable(
        DWORD grfBSCF,
        DWORD dwSize,
        FORMATETC *pformatetc,
        STGMEDIUM *pstgmed) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnObjectAvailable(
        REFIID riid,
        IUnknown *punk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBindStatusCallback, 0x79eac9c1, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IBindStatusCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBindStatusCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBindStatusCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBindStatusCallback *This);

    /*** IBindStatusCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStartBinding)(
        IBindStatusCallback *This,
        DWORD dwReserved,
        IBinding *pib);

    HRESULT (STDMETHODCALLTYPE *GetPriority)(
        IBindStatusCallback *This,
        LONG *pnPriority);

    HRESULT (STDMETHODCALLTYPE *OnLowResource)(
        IBindStatusCallback *This,
        DWORD reserved);

    HRESULT (STDMETHODCALLTYPE *OnProgress)(
        IBindStatusCallback *This,
        ULONG ulProgress,
        ULONG ulProgressMax,
        ULONG ulStatusCode,
        LPCWSTR szStatusText);

    HRESULT (STDMETHODCALLTYPE *OnStopBinding)(
        IBindStatusCallback *This,
        HRESULT hresult,
        LPCWSTR szError);

    HRESULT (STDMETHODCALLTYPE *GetBindInfo)(
        IBindStatusCallback *This,
        DWORD *grfBINDF,
        BINDINFO *pbindinfo);

    HRESULT (STDMETHODCALLTYPE *OnDataAvailable)(
        IBindStatusCallback *This,
        DWORD grfBSCF,
        DWORD dwSize,
        FORMATETC *pformatetc,
        STGMEDIUM *pstgmed);

    HRESULT (STDMETHODCALLTYPE *OnObjectAvailable)(
        IBindStatusCallback *This,
        REFIID riid,
        IUnknown *punk);

    END_INTERFACE
} IBindStatusCallbackVtbl;

interface IBindStatusCallback {
    CONST_VTBL IBindStatusCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBindStatusCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBindStatusCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBindStatusCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IBindStatusCallback methods ***/
#define IBindStatusCallback_OnStartBinding(This,dwReserved,pib) (This)->lpVtbl->OnStartBinding(This,dwReserved,pib)
#define IBindStatusCallback_GetPriority(This,pnPriority) (This)->lpVtbl->GetPriority(This,pnPriority)
#define IBindStatusCallback_OnLowResource(This,reserved) (This)->lpVtbl->OnLowResource(This,reserved)
#define IBindStatusCallback_OnProgress(This,ulProgress,ulProgressMax,ulStatusCode,szStatusText) (This)->lpVtbl->OnProgress(This,ulProgress,ulProgressMax,ulStatusCode,szStatusText)
#define IBindStatusCallback_OnStopBinding(This,hresult,szError) (This)->lpVtbl->OnStopBinding(This,hresult,szError)
#define IBindStatusCallback_GetBindInfo(This,grfBINDF,pbindinfo) (This)->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo)
#define IBindStatusCallback_OnDataAvailable(This,grfBSCF,dwSize,pformatetc,pstgmed) (This)->lpVtbl->OnDataAvailable(This,grfBSCF,dwSize,pformatetc,pstgmed)
#define IBindStatusCallback_OnObjectAvailable(This,riid,punk) (This)->lpVtbl->OnObjectAvailable(This,riid,punk)
#else
/*** IUnknown methods ***/
static inline HRESULT IBindStatusCallback_QueryInterface(IBindStatusCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBindStatusCallback_AddRef(IBindStatusCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBindStatusCallback_Release(IBindStatusCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IBindStatusCallback methods ***/
static inline HRESULT IBindStatusCallback_OnStartBinding(IBindStatusCallback* This,DWORD dwReserved,IBinding *pib) {
    return This->lpVtbl->OnStartBinding(This,dwReserved,pib);
}
static inline HRESULT IBindStatusCallback_GetPriority(IBindStatusCallback* This,LONG *pnPriority) {
    return This->lpVtbl->GetPriority(This,pnPriority);
}
static inline HRESULT IBindStatusCallback_OnLowResource(IBindStatusCallback* This,DWORD reserved) {
    return This->lpVtbl->OnLowResource(This,reserved);
}
static inline HRESULT IBindStatusCallback_OnProgress(IBindStatusCallback* This,ULONG ulProgress,ULONG ulProgressMax,ULONG ulStatusCode,LPCWSTR szStatusText) {
    return This->lpVtbl->OnProgress(This,ulProgress,ulProgressMax,ulStatusCode,szStatusText);
}
static inline HRESULT IBindStatusCallback_OnStopBinding(IBindStatusCallback* This,HRESULT hresult,LPCWSTR szError) {
    return This->lpVtbl->OnStopBinding(This,hresult,szError);
}
static inline HRESULT IBindStatusCallback_GetBindInfo(IBindStatusCallback* This,DWORD *grfBINDF,BINDINFO *pbindinfo) {
    return This->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo);
}
static inline HRESULT IBindStatusCallback_OnDataAvailable(IBindStatusCallback* This,DWORD grfBSCF,DWORD dwSize,FORMATETC *pformatetc,STGMEDIUM *pstgmed) {
    return This->lpVtbl->OnDataAvailable(This,grfBSCF,dwSize,pformatetc,pstgmed);
}
static inline HRESULT IBindStatusCallback_OnObjectAvailable(IBindStatusCallback* This,REFIID riid,IUnknown *punk) {
    return This->lpVtbl->OnObjectAvailable(This,riid,punk);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IBindStatusCallback_RemoteGetBindInfo_Proxy(
    IBindStatusCallback* This,
    DWORD *grfBINDF,
    RemBINDINFO *pbindinfo,
    RemSTGMEDIUM *pstgmed);
void __RPC_STUB IBindStatusCallback_RemoteGetBindInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindStatusCallback_RemoteOnDataAvailable_Proxy(
    IBindStatusCallback* This,
    DWORD grfBSCF,
    DWORD dwSize,
    RemFORMATETC *pformatetc,
    RemSTGMEDIUM *pstgmed);
void __RPC_STUB IBindStatusCallback_RemoteOnDataAvailable_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IBindStatusCallback_GetBindInfo_Proxy(
    IBindStatusCallback* This,
    DWORD *grfBINDF,
    BINDINFO *pbindinfo);
HRESULT __RPC_STUB IBindStatusCallback_GetBindInfo_Stub(
    IBindStatusCallback* This,
    DWORD *grfBINDF,
    RemBINDINFO *pbindinfo,
    RemSTGMEDIUM *pstgmed);
HRESULT CALLBACK IBindStatusCallback_OnDataAvailable_Proxy(
    IBindStatusCallback* This,
    DWORD grfBSCF,
    DWORD dwSize,
    FORMATETC *pformatetc,
    STGMEDIUM *pstgmed);
HRESULT __RPC_STUB IBindStatusCallback_OnDataAvailable_Stub(
    IBindStatusCallback* This,
    DWORD grfBSCF,
    DWORD dwSize,
    RemFORMATETC *pformatetc,
    RemSTGMEDIUM *pstgmed);

#endif  /* __IBindStatusCallback_INTERFACE_DEFINED__ */

#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef _LPBINDSTATUSCALLBACKEX_DEFINED
#define _LPBINDSTATUSCALLBACKEX_DEFINED

/*****************************************************************************
 * IBindStatusCallbackEx interface
 */
#ifndef __IBindStatusCallbackEx_INTERFACE_DEFINED__
#define __IBindStatusCallbackEx_INTERFACE_DEFINED__

typedef IBindStatusCallbackEx *LPBINDSTATUSCALLBACKEX;

typedef enum __WIDL_urlmon_generated_name_00000016 {
    BINDF2_DISABLEBASICOVERHTTP = 0x1,
    BINDF2_DISABLEAUTOCOOKIEHANDLING = 0x2,
    BINDF2_READ_DATA_GREATER_THAN_4GB = 0x4,
    BINDF2_DISABLE_HTTP_REDIRECT_XSECURITYID = 0x8,
    BINDF2_SETDOWNLOADMODE = 0x20,
    BINDF2_DISABLE_HTTP_REDIRECT_CACHING = 0x40,
    BINDF2_KEEP_CALLBACK_MODULE_LOADED = 0x80,
    BINDF2_ALLOW_PROXY_CRED_PROMPT = 0x100,
    BINDF2_RESERVED_F = 0x20000,
    BINDF2_RESERVED_E = 0x40000,
    BINDF2_RESERVED_D = 0x80000,
    BINDF2_RESERVED_C = 0x100000,
    BINDF2_RESERVED_B = 0x200000,
    BINDF2_RESERVED_A = 0x400000,
    BINDF2_RESERVED_9 = 0x800000,
    BINDF2_RESERVED_8 = 0x1000000,
    BINDF2_RESERVED_7 = 0x2000000,
    BINDF2_RESERVED_6 = 0x4000000,
    BINDF2_RESERVED_5 = 0x8000000,
    BINDF2_RESERVED_4 = 0x10000000,
    BINDF2_RESERVED_3 = 0x20000000,
    BINDF2_RESERVED_2 = 0x40000000,
    BINDF2_RESERVED_1 = 0x80000000
} BINDF2;

DEFINE_GUID(IID_IBindStatusCallbackEx, 0xaaa74ef9, 0x8ee7, 0x4659, 0x88,0xd9, 0xf8,0xc5,0x04,0xda,0x73,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aaa74ef9-8ee7-4659-88d9-f8c504da73cc")
IBindStatusCallbackEx : public IBindStatusCallback
{
    virtual HRESULT STDMETHODCALLTYPE GetBindInfoEx(
        DWORD *grfBINDF,
        BINDINFO *pbindinfo,
        DWORD *grfBINDF2,
        DWORD *pdwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBindStatusCallbackEx, 0xaaa74ef9, 0x8ee7, 0x4659, 0x88,0xd9, 0xf8,0xc5,0x04,0xda,0x73,0xcc)
#endif
#else
typedef struct IBindStatusCallbackExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBindStatusCallbackEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBindStatusCallbackEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBindStatusCallbackEx *This);

    /*** IBindStatusCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStartBinding)(
        IBindStatusCallbackEx *This,
        DWORD dwReserved,
        IBinding *pib);

    HRESULT (STDMETHODCALLTYPE *GetPriority)(
        IBindStatusCallbackEx *This,
        LONG *pnPriority);

    HRESULT (STDMETHODCALLTYPE *OnLowResource)(
        IBindStatusCallbackEx *This,
        DWORD reserved);

    HRESULT (STDMETHODCALLTYPE *OnProgress)(
        IBindStatusCallbackEx *This,
        ULONG ulProgress,
        ULONG ulProgressMax,
        ULONG ulStatusCode,
        LPCWSTR szStatusText);

    HRESULT (STDMETHODCALLTYPE *OnStopBinding)(
        IBindStatusCallbackEx *This,
        HRESULT hresult,
        LPCWSTR szError);

    HRESULT (STDMETHODCALLTYPE *GetBindInfo)(
        IBindStatusCallbackEx *This,
        DWORD *grfBINDF,
        BINDINFO *pbindinfo);

    HRESULT (STDMETHODCALLTYPE *OnDataAvailable)(
        IBindStatusCallbackEx *This,
        DWORD grfBSCF,
        DWORD dwSize,
        FORMATETC *pformatetc,
        STGMEDIUM *pstgmed);

    HRESULT (STDMETHODCALLTYPE *OnObjectAvailable)(
        IBindStatusCallbackEx *This,
        REFIID riid,
        IUnknown *punk);

    /*** IBindStatusCallbackEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBindInfoEx)(
        IBindStatusCallbackEx *This,
        DWORD *grfBINDF,
        BINDINFO *pbindinfo,
        DWORD *grfBINDF2,
        DWORD *pdwReserved);

    END_INTERFACE
} IBindStatusCallbackExVtbl;

interface IBindStatusCallbackEx {
    CONST_VTBL IBindStatusCallbackExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBindStatusCallbackEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBindStatusCallbackEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBindStatusCallbackEx_Release(This) (This)->lpVtbl->Release(This)
/*** IBindStatusCallback methods ***/
#define IBindStatusCallbackEx_OnStartBinding(This,dwReserved,pib) (This)->lpVtbl->OnStartBinding(This,dwReserved,pib)
#define IBindStatusCallbackEx_GetPriority(This,pnPriority) (This)->lpVtbl->GetPriority(This,pnPriority)
#define IBindStatusCallbackEx_OnLowResource(This,reserved) (This)->lpVtbl->OnLowResource(This,reserved)
#define IBindStatusCallbackEx_OnProgress(This,ulProgress,ulProgressMax,ulStatusCode,szStatusText) (This)->lpVtbl->OnProgress(This,ulProgress,ulProgressMax,ulStatusCode,szStatusText)
#define IBindStatusCallbackEx_OnStopBinding(This,hresult,szError) (This)->lpVtbl->OnStopBinding(This,hresult,szError)
#define IBindStatusCallbackEx_GetBindInfo(This,grfBINDF,pbindinfo) (This)->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo)
#define IBindStatusCallbackEx_OnDataAvailable(This,grfBSCF,dwSize,pformatetc,pstgmed) (This)->lpVtbl->OnDataAvailable(This,grfBSCF,dwSize,pformatetc,pstgmed)
#define IBindStatusCallbackEx_OnObjectAvailable(This,riid,punk) (This)->lpVtbl->OnObjectAvailable(This,riid,punk)
/*** IBindStatusCallbackEx methods ***/
#define IBindStatusCallbackEx_GetBindInfoEx(This,grfBINDF,pbindinfo,grfBINDF2,pdwReserved) (This)->lpVtbl->GetBindInfoEx(This,grfBINDF,pbindinfo,grfBINDF2,pdwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IBindStatusCallbackEx_QueryInterface(IBindStatusCallbackEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBindStatusCallbackEx_AddRef(IBindStatusCallbackEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBindStatusCallbackEx_Release(IBindStatusCallbackEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IBindStatusCallback methods ***/
static inline HRESULT IBindStatusCallbackEx_OnStartBinding(IBindStatusCallbackEx* This,DWORD dwReserved,IBinding *pib) {
    return This->lpVtbl->OnStartBinding(This,dwReserved,pib);
}
static inline HRESULT IBindStatusCallbackEx_GetPriority(IBindStatusCallbackEx* This,LONG *pnPriority) {
    return This->lpVtbl->GetPriority(This,pnPriority);
}
static inline HRESULT IBindStatusCallbackEx_OnLowResource(IBindStatusCallbackEx* This,DWORD reserved) {
    return This->lpVtbl->OnLowResource(This,reserved);
}
static inline HRESULT IBindStatusCallbackEx_OnProgress(IBindStatusCallbackEx* This,ULONG ulProgress,ULONG ulProgressMax,ULONG ulStatusCode,LPCWSTR szStatusText) {
    return This->lpVtbl->OnProgress(This,ulProgress,ulProgressMax,ulStatusCode,szStatusText);
}
static inline HRESULT IBindStatusCallbackEx_OnStopBinding(IBindStatusCallbackEx* This,HRESULT hresult,LPCWSTR szError) {
    return This->lpVtbl->OnStopBinding(This,hresult,szError);
}
static inline HRESULT IBindStatusCallbackEx_GetBindInfo(IBindStatusCallbackEx* This,DWORD *grfBINDF,BINDINFO *pbindinfo) {
    return This->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo);
}
static inline HRESULT IBindStatusCallbackEx_OnDataAvailable(IBindStatusCallbackEx* This,DWORD grfBSCF,DWORD dwSize,FORMATETC *pformatetc,STGMEDIUM *pstgmed) {
    return This->lpVtbl->OnDataAvailable(This,grfBSCF,dwSize,pformatetc,pstgmed);
}
static inline HRESULT IBindStatusCallbackEx_OnObjectAvailable(IBindStatusCallbackEx* This,REFIID riid,IUnknown *punk) {
    return This->lpVtbl->OnObjectAvailable(This,riid,punk);
}
/*** IBindStatusCallbackEx methods ***/
static inline HRESULT IBindStatusCallbackEx_GetBindInfoEx(IBindStatusCallbackEx* This,DWORD *grfBINDF,BINDINFO *pbindinfo,DWORD *grfBINDF2,DWORD *pdwReserved) {
    return This->lpVtbl->GetBindInfoEx(This,grfBINDF,pbindinfo,grfBINDF2,pdwReserved);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IBindStatusCallbackEx_RemoteGetBindInfoEx_Proxy(
    IBindStatusCallbackEx* This,
    DWORD *grfBINDF,
    RemBINDINFO *pbindinfo,
    RemSTGMEDIUM *pstgmed,
    DWORD *grfBINDF2,
    DWORD *pdwReserved);
void __RPC_STUB IBindStatusCallbackEx_RemoteGetBindInfoEx_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IBindStatusCallbackEx_GetBindInfoEx_Proxy(
    IBindStatusCallbackEx* This,
    DWORD *grfBINDF,
    BINDINFO *pbindinfo,
    DWORD *grfBINDF2,
    DWORD *pdwReserved);
HRESULT __RPC_STUB IBindStatusCallbackEx_GetBindInfoEx_Stub(
    IBindStatusCallbackEx* This,
    DWORD *grfBINDF,
    RemBINDINFO *pbindinfo,
    RemSTGMEDIUM *pstgmed,
    DWORD *grfBINDF2,
    DWORD *pdwReserved);

#endif  /* __IBindStatusCallbackEx_INTERFACE_DEFINED__ */

#endif

#ifndef _LPAUTHENTICATION_DEFINED
#define _LPAUTHENTICATION_DEFINED

/*****************************************************************************
 * IAuthenticate interface
 */
#ifndef __IAuthenticate_INTERFACE_DEFINED__
#define __IAuthenticate_INTERFACE_DEFINED__

typedef IAuthenticate *LPAUTHENTICATION;

DEFINE_GUID(IID_IAuthenticate, 0x79eac9d0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9d0-baf9-11ce-8c82-00aa004ba90b")
IAuthenticate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Authenticate(
        HWND *phwnd,
        LPWSTR *pszUsername,
        LPWSTR *pszPassword) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAuthenticate, 0x79eac9d0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IAuthenticateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAuthenticate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAuthenticate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAuthenticate *This);

    /*** IAuthenticate methods ***/
    HRESULT (STDMETHODCALLTYPE *Authenticate)(
        IAuthenticate *This,
        HWND *phwnd,
        LPWSTR *pszUsername,
        LPWSTR *pszPassword);

    END_INTERFACE
} IAuthenticateVtbl;

interface IAuthenticate {
    CONST_VTBL IAuthenticateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAuthenticate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAuthenticate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAuthenticate_Release(This) (This)->lpVtbl->Release(This)
/*** IAuthenticate methods ***/
#define IAuthenticate_Authenticate(This,phwnd,pszUsername,pszPassword) (This)->lpVtbl->Authenticate(This,phwnd,pszUsername,pszPassword)
#else
/*** IUnknown methods ***/
static inline HRESULT IAuthenticate_QueryInterface(IAuthenticate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAuthenticate_AddRef(IAuthenticate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAuthenticate_Release(IAuthenticate* This) {
    return This->lpVtbl->Release(This);
}
/*** IAuthenticate methods ***/
static inline HRESULT IAuthenticate_Authenticate(IAuthenticate* This,HWND *phwnd,LPWSTR *pszUsername,LPWSTR *pszPassword) {
    return This->lpVtbl->Authenticate(This,phwnd,pszUsername,pszPassword);
}
#endif
#endif

#endif


#endif  /* __IAuthenticate_INTERFACE_DEFINED__ */

#endif

#ifndef _LPAUTHENTICATIONEX_DEFINED
#define _LPAUTHENTICATIONEX_DEFINED

/*****************************************************************************
 * IAuthenticateEx interface
 */
#ifndef __IAuthenticateEx_INTERFACE_DEFINED__
#define __IAuthenticateEx_INTERFACE_DEFINED__

typedef IAuthenticateEx *LPAUTHENTICATIONEX;

typedef enum __WIDL_urlmon_generated_name_00000017 {
    AUTHENTICATEF_PROXY = 0x1,
    AUTHENTICATEF_BASIC = 0x2,
    AUTHENTICATEF_HTTP = 0x4
} AUTHENTICATEF;

typedef struct _tagAUTHENTICATEINFO {
    DWORD dwFlags;
    DWORD dwReserved;
} AUTHENTICATEINFO;

DEFINE_GUID(IID_IAuthenticateEx, 0x2ad1edaf, 0xd83d, 0x48b5, 0x9a,0xdf, 0x03,0xdb,0xe1,0x9f,0x53,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2ad1edaf-d83d-48b5-9adf-03dbe19f53bd")
IAuthenticateEx : public IAuthenticate
{
    virtual HRESULT STDMETHODCALLTYPE AuthenticateEx(
        HWND *phwnd,
        LPWSTR *pszUsername,
        LPWSTR *pszPassword,
        AUTHENTICATEINFO *pauthinfo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAuthenticateEx, 0x2ad1edaf, 0xd83d, 0x48b5, 0x9a,0xdf, 0x03,0xdb,0xe1,0x9f,0x53,0xbd)
#endif
#else
typedef struct IAuthenticateExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAuthenticateEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAuthenticateEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAuthenticateEx *This);

    /*** IAuthenticate methods ***/
    HRESULT (STDMETHODCALLTYPE *Authenticate)(
        IAuthenticateEx *This,
        HWND *phwnd,
        LPWSTR *pszUsername,
        LPWSTR *pszPassword);

    /*** IAuthenticateEx methods ***/
    HRESULT (STDMETHODCALLTYPE *AuthenticateEx)(
        IAuthenticateEx *This,
        HWND *phwnd,
        LPWSTR *pszUsername,
        LPWSTR *pszPassword,
        AUTHENTICATEINFO *pauthinfo);

    END_INTERFACE
} IAuthenticateExVtbl;

interface IAuthenticateEx {
    CONST_VTBL IAuthenticateExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAuthenticateEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAuthenticateEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAuthenticateEx_Release(This) (This)->lpVtbl->Release(This)
/*** IAuthenticate methods ***/
#define IAuthenticateEx_Authenticate(This,phwnd,pszUsername,pszPassword) (This)->lpVtbl->Authenticate(This,phwnd,pszUsername,pszPassword)
/*** IAuthenticateEx methods ***/
#define IAuthenticateEx_AuthenticateEx(This,phwnd,pszUsername,pszPassword,pauthinfo) (This)->lpVtbl->AuthenticateEx(This,phwnd,pszUsername,pszPassword,pauthinfo)
#else
/*** IUnknown methods ***/
static inline HRESULT IAuthenticateEx_QueryInterface(IAuthenticateEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAuthenticateEx_AddRef(IAuthenticateEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAuthenticateEx_Release(IAuthenticateEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IAuthenticate methods ***/
static inline HRESULT IAuthenticateEx_Authenticate(IAuthenticateEx* This,HWND *phwnd,LPWSTR *pszUsername,LPWSTR *pszPassword) {
    return This->lpVtbl->Authenticate(This,phwnd,pszUsername,pszPassword);
}
/*** IAuthenticateEx methods ***/
static inline HRESULT IAuthenticateEx_AuthenticateEx(IAuthenticateEx* This,HWND *phwnd,LPWSTR *pszUsername,LPWSTR *pszPassword,AUTHENTICATEINFO *pauthinfo) {
    return This->lpVtbl->AuthenticateEx(This,phwnd,pszUsername,pszPassword,pauthinfo);
}
#endif
#endif

#endif


#endif  /* __IAuthenticateEx_INTERFACE_DEFINED__ */

#endif

#ifndef _LPHTTPNEGOTIATE_DEFINED
#define _LPHTTPNEGOTIATE_DEFINED

/*****************************************************************************
 * IHttpNegotiate interface
 */
#ifndef __IHttpNegotiate_INTERFACE_DEFINED__
#define __IHttpNegotiate_INTERFACE_DEFINED__

typedef IHttpNegotiate *LPHTTPNEGOTIATE;

DEFINE_GUID(IID_IHttpNegotiate, 0x79eac9d2, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9d2-baf9-11ce-8c82-00aa004ba90b")
IHttpNegotiate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BeginningTransaction(
        LPCWSTR szURL,
        LPCWSTR szHeaders,
        DWORD dwReserved,
        LPWSTR *pszAdditionalHeaders) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnResponse(
        DWORD dwResponseCode,
        LPCWSTR szResponseHeaders,
        LPCWSTR szRequestHeaders,
        LPWSTR *pszAdditionalRequestHeaders) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IHttpNegotiate, 0x79eac9d2, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IHttpNegotiateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IHttpNegotiate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IHttpNegotiate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IHttpNegotiate *This);

    /*** IHttpNegotiate methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginningTransaction)(
        IHttpNegotiate *This,
        LPCWSTR szURL,
        LPCWSTR szHeaders,
        DWORD dwReserved,
        LPWSTR *pszAdditionalHeaders);

    HRESULT (STDMETHODCALLTYPE *OnResponse)(
        IHttpNegotiate *This,
        DWORD dwResponseCode,
        LPCWSTR szResponseHeaders,
        LPCWSTR szRequestHeaders,
        LPWSTR *pszAdditionalRequestHeaders);

    END_INTERFACE
} IHttpNegotiateVtbl;

interface IHttpNegotiate {
    CONST_VTBL IHttpNegotiateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IHttpNegotiate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHttpNegotiate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHttpNegotiate_Release(This) (This)->lpVtbl->Release(This)
/*** IHttpNegotiate methods ***/
#define IHttpNegotiate_BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders) (This)->lpVtbl->BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders)
#define IHttpNegotiate_OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders) (This)->lpVtbl->OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders)
#else
/*** IUnknown methods ***/
static inline HRESULT IHttpNegotiate_QueryInterface(IHttpNegotiate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IHttpNegotiate_AddRef(IHttpNegotiate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IHttpNegotiate_Release(IHttpNegotiate* This) {
    return This->lpVtbl->Release(This);
}
/*** IHttpNegotiate methods ***/
static inline HRESULT IHttpNegotiate_BeginningTransaction(IHttpNegotiate* This,LPCWSTR szURL,LPCWSTR szHeaders,DWORD dwReserved,LPWSTR *pszAdditionalHeaders) {
    return This->lpVtbl->BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders);
}
static inline HRESULT IHttpNegotiate_OnResponse(IHttpNegotiate* This,DWORD dwResponseCode,LPCWSTR szResponseHeaders,LPCWSTR szRequestHeaders,LPWSTR *pszAdditionalRequestHeaders) {
    return This->lpVtbl->OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders);
}
#endif
#endif

#endif


#endif  /* __IHttpNegotiate_INTERFACE_DEFINED__ */

#endif

#ifndef _LPHTTPNEGOTIATE2_DEFINED
#define _LPHTTPNEGOTIATE2_DEFINED

/*****************************************************************************
 * IHttpNegotiate2 interface
 */
#ifndef __IHttpNegotiate2_INTERFACE_DEFINED__
#define __IHttpNegotiate2_INTERFACE_DEFINED__

typedef IHttpNegotiate2 *LPHTTPNEGOTIATE2;

DEFINE_GUID(IID_IHttpNegotiate2, 0x4f9f9fcb, 0xe0f4, 0x48eb, 0xb7,0xab, 0xfa,0x2e,0xa9,0x36,0x5c,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4f9f9fcb-e0f4-48eb-b7ab-fa2ea9365cb4")
IHttpNegotiate2 : public IHttpNegotiate
{
    virtual HRESULT STDMETHODCALLTYPE GetRootSecurityId(
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IHttpNegotiate2, 0x4f9f9fcb, 0xe0f4, 0x48eb, 0xb7,0xab, 0xfa,0x2e,0xa9,0x36,0x5c,0xb4)
#endif
#else
typedef struct IHttpNegotiate2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IHttpNegotiate2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IHttpNegotiate2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IHttpNegotiate2 *This);

    /*** IHttpNegotiate methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginningTransaction)(
        IHttpNegotiate2 *This,
        LPCWSTR szURL,
        LPCWSTR szHeaders,
        DWORD dwReserved,
        LPWSTR *pszAdditionalHeaders);

    HRESULT (STDMETHODCALLTYPE *OnResponse)(
        IHttpNegotiate2 *This,
        DWORD dwResponseCode,
        LPCWSTR szResponseHeaders,
        LPCWSTR szRequestHeaders,
        LPWSTR *pszAdditionalRequestHeaders);

    /*** IHttpNegotiate2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRootSecurityId)(
        IHttpNegotiate2 *This,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved);

    END_INTERFACE
} IHttpNegotiate2Vtbl;

interface IHttpNegotiate2 {
    CONST_VTBL IHttpNegotiate2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IHttpNegotiate2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHttpNegotiate2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHttpNegotiate2_Release(This) (This)->lpVtbl->Release(This)
/*** IHttpNegotiate methods ***/
#define IHttpNegotiate2_BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders) (This)->lpVtbl->BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders)
#define IHttpNegotiate2_OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders) (This)->lpVtbl->OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders)
/*** IHttpNegotiate2 methods ***/
#define IHttpNegotiate2_GetRootSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved) (This)->lpVtbl->GetRootSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IHttpNegotiate2_QueryInterface(IHttpNegotiate2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IHttpNegotiate2_AddRef(IHttpNegotiate2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IHttpNegotiate2_Release(IHttpNegotiate2* This) {
    return This->lpVtbl->Release(This);
}
/*** IHttpNegotiate methods ***/
static inline HRESULT IHttpNegotiate2_BeginningTransaction(IHttpNegotiate2* This,LPCWSTR szURL,LPCWSTR szHeaders,DWORD dwReserved,LPWSTR *pszAdditionalHeaders) {
    return This->lpVtbl->BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders);
}
static inline HRESULT IHttpNegotiate2_OnResponse(IHttpNegotiate2* This,DWORD dwResponseCode,LPCWSTR szResponseHeaders,LPCWSTR szRequestHeaders,LPWSTR *pszAdditionalRequestHeaders) {
    return This->lpVtbl->OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders);
}
/*** IHttpNegotiate2 methods ***/
static inline HRESULT IHttpNegotiate2_GetRootSecurityId(IHttpNegotiate2* This,BYTE *pbSecurityId,DWORD *pcbSecurityId,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetRootSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IHttpNegotiate2_INTERFACE_DEFINED__ */

#endif

#ifndef _LPHTTPNEGOTIATE3_DEFINED
#define _LPHTTPNEGOTIATE3_DEFINED

/*****************************************************************************
 * IHttpNegotiate3 interface
 */
#ifndef __IHttpNegotiate3_INTERFACE_DEFINED__
#define __IHttpNegotiate3_INTERFACE_DEFINED__

typedef IHttpNegotiate3 *LPHTTPNEGOTIATE3;

DEFINE_GUID(IID_IHttpNegotiate3, 0x57b6c80a, 0x34c2, 0x4602, 0xbc,0x26, 0x66,0xa0,0x2f,0xc5,0x71,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("57b6c80a-34c2-4602-bc26-66a02fc57153")
IHttpNegotiate3 : public IHttpNegotiate2
{
    virtual HRESULT STDMETHODCALLTYPE GetSerializedClientCertContext(
        BYTE **ppbCert,
        DWORD *pcbCert) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IHttpNegotiate3, 0x57b6c80a, 0x34c2, 0x4602, 0xbc,0x26, 0x66,0xa0,0x2f,0xc5,0x71,0x53)
#endif
#else
typedef struct IHttpNegotiate3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IHttpNegotiate3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IHttpNegotiate3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IHttpNegotiate3 *This);

    /*** IHttpNegotiate methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginningTransaction)(
        IHttpNegotiate3 *This,
        LPCWSTR szURL,
        LPCWSTR szHeaders,
        DWORD dwReserved,
        LPWSTR *pszAdditionalHeaders);

    HRESULT (STDMETHODCALLTYPE *OnResponse)(
        IHttpNegotiate3 *This,
        DWORD dwResponseCode,
        LPCWSTR szResponseHeaders,
        LPCWSTR szRequestHeaders,
        LPWSTR *pszAdditionalRequestHeaders);

    /*** IHttpNegotiate2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRootSecurityId)(
        IHttpNegotiate3 *This,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved);

    /*** IHttpNegotiate3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSerializedClientCertContext)(
        IHttpNegotiate3 *This,
        BYTE **ppbCert,
        DWORD *pcbCert);

    END_INTERFACE
} IHttpNegotiate3Vtbl;

interface IHttpNegotiate3 {
    CONST_VTBL IHttpNegotiate3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IHttpNegotiate3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHttpNegotiate3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHttpNegotiate3_Release(This) (This)->lpVtbl->Release(This)
/*** IHttpNegotiate methods ***/
#define IHttpNegotiate3_BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders) (This)->lpVtbl->BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders)
#define IHttpNegotiate3_OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders) (This)->lpVtbl->OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders)
/*** IHttpNegotiate2 methods ***/
#define IHttpNegotiate3_GetRootSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved) (This)->lpVtbl->GetRootSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved)
/*** IHttpNegotiate3 methods ***/
#define IHttpNegotiate3_GetSerializedClientCertContext(This,ppbCert,pcbCert) (This)->lpVtbl->GetSerializedClientCertContext(This,ppbCert,pcbCert)
#else
/*** IUnknown methods ***/
static inline HRESULT IHttpNegotiate3_QueryInterface(IHttpNegotiate3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IHttpNegotiate3_AddRef(IHttpNegotiate3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IHttpNegotiate3_Release(IHttpNegotiate3* This) {
    return This->lpVtbl->Release(This);
}
/*** IHttpNegotiate methods ***/
static inline HRESULT IHttpNegotiate3_BeginningTransaction(IHttpNegotiate3* This,LPCWSTR szURL,LPCWSTR szHeaders,DWORD dwReserved,LPWSTR *pszAdditionalHeaders) {
    return This->lpVtbl->BeginningTransaction(This,szURL,szHeaders,dwReserved,pszAdditionalHeaders);
}
static inline HRESULT IHttpNegotiate3_OnResponse(IHttpNegotiate3* This,DWORD dwResponseCode,LPCWSTR szResponseHeaders,LPCWSTR szRequestHeaders,LPWSTR *pszAdditionalRequestHeaders) {
    return This->lpVtbl->OnResponse(This,dwResponseCode,szResponseHeaders,szRequestHeaders,pszAdditionalRequestHeaders);
}
/*** IHttpNegotiate2 methods ***/
static inline HRESULT IHttpNegotiate3_GetRootSecurityId(IHttpNegotiate3* This,BYTE *pbSecurityId,DWORD *pcbSecurityId,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetRootSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved);
}
/*** IHttpNegotiate3 methods ***/
static inline HRESULT IHttpNegotiate3_GetSerializedClientCertContext(IHttpNegotiate3* This,BYTE **ppbCert,DWORD *pcbCert) {
    return This->lpVtbl->GetSerializedClientCertContext(This,ppbCert,pcbCert);
}
#endif
#endif

#endif


#endif  /* __IHttpNegotiate3_INTERFACE_DEFINED__ */

#endif

#ifndef _LPWININETFILESTREAM_DEFINED
#define _LPWININETFILESTREAM_DEFINED

/*****************************************************************************
 * IWinInetFileStream interface
 */
#ifndef __IWinInetFileStream_INTERFACE_DEFINED__
#define __IWinInetFileStream_INTERFACE_DEFINED__

typedef IWinInetFileStream *LPWININETFILESTREAM;

DEFINE_GUID(IID_IWinInetFileStream, 0xf134c4b7, 0xb1f8, 0x4e75, 0xb8,0x86, 0x74,0xb9,0x09,0x43,0xbe,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f134c4b7-b1f8-4e75-b886-74b90943becb")
IWinInetFileStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetHandleForUnlock(
        DWORD_PTR hWinInetLockHandle,
        DWORD_PTR dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDeleteFile(
        DWORD_PTR dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWinInetFileStream, 0xf134c4b7, 0xb1f8, 0x4e75, 0xb8,0x86, 0x74,0xb9,0x09,0x43,0xbe,0xcb)
#endif
#else
typedef struct IWinInetFileStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWinInetFileStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWinInetFileStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWinInetFileStream *This);

    /*** IWinInetFileStream methods ***/
    HRESULT (STDMETHODCALLTYPE *SetHandleForUnlock)(
        IWinInetFileStream *This,
        DWORD_PTR hWinInetLockHandle,
        DWORD_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *SetDeleteFile)(
        IWinInetFileStream *This,
        DWORD_PTR dwReserved);

    END_INTERFACE
} IWinInetFileStreamVtbl;

interface IWinInetFileStream {
    CONST_VTBL IWinInetFileStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWinInetFileStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWinInetFileStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWinInetFileStream_Release(This) (This)->lpVtbl->Release(This)
/*** IWinInetFileStream methods ***/
#define IWinInetFileStream_SetHandleForUnlock(This,hWinInetLockHandle,dwReserved) (This)->lpVtbl->SetHandleForUnlock(This,hWinInetLockHandle,dwReserved)
#define IWinInetFileStream_SetDeleteFile(This,dwReserved) (This)->lpVtbl->SetDeleteFile(This,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IWinInetFileStream_QueryInterface(IWinInetFileStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWinInetFileStream_AddRef(IWinInetFileStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWinInetFileStream_Release(IWinInetFileStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IWinInetFileStream methods ***/
static inline HRESULT IWinInetFileStream_SetHandleForUnlock(IWinInetFileStream* This,DWORD_PTR hWinInetLockHandle,DWORD_PTR dwReserved) {
    return This->lpVtbl->SetHandleForUnlock(This,hWinInetLockHandle,dwReserved);
}
static inline HRESULT IWinInetFileStream_SetDeleteFile(IWinInetFileStream* This,DWORD_PTR dwReserved) {
    return This->lpVtbl->SetDeleteFile(This,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IWinInetFileStream_INTERFACE_DEFINED__ */

#endif

#ifndef _LPWINDOWFORBINDINGUI_DEFINED
#define _LPWINDOWFORBINDINGUI_DEFINED

/*****************************************************************************
 * IWindowForBindingUI interface
 */
#ifndef __IWindowForBindingUI_INTERFACE_DEFINED__
#define __IWindowForBindingUI_INTERFACE_DEFINED__

typedef IWindowForBindingUI *LPWINDOWFORBINDINGUI;

DEFINE_GUID(IID_IWindowForBindingUI, 0x79eac9d5, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9d5-bafa-11ce-8c82-00aa004ba90b")
IWindowForBindingUI : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetWindow(
        REFGUID rguidReason,
        HWND *phwnd) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWindowForBindingUI, 0x79eac9d5, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IWindowForBindingUIVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWindowForBindingUI *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWindowForBindingUI *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWindowForBindingUI *This);

    /*** IWindowForBindingUI methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IWindowForBindingUI *This,
        REFGUID rguidReason,
        HWND *phwnd);

    END_INTERFACE
} IWindowForBindingUIVtbl;

interface IWindowForBindingUI {
    CONST_VTBL IWindowForBindingUIVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWindowForBindingUI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWindowForBindingUI_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWindowForBindingUI_Release(This) (This)->lpVtbl->Release(This)
/*** IWindowForBindingUI methods ***/
#define IWindowForBindingUI_GetWindow(This,rguidReason,phwnd) (This)->lpVtbl->GetWindow(This,rguidReason,phwnd)
#else
/*** IUnknown methods ***/
static inline HRESULT IWindowForBindingUI_QueryInterface(IWindowForBindingUI* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWindowForBindingUI_AddRef(IWindowForBindingUI* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWindowForBindingUI_Release(IWindowForBindingUI* This) {
    return This->lpVtbl->Release(This);
}
/*** IWindowForBindingUI methods ***/
static inline HRESULT IWindowForBindingUI_GetWindow(IWindowForBindingUI* This,REFGUID rguidReason,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,rguidReason,phwnd);
}
#endif
#endif

#endif


#endif  /* __IWindowForBindingUI_INTERFACE_DEFINED__ */

#endif

#ifndef _LPCODEINSTALL_DEFINED
#define _LPCODEINSTALL_DEFINED

/*****************************************************************************
 * ICodeInstall interface
 */
#ifndef __ICodeInstall_INTERFACE_DEFINED__
#define __ICodeInstall_INTERFACE_DEFINED__

typedef ICodeInstall *LPCODEINSTALL;

typedef enum __WIDL_urlmon_generated_name_00000018 {
    CIP_DISK_FULL = 0,
    CIP_ACCESS_DENIED = 1,
    CIP_NEWER_VERSION_EXISTS = 2,
    CIP_OLDER_VERSION_EXISTS = 3,
    CIP_NAME_CONFLICT = 4,
    CIP_TRUST_VERIFICATION_COMPONENT_MISSING = 5,
    CIP_EXE_SELF_REGISTERATION_TIMEOUT = 6,
    CIP_UNSAFE_TO_ABORT = 7,
    CIP_NEED_REBOOT = 8,
    CIP_NEED_REBOOT_UI_PERMISSION = 9
} CIP_STATUS;

DEFINE_GUID(IID_ICodeInstall, 0x79eac9d1, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9d1-baf9-11ce-8c82-00aa004ba90b")
ICodeInstall : public IWindowForBindingUI
{
    virtual HRESULT STDMETHODCALLTYPE OnCodeInstallProblem(
        ULONG ulStatusCode,
        LPCWSTR szDestination,
        LPCWSTR szSource,
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICodeInstall, 0x79eac9d1, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct ICodeInstallVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICodeInstall *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICodeInstall *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICodeInstall *This);

    /*** IWindowForBindingUI methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        ICodeInstall *This,
        REFGUID rguidReason,
        HWND *phwnd);

    /*** ICodeInstall methods ***/
    HRESULT (STDMETHODCALLTYPE *OnCodeInstallProblem)(
        ICodeInstall *This,
        ULONG ulStatusCode,
        LPCWSTR szDestination,
        LPCWSTR szSource,
        DWORD dwReserved);

    END_INTERFACE
} ICodeInstallVtbl;

interface ICodeInstall {
    CONST_VTBL ICodeInstallVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICodeInstall_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICodeInstall_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICodeInstall_Release(This) (This)->lpVtbl->Release(This)
/*** IWindowForBindingUI methods ***/
#define ICodeInstall_GetWindow(This,rguidReason,phwnd) (This)->lpVtbl->GetWindow(This,rguidReason,phwnd)
/*** ICodeInstall methods ***/
#define ICodeInstall_OnCodeInstallProblem(This,ulStatusCode,szDestination,szSource,dwReserved) (This)->lpVtbl->OnCodeInstallProblem(This,ulStatusCode,szDestination,szSource,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT ICodeInstall_QueryInterface(ICodeInstall* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICodeInstall_AddRef(ICodeInstall* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICodeInstall_Release(ICodeInstall* This) {
    return This->lpVtbl->Release(This);
}
/*** IWindowForBindingUI methods ***/
static inline HRESULT ICodeInstall_GetWindow(ICodeInstall* This,REFGUID rguidReason,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,rguidReason,phwnd);
}
/*** ICodeInstall methods ***/
static inline HRESULT ICodeInstall_OnCodeInstallProblem(ICodeInstall* This,ULONG ulStatusCode,LPCWSTR szDestination,LPCWSTR szSource,DWORD dwReserved) {
    return This->lpVtbl->OnCodeInstallProblem(This,ulStatusCode,szDestination,szSource,dwReserved);
}
#endif
#endif

#endif


#endif  /* __ICodeInstall_INTERFACE_DEFINED__ */

#endif
#endif

#if (_WIN32_IE >= _WIN32_IE_IE70)
#ifndef _LPUri_DEFINED
#define _LPUri_DEFINED
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IUri interface
 */
#ifndef __IUri_INTERFACE_DEFINED__
#define __IUri_INTERFACE_DEFINED__

typedef enum __WIDL_urlmon_generated_name_00000019 {
    Uri_PROPERTY_ABSOLUTE_URI = 0,
    Uri_PROPERTY_STRING_START = Uri_PROPERTY_ABSOLUTE_URI,
    Uri_PROPERTY_AUTHORITY = 1,
    Uri_PROPERTY_DISPLAY_URI = 2,
    Uri_PROPERTY_DOMAIN = 3,
    Uri_PROPERTY_EXTENSION = 4,
    Uri_PROPERTY_FRAGMENT = 5,
    Uri_PROPERTY_HOST = 6,
    Uri_PROPERTY_PASSWORD = 7,
    Uri_PROPERTY_PATH = 8,
    Uri_PROPERTY_PATH_AND_QUERY = 9,
    Uri_PROPERTY_QUERY = 10,
    Uri_PROPERTY_RAW_URI = 11,
    Uri_PROPERTY_SCHEME_NAME = 12,
    Uri_PROPERTY_USER_INFO = 13,
    Uri_PROPERTY_USER_NAME = 14,
    Uri_PROPERTY_STRING_LAST = Uri_PROPERTY_USER_NAME,
    Uri_PROPERTY_HOST_TYPE = 15,
    Uri_PROPERTY_DWORD_START = Uri_PROPERTY_HOST_TYPE,
    Uri_PROPERTY_PORT = 16,
    Uri_PROPERTY_SCHEME = 17,
    Uri_PROPERTY_ZONE = 18,
    Uri_PROPERTY_DWORD_LAST = Uri_PROPERTY_ZONE
} Uri_PROPERTY;

typedef enum __WIDL_urlmon_generated_name_0000001A {
    Uri_HOST_UNKNOWN = 0,
    Uri_HOST_DNS = 1,
    Uri_HOST_IPV4 = 2,
    Uri_HOST_IPV6 = 3,
    Uri_HOST_IDN = 4
} Uri_HOST_TYPE;

DEFINE_GUID(IID_IUri, 0xa39ee748, 0x6a27, 0x4817, 0xa6,0xf2, 0x13,0x91,0x4b,0xef,0x58,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a39ee748-6a27-4817-a6f2-13914bef5890")
IUri : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyBSTR(
        Uri_PROPERTY uriProp,
        BSTR *pbstrProperty,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyLength(
        Uri_PROPERTY uriProp,
        DWORD *pcchProperty,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyDWORD(
        Uri_PROPERTY uriProp,
        DWORD *pdwProperty,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasProperty(
        Uri_PROPERTY uriProp,
        WINBOOL *pfHasProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAbsoluteUri(
        BSTR *pbstrAbsoluteUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAuthority(
        BSTR *pbstrAuthority) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayUri(
        BSTR *pbstrDisplayString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDomain(
        BSTR *pbstrDomain) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtension(
        BSTR *pbstrExtension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFragment(
        BSTR *pbstrFragment) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHost(
        BSTR *pbstrHost) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPassword(
        BSTR *pbstrPassword) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPath(
        BSTR *pbstrPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPathAndQuery(
        BSTR *pbstrPathAndQuery) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetQuery(
        BSTR *pbstrQuery) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRawUri(
        BSTR *pbstrRawUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSchemeName(
        BSTR *pbstrSchemeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserInfo(
        BSTR *pbstrUserInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserName(
        BSTR *pbstrUserName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHostType(
        DWORD *pdwHostType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPort(
        DWORD *pdwPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScheme(
        DWORD *pdwScheme) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetZone(
        DWORD *pdwZone) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperties(
        LPDWORD pdwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqual(
        IUri *pUri,
        WINBOOL *pfEqual) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUri, 0xa39ee748, 0x6a27, 0x4817, 0xa6,0xf2, 0x13,0x91,0x4b,0xef,0x58,0x90)
#endif
#else
typedef struct IUriVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUri *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUri *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUri *This);

    /*** IUri methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyBSTR)(
        IUri *This,
        Uri_PROPERTY uriProp,
        BSTR *pbstrProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetPropertyLength)(
        IUri *This,
        Uri_PROPERTY uriProp,
        DWORD *pcchProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetPropertyDWORD)(
        IUri *This,
        Uri_PROPERTY uriProp,
        DWORD *pdwProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *HasProperty)(
        IUri *This,
        Uri_PROPERTY uriProp,
        WINBOOL *pfHasProperty);

    HRESULT (STDMETHODCALLTYPE *GetAbsoluteUri)(
        IUri *This,
        BSTR *pbstrAbsoluteUri);

    HRESULT (STDMETHODCALLTYPE *GetAuthority)(
        IUri *This,
        BSTR *pbstrAuthority);

    HRESULT (STDMETHODCALLTYPE *GetDisplayUri)(
        IUri *This,
        BSTR *pbstrDisplayString);

    HRESULT (STDMETHODCALLTYPE *GetDomain)(
        IUri *This,
        BSTR *pbstrDomain);

    HRESULT (STDMETHODCALLTYPE *GetExtension)(
        IUri *This,
        BSTR *pbstrExtension);

    HRESULT (STDMETHODCALLTYPE *GetFragment)(
        IUri *This,
        BSTR *pbstrFragment);

    HRESULT (STDMETHODCALLTYPE *GetHost)(
        IUri *This,
        BSTR *pbstrHost);

    HRESULT (STDMETHODCALLTYPE *GetPassword)(
        IUri *This,
        BSTR *pbstrPassword);

    HRESULT (STDMETHODCALLTYPE *GetPath)(
        IUri *This,
        BSTR *pbstrPath);

    HRESULT (STDMETHODCALLTYPE *GetPathAndQuery)(
        IUri *This,
        BSTR *pbstrPathAndQuery);

    HRESULT (STDMETHODCALLTYPE *GetQuery)(
        IUri *This,
        BSTR *pbstrQuery);

    HRESULT (STDMETHODCALLTYPE *GetRawUri)(
        IUri *This,
        BSTR *pbstrRawUri);

    HRESULT (STDMETHODCALLTYPE *GetSchemeName)(
        IUri *This,
        BSTR *pbstrSchemeName);

    HRESULT (STDMETHODCALLTYPE *GetUserInfo)(
        IUri *This,
        BSTR *pbstrUserInfo);

    HRESULT (STDMETHODCALLTYPE *GetUserName)(
        IUri *This,
        BSTR *pbstrUserName);

    HRESULT (STDMETHODCALLTYPE *GetHostType)(
        IUri *This,
        DWORD *pdwHostType);

    HRESULT (STDMETHODCALLTYPE *GetPort)(
        IUri *This,
        DWORD *pdwPort);

    HRESULT (STDMETHODCALLTYPE *GetScheme)(
        IUri *This,
        DWORD *pdwScheme);

    HRESULT (STDMETHODCALLTYPE *GetZone)(
        IUri *This,
        DWORD *pdwZone);

    HRESULT (STDMETHODCALLTYPE *GetProperties)(
        IUri *This,
        LPDWORD pdwFlags);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IUri *This,
        IUri *pUri,
        WINBOOL *pfEqual);

    END_INTERFACE
} IUriVtbl;

interface IUri {
    CONST_VTBL IUriVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUri_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUri_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUri_Release(This) (This)->lpVtbl->Release(This)
/*** IUri methods ***/
#define IUri_GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags) (This)->lpVtbl->GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags)
#define IUri_GetPropertyLength(This,uriProp,pcchProperty,dwFlags) (This)->lpVtbl->GetPropertyLength(This,uriProp,pcchProperty,dwFlags)
#define IUri_GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags) (This)->lpVtbl->GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags)
#define IUri_HasProperty(This,uriProp,pfHasProperty) (This)->lpVtbl->HasProperty(This,uriProp,pfHasProperty)
#define IUri_GetAbsoluteUri(This,pbstrAbsoluteUri) (This)->lpVtbl->GetAbsoluteUri(This,pbstrAbsoluteUri)
#define IUri_GetAuthority(This,pbstrAuthority) (This)->lpVtbl->GetAuthority(This,pbstrAuthority)
#define IUri_GetDisplayUri(This,pbstrDisplayString) (This)->lpVtbl->GetDisplayUri(This,pbstrDisplayString)
#define IUri_GetDomain(This,pbstrDomain) (This)->lpVtbl->GetDomain(This,pbstrDomain)
#define IUri_GetExtension(This,pbstrExtension) (This)->lpVtbl->GetExtension(This,pbstrExtension)
#define IUri_GetFragment(This,pbstrFragment) (This)->lpVtbl->GetFragment(This,pbstrFragment)
#define IUri_GetHost(This,pbstrHost) (This)->lpVtbl->GetHost(This,pbstrHost)
#define IUri_GetPassword(This,pbstrPassword) (This)->lpVtbl->GetPassword(This,pbstrPassword)
#define IUri_GetPath(This,pbstrPath) (This)->lpVtbl->GetPath(This,pbstrPath)
#define IUri_GetPathAndQuery(This,pbstrPathAndQuery) (This)->lpVtbl->GetPathAndQuery(This,pbstrPathAndQuery)
#define IUri_GetQuery(This,pbstrQuery) (This)->lpVtbl->GetQuery(This,pbstrQuery)
#define IUri_GetRawUri(This,pbstrRawUri) (This)->lpVtbl->GetRawUri(This,pbstrRawUri)
#define IUri_GetSchemeName(This,pbstrSchemeName) (This)->lpVtbl->GetSchemeName(This,pbstrSchemeName)
#define IUri_GetUserInfo(This,pbstrUserInfo) (This)->lpVtbl->GetUserInfo(This,pbstrUserInfo)
#define IUri_GetUserName(This,pbstrUserName) (This)->lpVtbl->GetUserName(This,pbstrUserName)
#define IUri_GetHostType(This,pdwHostType) (This)->lpVtbl->GetHostType(This,pdwHostType)
#define IUri_GetPort(This,pdwPort) (This)->lpVtbl->GetPort(This,pdwPort)
#define IUri_GetScheme(This,pdwScheme) (This)->lpVtbl->GetScheme(This,pdwScheme)
#define IUri_GetZone(This,pdwZone) (This)->lpVtbl->GetZone(This,pdwZone)
#define IUri_GetProperties(This,pdwFlags) (This)->lpVtbl->GetProperties(This,pdwFlags)
#define IUri_IsEqual(This,pUri,pfEqual) (This)->lpVtbl->IsEqual(This,pUri,pfEqual)
#else
/*** IUnknown methods ***/
static inline HRESULT IUri_QueryInterface(IUri* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUri_AddRef(IUri* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUri_Release(IUri* This) {
    return This->lpVtbl->Release(This);
}
/*** IUri methods ***/
static inline HRESULT IUri_GetPropertyBSTR(IUri* This,Uri_PROPERTY uriProp,BSTR *pbstrProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags);
}
static inline HRESULT IUri_GetPropertyLength(IUri* This,Uri_PROPERTY uriProp,DWORD *pcchProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyLength(This,uriProp,pcchProperty,dwFlags);
}
static inline HRESULT IUri_GetPropertyDWORD(IUri* This,Uri_PROPERTY uriProp,DWORD *pdwProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags);
}
static inline HRESULT IUri_HasProperty(IUri* This,Uri_PROPERTY uriProp,WINBOOL *pfHasProperty) {
    return This->lpVtbl->HasProperty(This,uriProp,pfHasProperty);
}
static inline HRESULT IUri_GetAbsoluteUri(IUri* This,BSTR *pbstrAbsoluteUri) {
    return This->lpVtbl->GetAbsoluteUri(This,pbstrAbsoluteUri);
}
static inline HRESULT IUri_GetAuthority(IUri* This,BSTR *pbstrAuthority) {
    return This->lpVtbl->GetAuthority(This,pbstrAuthority);
}
static inline HRESULT IUri_GetDisplayUri(IUri* This,BSTR *pbstrDisplayString) {
    return This->lpVtbl->GetDisplayUri(This,pbstrDisplayString);
}
static inline HRESULT IUri_GetDomain(IUri* This,BSTR *pbstrDomain) {
    return This->lpVtbl->GetDomain(This,pbstrDomain);
}
static inline HRESULT IUri_GetExtension(IUri* This,BSTR *pbstrExtension) {
    return This->lpVtbl->GetExtension(This,pbstrExtension);
}
static inline HRESULT IUri_GetFragment(IUri* This,BSTR *pbstrFragment) {
    return This->lpVtbl->GetFragment(This,pbstrFragment);
}
static inline HRESULT IUri_GetHost(IUri* This,BSTR *pbstrHost) {
    return This->lpVtbl->GetHost(This,pbstrHost);
}
static inline HRESULT IUri_GetPassword(IUri* This,BSTR *pbstrPassword) {
    return This->lpVtbl->GetPassword(This,pbstrPassword);
}
static inline HRESULT IUri_GetPath(IUri* This,BSTR *pbstrPath) {
    return This->lpVtbl->GetPath(This,pbstrPath);
}
static inline HRESULT IUri_GetPathAndQuery(IUri* This,BSTR *pbstrPathAndQuery) {
    return This->lpVtbl->GetPathAndQuery(This,pbstrPathAndQuery);
}
static inline HRESULT IUri_GetQuery(IUri* This,BSTR *pbstrQuery) {
    return This->lpVtbl->GetQuery(This,pbstrQuery);
}
static inline HRESULT IUri_GetRawUri(IUri* This,BSTR *pbstrRawUri) {
    return This->lpVtbl->GetRawUri(This,pbstrRawUri);
}
static inline HRESULT IUri_GetSchemeName(IUri* This,BSTR *pbstrSchemeName) {
    return This->lpVtbl->GetSchemeName(This,pbstrSchemeName);
}
static inline HRESULT IUri_GetUserInfo(IUri* This,BSTR *pbstrUserInfo) {
    return This->lpVtbl->GetUserInfo(This,pbstrUserInfo);
}
static inline HRESULT IUri_GetUserName(IUri* This,BSTR *pbstrUserName) {
    return This->lpVtbl->GetUserName(This,pbstrUserName);
}
static inline HRESULT IUri_GetHostType(IUri* This,DWORD *pdwHostType) {
    return This->lpVtbl->GetHostType(This,pdwHostType);
}
static inline HRESULT IUri_GetPort(IUri* This,DWORD *pdwPort) {
    return This->lpVtbl->GetPort(This,pdwPort);
}
static inline HRESULT IUri_GetScheme(IUri* This,DWORD *pdwScheme) {
    return This->lpVtbl->GetScheme(This,pdwScheme);
}
static inline HRESULT IUri_GetZone(IUri* This,DWORD *pdwZone) {
    return This->lpVtbl->GetZone(This,pdwZone);
}
static inline HRESULT IUri_GetProperties(IUri* This,LPDWORD pdwFlags) {
    return This->lpVtbl->GetProperties(This,pdwFlags);
}
static inline HRESULT IUri_IsEqual(IUri* This,IUri *pUri,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqual(This,pUri,pfEqual);
}
#endif
#endif

#endif


#endif  /* __IUri_INTERFACE_DEFINED__ */


STDAPI CreateUri(LPCWSTR pwzURI, DWORD dwFlags, DWORD_PTR dwReserved, IUri **ppURI);
STDAPI CreateUriWithFragment(
LPCWSTR pwzURI, LPCWSTR pwzFragment, DWORD dwFlags, DWORD_PTR dwReserved, IUri **ppURI);
#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
STDAPI CreateUriFromMultiByteString(LPCSTR pszANSIInputUri, DWORD dwEncodingFlags, DWORD dwCodePage, DWORD dwCreateFlags, DWORD_PTR dwReserved, IUri **ppUri);
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#define Uri_HAS_ABSOLUTE_URI (1 << Uri_PROPERTY_ABSOLUTE_URI)
#define Uri_HAS_AUTHORITY (1 << Uri_PROPERTY_AUTHORITY)
#define Uri_HAS_DISPLAY_URI (1 << Uri_PROPERTY_DISPLAY_URI)
#define Uri_HAS_DOMAIN (1 << Uri_PROPERTY_DOMAIN)
#define Uri_HAS_EXTENSION (1 << Uri_PROPERTY_EXTENSION)
#define Uri_HAS_FRAGMENT (1 << Uri_PROPERTY_FRAGMENT)
#define Uri_HAS_HOST (1 << Uri_PROPERTY_HOST)
#define Uri_HAS_PASSWORD (1 << Uri_PROPERTY_PASSWORD)
#define Uri_HAS_PATH (1 << Uri_PROPERTY_PATH)
#define Uri_HAS_QUERY (1 << Uri_PROPERTY_QUERY)
#define Uri_HAS_RAW_URI (1 << Uri_PROPERTY_RAW_URI)
#define Uri_HAS_SCHEME_NAME (1 << Uri_PROPERTY_SCHEME_NAME)
#define Uri_HAS_USER_NAME (1 << Uri_PROPERTY_USER_NAME)
#define Uri_HAS_PATH_AND_QUERY (1 << Uri_PROPERTY_PATH_AND_QUERY)
#define Uri_HAS_USER_INFO (1 << Uri_PROPERTY_USER_INFO)
#define Uri_HAS_HOST_TYPE (1 << Uri_PROPERTY_HOST_TYPE)
#define Uri_HAS_PORT (1 << Uri_PROPERTY_PORT)
#define Uri_HAS_SCHEME (1 << Uri_PROPERTY_SCHEME)
#define Uri_HAS_ZONE (1 << Uri_PROPERTY_ZONE)

#define Uri_CREATE_ALLOW_RELATIVE 0x1
#define Uri_CREATE_ALLOW_IMPLICIT_WILDCARD_SCHEME 0x2
#define Uri_CREATE_ALLOW_IMPLICIT_FILE_SCHEME 0x4
#define Uri_CREATE_NOFRAG 0x8
#define Uri_CREATE_NO_CANONICALIZE 0x10
#define Uri_CREATE_CANONICALIZE 0x100
#define Uri_CREATE_FILE_USE_DOS_PATH 0x20
#define Uri_CREATE_DECODE_EXTRA_INFO 0x40
#define Uri_CREATE_NO_DECODE_EXTRA_INFO 0x80
#define Uri_CREATE_CRACK_UNKNOWN_SCHEMES 0x200
#define Uri_CREATE_NO_CRACK_UNKNOWN_SCHEMES 0x400
#define Uri_CREATE_PRE_PROCESS_HTML_URI 0x800
#define Uri_CREATE_NO_PRE_PROCESS_HTML_URI 0x1000
#define Uri_CREATE_IE_SETTINGS 0x2000
#define Uri_CREATE_NO_IE_SETTINGS 0x4000
#define Uri_CREATE_NO_ENCODE_FORBIDDEN_CHARACTERS 0x8000
#define Uri_CREATE_NORMALIZE_INTL_CHARACTERS 0x10000
#define Uri_CREATE_CANONICALIZE_ABSOLUTE 0x20000

#define Uri_DISPLAY_NO_FRAGMENT 0x1
#define Uri_PUNYCODE_IDN_HOST 0x2
#define Uri_DISPLAY_IDN_HOST 0x4
#define Uri_DISPLAY_NO_PUNYCODE 0x8

#define Uri_ENCODING_USER_INFO_AND_PATH_IS_PERCENT_ENCODED_UTF8 0x1
#define Uri_ENCODING_USER_INFO_AND_PATH_IS_CP 0x2
#define Uri_ENCODING_HOST_IS_IDN 0x4
#define Uri_ENCODING_HOST_IS_PERCENT_ENCODED_UTF8 0x8
#define Uri_ENCODING_HOST_IS_PERCENT_ENCODED_CP 0x10
#define Uri_ENCODING_QUERY_AND_FRAGMENT_IS_PERCENT_ENCODED_UTF8 0x20
#define Uri_ENCODING_QUERY_AND_FRAGMENT_IS_CP 0x40

#define Uri_ENCODING_RFC (Uri_ENCODING_USER_INFO_AND_PATH_IS_PERCENT_ENCODED_UTF8 | Uri_ENCODING_HOST_IS_PERCENT_ENCODED_UTF8 | Uri_ENCODING_QUERY_AND_FRAGMENT_IS_PERCENT_ENCODED_UTF8)

#define UriBuilder_USE_ORIGINAL_FLAGS 0x1
#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IUriContainer interface
 */
#ifndef __IUriContainer_INTERFACE_DEFINED__
#define __IUriContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUriContainer, 0xa158a630, 0xed6f, 0x45fb, 0xb9,0x87, 0xf6,0x86,0x76,0xf5,0x77,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a158a630-ed6f-45fb-b987-f68676f57752")
IUriContainer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetIUri(
        IUri **ppIUri) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUriContainer, 0xa158a630, 0xed6f, 0x45fb, 0xb9,0x87, 0xf6,0x86,0x76,0xf5,0x77,0x52)
#endif
#else
typedef struct IUriContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUriContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUriContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUriContainer *This);

    /*** IUriContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIUri)(
        IUriContainer *This,
        IUri **ppIUri);

    END_INTERFACE
} IUriContainerVtbl;

interface IUriContainer {
    CONST_VTBL IUriContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUriContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUriContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUriContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IUriContainer methods ***/
#define IUriContainer_GetIUri(This,ppIUri) (This)->lpVtbl->GetIUri(This,ppIUri)
#else
/*** IUnknown methods ***/
static inline HRESULT IUriContainer_QueryInterface(IUriContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUriContainer_AddRef(IUriContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUriContainer_Release(IUriContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IUriContainer methods ***/
static inline HRESULT IUriContainer_GetIUri(IUriContainer* This,IUri **ppIUri) {
    return This->lpVtbl->GetIUri(This,ppIUri);
}
#endif
#endif

#endif


#endif  /* __IUriContainer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IUriBuilder interface
 */
#ifndef __IUriBuilder_INTERFACE_DEFINED__
#define __IUriBuilder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUriBuilder, 0x4221b2e1, 0x8955, 0x46c0, 0xbd,0x5b, 0xde,0x98,0x97,0x56,0x5d,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4221b2e1-8955-46c0-bd5b-de9897565de7")
IUriBuilder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateUriSimple(
        DWORD dwAllowEncodingPropertyMask,
        DWORD_PTR dwReserved,
        IUri **ppIUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateUri(
        DWORD dwCreateFlags,
        DWORD dwAllowEncodingPropertyMask,
        DWORD_PTR dwReserved,
        IUri **ppIUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateUriWithFlags(
        DWORD dwCreateFlags,
        DWORD dwUriBuilderFlags,
        DWORD dwAllowEncodingPropertyMask,
        DWORD_PTR dwReserved,
        IUri **ppIUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIUri(
        IUri **ppIUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIUri(
        IUri *pIUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFragment(
        DWORD *pcchFragment,
        LPCWSTR *ppwzFragment) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHost(
        DWORD *pcchHost,
        LPCWSTR *ppwzHost) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPassword(
        DWORD *pcchPassword,
        LPCWSTR *ppwzPassword) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPath(
        DWORD *pcchPath,
        LPCWSTR *ppwzPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPort(
        WINBOOL *pfHasPort,
        DWORD *pdwPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetQuery(
        DWORD *pcchQuery,
        LPCWSTR *ppwzQuery) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSchemeName(
        DWORD *pcchSchemeName,
        LPCWSTR *ppwzSchemeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserName(
        DWORD *pcchUserName,
        LPCWSTR *ppwzUserName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFragment(
        LPCWSTR pwzNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHost(
        LPCWSTR pwzNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPassword(
        LPCWSTR pwzNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPath(
        LPCWSTR pwzNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPort(
        WINBOOL fHasPort,
        DWORD dwNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetQuery(
        LPCWSTR pwzNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSchemeName(
        LPCWSTR pwzNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUserName(
        LPCWSTR pwzNewValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveProperties(
        DWORD dwPropertyMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasBeenModified(
        WINBOOL *pfModified) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUriBuilder, 0x4221b2e1, 0x8955, 0x46c0, 0xbd,0x5b, 0xde,0x98,0x97,0x56,0x5d,0xe7)
#endif
#else
typedef struct IUriBuilderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUriBuilder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUriBuilder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUriBuilder *This);

    /*** IUriBuilder methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateUriSimple)(
        IUriBuilder *This,
        DWORD dwAllowEncodingPropertyMask,
        DWORD_PTR dwReserved,
        IUri **ppIUri);

    HRESULT (STDMETHODCALLTYPE *CreateUri)(
        IUriBuilder *This,
        DWORD dwCreateFlags,
        DWORD dwAllowEncodingPropertyMask,
        DWORD_PTR dwReserved,
        IUri **ppIUri);

    HRESULT (STDMETHODCALLTYPE *CreateUriWithFlags)(
        IUriBuilder *This,
        DWORD dwCreateFlags,
        DWORD dwUriBuilderFlags,
        DWORD dwAllowEncodingPropertyMask,
        DWORD_PTR dwReserved,
        IUri **ppIUri);

    HRESULT (STDMETHODCALLTYPE *GetIUri)(
        IUriBuilder *This,
        IUri **ppIUri);

    HRESULT (STDMETHODCALLTYPE *SetIUri)(
        IUriBuilder *This,
        IUri *pIUri);

    HRESULT (STDMETHODCALLTYPE *GetFragment)(
        IUriBuilder *This,
        DWORD *pcchFragment,
        LPCWSTR *ppwzFragment);

    HRESULT (STDMETHODCALLTYPE *GetHost)(
        IUriBuilder *This,
        DWORD *pcchHost,
        LPCWSTR *ppwzHost);

    HRESULT (STDMETHODCALLTYPE *GetPassword)(
        IUriBuilder *This,
        DWORD *pcchPassword,
        LPCWSTR *ppwzPassword);

    HRESULT (STDMETHODCALLTYPE *GetPath)(
        IUriBuilder *This,
        DWORD *pcchPath,
        LPCWSTR *ppwzPath);

    HRESULT (STDMETHODCALLTYPE *GetPort)(
        IUriBuilder *This,
        WINBOOL *pfHasPort,
        DWORD *pdwPort);

    HRESULT (STDMETHODCALLTYPE *GetQuery)(
        IUriBuilder *This,
        DWORD *pcchQuery,
        LPCWSTR *ppwzQuery);

    HRESULT (STDMETHODCALLTYPE *GetSchemeName)(
        IUriBuilder *This,
        DWORD *pcchSchemeName,
        LPCWSTR *ppwzSchemeName);

    HRESULT (STDMETHODCALLTYPE *GetUserName)(
        IUriBuilder *This,
        DWORD *pcchUserName,
        LPCWSTR *ppwzUserName);

    HRESULT (STDMETHODCALLTYPE *SetFragment)(
        IUriBuilder *This,
        LPCWSTR pwzNewValue);

    HRESULT (STDMETHODCALLTYPE *SetHost)(
        IUriBuilder *This,
        LPCWSTR pwzNewValue);

    HRESULT (STDMETHODCALLTYPE *SetPassword)(
        IUriBuilder *This,
        LPCWSTR pwzNewValue);

    HRESULT (STDMETHODCALLTYPE *SetPath)(
        IUriBuilder *This,
        LPCWSTR pwzNewValue);

    HRESULT (STDMETHODCALLTYPE *SetPort)(
        IUriBuilder *This,
        WINBOOL fHasPort,
        DWORD dwNewValue);

    HRESULT (STDMETHODCALLTYPE *SetQuery)(
        IUriBuilder *This,
        LPCWSTR pwzNewValue);

    HRESULT (STDMETHODCALLTYPE *SetSchemeName)(
        IUriBuilder *This,
        LPCWSTR pwzNewValue);

    HRESULT (STDMETHODCALLTYPE *SetUserName)(
        IUriBuilder *This,
        LPCWSTR pwzNewValue);

    HRESULT (STDMETHODCALLTYPE *RemoveProperties)(
        IUriBuilder *This,
        DWORD dwPropertyMask);

    HRESULT (STDMETHODCALLTYPE *HasBeenModified)(
        IUriBuilder *This,
        WINBOOL *pfModified);

    END_INTERFACE
} IUriBuilderVtbl;

interface IUriBuilder {
    CONST_VTBL IUriBuilderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUriBuilder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUriBuilder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUriBuilder_Release(This) (This)->lpVtbl->Release(This)
/*** IUriBuilder methods ***/
#define IUriBuilder_CreateUriSimple(This,dwAllowEncodingPropertyMask,dwReserved,ppIUri) (This)->lpVtbl->CreateUriSimple(This,dwAllowEncodingPropertyMask,dwReserved,ppIUri)
#define IUriBuilder_CreateUri(This,dwCreateFlags,dwAllowEncodingPropertyMask,dwReserved,ppIUri) (This)->lpVtbl->CreateUri(This,dwCreateFlags,dwAllowEncodingPropertyMask,dwReserved,ppIUri)
#define IUriBuilder_CreateUriWithFlags(This,dwCreateFlags,dwUriBuilderFlags,dwAllowEncodingPropertyMask,dwReserved,ppIUri) (This)->lpVtbl->CreateUriWithFlags(This,dwCreateFlags,dwUriBuilderFlags,dwAllowEncodingPropertyMask,dwReserved,ppIUri)
#define IUriBuilder_GetIUri(This,ppIUri) (This)->lpVtbl->GetIUri(This,ppIUri)
#define IUriBuilder_SetIUri(This,pIUri) (This)->lpVtbl->SetIUri(This,pIUri)
#define IUriBuilder_GetFragment(This,pcchFragment,ppwzFragment) (This)->lpVtbl->GetFragment(This,pcchFragment,ppwzFragment)
#define IUriBuilder_GetHost(This,pcchHost,ppwzHost) (This)->lpVtbl->GetHost(This,pcchHost,ppwzHost)
#define IUriBuilder_GetPassword(This,pcchPassword,ppwzPassword) (This)->lpVtbl->GetPassword(This,pcchPassword,ppwzPassword)
#define IUriBuilder_GetPath(This,pcchPath,ppwzPath) (This)->lpVtbl->GetPath(This,pcchPath,ppwzPath)
#define IUriBuilder_GetPort(This,pfHasPort,pdwPort) (This)->lpVtbl->GetPort(This,pfHasPort,pdwPort)
#define IUriBuilder_GetQuery(This,pcchQuery,ppwzQuery) (This)->lpVtbl->GetQuery(This,pcchQuery,ppwzQuery)
#define IUriBuilder_GetSchemeName(This,pcchSchemeName,ppwzSchemeName) (This)->lpVtbl->GetSchemeName(This,pcchSchemeName,ppwzSchemeName)
#define IUriBuilder_GetUserName(This,pcchUserName,ppwzUserName) (This)->lpVtbl->GetUserName(This,pcchUserName,ppwzUserName)
#define IUriBuilder_SetFragment(This,pwzNewValue) (This)->lpVtbl->SetFragment(This,pwzNewValue)
#define IUriBuilder_SetHost(This,pwzNewValue) (This)->lpVtbl->SetHost(This,pwzNewValue)
#define IUriBuilder_SetPassword(This,pwzNewValue) (This)->lpVtbl->SetPassword(This,pwzNewValue)
#define IUriBuilder_SetPath(This,pwzNewValue) (This)->lpVtbl->SetPath(This,pwzNewValue)
#define IUriBuilder_SetPort(This,fHasPort,dwNewValue) (This)->lpVtbl->SetPort(This,fHasPort,dwNewValue)
#define IUriBuilder_SetQuery(This,pwzNewValue) (This)->lpVtbl->SetQuery(This,pwzNewValue)
#define IUriBuilder_SetSchemeName(This,pwzNewValue) (This)->lpVtbl->SetSchemeName(This,pwzNewValue)
#define IUriBuilder_SetUserName(This,pwzNewValue) (This)->lpVtbl->SetUserName(This,pwzNewValue)
#define IUriBuilder_RemoveProperties(This,dwPropertyMask) (This)->lpVtbl->RemoveProperties(This,dwPropertyMask)
#define IUriBuilder_HasBeenModified(This,pfModified) (This)->lpVtbl->HasBeenModified(This,pfModified)
#else
/*** IUnknown methods ***/
static inline HRESULT IUriBuilder_QueryInterface(IUriBuilder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUriBuilder_AddRef(IUriBuilder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUriBuilder_Release(IUriBuilder* This) {
    return This->lpVtbl->Release(This);
}
/*** IUriBuilder methods ***/
static inline HRESULT IUriBuilder_CreateUriSimple(IUriBuilder* This,DWORD dwAllowEncodingPropertyMask,DWORD_PTR dwReserved,IUri **ppIUri) {
    return This->lpVtbl->CreateUriSimple(This,dwAllowEncodingPropertyMask,dwReserved,ppIUri);
}
static inline HRESULT IUriBuilder_CreateUri(IUriBuilder* This,DWORD dwCreateFlags,DWORD dwAllowEncodingPropertyMask,DWORD_PTR dwReserved,IUri **ppIUri) {
    return This->lpVtbl->CreateUri(This,dwCreateFlags,dwAllowEncodingPropertyMask,dwReserved,ppIUri);
}
static inline HRESULT IUriBuilder_CreateUriWithFlags(IUriBuilder* This,DWORD dwCreateFlags,DWORD dwUriBuilderFlags,DWORD dwAllowEncodingPropertyMask,DWORD_PTR dwReserved,IUri **ppIUri) {
    return This->lpVtbl->CreateUriWithFlags(This,dwCreateFlags,dwUriBuilderFlags,dwAllowEncodingPropertyMask,dwReserved,ppIUri);
}
static inline HRESULT IUriBuilder_GetIUri(IUriBuilder* This,IUri **ppIUri) {
    return This->lpVtbl->GetIUri(This,ppIUri);
}
static inline HRESULT IUriBuilder_SetIUri(IUriBuilder* This,IUri *pIUri) {
    return This->lpVtbl->SetIUri(This,pIUri);
}
static inline HRESULT IUriBuilder_GetFragment(IUriBuilder* This,DWORD *pcchFragment,LPCWSTR *ppwzFragment) {
    return This->lpVtbl->GetFragment(This,pcchFragment,ppwzFragment);
}
static inline HRESULT IUriBuilder_GetHost(IUriBuilder* This,DWORD *pcchHost,LPCWSTR *ppwzHost) {
    return This->lpVtbl->GetHost(This,pcchHost,ppwzHost);
}
static inline HRESULT IUriBuilder_GetPassword(IUriBuilder* This,DWORD *pcchPassword,LPCWSTR *ppwzPassword) {
    return This->lpVtbl->GetPassword(This,pcchPassword,ppwzPassword);
}
static inline HRESULT IUriBuilder_GetPath(IUriBuilder* This,DWORD *pcchPath,LPCWSTR *ppwzPath) {
    return This->lpVtbl->GetPath(This,pcchPath,ppwzPath);
}
static inline HRESULT IUriBuilder_GetPort(IUriBuilder* This,WINBOOL *pfHasPort,DWORD *pdwPort) {
    return This->lpVtbl->GetPort(This,pfHasPort,pdwPort);
}
static inline HRESULT IUriBuilder_GetQuery(IUriBuilder* This,DWORD *pcchQuery,LPCWSTR *ppwzQuery) {
    return This->lpVtbl->GetQuery(This,pcchQuery,ppwzQuery);
}
static inline HRESULT IUriBuilder_GetSchemeName(IUriBuilder* This,DWORD *pcchSchemeName,LPCWSTR *ppwzSchemeName) {
    return This->lpVtbl->GetSchemeName(This,pcchSchemeName,ppwzSchemeName);
}
static inline HRESULT IUriBuilder_GetUserName(IUriBuilder* This,DWORD *pcchUserName,LPCWSTR *ppwzUserName) {
    return This->lpVtbl->GetUserName(This,pcchUserName,ppwzUserName);
}
static inline HRESULT IUriBuilder_SetFragment(IUriBuilder* This,LPCWSTR pwzNewValue) {
    return This->lpVtbl->SetFragment(This,pwzNewValue);
}
static inline HRESULT IUriBuilder_SetHost(IUriBuilder* This,LPCWSTR pwzNewValue) {
    return This->lpVtbl->SetHost(This,pwzNewValue);
}
static inline HRESULT IUriBuilder_SetPassword(IUriBuilder* This,LPCWSTR pwzNewValue) {
    return This->lpVtbl->SetPassword(This,pwzNewValue);
}
static inline HRESULT IUriBuilder_SetPath(IUriBuilder* This,LPCWSTR pwzNewValue) {
    return This->lpVtbl->SetPath(This,pwzNewValue);
}
static inline HRESULT IUriBuilder_SetPort(IUriBuilder* This,WINBOOL fHasPort,DWORD dwNewValue) {
    return This->lpVtbl->SetPort(This,fHasPort,dwNewValue);
}
static inline HRESULT IUriBuilder_SetQuery(IUriBuilder* This,LPCWSTR pwzNewValue) {
    return This->lpVtbl->SetQuery(This,pwzNewValue);
}
static inline HRESULT IUriBuilder_SetSchemeName(IUriBuilder* This,LPCWSTR pwzNewValue) {
    return This->lpVtbl->SetSchemeName(This,pwzNewValue);
}
static inline HRESULT IUriBuilder_SetUserName(IUriBuilder* This,LPCWSTR pwzNewValue) {
    return This->lpVtbl->SetUserName(This,pwzNewValue);
}
static inline HRESULT IUriBuilder_RemoveProperties(IUriBuilder* This,DWORD dwPropertyMask) {
    return This->lpVtbl->RemoveProperties(This,dwPropertyMask);
}
static inline HRESULT IUriBuilder_HasBeenModified(IUriBuilder* This,WINBOOL *pfModified) {
    return This->lpVtbl->HasBeenModified(This,pfModified);
}
#endif
#endif

#endif


#endif  /* __IUriBuilder_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IUriBuilderFactory interface
 */
#ifndef __IUriBuilderFactory_INTERFACE_DEFINED__
#define __IUriBuilderFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUriBuilderFactory, 0xe982ce48, 0x0b96, 0x440c, 0xbc,0x37, 0x0c,0x86,0x9b,0x27,0xa2,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e982ce48-0b96-440c-bc37-0c869b27a29e")
IUriBuilderFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateIUriBuilder(
        DWORD dwFlags,
        DWORD_PTR dwReserved,
        IUriBuilder **ppIUriBuilder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInitializedIUriBuilder(
        DWORD dwFlags,
        DWORD_PTR dwReserved,
        IUriBuilder **ppIUriBuilder) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUriBuilderFactory, 0xe982ce48, 0x0b96, 0x440c, 0xbc,0x37, 0x0c,0x86,0x9b,0x27,0xa2,0x9e)
#endif
#else
typedef struct IUriBuilderFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUriBuilderFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUriBuilderFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUriBuilderFactory *This);

    /*** IUriBuilderFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateIUriBuilder)(
        IUriBuilderFactory *This,
        DWORD dwFlags,
        DWORD_PTR dwReserved,
        IUriBuilder **ppIUriBuilder);

    HRESULT (STDMETHODCALLTYPE *CreateInitializedIUriBuilder)(
        IUriBuilderFactory *This,
        DWORD dwFlags,
        DWORD_PTR dwReserved,
        IUriBuilder **ppIUriBuilder);

    END_INTERFACE
} IUriBuilderFactoryVtbl;

interface IUriBuilderFactory {
    CONST_VTBL IUriBuilderFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUriBuilderFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUriBuilderFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUriBuilderFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IUriBuilderFactory methods ***/
#define IUriBuilderFactory_CreateIUriBuilder(This,dwFlags,dwReserved,ppIUriBuilder) (This)->lpVtbl->CreateIUriBuilder(This,dwFlags,dwReserved,ppIUriBuilder)
#define IUriBuilderFactory_CreateInitializedIUriBuilder(This,dwFlags,dwReserved,ppIUriBuilder) (This)->lpVtbl->CreateInitializedIUriBuilder(This,dwFlags,dwReserved,ppIUriBuilder)
#else
/*** IUnknown methods ***/
static inline HRESULT IUriBuilderFactory_QueryInterface(IUriBuilderFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUriBuilderFactory_AddRef(IUriBuilderFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUriBuilderFactory_Release(IUriBuilderFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IUriBuilderFactory methods ***/
static inline HRESULT IUriBuilderFactory_CreateIUriBuilder(IUriBuilderFactory* This,DWORD dwFlags,DWORD_PTR dwReserved,IUriBuilder **ppIUriBuilder) {
    return This->lpVtbl->CreateIUriBuilder(This,dwFlags,dwReserved,ppIUriBuilder);
}
static inline HRESULT IUriBuilderFactory_CreateInitializedIUriBuilder(IUriBuilderFactory* This,DWORD dwFlags,DWORD_PTR dwReserved,IUriBuilder **ppIUriBuilder) {
    return This->lpVtbl->CreateInitializedIUriBuilder(This,dwFlags,dwReserved,ppIUriBuilder);
}
#endif
#endif

#endif


#endif  /* __IUriBuilderFactory_INTERFACE_DEFINED__ */


STDAPI CreateIUriBuilder(IUri *pIUri, DWORD dwFlags, DWORD_PTR dwReserved, IUriBuilder **ppIUriBuilder);
#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef _LPWININETINFO_DEFINED
#define _LPWININETINFO_DEFINED

/*****************************************************************************
 * IWinInetInfo interface
 */
#ifndef __IWinInetInfo_INTERFACE_DEFINED__
#define __IWinInetInfo_INTERFACE_DEFINED__

typedef IWinInetInfo *LPWININETINFO;
DEFINE_GUID(IID_IWinInetInfo, 0x79eac9d6, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9d6-bafa-11ce-8c82-00aa004ba90b")
IWinInetInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryOption(
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD *pcbBuf) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWinInetInfo, 0x79eac9d6, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IWinInetInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWinInetInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWinInetInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWinInetInfo *This);

    /*** IWinInetInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryOption)(
        IWinInetInfo *This,
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD *pcbBuf);

    END_INTERFACE
} IWinInetInfoVtbl;

interface IWinInetInfo {
    CONST_VTBL IWinInetInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWinInetInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWinInetInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWinInetInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWinInetInfo methods ***/
#define IWinInetInfo_QueryOption(This,dwOption,pBuffer,pcbBuf) (This)->lpVtbl->QueryOption(This,dwOption,pBuffer,pcbBuf)
#else
/*** IUnknown methods ***/
static inline HRESULT IWinInetInfo_QueryInterface(IWinInetInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWinInetInfo_AddRef(IWinInetInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWinInetInfo_Release(IWinInetInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWinInetInfo methods ***/
static inline HRESULT IWinInetInfo_QueryOption(IWinInetInfo* This,DWORD dwOption,LPVOID pBuffer,DWORD *pcbBuf) {
    return This->lpVtbl->QueryOption(This,dwOption,pBuffer,pcbBuf);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IWinInetInfo_RemoteQueryOption_Proxy(
    IWinInetInfo* This,
    DWORD dwOption,
    BYTE *pBuffer,
    DWORD *pcbBuf);
void __RPC_STUB IWinInetInfo_RemoteQueryOption_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IWinInetInfo_QueryOption_Proxy(
    IWinInetInfo* This,
    DWORD dwOption,
    LPVOID pBuffer,
    DWORD *pcbBuf);
HRESULT __RPC_STUB IWinInetInfo_QueryOption_Stub(
    IWinInetInfo* This,
    DWORD dwOption,
    BYTE *pBuffer,
    DWORD *pcbBuf);

#endif  /* __IWinInetInfo_INTERFACE_DEFINED__ */

#endif

#define WININETINFO_OPTION_LOCK_HANDLE 65534

#ifndef _LPHTTPSECURITY_DEFINED
#define _LPHTTPSECURITY_DEFINED

/*****************************************************************************
 * IHttpSecurity interface
 */
#ifndef __IHttpSecurity_INTERFACE_DEFINED__
#define __IHttpSecurity_INTERFACE_DEFINED__

typedef IHttpSecurity *LPHTTPSECURITY;

DEFINE_GUID(IID_IHttpSecurity, 0x79eac9d7, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9d7-bafa-11ce-8c82-00aa004ba90b")
IHttpSecurity : public IWindowForBindingUI
{
    virtual HRESULT STDMETHODCALLTYPE OnSecurityProblem(
        DWORD dwProblem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IHttpSecurity, 0x79eac9d7, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IHttpSecurityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IHttpSecurity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IHttpSecurity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IHttpSecurity *This);

    /*** IWindowForBindingUI methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IHttpSecurity *This,
        REFGUID rguidReason,
        HWND *phwnd);

    /*** IHttpSecurity methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSecurityProblem)(
        IHttpSecurity *This,
        DWORD dwProblem);

    END_INTERFACE
} IHttpSecurityVtbl;

interface IHttpSecurity {
    CONST_VTBL IHttpSecurityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IHttpSecurity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IHttpSecurity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IHttpSecurity_Release(This) (This)->lpVtbl->Release(This)
/*** IWindowForBindingUI methods ***/
#define IHttpSecurity_GetWindow(This,rguidReason,phwnd) (This)->lpVtbl->GetWindow(This,rguidReason,phwnd)
/*** IHttpSecurity methods ***/
#define IHttpSecurity_OnSecurityProblem(This,dwProblem) (This)->lpVtbl->OnSecurityProblem(This,dwProblem)
#else
/*** IUnknown methods ***/
static inline HRESULT IHttpSecurity_QueryInterface(IHttpSecurity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IHttpSecurity_AddRef(IHttpSecurity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IHttpSecurity_Release(IHttpSecurity* This) {
    return This->lpVtbl->Release(This);
}
/*** IWindowForBindingUI methods ***/
static inline HRESULT IHttpSecurity_GetWindow(IHttpSecurity* This,REFGUID rguidReason,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,rguidReason,phwnd);
}
/*** IHttpSecurity methods ***/
static inline HRESULT IHttpSecurity_OnSecurityProblem(IHttpSecurity* This,DWORD dwProblem) {
    return This->lpVtbl->OnSecurityProblem(This,dwProblem);
}
#endif
#endif

#endif


#endif  /* __IHttpSecurity_INTERFACE_DEFINED__ */

#endif

#ifndef _LPWININETHTTPINFO_DEFINED
#define _LPWININETHTTPINFO_DEFINED

/*****************************************************************************
 * IWinInetHttpInfo interface
 */
#ifndef __IWinInetHttpInfo_INTERFACE_DEFINED__
#define __IWinInetHttpInfo_INTERFACE_DEFINED__

typedef IWinInetHttpInfo *LPWININETHTTPINFO;

DEFINE_GUID(IID_IWinInetHttpInfo, 0x79eac9d8, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9d8-bafa-11ce-8c82-00aa004ba90b")
IWinInetHttpInfo : public IWinInetInfo
{
    virtual HRESULT STDMETHODCALLTYPE QueryInfo(
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD *pcbBuf,
        DWORD *pdwFlags,
        DWORD *pdwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWinInetHttpInfo, 0x79eac9d8, 0xbafa, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IWinInetHttpInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWinInetHttpInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWinInetHttpInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWinInetHttpInfo *This);

    /*** IWinInetInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryOption)(
        IWinInetHttpInfo *This,
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD *pcbBuf);

    /*** IWinInetHttpInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInfo)(
        IWinInetHttpInfo *This,
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD *pcbBuf,
        DWORD *pdwFlags,
        DWORD *pdwReserved);

    END_INTERFACE
} IWinInetHttpInfoVtbl;

interface IWinInetHttpInfo {
    CONST_VTBL IWinInetHttpInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWinInetHttpInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWinInetHttpInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWinInetHttpInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWinInetInfo methods ***/
#define IWinInetHttpInfo_QueryOption(This,dwOption,pBuffer,pcbBuf) (This)->lpVtbl->QueryOption(This,dwOption,pBuffer,pcbBuf)
/*** IWinInetHttpInfo methods ***/
#define IWinInetHttpInfo_QueryInfo(This,dwOption,pBuffer,pcbBuf,pdwFlags,pdwReserved) (This)->lpVtbl->QueryInfo(This,dwOption,pBuffer,pcbBuf,pdwFlags,pdwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IWinInetHttpInfo_QueryInterface(IWinInetHttpInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWinInetHttpInfo_AddRef(IWinInetHttpInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWinInetHttpInfo_Release(IWinInetHttpInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWinInetInfo methods ***/
static inline HRESULT IWinInetHttpInfo_QueryOption(IWinInetHttpInfo* This,DWORD dwOption,LPVOID pBuffer,DWORD *pcbBuf) {
    return This->lpVtbl->QueryOption(This,dwOption,pBuffer,pcbBuf);
}
/*** IWinInetHttpInfo methods ***/
static inline HRESULT IWinInetHttpInfo_QueryInfo(IWinInetHttpInfo* This,DWORD dwOption,LPVOID pBuffer,DWORD *pcbBuf,DWORD *pdwFlags,DWORD *pdwReserved) {
    return This->lpVtbl->QueryInfo(This,dwOption,pBuffer,pcbBuf,pdwFlags,pdwReserved);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IWinInetHttpInfo_RemoteQueryInfo_Proxy(
    IWinInetHttpInfo* This,
    DWORD dwOption,
    BYTE *pBuffer,
    DWORD *pcbBuf,
    DWORD *pdwFlags,
    DWORD *pdwReserved);
void __RPC_STUB IWinInetHttpInfo_RemoteQueryInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IWinInetHttpInfo_QueryInfo_Proxy(
    IWinInetHttpInfo* This,
    DWORD dwOption,
    LPVOID pBuffer,
    DWORD *pcbBuf,
    DWORD *pdwFlags,
    DWORD *pdwReserved);
HRESULT __RPC_STUB IWinInetHttpInfo_QueryInfo_Stub(
    IWinInetHttpInfo* This,
    DWORD dwOption,
    BYTE *pBuffer,
    DWORD *pcbBuf,
    DWORD *pdwFlags,
    DWORD *pdwReserved);

#endif  /* __IWinInetHttpInfo_INTERFACE_DEFINED__ */

#endif

#ifndef _LPWININETHTTPTIMEOUTS_DEFINED
#define _LPWININETHTTPTIMEOUTS_DEFINED
/*****************************************************************************
 * IWinInetHttpTimeouts interface
 */
#ifndef __IWinInetHttpTimeouts_INTERFACE_DEFINED__
#define __IWinInetHttpTimeouts_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWinInetHttpTimeouts, 0xf286fa56, 0xc1fd, 0x4270, 0x8e,0x67, 0xb3,0xeb,0x79,0x0a,0x81,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f286fa56-c1fd-4270-8e67-b3eb790a81e8")
IWinInetHttpTimeouts : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRequestTimeouts(
        DWORD *pdwConnectTimeout,
        DWORD *pdwSendTimeout,
        DWORD *pdwReceiveTimeout) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWinInetHttpTimeouts, 0xf286fa56, 0xc1fd, 0x4270, 0x8e,0x67, 0xb3,0xeb,0x79,0x0a,0x81,0xe8)
#endif
#else
typedef struct IWinInetHttpTimeoutsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWinInetHttpTimeouts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWinInetHttpTimeouts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWinInetHttpTimeouts *This);

    /*** IWinInetHttpTimeouts methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRequestTimeouts)(
        IWinInetHttpTimeouts *This,
        DWORD *pdwConnectTimeout,
        DWORD *pdwSendTimeout,
        DWORD *pdwReceiveTimeout);

    END_INTERFACE
} IWinInetHttpTimeoutsVtbl;

interface IWinInetHttpTimeouts {
    CONST_VTBL IWinInetHttpTimeoutsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWinInetHttpTimeouts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWinInetHttpTimeouts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWinInetHttpTimeouts_Release(This) (This)->lpVtbl->Release(This)
/*** IWinInetHttpTimeouts methods ***/
#define IWinInetHttpTimeouts_GetRequestTimeouts(This,pdwConnectTimeout,pdwSendTimeout,pdwReceiveTimeout) (This)->lpVtbl->GetRequestTimeouts(This,pdwConnectTimeout,pdwSendTimeout,pdwReceiveTimeout)
#else
/*** IUnknown methods ***/
static inline HRESULT IWinInetHttpTimeouts_QueryInterface(IWinInetHttpTimeouts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWinInetHttpTimeouts_AddRef(IWinInetHttpTimeouts* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWinInetHttpTimeouts_Release(IWinInetHttpTimeouts* This) {
    return This->lpVtbl->Release(This);
}
/*** IWinInetHttpTimeouts methods ***/
static inline HRESULT IWinInetHttpTimeouts_GetRequestTimeouts(IWinInetHttpTimeouts* This,DWORD *pdwConnectTimeout,DWORD *pdwSendTimeout,DWORD *pdwReceiveTimeout) {
    return This->lpVtbl->GetRequestTimeouts(This,pdwConnectTimeout,pdwSendTimeout,pdwReceiveTimeout);
}
#endif
#endif

#endif


#endif  /* __IWinInetHttpTimeouts_INTERFACE_DEFINED__ */

#endif

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#ifndef _LPWININETCACHEHINTS_DEFINED
#define _LPWININETCACHEHINTS_DEFINED

/*****************************************************************************
 * IWinInetCacheHints interface
 */
#ifndef __IWinInetCacheHints_INTERFACE_DEFINED__
#define __IWinInetCacheHints_INTERFACE_DEFINED__

typedef IWinInetCacheHints *LPWININETCACHEHINTS;

DEFINE_GUID(IID_IWinInetCacheHints, 0xdd1ec3b3, 0x8391, 0x4fdb, 0xa9,0xe6, 0x34,0x7c,0x3c,0xaa,0xa7,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dd1ec3b3-8391-4fdb-a9e6-347c3caaa7dd")
IWinInetCacheHints : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetCacheExtension(
        LPCWSTR pwzExt,
        LPVOID pszCacheFile,
        DWORD *pcbCacheFile,
        DWORD *pdwWinInetError,
        DWORD *pdwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWinInetCacheHints, 0xdd1ec3b3, 0x8391, 0x4fdb, 0xa9,0xe6, 0x34,0x7c,0x3c,0xaa,0xa7,0xdd)
#endif
#else
typedef struct IWinInetCacheHintsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWinInetCacheHints *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWinInetCacheHints *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWinInetCacheHints *This);

    /*** IWinInetCacheHints methods ***/
    HRESULT (STDMETHODCALLTYPE *SetCacheExtension)(
        IWinInetCacheHints *This,
        LPCWSTR pwzExt,
        LPVOID pszCacheFile,
        DWORD *pcbCacheFile,
        DWORD *pdwWinInetError,
        DWORD *pdwReserved);

    END_INTERFACE
} IWinInetCacheHintsVtbl;

interface IWinInetCacheHints {
    CONST_VTBL IWinInetCacheHintsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWinInetCacheHints_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWinInetCacheHints_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWinInetCacheHints_Release(This) (This)->lpVtbl->Release(This)
/*** IWinInetCacheHints methods ***/
#define IWinInetCacheHints_SetCacheExtension(This,pwzExt,pszCacheFile,pcbCacheFile,pdwWinInetError,pdwReserved) (This)->lpVtbl->SetCacheExtension(This,pwzExt,pszCacheFile,pcbCacheFile,pdwWinInetError,pdwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IWinInetCacheHints_QueryInterface(IWinInetCacheHints* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWinInetCacheHints_AddRef(IWinInetCacheHints* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWinInetCacheHints_Release(IWinInetCacheHints* This) {
    return This->lpVtbl->Release(This);
}
/*** IWinInetCacheHints methods ***/
static inline HRESULT IWinInetCacheHints_SetCacheExtension(IWinInetCacheHints* This,LPCWSTR pwzExt,LPVOID pszCacheFile,DWORD *pcbCacheFile,DWORD *pdwWinInetError,DWORD *pdwReserved) {
    return This->lpVtbl->SetCacheExtension(This,pwzExt,pszCacheFile,pcbCacheFile,pdwWinInetError,pdwReserved);
}
#endif
#endif

#endif


#endif  /* __IWinInetCacheHints_INTERFACE_DEFINED__ */

#endif
#endif

#if (_WIN32_IE >= _WIN32_IE_IE70)
#ifndef _LPWININETCACHEHINTS2_DEFINED
#define _LPWININETCACHEHINTS2_DEFINED

/*****************************************************************************
 * IWinInetCacheHints2 interface
 */
#ifndef __IWinInetCacheHints2_INTERFACE_DEFINED__
#define __IWinInetCacheHints2_INTERFACE_DEFINED__

typedef IWinInetCacheHints2 *LPWININETCACHEHINTS2;

DEFINE_GUID(IID_IWinInetCacheHints2, 0x7857aeac, 0xd31f, 0x49bf, 0x88,0x4e, 0xdd,0x46,0xdf,0x36,0x78,0x0a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7857aeac-d31f-49bf-884e-dd46df36780a")
IWinInetCacheHints2 : public IWinInetCacheHints
{
    virtual HRESULT STDMETHODCALLTYPE SetCacheExtension2(
        LPCWSTR pwzExt,
        WCHAR *pwzCacheFile,
        DWORD *pcchCacheFile,
        DWORD *pdwWinInetError,
        DWORD *pdwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWinInetCacheHints2, 0x7857aeac, 0xd31f, 0x49bf, 0x88,0x4e, 0xdd,0x46,0xdf,0x36,0x78,0x0a)
#endif
#else
typedef struct IWinInetCacheHints2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWinInetCacheHints2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWinInetCacheHints2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWinInetCacheHints2 *This);

    /*** IWinInetCacheHints methods ***/
    HRESULT (STDMETHODCALLTYPE *SetCacheExtension)(
        IWinInetCacheHints2 *This,
        LPCWSTR pwzExt,
        LPVOID pszCacheFile,
        DWORD *pcbCacheFile,
        DWORD *pdwWinInetError,
        DWORD *pdwReserved);

    /*** IWinInetCacheHints2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetCacheExtension2)(
        IWinInetCacheHints2 *This,
        LPCWSTR pwzExt,
        WCHAR *pwzCacheFile,
        DWORD *pcchCacheFile,
        DWORD *pdwWinInetError,
        DWORD *pdwReserved);

    END_INTERFACE
} IWinInetCacheHints2Vtbl;

interface IWinInetCacheHints2 {
    CONST_VTBL IWinInetCacheHints2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWinInetCacheHints2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWinInetCacheHints2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWinInetCacheHints2_Release(This) (This)->lpVtbl->Release(This)
/*** IWinInetCacheHints methods ***/
#define IWinInetCacheHints2_SetCacheExtension(This,pwzExt,pszCacheFile,pcbCacheFile,pdwWinInetError,pdwReserved) (This)->lpVtbl->SetCacheExtension(This,pwzExt,pszCacheFile,pcbCacheFile,pdwWinInetError,pdwReserved)
/*** IWinInetCacheHints2 methods ***/
#define IWinInetCacheHints2_SetCacheExtension2(This,pwzExt,pwzCacheFile,pcchCacheFile,pdwWinInetError,pdwReserved) (This)->lpVtbl->SetCacheExtension2(This,pwzExt,pwzCacheFile,pcchCacheFile,pdwWinInetError,pdwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IWinInetCacheHints2_QueryInterface(IWinInetCacheHints2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWinInetCacheHints2_AddRef(IWinInetCacheHints2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWinInetCacheHints2_Release(IWinInetCacheHints2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWinInetCacheHints methods ***/
static inline HRESULT IWinInetCacheHints2_SetCacheExtension(IWinInetCacheHints2* This,LPCWSTR pwzExt,LPVOID pszCacheFile,DWORD *pcbCacheFile,DWORD *pdwWinInetError,DWORD *pdwReserved) {
    return This->lpVtbl->SetCacheExtension(This,pwzExt,pszCacheFile,pcbCacheFile,pdwWinInetError,pdwReserved);
}
/*** IWinInetCacheHints2 methods ***/
static inline HRESULT IWinInetCacheHints2_SetCacheExtension2(IWinInetCacheHints2* This,LPCWSTR pwzExt,WCHAR *pwzCacheFile,DWORD *pcchCacheFile,DWORD *pdwWinInetError,DWORD *pdwReserved) {
    return This->lpVtbl->SetCacheExtension2(This,pwzExt,pwzCacheFile,pcchCacheFile,pdwWinInetError,pdwReserved);
}
#endif
#endif

#endif


#endif  /* __IWinInetCacheHints2_INTERFACE_DEFINED__ */

#endif
#endif

#define SID_IBindHost IID_IBindHost
#define SID_SBindHost IID_IBindHost

#ifndef _LPBINDHOST_DEFINED
#define _LPBINDHOST_DEFINED

EXTERN_C const GUID SID_BindHost;

/*****************************************************************************
 * IBindHost interface
 */
#ifndef __IBindHost_INTERFACE_DEFINED__
#define __IBindHost_INTERFACE_DEFINED__

typedef IBindHost *LPBINDHOST;

DEFINE_GUID(IID_IBindHost, 0xfc4801a1, 0x2ba9, 0x11cf, 0xa2,0x29, 0x00,0xaa,0x00,0x3d,0x73,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fc4801a1-2ba9-11cf-a229-00aa003d7352")
IBindHost : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateMoniker(
        LPOLESTR szName,
        IBindCtx *pBC,
        IMoniker **ppmk,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE MonikerBindToStorage(
        IMoniker *pMk,
        IBindCtx *pBC,
        IBindStatusCallback *pBSC,
        REFIID riid,
        void **ppvObj) = 0;

    virtual HRESULT STDMETHODCALLTYPE MonikerBindToObject(
        IMoniker *pMk,
        IBindCtx *pBC,
        IBindStatusCallback *pBSC,
        REFIID riid,
        void **ppvObj) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBindHost, 0xfc4801a1, 0x2ba9, 0x11cf, 0xa2,0x29, 0x00,0xaa,0x00,0x3d,0x73,0x52)
#endif
#else
typedef struct IBindHostVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBindHost *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBindHost *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBindHost *This);

    /*** IBindHost methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateMoniker)(
        IBindHost *This,
        LPOLESTR szName,
        IBindCtx *pBC,
        IMoniker **ppmk,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *MonikerBindToStorage)(
        IBindHost *This,
        IMoniker *pMk,
        IBindCtx *pBC,
        IBindStatusCallback *pBSC,
        REFIID riid,
        void **ppvObj);

    HRESULT (STDMETHODCALLTYPE *MonikerBindToObject)(
        IBindHost *This,
        IMoniker *pMk,
        IBindCtx *pBC,
        IBindStatusCallback *pBSC,
        REFIID riid,
        void **ppvObj);

    END_INTERFACE
} IBindHostVtbl;

interface IBindHost {
    CONST_VTBL IBindHostVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBindHost_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBindHost_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBindHost_Release(This) (This)->lpVtbl->Release(This)
/*** IBindHost methods ***/
#define IBindHost_CreateMoniker(This,szName,pBC,ppmk,dwReserved) (This)->lpVtbl->CreateMoniker(This,szName,pBC,ppmk,dwReserved)
#define IBindHost_MonikerBindToStorage(This,pMk,pBC,pBSC,riid,ppvObj) (This)->lpVtbl->MonikerBindToStorage(This,pMk,pBC,pBSC,riid,ppvObj)
#define IBindHost_MonikerBindToObject(This,pMk,pBC,pBSC,riid,ppvObj) (This)->lpVtbl->MonikerBindToObject(This,pMk,pBC,pBSC,riid,ppvObj)
#else
/*** IUnknown methods ***/
static inline HRESULT IBindHost_QueryInterface(IBindHost* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBindHost_AddRef(IBindHost* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBindHost_Release(IBindHost* This) {
    return This->lpVtbl->Release(This);
}
/*** IBindHost methods ***/
static inline HRESULT IBindHost_CreateMoniker(IBindHost* This,LPOLESTR szName,IBindCtx *pBC,IMoniker **ppmk,DWORD dwReserved) {
    return This->lpVtbl->CreateMoniker(This,szName,pBC,ppmk,dwReserved);
}
static inline HRESULT IBindHost_MonikerBindToStorage(IBindHost* This,IMoniker *pMk,IBindCtx *pBC,IBindStatusCallback *pBSC,REFIID riid,void **ppvObj) {
    return This->lpVtbl->MonikerBindToStorage(This,pMk,pBC,pBSC,riid,ppvObj);
}
static inline HRESULT IBindHost_MonikerBindToObject(IBindHost* This,IMoniker *pMk,IBindCtx *pBC,IBindStatusCallback *pBSC,REFIID riid,void **ppvObj) {
    return This->lpVtbl->MonikerBindToObject(This,pMk,pBC,pBSC,riid,ppvObj);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IBindHost_RemoteMonikerBindToStorage_Proxy(
    IBindHost* This,
    IMoniker *pMk,
    IBindCtx *pBC,
    IBindStatusCallback *pBSC,
    REFIID riid,
    IUnknown **ppvObj);
void __RPC_STUB IBindHost_RemoteMonikerBindToStorage_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IBindHost_RemoteMonikerBindToObject_Proxy(
    IBindHost* This,
    IMoniker *pMk,
    IBindCtx *pBC,
    IBindStatusCallback *pBSC,
    REFIID riid,
    IUnknown **ppvObj);
void __RPC_STUB IBindHost_RemoteMonikerBindToObject_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IBindHost_MonikerBindToStorage_Proxy(
    IBindHost* This,
    IMoniker *pMk,
    IBindCtx *pBC,
    IBindStatusCallback *pBSC,
    REFIID riid,
    void **ppvObj);
HRESULT __RPC_STUB IBindHost_MonikerBindToStorage_Stub(
    IBindHost* This,
    IMoniker *pMk,
    IBindCtx *pBC,
    IBindStatusCallback *pBSC,
    REFIID riid,
    IUnknown **ppvObj);
HRESULT CALLBACK IBindHost_MonikerBindToObject_Proxy(
    IBindHost* This,
    IMoniker *pMk,
    IBindCtx *pBC,
    IBindStatusCallback *pBSC,
    REFIID riid,
    void **ppvObj);
HRESULT __RPC_STUB IBindHost_MonikerBindToObject_Stub(
    IBindHost* This,
    IMoniker *pMk,
    IBindCtx *pBC,
    IBindStatusCallback *pBSC,
    REFIID riid,
    IUnknown **ppvObj);

#endif  /* __IBindHost_INTERFACE_DEFINED__ */

#endif

#define URLOSTRM_USECACHEDCOPY_ONLY 0x1
#define URLOSTRM_USECACHEDCOPY 0x2
#define URLOSTRM_GETNEWESTVERSION 0x3

struct IBindStatusCallback;
STDAPI HlinkSimpleNavigateToString(LPCWSTR szTarget, LPCWSTR szLocation, LPCWSTR szTargetFrameName, IUnknown *pUnk, IBindCtx *pbc, IBindStatusCallback *, DWORD grfHLNF, DWORD dwReserved);
STDAPI HlinkSimpleNavigateToMoniker(IMoniker *pmkTarget, LPCWSTR szLocation, LPCWSTR szTargetFrameName, IUnknown *pUnk, IBindCtx *pbc, IBindStatusCallback *, DWORD grfHLNF, DWORD dwReserved);
STDAPI URLOpenStreamA(LPUNKNOWN,LPCSTR,DWORD,LPBINDSTATUSCALLBACK);
STDAPI URLOpenStreamW(LPUNKNOWN,LPCWSTR,DWORD,LPBINDSTATUSCALLBACK);
STDAPI URLOpenPullStreamA(LPUNKNOWN,LPCSTR,DWORD,LPBINDSTATUSCALLBACK);
STDAPI URLOpenPullStreamW(LPUNKNOWN,LPCWSTR,DWORD,LPBINDSTATUSCALLBACK);
STDAPI URLDownloadToFileA(LPUNKNOWN,LPCSTR,LPCSTR,DWORD,LPBINDSTATUSCALLBACK);
STDAPI URLDownloadToFileW(LPUNKNOWN,LPCWSTR,LPCWSTR,DWORD,LPBINDSTATUSCALLBACK);
STDAPI URLDownloadToCacheFileA(LPUNKNOWN, LPCSTR,  LPSTR,  DWORD, DWORD, LPBINDSTATUSCALLBACK);
STDAPI URLDownloadToCacheFileW(LPUNKNOWN, LPCWSTR, LPWSTR, DWORD, DWORD, LPBINDSTATUSCALLBACK);
STDAPI URLOpenBlockingStreamA(LPUNKNOWN,LPCSTR,LPSTREAM*,DWORD,LPBINDSTATUSCALLBACK);
STDAPI URLOpenBlockingStreamW(LPUNKNOWN,LPCWSTR,LPSTREAM*,DWORD,LPBINDSTATUSCALLBACK);

#define URLOpenStream __MINGW_NAME_AW(URLOpenStream)
#define URLOpenPullStream __MINGW_NAME_AW(URLOpenPullStream)
#define URLDownloadToFile __MINGW_NAME_AW(URLDownloadToFile)
#define URLDownloadToCacheFile __MINGW_NAME_AW(URLDownloadToCacheFile)
#define URLOpenBlockingStream __MINGW_NAME_AW(URLOpenBlockingStream)

STDAPI HlinkGoBack(IUnknown *pUnk);
STDAPI HlinkGoForward(IUnknown *pUnk);
STDAPI HlinkNavigateString(IUnknown *pUnk, LPCWSTR szTarget);
STDAPI HlinkNavigateMoniker(IUnknown *pUnk, IMoniker *pmkTarget);

#ifndef  _URLMON_NO_ASYNC_PLUGABLE_PROTOCOLS_
#ifndef __IInternet_FWD_DEFINED__
#define __IInternet_FWD_DEFINED__
typedef interface IInternet IInternet;
#ifdef __cplusplus
interface IInternet;
#endif /* __cplusplus */
#endif

#ifndef __IInternetBindInfo_FWD_DEFINED__
#define __IInternetBindInfo_FWD_DEFINED__
typedef interface IInternetBindInfo IInternetBindInfo;
#ifdef __cplusplus
interface IInternetBindInfo;
#endif /* __cplusplus */
#endif

#ifndef __IInternetBindInfoEx_FWD_DEFINED__
#define __IInternetBindInfoEx_FWD_DEFINED__
typedef interface IInternetBindInfoEx IInternetBindInfoEx;
#ifdef __cplusplus
interface IInternetBindInfoEx;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolRoot_FWD_DEFINED__
#define __IInternetProtocolRoot_FWD_DEFINED__
typedef interface IInternetProtocolRoot IInternetProtocolRoot;
#ifdef __cplusplus
interface IInternetProtocolRoot;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocol_FWD_DEFINED__
#define __IInternetProtocol_FWD_DEFINED__
typedef interface IInternetProtocol IInternetProtocol;
#ifdef __cplusplus
interface IInternetProtocol;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolEx_FWD_DEFINED__
#define __IInternetProtocolEx_FWD_DEFINED__
typedef interface IInternetProtocolEx IInternetProtocolEx;
#ifdef __cplusplus
interface IInternetProtocolEx;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolSink_FWD_DEFINED__
#define __IInternetProtocolSink_FWD_DEFINED__
typedef interface IInternetProtocolSink IInternetProtocolSink;
#ifdef __cplusplus
interface IInternetProtocolSink;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolInfo_FWD_DEFINED__
#define __IInternetProtocolInfo_FWD_DEFINED__
typedef interface IInternetProtocolInfo IInternetProtocolInfo;
#ifdef __cplusplus
interface IInternetProtocolInfo;
#endif /* __cplusplus */
#endif

#ifndef __IInternetSession_FWD_DEFINED__
#define __IInternetSession_FWD_DEFINED__
typedef interface IInternetSession IInternetSession;
#ifdef __cplusplus
interface IInternetSession;
#endif /* __cplusplus */
#endif

#ifndef __IInternetProtocolSinkStackable_FWD_DEFINED__
#define __IInternetProtocolSinkStackable_FWD_DEFINED__
typedef interface IInternetProtocolSinkStackable IInternetProtocolSinkStackable;
#ifdef __cplusplus
interface IInternetProtocolSinkStackable;
#endif /* __cplusplus */
#endif


#ifndef _LPIINTERNET
#define _LPIINTERNET

/*****************************************************************************
 * IInternet interface
 */
#ifndef __IInternet_INTERFACE_DEFINED__
#define __IInternet_INTERFACE_DEFINED__

typedef IInternet *LPIINTERNET;
DEFINE_GUID(IID_IInternet, 0x79eac9e0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9e0-baf9-11ce-8c82-00aa004ba90b")
IInternet : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternet, 0x79eac9e0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternet *This);

    END_INTERFACE
} IInternetVtbl;

interface IInternet {
    CONST_VTBL IInternetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternet_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternet_QueryInterface(IInternet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternet_AddRef(IInternet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternet_Release(IInternet* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IInternet_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETBINDINFO
#define _LPIINTERNETBINDINFO

/*****************************************************************************
 * IInternetBindInfo interface
 */
#ifndef __IInternetBindInfo_INTERFACE_DEFINED__
#define __IInternetBindInfo_INTERFACE_DEFINED__

typedef IInternetBindInfo *LPIINTERNETBINDINFO;

typedef enum tagBINDSTRING {
    BINDSTRING_HEADERS = 1,
    BINDSTRING_ACCEPT_MIMES = 2,
    BINDSTRING_EXTRA_URL = 3,
    BINDSTRING_LANGUAGE = 4,
    BINDSTRING_USERNAME = 5,
    BINDSTRING_PASSWORD = 6,
    BINDSTRING_UA_PIXELS = 7,
    BINDSTRING_UA_COLOR = 8,
    BINDSTRING_OS = 9,
    BINDSTRING_USER_AGENT = 10,
    BINDSTRING_ACCEPT_ENCODINGS = 11,
    BINDSTRING_POST_COOKIE = 12,
    BINDSTRING_POST_DATA_MIME = 13,
    BINDSTRING_URL = 14,
    BINDSTRING_IID = 15,
    BINDSTRING_FLAG_BIND_TO_OBJECT = 16,
    BINDSTRING_PTR_BIND_CONTEXT = 17,
    BINDSTRING_XDR_ORIGIN = 18,
    BINDSTRING_DOWNLOADPATH = 19,
    BINDSTRING_ROOTDOC_URL = 20,
    BINDSTRING_INITIAL_FILENAME = 21,
    BINDSTRING_PROXY_USERNAME = 22,
    BINDSTRING_PROXY_PASSWORD = 23
} BINDSTRING;

DEFINE_GUID(IID_IInternetBindInfo, 0x79eac9e1, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9e1-baf9-11ce-8c82-00aa004ba90b")
IInternetBindInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetBindInfo(
        DWORD *grfBINDF,
        BINDINFO *pbindinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBindString(
        ULONG ulStringType,
        LPOLESTR *ppwzStr,
        ULONG cEl,
        ULONG *pcElFetched) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetBindInfo, 0x79eac9e1, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetBindInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetBindInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetBindInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetBindInfo *This);

    /*** IInternetBindInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBindInfo)(
        IInternetBindInfo *This,
        DWORD *grfBINDF,
        BINDINFO *pbindinfo);

    HRESULT (STDMETHODCALLTYPE *GetBindString)(
        IInternetBindInfo *This,
        ULONG ulStringType,
        LPOLESTR *ppwzStr,
        ULONG cEl,
        ULONG *pcElFetched);

    END_INTERFACE
} IInternetBindInfoVtbl;

interface IInternetBindInfo {
    CONST_VTBL IInternetBindInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetBindInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetBindInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetBindInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetBindInfo methods ***/
#define IInternetBindInfo_GetBindInfo(This,grfBINDF,pbindinfo) (This)->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo)
#define IInternetBindInfo_GetBindString(This,ulStringType,ppwzStr,cEl,pcElFetched) (This)->lpVtbl->GetBindString(This,ulStringType,ppwzStr,cEl,pcElFetched)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetBindInfo_QueryInterface(IInternetBindInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetBindInfo_AddRef(IInternetBindInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetBindInfo_Release(IInternetBindInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetBindInfo methods ***/
static inline HRESULT IInternetBindInfo_GetBindInfo(IInternetBindInfo* This,DWORD *grfBINDF,BINDINFO *pbindinfo) {
    return This->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo);
}
static inline HRESULT IInternetBindInfo_GetBindString(IInternetBindInfo* This,ULONG ulStringType,LPOLESTR *ppwzStr,ULONG cEl,ULONG *pcElFetched) {
    return This->lpVtbl->GetBindString(This,ulStringType,ppwzStr,cEl,pcElFetched);
}
#endif
#endif

#endif


#endif  /* __IInternetBindInfo_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETBINDINFOEX
#define _LPIINTERNETBINDINFOEX

/*****************************************************************************
 * IInternetBindInfoEx interface
 */
#ifndef __IInternetBindInfoEx_INTERFACE_DEFINED__
#define __IInternetBindInfoEx_INTERFACE_DEFINED__

typedef IInternetBindInfoEx *LPIINTERNETBINDINFOEX;

DEFINE_GUID(IID_IInternetBindInfoEx, 0xa3e015b7, 0xa82c, 0x4dcd, 0xa1,0x50, 0x56,0x9a,0xee,0xed,0x36,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a3e015b7-a82c-4dcd-a150-569aeeed36ab")
IInternetBindInfoEx : public IInternetBindInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetBindInfoEx(
        DWORD *grfBINDF,
        BINDINFO *pbindinfo,
        DWORD *grfBINDF2,
        DWORD *pdwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetBindInfoEx, 0xa3e015b7, 0xa82c, 0x4dcd, 0xa1,0x50, 0x56,0x9a,0xee,0xed,0x36,0xab)
#endif
#else
typedef struct IInternetBindInfoExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetBindInfoEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetBindInfoEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetBindInfoEx *This);

    /*** IInternetBindInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBindInfo)(
        IInternetBindInfoEx *This,
        DWORD *grfBINDF,
        BINDINFO *pbindinfo);

    HRESULT (STDMETHODCALLTYPE *GetBindString)(
        IInternetBindInfoEx *This,
        ULONG ulStringType,
        LPOLESTR *ppwzStr,
        ULONG cEl,
        ULONG *pcElFetched);

    /*** IInternetBindInfoEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBindInfoEx)(
        IInternetBindInfoEx *This,
        DWORD *grfBINDF,
        BINDINFO *pbindinfo,
        DWORD *grfBINDF2,
        DWORD *pdwReserved);

    END_INTERFACE
} IInternetBindInfoExVtbl;

interface IInternetBindInfoEx {
    CONST_VTBL IInternetBindInfoExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetBindInfoEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetBindInfoEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetBindInfoEx_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetBindInfo methods ***/
#define IInternetBindInfoEx_GetBindInfo(This,grfBINDF,pbindinfo) (This)->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo)
#define IInternetBindInfoEx_GetBindString(This,ulStringType,ppwzStr,cEl,pcElFetched) (This)->lpVtbl->GetBindString(This,ulStringType,ppwzStr,cEl,pcElFetched)
/*** IInternetBindInfoEx methods ***/
#define IInternetBindInfoEx_GetBindInfoEx(This,grfBINDF,pbindinfo,grfBINDF2,pdwReserved) (This)->lpVtbl->GetBindInfoEx(This,grfBINDF,pbindinfo,grfBINDF2,pdwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetBindInfoEx_QueryInterface(IInternetBindInfoEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetBindInfoEx_AddRef(IInternetBindInfoEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetBindInfoEx_Release(IInternetBindInfoEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetBindInfo methods ***/
static inline HRESULT IInternetBindInfoEx_GetBindInfo(IInternetBindInfoEx* This,DWORD *grfBINDF,BINDINFO *pbindinfo) {
    return This->lpVtbl->GetBindInfo(This,grfBINDF,pbindinfo);
}
static inline HRESULT IInternetBindInfoEx_GetBindString(IInternetBindInfoEx* This,ULONG ulStringType,LPOLESTR *ppwzStr,ULONG cEl,ULONG *pcElFetched) {
    return This->lpVtbl->GetBindString(This,ulStringType,ppwzStr,cEl,pcElFetched);
}
/*** IInternetBindInfoEx methods ***/
static inline HRESULT IInternetBindInfoEx_GetBindInfoEx(IInternetBindInfoEx* This,DWORD *grfBINDF,BINDINFO *pbindinfo,DWORD *grfBINDF2,DWORD *pdwReserved) {
    return This->lpVtbl->GetBindInfoEx(This,grfBINDF,pbindinfo,grfBINDF2,pdwReserved);
}
#endif
#endif

#endif


#endif  /* __IInternetBindInfoEx_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETPROTOCOLROOT_DEFINED
#define _LPIINTERNETPROTOCOLROOT_DEFINED

/*****************************************************************************
 * IInternetProtocolRoot interface
 */
#ifndef __IInternetProtocolRoot_INTERFACE_DEFINED__
#define __IInternetProtocolRoot_INTERFACE_DEFINED__

typedef IInternetProtocolRoot *LPIINTERNETPROTOCOLROOT;

typedef enum _tagPI_FLAGS {
    PI_PARSE_URL = 0x1,
    PI_FILTER_MODE = 0x2,
    PI_FORCE_ASYNC = 0x4,
    PI_USE_WORKERTHREAD = 0x8,
    PI_MIMEVERIFICATION = 0x10,
    PI_CLSIDLOOKUP = 0x20,
    PI_DATAPROGRESS = 0x40,
    PI_SYNCHRONOUS = 0x80,
    PI_APARTMENTTHREADED = 0x100,
    PI_CLASSINSTALL = 0x200,
    PI_PASSONBINDCTX = 0x2000,
    PI_NOMIMEHANDLER = 0x8000,
    PI_LOADAPPDIRECT = 0x4000,
    PD_FORCE_SWITCH = 0x10000,
    PI_PREFERDEFAULTHANDLER = 0x20000
} PI_FLAGS;
typedef struct _tagPROTOCOLDATA {
    DWORD grfFlags;
    DWORD dwState;
    LPVOID pData;
    ULONG cbData;
} PROTOCOLDATA;

typedef struct _tagStartParam {
    IID iid;
    IBindCtx *pIBindCtx;
    IUnknown *pItf;
} StartParam;

DEFINE_GUID(IID_IInternetProtocolRoot, 0x79eac9e3, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9e3-baf9-11ce-8c82-00aa004ba90b")
IInternetProtocolRoot : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Start(
        LPCWSTR szUrl,
        IInternetProtocolSink *pOIProtSink,
        IInternetBindInfo *pOIBindInfo,
        DWORD grfPI,
        HANDLE_PTR dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE Continue(
        PROTOCOLDATA *pProtocolData) = 0;

    virtual HRESULT STDMETHODCALLTYPE Abort(
        HRESULT hrReason,
        DWORD dwOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE Terminate(
        DWORD dwOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE Suspend(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetProtocolRoot, 0x79eac9e3, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetProtocolRootVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetProtocolRoot *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetProtocolRoot *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetProtocolRoot *This);

    /*** IInternetProtocolRoot methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IInternetProtocolRoot *This,
        LPCWSTR szUrl,
        IInternetProtocolSink *pOIProtSink,
        IInternetBindInfo *pOIBindInfo,
        DWORD grfPI,
        HANDLE_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *Continue)(
        IInternetProtocolRoot *This,
        PROTOCOLDATA *pProtocolData);

    HRESULT (STDMETHODCALLTYPE *Abort)(
        IInternetProtocolRoot *This,
        HRESULT hrReason,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *Terminate)(
        IInternetProtocolRoot *This,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IInternetProtocolRoot *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IInternetProtocolRoot *This);

    END_INTERFACE
} IInternetProtocolRootVtbl;

interface IInternetProtocolRoot {
    CONST_VTBL IInternetProtocolRootVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetProtocolRoot_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetProtocolRoot_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetProtocolRoot_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetProtocolRoot methods ***/
#define IInternetProtocolRoot_Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved) (This)->lpVtbl->Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved)
#define IInternetProtocolRoot_Continue(This,pProtocolData) (This)->lpVtbl->Continue(This,pProtocolData)
#define IInternetProtocolRoot_Abort(This,hrReason,dwOptions) (This)->lpVtbl->Abort(This,hrReason,dwOptions)
#define IInternetProtocolRoot_Terminate(This,dwOptions) (This)->lpVtbl->Terminate(This,dwOptions)
#define IInternetProtocolRoot_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IInternetProtocolRoot_Resume(This) (This)->lpVtbl->Resume(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetProtocolRoot_QueryInterface(IInternetProtocolRoot* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetProtocolRoot_AddRef(IInternetProtocolRoot* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetProtocolRoot_Release(IInternetProtocolRoot* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetProtocolRoot methods ***/
static inline HRESULT IInternetProtocolRoot_Start(IInternetProtocolRoot* This,LPCWSTR szUrl,IInternetProtocolSink *pOIProtSink,IInternetBindInfo *pOIBindInfo,DWORD grfPI,HANDLE_PTR dwReserved) {
    return This->lpVtbl->Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved);
}
static inline HRESULT IInternetProtocolRoot_Continue(IInternetProtocolRoot* This,PROTOCOLDATA *pProtocolData) {
    return This->lpVtbl->Continue(This,pProtocolData);
}
static inline HRESULT IInternetProtocolRoot_Abort(IInternetProtocolRoot* This,HRESULT hrReason,DWORD dwOptions) {
    return This->lpVtbl->Abort(This,hrReason,dwOptions);
}
static inline HRESULT IInternetProtocolRoot_Terminate(IInternetProtocolRoot* This,DWORD dwOptions) {
    return This->lpVtbl->Terminate(This,dwOptions);
}
static inline HRESULT IInternetProtocolRoot_Suspend(IInternetProtocolRoot* This) {
    return This->lpVtbl->Suspend(This);
}
static inline HRESULT IInternetProtocolRoot_Resume(IInternetProtocolRoot* This) {
    return This->lpVtbl->Resume(This);
}
#endif
#endif

#endif


#endif  /* __IInternetProtocolRoot_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETPROTOCOL_DEFINED
#define _LPIINTERNETPROTOCOL_DEFINED

/*****************************************************************************
 * IInternetProtocol interface
 */
#ifndef __IInternetProtocol_INTERFACE_DEFINED__
#define __IInternetProtocol_INTERFACE_DEFINED__

typedef IInternetProtocol *LPIINTERNETPROTOCOL;

DEFINE_GUID(IID_IInternetProtocol, 0x79eac9e4, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9e4-baf9-11ce-8c82-00aa004ba90b")
IInternetProtocol : public IInternetProtocolRoot
{
    virtual HRESULT STDMETHODCALLTYPE Read(
        void *pv,
        ULONG cb,
        ULONG *pcbRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE Seek(
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockRequest(
        DWORD dwOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockRequest(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetProtocol, 0x79eac9e4, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetProtocolVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetProtocol *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetProtocol *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetProtocol *This);

    /*** IInternetProtocolRoot methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IInternetProtocol *This,
        LPCWSTR szUrl,
        IInternetProtocolSink *pOIProtSink,
        IInternetBindInfo *pOIBindInfo,
        DWORD grfPI,
        HANDLE_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *Continue)(
        IInternetProtocol *This,
        PROTOCOLDATA *pProtocolData);

    HRESULT (STDMETHODCALLTYPE *Abort)(
        IInternetProtocol *This,
        HRESULT hrReason,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *Terminate)(
        IInternetProtocol *This,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IInternetProtocol *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IInternetProtocol *This);

    /*** IInternetProtocol methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IInternetProtocol *This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Seek)(
        IInternetProtocol *This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *LockRequest)(
        IInternetProtocol *This,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *UnlockRequest)(
        IInternetProtocol *This);

    END_INTERFACE
} IInternetProtocolVtbl;

interface IInternetProtocol {
    CONST_VTBL IInternetProtocolVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetProtocol_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetProtocol_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetProtocol_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetProtocolRoot methods ***/
#define IInternetProtocol_Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved) (This)->lpVtbl->Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved)
#define IInternetProtocol_Continue(This,pProtocolData) (This)->lpVtbl->Continue(This,pProtocolData)
#define IInternetProtocol_Abort(This,hrReason,dwOptions) (This)->lpVtbl->Abort(This,hrReason,dwOptions)
#define IInternetProtocol_Terminate(This,dwOptions) (This)->lpVtbl->Terminate(This,dwOptions)
#define IInternetProtocol_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IInternetProtocol_Resume(This) (This)->lpVtbl->Resume(This)
/*** IInternetProtocol methods ***/
#define IInternetProtocol_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IInternetProtocol_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IInternetProtocol_LockRequest(This,dwOptions) (This)->lpVtbl->LockRequest(This,dwOptions)
#define IInternetProtocol_UnlockRequest(This) (This)->lpVtbl->UnlockRequest(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetProtocol_QueryInterface(IInternetProtocol* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetProtocol_AddRef(IInternetProtocol* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetProtocol_Release(IInternetProtocol* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetProtocolRoot methods ***/
static inline HRESULT IInternetProtocol_Start(IInternetProtocol* This,LPCWSTR szUrl,IInternetProtocolSink *pOIProtSink,IInternetBindInfo *pOIBindInfo,DWORD grfPI,HANDLE_PTR dwReserved) {
    return This->lpVtbl->Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved);
}
static inline HRESULT IInternetProtocol_Continue(IInternetProtocol* This,PROTOCOLDATA *pProtocolData) {
    return This->lpVtbl->Continue(This,pProtocolData);
}
static inline HRESULT IInternetProtocol_Abort(IInternetProtocol* This,HRESULT hrReason,DWORD dwOptions) {
    return This->lpVtbl->Abort(This,hrReason,dwOptions);
}
static inline HRESULT IInternetProtocol_Terminate(IInternetProtocol* This,DWORD dwOptions) {
    return This->lpVtbl->Terminate(This,dwOptions);
}
static inline HRESULT IInternetProtocol_Suspend(IInternetProtocol* This) {
    return This->lpVtbl->Suspend(This);
}
static inline HRESULT IInternetProtocol_Resume(IInternetProtocol* This) {
    return This->lpVtbl->Resume(This);
}
/*** IInternetProtocol methods ***/
static inline HRESULT IInternetProtocol_Read(IInternetProtocol* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static inline HRESULT IInternetProtocol_Seek(IInternetProtocol* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static inline HRESULT IInternetProtocol_LockRequest(IInternetProtocol* This,DWORD dwOptions) {
    return This->lpVtbl->LockRequest(This,dwOptions);
}
static inline HRESULT IInternetProtocol_UnlockRequest(IInternetProtocol* This) {
    return This->lpVtbl->UnlockRequest(This);
}
#endif
#endif

#endif


#endif  /* __IInternetProtocol_INTERFACE_DEFINED__ */

#endif

#if (_WIN32_IE >= _WIN32_IE_IE70)
#ifndef _LPIINTERNETPROTOCOLEX_DEFINED
#define _LPIINTERNETPROTOCOLEX_DEFINED

/*****************************************************************************
 * IInternetProtocolEx interface
 */
#ifndef __IInternetProtocolEx_INTERFACE_DEFINED__
#define __IInternetProtocolEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternetProtocolEx, 0xc7a98e66, 0x1010, 0x492c, 0xa1,0xc8, 0xc8,0x09,0xe1,0xf7,0x59,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c7a98e66-1010-492c-a1c8-c809e1f75905")
IInternetProtocolEx : public IInternetProtocol
{
    virtual HRESULT STDMETHODCALLTYPE StartEx(
        IUri *pUri,
        IInternetProtocolSink *pOIProtSink,
        IInternetBindInfo *pOIBindInfo,
        DWORD grfPI,
        HANDLE_PTR dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetProtocolEx, 0xc7a98e66, 0x1010, 0x492c, 0xa1,0xc8, 0xc8,0x09,0xe1,0xf7,0x59,0x05)
#endif
#else
typedef struct IInternetProtocolExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetProtocolEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetProtocolEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetProtocolEx *This);

    /*** IInternetProtocolRoot methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IInternetProtocolEx *This,
        LPCWSTR szUrl,
        IInternetProtocolSink *pOIProtSink,
        IInternetBindInfo *pOIBindInfo,
        DWORD grfPI,
        HANDLE_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *Continue)(
        IInternetProtocolEx *This,
        PROTOCOLDATA *pProtocolData);

    HRESULT (STDMETHODCALLTYPE *Abort)(
        IInternetProtocolEx *This,
        HRESULT hrReason,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *Terminate)(
        IInternetProtocolEx *This,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IInternetProtocolEx *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IInternetProtocolEx *This);

    /*** IInternetProtocol methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IInternetProtocolEx *This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Seek)(
        IInternetProtocolEx *This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *LockRequest)(
        IInternetProtocolEx *This,
        DWORD dwOptions);

    HRESULT (STDMETHODCALLTYPE *UnlockRequest)(
        IInternetProtocolEx *This);

    /*** IInternetProtocolEx methods ***/
    HRESULT (STDMETHODCALLTYPE *StartEx)(
        IInternetProtocolEx *This,
        IUri *pUri,
        IInternetProtocolSink *pOIProtSink,
        IInternetBindInfo *pOIBindInfo,
        DWORD grfPI,
        HANDLE_PTR dwReserved);

    END_INTERFACE
} IInternetProtocolExVtbl;

interface IInternetProtocolEx {
    CONST_VTBL IInternetProtocolExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetProtocolEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetProtocolEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetProtocolEx_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetProtocolRoot methods ***/
#define IInternetProtocolEx_Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved) (This)->lpVtbl->Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved)
#define IInternetProtocolEx_Continue(This,pProtocolData) (This)->lpVtbl->Continue(This,pProtocolData)
#define IInternetProtocolEx_Abort(This,hrReason,dwOptions) (This)->lpVtbl->Abort(This,hrReason,dwOptions)
#define IInternetProtocolEx_Terminate(This,dwOptions) (This)->lpVtbl->Terminate(This,dwOptions)
#define IInternetProtocolEx_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IInternetProtocolEx_Resume(This) (This)->lpVtbl->Resume(This)
/*** IInternetProtocol methods ***/
#define IInternetProtocolEx_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IInternetProtocolEx_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IInternetProtocolEx_LockRequest(This,dwOptions) (This)->lpVtbl->LockRequest(This,dwOptions)
#define IInternetProtocolEx_UnlockRequest(This) (This)->lpVtbl->UnlockRequest(This)
/*** IInternetProtocolEx methods ***/
#define IInternetProtocolEx_StartEx(This,pUri,pOIProtSink,pOIBindInfo,grfPI,dwReserved) (This)->lpVtbl->StartEx(This,pUri,pOIProtSink,pOIBindInfo,grfPI,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetProtocolEx_QueryInterface(IInternetProtocolEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetProtocolEx_AddRef(IInternetProtocolEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetProtocolEx_Release(IInternetProtocolEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetProtocolRoot methods ***/
static inline HRESULT IInternetProtocolEx_Start(IInternetProtocolEx* This,LPCWSTR szUrl,IInternetProtocolSink *pOIProtSink,IInternetBindInfo *pOIBindInfo,DWORD grfPI,HANDLE_PTR dwReserved) {
    return This->lpVtbl->Start(This,szUrl,pOIProtSink,pOIBindInfo,grfPI,dwReserved);
}
static inline HRESULT IInternetProtocolEx_Continue(IInternetProtocolEx* This,PROTOCOLDATA *pProtocolData) {
    return This->lpVtbl->Continue(This,pProtocolData);
}
static inline HRESULT IInternetProtocolEx_Abort(IInternetProtocolEx* This,HRESULT hrReason,DWORD dwOptions) {
    return This->lpVtbl->Abort(This,hrReason,dwOptions);
}
static inline HRESULT IInternetProtocolEx_Terminate(IInternetProtocolEx* This,DWORD dwOptions) {
    return This->lpVtbl->Terminate(This,dwOptions);
}
static inline HRESULT IInternetProtocolEx_Suspend(IInternetProtocolEx* This) {
    return This->lpVtbl->Suspend(This);
}
static inline HRESULT IInternetProtocolEx_Resume(IInternetProtocolEx* This) {
    return This->lpVtbl->Resume(This);
}
/*** IInternetProtocol methods ***/
static inline HRESULT IInternetProtocolEx_Read(IInternetProtocolEx* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static inline HRESULT IInternetProtocolEx_Seek(IInternetProtocolEx* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static inline HRESULT IInternetProtocolEx_LockRequest(IInternetProtocolEx* This,DWORD dwOptions) {
    return This->lpVtbl->LockRequest(This,dwOptions);
}
static inline HRESULT IInternetProtocolEx_UnlockRequest(IInternetProtocolEx* This) {
    return This->lpVtbl->UnlockRequest(This);
}
/*** IInternetProtocolEx methods ***/
static inline HRESULT IInternetProtocolEx_StartEx(IInternetProtocolEx* This,IUri *pUri,IInternetProtocolSink *pOIProtSink,IInternetBindInfo *pOIBindInfo,DWORD grfPI,HANDLE_PTR dwReserved) {
    return This->lpVtbl->StartEx(This,pUri,pOIProtSink,pOIBindInfo,grfPI,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IInternetProtocolEx_INTERFACE_DEFINED__ */

#endif
#endif

#ifndef _LPIINTERNETPROTOCOLSINK_DEFINED
#define _LPIINTERNETPROTOCOLSINK_DEFINED
/*****************************************************************************
 * IInternetProtocolSink interface
 */
#ifndef __IInternetProtocolSink_INTERFACE_DEFINED__
#define __IInternetProtocolSink_INTERFACE_DEFINED__

typedef IInternetProtocolSink *LPIINTERNETPROTOCOLSINK;

DEFINE_GUID(IID_IInternetProtocolSink, 0x79eac9e5, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9e5-baf9-11ce-8c82-00aa004ba90b")
IInternetProtocolSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Switch(
        PROTOCOLDATA *pProtocolData) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReportProgress(
        ULONG ulStatusCode,
        LPCWSTR szStatusText) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReportData(
        DWORD grfBSCF,
        ULONG ulProgress,
        ULONG ulProgressMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReportResult(
        HRESULT hrResult,
        DWORD dwError,
        LPCWSTR szResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetProtocolSink, 0x79eac9e5, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetProtocolSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetProtocolSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetProtocolSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetProtocolSink *This);

    /*** IInternetProtocolSink methods ***/
    HRESULT (STDMETHODCALLTYPE *Switch)(
        IInternetProtocolSink *This,
        PROTOCOLDATA *pProtocolData);

    HRESULT (STDMETHODCALLTYPE *ReportProgress)(
        IInternetProtocolSink *This,
        ULONG ulStatusCode,
        LPCWSTR szStatusText);

    HRESULT (STDMETHODCALLTYPE *ReportData)(
        IInternetProtocolSink *This,
        DWORD grfBSCF,
        ULONG ulProgress,
        ULONG ulProgressMax);

    HRESULT (STDMETHODCALLTYPE *ReportResult)(
        IInternetProtocolSink *This,
        HRESULT hrResult,
        DWORD dwError,
        LPCWSTR szResult);

    END_INTERFACE
} IInternetProtocolSinkVtbl;

interface IInternetProtocolSink {
    CONST_VTBL IInternetProtocolSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetProtocolSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetProtocolSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetProtocolSink_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetProtocolSink methods ***/
#define IInternetProtocolSink_Switch(This,pProtocolData) (This)->lpVtbl->Switch(This,pProtocolData)
#define IInternetProtocolSink_ReportProgress(This,ulStatusCode,szStatusText) (This)->lpVtbl->ReportProgress(This,ulStatusCode,szStatusText)
#define IInternetProtocolSink_ReportData(This,grfBSCF,ulProgress,ulProgressMax) (This)->lpVtbl->ReportData(This,grfBSCF,ulProgress,ulProgressMax)
#define IInternetProtocolSink_ReportResult(This,hrResult,dwError,szResult) (This)->lpVtbl->ReportResult(This,hrResult,dwError,szResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetProtocolSink_QueryInterface(IInternetProtocolSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetProtocolSink_AddRef(IInternetProtocolSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetProtocolSink_Release(IInternetProtocolSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetProtocolSink methods ***/
static inline HRESULT IInternetProtocolSink_Switch(IInternetProtocolSink* This,PROTOCOLDATA *pProtocolData) {
    return This->lpVtbl->Switch(This,pProtocolData);
}
static inline HRESULT IInternetProtocolSink_ReportProgress(IInternetProtocolSink* This,ULONG ulStatusCode,LPCWSTR szStatusText) {
    return This->lpVtbl->ReportProgress(This,ulStatusCode,szStatusText);
}
static inline HRESULT IInternetProtocolSink_ReportData(IInternetProtocolSink* This,DWORD grfBSCF,ULONG ulProgress,ULONG ulProgressMax) {
    return This->lpVtbl->ReportData(This,grfBSCF,ulProgress,ulProgressMax);
}
static inline HRESULT IInternetProtocolSink_ReportResult(IInternetProtocolSink* This,HRESULT hrResult,DWORD dwError,LPCWSTR szResult) {
    return This->lpVtbl->ReportResult(This,hrResult,dwError,szResult);
}
#endif
#endif

#endif


#endif  /* __IInternetProtocolSink_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETPROTOCOLSINKSTACKABLE_DEFINED
#define _LPIINTERNETPROTOCOLSINKSTACKABLE_DEFINED

/*****************************************************************************
 * IInternetProtocolSinkStackable interface
 */
#ifndef __IInternetProtocolSinkStackable_INTERFACE_DEFINED__
#define __IInternetProtocolSinkStackable_INTERFACE_DEFINED__

typedef IInternetProtocolSinkStackable *LPIINTERNETPROTOCOLSINKStackable;

DEFINE_GUID(IID_IInternetProtocolSinkStackable, 0x79eac9f0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9f0-baf9-11ce-8c82-00aa004ba90b")
IInternetProtocolSinkStackable : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SwitchSink(
        IInternetProtocolSink *pOIProtSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitSwitch(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RollbackSwitch(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetProtocolSinkStackable, 0x79eac9f0, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetProtocolSinkStackableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetProtocolSinkStackable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetProtocolSinkStackable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetProtocolSinkStackable *This);

    /*** IInternetProtocolSinkStackable methods ***/
    HRESULT (STDMETHODCALLTYPE *SwitchSink)(
        IInternetProtocolSinkStackable *This,
        IInternetProtocolSink *pOIProtSink);

    HRESULT (STDMETHODCALLTYPE *CommitSwitch)(
        IInternetProtocolSinkStackable *This);

    HRESULT (STDMETHODCALLTYPE *RollbackSwitch)(
        IInternetProtocolSinkStackable *This);

    END_INTERFACE
} IInternetProtocolSinkStackableVtbl;

interface IInternetProtocolSinkStackable {
    CONST_VTBL IInternetProtocolSinkStackableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetProtocolSinkStackable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetProtocolSinkStackable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetProtocolSinkStackable_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetProtocolSinkStackable methods ***/
#define IInternetProtocolSinkStackable_SwitchSink(This,pOIProtSink) (This)->lpVtbl->SwitchSink(This,pOIProtSink)
#define IInternetProtocolSinkStackable_CommitSwitch(This) (This)->lpVtbl->CommitSwitch(This)
#define IInternetProtocolSinkStackable_RollbackSwitch(This) (This)->lpVtbl->RollbackSwitch(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetProtocolSinkStackable_QueryInterface(IInternetProtocolSinkStackable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetProtocolSinkStackable_AddRef(IInternetProtocolSinkStackable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetProtocolSinkStackable_Release(IInternetProtocolSinkStackable* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetProtocolSinkStackable methods ***/
static inline HRESULT IInternetProtocolSinkStackable_SwitchSink(IInternetProtocolSinkStackable* This,IInternetProtocolSink *pOIProtSink) {
    return This->lpVtbl->SwitchSink(This,pOIProtSink);
}
static inline HRESULT IInternetProtocolSinkStackable_CommitSwitch(IInternetProtocolSinkStackable* This) {
    return This->lpVtbl->CommitSwitch(This);
}
static inline HRESULT IInternetProtocolSinkStackable_RollbackSwitch(IInternetProtocolSinkStackable* This) {
    return This->lpVtbl->RollbackSwitch(This);
}
#endif
#endif

#endif


#endif  /* __IInternetProtocolSinkStackable_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETSESSION_DEFINED
#define _LPIINTERNETSESSION_DEFINED

/*****************************************************************************
 * IInternetSession interface
 */
#ifndef __IInternetSession_INTERFACE_DEFINED__
#define __IInternetSession_INTERFACE_DEFINED__

typedef IInternetSession *LPIINTERNETSESSION;

typedef enum _tagOIBDG_FLAGS {
    OIBDG_APARTMENTTHREADED = 0x100,
    OIBDG_DATAONLY = 0x1000
} OIBDG_FLAGS;

DEFINE_GUID(IID_IInternetSession, 0x79eac9e7, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9e7-baf9-11ce-8c82-00aa004ba90b")
IInternetSession : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterNameSpace(
        IClassFactory *pCF,
        REFCLSID rclsid,
        LPCWSTR pwzProtocol,
        ULONG cPatterns,
        const LPCWSTR *ppwzPatterns,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterNameSpace(
        IClassFactory *pCF,
        LPCWSTR pszProtocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterMimeFilter(
        IClassFactory *pCF,
        REFCLSID rclsid,
        LPCWSTR pwzType) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterMimeFilter(
        IClassFactory *pCF,
        LPCWSTR pwzType) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBinding(
        LPBC pBC,
        LPCWSTR szUrl,
        IUnknown *pUnkOuter,
        IUnknown **ppUnk,
        IInternetProtocol **ppOInetProt,
        DWORD dwOption) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSessionOption(
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD dwBufferLength,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionOption(
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD *pdwBufferLength,
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetSession, 0x79eac9e7, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetSession *This);

    /*** IInternetSession methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterNameSpace)(
        IInternetSession *This,
        IClassFactory *pCF,
        REFCLSID rclsid,
        LPCWSTR pwzProtocol,
        ULONG cPatterns,
        const LPCWSTR *ppwzPatterns,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *UnregisterNameSpace)(
        IInternetSession *This,
        IClassFactory *pCF,
        LPCWSTR pszProtocol);

    HRESULT (STDMETHODCALLTYPE *RegisterMimeFilter)(
        IInternetSession *This,
        IClassFactory *pCF,
        REFCLSID rclsid,
        LPCWSTR pwzType);

    HRESULT (STDMETHODCALLTYPE *UnregisterMimeFilter)(
        IInternetSession *This,
        IClassFactory *pCF,
        LPCWSTR pwzType);

    HRESULT (STDMETHODCALLTYPE *CreateBinding)(
        IInternetSession *This,
        LPBC pBC,
        LPCWSTR szUrl,
        IUnknown *pUnkOuter,
        IUnknown **ppUnk,
        IInternetProtocol **ppOInetProt,
        DWORD dwOption);

    HRESULT (STDMETHODCALLTYPE *SetSessionOption)(
        IInternetSession *This,
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD dwBufferLength,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *GetSessionOption)(
        IInternetSession *This,
        DWORD dwOption,
        LPVOID pBuffer,
        DWORD *pdwBufferLength,
        DWORD dwReserved);

    END_INTERFACE
} IInternetSessionVtbl;

interface IInternetSession {
    CONST_VTBL IInternetSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetSession_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetSession methods ***/
#define IInternetSession_RegisterNameSpace(This,pCF,rclsid,pwzProtocol,cPatterns,ppwzPatterns,dwReserved) (This)->lpVtbl->RegisterNameSpace(This,pCF,rclsid,pwzProtocol,cPatterns,ppwzPatterns,dwReserved)
#define IInternetSession_UnregisterNameSpace(This,pCF,pszProtocol) (This)->lpVtbl->UnregisterNameSpace(This,pCF,pszProtocol)
#define IInternetSession_RegisterMimeFilter(This,pCF,rclsid,pwzType) (This)->lpVtbl->RegisterMimeFilter(This,pCF,rclsid,pwzType)
#define IInternetSession_UnregisterMimeFilter(This,pCF,pwzType) (This)->lpVtbl->UnregisterMimeFilter(This,pCF,pwzType)
#define IInternetSession_CreateBinding(This,pBC,szUrl,pUnkOuter,ppUnk,ppOInetProt,dwOption) (This)->lpVtbl->CreateBinding(This,pBC,szUrl,pUnkOuter,ppUnk,ppOInetProt,dwOption)
#define IInternetSession_SetSessionOption(This,dwOption,pBuffer,dwBufferLength,dwReserved) (This)->lpVtbl->SetSessionOption(This,dwOption,pBuffer,dwBufferLength,dwReserved)
#define IInternetSession_GetSessionOption(This,dwOption,pBuffer,pdwBufferLength,dwReserved) (This)->lpVtbl->GetSessionOption(This,dwOption,pBuffer,pdwBufferLength,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetSession_QueryInterface(IInternetSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetSession_AddRef(IInternetSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetSession_Release(IInternetSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetSession methods ***/
static inline HRESULT IInternetSession_RegisterNameSpace(IInternetSession* This,IClassFactory *pCF,REFCLSID rclsid,LPCWSTR pwzProtocol,ULONG cPatterns,const LPCWSTR *ppwzPatterns,DWORD dwReserved) {
    return This->lpVtbl->RegisterNameSpace(This,pCF,rclsid,pwzProtocol,cPatterns,ppwzPatterns,dwReserved);
}
static inline HRESULT IInternetSession_UnregisterNameSpace(IInternetSession* This,IClassFactory *pCF,LPCWSTR pszProtocol) {
    return This->lpVtbl->UnregisterNameSpace(This,pCF,pszProtocol);
}
static inline HRESULT IInternetSession_RegisterMimeFilter(IInternetSession* This,IClassFactory *pCF,REFCLSID rclsid,LPCWSTR pwzType) {
    return This->lpVtbl->RegisterMimeFilter(This,pCF,rclsid,pwzType);
}
static inline HRESULT IInternetSession_UnregisterMimeFilter(IInternetSession* This,IClassFactory *pCF,LPCWSTR pwzType) {
    return This->lpVtbl->UnregisterMimeFilter(This,pCF,pwzType);
}
static inline HRESULT IInternetSession_CreateBinding(IInternetSession* This,LPBC pBC,LPCWSTR szUrl,IUnknown *pUnkOuter,IUnknown **ppUnk,IInternetProtocol **ppOInetProt,DWORD dwOption) {
    return This->lpVtbl->CreateBinding(This,pBC,szUrl,pUnkOuter,ppUnk,ppOInetProt,dwOption);
}
static inline HRESULT IInternetSession_SetSessionOption(IInternetSession* This,DWORD dwOption,LPVOID pBuffer,DWORD dwBufferLength,DWORD dwReserved) {
    return This->lpVtbl->SetSessionOption(This,dwOption,pBuffer,dwBufferLength,dwReserved);
}
static inline HRESULT IInternetSession_GetSessionOption(IInternetSession* This,DWORD dwOption,LPVOID pBuffer,DWORD *pdwBufferLength,DWORD dwReserved) {
    return This->lpVtbl->GetSessionOption(This,dwOption,pBuffer,pdwBufferLength,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IInternetSession_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETTHREADSWITCH_DEFINED
#define _LPIINTERNETTHREADSWITCH_DEFINED

/*****************************************************************************
 * IInternetThreadSwitch interface
 */
#ifndef __IInternetThreadSwitch_INTERFACE_DEFINED__
#define __IInternetThreadSwitch_INTERFACE_DEFINED__

typedef IInternetThreadSwitch *LPIINTERNETTHREADSWITCH;

DEFINE_GUID(IID_IInternetThreadSwitch, 0x79eac9e8, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9e8-baf9-11ce-8c82-00aa004ba90b")
IInternetThreadSwitch : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Prepare(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Continue(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetThreadSwitch, 0x79eac9e8, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetThreadSwitchVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetThreadSwitch *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetThreadSwitch *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetThreadSwitch *This);

    /*** IInternetThreadSwitch methods ***/
    HRESULT (STDMETHODCALLTYPE *Prepare)(
        IInternetThreadSwitch *This);

    HRESULT (STDMETHODCALLTYPE *Continue)(
        IInternetThreadSwitch *This);

    END_INTERFACE
} IInternetThreadSwitchVtbl;

interface IInternetThreadSwitch {
    CONST_VTBL IInternetThreadSwitchVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetThreadSwitch_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetThreadSwitch_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetThreadSwitch_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetThreadSwitch methods ***/
#define IInternetThreadSwitch_Prepare(This) (This)->lpVtbl->Prepare(This)
#define IInternetThreadSwitch_Continue(This) (This)->lpVtbl->Continue(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetThreadSwitch_QueryInterface(IInternetThreadSwitch* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetThreadSwitch_AddRef(IInternetThreadSwitch* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetThreadSwitch_Release(IInternetThreadSwitch* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetThreadSwitch methods ***/
static inline HRESULT IInternetThreadSwitch_Prepare(IInternetThreadSwitch* This) {
    return This->lpVtbl->Prepare(This);
}
static inline HRESULT IInternetThreadSwitch_Continue(IInternetThreadSwitch* This) {
    return This->lpVtbl->Continue(This);
}
#endif
#endif

#endif


#endif  /* __IInternetThreadSwitch_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETPRIORITY_DEFINED
#define _LPIINTERNETPRIORITY_DEFINED

/*****************************************************************************
 * IInternetPriority interface
 */
#ifndef __IInternetPriority_INTERFACE_DEFINED__
#define __IInternetPriority_INTERFACE_DEFINED__

typedef IInternetPriority *LPIINTERNETPRIORITY;

DEFINE_GUID(IID_IInternetPriority, 0x79eac9eb, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9eb-baf9-11ce-8c82-00aa004ba90b")
IInternetPriority : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetPriority(
        LONG nPriority) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPriority(
        LONG *pnPriority) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetPriority, 0x79eac9eb, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetPriorityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetPriority *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetPriority *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetPriority *This);

    /*** IInternetPriority methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPriority)(
        IInternetPriority *This,
        LONG nPriority);

    HRESULT (STDMETHODCALLTYPE *GetPriority)(
        IInternetPriority *This,
        LONG *pnPriority);

    END_INTERFACE
} IInternetPriorityVtbl;

interface IInternetPriority {
    CONST_VTBL IInternetPriorityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetPriority_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetPriority_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetPriority_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetPriority methods ***/
#define IInternetPriority_SetPriority(This,nPriority) (This)->lpVtbl->SetPriority(This,nPriority)
#define IInternetPriority_GetPriority(This,pnPriority) (This)->lpVtbl->GetPriority(This,pnPriority)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetPriority_QueryInterface(IInternetPriority* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetPriority_AddRef(IInternetPriority* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetPriority_Release(IInternetPriority* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetPriority methods ***/
static inline HRESULT IInternetPriority_SetPriority(IInternetPriority* This,LONG nPriority) {
    return This->lpVtbl->SetPriority(This,nPriority);
}
static inline HRESULT IInternetPriority_GetPriority(IInternetPriority* This,LONG *pnPriority) {
    return This->lpVtbl->GetPriority(This,pnPriority);
}
#endif
#endif

#endif


#endif  /* __IInternetPriority_INTERFACE_DEFINED__ */

#endif

#ifndef _LPIINTERNETPROTOCOLINFO_DEFINED
#define _LPIINTERNETPROTOCOLINFO_DEFINED

/*****************************************************************************
 * IInternetProtocolInfo interface
 */
#ifndef __IInternetProtocolInfo_INTERFACE_DEFINED__
#define __IInternetProtocolInfo_INTERFACE_DEFINED__

typedef IInternetProtocolInfo *LPIINTERNETPROTOCOLINFO;

typedef enum _tagPARSEACTION {
    PARSE_CANONICALIZE = 1,
    PARSE_FRIENDLY = 2,
    PARSE_SECURITY_URL = 3,
    PARSE_ROOTDOCUMENT = 4,
    PARSE_DOCUMENT = 5,
    PARSE_ANCHOR = 6,
    PARSE_ENCODE_IS_UNESCAPE = 7,
    PARSE_DECODE_IS_ESCAPE = 8,
    PARSE_PATH_FROM_URL = 9,
    PARSE_URL_FROM_PATH = 10,
    PARSE_MIME = 11,
    PARSE_SERVER = 12,
    PARSE_SCHEMA = 13,
    PARSE_SITE = 14,
    PARSE_DOMAIN = 15,
    PARSE_LOCATION = 16,
    PARSE_SECURITY_DOMAIN = 17,
    PARSE_ESCAPE = 18,
    PARSE_UNESCAPE = 19
} PARSEACTION;

typedef enum _tagPSUACTION {
    PSU_DEFAULT = 1,
    PSU_SECURITY_URL_ONLY = 2
} PSUACTION;

typedef enum _tagQUERYOPTION {
    QUERY_EXPIRATION_DATE = 1,
    QUERY_TIME_OF_LAST_CHANGE = 2,
    QUERY_CONTENT_ENCODING = 3,
    QUERY_CONTENT_TYPE = 4,
    QUERY_REFRESH = 5,
    QUERY_RECOMBINE = 6,
    QUERY_CAN_NAVIGATE = 7,
    QUERY_USES_NETWORK = 8,
    QUERY_IS_CACHED = 9,
    QUERY_IS_INSTALLEDENTRY = 10,
    QUERY_IS_CACHED_OR_MAPPED = 11,
    QUERY_USES_CACHE = 12,
    QUERY_IS_SECURE = 13,
    QUERY_IS_SAFE = 14,
    QUERY_USES_HISTORYFOLDER = 15,
    QUERY_IS_CACHED_AND_USABLE_OFFLINE = 16
} QUERYOPTION;

DEFINE_GUID(IID_IInternetProtocolInfo, 0x79eac9ec, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9ec-baf9-11ce-8c82-00aa004ba90b")
IInternetProtocolInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseUrl(
        LPCWSTR pwzUrl,
        PARSEACTION ParseAction,
        DWORD dwParseFlags,
        LPWSTR pwzResult,
        DWORD cchResult,
        DWORD *pcchResult,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE CombineUrl(
        LPCWSTR pwzBaseUrl,
        LPCWSTR pwzRelativeUrl,
        DWORD dwCombineFlags,
        LPWSTR pwzResult,
        DWORD cchResult,
        DWORD *pcchResult,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompareUrl(
        LPCWSTR pwzUrl1,
        LPCWSTR pwzUrl2,
        DWORD dwCompareFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryInfo(
        LPCWSTR pwzUrl,
        QUERYOPTION OueryOption,
        DWORD dwQueryFlags,
        LPVOID pBuffer,
        DWORD cbBuffer,
        DWORD *pcbBuf,
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetProtocolInfo, 0x79eac9ec, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetProtocolInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetProtocolInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetProtocolInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetProtocolInfo *This);

    /*** IInternetProtocolInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseUrl)(
        IInternetProtocolInfo *This,
        LPCWSTR pwzUrl,
        PARSEACTION ParseAction,
        DWORD dwParseFlags,
        LPWSTR pwzResult,
        DWORD cchResult,
        DWORD *pcchResult,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *CombineUrl)(
        IInternetProtocolInfo *This,
        LPCWSTR pwzBaseUrl,
        LPCWSTR pwzRelativeUrl,
        DWORD dwCombineFlags,
        LPWSTR pwzResult,
        DWORD cchResult,
        DWORD *pcchResult,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *CompareUrl)(
        IInternetProtocolInfo *This,
        LPCWSTR pwzUrl1,
        LPCWSTR pwzUrl2,
        DWORD dwCompareFlags);

    HRESULT (STDMETHODCALLTYPE *QueryInfo)(
        IInternetProtocolInfo *This,
        LPCWSTR pwzUrl,
        QUERYOPTION OueryOption,
        DWORD dwQueryFlags,
        LPVOID pBuffer,
        DWORD cbBuffer,
        DWORD *pcbBuf,
        DWORD dwReserved);

    END_INTERFACE
} IInternetProtocolInfoVtbl;

interface IInternetProtocolInfo {
    CONST_VTBL IInternetProtocolInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetProtocolInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetProtocolInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetProtocolInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetProtocolInfo methods ***/
#define IInternetProtocolInfo_ParseUrl(This,pwzUrl,ParseAction,dwParseFlags,pwzResult,cchResult,pcchResult,dwReserved) (This)->lpVtbl->ParseUrl(This,pwzUrl,ParseAction,dwParseFlags,pwzResult,cchResult,pcchResult,dwReserved)
#define IInternetProtocolInfo_CombineUrl(This,pwzBaseUrl,pwzRelativeUrl,dwCombineFlags,pwzResult,cchResult,pcchResult,dwReserved) (This)->lpVtbl->CombineUrl(This,pwzBaseUrl,pwzRelativeUrl,dwCombineFlags,pwzResult,cchResult,pcchResult,dwReserved)
#define IInternetProtocolInfo_CompareUrl(This,pwzUrl1,pwzUrl2,dwCompareFlags) (This)->lpVtbl->CompareUrl(This,pwzUrl1,pwzUrl2,dwCompareFlags)
#define IInternetProtocolInfo_QueryInfo(This,pwzUrl,OueryOption,dwQueryFlags,pBuffer,cbBuffer,pcbBuf,dwReserved) (This)->lpVtbl->QueryInfo(This,pwzUrl,OueryOption,dwQueryFlags,pBuffer,cbBuffer,pcbBuf,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetProtocolInfo_QueryInterface(IInternetProtocolInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetProtocolInfo_AddRef(IInternetProtocolInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetProtocolInfo_Release(IInternetProtocolInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetProtocolInfo methods ***/
static inline HRESULT IInternetProtocolInfo_ParseUrl(IInternetProtocolInfo* This,LPCWSTR pwzUrl,PARSEACTION ParseAction,DWORD dwParseFlags,LPWSTR pwzResult,DWORD cchResult,DWORD *pcchResult,DWORD dwReserved) {
    return This->lpVtbl->ParseUrl(This,pwzUrl,ParseAction,dwParseFlags,pwzResult,cchResult,pcchResult,dwReserved);
}
static inline HRESULT IInternetProtocolInfo_CombineUrl(IInternetProtocolInfo* This,LPCWSTR pwzBaseUrl,LPCWSTR pwzRelativeUrl,DWORD dwCombineFlags,LPWSTR pwzResult,DWORD cchResult,DWORD *pcchResult,DWORD dwReserved) {
    return This->lpVtbl->CombineUrl(This,pwzBaseUrl,pwzRelativeUrl,dwCombineFlags,pwzResult,cchResult,pcchResult,dwReserved);
}
static inline HRESULT IInternetProtocolInfo_CompareUrl(IInternetProtocolInfo* This,LPCWSTR pwzUrl1,LPCWSTR pwzUrl2,DWORD dwCompareFlags) {
    return This->lpVtbl->CompareUrl(This,pwzUrl1,pwzUrl2,dwCompareFlags);
}
static inline HRESULT IInternetProtocolInfo_QueryInfo(IInternetProtocolInfo* This,LPCWSTR pwzUrl,QUERYOPTION OueryOption,DWORD dwQueryFlags,LPVOID pBuffer,DWORD cbBuffer,DWORD *pcbBuf,DWORD dwReserved) {
    return This->lpVtbl->QueryInfo(This,pwzUrl,OueryOption,dwQueryFlags,pBuffer,cbBuffer,pcbBuf,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IInternetProtocolInfo_INTERFACE_DEFINED__ */


#ifndef URLMON_STRICT
#define PARSE_ENCODE PARSE_ENCODE_IS_UNESCAPE
#define PARSE_DECODE PARSE_DECODE_IS_ESCAPE
#endif
#endif

#define IOInet               IInternet
#define IOInetBindInfo       IInternetBindInfo
#define IOInetBindInfoEx     IInternetBindInfoEx
#define IOInetProtocolRoot   IInternetProtocolRoot
#define IOInetProtocol       IInternetProtocol

#if (_WIN32_IE >= _WIN32_IE_IE70)
#define IOInetProtocolEx     IInternetProtocolEx
#endif
#define IOInetProtocolSink   IInternetProtocolSink
#define IOInetProtocolInfo   IInternetProtocolInfo
#define IOInetSession        IInternetSession
#define IOInetPriority       IInternetPriority
#define IOInetThreadSwitch   IInternetThreadSwitch
#define IOInetProtocolSinkStackable   IInternetProtocolSinkStackable

#define LPOINET              LPIINTERNET
#define LPOINETPROTOCOLINFO  LPIINTERNETPROTOCOLINFO
#define LPOINETBINDINFO      LPIINTERNETBINDINFO
#define LPOINETPROTOCOLROOT  LPIINTERNETPROTOCOLROOT
#define LPOINETPROTOCOL      LPIINTERNETPROTOCOL

#if (_WIN32_IE >= _WIN32_IE_IE70)
#define LPOINETPROTOCOLEX LPIINTERNETPROTOCOLEX
#endif
#define LPOINETPROTOCOLSINK  LPIINTERNETPROTOCOLSINK
#define LPOINETSESSION       LPIINTERNETSESSION
#define LPOINETTHREADSWITCH  LPIINTERNETTHREADSWITCH
#define LPOINETPRIORITY      LPIINTERNETPRIORITY
#define LPOINETPROTOCOLINFO  LPIINTERNETPROTOCOLINFO
#define LPOINETPROTOCOLSINKSTACKABLE  LPIINTERNETPROTOCOLSINKSTACKABLE

#define IID_IOInet               IID_IInternet
#define IID_IOInetBindInfo       IID_IInternetBindInfo
#define IID_IOInetBindInfoEx     IID_IInternetBindInfoEx
#define IID_IOInetProtocolRoot   IID_IInternetProtocolRoot
#define IID_IOInetProtocol       IID_IInternetProtocol

#if (_WIN32_IE >= _WIN32_IE_IE70)
#define IID_IOInetProtocolEx IID_IInternetProtocolEx
#endif
#define IID_IOInetProtocolSink   IID_IInternetProtocolSink
#define IID_IOInetProtocolInfo   IID_IInternetProtocolInfo
#define IID_IOInetSession        IID_IInternetSession
#define IID_IOInetPriority       IID_IInternetPriority
#define IID_IOInetThreadSwitch   IID_IInternetThreadSwitch
#define IID_IOInetProtocolSinkStackable   IID_IInternetProtocolSinkStackable

STDAPI CoInternetParseUrl(LPCWSTR pwzUrl, PARSEACTION ParseAction, DWORD dwFlags, LPWSTR pszResult, DWORD cchResult, DWORD *pcchResult, DWORD dwReserved);
#if (_WIN32_IE >= _WIN32_IE_IE70)
STDAPI CoInternetParseIUri(IUri *pIUri, PARSEACTION ParseAction, DWORD dwFlags, LPWSTR pwzResult, DWORD cchResult, DWORD *pcchResult, DWORD_PTR dwReserved);
#endif
STDAPI CoInternetCombineUrl(LPCWSTR pwzBaseUrl, LPCWSTR pwzRelativeUrl, DWORD dwCombineFlags, LPWSTR pszResult, DWORD cchResult, DWORD *pcchResult, DWORD dwReserved);
#if (_WIN32_IE >= _WIN32_IE_IE70)
STDAPI CoInternetCombineUrlEx(IUri *pBaseUri, LPCWSTR pwzRelativeUrl, DWORD dwCombineFlags, IUri **ppCombinedUri, DWORD_PTR dwReserved);
STDAPI CoInternetCombineIUri (IUri *pBaseUri, IUri *pRelativeUri, DWORD dwCombineFlags, IUri **ppCombinedUri, DWORD_PTR dwReserved);
#endif
STDAPI CoInternetCompareUrl(LPCWSTR pwzUrl1, LPCWSTR pwzUrl2, DWORD dwFlags);
STDAPI CoInternetGetProtocolFlags(LPCWSTR pwzUrl, DWORD *pdwFlags, DWORD dwReserved);
STDAPI CoInternetQueryInfo(LPCWSTR pwzUrl, QUERYOPTION QueryOptions, DWORD dwQueryFlags, LPVOID pvBuffer, DWORD cbBuffer, DWORD *pcbBuffer, DWORD dwReserved);
STDAPI CoInternetGetSession(DWORD dwSessionMode, IInternetSession **ppIInternetSession, DWORD dwReserved);
STDAPI CoInternetGetSecurityUrl(LPCWSTR pwszUrl, LPWSTR *ppwszSecUrl, PSUACTION psuAction, DWORD dwReserved);
STDAPI AsyncInstallDistributionUnit(LPCWSTR szDistUnit, LPCWSTR szTYPE, LPCWSTR szExt, DWORD dwFileVersionMS, DWORD dwFileVersionLS, LPCWSTR szURL,IBindCtx *pbc, LPVOID pvReserved,DWORD flags);
#if (_WIN32_IE >= _WIN32_IE_IE70)
STDAPI CoInternetGetSecurityUrlEx(IUri *pUri, IUri **ppSecUri, PSUACTION psuAction, DWORD_PTR dwReserved);
#endif

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#ifndef _INTERNETFEATURELIST_DEFINED
#define _INTERNETFEATURELIST_DEFINED

typedef enum _tagINTERNETFEATURELIST {
    FEATURE_OBJECT_CACHING = 0,
    FEATURE_ZONE_ELEVATION = 1,
    FEATURE_MIME_HANDLING = 2,
    FEATURE_MIME_SNIFFING = 3,
    FEATURE_WINDOW_RESTRICTIONS = 4,
    FEATURE_WEBOC_POPUPMANAGEMENT = 5,
    FEATURE_BEHAVIORS = 6,
    FEATURE_DISABLE_MK_PROTOCOL = 7,
    FEATURE_LOCALMACHINE_LOCKDOWN = 8,
    FEATURE_SECURITYBAND = 9,
    FEATURE_RESTRICT_ACTIVEXINSTALL = 10,
    FEATURE_VALIDATE_NAVIGATE_URL = 11,
    FEATURE_RESTRICT_FILEDOWNLOAD = 12,
    FEATURE_ADDON_MANAGEMENT = 13,
    FEATURE_PROTOCOL_LOCKDOWN = 14,
    FEATURE_HTTP_USERNAME_PASSWORD_DISABLE = 15,
    FEATURE_SAFE_BINDTOOBJECT = 16,
    FEATURE_UNC_SAVEDFILECHECK = 17,
    FEATURE_GET_URL_DOM_FILEPATH_UNENCODED = 18,
    FEATURE_TABBED_BROWSING = 19,
    FEATURE_SSLUX = 20,
    FEATURE_DISABLE_NAVIGATION_SOUNDS = 21,
    FEATURE_DISABLE_LEGACY_COMPRESSION = 22,
    FEATURE_FORCE_ADDR_AND_STATUS = 23,
    FEATURE_XMLHTTP = 24,
    FEATURE_DISABLE_TELNET_PROTOCOL = 25,
    FEATURE_FEEDS = 26,
    FEATURE_BLOCK_INPUT_PROMPTS = 27,
    FEATURE_ENTRY_COUNT = 28
} INTERNETFEATURELIST;

#define SET_FEATURE_ON_THREAD 0x1
#define SET_FEATURE_ON_PROCESS 0x2
#define SET_FEATURE_IN_REGISTRY 0x4
#define SET_FEATURE_ON_THREAD_LOCALMACHINE 0x8
#define SET_FEATURE_ON_THREAD_INTRANET 0x10
#define SET_FEATURE_ON_THREAD_TRUSTED 0x20
#define SET_FEATURE_ON_THREAD_INTERNET 0x40
#define SET_FEATURE_ON_THREAD_RESTRICTED 0x80

#define GET_FEATURE_FROM_THREAD 0x1
#define GET_FEATURE_FROM_PROCESS 0x2
#define GET_FEATURE_FROM_REGISTRY 0x4
#define GET_FEATURE_FROM_THREAD_LOCALMACHINE 0x8
#define GET_FEATURE_FROM_THREAD_INTRANET 0x10
#define GET_FEATURE_FROM_THREAD_TRUSTED 0x20
#define GET_FEATURE_FROM_THREAD_INTERNET 0x40
#define GET_FEATURE_FROM_THREAD_RESTRICTED 0x80
#endif

STDAPI CoInternetSetFeatureEnabled(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags, WINBOOL fEnable);
STDAPI CoInternetIsFeatureEnabled(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags);
STDAPI CoInternetIsFeatureEnabledForUrl(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags, LPCWSTR szURL, IInternetSecurityManager *pSecMgr);
STDAPI CoInternetIsFeatureEnabledForIUri(INTERNETFEATURELIST FeatureEntry, DWORD dwFlags, IUri *pIUri, IInternetSecurityManagerEx2 *pSecMgr);
STDAPI CoInternetIsFeatureZoneElevationEnabled(LPCWSTR szFromURL, LPCWSTR szToURL, IInternetSecurityManager *pSecMgr, DWORD dwFlags);
#endif
STDAPI CopyStgMedium(const STGMEDIUM *pcstgmedSrc, STGMEDIUM *pstgmedDest);
STDAPI CopyBindInfo(const BINDINFO *pcbiSrc, BINDINFO *pbiDest);
STDAPI_(void) ReleaseBindInfo(BINDINFO *pbindinfo);

#define INET_E_USE_DEFAULT_PROTOCOLHANDLER _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0011))
#define INET_E_USE_DEFAULT_SETTING         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0012))
#define INET_E_DEFAULT_ACTION              INET_E_USE_DEFAULT_PROTOCOLHANDLER
#define INET_E_QUERYOPTION_UNKNOWN         _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0013))
#define INET_E_REDIRECTING                 _HRESULT_TYPEDEF_(__MSABI_LONG(0x800C0014))

#define OInetParseUrl CoInternetParseUrl
#define OInetCombineUrl CoInternetCombineUrl
#if (_WIN32_IE >= _WIN32_IE_IE70)
#define OInetCombineUrlEx CoInternetCombineUrlEx
#define OInetCombineIUri CoInternetCombineIUri
#endif
#define OInetCompareUrl CoInternetCompareUrl
#define OInetQueryInfo CoInternetQueryInfo
#define OInetGetSession CoInternetGetSession
#endif

#define PROTOCOLFLAG_NO_PICS_CHECK 0x1

STDAPI CoInternetCreateSecurityManager(IServiceProvider *pSP, IInternetSecurityManager **ppSM, DWORD dwReserved);
STDAPI CoInternetCreateZoneManager(IServiceProvider *pSP, IInternetZoneManager **ppZM, DWORD dwReserved);

EXTERN_C const IID CLSID_InternetSecurityManager;
EXTERN_C const IID CLSID_InternetZoneManager;

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
EXTERN_C const IID CLSID_PersistentZoneIdentifier;
#endif

#define SID_SInternetSecurityManager IID_IInternetSecurityManager
#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#define SID_SInternetSecurityManagerEx IID_IInternetSecurityManagerEx
#endif
#if (_WIN32_IE >= _WIN32_IE_IE70)
#define SID_SInternetSecurityManagerEx2 IID_IInternetSecurityManagerEx2
#endif
#define SID_SInternetHostSecurityManager IID_IInternetHostSecurityManager

#ifndef _LPINTERNETSECURITYMGRSITE_DEFINED
#define _LPINTERNETSECURITYMGRSITE_DEFINED

/*****************************************************************************
 * IInternetSecurityMgrSite interface
 */
#ifndef __IInternetSecurityMgrSite_INTERFACE_DEFINED__
#define __IInternetSecurityMgrSite_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternetSecurityMgrSite, 0x79eac9ed, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9ed-baf9-11ce-8c82-00aa004ba90b")
IInternetSecurityMgrSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetWindow(
        HWND *phwnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableModeless(
        WINBOOL fEnable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetSecurityMgrSite, 0x79eac9ed, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetSecurityMgrSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetSecurityMgrSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetSecurityMgrSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetSecurityMgrSite *This);

    /*** IInternetSecurityMgrSite methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IInternetSecurityMgrSite *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *EnableModeless)(
        IInternetSecurityMgrSite *This,
        WINBOOL fEnable);

    END_INTERFACE
} IInternetSecurityMgrSiteVtbl;

interface IInternetSecurityMgrSite {
    CONST_VTBL IInternetSecurityMgrSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetSecurityMgrSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetSecurityMgrSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetSecurityMgrSite_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetSecurityMgrSite methods ***/
#define IInternetSecurityMgrSite_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IInternetSecurityMgrSite_EnableModeless(This,fEnable) (This)->lpVtbl->EnableModeless(This,fEnable)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetSecurityMgrSite_QueryInterface(IInternetSecurityMgrSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetSecurityMgrSite_AddRef(IInternetSecurityMgrSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetSecurityMgrSite_Release(IInternetSecurityMgrSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetSecurityMgrSite methods ***/
static inline HRESULT IInternetSecurityMgrSite_GetWindow(IInternetSecurityMgrSite* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IInternetSecurityMgrSite_EnableModeless(IInternetSecurityMgrSite* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableModeless(This,fEnable);
}
#endif
#endif

#endif


#endif  /* __IInternetSecurityMgrSite_INTERFACE_DEFINED__ */

#endif

#ifndef _LPINTERNETSECURITYMANANGER_DEFINED
#define _LPINTERNETSECURITYMANANGER_DEFINED

/*****************************************************************************
 * IInternetSecurityManager interface
 */
#ifndef __IInternetSecurityManager_INTERFACE_DEFINED__
#define __IInternetSecurityManager_INTERFACE_DEFINED__


#define MUTZ_NOSAVEDFILECHECK 0x1
#define MUTZ_ISFILE 0x2
#define MUTZ_ACCEPT_WILDCARD_SCHEME 0x80
#define MUTZ_ENFORCERESTRICTED 0x100
#define MUTZ_RESERVED 0x200
#define MUTZ_REQUIRESAVEDFILECHECK 0x400
#define MUTZ_DONT_UNESCAPE 0x800
#define MUTZ_DONT_USE_CACHE 0x1000
#define MUTZ_FORCE_INTRANET_FLAGS 0x2000
#define MUTZ_IGNORE_ZONE_MAPPINGS 0x4000


#define MAX_SIZE_SECURITY_ID 512


typedef enum __WIDL_urlmon_generated_name_0000001B {
    PUAF_DEFAULT = 0x0,
    PUAF_NOUI = 0x1,
    PUAF_ISFILE = 0x2,
    PUAF_WARN_IF_DENIED = 0x4,
    PUAF_FORCEUI_FOREGROUND = 0x8,
    PUAF_CHECK_TIFS = 0x10,
    PUAF_DONTCHECKBOXINDIALOG = 0x20,
    PUAF_TRUSTED = 0x40,
    PUAF_ACCEPT_WILDCARD_SCHEME = 0x80,
    PUAF_ENFORCERESTRICTED = 0x100,
    PUAF_NOSAVEDFILECHECK = 0x200,
    PUAF_REQUIRESAVEDFILECHECK = 0x400,
    PUAF_DONT_USE_CACHE = 0x1000,
    PUAF_RESERVED1 = 0x2000,
    PUAF_RESERVED2 = 0x4000,
    PUAF_LMZ_UNLOCKED = 0x10000,
    PUAF_LMZ_LOCKED = 0x20000,
    PUAF_DEFAULTZONEPOL = 0x40000,
    PUAF_NPL_USE_LOCKED_IF_RESTRICTED = 0x80000,
    PUAF_NOUIIFLOCKED = 0x100000,
    PUAF_DRAGPROTOCOLCHECK = 0x200000
} PUAF;
typedef enum __WIDL_urlmon_generated_name_0000001C {
    PUAFOUT_DEFAULT = 0x0,
    PUAFOUT_ISLOCKZONEPOLICY = 0x1
} PUAFOUT;


typedef enum __WIDL_urlmon_generated_name_0000001D {
    SZM_CREATE = 0x0,
    SZM_DELETE = 0x1
} SZM_FLAGS;

DEFINE_GUID(IID_IInternetSecurityManager, 0x79eac9ee, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9ee-baf9-11ce-8c82-00aa004ba90b")
IInternetSecurityManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetSecuritySite(
        IInternetSecurityMgrSite *pSite) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSecuritySite(
        IInternetSecurityMgrSite **ppSite) = 0;

    virtual HRESULT STDMETHODCALLTYPE MapUrlToZone(
        LPCWSTR pwszUrl,
        DWORD *pdwZone,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSecurityId(
        LPCWSTR pwszUrl,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessUrlAction(
        LPCWSTR pwszUrl,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryCustomPolicy(
        LPCWSTR pwszUrl,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetZoneMapping(
        DWORD dwZone,
        LPCWSTR lpszPattern,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetZoneMappings(
        DWORD dwZone,
        IEnumString **ppenumString,
        DWORD dwFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetSecurityManager, 0x79eac9ee, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetSecurityManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetSecurityManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetSecurityManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetSecurityManager *This);

    /*** IInternetSecurityManager methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSecuritySite)(
        IInternetSecurityManager *This,
        IInternetSecurityMgrSite *pSite);

    HRESULT (STDMETHODCALLTYPE *GetSecuritySite)(
        IInternetSecurityManager *This,
        IInternetSecurityMgrSite **ppSite);

    HRESULT (STDMETHODCALLTYPE *MapUrlToZone)(
        IInternetSecurityManager *This,
        LPCWSTR pwszUrl,
        DWORD *pdwZone,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetSecurityId)(
        IInternetSecurityManager *This,
        LPCWSTR pwszUrl,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *ProcessUrlAction)(
        IInternetSecurityManager *This,
        LPCWSTR pwszUrl,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *QueryCustomPolicy)(
        IInternetSecurityManager *This,
        LPCWSTR pwszUrl,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *SetZoneMapping)(
        IInternetSecurityManager *This,
        DWORD dwZone,
        LPCWSTR lpszPattern,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetZoneMappings)(
        IInternetSecurityManager *This,
        DWORD dwZone,
        IEnumString **ppenumString,
        DWORD dwFlags);

    END_INTERFACE
} IInternetSecurityManagerVtbl;

interface IInternetSecurityManager {
    CONST_VTBL IInternetSecurityManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetSecurityManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetSecurityManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetSecurityManager_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetSecurityManager methods ***/
#define IInternetSecurityManager_SetSecuritySite(This,pSite) (This)->lpVtbl->SetSecuritySite(This,pSite)
#define IInternetSecurityManager_GetSecuritySite(This,ppSite) (This)->lpVtbl->GetSecuritySite(This,ppSite)
#define IInternetSecurityManager_MapUrlToZone(This,pwszUrl,pdwZone,dwFlags) (This)->lpVtbl->MapUrlToZone(This,pwszUrl,pdwZone,dwFlags)
#define IInternetSecurityManager_GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved) (This)->lpVtbl->GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved)
#define IInternetSecurityManager_ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved) (This)->lpVtbl->ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved)
#define IInternetSecurityManager_QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved) (This)->lpVtbl->QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved)
#define IInternetSecurityManager_SetZoneMapping(This,dwZone,lpszPattern,dwFlags) (This)->lpVtbl->SetZoneMapping(This,dwZone,lpszPattern,dwFlags)
#define IInternetSecurityManager_GetZoneMappings(This,dwZone,ppenumString,dwFlags) (This)->lpVtbl->GetZoneMappings(This,dwZone,ppenumString,dwFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetSecurityManager_QueryInterface(IInternetSecurityManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetSecurityManager_AddRef(IInternetSecurityManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetSecurityManager_Release(IInternetSecurityManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetSecurityManager methods ***/
static inline HRESULT IInternetSecurityManager_SetSecuritySite(IInternetSecurityManager* This,IInternetSecurityMgrSite *pSite) {
    return This->lpVtbl->SetSecuritySite(This,pSite);
}
static inline HRESULT IInternetSecurityManager_GetSecuritySite(IInternetSecurityManager* This,IInternetSecurityMgrSite **ppSite) {
    return This->lpVtbl->GetSecuritySite(This,ppSite);
}
static inline HRESULT IInternetSecurityManager_MapUrlToZone(IInternetSecurityManager* This,LPCWSTR pwszUrl,DWORD *pdwZone,DWORD dwFlags) {
    return This->lpVtbl->MapUrlToZone(This,pwszUrl,pdwZone,dwFlags);
}
static inline HRESULT IInternetSecurityManager_GetSecurityId(IInternetSecurityManager* This,LPCWSTR pwszUrl,BYTE *pbSecurityId,DWORD *pcbSecurityId,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved);
}
static inline HRESULT IInternetSecurityManager_ProcessUrlAction(IInternetSecurityManager* This,LPCWSTR pwszUrl,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwFlags,DWORD dwReserved) {
    return This->lpVtbl->ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved);
}
static inline HRESULT IInternetSecurityManager_QueryCustomPolicy(IInternetSecurityManager* This,LPCWSTR pwszUrl,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwReserved) {
    return This->lpVtbl->QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved);
}
static inline HRESULT IInternetSecurityManager_SetZoneMapping(IInternetSecurityManager* This,DWORD dwZone,LPCWSTR lpszPattern,DWORD dwFlags) {
    return This->lpVtbl->SetZoneMapping(This,dwZone,lpszPattern,dwFlags);
}
static inline HRESULT IInternetSecurityManager_GetZoneMappings(IInternetSecurityManager* This,DWORD dwZone,IEnumString **ppenumString,DWORD dwFlags) {
    return This->lpVtbl->GetZoneMappings(This,dwZone,ppenumString,dwFlags);
}
#endif
#endif

#endif


#endif  /* __IInternetSecurityManager_INTERFACE_DEFINED__ */

#endif

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#ifndef _LPINTERNETSECURITYMANANGEREX_DEFINED
#define _LPINTERNETSECURITYMANANGEREX_DEFINED

/*****************************************************************************
 * IInternetSecurityManagerEx interface
 */
#ifndef __IInternetSecurityManagerEx_INTERFACE_DEFINED__
#define __IInternetSecurityManagerEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternetSecurityManagerEx, 0xf164edf1, 0xcc7c, 0x4f0d, 0x9a,0x94, 0x34,0x22,0x26,0x25,0xc3,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f164edf1-cc7c-4f0d-9a94-34222625c393")
IInternetSecurityManagerEx : public IInternetSecurityManager
{
    virtual HRESULT STDMETHODCALLTYPE ProcessUrlActionEx(
        LPCWSTR pwszUrl,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved,
        DWORD *pdwOutFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetSecurityManagerEx, 0xf164edf1, 0xcc7c, 0x4f0d, 0x9a,0x94, 0x34,0x22,0x26,0x25,0xc3,0x93)
#endif
#else
typedef struct IInternetSecurityManagerExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetSecurityManagerEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetSecurityManagerEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetSecurityManagerEx *This);

    /*** IInternetSecurityManager methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSecuritySite)(
        IInternetSecurityManagerEx *This,
        IInternetSecurityMgrSite *pSite);

    HRESULT (STDMETHODCALLTYPE *GetSecuritySite)(
        IInternetSecurityManagerEx *This,
        IInternetSecurityMgrSite **ppSite);

    HRESULT (STDMETHODCALLTYPE *MapUrlToZone)(
        IInternetSecurityManagerEx *This,
        LPCWSTR pwszUrl,
        DWORD *pdwZone,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetSecurityId)(
        IInternetSecurityManagerEx *This,
        LPCWSTR pwszUrl,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *ProcessUrlAction)(
        IInternetSecurityManagerEx *This,
        LPCWSTR pwszUrl,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *QueryCustomPolicy)(
        IInternetSecurityManagerEx *This,
        LPCWSTR pwszUrl,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *SetZoneMapping)(
        IInternetSecurityManagerEx *This,
        DWORD dwZone,
        LPCWSTR lpszPattern,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetZoneMappings)(
        IInternetSecurityManagerEx *This,
        DWORD dwZone,
        IEnumString **ppenumString,
        DWORD dwFlags);

    /*** IInternetSecurityManagerEx methods ***/
    HRESULT (STDMETHODCALLTYPE *ProcessUrlActionEx)(
        IInternetSecurityManagerEx *This,
        LPCWSTR pwszUrl,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved,
        DWORD *pdwOutFlags);

    END_INTERFACE
} IInternetSecurityManagerExVtbl;

interface IInternetSecurityManagerEx {
    CONST_VTBL IInternetSecurityManagerExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetSecurityManagerEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetSecurityManagerEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetSecurityManagerEx_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetSecurityManager methods ***/
#define IInternetSecurityManagerEx_SetSecuritySite(This,pSite) (This)->lpVtbl->SetSecuritySite(This,pSite)
#define IInternetSecurityManagerEx_GetSecuritySite(This,ppSite) (This)->lpVtbl->GetSecuritySite(This,ppSite)
#define IInternetSecurityManagerEx_MapUrlToZone(This,pwszUrl,pdwZone,dwFlags) (This)->lpVtbl->MapUrlToZone(This,pwszUrl,pdwZone,dwFlags)
#define IInternetSecurityManagerEx_GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved) (This)->lpVtbl->GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved)
#define IInternetSecurityManagerEx_ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved) (This)->lpVtbl->ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved)
#define IInternetSecurityManagerEx_QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved) (This)->lpVtbl->QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved)
#define IInternetSecurityManagerEx_SetZoneMapping(This,dwZone,lpszPattern,dwFlags) (This)->lpVtbl->SetZoneMapping(This,dwZone,lpszPattern,dwFlags)
#define IInternetSecurityManagerEx_GetZoneMappings(This,dwZone,ppenumString,dwFlags) (This)->lpVtbl->GetZoneMappings(This,dwZone,ppenumString,dwFlags)
/*** IInternetSecurityManagerEx methods ***/
#define IInternetSecurityManagerEx_ProcessUrlActionEx(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags) (This)->lpVtbl->ProcessUrlActionEx(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetSecurityManagerEx_QueryInterface(IInternetSecurityManagerEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetSecurityManagerEx_AddRef(IInternetSecurityManagerEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetSecurityManagerEx_Release(IInternetSecurityManagerEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetSecurityManager methods ***/
static inline HRESULT IInternetSecurityManagerEx_SetSecuritySite(IInternetSecurityManagerEx* This,IInternetSecurityMgrSite *pSite) {
    return This->lpVtbl->SetSecuritySite(This,pSite);
}
static inline HRESULT IInternetSecurityManagerEx_GetSecuritySite(IInternetSecurityManagerEx* This,IInternetSecurityMgrSite **ppSite) {
    return This->lpVtbl->GetSecuritySite(This,ppSite);
}
static inline HRESULT IInternetSecurityManagerEx_MapUrlToZone(IInternetSecurityManagerEx* This,LPCWSTR pwszUrl,DWORD *pdwZone,DWORD dwFlags) {
    return This->lpVtbl->MapUrlToZone(This,pwszUrl,pdwZone,dwFlags);
}
static inline HRESULT IInternetSecurityManagerEx_GetSecurityId(IInternetSecurityManagerEx* This,LPCWSTR pwszUrl,BYTE *pbSecurityId,DWORD *pcbSecurityId,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved);
}
static inline HRESULT IInternetSecurityManagerEx_ProcessUrlAction(IInternetSecurityManagerEx* This,LPCWSTR pwszUrl,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwFlags,DWORD dwReserved) {
    return This->lpVtbl->ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved);
}
static inline HRESULT IInternetSecurityManagerEx_QueryCustomPolicy(IInternetSecurityManagerEx* This,LPCWSTR pwszUrl,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwReserved) {
    return This->lpVtbl->QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved);
}
static inline HRESULT IInternetSecurityManagerEx_SetZoneMapping(IInternetSecurityManagerEx* This,DWORD dwZone,LPCWSTR lpszPattern,DWORD dwFlags) {
    return This->lpVtbl->SetZoneMapping(This,dwZone,lpszPattern,dwFlags);
}
static inline HRESULT IInternetSecurityManagerEx_GetZoneMappings(IInternetSecurityManagerEx* This,DWORD dwZone,IEnumString **ppenumString,DWORD dwFlags) {
    return This->lpVtbl->GetZoneMappings(This,dwZone,ppenumString,dwFlags);
}
/*** IInternetSecurityManagerEx methods ***/
static inline HRESULT IInternetSecurityManagerEx_ProcessUrlActionEx(IInternetSecurityManagerEx* This,LPCWSTR pwszUrl,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwFlags,DWORD dwReserved,DWORD *pdwOutFlags) {
    return This->lpVtbl->ProcessUrlActionEx(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags);
}
#endif
#endif

#endif


#endif  /* __IInternetSecurityManagerEx_INTERFACE_DEFINED__ */

#endif
#endif

#if (_WIN32_IE >= _WIN32_IE_IE70)
#ifndef _LPINTERNETSECURITYMANANGEREx2_DEFINED
#define _LPINTERNETSECURITYMANANGEREx2_DEFINED

/*****************************************************************************
 * IInternetSecurityManagerEx2 interface
 */
#ifndef __IInternetSecurityManagerEx2_INTERFACE_DEFINED__
#define __IInternetSecurityManagerEx2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternetSecurityManagerEx2, 0xf1e50292, 0xa795, 0x4117, 0x8e,0x09, 0x2b,0x56,0x0a,0x72,0xac,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f1e50292-a795-4117-8e09-2b560a72ac60")
IInternetSecurityManagerEx2 : public IInternetSecurityManagerEx
{
    virtual HRESULT STDMETHODCALLTYPE MapUrlToZoneEx2(
        IUri *pUri,
        DWORD *pdwZone,
        DWORD dwFlags,
        LPWSTR *ppwszMappedUrl,
        DWORD *pdwOutFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessUrlActionEx2(
        IUri *pUri,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD_PTR dwReserved,
        DWORD *pdwOutFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSecurityIdEx2(
        IUri *pUri,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryCustomPolicyEx2(
        IUri *pUri,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD_PTR dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetSecurityManagerEx2, 0xf1e50292, 0xa795, 0x4117, 0x8e,0x09, 0x2b,0x56,0x0a,0x72,0xac,0x60)
#endif
#else
typedef struct IInternetSecurityManagerEx2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetSecurityManagerEx2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetSecurityManagerEx2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetSecurityManagerEx2 *This);

    /*** IInternetSecurityManager methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSecuritySite)(
        IInternetSecurityManagerEx2 *This,
        IInternetSecurityMgrSite *pSite);

    HRESULT (STDMETHODCALLTYPE *GetSecuritySite)(
        IInternetSecurityManagerEx2 *This,
        IInternetSecurityMgrSite **ppSite);

    HRESULT (STDMETHODCALLTYPE *MapUrlToZone)(
        IInternetSecurityManagerEx2 *This,
        LPCWSTR pwszUrl,
        DWORD *pdwZone,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetSecurityId)(
        IInternetSecurityManagerEx2 *This,
        LPCWSTR pwszUrl,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *ProcessUrlAction)(
        IInternetSecurityManagerEx2 *This,
        LPCWSTR pwszUrl,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *QueryCustomPolicy)(
        IInternetSecurityManagerEx2 *This,
        LPCWSTR pwszUrl,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *SetZoneMapping)(
        IInternetSecurityManagerEx2 *This,
        DWORD dwZone,
        LPCWSTR lpszPattern,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetZoneMappings)(
        IInternetSecurityManagerEx2 *This,
        DWORD dwZone,
        IEnumString **ppenumString,
        DWORD dwFlags);

    /*** IInternetSecurityManagerEx methods ***/
    HRESULT (STDMETHODCALLTYPE *ProcessUrlActionEx)(
        IInternetSecurityManagerEx2 *This,
        LPCWSTR pwszUrl,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved,
        DWORD *pdwOutFlags);

    /*** IInternetSecurityManagerEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *MapUrlToZoneEx2)(
        IInternetSecurityManagerEx2 *This,
        IUri *pUri,
        DWORD *pdwZone,
        DWORD dwFlags,
        LPWSTR *ppwszMappedUrl,
        DWORD *pdwOutFlags);

    HRESULT (STDMETHODCALLTYPE *ProcessUrlActionEx2)(
        IInternetSecurityManagerEx2 *This,
        IUri *pUri,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD_PTR dwReserved,
        DWORD *pdwOutFlags);

    HRESULT (STDMETHODCALLTYPE *GetSecurityIdEx2)(
        IInternetSecurityManagerEx2 *This,
        IUri *pUri,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *QueryCustomPolicyEx2)(
        IInternetSecurityManagerEx2 *This,
        IUri *pUri,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD_PTR dwReserved);

    END_INTERFACE
} IInternetSecurityManagerEx2Vtbl;

interface IInternetSecurityManagerEx2 {
    CONST_VTBL IInternetSecurityManagerEx2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetSecurityManagerEx2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetSecurityManagerEx2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetSecurityManagerEx2_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetSecurityManager methods ***/
#define IInternetSecurityManagerEx2_SetSecuritySite(This,pSite) (This)->lpVtbl->SetSecuritySite(This,pSite)
#define IInternetSecurityManagerEx2_GetSecuritySite(This,ppSite) (This)->lpVtbl->GetSecuritySite(This,ppSite)
#define IInternetSecurityManagerEx2_MapUrlToZone(This,pwszUrl,pdwZone,dwFlags) (This)->lpVtbl->MapUrlToZone(This,pwszUrl,pdwZone,dwFlags)
#define IInternetSecurityManagerEx2_GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved) (This)->lpVtbl->GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved)
#define IInternetSecurityManagerEx2_ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved) (This)->lpVtbl->ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved)
#define IInternetSecurityManagerEx2_QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved) (This)->lpVtbl->QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved)
#define IInternetSecurityManagerEx2_SetZoneMapping(This,dwZone,lpszPattern,dwFlags) (This)->lpVtbl->SetZoneMapping(This,dwZone,lpszPattern,dwFlags)
#define IInternetSecurityManagerEx2_GetZoneMappings(This,dwZone,ppenumString,dwFlags) (This)->lpVtbl->GetZoneMappings(This,dwZone,ppenumString,dwFlags)
/*** IInternetSecurityManagerEx methods ***/
#define IInternetSecurityManagerEx2_ProcessUrlActionEx(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags) (This)->lpVtbl->ProcessUrlActionEx(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags)
/*** IInternetSecurityManagerEx2 methods ***/
#define IInternetSecurityManagerEx2_MapUrlToZoneEx2(This,pUri,pdwZone,dwFlags,ppwszMappedUrl,pdwOutFlags) (This)->lpVtbl->MapUrlToZoneEx2(This,pUri,pdwZone,dwFlags,ppwszMappedUrl,pdwOutFlags)
#define IInternetSecurityManagerEx2_ProcessUrlActionEx2(This,pUri,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags) (This)->lpVtbl->ProcessUrlActionEx2(This,pUri,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags)
#define IInternetSecurityManagerEx2_GetSecurityIdEx2(This,pUri,pbSecurityId,pcbSecurityId,dwReserved) (This)->lpVtbl->GetSecurityIdEx2(This,pUri,pbSecurityId,pcbSecurityId,dwReserved)
#define IInternetSecurityManagerEx2_QueryCustomPolicyEx2(This,pUri,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved) (This)->lpVtbl->QueryCustomPolicyEx2(This,pUri,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetSecurityManagerEx2_QueryInterface(IInternetSecurityManagerEx2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetSecurityManagerEx2_AddRef(IInternetSecurityManagerEx2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetSecurityManagerEx2_Release(IInternetSecurityManagerEx2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetSecurityManager methods ***/
static inline HRESULT IInternetSecurityManagerEx2_SetSecuritySite(IInternetSecurityManagerEx2* This,IInternetSecurityMgrSite *pSite) {
    return This->lpVtbl->SetSecuritySite(This,pSite);
}
static inline HRESULT IInternetSecurityManagerEx2_GetSecuritySite(IInternetSecurityManagerEx2* This,IInternetSecurityMgrSite **ppSite) {
    return This->lpVtbl->GetSecuritySite(This,ppSite);
}
static inline HRESULT IInternetSecurityManagerEx2_MapUrlToZone(IInternetSecurityManagerEx2* This,LPCWSTR pwszUrl,DWORD *pdwZone,DWORD dwFlags) {
    return This->lpVtbl->MapUrlToZone(This,pwszUrl,pdwZone,dwFlags);
}
static inline HRESULT IInternetSecurityManagerEx2_GetSecurityId(IInternetSecurityManagerEx2* This,LPCWSTR pwszUrl,BYTE *pbSecurityId,DWORD *pcbSecurityId,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetSecurityId(This,pwszUrl,pbSecurityId,pcbSecurityId,dwReserved);
}
static inline HRESULT IInternetSecurityManagerEx2_ProcessUrlAction(IInternetSecurityManagerEx2* This,LPCWSTR pwszUrl,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwFlags,DWORD dwReserved) {
    return This->lpVtbl->ProcessUrlAction(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved);
}
static inline HRESULT IInternetSecurityManagerEx2_QueryCustomPolicy(IInternetSecurityManagerEx2* This,LPCWSTR pwszUrl,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwReserved) {
    return This->lpVtbl->QueryCustomPolicy(This,pwszUrl,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved);
}
static inline HRESULT IInternetSecurityManagerEx2_SetZoneMapping(IInternetSecurityManagerEx2* This,DWORD dwZone,LPCWSTR lpszPattern,DWORD dwFlags) {
    return This->lpVtbl->SetZoneMapping(This,dwZone,lpszPattern,dwFlags);
}
static inline HRESULT IInternetSecurityManagerEx2_GetZoneMappings(IInternetSecurityManagerEx2* This,DWORD dwZone,IEnumString **ppenumString,DWORD dwFlags) {
    return This->lpVtbl->GetZoneMappings(This,dwZone,ppenumString,dwFlags);
}
/*** IInternetSecurityManagerEx methods ***/
static inline HRESULT IInternetSecurityManagerEx2_ProcessUrlActionEx(IInternetSecurityManagerEx2* This,LPCWSTR pwszUrl,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwFlags,DWORD dwReserved,DWORD *pdwOutFlags) {
    return This->lpVtbl->ProcessUrlActionEx(This,pwszUrl,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags);
}
/*** IInternetSecurityManagerEx2 methods ***/
static inline HRESULT IInternetSecurityManagerEx2_MapUrlToZoneEx2(IInternetSecurityManagerEx2* This,IUri *pUri,DWORD *pdwZone,DWORD dwFlags,LPWSTR *ppwszMappedUrl,DWORD *pdwOutFlags) {
    return This->lpVtbl->MapUrlToZoneEx2(This,pUri,pdwZone,dwFlags,ppwszMappedUrl,pdwOutFlags);
}
static inline HRESULT IInternetSecurityManagerEx2_ProcessUrlActionEx2(IInternetSecurityManagerEx2* This,IUri *pUri,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwFlags,DWORD_PTR dwReserved,DWORD *pdwOutFlags) {
    return This->lpVtbl->ProcessUrlActionEx2(This,pUri,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved,pdwOutFlags);
}
static inline HRESULT IInternetSecurityManagerEx2_GetSecurityIdEx2(IInternetSecurityManagerEx2* This,IUri *pUri,BYTE *pbSecurityId,DWORD *pcbSecurityId,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetSecurityIdEx2(This,pUri,pbSecurityId,pcbSecurityId,dwReserved);
}
static inline HRESULT IInternetSecurityManagerEx2_QueryCustomPolicyEx2(IInternetSecurityManagerEx2* This,IUri *pUri,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,BYTE *pContext,DWORD cbContext,DWORD_PTR dwReserved) {
    return This->lpVtbl->QueryCustomPolicyEx2(This,pUri,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IInternetSecurityManagerEx2_INTERFACE_DEFINED__ */

#endif
#endif

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
/*****************************************************************************
 * IZoneIdentifier interface
 */
#ifndef __IZoneIdentifier_INTERFACE_DEFINED__
#define __IZoneIdentifier_INTERFACE_DEFINED__

DEFINE_GUID(IID_IZoneIdentifier, 0xcd45f185, 0x1b21, 0x48e2, 0x96,0x7b, 0xea,0xd7,0x43,0xa8,0x91,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cd45f185-1b21-48e2-967b-ead743a8914e")
IZoneIdentifier : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetId(
        DWORD *pdwZone) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetId(
        DWORD dwZone) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IZoneIdentifier, 0xcd45f185, 0x1b21, 0x48e2, 0x96,0x7b, 0xea,0xd7,0x43,0xa8,0x91,0x4e)
#endif
#else
typedef struct IZoneIdentifierVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IZoneIdentifier *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IZoneIdentifier *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IZoneIdentifier *This);

    /*** IZoneIdentifier methods ***/
    HRESULT (STDMETHODCALLTYPE *GetId)(
        IZoneIdentifier *This,
        DWORD *pdwZone);

    HRESULT (STDMETHODCALLTYPE *SetId)(
        IZoneIdentifier *This,
        DWORD dwZone);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IZoneIdentifier *This);

    END_INTERFACE
} IZoneIdentifierVtbl;

interface IZoneIdentifier {
    CONST_VTBL IZoneIdentifierVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IZoneIdentifier_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IZoneIdentifier_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IZoneIdentifier_Release(This) (This)->lpVtbl->Release(This)
/*** IZoneIdentifier methods ***/
#define IZoneIdentifier_GetId(This,pdwZone) (This)->lpVtbl->GetId(This,pdwZone)
#define IZoneIdentifier_SetId(This,dwZone) (This)->lpVtbl->SetId(This,dwZone)
#define IZoneIdentifier_Remove(This) (This)->lpVtbl->Remove(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IZoneIdentifier_QueryInterface(IZoneIdentifier* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IZoneIdentifier_AddRef(IZoneIdentifier* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IZoneIdentifier_Release(IZoneIdentifier* This) {
    return This->lpVtbl->Release(This);
}
/*** IZoneIdentifier methods ***/
static inline HRESULT IZoneIdentifier_GetId(IZoneIdentifier* This,DWORD *pdwZone) {
    return This->lpVtbl->GetId(This,pdwZone);
}
static inline HRESULT IZoneIdentifier_SetId(IZoneIdentifier* This,DWORD dwZone) {
    return This->lpVtbl->SetId(This,dwZone);
}
static inline HRESULT IZoneIdentifier_Remove(IZoneIdentifier* This) {
    return This->lpVtbl->Remove(This);
}
#endif
#endif

#endif


#endif  /* __IZoneIdentifier_INTERFACE_DEFINED__ */

#endif

#ifndef _LPINTERNETHOSTSECURITYMANANGER_DEFINED
#define _LPINTERNETHOSTSECURITYMANANGER_DEFINED

/*****************************************************************************
 * IInternetHostSecurityManager interface
 */
#ifndef __IInternetHostSecurityManager_INTERFACE_DEFINED__
#define __IInternetHostSecurityManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternetHostSecurityManager, 0x3af280b6, 0xcb3f, 0x11d0, 0x89,0x1e, 0x00,0xc0,0x4f,0xb6,0xbf,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3af280b6-cb3f-11d0-891e-00c04fb6bfc4")
IInternetHostSecurityManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSecurityId(
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessUrlAction(
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryCustomPolicy(
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetHostSecurityManager, 0x3af280b6, 0xcb3f, 0x11d0, 0x89,0x1e, 0x00,0xc0,0x4f,0xb6,0xbf,0xc4)
#endif
#else
typedef struct IInternetHostSecurityManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetHostSecurityManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetHostSecurityManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetHostSecurityManager *This);

    /*** IInternetHostSecurityManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSecurityId)(
        IInternetHostSecurityManager *This,
        BYTE *pbSecurityId,
        DWORD *pcbSecurityId,
        DWORD_PTR dwReserved);

    HRESULT (STDMETHODCALLTYPE *ProcessUrlAction)(
        IInternetHostSecurityManager *This,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwFlags,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *QueryCustomPolicy)(
        IInternetHostSecurityManager *This,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        BYTE *pContext,
        DWORD cbContext,
        DWORD dwReserved);

    END_INTERFACE
} IInternetHostSecurityManagerVtbl;

interface IInternetHostSecurityManager {
    CONST_VTBL IInternetHostSecurityManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetHostSecurityManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetHostSecurityManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetHostSecurityManager_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetHostSecurityManager methods ***/
#define IInternetHostSecurityManager_GetSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved) (This)->lpVtbl->GetSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved)
#define IInternetHostSecurityManager_ProcessUrlAction(This,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved) (This)->lpVtbl->ProcessUrlAction(This,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved)
#define IInternetHostSecurityManager_QueryCustomPolicy(This,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved) (This)->lpVtbl->QueryCustomPolicy(This,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetHostSecurityManager_QueryInterface(IInternetHostSecurityManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetHostSecurityManager_AddRef(IInternetHostSecurityManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetHostSecurityManager_Release(IInternetHostSecurityManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetHostSecurityManager methods ***/
static inline HRESULT IInternetHostSecurityManager_GetSecurityId(IInternetHostSecurityManager* This,BYTE *pbSecurityId,DWORD *pcbSecurityId,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetSecurityId(This,pbSecurityId,pcbSecurityId,dwReserved);
}
static inline HRESULT IInternetHostSecurityManager_ProcessUrlAction(IInternetHostSecurityManager* This,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwFlags,DWORD dwReserved) {
    return This->lpVtbl->ProcessUrlAction(This,dwAction,pPolicy,cbPolicy,pContext,cbContext,dwFlags,dwReserved);
}
static inline HRESULT IInternetHostSecurityManager_QueryCustomPolicy(IInternetHostSecurityManager* This,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,BYTE *pContext,DWORD cbContext,DWORD dwReserved) {
    return This->lpVtbl->QueryCustomPolicy(This,guidKey,ppPolicy,pcbPolicy,pContext,cbContext,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IInternetHostSecurityManager_INTERFACE_DEFINED__ */

#endif

#define URLACTION_MIN 0x1000

#define URLACTION_DOWNLOAD_MIN 0x1000
#define URLACTION_DOWNLOAD_SIGNED_ACTIVEX 0x1001
#define URLACTION_DOWNLOAD_UNSIGNED_ACTIVEX 0x1004
#define URLACTION_DOWNLOAD_CURR_MAX 0x1004
#define URLACTION_DOWNLOAD_MAX 0x11FF

#define URLACTION_ACTIVEX_MIN 0x1200
#define URLACTION_ACTIVEX_RUN 0x1200
#define URLPOLICY_ACTIVEX_CHECK_LIST 0x10000
#define URLACTION_ACTIVEX_OVERRIDE_OBJECT_SAFETY 0x1201
#define URLACTION_ACTIVEX_OVERRIDE_DATA_SAFETY 0x1202
#define URLACTION_ACTIVEX_OVERRIDE_SCRIPT_SAFETY 0x1203
#define URLACTION_SCRIPT_OVERRIDE_SAFETY 0x1401
#define URLACTION_ACTIVEX_CONFIRM_NOOBJECTSAFETY 0x1204
#define URLACTION_ACTIVEX_TREATASUNTRUSTED 0x1205
#define URLACTION_ACTIVEX_NO_WEBOC_SCRIPT 0x1206
#define URLACTION_ACTIVEX_OVERRIDE_REPURPOSEDETECTION 0x1207
#define URLACTION_ACTIVEX_OVERRIDE_OPTIN 0x1208
#define URLACTION_ACTIVEX_SCRIPTLET_RUN 0x1209
#define URLACTION_ACTIVEX_DYNSRC_VIDEO_AND_ANIMATION 0x120A
#define URLACTION_ACTIVEX_OVERRIDE_DOMAINLIST 0x120B
#define URLACTION_ACTIVEX_CURR_MAX 0x120B
#define URLACTION_ACTIVEX_MAX 0x13ff

#define URLACTION_SCRIPT_MIN 0x1400
#define URLACTION_SCRIPT_RUN 0x1400
#define URLACTION_SCRIPT_JAVA_USE 0x1402
#define URLACTION_SCRIPT_SAFE_ACTIVEX 0x1405
#define URLACTION_CROSS_DOMAIN_DATA 0x1406
#define URLACTION_SCRIPT_PASTE 0x1407
#define URLACTION_ALLOW_XDOMAIN_SUBFRAME_RESIZE 0x1408
#define URLACTION_SCRIPT_XSSFILTER 0x1409
#define URLACTION_SCRIPT_NAVIGATE 0x140A
#define URLACTION_PLUGGABLE_PROTOCOL_XHR 0x140B
#define URLACTION_SCRIPT_CURR_MAX 0x140B
#define URLACTION_SCRIPT_MAX 0x15ff

#define URLACTION_HTML_MIN 0x1600
#define URLACTION_HTML_SUBMIT_FORMS 0x1601
#define URLACTION_HTML_SUBMIT_FORMS_FROM 0x1602
#define URLACTION_HTML_SUBMIT_FORMS_TO 0x1603
#define URLACTION_HTML_FONT_DOWNLOAD 0x1604
#define URLACTION_HTML_JAVA_RUN 0x1605
#define URLACTION_HTML_USERDATA_SAVE 0x1606
#define URLACTION_HTML_SUBFRAME_NAVIGATE 0x1607
#define URLACTION_HTML_META_REFRESH 0x1608
#define URLACTION_HTML_MIXED_CONTENT 0x1609
#define URLACTION_HTML_INCLUDE_FILE_PATH 0x160A
#define URLACTION_HTML_ALLOW_INJECTED_DYNAMIC_HTML 0x160B
#define URLACTION_HTML_REQUIRE_UTF8_DOCUMENT_CODEPAGE 0x160C
#define URLACTION_HTML_ALLOW_CROSS_DOMAIN_CANVAS 0x160D
#define URLACTION_HTML_ALLOW_WINDOW_CLOSE 0x160E
#define URLACTION_HTML_ALLOW_CROSS_DOMAIN_WEBWORKER 0x160F
#define URLACTION_HTML_ALLOW_CROSS_DOMAIN_TEXTTRACK 0x1610
#define URLACTION_HTML_ALLOW_INDEXEDDB 0x1611

#define URLACTION_HTML_MAX 0x17ff

#define URLACTION_SHELL_MIN 0x1800
#define URLACTION_SHELL_INSTALL_DTITEMS 0x1800
#define URLACTION_SHELL_MOVE_OR_COPY 0x1802
#define URLACTION_SHELL_FILE_DOWNLOAD 0x1803
#define URLACTION_SHELL_VERB 0x1804
#define URLACTION_SHELL_WEBVIEW_VERB 0x1805
#define URLACTION_SHELL_SHELLEXECUTE 0x1806

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#define URLACTION_SHELL_EXECUTE_HIGHRISK 0x1806
#define URLACTION_SHELL_EXECUTE_MODRISK 0x1807
#define URLACTION_SHELL_EXECUTE_LOWRISK 0x1808
#define URLACTION_SHELL_POPUPMGR 0x1809
#define URLACTION_SHELL_RTF_OBJECTS_LOAD 0x180A
#define URLACTION_SHELL_ENHANCED_DRAGDROP_SECURITY 0x180B
#define URLACTION_SHELL_EXTENSIONSECURITY 0x180C
#define URLACTION_SHELL_SECURE_DRAGSOURCE 0x180D
#endif
#if (_WIN32_IE >= _WIN32_IE_WIN7)
#define URLACTION_SHELL_REMOTEQUERY 0x180E
#define URLACTION_SHELL_PREVIEW 0x180F
#define URLACTION_SHELL_SHARE 0x1810
#define URLACTION_SHELL_ALLOW_CROSS_SITE_SHARE 0x1811
#endif
#define URLACTION_SHELL_CURR_MAX 0x1811
#define URLACTION_SHELL_MAX 0x19ff

#define URLACTION_NETWORK_MIN 0x1A00

#define URLACTION_CREDENTIALS_USE 0x1A00
#define URLPOLICY_CREDENTIALS_SILENT_LOGON_OK 0x0
#define URLPOLICY_CREDENTIALS_MUST_PROMPT_USER 0x10000
#define URLPOLICY_CREDENTIALS_CONDITIONAL_PROMPT 0x20000
#define URLPOLICY_CREDENTIALS_ANONYMOUS_ONLY 0x30000

#define URLACTION_AUTHENTICATE_CLIENT 0x1A01
#define URLPOLICY_AUTHENTICATE_CLEARTEXT_OK 0x0
#define URLPOLICY_AUTHENTICATE_CHALLENGE_RESPONSE 0x10000
#define URLPOLICY_AUTHENTICATE_MUTUAL_ONLY 0x30000

#define URLACTION_COOKIES 0x1A02
#define URLACTION_COOKIES_SESSION 0x1A03

#define URLACTION_CLIENT_CERT_PROMPT 0x1A04

#define URLACTION_COOKIES_THIRD_PARTY 0x1A05
#define URLACTION_COOKIES_SESSION_THIRD_PARTY 0x1A06

#define URLACTION_COOKIES_ENABLED 0x1A10

#define URLACTION_NETWORK_CURR_MAX 0x1A10
#define URLACTION_NETWORK_MAX 0x1Bff

#define URLACTION_JAVA_MIN 0x1C00
#define URLACTION_JAVA_PERMISSIONS 0x1C00
#define URLPOLICY_JAVA_PROHIBIT 0x0
#define URLPOLICY_JAVA_HIGH 0x10000
#define URLPOLICY_JAVA_MEDIUM 0x20000
#define URLPOLICY_JAVA_LOW 0x30000
#define URLPOLICY_JAVA_CUSTOM 0x800000
#define URLACTION_JAVA_CURR_MAX 0x1C00
#define URLACTION_JAVA_MAX 0x1Cff

#define URLACTION_INFODELIVERY_MIN 0x1D00
#define URLACTION_INFODELIVERY_NO_ADDING_CHANNELS 0x1D00
#define URLACTION_INFODELIVERY_NO_EDITING_CHANNELS 0x1D01
#define URLACTION_INFODELIVERY_NO_REMOVING_CHANNELS 0x1D02
#define URLACTION_INFODELIVERY_NO_ADDING_SUBSCRIPTIONS 0x1D03
#define URLACTION_INFODELIVERY_NO_EDITING_SUBSCRIPTIONS 0x1D04
#define URLACTION_INFODELIVERY_NO_REMOVING_SUBSCRIPTIONS 0x1D05
#define URLACTION_INFODELIVERY_NO_CHANNEL_LOGGING 0x1D06
#define URLACTION_INFODELIVERY_CURR_MAX 0x1D06
#define URLACTION_INFODELIVERY_MAX 0x1Dff
#define URLACTION_CHANNEL_SOFTDIST_MIN 0x1E00
#define URLACTION_CHANNEL_SOFTDIST_PERMISSIONS 0x1E05
#define URLPOLICY_CHANNEL_SOFTDIST_PROHIBIT 0x10000
#define URLPOLICY_CHANNEL_SOFTDIST_PRECACHE 0x20000
#define URLPOLICY_CHANNEL_SOFTDIST_AUTOINSTALL 0x30000
#define URLACTION_CHANNEL_SOFTDIST_MAX 0x1Eff
#if (_WIN32_IE >= _WIN32_IE_IE80)
#define URLACTION_DOTNET_USERCONTROLS 0x2005
#endif

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#define URLACTION_BEHAVIOR_MIN 0x2000
#define URLACTION_BEHAVIOR_RUN 0x2000
#define URLPOLICY_BEHAVIOR_CHECK_LIST 0x10000

#define URLACTION_FEATURE_MIN 0x2100
#define URLACTION_FEATURE_MIME_SNIFFING 0x2100
#define URLACTION_FEATURE_ZONE_ELEVATION 0x2101
#define URLACTION_FEATURE_WINDOW_RESTRICTIONS 0x2102
#define URLACTION_FEATURE_SCRIPT_STATUS_BAR 0x2103
#define URLACTION_FEATURE_FORCE_ADDR_AND_STATUS 0x2104
#define URLACTION_FEATURE_BLOCK_INPUT_PROMPTS 0x2105
#define URLACTION_FEATURE_DATA_BINDING 0x2106
#define URLACTION_FEATURE_CROSSDOMAIN_FOCUS_CHANGE 0x2107

#define URLACTION_AUTOMATIC_DOWNLOAD_UI_MIN 0x2200
#define URLACTION_AUTOMATIC_DOWNLOAD_UI 0x2200
#define URLACTION_AUTOMATIC_ACTIVEX_UI 0x2201

#define URLACTION_ALLOW_RESTRICTEDPROTOCOLS 0x2300
#endif
#if (_WIN32_IE >= _WIN32_IE_IE70)
#define URLACTION_ALLOW_APEVALUATION 0x2301
#define URLACTION_ALLOW_XHR_EVALUATION 0x2302
#define URLACTION_WINDOWS_BROWSER_APPLICATIONS 0x2400
#define URLACTION_XPS_DOCUMENTS 0x2401
#define URLACTION_LOOSE_XAML 0x2402
#define URLACTION_LOWRIGHTS 0x2500
#define URLACTION_WINFX_SETUP 0x2600
#define URLACTION_INPRIVATE_BLOCKING 0x2700
#endif
#define URLACTION_ALLOW_AUDIO_VIDEO 0x2701
#define URLACTION_ALLOW_ACTIVEX_FILTERING 0x2702
#define URLACTION_ALLOW_STRUCTURED_STORAGE_SNIFFING 0x2703
#define URLACTION_ALLOW_AUDIO_VIDEO_PLUGINS 0x2704
#define URLACTION_ALLOW_ZONE_ELEVATION_VIA_OPT_OUT 0x2705
#define URLACTION_ALLOW_ZONE_ELEVATION_OPT_OUT_ADDITION 0x2706
#define URLACTION_ALLOW_CROSSDOMAIN_DROP_WITHIN_WINDOW 0x2708
#define URLACTION_ALLOW_CROSSDOMAIN_DROP_ACROSS_WINDOWS 0x2709
#define URLACTION_ALLOW_CROSSDOMAIN_APPCACHE_MANIFEST 0x270A
#define URLACTION_ALLOW_RENDER_LEGACY_DXTFILTERS 0x270B

#define URLPOLICY_ALLOW 0x0
#define URLPOLICY_QUERY 0x1
#define URLPOLICY_DISALLOW 0x3
#define URLPOLICY_NOTIFY_ON_ALLOW 0x10
#define URLPOLICY_NOTIFY_ON_DISALLOW 0x20
#define URLPOLICY_LOG_ON_ALLOW 0x40
#define URLPOLICY_LOG_ON_DISALLOW 0x80

#define URLPOLICY_MASK_PERMISSIONS 0x0f
#define GetUrlPolicyPermissions(dw) (dw & URLPOLICY_MASK_PERMISSIONS)
#define SetUrlPolicyPermissions(dw,dw2) ((dw) = ((dw) & ~(URLPOLICY_MASK_PERMISSIONS)) | (dw2))

#define URLPOLICY_DONTCHECKDLGBOX 0x100

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
EXTERN_C const GUID GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED;
#endif

#ifndef _LPINTERNETZONEMANAGER_DEFINED
#define _LPINTERNETZONEMANAGER_DEFINED

/*****************************************************************************
 * IInternetZoneManager interface
 */
#ifndef __IInternetZoneManager_INTERFACE_DEFINED__
#define __IInternetZoneManager_INTERFACE_DEFINED__

typedef IInternetZoneManager *LPURLZONEMANAGER;

typedef enum tagURLZONE {
    URLZONE_INVALID = -1,
    URLZONE_PREDEFINED_MIN = 0,
    URLZONE_LOCAL_MACHINE = 0,
    URLZONE_INTRANET = 1,
    URLZONE_TRUSTED = 2,
    URLZONE_INTERNET = 3,
    URLZONE_UNTRUSTED = 4,
    URLZONE_PREDEFINED_MAX = 999,
    URLZONE_USER_MIN = 1000,
    URLZONE_USER_MAX = 10000
} URLZONE;

#define URLZONE_ESC_FLAG 0x100

typedef enum tagURLTEMPLATE {
    URLTEMPLATE_CUSTOM = 0x0,
    URLTEMPLATE_PREDEFINED_MIN = 0x10000,
    URLTEMPLATE_LOW = 0x10000,
    URLTEMPLATE_MEDLOW = 0x10500,
    URLTEMPLATE_MEDIUM = 0x11000,
    URLTEMPLATE_MEDHIGH = 0x11500,
    URLTEMPLATE_HIGH = 0x12000,
    URLTEMPLATE_PREDEFINED_MAX = 0x20000
} URLTEMPLATE;

enum {
    MAX_ZONE_PATH = 260,
    MAX_ZONE_DESCRIPTION = 200
};

typedef enum __WIDL_urlmon_generated_name_0000001E {
    ZAFLAGS_CUSTOM_EDIT = 0x1,
    ZAFLAGS_ADD_SITES = 0x2,
    ZAFLAGS_REQUIRE_VERIFICATION = 0x4,
    ZAFLAGS_INCLUDE_PROXY_OVERRIDE = 0x8,
    ZAFLAGS_INCLUDE_INTRANET_SITES = 0x10,
    ZAFLAGS_NO_UI = 0x20,
    ZAFLAGS_SUPPORTS_VERIFICATION = 0x40,
    ZAFLAGS_UNC_AS_INTRANET = 0x80,
    ZAFLAGS_DETECT_INTRANET = 0x100,
    ZAFLAGS_USE_LOCKED_ZONES = 0x10000,
    ZAFLAGS_VERIFY_TEMPLATE_SETTINGS = 0x20000,
    ZAFLAGS_NO_CACHE = 0x40000
} ZAFLAGS;

typedef struct _ZONEATTRIBUTES {
    ULONG cbSize;
    WCHAR szDisplayName[260];
    WCHAR szDescription[200];
    WCHAR szIconPath[260];
    DWORD dwTemplateMinLevel;
    DWORD dwTemplateRecommended;
    DWORD dwTemplateCurrentLevel;
    DWORD dwFlags;
} ZONEATTRIBUTES;
typedef struct _ZONEATTRIBUTES *LPZONEATTRIBUTES;


typedef enum _URLZONEREG {
    URLZONEREG_DEFAULT = 0,
    URLZONEREG_HKLM = 1,
    URLZONEREG_HKCU = 2
} URLZONEREG;

DEFINE_GUID(IID_IInternetZoneManager, 0x79eac9ef, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79eac9ef-baf9-11ce-8c82-00aa004ba90b")
IInternetZoneManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetZoneAttributes(
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetZoneAttributes(
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetZoneCustomPolicy(
        DWORD dwZone,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        URLZONEREG urlZoneReg) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetZoneCustomPolicy(
        DWORD dwZone,
        REFGUID guidKey,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetZoneActionPolicy(
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetZoneActionPolicy(
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg) = 0;

    virtual HRESULT STDMETHODCALLTYPE PromptAction(
        DWORD dwAction,
        HWND hwndParent,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwPromptFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE LogAction(
        DWORD dwAction,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwLogFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateZoneEnumerator(
        DWORD *pdwEnum,
        DWORD *pdwCount,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetZoneAt(
        DWORD dwEnum,
        DWORD dwIndex,
        DWORD *pdwZone) = 0;

    virtual HRESULT STDMETHODCALLTYPE DestroyZoneEnumerator(
        DWORD dwEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyTemplatePoliciesToZone(
        DWORD dwTemplate,
        DWORD dwZone,
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetZoneManager, 0x79eac9ef, 0xbaf9, 0x11ce, 0x8c,0x82, 0x00,0xaa,0x00,0x4b,0xa9,0x0b)
#endif
#else
typedef struct IInternetZoneManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetZoneManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetZoneManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetZoneManager *This);

    /*** IInternetZoneManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetZoneAttributes)(
        IInternetZoneManager *This,
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes);

    HRESULT (STDMETHODCALLTYPE *SetZoneAttributes)(
        IInternetZoneManager *This,
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes);

    HRESULT (STDMETHODCALLTYPE *GetZoneCustomPolicy)(
        IInternetZoneManager *This,
        DWORD dwZone,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *SetZoneCustomPolicy)(
        IInternetZoneManager *This,
        DWORD dwZone,
        REFGUID guidKey,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *GetZoneActionPolicy)(
        IInternetZoneManager *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *SetZoneActionPolicy)(
        IInternetZoneManager *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *PromptAction)(
        IInternetZoneManager *This,
        DWORD dwAction,
        HWND hwndParent,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwPromptFlags);

    HRESULT (STDMETHODCALLTYPE *LogAction)(
        IInternetZoneManager *This,
        DWORD dwAction,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwLogFlags);

    HRESULT (STDMETHODCALLTYPE *CreateZoneEnumerator)(
        IInternetZoneManager *This,
        DWORD *pdwEnum,
        DWORD *pdwCount,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetZoneAt)(
        IInternetZoneManager *This,
        DWORD dwEnum,
        DWORD dwIndex,
        DWORD *pdwZone);

    HRESULT (STDMETHODCALLTYPE *DestroyZoneEnumerator)(
        IInternetZoneManager *This,
        DWORD dwEnum);

    HRESULT (STDMETHODCALLTYPE *CopyTemplatePoliciesToZone)(
        IInternetZoneManager *This,
        DWORD dwTemplate,
        DWORD dwZone,
        DWORD dwReserved);

    END_INTERFACE
} IInternetZoneManagerVtbl;

interface IInternetZoneManager {
    CONST_VTBL IInternetZoneManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetZoneManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetZoneManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetZoneManager_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetZoneManager methods ***/
#define IInternetZoneManager_GetZoneAttributes(This,dwZone,pZoneAttributes) (This)->lpVtbl->GetZoneAttributes(This,dwZone,pZoneAttributes)
#define IInternetZoneManager_SetZoneAttributes(This,dwZone,pZoneAttributes) (This)->lpVtbl->SetZoneAttributes(This,dwZone,pZoneAttributes)
#define IInternetZoneManager_GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg) (This)->lpVtbl->GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg)
#define IInternetZoneManager_SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManager_GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManager_SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManager_PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags) (This)->lpVtbl->PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags)
#define IInternetZoneManager_LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags) (This)->lpVtbl->LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags)
#define IInternetZoneManager_CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags) (This)->lpVtbl->CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags)
#define IInternetZoneManager_GetZoneAt(This,dwEnum,dwIndex,pdwZone) (This)->lpVtbl->GetZoneAt(This,dwEnum,dwIndex,pdwZone)
#define IInternetZoneManager_DestroyZoneEnumerator(This,dwEnum) (This)->lpVtbl->DestroyZoneEnumerator(This,dwEnum)
#define IInternetZoneManager_CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved) (This)->lpVtbl->CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetZoneManager_QueryInterface(IInternetZoneManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetZoneManager_AddRef(IInternetZoneManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetZoneManager_Release(IInternetZoneManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetZoneManager methods ***/
static inline HRESULT IInternetZoneManager_GetZoneAttributes(IInternetZoneManager* This,DWORD dwZone,ZONEATTRIBUTES *pZoneAttributes) {
    return This->lpVtbl->GetZoneAttributes(This,dwZone,pZoneAttributes);
}
static inline HRESULT IInternetZoneManager_SetZoneAttributes(IInternetZoneManager* This,DWORD dwZone,ZONEATTRIBUTES *pZoneAttributes) {
    return This->lpVtbl->SetZoneAttributes(This,dwZone,pZoneAttributes);
}
static inline HRESULT IInternetZoneManager_GetZoneCustomPolicy(IInternetZoneManager* This,DWORD dwZone,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManager_SetZoneCustomPolicy(IInternetZoneManager* This,DWORD dwZone,REFGUID guidKey,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManager_GetZoneActionPolicy(IInternetZoneManager* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManager_SetZoneActionPolicy(IInternetZoneManager* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManager_PromptAction(IInternetZoneManager* This,DWORD dwAction,HWND hwndParent,LPCWSTR pwszUrl,LPCWSTR pwszText,DWORD dwPromptFlags) {
    return This->lpVtbl->PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags);
}
static inline HRESULT IInternetZoneManager_LogAction(IInternetZoneManager* This,DWORD dwAction,LPCWSTR pwszUrl,LPCWSTR pwszText,DWORD dwLogFlags) {
    return This->lpVtbl->LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags);
}
static inline HRESULT IInternetZoneManager_CreateZoneEnumerator(IInternetZoneManager* This,DWORD *pdwEnum,DWORD *pdwCount,DWORD dwFlags) {
    return This->lpVtbl->CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags);
}
static inline HRESULT IInternetZoneManager_GetZoneAt(IInternetZoneManager* This,DWORD dwEnum,DWORD dwIndex,DWORD *pdwZone) {
    return This->lpVtbl->GetZoneAt(This,dwEnum,dwIndex,pdwZone);
}
static inline HRESULT IInternetZoneManager_DestroyZoneEnumerator(IInternetZoneManager* This,DWORD dwEnum) {
    return This->lpVtbl->DestroyZoneEnumerator(This,dwEnum);
}
static inline HRESULT IInternetZoneManager_CopyTemplatePoliciesToZone(IInternetZoneManager* This,DWORD dwTemplate,DWORD dwZone,DWORD dwReserved) {
    return This->lpVtbl->CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IInternetZoneManager_INTERFACE_DEFINED__ */

#endif

#if (_WIN32_IE >= _WIN32_IE_IE60SP2)
#ifndef _LPINTERNETZONEMANAGEREX_DEFINED
#define _LPINTERNETZONEMANAGEREX_DEFINED

/*****************************************************************************
 * IInternetZoneManagerEx interface
 */
#ifndef __IInternetZoneManagerEx_INTERFACE_DEFINED__
#define __IInternetZoneManagerEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternetZoneManagerEx, 0xa4c23339, 0x8e06, 0x431e, 0x9b,0xf4, 0x7e,0x71,0x1c,0x08,0x56,0x48);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a4c23339-8e06-431e-9bf4-7e711c085648")
IInternetZoneManagerEx : public IInternetZoneManager
{
    virtual HRESULT STDMETHODCALLTYPE GetZoneActionPolicyEx(
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetZoneActionPolicyEx(
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg,
        DWORD dwFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetZoneManagerEx, 0xa4c23339, 0x8e06, 0x431e, 0x9b,0xf4, 0x7e,0x71,0x1c,0x08,0x56,0x48)
#endif
#else
typedef struct IInternetZoneManagerExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetZoneManagerEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetZoneManagerEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetZoneManagerEx *This);

    /*** IInternetZoneManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetZoneAttributes)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes);

    HRESULT (STDMETHODCALLTYPE *SetZoneAttributes)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes);

    HRESULT (STDMETHODCALLTYPE *GetZoneCustomPolicy)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *SetZoneCustomPolicy)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        REFGUID guidKey,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *GetZoneActionPolicy)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *SetZoneActionPolicy)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *PromptAction)(
        IInternetZoneManagerEx *This,
        DWORD dwAction,
        HWND hwndParent,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwPromptFlags);

    HRESULT (STDMETHODCALLTYPE *LogAction)(
        IInternetZoneManagerEx *This,
        DWORD dwAction,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwLogFlags);

    HRESULT (STDMETHODCALLTYPE *CreateZoneEnumerator)(
        IInternetZoneManagerEx *This,
        DWORD *pdwEnum,
        DWORD *pdwCount,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetZoneAt)(
        IInternetZoneManagerEx *This,
        DWORD dwEnum,
        DWORD dwIndex,
        DWORD *pdwZone);

    HRESULT (STDMETHODCALLTYPE *DestroyZoneEnumerator)(
        IInternetZoneManagerEx *This,
        DWORD dwEnum);

    HRESULT (STDMETHODCALLTYPE *CopyTemplatePoliciesToZone)(
        IInternetZoneManagerEx *This,
        DWORD dwTemplate,
        DWORD dwZone,
        DWORD dwReserved);

    /*** IInternetZoneManagerEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetZoneActionPolicyEx)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *SetZoneActionPolicyEx)(
        IInternetZoneManagerEx *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg,
        DWORD dwFlags);

    END_INTERFACE
} IInternetZoneManagerExVtbl;

interface IInternetZoneManagerEx {
    CONST_VTBL IInternetZoneManagerExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetZoneManagerEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetZoneManagerEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetZoneManagerEx_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetZoneManager methods ***/
#define IInternetZoneManagerEx_GetZoneAttributes(This,dwZone,pZoneAttributes) (This)->lpVtbl->GetZoneAttributes(This,dwZone,pZoneAttributes)
#define IInternetZoneManagerEx_SetZoneAttributes(This,dwZone,pZoneAttributes) (This)->lpVtbl->SetZoneAttributes(This,dwZone,pZoneAttributes)
#define IInternetZoneManagerEx_GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg) (This)->lpVtbl->GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg)
#define IInternetZoneManagerEx_SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManagerEx_GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManagerEx_SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManagerEx_PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags) (This)->lpVtbl->PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags)
#define IInternetZoneManagerEx_LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags) (This)->lpVtbl->LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags)
#define IInternetZoneManagerEx_CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags) (This)->lpVtbl->CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags)
#define IInternetZoneManagerEx_GetZoneAt(This,dwEnum,dwIndex,pdwZone) (This)->lpVtbl->GetZoneAt(This,dwEnum,dwIndex,pdwZone)
#define IInternetZoneManagerEx_DestroyZoneEnumerator(This,dwEnum) (This)->lpVtbl->DestroyZoneEnumerator(This,dwEnum)
#define IInternetZoneManagerEx_CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved) (This)->lpVtbl->CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved)
/*** IInternetZoneManagerEx methods ***/
#define IInternetZoneManagerEx_GetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags) (This)->lpVtbl->GetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags)
#define IInternetZoneManagerEx_SetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags) (This)->lpVtbl->SetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetZoneManagerEx_QueryInterface(IInternetZoneManagerEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetZoneManagerEx_AddRef(IInternetZoneManagerEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetZoneManagerEx_Release(IInternetZoneManagerEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetZoneManager methods ***/
static inline HRESULT IInternetZoneManagerEx_GetZoneAttributes(IInternetZoneManagerEx* This,DWORD dwZone,ZONEATTRIBUTES *pZoneAttributes) {
    return This->lpVtbl->GetZoneAttributes(This,dwZone,pZoneAttributes);
}
static inline HRESULT IInternetZoneManagerEx_SetZoneAttributes(IInternetZoneManagerEx* This,DWORD dwZone,ZONEATTRIBUTES *pZoneAttributes) {
    return This->lpVtbl->SetZoneAttributes(This,dwZone,pZoneAttributes);
}
static inline HRESULT IInternetZoneManagerEx_GetZoneCustomPolicy(IInternetZoneManagerEx* This,DWORD dwZone,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx_SetZoneCustomPolicy(IInternetZoneManagerEx* This,DWORD dwZone,REFGUID guidKey,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx_GetZoneActionPolicy(IInternetZoneManagerEx* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx_SetZoneActionPolicy(IInternetZoneManagerEx* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx_PromptAction(IInternetZoneManagerEx* This,DWORD dwAction,HWND hwndParent,LPCWSTR pwszUrl,LPCWSTR pwszText,DWORD dwPromptFlags) {
    return This->lpVtbl->PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags);
}
static inline HRESULT IInternetZoneManagerEx_LogAction(IInternetZoneManagerEx* This,DWORD dwAction,LPCWSTR pwszUrl,LPCWSTR pwszText,DWORD dwLogFlags) {
    return This->lpVtbl->LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags);
}
static inline HRESULT IInternetZoneManagerEx_CreateZoneEnumerator(IInternetZoneManagerEx* This,DWORD *pdwEnum,DWORD *pdwCount,DWORD dwFlags) {
    return This->lpVtbl->CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags);
}
static inline HRESULT IInternetZoneManagerEx_GetZoneAt(IInternetZoneManagerEx* This,DWORD dwEnum,DWORD dwIndex,DWORD *pdwZone) {
    return This->lpVtbl->GetZoneAt(This,dwEnum,dwIndex,pdwZone);
}
static inline HRESULT IInternetZoneManagerEx_DestroyZoneEnumerator(IInternetZoneManagerEx* This,DWORD dwEnum) {
    return This->lpVtbl->DestroyZoneEnumerator(This,dwEnum);
}
static inline HRESULT IInternetZoneManagerEx_CopyTemplatePoliciesToZone(IInternetZoneManagerEx* This,DWORD dwTemplate,DWORD dwZone,DWORD dwReserved) {
    return This->lpVtbl->CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved);
}
/*** IInternetZoneManagerEx methods ***/
static inline HRESULT IInternetZoneManagerEx_GetZoneActionPolicyEx(IInternetZoneManagerEx* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg,DWORD dwFlags) {
    return This->lpVtbl->GetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags);
}
static inline HRESULT IInternetZoneManagerEx_SetZoneActionPolicyEx(IInternetZoneManagerEx* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg,DWORD dwFlags) {
    return This->lpVtbl->SetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags);
}
#endif
#endif

#endif


#endif  /* __IInternetZoneManagerEx_INTERFACE_DEFINED__ */

#endif
#endif

#if (_WIN32_IE >= _WIN32_IE_IE70)
#ifndef _LPINTERNETZONEMANAGEREX2_DEFINED
#define _LPINTERNETZONEMANAGEREX2_DEFINED

#define SECURITY_IE_STATE_GREEN 0x0
#define SECURITY_IE_STATE_RED 0x1

/*****************************************************************************
 * IInternetZoneManagerEx2 interface
 */
#ifndef __IInternetZoneManagerEx2_INTERFACE_DEFINED__
#define __IInternetZoneManagerEx2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternetZoneManagerEx2, 0xedc17559, 0xdd5d, 0x4846, 0x8e,0xef, 0x8b,0xec,0xba,0x5a,0x4a,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("edc17559-dd5d-4846-8eef-8becba5a4abf")
IInternetZoneManagerEx2 : public IInternetZoneManagerEx
{
    virtual HRESULT STDMETHODCALLTYPE GetZoneAttributesEx(
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetZoneSecurityState(
        DWORD dwZoneIndex,
        WINBOOL fRespectPolicy,
        LPDWORD pdwState,
        WINBOOL *pfPolicyEncountered) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIESecurityState(
        WINBOOL fRespectPolicy,
        LPDWORD pdwState,
        WINBOOL *pfPolicyEncountered,
        WINBOOL fNoCache) = 0;

    virtual HRESULT STDMETHODCALLTYPE FixUnsecureSettings(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternetZoneManagerEx2, 0xedc17559, 0xdd5d, 0x4846, 0x8e,0xef, 0x8b,0xec,0xba,0x5a,0x4a,0xbf)
#endif
#else
typedef struct IInternetZoneManagerEx2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternetZoneManagerEx2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternetZoneManagerEx2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternetZoneManagerEx2 *This);

    /*** IInternetZoneManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetZoneAttributes)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes);

    HRESULT (STDMETHODCALLTYPE *SetZoneAttributes)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes);

    HRESULT (STDMETHODCALLTYPE *GetZoneCustomPolicy)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        REFGUID guidKey,
        BYTE **ppPolicy,
        DWORD *pcbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *SetZoneCustomPolicy)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        REFGUID guidKey,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *GetZoneActionPolicy)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *SetZoneActionPolicy)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg);

    HRESULT (STDMETHODCALLTYPE *PromptAction)(
        IInternetZoneManagerEx2 *This,
        DWORD dwAction,
        HWND hwndParent,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwPromptFlags);

    HRESULT (STDMETHODCALLTYPE *LogAction)(
        IInternetZoneManagerEx2 *This,
        DWORD dwAction,
        LPCWSTR pwszUrl,
        LPCWSTR pwszText,
        DWORD dwLogFlags);

    HRESULT (STDMETHODCALLTYPE *CreateZoneEnumerator)(
        IInternetZoneManagerEx2 *This,
        DWORD *pdwEnum,
        DWORD *pdwCount,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetZoneAt)(
        IInternetZoneManagerEx2 *This,
        DWORD dwEnum,
        DWORD dwIndex,
        DWORD *pdwZone);

    HRESULT (STDMETHODCALLTYPE *DestroyZoneEnumerator)(
        IInternetZoneManagerEx2 *This,
        DWORD dwEnum);

    HRESULT (STDMETHODCALLTYPE *CopyTemplatePoliciesToZone)(
        IInternetZoneManagerEx2 *This,
        DWORD dwTemplate,
        DWORD dwZone,
        DWORD dwReserved);

    /*** IInternetZoneManagerEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetZoneActionPolicyEx)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *SetZoneActionPolicyEx)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        DWORD dwAction,
        BYTE *pPolicy,
        DWORD cbPolicy,
        URLZONEREG urlZoneReg,
        DWORD dwFlags);

    /*** IInternetZoneManagerEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetZoneAttributesEx)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZone,
        ZONEATTRIBUTES *pZoneAttributes,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetZoneSecurityState)(
        IInternetZoneManagerEx2 *This,
        DWORD dwZoneIndex,
        WINBOOL fRespectPolicy,
        LPDWORD pdwState,
        WINBOOL *pfPolicyEncountered);

    HRESULT (STDMETHODCALLTYPE *GetIESecurityState)(
        IInternetZoneManagerEx2 *This,
        WINBOOL fRespectPolicy,
        LPDWORD pdwState,
        WINBOOL *pfPolicyEncountered,
        WINBOOL fNoCache);

    HRESULT (STDMETHODCALLTYPE *FixUnsecureSettings)(
        IInternetZoneManagerEx2 *This);

    END_INTERFACE
} IInternetZoneManagerEx2Vtbl;

interface IInternetZoneManagerEx2 {
    CONST_VTBL IInternetZoneManagerEx2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternetZoneManagerEx2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternetZoneManagerEx2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternetZoneManagerEx2_Release(This) (This)->lpVtbl->Release(This)
/*** IInternetZoneManager methods ***/
#define IInternetZoneManagerEx2_GetZoneAttributes(This,dwZone,pZoneAttributes) (This)->lpVtbl->GetZoneAttributes(This,dwZone,pZoneAttributes)
#define IInternetZoneManagerEx2_SetZoneAttributes(This,dwZone,pZoneAttributes) (This)->lpVtbl->SetZoneAttributes(This,dwZone,pZoneAttributes)
#define IInternetZoneManagerEx2_GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg) (This)->lpVtbl->GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg)
#define IInternetZoneManagerEx2_SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManagerEx2_GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManagerEx2_SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg) (This)->lpVtbl->SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg)
#define IInternetZoneManagerEx2_PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags) (This)->lpVtbl->PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags)
#define IInternetZoneManagerEx2_LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags) (This)->lpVtbl->LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags)
#define IInternetZoneManagerEx2_CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags) (This)->lpVtbl->CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags)
#define IInternetZoneManagerEx2_GetZoneAt(This,dwEnum,dwIndex,pdwZone) (This)->lpVtbl->GetZoneAt(This,dwEnum,dwIndex,pdwZone)
#define IInternetZoneManagerEx2_DestroyZoneEnumerator(This,dwEnum) (This)->lpVtbl->DestroyZoneEnumerator(This,dwEnum)
#define IInternetZoneManagerEx2_CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved) (This)->lpVtbl->CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved)
/*** IInternetZoneManagerEx methods ***/
#define IInternetZoneManagerEx2_GetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags) (This)->lpVtbl->GetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags)
#define IInternetZoneManagerEx2_SetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags) (This)->lpVtbl->SetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags)
/*** IInternetZoneManagerEx2 methods ***/
#define IInternetZoneManagerEx2_GetZoneAttributesEx(This,dwZone,pZoneAttributes,dwFlags) (This)->lpVtbl->GetZoneAttributesEx(This,dwZone,pZoneAttributes,dwFlags)
#define IInternetZoneManagerEx2_GetZoneSecurityState(This,dwZoneIndex,fRespectPolicy,pdwState,pfPolicyEncountered) (This)->lpVtbl->GetZoneSecurityState(This,dwZoneIndex,fRespectPolicy,pdwState,pfPolicyEncountered)
#define IInternetZoneManagerEx2_GetIESecurityState(This,fRespectPolicy,pdwState,pfPolicyEncountered,fNoCache) (This)->lpVtbl->GetIESecurityState(This,fRespectPolicy,pdwState,pfPolicyEncountered,fNoCache)
#define IInternetZoneManagerEx2_FixUnsecureSettings(This) (This)->lpVtbl->FixUnsecureSettings(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternetZoneManagerEx2_QueryInterface(IInternetZoneManagerEx2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternetZoneManagerEx2_AddRef(IInternetZoneManagerEx2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternetZoneManagerEx2_Release(IInternetZoneManagerEx2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternetZoneManager methods ***/
static inline HRESULT IInternetZoneManagerEx2_GetZoneAttributes(IInternetZoneManagerEx2* This,DWORD dwZone,ZONEATTRIBUTES *pZoneAttributes) {
    return This->lpVtbl->GetZoneAttributes(This,dwZone,pZoneAttributes);
}
static inline HRESULT IInternetZoneManagerEx2_SetZoneAttributes(IInternetZoneManagerEx2* This,DWORD dwZone,ZONEATTRIBUTES *pZoneAttributes) {
    return This->lpVtbl->SetZoneAttributes(This,dwZone,pZoneAttributes);
}
static inline HRESULT IInternetZoneManagerEx2_GetZoneCustomPolicy(IInternetZoneManagerEx2* This,DWORD dwZone,REFGUID guidKey,BYTE **ppPolicy,DWORD *pcbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->GetZoneCustomPolicy(This,dwZone,guidKey,ppPolicy,pcbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx2_SetZoneCustomPolicy(IInternetZoneManagerEx2* This,DWORD dwZone,REFGUID guidKey,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->SetZoneCustomPolicy(This,dwZone,guidKey,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx2_GetZoneActionPolicy(IInternetZoneManagerEx2* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->GetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx2_SetZoneActionPolicy(IInternetZoneManagerEx2* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg) {
    return This->lpVtbl->SetZoneActionPolicy(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg);
}
static inline HRESULT IInternetZoneManagerEx2_PromptAction(IInternetZoneManagerEx2* This,DWORD dwAction,HWND hwndParent,LPCWSTR pwszUrl,LPCWSTR pwszText,DWORD dwPromptFlags) {
    return This->lpVtbl->PromptAction(This,dwAction,hwndParent,pwszUrl,pwszText,dwPromptFlags);
}
static inline HRESULT IInternetZoneManagerEx2_LogAction(IInternetZoneManagerEx2* This,DWORD dwAction,LPCWSTR pwszUrl,LPCWSTR pwszText,DWORD dwLogFlags) {
    return This->lpVtbl->LogAction(This,dwAction,pwszUrl,pwszText,dwLogFlags);
}
static inline HRESULT IInternetZoneManagerEx2_CreateZoneEnumerator(IInternetZoneManagerEx2* This,DWORD *pdwEnum,DWORD *pdwCount,DWORD dwFlags) {
    return This->lpVtbl->CreateZoneEnumerator(This,pdwEnum,pdwCount,dwFlags);
}
static inline HRESULT IInternetZoneManagerEx2_GetZoneAt(IInternetZoneManagerEx2* This,DWORD dwEnum,DWORD dwIndex,DWORD *pdwZone) {
    return This->lpVtbl->GetZoneAt(This,dwEnum,dwIndex,pdwZone);
}
static inline HRESULT IInternetZoneManagerEx2_DestroyZoneEnumerator(IInternetZoneManagerEx2* This,DWORD dwEnum) {
    return This->lpVtbl->DestroyZoneEnumerator(This,dwEnum);
}
static inline HRESULT IInternetZoneManagerEx2_CopyTemplatePoliciesToZone(IInternetZoneManagerEx2* This,DWORD dwTemplate,DWORD dwZone,DWORD dwReserved) {
    return This->lpVtbl->CopyTemplatePoliciesToZone(This,dwTemplate,dwZone,dwReserved);
}
/*** IInternetZoneManagerEx methods ***/
static inline HRESULT IInternetZoneManagerEx2_GetZoneActionPolicyEx(IInternetZoneManagerEx2* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg,DWORD dwFlags) {
    return This->lpVtbl->GetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags);
}
static inline HRESULT IInternetZoneManagerEx2_SetZoneActionPolicyEx(IInternetZoneManagerEx2* This,DWORD dwZone,DWORD dwAction,BYTE *pPolicy,DWORD cbPolicy,URLZONEREG urlZoneReg,DWORD dwFlags) {
    return This->lpVtbl->SetZoneActionPolicyEx(This,dwZone,dwAction,pPolicy,cbPolicy,urlZoneReg,dwFlags);
}
/*** IInternetZoneManagerEx2 methods ***/
static inline HRESULT IInternetZoneManagerEx2_GetZoneAttributesEx(IInternetZoneManagerEx2* This,DWORD dwZone,ZONEATTRIBUTES *pZoneAttributes,DWORD dwFlags) {
    return This->lpVtbl->GetZoneAttributesEx(This,dwZone,pZoneAttributes,dwFlags);
}
static inline HRESULT IInternetZoneManagerEx2_GetZoneSecurityState(IInternetZoneManagerEx2* This,DWORD dwZoneIndex,WINBOOL fRespectPolicy,LPDWORD pdwState,WINBOOL *pfPolicyEncountered) {
    return This->lpVtbl->GetZoneSecurityState(This,dwZoneIndex,fRespectPolicy,pdwState,pfPolicyEncountered);
}
static inline HRESULT IInternetZoneManagerEx2_GetIESecurityState(IInternetZoneManagerEx2* This,WINBOOL fRespectPolicy,LPDWORD pdwState,WINBOOL *pfPolicyEncountered,WINBOOL fNoCache) {
    return This->lpVtbl->GetIESecurityState(This,fRespectPolicy,pdwState,pfPolicyEncountered,fNoCache);
}
static inline HRESULT IInternetZoneManagerEx2_FixUnsecureSettings(IInternetZoneManagerEx2* This) {
    return This->lpVtbl->FixUnsecureSettings(This);
}
#endif
#endif

#endif


#endif  /* __IInternetZoneManagerEx2_INTERFACE_DEFINED__ */

#endif
#endif

EXTERN_C const IID CLSID_SoftDistExt;

#ifndef _LPSOFTDISTEXT_DEFINED
#define _LPSOFTDISTEXT_DEFINED

#define SOFTDIST_FLAG_USAGE_EMAIL 0x1
#define SOFTDIST_FLAG_USAGE_PRECACHE 0x2
#define SOFTDIST_FLAG_USAGE_AUTOINSTALL 0x4
#define SOFTDIST_FLAG_DELETE_SUBSCRIPTION 0x8

#define SOFTDIST_ADSTATE_NONE 0x0
#define SOFTDIST_ADSTATE_AVAILABLE 0x1
#define SOFTDIST_ADSTATE_DOWNLOADED 0x2
#define SOFTDIST_ADSTATE_INSTALLED 0x3

typedef struct _tagCODEBASEHOLD {
    ULONG cbSize;
    LPWSTR szDistUnit;
    LPWSTR szCodeBase;
    DWORD dwVersionMS;
    DWORD dwVersionLS;
    DWORD dwStyle;
} CODEBASEHOLD;
typedef struct _tagCODEBASEHOLD *LPCODEBASEHOLD;

typedef struct _tagSOFTDISTINFO {
    ULONG cbSize;
    DWORD dwFlags;
    DWORD dwAdState;
    LPWSTR szTitle;
    LPWSTR szAbstract;
    LPWSTR szHREF;
    DWORD dwInstalledVersionMS;
    DWORD dwInstalledVersionLS;
    DWORD dwUpdateVersionMS;
    DWORD dwUpdateVersionLS;
    DWORD dwAdvertisedVersionMS;
    DWORD dwAdvertisedVersionLS;
    DWORD dwReserved;
} SOFTDISTINFO;
typedef struct _tagSOFTDISTINFO *LPSOFTDISTINFO;

/*****************************************************************************
 * ISoftDistExt interface
 */
#ifndef __ISoftDistExt_INTERFACE_DEFINED__
#define __ISoftDistExt_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISoftDistExt, 0xb15b8dc1, 0xc7e1, 0x11d0, 0x86,0x80, 0x00,0xaa,0x00,0xbd,0xcb,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b15b8dc1-c7e1-11d0-8680-00aa00bdcb71")
ISoftDistExt : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ProcessSoftDist(
        LPCWSTR szCDFURL,
        IXMLElement *pSoftDistElement,
        LPSOFTDISTINFO lpsdi) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFirstCodeBase(
        LPWSTR *szCodeBase,
        LPDWORD dwMaxSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNextCodeBase(
        LPWSTR *szCodeBase,
        LPDWORD dwMaxSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE AsyncInstallDistributionUnit(
        IBindCtx *pbc,
        LPVOID pvReserved,
        DWORD flags,
        LPCODEBASEHOLD lpcbh) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISoftDistExt, 0xb15b8dc1, 0xc7e1, 0x11d0, 0x86,0x80, 0x00,0xaa,0x00,0xbd,0xcb,0x71)
#endif
#else
typedef struct ISoftDistExtVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISoftDistExt *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISoftDistExt *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISoftDistExt *This);

    /*** ISoftDistExt methods ***/
    HRESULT (STDMETHODCALLTYPE *ProcessSoftDist)(
        ISoftDistExt *This,
        LPCWSTR szCDFURL,
        IXMLElement *pSoftDistElement,
        LPSOFTDISTINFO lpsdi);

    HRESULT (STDMETHODCALLTYPE *GetFirstCodeBase)(
        ISoftDistExt *This,
        LPWSTR *szCodeBase,
        LPDWORD dwMaxSize);

    HRESULT (STDMETHODCALLTYPE *GetNextCodeBase)(
        ISoftDistExt *This,
        LPWSTR *szCodeBase,
        LPDWORD dwMaxSize);

    HRESULT (STDMETHODCALLTYPE *AsyncInstallDistributionUnit)(
        ISoftDistExt *This,
        IBindCtx *pbc,
        LPVOID pvReserved,
        DWORD flags,
        LPCODEBASEHOLD lpcbh);

    END_INTERFACE
} ISoftDistExtVtbl;

interface ISoftDistExt {
    CONST_VTBL ISoftDistExtVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISoftDistExt_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISoftDistExt_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISoftDistExt_Release(This) (This)->lpVtbl->Release(This)
/*** ISoftDistExt methods ***/
#define ISoftDistExt_ProcessSoftDist(This,szCDFURL,pSoftDistElement,lpsdi) (This)->lpVtbl->ProcessSoftDist(This,szCDFURL,pSoftDistElement,lpsdi)
#define ISoftDistExt_GetFirstCodeBase(This,szCodeBase,dwMaxSize) (This)->lpVtbl->GetFirstCodeBase(This,szCodeBase,dwMaxSize)
#define ISoftDistExt_GetNextCodeBase(This,szCodeBase,dwMaxSize) (This)->lpVtbl->GetNextCodeBase(This,szCodeBase,dwMaxSize)
#define ISoftDistExt_AsyncInstallDistributionUnit(This,pbc,pvReserved,flags,lpcbh) (This)->lpVtbl->AsyncInstallDistributionUnit(This,pbc,pvReserved,flags,lpcbh)
#else
/*** IUnknown methods ***/
static inline HRESULT ISoftDistExt_QueryInterface(ISoftDistExt* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISoftDistExt_AddRef(ISoftDistExt* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISoftDistExt_Release(ISoftDistExt* This) {
    return This->lpVtbl->Release(This);
}
/*** ISoftDistExt methods ***/
static inline HRESULT ISoftDistExt_ProcessSoftDist(ISoftDistExt* This,LPCWSTR szCDFURL,IXMLElement *pSoftDistElement,LPSOFTDISTINFO lpsdi) {
    return This->lpVtbl->ProcessSoftDist(This,szCDFURL,pSoftDistElement,lpsdi);
}
static inline HRESULT ISoftDistExt_GetFirstCodeBase(ISoftDistExt* This,LPWSTR *szCodeBase,LPDWORD dwMaxSize) {
    return This->lpVtbl->GetFirstCodeBase(This,szCodeBase,dwMaxSize);
}
static inline HRESULT ISoftDistExt_GetNextCodeBase(ISoftDistExt* This,LPWSTR *szCodeBase,LPDWORD dwMaxSize) {
    return This->lpVtbl->GetNextCodeBase(This,szCodeBase,dwMaxSize);
}
static inline HRESULT ISoftDistExt_AsyncInstallDistributionUnit(ISoftDistExt* This,IBindCtx *pbc,LPVOID pvReserved,DWORD flags,LPCODEBASEHOLD lpcbh) {
    return This->lpVtbl->AsyncInstallDistributionUnit(This,pbc,pvReserved,flags,lpcbh);
}
#endif
#endif

#endif


#endif  /* __ISoftDistExt_INTERFACE_DEFINED__ */


STDAPI GetSoftwareUpdateInfo(LPCWSTR szDistUnit, LPSOFTDISTINFO psdi);
STDAPI SetSoftwareUpdateAdvertisementState(LPCWSTR szDistUnit, DWORD dwAdState, DWORD dwAdvertisedVersionMS, DWORD dwAdvertisedVersionLS);
#endif

#ifndef _LPCATALOGFILEINFO_DEFINED
#define _LPCATALOGFILEINFO_DEFINED

/*****************************************************************************
 * ICatalogFileInfo interface
 */
#ifndef __ICatalogFileInfo_INTERFACE_DEFINED__
#define __ICatalogFileInfo_INTERFACE_DEFINED__

typedef ICatalogFileInfo *LPCATALOGFILEINFO;

DEFINE_GUID(IID_ICatalogFileInfo, 0x711c7600, 0x6b48, 0x11d1, 0xb4,0x03, 0x00,0xaa,0x00,0xb9,0x2a,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("711c7600-6b48-11d1-b403-00aa00b92af1")
ICatalogFileInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCatalogFile(
        LPSTR *ppszCatalogFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetJavaTrust(
        void **ppJavaTrust) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICatalogFileInfo, 0x711c7600, 0x6b48, 0x11d1, 0xb4,0x03, 0x00,0xaa,0x00,0xb9,0x2a,0xf1)
#endif
#else
typedef struct ICatalogFileInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICatalogFileInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICatalogFileInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICatalogFileInfo *This);

    /*** ICatalogFileInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCatalogFile)(
        ICatalogFileInfo *This,
        LPSTR *ppszCatalogFile);

    HRESULT (STDMETHODCALLTYPE *GetJavaTrust)(
        ICatalogFileInfo *This,
        void **ppJavaTrust);

    END_INTERFACE
} ICatalogFileInfoVtbl;

interface ICatalogFileInfo {
    CONST_VTBL ICatalogFileInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICatalogFileInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICatalogFileInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICatalogFileInfo_Release(This) (This)->lpVtbl->Release(This)
/*** ICatalogFileInfo methods ***/
#define ICatalogFileInfo_GetCatalogFile(This,ppszCatalogFile) (This)->lpVtbl->GetCatalogFile(This,ppszCatalogFile)
#define ICatalogFileInfo_GetJavaTrust(This,ppJavaTrust) (This)->lpVtbl->GetJavaTrust(This,ppJavaTrust)
#else
/*** IUnknown methods ***/
static inline HRESULT ICatalogFileInfo_QueryInterface(ICatalogFileInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICatalogFileInfo_AddRef(ICatalogFileInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICatalogFileInfo_Release(ICatalogFileInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** ICatalogFileInfo methods ***/
static inline HRESULT ICatalogFileInfo_GetCatalogFile(ICatalogFileInfo* This,LPSTR *ppszCatalogFile) {
    return This->lpVtbl->GetCatalogFile(This,ppszCatalogFile);
}
static inline HRESULT ICatalogFileInfo_GetJavaTrust(ICatalogFileInfo* This,void **ppJavaTrust) {
    return This->lpVtbl->GetJavaTrust(This,ppJavaTrust);
}
#endif
#endif

#endif


#endif  /* __ICatalogFileInfo_INTERFACE_DEFINED__ */

#endif

#ifndef _LPDATAFILTER_DEFINED
#define _LPDATAFILTER_DEFINED

/*****************************************************************************
 * IDataFilter interface
 */
#ifndef __IDataFilter_INTERFACE_DEFINED__
#define __IDataFilter_INTERFACE_DEFINED__

typedef IDataFilter *LPDATAFILTER;

DEFINE_GUID(IID_IDataFilter, 0x69d14c80, 0xc18e, 0x11d0, 0xa9,0xce, 0x00,0x60,0x97,0x94,0x23,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("69d14c80-c18e-11d0-a9ce-006097942311")
IDataFilter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE DoEncode(
        DWORD dwFlags,
        LONG lInBufferSize,
        BYTE *pbInBuffer,
        LONG lOutBufferSize,
        BYTE *pbOutBuffer,
        LONG lInBytesAvailable,
        LONG *plInBytesRead,
        LONG *plOutBytesWritten,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoDecode(
        DWORD dwFlags,
        LONG lInBufferSize,
        BYTE *pbInBuffer,
        LONG lOutBufferSize,
        BYTE *pbOutBuffer,
        LONG lInBytesAvailable,
        LONG *plInBytesRead,
        LONG *plOutBytesWritten,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEncodingLevel(
        DWORD dwEncLevel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDataFilter, 0x69d14c80, 0xc18e, 0x11d0, 0xa9,0xce, 0x00,0x60,0x97,0x94,0x23,0x11)
#endif
#else
typedef struct IDataFilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDataFilter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDataFilter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDataFilter *This);

    /*** IDataFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *DoEncode)(
        IDataFilter *This,
        DWORD dwFlags,
        LONG lInBufferSize,
        BYTE *pbInBuffer,
        LONG lOutBufferSize,
        BYTE *pbOutBuffer,
        LONG lInBytesAvailable,
        LONG *plInBytesRead,
        LONG *plOutBytesWritten,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *DoDecode)(
        IDataFilter *This,
        DWORD dwFlags,
        LONG lInBufferSize,
        BYTE *pbInBuffer,
        LONG lOutBufferSize,
        BYTE *pbOutBuffer,
        LONG lInBytesAvailable,
        LONG *plInBytesRead,
        LONG *plOutBytesWritten,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *SetEncodingLevel)(
        IDataFilter *This,
        DWORD dwEncLevel);

    END_INTERFACE
} IDataFilterVtbl;

interface IDataFilter {
    CONST_VTBL IDataFilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDataFilter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDataFilter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDataFilter_Release(This) (This)->lpVtbl->Release(This)
/*** IDataFilter methods ***/
#define IDataFilter_DoEncode(This,dwFlags,lInBufferSize,pbInBuffer,lOutBufferSize,pbOutBuffer,lInBytesAvailable,plInBytesRead,plOutBytesWritten,dwReserved) (This)->lpVtbl->DoEncode(This,dwFlags,lInBufferSize,pbInBuffer,lOutBufferSize,pbOutBuffer,lInBytesAvailable,plInBytesRead,plOutBytesWritten,dwReserved)
#define IDataFilter_DoDecode(This,dwFlags,lInBufferSize,pbInBuffer,lOutBufferSize,pbOutBuffer,lInBytesAvailable,plInBytesRead,plOutBytesWritten,dwReserved) (This)->lpVtbl->DoDecode(This,dwFlags,lInBufferSize,pbInBuffer,lOutBufferSize,pbOutBuffer,lInBytesAvailable,plInBytesRead,plOutBytesWritten,dwReserved)
#define IDataFilter_SetEncodingLevel(This,dwEncLevel) (This)->lpVtbl->SetEncodingLevel(This,dwEncLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT IDataFilter_QueryInterface(IDataFilter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDataFilter_AddRef(IDataFilter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDataFilter_Release(IDataFilter* This) {
    return This->lpVtbl->Release(This);
}
/*** IDataFilter methods ***/
static inline HRESULT IDataFilter_DoEncode(IDataFilter* This,DWORD dwFlags,LONG lInBufferSize,BYTE *pbInBuffer,LONG lOutBufferSize,BYTE *pbOutBuffer,LONG lInBytesAvailable,LONG *plInBytesRead,LONG *plOutBytesWritten,DWORD dwReserved) {
    return This->lpVtbl->DoEncode(This,dwFlags,lInBufferSize,pbInBuffer,lOutBufferSize,pbOutBuffer,lInBytesAvailable,plInBytesRead,plOutBytesWritten,dwReserved);
}
static inline HRESULT IDataFilter_DoDecode(IDataFilter* This,DWORD dwFlags,LONG lInBufferSize,BYTE *pbInBuffer,LONG lOutBufferSize,BYTE *pbOutBuffer,LONG lInBytesAvailable,LONG *plInBytesRead,LONG *plOutBytesWritten,DWORD dwReserved) {
    return This->lpVtbl->DoDecode(This,dwFlags,lInBufferSize,pbInBuffer,lOutBufferSize,pbOutBuffer,lInBytesAvailable,plInBytesRead,plOutBytesWritten,dwReserved);
}
static inline HRESULT IDataFilter_SetEncodingLevel(IDataFilter* This,DWORD dwEncLevel) {
    return This->lpVtbl->SetEncodingLevel(This,dwEncLevel);
}
#endif
#endif

#endif


#endif  /* __IDataFilter_INTERFACE_DEFINED__ */

#endif

#ifndef _LPENCODINGFILTERFACTORY_DEFINED
#define _LPENCODINGFILTERFACTORY_DEFINED

typedef struct _tagPROTOCOLFILTERDATA {
    DWORD cbSize;
    IInternetProtocolSink *pProtocolSink;
    IInternetProtocol *pProtocol;
    IUnknown *pUnk;
    DWORD dwFilterFlags;
} PROTOCOLFILTERDATA;

/*****************************************************************************
 * IEncodingFilterFactory interface
 */
#ifndef __IEncodingFilterFactory_INTERFACE_DEFINED__
#define __IEncodingFilterFactory_INTERFACE_DEFINED__

typedef IEncodingFilterFactory *LPENCODINGFILTERFACTORY;

typedef struct _tagDATAINFO {
    ULONG ulTotalSize;
    ULONG ulavrPacketSize;
    ULONG ulConnectSpeed;
    ULONG ulProcessorSpeed;
} DATAINFO;

DEFINE_GUID(IID_IEncodingFilterFactory, 0x70bdde00, 0xc18e, 0x11d0, 0xa9,0xce, 0x00,0x60,0x97,0x94,0x23,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("70bdde00-c18e-11d0-a9ce-006097942311")
IEncodingFilterFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FindBestFilter(
        LPCWSTR pwzCodeIn,
        LPCWSTR pwzCodeOut,
        DATAINFO info,
        IDataFilter **ppDF) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultFilter(
        LPCWSTR pwzCodeIn,
        LPCWSTR pwzCodeOut,
        IDataFilter **ppDF) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEncodingFilterFactory, 0x70bdde00, 0xc18e, 0x11d0, 0xa9,0xce, 0x00,0x60,0x97,0x94,0x23,0x11)
#endif
#else
typedef struct IEncodingFilterFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEncodingFilterFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEncodingFilterFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEncodingFilterFactory *This);

    /*** IEncodingFilterFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *FindBestFilter)(
        IEncodingFilterFactory *This,
        LPCWSTR pwzCodeIn,
        LPCWSTR pwzCodeOut,
        DATAINFO info,
        IDataFilter **ppDF);

    HRESULT (STDMETHODCALLTYPE *GetDefaultFilter)(
        IEncodingFilterFactory *This,
        LPCWSTR pwzCodeIn,
        LPCWSTR pwzCodeOut,
        IDataFilter **ppDF);

    END_INTERFACE
} IEncodingFilterFactoryVtbl;

interface IEncodingFilterFactory {
    CONST_VTBL IEncodingFilterFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEncodingFilterFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEncodingFilterFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEncodingFilterFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IEncodingFilterFactory methods ***/
#define IEncodingFilterFactory_FindBestFilter(This,pwzCodeIn,pwzCodeOut,info,ppDF) (This)->lpVtbl->FindBestFilter(This,pwzCodeIn,pwzCodeOut,info,ppDF)
#define IEncodingFilterFactory_GetDefaultFilter(This,pwzCodeIn,pwzCodeOut,ppDF) (This)->lpVtbl->GetDefaultFilter(This,pwzCodeIn,pwzCodeOut,ppDF)
#else
/*** IUnknown methods ***/
static inline HRESULT IEncodingFilterFactory_QueryInterface(IEncodingFilterFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEncodingFilterFactory_AddRef(IEncodingFilterFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEncodingFilterFactory_Release(IEncodingFilterFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IEncodingFilterFactory methods ***/
static inline HRESULT IEncodingFilterFactory_FindBestFilter(IEncodingFilterFactory* This,LPCWSTR pwzCodeIn,LPCWSTR pwzCodeOut,DATAINFO info,IDataFilter **ppDF) {
    return This->lpVtbl->FindBestFilter(This,pwzCodeIn,pwzCodeOut,info,ppDF);
}
static inline HRESULT IEncodingFilterFactory_GetDefaultFilter(IEncodingFilterFactory* This,LPCWSTR pwzCodeIn,LPCWSTR pwzCodeOut,IDataFilter **ppDF) {
    return This->lpVtbl->GetDefaultFilter(This,pwzCodeIn,pwzCodeOut,ppDF);
}
#endif
#endif

#endif


#endif  /* __IEncodingFilterFactory_INTERFACE_DEFINED__ */

#endif

#ifndef _HITLOGGING_DEFINED
#define _HITLOGGING_DEFINED

WINBOOL WINAPI IsLoggingEnabledA(LPCSTR pszUrl);
WINBOOL WINAPI IsLoggingEnabledW(LPCWSTR pwszUrl);

#define IsLoggingEnabled __MINGW_NAME_AW(IsLoggingEnabled)
typedef struct _tagHIT_LOGGING_INFO {
    DWORD dwStructSize;
    LPSTR lpszLoggedUrlName;
    SYSTEMTIME StartTime;
    SYSTEMTIME EndTime;
    LPSTR lpszExtendedInfo;
} HIT_LOGGING_INFO;
typedef struct _tagHIT_LOGGING_INFO *LPHIT_LOGGING_INFO;

WINBOOL WINAPI WriteHitLogging(LPHIT_LOGGING_INFO lpLogginginfo);

#define CONFIRMSAFETYACTION_LOADOBJECT 0x1

struct CONFIRMSAFETY {
    CLSID clsid;
    IUnknown *pUnk;
    DWORD dwFlags;
};

EXTERN_C const GUID GUID_CUSTOM_CONFIRMOBJECTSAFETY;
#endif

#ifndef _LPIWRAPPEDPROTOCOL_DEFINED
#define _LPIWRAPPEDPROTOCOL_DEFINED

/*****************************************************************************
 * IWrappedProtocol interface
 */
#ifndef __IWrappedProtocol_INTERFACE_DEFINED__
#define __IWrappedProtocol_INTERFACE_DEFINED__

typedef IWrappedProtocol *LPIWRAPPEDPROTOCOL;

DEFINE_GUID(IID_IWrappedProtocol, 0x53c84785, 0x8425, 0x4dc5, 0x97,0x1b, 0xe5,0x8d,0x9c,0x19,0xf9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("53c84785-8425-4dc5-971b-e58d9c19f9b6")
IWrappedProtocol : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetWrapperCode(
        LONG *pnCode,
        DWORD_PTR dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWrappedProtocol, 0x53c84785, 0x8425, 0x4dc5, 0x97,0x1b, 0xe5,0x8d,0x9c,0x19,0xf9,0xb6)
#endif
#else
typedef struct IWrappedProtocolVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWrappedProtocol *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWrappedProtocol *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWrappedProtocol *This);

    /*** IWrappedProtocol methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWrapperCode)(
        IWrappedProtocol *This,
        LONG *pnCode,
        DWORD_PTR dwReserved);

    END_INTERFACE
} IWrappedProtocolVtbl;

interface IWrappedProtocol {
    CONST_VTBL IWrappedProtocolVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWrappedProtocol_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWrappedProtocol_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWrappedProtocol_Release(This) (This)->lpVtbl->Release(This)
/*** IWrappedProtocol methods ***/
#define IWrappedProtocol_GetWrapperCode(This,pnCode,dwReserved) (This)->lpVtbl->GetWrapperCode(This,pnCode,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IWrappedProtocol_QueryInterface(IWrappedProtocol* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWrappedProtocol_AddRef(IWrappedProtocol* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWrappedProtocol_Release(IWrappedProtocol* This) {
    return This->lpVtbl->Release(This);
}
/*** IWrappedProtocol methods ***/
static inline HRESULT IWrappedProtocol_GetWrapperCode(IWrappedProtocol* This,LONG *pnCode,DWORD_PTR dwReserved) {
    return This->lpVtbl->GetWrapperCode(This,pnCode,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IWrappedProtocol_INTERFACE_DEFINED__ */

#endif

#ifndef _LPGETBINDHANDLE_DEFINED
#define _LPGETBINDHANDLE_DEFINED

/*****************************************************************************
 * IGetBindHandle interface
 */
#ifndef __IGetBindHandle_INTERFACE_DEFINED__
#define __IGetBindHandle_INTERFACE_DEFINED__

typedef IGetBindHandle *LPGETBINDHANDLE;

typedef enum __WIDL_urlmon_generated_name_0000001F {
    BINDHANDLETYPES_APPCACHE = 0x0,
    BINDHANDLETYPES_DEPENDENCY = 0x1,
    BINDHANDLETYPES_COUNT = 0x2
} BINDHANDLETYPES;

DEFINE_GUID(IID_IGetBindHandle, 0xaf0ff408, 0x129d, 0x4b20, 0x91,0xf0, 0x02,0xbd,0x23,0xd8,0x83,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("af0ff408-129d-4b20-91f0-02bd23d88352")
IGetBindHandle : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetBindHandle(
        BINDHANDLETYPES enumRequestedHandle,
        HANDLE *pRetHandle) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGetBindHandle, 0xaf0ff408, 0x129d, 0x4b20, 0x91,0xf0, 0x02,0xbd,0x23,0xd8,0x83,0x52)
#endif
#else
typedef struct IGetBindHandleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGetBindHandle *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGetBindHandle *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGetBindHandle *This);

    /*** IGetBindHandle methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBindHandle)(
        IGetBindHandle *This,
        BINDHANDLETYPES enumRequestedHandle,
        HANDLE *pRetHandle);

    END_INTERFACE
} IGetBindHandleVtbl;

interface IGetBindHandle {
    CONST_VTBL IGetBindHandleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGetBindHandle_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGetBindHandle_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGetBindHandle_Release(This) (This)->lpVtbl->Release(This)
/*** IGetBindHandle methods ***/
#define IGetBindHandle_GetBindHandle(This,enumRequestedHandle,pRetHandle) (This)->lpVtbl->GetBindHandle(This,enumRequestedHandle,pRetHandle)
#else
/*** IUnknown methods ***/
static inline HRESULT IGetBindHandle_QueryInterface(IGetBindHandle* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IGetBindHandle_AddRef(IGetBindHandle* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IGetBindHandle_Release(IGetBindHandle* This) {
    return This->lpVtbl->Release(This);
}
/*** IGetBindHandle methods ***/
static inline HRESULT IGetBindHandle_GetBindHandle(IGetBindHandle* This,BINDHANDLETYPES enumRequestedHandle,HANDLE *pRetHandle) {
    return This->lpVtbl->GetBindHandle(This,enumRequestedHandle,pRetHandle);
}
#endif
#endif

#endif


#endif  /* __IGetBindHandle_INTERFACE_DEFINED__ */

#endif

#ifndef _XHRPLUGGABLEPROTOCOL_DEFINED
#define _XHRPLUGGABLEPROTOCOL_DEFINED

typedef struct _tagPROTOCOL_ARGUMENT {
    LPCWSTR szMethod;
    LPCWSTR szTargetUrl;
} PROTOCOL_ARGUMENT;
typedef struct _tagPROTOCOL_ARGUMENT *LPPROTOCOL_ARGUMENT;
#endif

#ifndef _LPBINDCALLBACKREDIRECT_DEFINED
#define _LPBINDCALLBACKREDIRECT_DEFINED

/*****************************************************************************
 * IBindCallbackRedirect interface
 */
#ifndef __IBindCallbackRedirect_INTERFACE_DEFINED__
#define __IBindCallbackRedirect_INTERFACE_DEFINED__

typedef IBindCallbackRedirect *LPBINDCALLBACKREDIRECT;

DEFINE_GUID(IID_IBindCallbackRedirect, 0x11c81bc2, 0x121e, 0x4ed5, 0xb9,0xc4, 0xb4,0x30,0xbd,0x54,0xf2,0xc0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("11c81bc2-121e-4ed5-b9c4-b430bd54f2c0")
IBindCallbackRedirect : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Redirect(
        LPCWSTR lpcUrl,
        VARIANT_BOOL *vbCancel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBindCallbackRedirect, 0x11c81bc2, 0x121e, 0x4ed5, 0xb9,0xc4, 0xb4,0x30,0xbd,0x54,0xf2,0xc0)
#endif
#else
typedef struct IBindCallbackRedirectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBindCallbackRedirect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBindCallbackRedirect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBindCallbackRedirect *This);

    /*** IBindCallbackRedirect methods ***/
    HRESULT (STDMETHODCALLTYPE *Redirect)(
        IBindCallbackRedirect *This,
        LPCWSTR lpcUrl,
        VARIANT_BOOL *vbCancel);

    END_INTERFACE
} IBindCallbackRedirectVtbl;

interface IBindCallbackRedirect {
    CONST_VTBL IBindCallbackRedirectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBindCallbackRedirect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBindCallbackRedirect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBindCallbackRedirect_Release(This) (This)->lpVtbl->Release(This)
/*** IBindCallbackRedirect methods ***/
#define IBindCallbackRedirect_Redirect(This,lpcUrl,vbCancel) (This)->lpVtbl->Redirect(This,lpcUrl,vbCancel)
#else
/*** IUnknown methods ***/
static inline HRESULT IBindCallbackRedirect_QueryInterface(IBindCallbackRedirect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBindCallbackRedirect_AddRef(IBindCallbackRedirect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBindCallbackRedirect_Release(IBindCallbackRedirect* This) {
    return This->lpVtbl->Release(This);
}
/*** IBindCallbackRedirect methods ***/
static inline HRESULT IBindCallbackRedirect_Redirect(IBindCallbackRedirect* This,LPCWSTR lpcUrl,VARIANT_BOOL *vbCancel) {
    return This->lpVtbl->Redirect(This,lpcUrl,vbCancel);
}
#endif
#endif

#endif


#endif  /* __IBindCallbackRedirect_INTERFACE_DEFINED__ */

#endif

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER STGMEDIUM_UserSize     (ULONG *, ULONG, STGMEDIUM *);
unsigned char * __RPC_USER STGMEDIUM_UserMarshal  (ULONG *, unsigned char *, STGMEDIUM *);
unsigned char * __RPC_USER STGMEDIUM_UserUnmarshal(ULONG *, unsigned char *, STGMEDIUM *);
void            __RPC_USER STGMEDIUM_UserFree     (ULONG *, STGMEDIUM *);
ULONG           __RPC_USER CLIPFORMAT_UserSize     (ULONG *, ULONG, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserMarshal  (ULONG *, unsigned char *, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserUnmarshal(ULONG *, unsigned char *, CLIPFORMAT *);
void            __RPC_USER CLIPFORMAT_UserFree     (ULONG *, CLIPFORMAT *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __urlmon_h__ */
