/*** Autogenerated by WIDL 10.12 from include/dimm.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dimm_h__
#define __dimm_h__

/* Forward declarations */

#ifndef __IEnumInputContext_FWD_DEFINED__
#define __IEnumInputContext_FWD_DEFINED__
typedef interface IEnumInputContext IEnumInputContext;
#ifdef __cplusplus
interface IEnumInputContext;
#endif /* __cplusplus */
#endif

#ifndef __IActiveIMMRegistrar_FWD_DEFINED__
#define __IActiveIMMRegistrar_FWD_DEFINED__
typedef interface IActiveIMMRegistrar IActiveIMMRegistrar;
#ifdef __cplusplus
interface IActiveIMMRegistrar;
#endif /* __cplusplus */
#endif

#ifndef __IActiveIMMMessagePumpOwner_FWD_DEFINED__
#define __IActiveIMMMessagePumpOwner_FWD_DEFINED__
typedef interface IActiveIMMMessagePumpOwner IActiveIMMMessagePumpOwner;
#ifdef __cplusplus
interface IActiveIMMMessagePumpOwner;
#endif /* __cplusplus */
#endif

#ifndef __IActiveIMMApp_FWD_DEFINED__
#define __IActiveIMMApp_FWD_DEFINED__
typedef interface IActiveIMMApp IActiveIMMApp;
#ifdef __cplusplus
interface IActiveIMMApp;
#endif /* __cplusplus */
#endif

#ifndef __IActiveIMMIME_FWD_DEFINED__
#define __IActiveIMMIME_FWD_DEFINED__
typedef interface IActiveIMMIME IActiveIMMIME;
#ifdef __cplusplus
interface IActiveIMMIME;
#endif /* __cplusplus */
#endif

#ifndef __IActiveIME_FWD_DEFINED__
#define __IActiveIME_FWD_DEFINED__
typedef interface IActiveIME IActiveIME;
#ifdef __cplusplus
interface IActiveIME;
#endif /* __cplusplus */
#endif

#ifndef __IActiveIME2_FWD_DEFINED__
#define __IActiveIME2_FWD_DEFINED__
typedef interface IActiveIME2 IActiveIME2;
#ifdef __cplusplus
interface IActiveIME2;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRegisterWordA_FWD_DEFINED__
#define __IEnumRegisterWordA_FWD_DEFINED__
typedef interface IEnumRegisterWordA IEnumRegisterWordA;
#ifdef __cplusplus
interface IEnumRegisterWordA;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRegisterWordW_FWD_DEFINED__
#define __IEnumRegisterWordW_FWD_DEFINED__
typedef interface IEnumRegisterWordW IEnumRegisterWordW;
#ifdef __cplusplus
interface IEnumRegisterWordW;
#endif /* __cplusplus */
#endif

#ifndef __CActiveIMM_FWD_DEFINED__
#define __CActiveIMM_FWD_DEFINED__
#ifdef __cplusplus
typedef class CActiveIMM CActiveIMM;
#else
typedef struct CActiveIMM CActiveIMM;
#endif /* defined __cplusplus */
#endif /* defined __CActiveIMM_FWD_DEFINED__ */

/* Headers for imported files */

#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#ifndef __IEnumRegisterWordA_FWD_DEFINED__
#define __IEnumRegisterWordA_FWD_DEFINED__
typedef interface IEnumRegisterWordA IEnumRegisterWordA;
#ifdef __cplusplus
interface IEnumRegisterWordA;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRegisterWordW_FWD_DEFINED__
#define __IEnumRegisterWordW_FWD_DEFINED__
typedef interface IEnumRegisterWordW IEnumRegisterWordW;
#ifdef __cplusplus
interface IEnumRegisterWordW;
#endif /* __cplusplus */
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __ActiveIMM_LIBRARY_DEFINED__
#define __ActiveIMM_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_ActiveIMM, 0x4955dd30, 0xb159, 0x11d0, 0x8f,0xcf, 0x00,0xaa,0x00,0x6b,0xcc,0x59);

#include <imm.h>
#if 0
typedef WORD LANGID;
typedef struct __WIDL_dimm_generated_name_0000000C {
    LPSTR lpReading;
    LPSTR lpWord;
} REGISTERWORDA;
typedef struct __WIDL_dimm_generated_name_0000000D {
    LPWSTR lpReading;
    LPWSTR lpWord;
} REGISTERWORDW;
typedef struct __WIDL_dimm_generated_name_0000000E {
    LONG lfHeight;
    LONG lfWidth;
    LONG lfEscapement;
    LONG lfOrientation;
    LONG lfWeight;
    BYTE lfItalic;
    BYTE lfUnderline;
    BYTE lfStrikeOut;
    BYTE lfCharSet;
    BYTE lfOutPrecision;
    BYTE lfClipPrecision;
    BYTE lfQuality;
    BYTE lfPitchAndFamily;
    CHAR lfFaceName[32];
} LOGFONTA;
typedef struct __WIDL_dimm_generated_name_0000000F {
    LONG lfHeight;
    LONG lfWidth;
    LONG lfEscapement;
    LONG lfOrientation;
    LONG lfWeight;
    BYTE lfItalic;
    BYTE lfUnderline;
    BYTE lfStrikeOut;
    BYTE lfCharSet;
    BYTE lfOutPrecision;
    BYTE lfClipPrecision;
    BYTE lfQuality;
    BYTE lfPitchAndFamily;
    WCHAR lfFaceName[32];
} LOGFONTW;
typedef DWORD HIMC;
typedef DWORD HIMCC;
typedef struct __WIDL_dimm_generated_name_00000010 {
    DWORD dwIndex;
    DWORD dwStyle;
    POINT ptCurrentPos;
    RECT rcArea;
} CANDIDATEFORM;
typedef struct __WIDL_dimm_generated_name_00000011 {
    DWORD dwStyle;
    POINT ptCurrentPos;
    RECT rcArea;
} COMPOSITIONFORM;
typedef struct __WIDL_dimm_generated_name_00000012 {
    DWORD dwSize;
    DWORD dwStyle;
    DWORD dwCount;
    DWORD dwSelection;
    DWORD dwPageStart;
    DWORD dwPageSize;
    DWORD dwOffset[1];
} CANDIDATELIST;
typedef struct __WIDL_dimm_generated_name_00000013 {
    DWORD dwStyle;
    CHAR szDescription[32];
} STYLEBUFA;
typedef struct __WIDL_dimm_generated_name_00000014 {
    DWORD dwStyle;
    WCHAR szDescription[32];
} STYLEBUFW;
typedef WORD ATOM;
typedef struct __WIDL_dimm_generated_name_00000015 {
    UINT cbSize;
    UINT fType;
    UINT fState;
    UINT wID;
    HBITMAP hbmpChecked;
    HBITMAP hbmpUnchecked;
    DWORD dwItemData;
    CHAR szString[80];
    HBITMAP hbmpItem;
} IMEMENUITEMINFOA;
typedef struct __WIDL_dimm_generated_name_00000016 {
    UINT cbSize;
    UINT fType;
    UINT fState;
    UINT wID;
    HBITMAP hbmpChecked;
    HBITMAP hbmpUnchecked;
    DWORD dwItemData;
    WCHAR szString[80];
    HBITMAP hbmpItem;
} IMEMENUITEMINFOW;
#endif
#ifndef _DDKIMM_H_
typedef struct __WIDL_dimm_generated_name_00000017 {
    HWND hWnd;
    WINBOOL fOpen;
    POINT ptStatusWndPos;
    POINT ptSoftKbdPos;
    DWORD fdwConversion;
    DWORD fdwSentence;
    union {
        LOGFONTA A;
        LOGFONTW W;
    } lfFont;
    COMPOSITIONFORM cfCompForm;
    CANDIDATEFORM cfCandForm[4];
    HIMCC hCompStr;
    HIMCC hCandInfo;
    HIMCC hGuideLine;
    HIMCC hPrivate;
    DWORD dwNumMsgBuf;
    HIMCC hMsgBuf;
    DWORD fdwInit;
    DWORD dwReserve[3];
} INPUTCONTEXT;
typedef struct __WIDL_dimm_generated_name_00000018 {
    DWORD dwPrivateDataSize;
    DWORD fdwProperty;
    DWORD fdwConversionCaps;
    DWORD fdwSentenceCaps;
    DWORD fdwUICaps;
    DWORD fdwSCSCaps;
    DWORD fdwSelectCaps;
} IMEINFO;
#endif
/*****************************************************************************
 * IEnumInputContext interface
 */
#ifndef __IEnumInputContext_INTERFACE_DEFINED__
#define __IEnumInputContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumInputContext, 0x09b5eab0, 0xf997, 0x11d1, 0x93,0xd4, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("09b5eab0-f997-11d1-93d4-0060b067b86e")
IEnumInputContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumInputContext **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        HIMC *rgInputContext,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumInputContext, 0x09b5eab0, 0xf997, 0x11d1, 0x93,0xd4, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct IEnumInputContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumInputContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumInputContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumInputContext *This);

    /*** IEnumInputContext methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumInputContext *This,
        IEnumInputContext **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumInputContext *This,
        ULONG ulCount,
        HIMC *rgInputContext,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumInputContext *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumInputContext *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumInputContextVtbl;

interface IEnumInputContext {
    CONST_VTBL IEnumInputContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumInputContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumInputContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumInputContext_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumInputContext methods ***/
#define IEnumInputContext_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumInputContext_Next(This,ulCount,rgInputContext,pcFetched) (This)->lpVtbl->Next(This,ulCount,rgInputContext,pcFetched)
#define IEnumInputContext_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumInputContext_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumInputContext_QueryInterface(IEnumInputContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumInputContext_AddRef(IEnumInputContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumInputContext_Release(IEnumInputContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumInputContext methods ***/
static inline HRESULT IEnumInputContext_Clone(IEnumInputContext* This,IEnumInputContext **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumInputContext_Next(IEnumInputContext* This,ULONG ulCount,HIMC *rgInputContext,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,rgInputContext,pcFetched);
}
static inline HRESULT IEnumInputContext_Reset(IEnumInputContext* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumInputContext_Skip(IEnumInputContext* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumInputContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveIMMRegistrar interface
 */
#ifndef __IActiveIMMRegistrar_INTERFACE_DEFINED__
#define __IActiveIMMRegistrar_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveIMMRegistrar, 0xb3458082, 0xbd00, 0x11d1, 0x93,0x9b, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b3458082-bd00-11d1-939b-0060b067b86e")
IActiveIMMRegistrar : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterIME(
        REFCLSID rclsid,
        LANGID lgid,
        LPCWSTR pszIconFile,
        LPCWSTR pszDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterIME(
        REFCLSID rclsid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveIMMRegistrar, 0xb3458082, 0xbd00, 0x11d1, 0x93,0x9b, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct IActiveIMMRegistrarVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveIMMRegistrar *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveIMMRegistrar *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveIMMRegistrar *This);

    /*** IActiveIMMRegistrar methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterIME)(
        IActiveIMMRegistrar *This,
        REFCLSID rclsid,
        LANGID lgid,
        LPCWSTR pszIconFile,
        LPCWSTR pszDesc);

    HRESULT (STDMETHODCALLTYPE *UnregisterIME)(
        IActiveIMMRegistrar *This,
        REFCLSID rclsid);

    END_INTERFACE
} IActiveIMMRegistrarVtbl;

interface IActiveIMMRegistrar {
    CONST_VTBL IActiveIMMRegistrarVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveIMMRegistrar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveIMMRegistrar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveIMMRegistrar_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveIMMRegistrar methods ***/
#define IActiveIMMRegistrar_RegisterIME(This,rclsid,lgid,pszIconFile,pszDesc) (This)->lpVtbl->RegisterIME(This,rclsid,lgid,pszIconFile,pszDesc)
#define IActiveIMMRegistrar_UnregisterIME(This,rclsid) (This)->lpVtbl->UnregisterIME(This,rclsid)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveIMMRegistrar_QueryInterface(IActiveIMMRegistrar* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveIMMRegistrar_AddRef(IActiveIMMRegistrar* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveIMMRegistrar_Release(IActiveIMMRegistrar* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveIMMRegistrar methods ***/
static inline HRESULT IActiveIMMRegistrar_RegisterIME(IActiveIMMRegistrar* This,REFCLSID rclsid,LANGID lgid,LPCWSTR pszIconFile,LPCWSTR pszDesc) {
    return This->lpVtbl->RegisterIME(This,rclsid,lgid,pszIconFile,pszDesc);
}
static inline HRESULT IActiveIMMRegistrar_UnregisterIME(IActiveIMMRegistrar* This,REFCLSID rclsid) {
    return This->lpVtbl->UnregisterIME(This,rclsid);
}
#endif
#endif

#endif


#endif  /* __IActiveIMMRegistrar_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveIMMMessagePumpOwner interface
 */
#ifndef __IActiveIMMMessagePumpOwner_INTERFACE_DEFINED__
#define __IActiveIMMMessagePumpOwner_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveIMMMessagePumpOwner, 0xb5cf2cfa, 0x8aeb, 0x11d1, 0x93,0x64, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b5cf2cfa-8aeb-11d1-9364-0060b067b86e")
IActiveIMMMessagePumpOwner : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE End(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnTranslateMessage(
        const MSG *pMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveIMMMessagePumpOwner, 0xb5cf2cfa, 0x8aeb, 0x11d1, 0x93,0x64, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct IActiveIMMMessagePumpOwnerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveIMMMessagePumpOwner *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveIMMMessagePumpOwner *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveIMMMessagePumpOwner *This);

    /*** IActiveIMMMessagePumpOwner methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IActiveIMMMessagePumpOwner *This);

    HRESULT (STDMETHODCALLTYPE *End)(
        IActiveIMMMessagePumpOwner *This);

    HRESULT (STDMETHODCALLTYPE *OnTranslateMessage)(
        IActiveIMMMessagePumpOwner *This,
        const MSG *pMsg);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IActiveIMMMessagePumpOwner *This,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IActiveIMMMessagePumpOwner *This,
        DWORD dwCookie);

    END_INTERFACE
} IActiveIMMMessagePumpOwnerVtbl;

interface IActiveIMMMessagePumpOwner {
    CONST_VTBL IActiveIMMMessagePumpOwnerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveIMMMessagePumpOwner_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveIMMMessagePumpOwner_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveIMMMessagePumpOwner_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveIMMMessagePumpOwner methods ***/
#define IActiveIMMMessagePumpOwner_Start(This) (This)->lpVtbl->Start(This)
#define IActiveIMMMessagePumpOwner_End(This) (This)->lpVtbl->End(This)
#define IActiveIMMMessagePumpOwner_OnTranslateMessage(This,pMsg) (This)->lpVtbl->OnTranslateMessage(This,pMsg)
#define IActiveIMMMessagePumpOwner_Pause(This,pdwCookie) (This)->lpVtbl->Pause(This,pdwCookie)
#define IActiveIMMMessagePumpOwner_Resume(This,dwCookie) (This)->lpVtbl->Resume(This,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveIMMMessagePumpOwner_QueryInterface(IActiveIMMMessagePumpOwner* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveIMMMessagePumpOwner_AddRef(IActiveIMMMessagePumpOwner* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveIMMMessagePumpOwner_Release(IActiveIMMMessagePumpOwner* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveIMMMessagePumpOwner methods ***/
static inline HRESULT IActiveIMMMessagePumpOwner_Start(IActiveIMMMessagePumpOwner* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT IActiveIMMMessagePumpOwner_End(IActiveIMMMessagePumpOwner* This) {
    return This->lpVtbl->End(This);
}
static inline HRESULT IActiveIMMMessagePumpOwner_OnTranslateMessage(IActiveIMMMessagePumpOwner* This,const MSG *pMsg) {
    return This->lpVtbl->OnTranslateMessage(This,pMsg);
}
static inline HRESULT IActiveIMMMessagePumpOwner_Pause(IActiveIMMMessagePumpOwner* This,DWORD *pdwCookie) {
    return This->lpVtbl->Pause(This,pdwCookie);
}
static inline HRESULT IActiveIMMMessagePumpOwner_Resume(IActiveIMMMessagePumpOwner* This,DWORD dwCookie) {
    return This->lpVtbl->Resume(This,dwCookie);
}
#endif
#endif

#endif


#endif  /* __IActiveIMMMessagePumpOwner_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveIMMApp interface
 */
#ifndef __IActiveIMMApp_INTERFACE_DEFINED__
#define __IActiveIMMApp_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveIMMApp, 0x08c0e040, 0x62d1, 0x11d1, 0x93,0x26, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("08c0e040-62d1-11d1-9326-0060b067b86e")
IActiveIMMApp : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AssociateContext(
        HWND hWnd,
        HIMC hIME,
        HIMC *phPrev) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConfigureIMEA(
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDA *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConfigureIMEW(
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDW *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateContext(
        HIMC *phIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE DestroyContext(
        HIMC hIME) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRegisterWordA(
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordA **pEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRegisterWordW(
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordW **pEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE EscapeA(
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EscapeW(
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListA(
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListW(
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListCountA(
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListCountW(
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateWindow(
        HIMC hIMC,
        DWORD dwIndex,
        CANDIDATEFORM *pCandidate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionFontA(
        HIMC hIMC,
        LOGFONTA *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionFontW(
        HIMC hIMC,
        LOGFONTW *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionStringA(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionStringW(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionWindow(
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContext(
        HWND hWnd,
        HIMC *phIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConversionListA(
        HKL hKL,
        HIMC hIMC,
        LPSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConversionListW(
        HKL hKL,
        HIMC hIMC,
        LPWSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConversionStatus(
        HIMC hIMC,
        DWORD *pfdwConversion,
        DWORD *pfdwSentence) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultIMEWnd(
        HWND hWnd,
        HWND *phDefWnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescriptionA(
        HKL hKL,
        UINT uBufLen,
        LPSTR szDescription,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescriptionW(
        HKL hKL,
        UINT uBufLen,
        LPWSTR szDescription,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuideLineA(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPSTR pBuf,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuideLineW(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPWSTR pBuf,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIMEFileNameA(
        HKL hKL,
        UINT uBufLen,
        LPSTR szFileName,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIMEFileNameW(
        HKL hKL,
        UINT uBufLen,
        LPWSTR szFileName,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOpenStatus(
        HIMC hIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        HKL hKL,
        DWORD fdwIndex,
        DWORD *pdwProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRegisterWordStyleA(
        HKL hKL,
        UINT nItem,
        STYLEBUFA *pStyleBuf,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRegisterWordStyleW(
        HKL hKL,
        UINT nItem,
        STYLEBUFW *pStyleBuf,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatusWindowPos(
        HIMC hIMC,
        POINT *pptPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVirtualKey(
        HWND hWnd,
        UINT *puVirtualKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallIMEA(
        LPSTR szIMEFileName,
        LPSTR szLayoutText,
        HKL *phKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallIMEW(
        LPWSTR szIMEFileName,
        LPWSTR szLayoutText,
        HKL *phKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsIME(
        HKL hKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsUIMessageA(
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsUIMessageW(
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyIME(
        HIMC hIMC,
        DWORD dwAction,
        DWORD dwIndex,
        DWORD dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterWordA(
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterWordW(
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseContext(
        HWND hWnd,
        HIMC hIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCandidateWindow(
        HIMC hIMC,
        CANDIDATEFORM *pCandidate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionFontA(
        HIMC hIMC,
        LOGFONTA *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionFontW(
        HIMC hIMC,
        LOGFONTW *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionStringA(
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionStringW(
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionWindow(
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConversionStatus(
        HIMC hIMC,
        DWORD fdwConversion,
        DWORD fdwSentence) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpenStatus(
        HIMC hIMC,
        WINBOOL fOpen) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStatusWindowPos(
        HIMC hIMC,
        POINT *pptPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE SimulateHotKey(
        HWND hWnd,
        DWORD dwHotKeyID) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterWordA(
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szUnregister) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterWordW(
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szUnregister) = 0;

    virtual HRESULT STDMETHODCALLTYPE Activate(
        WINBOOL fRestoreLayout) = 0;

    virtual HRESULT STDMETHODCALLTYPE Deactivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDefWindowProc(
        HWND hWnd,
        UINT Msg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE FilterClientWindows(
        ATOM *aaClassList,
        UINT uSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodePageA(
        HKL hKL,
        UINT *uCodePage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLangId(
        HKL hKL,
        LANGID *plid) = 0;

    virtual HRESULT STDMETHODCALLTYPE AssociateContextEx(
        HWND hWnd,
        HIMC hIMC,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableIME(
        DWORD idThread) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImeMenuItemsA(
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOA *pImeParentMenu,
        IMEMENUITEMINFOA *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImeMenuItemsW(
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOW *pImeParentMenu,
        IMEMENUITEMINFOW *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumInputContext(
        DWORD idThread,
        IEnumInputContext **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveIMMApp, 0x08c0e040, 0x62d1, 0x11d1, 0x93,0x26, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct IActiveIMMAppVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveIMMApp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveIMMApp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveIMMApp *This);

    /*** IActiveIMMApp methods ***/
    HRESULT (STDMETHODCALLTYPE *AssociateContext)(
        IActiveIMMApp *This,
        HWND hWnd,
        HIMC hIME,
        HIMC *phPrev);

    HRESULT (STDMETHODCALLTYPE *ConfigureIMEA)(
        IActiveIMMApp *This,
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDA *pData);

    HRESULT (STDMETHODCALLTYPE *ConfigureIMEW)(
        IActiveIMMApp *This,
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDW *pData);

    HRESULT (STDMETHODCALLTYPE *CreateContext)(
        IActiveIMMApp *This,
        HIMC *phIMC);

    HRESULT (STDMETHODCALLTYPE *DestroyContext)(
        IActiveIMMApp *This,
        HIMC hIME);

    HRESULT (STDMETHODCALLTYPE *EnumRegisterWordA)(
        IActiveIMMApp *This,
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordA **pEnum);

    HRESULT (STDMETHODCALLTYPE *EnumRegisterWordW)(
        IActiveIMMApp *This,
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordW **pEnum);

    HRESULT (STDMETHODCALLTYPE *EscapeA)(
        IActiveIMMApp *This,
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *EscapeW)(
        IActiveIMMApp *This,
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListCountA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListCountW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen);

    HRESULT (STDMETHODCALLTYPE *GetCandidateWindow)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        CANDIDATEFORM *pCandidate);

    HRESULT (STDMETHODCALLTYPE *GetCompositionFontA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        LOGFONTA *plf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionFontW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        LOGFONTW *plf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionStringA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionStringW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionWindow)(
        IActiveIMMApp *This,
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm);

    HRESULT (STDMETHODCALLTYPE *GetContext)(
        IActiveIMMApp *This,
        HWND hWnd,
        HIMC *phIMC);

    HRESULT (STDMETHODCALLTYPE *GetConversionListA)(
        IActiveIMMApp *This,
        HKL hKL,
        HIMC hIMC,
        LPSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetConversionListW)(
        IActiveIMMApp *This,
        HKL hKL,
        HIMC hIMC,
        LPWSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetConversionStatus)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD *pfdwConversion,
        DWORD *pfdwSentence);

    HRESULT (STDMETHODCALLTYPE *GetDefaultIMEWnd)(
        IActiveIMMApp *This,
        HWND hWnd,
        HWND *phDefWnd);

    HRESULT (STDMETHODCALLTYPE *GetDescriptionA)(
        IActiveIMMApp *This,
        HKL hKL,
        UINT uBufLen,
        LPSTR szDescription,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetDescriptionW)(
        IActiveIMMApp *This,
        HKL hKL,
        UINT uBufLen,
        LPWSTR szDescription,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetGuideLineA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPSTR pBuf,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *GetGuideLineW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPWSTR pBuf,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *GetIMEFileNameA)(
        IActiveIMMApp *This,
        HKL hKL,
        UINT uBufLen,
        LPSTR szFileName,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetIMEFileNameW)(
        IActiveIMMApp *This,
        HKL hKL,
        UINT uBufLen,
        LPWSTR szFileName,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetOpenStatus)(
        IActiveIMMApp *This,
        HIMC hIMC);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IActiveIMMApp *This,
        HKL hKL,
        DWORD fdwIndex,
        DWORD *pdwProperty);

    HRESULT (STDMETHODCALLTYPE *GetRegisterWordStyleA)(
        IActiveIMMApp *This,
        HKL hKL,
        UINT nItem,
        STYLEBUFA *pStyleBuf,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetRegisterWordStyleW)(
        IActiveIMMApp *This,
        HKL hKL,
        UINT nItem,
        STYLEBUFW *pStyleBuf,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetStatusWindowPos)(
        IActiveIMMApp *This,
        HIMC hIMC,
        POINT *pptPos);

    HRESULT (STDMETHODCALLTYPE *GetVirtualKey)(
        IActiveIMMApp *This,
        HWND hWnd,
        UINT *puVirtualKey);

    HRESULT (STDMETHODCALLTYPE *InstallIMEA)(
        IActiveIMMApp *This,
        LPSTR szIMEFileName,
        LPSTR szLayoutText,
        HKL *phKL);

    HRESULT (STDMETHODCALLTYPE *InstallIMEW)(
        IActiveIMMApp *This,
        LPWSTR szIMEFileName,
        LPWSTR szLayoutText,
        HKL *phKL);

    HRESULT (STDMETHODCALLTYPE *IsIME)(
        IActiveIMMApp *This,
        HKL hKL);

    HRESULT (STDMETHODCALLTYPE *IsUIMessageA)(
        IActiveIMMApp *This,
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam);

    HRESULT (STDMETHODCALLTYPE *IsUIMessageW)(
        IActiveIMMApp *This,
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam);

    HRESULT (STDMETHODCALLTYPE *NotifyIME)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwAction,
        DWORD dwIndex,
        DWORD dwValue);

    HRESULT (STDMETHODCALLTYPE *RegisterWordA)(
        IActiveIMMApp *This,
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister);

    HRESULT (STDMETHODCALLTYPE *RegisterWordW)(
        IActiveIMMApp *This,
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister);

    HRESULT (STDMETHODCALLTYPE *ReleaseContext)(
        IActiveIMMApp *This,
        HWND hWnd,
        HIMC hIMC);

    HRESULT (STDMETHODCALLTYPE *SetCandidateWindow)(
        IActiveIMMApp *This,
        HIMC hIMC,
        CANDIDATEFORM *pCandidate);

    HRESULT (STDMETHODCALLTYPE *SetCompositionFontA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        LOGFONTA *plf);

    HRESULT (STDMETHODCALLTYPE *SetCompositionFontW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        LOGFONTW *plf);

    HRESULT (STDMETHODCALLTYPE *SetCompositionStringA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen);

    HRESULT (STDMETHODCALLTYPE *SetCompositionStringW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen);

    HRESULT (STDMETHODCALLTYPE *SetCompositionWindow)(
        IActiveIMMApp *This,
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm);

    HRESULT (STDMETHODCALLTYPE *SetConversionStatus)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD fdwConversion,
        DWORD fdwSentence);

    HRESULT (STDMETHODCALLTYPE *SetOpenStatus)(
        IActiveIMMApp *This,
        HIMC hIMC,
        WINBOOL fOpen);

    HRESULT (STDMETHODCALLTYPE *SetStatusWindowPos)(
        IActiveIMMApp *This,
        HIMC hIMC,
        POINT *pptPos);

    HRESULT (STDMETHODCALLTYPE *SimulateHotKey)(
        IActiveIMMApp *This,
        HWND hWnd,
        DWORD dwHotKeyID);

    HRESULT (STDMETHODCALLTYPE *UnregisterWordA)(
        IActiveIMMApp *This,
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szUnregister);

    HRESULT (STDMETHODCALLTYPE *UnregisterWordW)(
        IActiveIMMApp *This,
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szUnregister);

    HRESULT (STDMETHODCALLTYPE *Activate)(
        IActiveIMMApp *This,
        WINBOOL fRestoreLayout);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        IActiveIMMApp *This);

    HRESULT (STDMETHODCALLTYPE *OnDefWindowProc)(
        IActiveIMMApp *This,
        HWND hWnd,
        UINT Msg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *FilterClientWindows)(
        IActiveIMMApp *This,
        ATOM *aaClassList,
        UINT uSize);

    HRESULT (STDMETHODCALLTYPE *GetCodePageA)(
        IActiveIMMApp *This,
        HKL hKL,
        UINT *uCodePage);

    HRESULT (STDMETHODCALLTYPE *GetLangId)(
        IActiveIMMApp *This,
        HKL hKL,
        LANGID *plid);

    HRESULT (STDMETHODCALLTYPE *AssociateContextEx)(
        IActiveIMMApp *This,
        HWND hWnd,
        HIMC hIMC,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *DisableIME)(
        IActiveIMMApp *This,
        DWORD idThread);

    HRESULT (STDMETHODCALLTYPE *GetImeMenuItemsA)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOA *pImeParentMenu,
        IMEMENUITEMINFOA *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *GetImeMenuItemsW)(
        IActiveIMMApp *This,
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOW *pImeParentMenu,
        IMEMENUITEMINFOW *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *EnumInputContext)(
        IActiveIMMApp *This,
        DWORD idThread,
        IEnumInputContext **ppEnum);

    END_INTERFACE
} IActiveIMMAppVtbl;

interface IActiveIMMApp {
    CONST_VTBL IActiveIMMAppVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveIMMApp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveIMMApp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveIMMApp_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveIMMApp methods ***/
#define IActiveIMMApp_AssociateContext(This,hWnd,hIME,phPrev) (This)->lpVtbl->AssociateContext(This,hWnd,hIME,phPrev)
#define IActiveIMMApp_ConfigureIMEA(This,hKL,hWnd,dwMode,pData) (This)->lpVtbl->ConfigureIMEA(This,hKL,hWnd,dwMode,pData)
#define IActiveIMMApp_ConfigureIMEW(This,hKL,hWnd,dwMode,pData) (This)->lpVtbl->ConfigureIMEW(This,hKL,hWnd,dwMode,pData)
#define IActiveIMMApp_CreateContext(This,phIMC) (This)->lpVtbl->CreateContext(This,phIMC)
#define IActiveIMMApp_DestroyContext(This,hIME) (This)->lpVtbl->DestroyContext(This,hIME)
#define IActiveIMMApp_EnumRegisterWordA(This,hKL,szReading,dwStyle,szRegister,pData,pEnum) (This)->lpVtbl->EnumRegisterWordA(This,hKL,szReading,dwStyle,szRegister,pData,pEnum)
#define IActiveIMMApp_EnumRegisterWordW(This,hKL,szReading,dwStyle,szRegister,pData,pEnum) (This)->lpVtbl->EnumRegisterWordW(This,hKL,szReading,dwStyle,szRegister,pData,pEnum)
#define IActiveIMMApp_EscapeA(This,hKL,hIMC,uEscape,pData,plResult) (This)->lpVtbl->EscapeA(This,hKL,hIMC,uEscape,pData,plResult)
#define IActiveIMMApp_EscapeW(This,hKL,hIMC,uEscape,pData,plResult) (This)->lpVtbl->EscapeW(This,hKL,hIMC,uEscape,pData,plResult)
#define IActiveIMMApp_GetCandidateListA(This,hIMC,dwIndex,uBufLen,pCandList,puCopied) (This)->lpVtbl->GetCandidateListA(This,hIMC,dwIndex,uBufLen,pCandList,puCopied)
#define IActiveIMMApp_GetCandidateListW(This,hIMC,dwIndex,uBufLen,pCandList,puCopied) (This)->lpVtbl->GetCandidateListW(This,hIMC,dwIndex,uBufLen,pCandList,puCopied)
#define IActiveIMMApp_GetCandidateListCountA(This,hIMC,pdwListSize,pdwBufLen) (This)->lpVtbl->GetCandidateListCountA(This,hIMC,pdwListSize,pdwBufLen)
#define IActiveIMMApp_GetCandidateListCountW(This,hIMC,pdwListSize,pdwBufLen) (This)->lpVtbl->GetCandidateListCountW(This,hIMC,pdwListSize,pdwBufLen)
#define IActiveIMMApp_GetCandidateWindow(This,hIMC,dwIndex,pCandidate) (This)->lpVtbl->GetCandidateWindow(This,hIMC,dwIndex,pCandidate)
#define IActiveIMMApp_GetCompositionFontA(This,hIMC,plf) (This)->lpVtbl->GetCompositionFontA(This,hIMC,plf)
#define IActiveIMMApp_GetCompositionFontW(This,hIMC,plf) (This)->lpVtbl->GetCompositionFontW(This,hIMC,plf)
#define IActiveIMMApp_GetCompositionStringA(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf) (This)->lpVtbl->GetCompositionStringA(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf)
#define IActiveIMMApp_GetCompositionStringW(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf) (This)->lpVtbl->GetCompositionStringW(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf)
#define IActiveIMMApp_GetCompositionWindow(This,hIMC,pCompForm) (This)->lpVtbl->GetCompositionWindow(This,hIMC,pCompForm)
#define IActiveIMMApp_GetContext(This,hWnd,phIMC) (This)->lpVtbl->GetContext(This,hWnd,phIMC)
#define IActiveIMMApp_GetConversionListA(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied) (This)->lpVtbl->GetConversionListA(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied)
#define IActiveIMMApp_GetConversionListW(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied) (This)->lpVtbl->GetConversionListW(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied)
#define IActiveIMMApp_GetConversionStatus(This,hIMC,pfdwConversion,pfdwSentence) (This)->lpVtbl->GetConversionStatus(This,hIMC,pfdwConversion,pfdwSentence)
#define IActiveIMMApp_GetDefaultIMEWnd(This,hWnd,phDefWnd) (This)->lpVtbl->GetDefaultIMEWnd(This,hWnd,phDefWnd)
#define IActiveIMMApp_GetDescriptionA(This,hKL,uBufLen,szDescription,puCopied) (This)->lpVtbl->GetDescriptionA(This,hKL,uBufLen,szDescription,puCopied)
#define IActiveIMMApp_GetDescriptionW(This,hKL,uBufLen,szDescription,puCopied) (This)->lpVtbl->GetDescriptionW(This,hKL,uBufLen,szDescription,puCopied)
#define IActiveIMMApp_GetGuideLineA(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult) (This)->lpVtbl->GetGuideLineA(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult)
#define IActiveIMMApp_GetGuideLineW(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult) (This)->lpVtbl->GetGuideLineW(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult)
#define IActiveIMMApp_GetIMEFileNameA(This,hKL,uBufLen,szFileName,puCopied) (This)->lpVtbl->GetIMEFileNameA(This,hKL,uBufLen,szFileName,puCopied)
#define IActiveIMMApp_GetIMEFileNameW(This,hKL,uBufLen,szFileName,puCopied) (This)->lpVtbl->GetIMEFileNameW(This,hKL,uBufLen,szFileName,puCopied)
#define IActiveIMMApp_GetOpenStatus(This,hIMC) (This)->lpVtbl->GetOpenStatus(This,hIMC)
#define IActiveIMMApp_GetProperty(This,hKL,fdwIndex,pdwProperty) (This)->lpVtbl->GetProperty(This,hKL,fdwIndex,pdwProperty)
#define IActiveIMMApp_GetRegisterWordStyleA(This,hKL,nItem,pStyleBuf,puCopied) (This)->lpVtbl->GetRegisterWordStyleA(This,hKL,nItem,pStyleBuf,puCopied)
#define IActiveIMMApp_GetRegisterWordStyleW(This,hKL,nItem,pStyleBuf,puCopied) (This)->lpVtbl->GetRegisterWordStyleW(This,hKL,nItem,pStyleBuf,puCopied)
#define IActiveIMMApp_GetStatusWindowPos(This,hIMC,pptPos) (This)->lpVtbl->GetStatusWindowPos(This,hIMC,pptPos)
#define IActiveIMMApp_GetVirtualKey(This,hWnd,puVirtualKey) (This)->lpVtbl->GetVirtualKey(This,hWnd,puVirtualKey)
#define IActiveIMMApp_InstallIMEA(This,szIMEFileName,szLayoutText,phKL) (This)->lpVtbl->InstallIMEA(This,szIMEFileName,szLayoutText,phKL)
#define IActiveIMMApp_InstallIMEW(This,szIMEFileName,szLayoutText,phKL) (This)->lpVtbl->InstallIMEW(This,szIMEFileName,szLayoutText,phKL)
#define IActiveIMMApp_IsIME(This,hKL) (This)->lpVtbl->IsIME(This,hKL)
#define IActiveIMMApp_IsUIMessageA(This,hWndIME,msg,wParam,lParam) (This)->lpVtbl->IsUIMessageA(This,hWndIME,msg,wParam,lParam)
#define IActiveIMMApp_IsUIMessageW(This,hWndIME,msg,wParam,lParam) (This)->lpVtbl->IsUIMessageW(This,hWndIME,msg,wParam,lParam)
#define IActiveIMMApp_NotifyIME(This,hIMC,dwAction,dwIndex,dwValue) (This)->lpVtbl->NotifyIME(This,hIMC,dwAction,dwIndex,dwValue)
#define IActiveIMMApp_RegisterWordA(This,hKL,szReading,dwStyle,szRegister) (This)->lpVtbl->RegisterWordA(This,hKL,szReading,dwStyle,szRegister)
#define IActiveIMMApp_RegisterWordW(This,hKL,szReading,dwStyle,szRegister) (This)->lpVtbl->RegisterWordW(This,hKL,szReading,dwStyle,szRegister)
#define IActiveIMMApp_ReleaseContext(This,hWnd,hIMC) (This)->lpVtbl->ReleaseContext(This,hWnd,hIMC)
#define IActiveIMMApp_SetCandidateWindow(This,hIMC,pCandidate) (This)->lpVtbl->SetCandidateWindow(This,hIMC,pCandidate)
#define IActiveIMMApp_SetCompositionFontA(This,hIMC,plf) (This)->lpVtbl->SetCompositionFontA(This,hIMC,plf)
#define IActiveIMMApp_SetCompositionFontW(This,hIMC,plf) (This)->lpVtbl->SetCompositionFontW(This,hIMC,plf)
#define IActiveIMMApp_SetCompositionStringA(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen) (This)->lpVtbl->SetCompositionStringA(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen)
#define IActiveIMMApp_SetCompositionStringW(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen) (This)->lpVtbl->SetCompositionStringW(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen)
#define IActiveIMMApp_SetCompositionWindow(This,hIMC,pCompForm) (This)->lpVtbl->SetCompositionWindow(This,hIMC,pCompForm)
#define IActiveIMMApp_SetConversionStatus(This,hIMC,fdwConversion,fdwSentence) (This)->lpVtbl->SetConversionStatus(This,hIMC,fdwConversion,fdwSentence)
#define IActiveIMMApp_SetOpenStatus(This,hIMC,fOpen) (This)->lpVtbl->SetOpenStatus(This,hIMC,fOpen)
#define IActiveIMMApp_SetStatusWindowPos(This,hIMC,pptPos) (This)->lpVtbl->SetStatusWindowPos(This,hIMC,pptPos)
#define IActiveIMMApp_SimulateHotKey(This,hWnd,dwHotKeyID) (This)->lpVtbl->SimulateHotKey(This,hWnd,dwHotKeyID)
#define IActiveIMMApp_UnregisterWordA(This,hKL,szReading,dwStyle,szUnregister) (This)->lpVtbl->UnregisterWordA(This,hKL,szReading,dwStyle,szUnregister)
#define IActiveIMMApp_UnregisterWordW(This,hKL,szReading,dwStyle,szUnregister) (This)->lpVtbl->UnregisterWordW(This,hKL,szReading,dwStyle,szUnregister)
#define IActiveIMMApp_Activate(This,fRestoreLayout) (This)->lpVtbl->Activate(This,fRestoreLayout)
#define IActiveIMMApp_Deactivate(This) (This)->lpVtbl->Deactivate(This)
#define IActiveIMMApp_OnDefWindowProc(This,hWnd,Msg,wParam,lParam,plResult) (This)->lpVtbl->OnDefWindowProc(This,hWnd,Msg,wParam,lParam,plResult)
#define IActiveIMMApp_FilterClientWindows(This,aaClassList,uSize) (This)->lpVtbl->FilterClientWindows(This,aaClassList,uSize)
#define IActiveIMMApp_GetCodePageA(This,hKL,uCodePage) (This)->lpVtbl->GetCodePageA(This,hKL,uCodePage)
#define IActiveIMMApp_GetLangId(This,hKL,plid) (This)->lpVtbl->GetLangId(This,hKL,plid)
#define IActiveIMMApp_AssociateContextEx(This,hWnd,hIMC,dwFlags) (This)->lpVtbl->AssociateContextEx(This,hWnd,hIMC,dwFlags)
#define IActiveIMMApp_DisableIME(This,idThread) (This)->lpVtbl->DisableIME(This,idThread)
#define IActiveIMMApp_GetImeMenuItemsA(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult) (This)->lpVtbl->GetImeMenuItemsA(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult)
#define IActiveIMMApp_GetImeMenuItemsW(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult) (This)->lpVtbl->GetImeMenuItemsW(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult)
#define IActiveIMMApp_EnumInputContext(This,idThread,ppEnum) (This)->lpVtbl->EnumInputContext(This,idThread,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveIMMApp_QueryInterface(IActiveIMMApp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveIMMApp_AddRef(IActiveIMMApp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveIMMApp_Release(IActiveIMMApp* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveIMMApp methods ***/
static inline HRESULT IActiveIMMApp_AssociateContext(IActiveIMMApp* This,HWND hWnd,HIMC hIME,HIMC *phPrev) {
    return This->lpVtbl->AssociateContext(This,hWnd,hIME,phPrev);
}
static inline HRESULT IActiveIMMApp_ConfigureIMEA(IActiveIMMApp* This,HKL hKL,HWND hWnd,DWORD dwMode,REGISTERWORDA *pData) {
    return This->lpVtbl->ConfigureIMEA(This,hKL,hWnd,dwMode,pData);
}
static inline HRESULT IActiveIMMApp_ConfigureIMEW(IActiveIMMApp* This,HKL hKL,HWND hWnd,DWORD dwMode,REGISTERWORDW *pData) {
    return This->lpVtbl->ConfigureIMEW(This,hKL,hWnd,dwMode,pData);
}
static inline HRESULT IActiveIMMApp_CreateContext(IActiveIMMApp* This,HIMC *phIMC) {
    return This->lpVtbl->CreateContext(This,phIMC);
}
static inline HRESULT IActiveIMMApp_DestroyContext(IActiveIMMApp* This,HIMC hIME) {
    return This->lpVtbl->DestroyContext(This,hIME);
}
static inline HRESULT IActiveIMMApp_EnumRegisterWordA(IActiveIMMApp* This,HKL hKL,LPSTR szReading,DWORD dwStyle,LPSTR szRegister,LPVOID pData,IEnumRegisterWordA **pEnum) {
    return This->lpVtbl->EnumRegisterWordA(This,hKL,szReading,dwStyle,szRegister,pData,pEnum);
}
static inline HRESULT IActiveIMMApp_EnumRegisterWordW(IActiveIMMApp* This,HKL hKL,LPWSTR szReading,DWORD dwStyle,LPWSTR szRegister,LPVOID pData,IEnumRegisterWordW **pEnum) {
    return This->lpVtbl->EnumRegisterWordW(This,hKL,szReading,dwStyle,szRegister,pData,pEnum);
}
static inline HRESULT IActiveIMMApp_EscapeA(IActiveIMMApp* This,HKL hKL,HIMC hIMC,UINT uEscape,LPVOID pData,LRESULT *plResult) {
    return This->lpVtbl->EscapeA(This,hKL,hIMC,uEscape,pData,plResult);
}
static inline HRESULT IActiveIMMApp_EscapeW(IActiveIMMApp* This,HKL hKL,HIMC hIMC,UINT uEscape,LPVOID pData,LRESULT *plResult) {
    return This->lpVtbl->EscapeW(This,hKL,hIMC,uEscape,pData,plResult);
}
static inline HRESULT IActiveIMMApp_GetCandidateListA(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,UINT uBufLen,CANDIDATELIST *pCandList,UINT *puCopied) {
    return This->lpVtbl->GetCandidateListA(This,hIMC,dwIndex,uBufLen,pCandList,puCopied);
}
static inline HRESULT IActiveIMMApp_GetCandidateListW(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,UINT uBufLen,CANDIDATELIST *pCandList,UINT *puCopied) {
    return This->lpVtbl->GetCandidateListW(This,hIMC,dwIndex,uBufLen,pCandList,puCopied);
}
static inline HRESULT IActiveIMMApp_GetCandidateListCountA(IActiveIMMApp* This,HIMC hIMC,DWORD *pdwListSize,DWORD *pdwBufLen) {
    return This->lpVtbl->GetCandidateListCountA(This,hIMC,pdwListSize,pdwBufLen);
}
static inline HRESULT IActiveIMMApp_GetCandidateListCountW(IActiveIMMApp* This,HIMC hIMC,DWORD *pdwListSize,DWORD *pdwBufLen) {
    return This->lpVtbl->GetCandidateListCountW(This,hIMC,pdwListSize,pdwBufLen);
}
static inline HRESULT IActiveIMMApp_GetCandidateWindow(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,CANDIDATEFORM *pCandidate) {
    return This->lpVtbl->GetCandidateWindow(This,hIMC,dwIndex,pCandidate);
}
static inline HRESULT IActiveIMMApp_GetCompositionFontA(IActiveIMMApp* This,HIMC hIMC,LOGFONTA *plf) {
    return This->lpVtbl->GetCompositionFontA(This,hIMC,plf);
}
static inline HRESULT IActiveIMMApp_GetCompositionFontW(IActiveIMMApp* This,HIMC hIMC,LOGFONTW *plf) {
    return This->lpVtbl->GetCompositionFontW(This,hIMC,plf);
}
static inline HRESULT IActiveIMMApp_GetCompositionStringA(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LONG *plCopied,LPVOID pBuf) {
    return This->lpVtbl->GetCompositionStringA(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf);
}
static inline HRESULT IActiveIMMApp_GetCompositionStringW(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LONG *plCopied,LPVOID pBuf) {
    return This->lpVtbl->GetCompositionStringW(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf);
}
static inline HRESULT IActiveIMMApp_GetCompositionWindow(IActiveIMMApp* This,HIMC hIMC,COMPOSITIONFORM *pCompForm) {
    return This->lpVtbl->GetCompositionWindow(This,hIMC,pCompForm);
}
static inline HRESULT IActiveIMMApp_GetContext(IActiveIMMApp* This,HWND hWnd,HIMC *phIMC) {
    return This->lpVtbl->GetContext(This,hWnd,phIMC);
}
static inline HRESULT IActiveIMMApp_GetConversionListA(IActiveIMMApp* This,HKL hKL,HIMC hIMC,LPSTR pSrc,UINT uBufLen,UINT uFlag,CANDIDATELIST *pDst,UINT *puCopied) {
    return This->lpVtbl->GetConversionListA(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied);
}
static inline HRESULT IActiveIMMApp_GetConversionListW(IActiveIMMApp* This,HKL hKL,HIMC hIMC,LPWSTR pSrc,UINT uBufLen,UINT uFlag,CANDIDATELIST *pDst,UINT *puCopied) {
    return This->lpVtbl->GetConversionListW(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied);
}
static inline HRESULT IActiveIMMApp_GetConversionStatus(IActiveIMMApp* This,HIMC hIMC,DWORD *pfdwConversion,DWORD *pfdwSentence) {
    return This->lpVtbl->GetConversionStatus(This,hIMC,pfdwConversion,pfdwSentence);
}
static inline HRESULT IActiveIMMApp_GetDefaultIMEWnd(IActiveIMMApp* This,HWND hWnd,HWND *phDefWnd) {
    return This->lpVtbl->GetDefaultIMEWnd(This,hWnd,phDefWnd);
}
static inline HRESULT IActiveIMMApp_GetDescriptionA(IActiveIMMApp* This,HKL hKL,UINT uBufLen,LPSTR szDescription,UINT *puCopied) {
    return This->lpVtbl->GetDescriptionA(This,hKL,uBufLen,szDescription,puCopied);
}
static inline HRESULT IActiveIMMApp_GetDescriptionW(IActiveIMMApp* This,HKL hKL,UINT uBufLen,LPWSTR szDescription,UINT *puCopied) {
    return This->lpVtbl->GetDescriptionW(This,hKL,uBufLen,szDescription,puCopied);
}
static inline HRESULT IActiveIMMApp_GetGuideLineA(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LPSTR pBuf,DWORD *pdwResult) {
    return This->lpVtbl->GetGuideLineA(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult);
}
static inline HRESULT IActiveIMMApp_GetGuideLineW(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LPWSTR pBuf,DWORD *pdwResult) {
    return This->lpVtbl->GetGuideLineW(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult);
}
static inline HRESULT IActiveIMMApp_GetIMEFileNameA(IActiveIMMApp* This,HKL hKL,UINT uBufLen,LPSTR szFileName,UINT *puCopied) {
    return This->lpVtbl->GetIMEFileNameA(This,hKL,uBufLen,szFileName,puCopied);
}
static inline HRESULT IActiveIMMApp_GetIMEFileNameW(IActiveIMMApp* This,HKL hKL,UINT uBufLen,LPWSTR szFileName,UINT *puCopied) {
    return This->lpVtbl->GetIMEFileNameW(This,hKL,uBufLen,szFileName,puCopied);
}
static inline HRESULT IActiveIMMApp_GetOpenStatus(IActiveIMMApp* This,HIMC hIMC) {
    return This->lpVtbl->GetOpenStatus(This,hIMC);
}
static inline HRESULT IActiveIMMApp_GetProperty(IActiveIMMApp* This,HKL hKL,DWORD fdwIndex,DWORD *pdwProperty) {
    return This->lpVtbl->GetProperty(This,hKL,fdwIndex,pdwProperty);
}
static inline HRESULT IActiveIMMApp_GetRegisterWordStyleA(IActiveIMMApp* This,HKL hKL,UINT nItem,STYLEBUFA *pStyleBuf,UINT *puCopied) {
    return This->lpVtbl->GetRegisterWordStyleA(This,hKL,nItem,pStyleBuf,puCopied);
}
static inline HRESULT IActiveIMMApp_GetRegisterWordStyleW(IActiveIMMApp* This,HKL hKL,UINT nItem,STYLEBUFW *pStyleBuf,UINT *puCopied) {
    return This->lpVtbl->GetRegisterWordStyleW(This,hKL,nItem,pStyleBuf,puCopied);
}
static inline HRESULT IActiveIMMApp_GetStatusWindowPos(IActiveIMMApp* This,HIMC hIMC,POINT *pptPos) {
    return This->lpVtbl->GetStatusWindowPos(This,hIMC,pptPos);
}
static inline HRESULT IActiveIMMApp_GetVirtualKey(IActiveIMMApp* This,HWND hWnd,UINT *puVirtualKey) {
    return This->lpVtbl->GetVirtualKey(This,hWnd,puVirtualKey);
}
static inline HRESULT IActiveIMMApp_InstallIMEA(IActiveIMMApp* This,LPSTR szIMEFileName,LPSTR szLayoutText,HKL *phKL) {
    return This->lpVtbl->InstallIMEA(This,szIMEFileName,szLayoutText,phKL);
}
static inline HRESULT IActiveIMMApp_InstallIMEW(IActiveIMMApp* This,LPWSTR szIMEFileName,LPWSTR szLayoutText,HKL *phKL) {
    return This->lpVtbl->InstallIMEW(This,szIMEFileName,szLayoutText,phKL);
}
static inline HRESULT IActiveIMMApp_IsIME(IActiveIMMApp* This,HKL hKL) {
    return This->lpVtbl->IsIME(This,hKL);
}
static inline HRESULT IActiveIMMApp_IsUIMessageA(IActiveIMMApp* This,HWND hWndIME,UINT msg,WPARAM wParam,LPARAM lParam) {
    return This->lpVtbl->IsUIMessageA(This,hWndIME,msg,wParam,lParam);
}
static inline HRESULT IActiveIMMApp_IsUIMessageW(IActiveIMMApp* This,HWND hWndIME,UINT msg,WPARAM wParam,LPARAM lParam) {
    return This->lpVtbl->IsUIMessageW(This,hWndIME,msg,wParam,lParam);
}
static inline HRESULT IActiveIMMApp_NotifyIME(IActiveIMMApp* This,HIMC hIMC,DWORD dwAction,DWORD dwIndex,DWORD dwValue) {
    return This->lpVtbl->NotifyIME(This,hIMC,dwAction,dwIndex,dwValue);
}
static inline HRESULT IActiveIMMApp_RegisterWordA(IActiveIMMApp* This,HKL hKL,LPSTR szReading,DWORD dwStyle,LPSTR szRegister) {
    return This->lpVtbl->RegisterWordA(This,hKL,szReading,dwStyle,szRegister);
}
static inline HRESULT IActiveIMMApp_RegisterWordW(IActiveIMMApp* This,HKL hKL,LPWSTR szReading,DWORD dwStyle,LPWSTR szRegister) {
    return This->lpVtbl->RegisterWordW(This,hKL,szReading,dwStyle,szRegister);
}
static inline HRESULT IActiveIMMApp_ReleaseContext(IActiveIMMApp* This,HWND hWnd,HIMC hIMC) {
    return This->lpVtbl->ReleaseContext(This,hWnd,hIMC);
}
static inline HRESULT IActiveIMMApp_SetCandidateWindow(IActiveIMMApp* This,HIMC hIMC,CANDIDATEFORM *pCandidate) {
    return This->lpVtbl->SetCandidateWindow(This,hIMC,pCandidate);
}
static inline HRESULT IActiveIMMApp_SetCompositionFontA(IActiveIMMApp* This,HIMC hIMC,LOGFONTA *plf) {
    return This->lpVtbl->SetCompositionFontA(This,hIMC,plf);
}
static inline HRESULT IActiveIMMApp_SetCompositionFontW(IActiveIMMApp* This,HIMC hIMC,LOGFONTW *plf) {
    return This->lpVtbl->SetCompositionFontW(This,hIMC,plf);
}
static inline HRESULT IActiveIMMApp_SetCompositionStringA(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,LPVOID pComp,DWORD dwCompLen,LPVOID pRead,DWORD dwReadLen) {
    return This->lpVtbl->SetCompositionStringA(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen);
}
static inline HRESULT IActiveIMMApp_SetCompositionStringW(IActiveIMMApp* This,HIMC hIMC,DWORD dwIndex,LPVOID pComp,DWORD dwCompLen,LPVOID pRead,DWORD dwReadLen) {
    return This->lpVtbl->SetCompositionStringW(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen);
}
static inline HRESULT IActiveIMMApp_SetCompositionWindow(IActiveIMMApp* This,HIMC hIMC,COMPOSITIONFORM *pCompForm) {
    return This->lpVtbl->SetCompositionWindow(This,hIMC,pCompForm);
}
static inline HRESULT IActiveIMMApp_SetConversionStatus(IActiveIMMApp* This,HIMC hIMC,DWORD fdwConversion,DWORD fdwSentence) {
    return This->lpVtbl->SetConversionStatus(This,hIMC,fdwConversion,fdwSentence);
}
static inline HRESULT IActiveIMMApp_SetOpenStatus(IActiveIMMApp* This,HIMC hIMC,WINBOOL fOpen) {
    return This->lpVtbl->SetOpenStatus(This,hIMC,fOpen);
}
static inline HRESULT IActiveIMMApp_SetStatusWindowPos(IActiveIMMApp* This,HIMC hIMC,POINT *pptPos) {
    return This->lpVtbl->SetStatusWindowPos(This,hIMC,pptPos);
}
static inline HRESULT IActiveIMMApp_SimulateHotKey(IActiveIMMApp* This,HWND hWnd,DWORD dwHotKeyID) {
    return This->lpVtbl->SimulateHotKey(This,hWnd,dwHotKeyID);
}
static inline HRESULT IActiveIMMApp_UnregisterWordA(IActiveIMMApp* This,HKL hKL,LPSTR szReading,DWORD dwStyle,LPSTR szUnregister) {
    return This->lpVtbl->UnregisterWordA(This,hKL,szReading,dwStyle,szUnregister);
}
static inline HRESULT IActiveIMMApp_UnregisterWordW(IActiveIMMApp* This,HKL hKL,LPWSTR szReading,DWORD dwStyle,LPWSTR szUnregister) {
    return This->lpVtbl->UnregisterWordW(This,hKL,szReading,dwStyle,szUnregister);
}
static inline HRESULT IActiveIMMApp_Activate(IActiveIMMApp* This,WINBOOL fRestoreLayout) {
    return This->lpVtbl->Activate(This,fRestoreLayout);
}
static inline HRESULT IActiveIMMApp_Deactivate(IActiveIMMApp* This) {
    return This->lpVtbl->Deactivate(This);
}
static inline HRESULT IActiveIMMApp_OnDefWindowProc(IActiveIMMApp* This,HWND hWnd,UINT Msg,WPARAM wParam,LPARAM lParam,LRESULT *plResult) {
    return This->lpVtbl->OnDefWindowProc(This,hWnd,Msg,wParam,lParam,plResult);
}
static inline HRESULT IActiveIMMApp_FilterClientWindows(IActiveIMMApp* This,ATOM *aaClassList,UINT uSize) {
    return This->lpVtbl->FilterClientWindows(This,aaClassList,uSize);
}
static inline HRESULT IActiveIMMApp_GetCodePageA(IActiveIMMApp* This,HKL hKL,UINT *uCodePage) {
    return This->lpVtbl->GetCodePageA(This,hKL,uCodePage);
}
static inline HRESULT IActiveIMMApp_GetLangId(IActiveIMMApp* This,HKL hKL,LANGID *plid) {
    return This->lpVtbl->GetLangId(This,hKL,plid);
}
static inline HRESULT IActiveIMMApp_AssociateContextEx(IActiveIMMApp* This,HWND hWnd,HIMC hIMC,DWORD dwFlags) {
    return This->lpVtbl->AssociateContextEx(This,hWnd,hIMC,dwFlags);
}
static inline HRESULT IActiveIMMApp_DisableIME(IActiveIMMApp* This,DWORD idThread) {
    return This->lpVtbl->DisableIME(This,idThread);
}
static inline HRESULT IActiveIMMApp_GetImeMenuItemsA(IActiveIMMApp* This,HIMC hIMC,DWORD dwFlags,DWORD dwType,IMEMENUITEMINFOA *pImeParentMenu,IMEMENUITEMINFOA *pImeMenu,DWORD dwSize,DWORD *pdwResult) {
    return This->lpVtbl->GetImeMenuItemsA(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult);
}
static inline HRESULT IActiveIMMApp_GetImeMenuItemsW(IActiveIMMApp* This,HIMC hIMC,DWORD dwFlags,DWORD dwType,IMEMENUITEMINFOW *pImeParentMenu,IMEMENUITEMINFOW *pImeMenu,DWORD dwSize,DWORD *pdwResult) {
    return This->lpVtbl->GetImeMenuItemsW(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult);
}
static inline HRESULT IActiveIMMApp_EnumInputContext(IActiveIMMApp* This,DWORD idThread,IEnumInputContext **ppEnum) {
    return This->lpVtbl->EnumInputContext(This,idThread,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IActiveIMMApp_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveIMMIME interface
 */
#ifndef __IActiveIMMIME_INTERFACE_DEFINED__
#define __IActiveIMMIME_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveIMMIME, 0x08c03411, 0xf96b, 0x11d0, 0xa4,0x75, 0x00,0xaa,0x00,0x6b,0xcc,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("08c03411-f96b-11d0-a475-00aa006bcc59")
IActiveIMMIME : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AssociateContext(
        HWND hWnd,
        HIMC hIME,
        HIMC *phPrev) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConfigureIMEA(
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDA *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConfigureIMEW(
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDW *pData) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateContext(
        HIMC *phIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE DestroyContext(
        HIMC hIME) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRegisterWordA(
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordA **pEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRegisterWordW(
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordW **pEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE EscapeA(
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EscapeW(
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListA(
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListW(
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListCountA(
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateListCountW(
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCandidateWindow(
        HIMC hIMC,
        DWORD dwIndex,
        CANDIDATEFORM *pCandidate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionFontA(
        HIMC hIMC,
        LOGFONTA *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionFontW(
        HIMC hIMC,
        LOGFONTW *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionStringA(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionStringW(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompositionWindow(
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContext(
        HWND hWnd,
        HIMC *phIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConversionListA(
        HKL hKL,
        HIMC hIMC,
        LPSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConversionListW(
        HKL hKL,
        HIMC hIMC,
        LPWSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConversionStatus(
        HIMC hIMC,
        DWORD *pfdwConversion,
        DWORD *pfdwSentence) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultIMEWnd(
        HWND hWnd,
        HWND *phDefWnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescriptionA(
        HKL hKL,
        UINT uBufLen,
        LPSTR szDescription,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescriptionW(
        HKL hKL,
        UINT uBufLen,
        LPWSTR szDescription,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuideLineA(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPSTR pBuf,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuideLineW(
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPWSTR pBuf,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIMEFileNameA(
        HKL hKL,
        UINT uBufLen,
        LPSTR szFileName,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIMEFileNameW(
        HKL hKL,
        UINT uBufLen,
        LPWSTR szFileName,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOpenStatus(
        HIMC hIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        HKL hKL,
        DWORD fdwIndex,
        DWORD *pdwProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRegisterWordStyleA(
        HKL hKL,
        UINT nItem,
        STYLEBUFA *pStyleBuf,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRegisterWordStyleW(
        HKL hKL,
        UINT nItem,
        STYLEBUFW *pStyleBuf,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatusWindowPos(
        HIMC hIMC,
        POINT *pptPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVirtualKey(
        HWND hWnd,
        UINT *puVirtualKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallIMEA(
        LPSTR szIMEFileName,
        LPSTR szLayoutText,
        HKL *phKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallIMEW(
        LPWSTR szIMEFileName,
        LPWSTR szLayoutText,
        HKL *phKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsIME(
        HKL hKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsUIMessageA(
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsUIMessageW(
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyIME(
        HIMC hIMC,
        DWORD dwAction,
        DWORD dwIndex,
        DWORD dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterWordA(
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterWordW(
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseContext(
        HWND hWnd,
        HIMC hIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCandidateWindow(
        HIMC hIMC,
        CANDIDATEFORM *pCandidate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionFontA(
        HIMC hIMC,
        LOGFONTA *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionFontW(
        HIMC hIMC,
        LOGFONTW *plf) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionStringA(
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionStringW(
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionWindow(
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConversionStatus(
        HIMC hIMC,
        DWORD fdwConversion,
        DWORD fdwSentence) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpenStatus(
        HIMC hIMC,
        WINBOOL fOpen) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStatusWindowPos(
        HIMC hIMC,
        POINT *pptPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE SimulateHotKey(
        HWND hWnd,
        DWORD dwHotKeyID) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterWordA(
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szUnregister) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterWordW(
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szUnregister) = 0;

    virtual HRESULT STDMETHODCALLTYPE GenerateMessage(
        HIMC hIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockIMC(
        HIMC hIMC,
        INPUTCONTEXT **ppIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockIMC(
        HIMC hIMC) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIMCLockCount(
        HIMC hIMC,
        DWORD *pdwLockCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateIMCC(
        DWORD dwSize,
        HIMCC *phIMCC) = 0;

    virtual HRESULT STDMETHODCALLTYPE DestroyIMCC(
        HIMCC hIMCC) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockIMCC(
        HIMCC hIMCC,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockIMCC(
        HIMCC hIMCC) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReSizeIMCC(
        HIMCC hIMCC,
        DWORD dwSize,
        HIMCC *phIMCC) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIMCCSize(
        HIMCC hIMCC,
        DWORD *pdwSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIMCCLockCount(
        HIMCC hIMCC,
        DWORD *pdwLockCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHotKey(
        DWORD dwHotKeyID,
        UINT *puModifiers,
        UINT *puVKey,
        HKL *phKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHotKey(
        DWORD dwHotKeyID,
        UINT uModifiers,
        UINT uVKey,
        HKL hKL) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSoftKeyboard(
        UINT uType,
        HWND hOwner,
        int x,
        int y,
        HWND *phSoftKbdWnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE DestroySoftKeyboard(
        HWND hSoftKbdWnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowSoftKeyboard(
        HWND hSoftKbdWnd,
        int nCmdShow) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodePageA(
        HKL hKL,
        UINT *uCodePage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLangId(
        HKL hKL,
        LANGID *plid) = 0;

    virtual HRESULT STDMETHODCALLTYPE KeybdEvent(
        LANGID lgidIME,
        BYTE bVk,
        BYTE bScan,
        DWORD dwFlags,
        DWORD dwExtraInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockModal(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockModal(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AssociateContextEx(
        HWND hWnd,
        HIMC hIMC,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableIME(
        DWORD idThread) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImeMenuItemsA(
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOA *pImeParentMenu,
        IMEMENUITEMINFOA *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImeMenuItemsW(
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOW *pImeParentMenu,
        IMEMENUITEMINFOW *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumInputContext(
        DWORD idThread,
        IEnumInputContext **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestMessageA(
        HIMC hIMC,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestMessageW(
        HIMC hIMC,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendIMCA(
        HWND hWnd,
        UINT uMsg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendIMCW(
        HWND hWnd,
        UINT uMsg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSleeping(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveIMMIME, 0x08c03411, 0xf96b, 0x11d0, 0xa4,0x75, 0x00,0xaa,0x00,0x6b,0xcc,0x59)
#endif
#else
typedef struct IActiveIMMIMEVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveIMMIME *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveIMMIME *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveIMMIME *This);

    /*** IActiveIMMIME methods ***/
    HRESULT (STDMETHODCALLTYPE *AssociateContext)(
        IActiveIMMIME *This,
        HWND hWnd,
        HIMC hIME,
        HIMC *phPrev);

    HRESULT (STDMETHODCALLTYPE *ConfigureIMEA)(
        IActiveIMMIME *This,
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDA *pData);

    HRESULT (STDMETHODCALLTYPE *ConfigureIMEW)(
        IActiveIMMIME *This,
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDW *pData);

    HRESULT (STDMETHODCALLTYPE *CreateContext)(
        IActiveIMMIME *This,
        HIMC *phIMC);

    HRESULT (STDMETHODCALLTYPE *DestroyContext)(
        IActiveIMMIME *This,
        HIMC hIME);

    HRESULT (STDMETHODCALLTYPE *EnumRegisterWordA)(
        IActiveIMMIME *This,
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordA **pEnum);

    HRESULT (STDMETHODCALLTYPE *EnumRegisterWordW)(
        IActiveIMMIME *This,
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordW **pEnum);

    HRESULT (STDMETHODCALLTYPE *EscapeA)(
        IActiveIMMIME *This,
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *EscapeW)(
        IActiveIMMIME *This,
        HKL hKL,
        HIMC hIMC,
        UINT uEscape,
        LPVOID pData,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        UINT uBufLen,
        CANDIDATELIST *pCandList,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListCountA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen);

    HRESULT (STDMETHODCALLTYPE *GetCandidateListCountW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD *pdwListSize,
        DWORD *pdwBufLen);

    HRESULT (STDMETHODCALLTYPE *GetCandidateWindow)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        CANDIDATEFORM *pCandidate);

    HRESULT (STDMETHODCALLTYPE *GetCompositionFontA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        LOGFONTA *plf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionFontW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        LOGFONTW *plf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionStringA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionStringW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LONG *plCopied,
        LPVOID pBuf);

    HRESULT (STDMETHODCALLTYPE *GetCompositionWindow)(
        IActiveIMMIME *This,
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm);

    HRESULT (STDMETHODCALLTYPE *GetContext)(
        IActiveIMMIME *This,
        HWND hWnd,
        HIMC *phIMC);

    HRESULT (STDMETHODCALLTYPE *GetConversionListA)(
        IActiveIMMIME *This,
        HKL hKL,
        HIMC hIMC,
        LPSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetConversionListW)(
        IActiveIMMIME *This,
        HKL hKL,
        HIMC hIMC,
        LPWSTR pSrc,
        UINT uBufLen,
        UINT uFlag,
        CANDIDATELIST *pDst,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetConversionStatus)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD *pfdwConversion,
        DWORD *pfdwSentence);

    HRESULT (STDMETHODCALLTYPE *GetDefaultIMEWnd)(
        IActiveIMMIME *This,
        HWND hWnd,
        HWND *phDefWnd);

    HRESULT (STDMETHODCALLTYPE *GetDescriptionA)(
        IActiveIMMIME *This,
        HKL hKL,
        UINT uBufLen,
        LPSTR szDescription,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetDescriptionW)(
        IActiveIMMIME *This,
        HKL hKL,
        UINT uBufLen,
        LPWSTR szDescription,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetGuideLineA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPSTR pBuf,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *GetGuideLineW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        DWORD dwBufLen,
        LPWSTR pBuf,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *GetIMEFileNameA)(
        IActiveIMMIME *This,
        HKL hKL,
        UINT uBufLen,
        LPSTR szFileName,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetIMEFileNameW)(
        IActiveIMMIME *This,
        HKL hKL,
        UINT uBufLen,
        LPWSTR szFileName,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetOpenStatus)(
        IActiveIMMIME *This,
        HIMC hIMC);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IActiveIMMIME *This,
        HKL hKL,
        DWORD fdwIndex,
        DWORD *pdwProperty);

    HRESULT (STDMETHODCALLTYPE *GetRegisterWordStyleA)(
        IActiveIMMIME *This,
        HKL hKL,
        UINT nItem,
        STYLEBUFA *pStyleBuf,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetRegisterWordStyleW)(
        IActiveIMMIME *This,
        HKL hKL,
        UINT nItem,
        STYLEBUFW *pStyleBuf,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *GetStatusWindowPos)(
        IActiveIMMIME *This,
        HIMC hIMC,
        POINT *pptPos);

    HRESULT (STDMETHODCALLTYPE *GetVirtualKey)(
        IActiveIMMIME *This,
        HWND hWnd,
        UINT *puVirtualKey);

    HRESULT (STDMETHODCALLTYPE *InstallIMEA)(
        IActiveIMMIME *This,
        LPSTR szIMEFileName,
        LPSTR szLayoutText,
        HKL *phKL);

    HRESULT (STDMETHODCALLTYPE *InstallIMEW)(
        IActiveIMMIME *This,
        LPWSTR szIMEFileName,
        LPWSTR szLayoutText,
        HKL *phKL);

    HRESULT (STDMETHODCALLTYPE *IsIME)(
        IActiveIMMIME *This,
        HKL hKL);

    HRESULT (STDMETHODCALLTYPE *IsUIMessageA)(
        IActiveIMMIME *This,
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam);

    HRESULT (STDMETHODCALLTYPE *IsUIMessageW)(
        IActiveIMMIME *This,
        HWND hWndIME,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam);

    HRESULT (STDMETHODCALLTYPE *NotifyIME)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwAction,
        DWORD dwIndex,
        DWORD dwValue);

    HRESULT (STDMETHODCALLTYPE *RegisterWordA)(
        IActiveIMMIME *This,
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szRegister);

    HRESULT (STDMETHODCALLTYPE *RegisterWordW)(
        IActiveIMMIME *This,
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister);

    HRESULT (STDMETHODCALLTYPE *ReleaseContext)(
        IActiveIMMIME *This,
        HWND hWnd,
        HIMC hIMC);

    HRESULT (STDMETHODCALLTYPE *SetCandidateWindow)(
        IActiveIMMIME *This,
        HIMC hIMC,
        CANDIDATEFORM *pCandidate);

    HRESULT (STDMETHODCALLTYPE *SetCompositionFontA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        LOGFONTA *plf);

    HRESULT (STDMETHODCALLTYPE *SetCompositionFontW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        LOGFONTW *plf);

    HRESULT (STDMETHODCALLTYPE *SetCompositionStringA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen);

    HRESULT (STDMETHODCALLTYPE *SetCompositionStringW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        LPVOID pComp,
        DWORD dwCompLen,
        LPVOID pRead,
        DWORD dwReadLen);

    HRESULT (STDMETHODCALLTYPE *SetCompositionWindow)(
        IActiveIMMIME *This,
        HIMC hIMC,
        COMPOSITIONFORM *pCompForm);

    HRESULT (STDMETHODCALLTYPE *SetConversionStatus)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD fdwConversion,
        DWORD fdwSentence);

    HRESULT (STDMETHODCALLTYPE *SetOpenStatus)(
        IActiveIMMIME *This,
        HIMC hIMC,
        WINBOOL fOpen);

    HRESULT (STDMETHODCALLTYPE *SetStatusWindowPos)(
        IActiveIMMIME *This,
        HIMC hIMC,
        POINT *pptPos);

    HRESULT (STDMETHODCALLTYPE *SimulateHotKey)(
        IActiveIMMIME *This,
        HWND hWnd,
        DWORD dwHotKeyID);

    HRESULT (STDMETHODCALLTYPE *UnregisterWordA)(
        IActiveIMMIME *This,
        HKL hKL,
        LPSTR szReading,
        DWORD dwStyle,
        LPSTR szUnregister);

    HRESULT (STDMETHODCALLTYPE *UnregisterWordW)(
        IActiveIMMIME *This,
        HKL hKL,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szUnregister);

    HRESULT (STDMETHODCALLTYPE *GenerateMessage)(
        IActiveIMMIME *This,
        HIMC hIMC);

    HRESULT (STDMETHODCALLTYPE *LockIMC)(
        IActiveIMMIME *This,
        HIMC hIMC,
        INPUTCONTEXT **ppIMC);

    HRESULT (STDMETHODCALLTYPE *UnlockIMC)(
        IActiveIMMIME *This,
        HIMC hIMC);

    HRESULT (STDMETHODCALLTYPE *GetIMCLockCount)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD *pdwLockCount);

    HRESULT (STDMETHODCALLTYPE *CreateIMCC)(
        IActiveIMMIME *This,
        DWORD dwSize,
        HIMCC *phIMCC);

    HRESULT (STDMETHODCALLTYPE *DestroyIMCC)(
        IActiveIMMIME *This,
        HIMCC hIMCC);

    HRESULT (STDMETHODCALLTYPE *LockIMCC)(
        IActiveIMMIME *This,
        HIMCC hIMCC,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *UnlockIMCC)(
        IActiveIMMIME *This,
        HIMCC hIMCC);

    HRESULT (STDMETHODCALLTYPE *ReSizeIMCC)(
        IActiveIMMIME *This,
        HIMCC hIMCC,
        DWORD dwSize,
        HIMCC *phIMCC);

    HRESULT (STDMETHODCALLTYPE *GetIMCCSize)(
        IActiveIMMIME *This,
        HIMCC hIMCC,
        DWORD *pdwSize);

    HRESULT (STDMETHODCALLTYPE *GetIMCCLockCount)(
        IActiveIMMIME *This,
        HIMCC hIMCC,
        DWORD *pdwLockCount);

    HRESULT (STDMETHODCALLTYPE *GetHotKey)(
        IActiveIMMIME *This,
        DWORD dwHotKeyID,
        UINT *puModifiers,
        UINT *puVKey,
        HKL *phKL);

    HRESULT (STDMETHODCALLTYPE *SetHotKey)(
        IActiveIMMIME *This,
        DWORD dwHotKeyID,
        UINT uModifiers,
        UINT uVKey,
        HKL hKL);

    HRESULT (STDMETHODCALLTYPE *CreateSoftKeyboard)(
        IActiveIMMIME *This,
        UINT uType,
        HWND hOwner,
        int x,
        int y,
        HWND *phSoftKbdWnd);

    HRESULT (STDMETHODCALLTYPE *DestroySoftKeyboard)(
        IActiveIMMIME *This,
        HWND hSoftKbdWnd);

    HRESULT (STDMETHODCALLTYPE *ShowSoftKeyboard)(
        IActiveIMMIME *This,
        HWND hSoftKbdWnd,
        int nCmdShow);

    HRESULT (STDMETHODCALLTYPE *GetCodePageA)(
        IActiveIMMIME *This,
        HKL hKL,
        UINT *uCodePage);

    HRESULT (STDMETHODCALLTYPE *GetLangId)(
        IActiveIMMIME *This,
        HKL hKL,
        LANGID *plid);

    HRESULT (STDMETHODCALLTYPE *KeybdEvent)(
        IActiveIMMIME *This,
        LANGID lgidIME,
        BYTE bVk,
        BYTE bScan,
        DWORD dwFlags,
        DWORD dwExtraInfo);

    HRESULT (STDMETHODCALLTYPE *LockModal)(
        IActiveIMMIME *This);

    HRESULT (STDMETHODCALLTYPE *UnlockModal)(
        IActiveIMMIME *This);

    HRESULT (STDMETHODCALLTYPE *AssociateContextEx)(
        IActiveIMMIME *This,
        HWND hWnd,
        HIMC hIMC,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *DisableIME)(
        IActiveIMMIME *This,
        DWORD idThread);

    HRESULT (STDMETHODCALLTYPE *GetImeMenuItemsA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOA *pImeParentMenu,
        IMEMENUITEMINFOA *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *GetImeMenuItemsW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        DWORD dwFlags,
        DWORD dwType,
        IMEMENUITEMINFOW *pImeParentMenu,
        IMEMENUITEMINFOW *pImeMenu,
        DWORD dwSize,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *EnumInputContext)(
        IActiveIMMIME *This,
        DWORD idThread,
        IEnumInputContext **ppEnum);

    HRESULT (STDMETHODCALLTYPE *RequestMessageA)(
        IActiveIMMIME *This,
        HIMC hIMC,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *RequestMessageW)(
        IActiveIMMIME *This,
        HIMC hIMC,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *SendIMCA)(
        IActiveIMMIME *This,
        HWND hWnd,
        UINT uMsg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *SendIMCW)(
        IActiveIMMIME *This,
        HWND hWnd,
        UINT uMsg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *IsSleeping)(
        IActiveIMMIME *This);

    END_INTERFACE
} IActiveIMMIMEVtbl;

interface IActiveIMMIME {
    CONST_VTBL IActiveIMMIMEVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveIMMIME_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveIMMIME_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveIMMIME_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveIMMIME methods ***/
#define IActiveIMMIME_AssociateContext(This,hWnd,hIME,phPrev) (This)->lpVtbl->AssociateContext(This,hWnd,hIME,phPrev)
#define IActiveIMMIME_ConfigureIMEA(This,hKL,hWnd,dwMode,pData) (This)->lpVtbl->ConfigureIMEA(This,hKL,hWnd,dwMode,pData)
#define IActiveIMMIME_ConfigureIMEW(This,hKL,hWnd,dwMode,pData) (This)->lpVtbl->ConfigureIMEW(This,hKL,hWnd,dwMode,pData)
#define IActiveIMMIME_CreateContext(This,phIMC) (This)->lpVtbl->CreateContext(This,phIMC)
#define IActiveIMMIME_DestroyContext(This,hIME) (This)->lpVtbl->DestroyContext(This,hIME)
#define IActiveIMMIME_EnumRegisterWordA(This,hKL,szReading,dwStyle,szRegister,pData,pEnum) (This)->lpVtbl->EnumRegisterWordA(This,hKL,szReading,dwStyle,szRegister,pData,pEnum)
#define IActiveIMMIME_EnumRegisterWordW(This,hKL,szReading,dwStyle,szRegister,pData,pEnum) (This)->lpVtbl->EnumRegisterWordW(This,hKL,szReading,dwStyle,szRegister,pData,pEnum)
#define IActiveIMMIME_EscapeA(This,hKL,hIMC,uEscape,pData,plResult) (This)->lpVtbl->EscapeA(This,hKL,hIMC,uEscape,pData,plResult)
#define IActiveIMMIME_EscapeW(This,hKL,hIMC,uEscape,pData,plResult) (This)->lpVtbl->EscapeW(This,hKL,hIMC,uEscape,pData,plResult)
#define IActiveIMMIME_GetCandidateListA(This,hIMC,dwIndex,uBufLen,pCandList,puCopied) (This)->lpVtbl->GetCandidateListA(This,hIMC,dwIndex,uBufLen,pCandList,puCopied)
#define IActiveIMMIME_GetCandidateListW(This,hIMC,dwIndex,uBufLen,pCandList,puCopied) (This)->lpVtbl->GetCandidateListW(This,hIMC,dwIndex,uBufLen,pCandList,puCopied)
#define IActiveIMMIME_GetCandidateListCountA(This,hIMC,pdwListSize,pdwBufLen) (This)->lpVtbl->GetCandidateListCountA(This,hIMC,pdwListSize,pdwBufLen)
#define IActiveIMMIME_GetCandidateListCountW(This,hIMC,pdwListSize,pdwBufLen) (This)->lpVtbl->GetCandidateListCountW(This,hIMC,pdwListSize,pdwBufLen)
#define IActiveIMMIME_GetCandidateWindow(This,hIMC,dwIndex,pCandidate) (This)->lpVtbl->GetCandidateWindow(This,hIMC,dwIndex,pCandidate)
#define IActiveIMMIME_GetCompositionFontA(This,hIMC,plf) (This)->lpVtbl->GetCompositionFontA(This,hIMC,plf)
#define IActiveIMMIME_GetCompositionFontW(This,hIMC,plf) (This)->lpVtbl->GetCompositionFontW(This,hIMC,plf)
#define IActiveIMMIME_GetCompositionStringA(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf) (This)->lpVtbl->GetCompositionStringA(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf)
#define IActiveIMMIME_GetCompositionStringW(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf) (This)->lpVtbl->GetCompositionStringW(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf)
#define IActiveIMMIME_GetCompositionWindow(This,hIMC,pCompForm) (This)->lpVtbl->GetCompositionWindow(This,hIMC,pCompForm)
#define IActiveIMMIME_GetContext(This,hWnd,phIMC) (This)->lpVtbl->GetContext(This,hWnd,phIMC)
#define IActiveIMMIME_GetConversionListA(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied) (This)->lpVtbl->GetConversionListA(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied)
#define IActiveIMMIME_GetConversionListW(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied) (This)->lpVtbl->GetConversionListW(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied)
#define IActiveIMMIME_GetConversionStatus(This,hIMC,pfdwConversion,pfdwSentence) (This)->lpVtbl->GetConversionStatus(This,hIMC,pfdwConversion,pfdwSentence)
#define IActiveIMMIME_GetDefaultIMEWnd(This,hWnd,phDefWnd) (This)->lpVtbl->GetDefaultIMEWnd(This,hWnd,phDefWnd)
#define IActiveIMMIME_GetDescriptionA(This,hKL,uBufLen,szDescription,puCopied) (This)->lpVtbl->GetDescriptionA(This,hKL,uBufLen,szDescription,puCopied)
#define IActiveIMMIME_GetDescriptionW(This,hKL,uBufLen,szDescription,puCopied) (This)->lpVtbl->GetDescriptionW(This,hKL,uBufLen,szDescription,puCopied)
#define IActiveIMMIME_GetGuideLineA(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult) (This)->lpVtbl->GetGuideLineA(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult)
#define IActiveIMMIME_GetGuideLineW(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult) (This)->lpVtbl->GetGuideLineW(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult)
#define IActiveIMMIME_GetIMEFileNameA(This,hKL,uBufLen,szFileName,puCopied) (This)->lpVtbl->GetIMEFileNameA(This,hKL,uBufLen,szFileName,puCopied)
#define IActiveIMMIME_GetIMEFileNameW(This,hKL,uBufLen,szFileName,puCopied) (This)->lpVtbl->GetIMEFileNameW(This,hKL,uBufLen,szFileName,puCopied)
#define IActiveIMMIME_GetOpenStatus(This,hIMC) (This)->lpVtbl->GetOpenStatus(This,hIMC)
#define IActiveIMMIME_GetProperty(This,hKL,fdwIndex,pdwProperty) (This)->lpVtbl->GetProperty(This,hKL,fdwIndex,pdwProperty)
#define IActiveIMMIME_GetRegisterWordStyleA(This,hKL,nItem,pStyleBuf,puCopied) (This)->lpVtbl->GetRegisterWordStyleA(This,hKL,nItem,pStyleBuf,puCopied)
#define IActiveIMMIME_GetRegisterWordStyleW(This,hKL,nItem,pStyleBuf,puCopied) (This)->lpVtbl->GetRegisterWordStyleW(This,hKL,nItem,pStyleBuf,puCopied)
#define IActiveIMMIME_GetStatusWindowPos(This,hIMC,pptPos) (This)->lpVtbl->GetStatusWindowPos(This,hIMC,pptPos)
#define IActiveIMMIME_GetVirtualKey(This,hWnd,puVirtualKey) (This)->lpVtbl->GetVirtualKey(This,hWnd,puVirtualKey)
#define IActiveIMMIME_InstallIMEA(This,szIMEFileName,szLayoutText,phKL) (This)->lpVtbl->InstallIMEA(This,szIMEFileName,szLayoutText,phKL)
#define IActiveIMMIME_InstallIMEW(This,szIMEFileName,szLayoutText,phKL) (This)->lpVtbl->InstallIMEW(This,szIMEFileName,szLayoutText,phKL)
#define IActiveIMMIME_IsIME(This,hKL) (This)->lpVtbl->IsIME(This,hKL)
#define IActiveIMMIME_IsUIMessageA(This,hWndIME,msg,wParam,lParam) (This)->lpVtbl->IsUIMessageA(This,hWndIME,msg,wParam,lParam)
#define IActiveIMMIME_IsUIMessageW(This,hWndIME,msg,wParam,lParam) (This)->lpVtbl->IsUIMessageW(This,hWndIME,msg,wParam,lParam)
#define IActiveIMMIME_NotifyIME(This,hIMC,dwAction,dwIndex,dwValue) (This)->lpVtbl->NotifyIME(This,hIMC,dwAction,dwIndex,dwValue)
#define IActiveIMMIME_RegisterWordA(This,hKL,szReading,dwStyle,szRegister) (This)->lpVtbl->RegisterWordA(This,hKL,szReading,dwStyle,szRegister)
#define IActiveIMMIME_RegisterWordW(This,hKL,szReading,dwStyle,szRegister) (This)->lpVtbl->RegisterWordW(This,hKL,szReading,dwStyle,szRegister)
#define IActiveIMMIME_ReleaseContext(This,hWnd,hIMC) (This)->lpVtbl->ReleaseContext(This,hWnd,hIMC)
#define IActiveIMMIME_SetCandidateWindow(This,hIMC,pCandidate) (This)->lpVtbl->SetCandidateWindow(This,hIMC,pCandidate)
#define IActiveIMMIME_SetCompositionFontA(This,hIMC,plf) (This)->lpVtbl->SetCompositionFontA(This,hIMC,plf)
#define IActiveIMMIME_SetCompositionFontW(This,hIMC,plf) (This)->lpVtbl->SetCompositionFontW(This,hIMC,plf)
#define IActiveIMMIME_SetCompositionStringA(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen) (This)->lpVtbl->SetCompositionStringA(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen)
#define IActiveIMMIME_SetCompositionStringW(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen) (This)->lpVtbl->SetCompositionStringW(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen)
#define IActiveIMMIME_SetCompositionWindow(This,hIMC,pCompForm) (This)->lpVtbl->SetCompositionWindow(This,hIMC,pCompForm)
#define IActiveIMMIME_SetConversionStatus(This,hIMC,fdwConversion,fdwSentence) (This)->lpVtbl->SetConversionStatus(This,hIMC,fdwConversion,fdwSentence)
#define IActiveIMMIME_SetOpenStatus(This,hIMC,fOpen) (This)->lpVtbl->SetOpenStatus(This,hIMC,fOpen)
#define IActiveIMMIME_SetStatusWindowPos(This,hIMC,pptPos) (This)->lpVtbl->SetStatusWindowPos(This,hIMC,pptPos)
#define IActiveIMMIME_SimulateHotKey(This,hWnd,dwHotKeyID) (This)->lpVtbl->SimulateHotKey(This,hWnd,dwHotKeyID)
#define IActiveIMMIME_UnregisterWordA(This,hKL,szReading,dwStyle,szUnregister) (This)->lpVtbl->UnregisterWordA(This,hKL,szReading,dwStyle,szUnregister)
#define IActiveIMMIME_UnregisterWordW(This,hKL,szReading,dwStyle,szUnregister) (This)->lpVtbl->UnregisterWordW(This,hKL,szReading,dwStyle,szUnregister)
#define IActiveIMMIME_GenerateMessage(This,hIMC) (This)->lpVtbl->GenerateMessage(This,hIMC)
#define IActiveIMMIME_LockIMC(This,hIMC,ppIMC) (This)->lpVtbl->LockIMC(This,hIMC,ppIMC)
#define IActiveIMMIME_UnlockIMC(This,hIMC) (This)->lpVtbl->UnlockIMC(This,hIMC)
#define IActiveIMMIME_GetIMCLockCount(This,hIMC,pdwLockCount) (This)->lpVtbl->GetIMCLockCount(This,hIMC,pdwLockCount)
#define IActiveIMMIME_CreateIMCC(This,dwSize,phIMCC) (This)->lpVtbl->CreateIMCC(This,dwSize,phIMCC)
#define IActiveIMMIME_DestroyIMCC(This,hIMCC) (This)->lpVtbl->DestroyIMCC(This,hIMCC)
#define IActiveIMMIME_LockIMCC(This,hIMCC,ppv) (This)->lpVtbl->LockIMCC(This,hIMCC,ppv)
#define IActiveIMMIME_UnlockIMCC(This,hIMCC) (This)->lpVtbl->UnlockIMCC(This,hIMCC)
#define IActiveIMMIME_ReSizeIMCC(This,hIMCC,dwSize,phIMCC) (This)->lpVtbl->ReSizeIMCC(This,hIMCC,dwSize,phIMCC)
#define IActiveIMMIME_GetIMCCSize(This,hIMCC,pdwSize) (This)->lpVtbl->GetIMCCSize(This,hIMCC,pdwSize)
#define IActiveIMMIME_GetIMCCLockCount(This,hIMCC,pdwLockCount) (This)->lpVtbl->GetIMCCLockCount(This,hIMCC,pdwLockCount)
#define IActiveIMMIME_GetHotKey(This,dwHotKeyID,puModifiers,puVKey,phKL) (This)->lpVtbl->GetHotKey(This,dwHotKeyID,puModifiers,puVKey,phKL)
#define IActiveIMMIME_SetHotKey(This,dwHotKeyID,uModifiers,uVKey,hKL) (This)->lpVtbl->SetHotKey(This,dwHotKeyID,uModifiers,uVKey,hKL)
#define IActiveIMMIME_CreateSoftKeyboard(This,uType,hOwner,x,y,phSoftKbdWnd) (This)->lpVtbl->CreateSoftKeyboard(This,uType,hOwner,x,y,phSoftKbdWnd)
#define IActiveIMMIME_DestroySoftKeyboard(This,hSoftKbdWnd) (This)->lpVtbl->DestroySoftKeyboard(This,hSoftKbdWnd)
#define IActiveIMMIME_ShowSoftKeyboard(This,hSoftKbdWnd,nCmdShow) (This)->lpVtbl->ShowSoftKeyboard(This,hSoftKbdWnd,nCmdShow)
#define IActiveIMMIME_GetCodePageA(This,hKL,uCodePage) (This)->lpVtbl->GetCodePageA(This,hKL,uCodePage)
#define IActiveIMMIME_GetLangId(This,hKL,plid) (This)->lpVtbl->GetLangId(This,hKL,plid)
#define IActiveIMMIME_KeybdEvent(This,lgidIME,bVk,bScan,dwFlags,dwExtraInfo) (This)->lpVtbl->KeybdEvent(This,lgidIME,bVk,bScan,dwFlags,dwExtraInfo)
#define IActiveIMMIME_LockModal(This) (This)->lpVtbl->LockModal(This)
#define IActiveIMMIME_UnlockModal(This) (This)->lpVtbl->UnlockModal(This)
#define IActiveIMMIME_AssociateContextEx(This,hWnd,hIMC,dwFlags) (This)->lpVtbl->AssociateContextEx(This,hWnd,hIMC,dwFlags)
#define IActiveIMMIME_DisableIME(This,idThread) (This)->lpVtbl->DisableIME(This,idThread)
#define IActiveIMMIME_GetImeMenuItemsA(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult) (This)->lpVtbl->GetImeMenuItemsA(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult)
#define IActiveIMMIME_GetImeMenuItemsW(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult) (This)->lpVtbl->GetImeMenuItemsW(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult)
#define IActiveIMMIME_EnumInputContext(This,idThread,ppEnum) (This)->lpVtbl->EnumInputContext(This,idThread,ppEnum)
#define IActiveIMMIME_RequestMessageA(This,hIMC,wParam,lParam,plResult) (This)->lpVtbl->RequestMessageA(This,hIMC,wParam,lParam,plResult)
#define IActiveIMMIME_RequestMessageW(This,hIMC,wParam,lParam,plResult) (This)->lpVtbl->RequestMessageW(This,hIMC,wParam,lParam,plResult)
#define IActiveIMMIME_SendIMCA(This,hWnd,uMsg,wParam,lParam,plResult) (This)->lpVtbl->SendIMCA(This,hWnd,uMsg,wParam,lParam,plResult)
#define IActiveIMMIME_SendIMCW(This,hWnd,uMsg,wParam,lParam,plResult) (This)->lpVtbl->SendIMCW(This,hWnd,uMsg,wParam,lParam,plResult)
#define IActiveIMMIME_IsSleeping(This) (This)->lpVtbl->IsSleeping(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveIMMIME_QueryInterface(IActiveIMMIME* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveIMMIME_AddRef(IActiveIMMIME* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveIMMIME_Release(IActiveIMMIME* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveIMMIME methods ***/
static inline HRESULT IActiveIMMIME_AssociateContext(IActiveIMMIME* This,HWND hWnd,HIMC hIME,HIMC *phPrev) {
    return This->lpVtbl->AssociateContext(This,hWnd,hIME,phPrev);
}
static inline HRESULT IActiveIMMIME_ConfigureIMEA(IActiveIMMIME* This,HKL hKL,HWND hWnd,DWORD dwMode,REGISTERWORDA *pData) {
    return This->lpVtbl->ConfigureIMEA(This,hKL,hWnd,dwMode,pData);
}
static inline HRESULT IActiveIMMIME_ConfigureIMEW(IActiveIMMIME* This,HKL hKL,HWND hWnd,DWORD dwMode,REGISTERWORDW *pData) {
    return This->lpVtbl->ConfigureIMEW(This,hKL,hWnd,dwMode,pData);
}
static inline HRESULT IActiveIMMIME_CreateContext(IActiveIMMIME* This,HIMC *phIMC) {
    return This->lpVtbl->CreateContext(This,phIMC);
}
static inline HRESULT IActiveIMMIME_DestroyContext(IActiveIMMIME* This,HIMC hIME) {
    return This->lpVtbl->DestroyContext(This,hIME);
}
static inline HRESULT IActiveIMMIME_EnumRegisterWordA(IActiveIMMIME* This,HKL hKL,LPSTR szReading,DWORD dwStyle,LPSTR szRegister,LPVOID pData,IEnumRegisterWordA **pEnum) {
    return This->lpVtbl->EnumRegisterWordA(This,hKL,szReading,dwStyle,szRegister,pData,pEnum);
}
static inline HRESULT IActiveIMMIME_EnumRegisterWordW(IActiveIMMIME* This,HKL hKL,LPWSTR szReading,DWORD dwStyle,LPWSTR szRegister,LPVOID pData,IEnumRegisterWordW **pEnum) {
    return This->lpVtbl->EnumRegisterWordW(This,hKL,szReading,dwStyle,szRegister,pData,pEnum);
}
static inline HRESULT IActiveIMMIME_EscapeA(IActiveIMMIME* This,HKL hKL,HIMC hIMC,UINT uEscape,LPVOID pData,LRESULT *plResult) {
    return This->lpVtbl->EscapeA(This,hKL,hIMC,uEscape,pData,plResult);
}
static inline HRESULT IActiveIMMIME_EscapeW(IActiveIMMIME* This,HKL hKL,HIMC hIMC,UINT uEscape,LPVOID pData,LRESULT *plResult) {
    return This->lpVtbl->EscapeW(This,hKL,hIMC,uEscape,pData,plResult);
}
static inline HRESULT IActiveIMMIME_GetCandidateListA(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,UINT uBufLen,CANDIDATELIST *pCandList,UINT *puCopied) {
    return This->lpVtbl->GetCandidateListA(This,hIMC,dwIndex,uBufLen,pCandList,puCopied);
}
static inline HRESULT IActiveIMMIME_GetCandidateListW(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,UINT uBufLen,CANDIDATELIST *pCandList,UINT *puCopied) {
    return This->lpVtbl->GetCandidateListW(This,hIMC,dwIndex,uBufLen,pCandList,puCopied);
}
static inline HRESULT IActiveIMMIME_GetCandidateListCountA(IActiveIMMIME* This,HIMC hIMC,DWORD *pdwListSize,DWORD *pdwBufLen) {
    return This->lpVtbl->GetCandidateListCountA(This,hIMC,pdwListSize,pdwBufLen);
}
static inline HRESULT IActiveIMMIME_GetCandidateListCountW(IActiveIMMIME* This,HIMC hIMC,DWORD *pdwListSize,DWORD *pdwBufLen) {
    return This->lpVtbl->GetCandidateListCountW(This,hIMC,pdwListSize,pdwBufLen);
}
static inline HRESULT IActiveIMMIME_GetCandidateWindow(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,CANDIDATEFORM *pCandidate) {
    return This->lpVtbl->GetCandidateWindow(This,hIMC,dwIndex,pCandidate);
}
static inline HRESULT IActiveIMMIME_GetCompositionFontA(IActiveIMMIME* This,HIMC hIMC,LOGFONTA *plf) {
    return This->lpVtbl->GetCompositionFontA(This,hIMC,plf);
}
static inline HRESULT IActiveIMMIME_GetCompositionFontW(IActiveIMMIME* This,HIMC hIMC,LOGFONTW *plf) {
    return This->lpVtbl->GetCompositionFontW(This,hIMC,plf);
}
static inline HRESULT IActiveIMMIME_GetCompositionStringA(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LONG *plCopied,LPVOID pBuf) {
    return This->lpVtbl->GetCompositionStringA(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf);
}
static inline HRESULT IActiveIMMIME_GetCompositionStringW(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LONG *plCopied,LPVOID pBuf) {
    return This->lpVtbl->GetCompositionStringW(This,hIMC,dwIndex,dwBufLen,plCopied,pBuf);
}
static inline HRESULT IActiveIMMIME_GetCompositionWindow(IActiveIMMIME* This,HIMC hIMC,COMPOSITIONFORM *pCompForm) {
    return This->lpVtbl->GetCompositionWindow(This,hIMC,pCompForm);
}
static inline HRESULT IActiveIMMIME_GetContext(IActiveIMMIME* This,HWND hWnd,HIMC *phIMC) {
    return This->lpVtbl->GetContext(This,hWnd,phIMC);
}
static inline HRESULT IActiveIMMIME_GetConversionListA(IActiveIMMIME* This,HKL hKL,HIMC hIMC,LPSTR pSrc,UINT uBufLen,UINT uFlag,CANDIDATELIST *pDst,UINT *puCopied) {
    return This->lpVtbl->GetConversionListA(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied);
}
static inline HRESULT IActiveIMMIME_GetConversionListW(IActiveIMMIME* This,HKL hKL,HIMC hIMC,LPWSTR pSrc,UINT uBufLen,UINT uFlag,CANDIDATELIST *pDst,UINT *puCopied) {
    return This->lpVtbl->GetConversionListW(This,hKL,hIMC,pSrc,uBufLen,uFlag,pDst,puCopied);
}
static inline HRESULT IActiveIMMIME_GetConversionStatus(IActiveIMMIME* This,HIMC hIMC,DWORD *pfdwConversion,DWORD *pfdwSentence) {
    return This->lpVtbl->GetConversionStatus(This,hIMC,pfdwConversion,pfdwSentence);
}
static inline HRESULT IActiveIMMIME_GetDefaultIMEWnd(IActiveIMMIME* This,HWND hWnd,HWND *phDefWnd) {
    return This->lpVtbl->GetDefaultIMEWnd(This,hWnd,phDefWnd);
}
static inline HRESULT IActiveIMMIME_GetDescriptionA(IActiveIMMIME* This,HKL hKL,UINT uBufLen,LPSTR szDescription,UINT *puCopied) {
    return This->lpVtbl->GetDescriptionA(This,hKL,uBufLen,szDescription,puCopied);
}
static inline HRESULT IActiveIMMIME_GetDescriptionW(IActiveIMMIME* This,HKL hKL,UINT uBufLen,LPWSTR szDescription,UINT *puCopied) {
    return This->lpVtbl->GetDescriptionW(This,hKL,uBufLen,szDescription,puCopied);
}
static inline HRESULT IActiveIMMIME_GetGuideLineA(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LPSTR pBuf,DWORD *pdwResult) {
    return This->lpVtbl->GetGuideLineA(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult);
}
static inline HRESULT IActiveIMMIME_GetGuideLineW(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,DWORD dwBufLen,LPWSTR pBuf,DWORD *pdwResult) {
    return This->lpVtbl->GetGuideLineW(This,hIMC,dwIndex,dwBufLen,pBuf,pdwResult);
}
static inline HRESULT IActiveIMMIME_GetIMEFileNameA(IActiveIMMIME* This,HKL hKL,UINT uBufLen,LPSTR szFileName,UINT *puCopied) {
    return This->lpVtbl->GetIMEFileNameA(This,hKL,uBufLen,szFileName,puCopied);
}
static inline HRESULT IActiveIMMIME_GetIMEFileNameW(IActiveIMMIME* This,HKL hKL,UINT uBufLen,LPWSTR szFileName,UINT *puCopied) {
    return This->lpVtbl->GetIMEFileNameW(This,hKL,uBufLen,szFileName,puCopied);
}
static inline HRESULT IActiveIMMIME_GetOpenStatus(IActiveIMMIME* This,HIMC hIMC) {
    return This->lpVtbl->GetOpenStatus(This,hIMC);
}
static inline HRESULT IActiveIMMIME_GetProperty(IActiveIMMIME* This,HKL hKL,DWORD fdwIndex,DWORD *pdwProperty) {
    return This->lpVtbl->GetProperty(This,hKL,fdwIndex,pdwProperty);
}
static inline HRESULT IActiveIMMIME_GetRegisterWordStyleA(IActiveIMMIME* This,HKL hKL,UINT nItem,STYLEBUFA *pStyleBuf,UINT *puCopied) {
    return This->lpVtbl->GetRegisterWordStyleA(This,hKL,nItem,pStyleBuf,puCopied);
}
static inline HRESULT IActiveIMMIME_GetRegisterWordStyleW(IActiveIMMIME* This,HKL hKL,UINT nItem,STYLEBUFW *pStyleBuf,UINT *puCopied) {
    return This->lpVtbl->GetRegisterWordStyleW(This,hKL,nItem,pStyleBuf,puCopied);
}
static inline HRESULT IActiveIMMIME_GetStatusWindowPos(IActiveIMMIME* This,HIMC hIMC,POINT *pptPos) {
    return This->lpVtbl->GetStatusWindowPos(This,hIMC,pptPos);
}
static inline HRESULT IActiveIMMIME_GetVirtualKey(IActiveIMMIME* This,HWND hWnd,UINT *puVirtualKey) {
    return This->lpVtbl->GetVirtualKey(This,hWnd,puVirtualKey);
}
static inline HRESULT IActiveIMMIME_InstallIMEA(IActiveIMMIME* This,LPSTR szIMEFileName,LPSTR szLayoutText,HKL *phKL) {
    return This->lpVtbl->InstallIMEA(This,szIMEFileName,szLayoutText,phKL);
}
static inline HRESULT IActiveIMMIME_InstallIMEW(IActiveIMMIME* This,LPWSTR szIMEFileName,LPWSTR szLayoutText,HKL *phKL) {
    return This->lpVtbl->InstallIMEW(This,szIMEFileName,szLayoutText,phKL);
}
static inline HRESULT IActiveIMMIME_IsIME(IActiveIMMIME* This,HKL hKL) {
    return This->lpVtbl->IsIME(This,hKL);
}
static inline HRESULT IActiveIMMIME_IsUIMessageA(IActiveIMMIME* This,HWND hWndIME,UINT msg,WPARAM wParam,LPARAM lParam) {
    return This->lpVtbl->IsUIMessageA(This,hWndIME,msg,wParam,lParam);
}
static inline HRESULT IActiveIMMIME_IsUIMessageW(IActiveIMMIME* This,HWND hWndIME,UINT msg,WPARAM wParam,LPARAM lParam) {
    return This->lpVtbl->IsUIMessageW(This,hWndIME,msg,wParam,lParam);
}
static inline HRESULT IActiveIMMIME_NotifyIME(IActiveIMMIME* This,HIMC hIMC,DWORD dwAction,DWORD dwIndex,DWORD dwValue) {
    return This->lpVtbl->NotifyIME(This,hIMC,dwAction,dwIndex,dwValue);
}
static inline HRESULT IActiveIMMIME_RegisterWordA(IActiveIMMIME* This,HKL hKL,LPSTR szReading,DWORD dwStyle,LPSTR szRegister) {
    return This->lpVtbl->RegisterWordA(This,hKL,szReading,dwStyle,szRegister);
}
static inline HRESULT IActiveIMMIME_RegisterWordW(IActiveIMMIME* This,HKL hKL,LPWSTR szReading,DWORD dwStyle,LPWSTR szRegister) {
    return This->lpVtbl->RegisterWordW(This,hKL,szReading,dwStyle,szRegister);
}
static inline HRESULT IActiveIMMIME_ReleaseContext(IActiveIMMIME* This,HWND hWnd,HIMC hIMC) {
    return This->lpVtbl->ReleaseContext(This,hWnd,hIMC);
}
static inline HRESULT IActiveIMMIME_SetCandidateWindow(IActiveIMMIME* This,HIMC hIMC,CANDIDATEFORM *pCandidate) {
    return This->lpVtbl->SetCandidateWindow(This,hIMC,pCandidate);
}
static inline HRESULT IActiveIMMIME_SetCompositionFontA(IActiveIMMIME* This,HIMC hIMC,LOGFONTA *plf) {
    return This->lpVtbl->SetCompositionFontA(This,hIMC,plf);
}
static inline HRESULT IActiveIMMIME_SetCompositionFontW(IActiveIMMIME* This,HIMC hIMC,LOGFONTW *plf) {
    return This->lpVtbl->SetCompositionFontW(This,hIMC,plf);
}
static inline HRESULT IActiveIMMIME_SetCompositionStringA(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,LPVOID pComp,DWORD dwCompLen,LPVOID pRead,DWORD dwReadLen) {
    return This->lpVtbl->SetCompositionStringA(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen);
}
static inline HRESULT IActiveIMMIME_SetCompositionStringW(IActiveIMMIME* This,HIMC hIMC,DWORD dwIndex,LPVOID pComp,DWORD dwCompLen,LPVOID pRead,DWORD dwReadLen) {
    return This->lpVtbl->SetCompositionStringW(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen);
}
static inline HRESULT IActiveIMMIME_SetCompositionWindow(IActiveIMMIME* This,HIMC hIMC,COMPOSITIONFORM *pCompForm) {
    return This->lpVtbl->SetCompositionWindow(This,hIMC,pCompForm);
}
static inline HRESULT IActiveIMMIME_SetConversionStatus(IActiveIMMIME* This,HIMC hIMC,DWORD fdwConversion,DWORD fdwSentence) {
    return This->lpVtbl->SetConversionStatus(This,hIMC,fdwConversion,fdwSentence);
}
static inline HRESULT IActiveIMMIME_SetOpenStatus(IActiveIMMIME* This,HIMC hIMC,WINBOOL fOpen) {
    return This->lpVtbl->SetOpenStatus(This,hIMC,fOpen);
}
static inline HRESULT IActiveIMMIME_SetStatusWindowPos(IActiveIMMIME* This,HIMC hIMC,POINT *pptPos) {
    return This->lpVtbl->SetStatusWindowPos(This,hIMC,pptPos);
}
static inline HRESULT IActiveIMMIME_SimulateHotKey(IActiveIMMIME* This,HWND hWnd,DWORD dwHotKeyID) {
    return This->lpVtbl->SimulateHotKey(This,hWnd,dwHotKeyID);
}
static inline HRESULT IActiveIMMIME_UnregisterWordA(IActiveIMMIME* This,HKL hKL,LPSTR szReading,DWORD dwStyle,LPSTR szUnregister) {
    return This->lpVtbl->UnregisterWordA(This,hKL,szReading,dwStyle,szUnregister);
}
static inline HRESULT IActiveIMMIME_UnregisterWordW(IActiveIMMIME* This,HKL hKL,LPWSTR szReading,DWORD dwStyle,LPWSTR szUnregister) {
    return This->lpVtbl->UnregisterWordW(This,hKL,szReading,dwStyle,szUnregister);
}
static inline HRESULT IActiveIMMIME_GenerateMessage(IActiveIMMIME* This,HIMC hIMC) {
    return This->lpVtbl->GenerateMessage(This,hIMC);
}
static inline HRESULT IActiveIMMIME_LockIMC(IActiveIMMIME* This,HIMC hIMC,INPUTCONTEXT **ppIMC) {
    return This->lpVtbl->LockIMC(This,hIMC,ppIMC);
}
static inline HRESULT IActiveIMMIME_UnlockIMC(IActiveIMMIME* This,HIMC hIMC) {
    return This->lpVtbl->UnlockIMC(This,hIMC);
}
static inline HRESULT IActiveIMMIME_GetIMCLockCount(IActiveIMMIME* This,HIMC hIMC,DWORD *pdwLockCount) {
    return This->lpVtbl->GetIMCLockCount(This,hIMC,pdwLockCount);
}
static inline HRESULT IActiveIMMIME_CreateIMCC(IActiveIMMIME* This,DWORD dwSize,HIMCC *phIMCC) {
    return This->lpVtbl->CreateIMCC(This,dwSize,phIMCC);
}
static inline HRESULT IActiveIMMIME_DestroyIMCC(IActiveIMMIME* This,HIMCC hIMCC) {
    return This->lpVtbl->DestroyIMCC(This,hIMCC);
}
static inline HRESULT IActiveIMMIME_LockIMCC(IActiveIMMIME* This,HIMCC hIMCC,void **ppv) {
    return This->lpVtbl->LockIMCC(This,hIMCC,ppv);
}
static inline HRESULT IActiveIMMIME_UnlockIMCC(IActiveIMMIME* This,HIMCC hIMCC) {
    return This->lpVtbl->UnlockIMCC(This,hIMCC);
}
static inline HRESULT IActiveIMMIME_ReSizeIMCC(IActiveIMMIME* This,HIMCC hIMCC,DWORD dwSize,HIMCC *phIMCC) {
    return This->lpVtbl->ReSizeIMCC(This,hIMCC,dwSize,phIMCC);
}
static inline HRESULT IActiveIMMIME_GetIMCCSize(IActiveIMMIME* This,HIMCC hIMCC,DWORD *pdwSize) {
    return This->lpVtbl->GetIMCCSize(This,hIMCC,pdwSize);
}
static inline HRESULT IActiveIMMIME_GetIMCCLockCount(IActiveIMMIME* This,HIMCC hIMCC,DWORD *pdwLockCount) {
    return This->lpVtbl->GetIMCCLockCount(This,hIMCC,pdwLockCount);
}
static inline HRESULT IActiveIMMIME_GetHotKey(IActiveIMMIME* This,DWORD dwHotKeyID,UINT *puModifiers,UINT *puVKey,HKL *phKL) {
    return This->lpVtbl->GetHotKey(This,dwHotKeyID,puModifiers,puVKey,phKL);
}
static inline HRESULT IActiveIMMIME_SetHotKey(IActiveIMMIME* This,DWORD dwHotKeyID,UINT uModifiers,UINT uVKey,HKL hKL) {
    return This->lpVtbl->SetHotKey(This,dwHotKeyID,uModifiers,uVKey,hKL);
}
static inline HRESULT IActiveIMMIME_CreateSoftKeyboard(IActiveIMMIME* This,UINT uType,HWND hOwner,int x,int y,HWND *phSoftKbdWnd) {
    return This->lpVtbl->CreateSoftKeyboard(This,uType,hOwner,x,y,phSoftKbdWnd);
}
static inline HRESULT IActiveIMMIME_DestroySoftKeyboard(IActiveIMMIME* This,HWND hSoftKbdWnd) {
    return This->lpVtbl->DestroySoftKeyboard(This,hSoftKbdWnd);
}
static inline HRESULT IActiveIMMIME_ShowSoftKeyboard(IActiveIMMIME* This,HWND hSoftKbdWnd,int nCmdShow) {
    return This->lpVtbl->ShowSoftKeyboard(This,hSoftKbdWnd,nCmdShow);
}
static inline HRESULT IActiveIMMIME_GetCodePageA(IActiveIMMIME* This,HKL hKL,UINT *uCodePage) {
    return This->lpVtbl->GetCodePageA(This,hKL,uCodePage);
}
static inline HRESULT IActiveIMMIME_GetLangId(IActiveIMMIME* This,HKL hKL,LANGID *plid) {
    return This->lpVtbl->GetLangId(This,hKL,plid);
}
static inline HRESULT IActiveIMMIME_KeybdEvent(IActiveIMMIME* This,LANGID lgidIME,BYTE bVk,BYTE bScan,DWORD dwFlags,DWORD dwExtraInfo) {
    return This->lpVtbl->KeybdEvent(This,lgidIME,bVk,bScan,dwFlags,dwExtraInfo);
}
static inline HRESULT IActiveIMMIME_LockModal(IActiveIMMIME* This) {
    return This->lpVtbl->LockModal(This);
}
static inline HRESULT IActiveIMMIME_UnlockModal(IActiveIMMIME* This) {
    return This->lpVtbl->UnlockModal(This);
}
static inline HRESULT IActiveIMMIME_AssociateContextEx(IActiveIMMIME* This,HWND hWnd,HIMC hIMC,DWORD dwFlags) {
    return This->lpVtbl->AssociateContextEx(This,hWnd,hIMC,dwFlags);
}
static inline HRESULT IActiveIMMIME_DisableIME(IActiveIMMIME* This,DWORD idThread) {
    return This->lpVtbl->DisableIME(This,idThread);
}
static inline HRESULT IActiveIMMIME_GetImeMenuItemsA(IActiveIMMIME* This,HIMC hIMC,DWORD dwFlags,DWORD dwType,IMEMENUITEMINFOA *pImeParentMenu,IMEMENUITEMINFOA *pImeMenu,DWORD dwSize,DWORD *pdwResult) {
    return This->lpVtbl->GetImeMenuItemsA(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult);
}
static inline HRESULT IActiveIMMIME_GetImeMenuItemsW(IActiveIMMIME* This,HIMC hIMC,DWORD dwFlags,DWORD dwType,IMEMENUITEMINFOW *pImeParentMenu,IMEMENUITEMINFOW *pImeMenu,DWORD dwSize,DWORD *pdwResult) {
    return This->lpVtbl->GetImeMenuItemsW(This,hIMC,dwFlags,dwType,pImeParentMenu,pImeMenu,dwSize,pdwResult);
}
static inline HRESULT IActiveIMMIME_EnumInputContext(IActiveIMMIME* This,DWORD idThread,IEnumInputContext **ppEnum) {
    return This->lpVtbl->EnumInputContext(This,idThread,ppEnum);
}
static inline HRESULT IActiveIMMIME_RequestMessageA(IActiveIMMIME* This,HIMC hIMC,WPARAM wParam,LPARAM lParam,LRESULT *plResult) {
    return This->lpVtbl->RequestMessageA(This,hIMC,wParam,lParam,plResult);
}
static inline HRESULT IActiveIMMIME_RequestMessageW(IActiveIMMIME* This,HIMC hIMC,WPARAM wParam,LPARAM lParam,LRESULT *plResult) {
    return This->lpVtbl->RequestMessageW(This,hIMC,wParam,lParam,plResult);
}
static inline HRESULT IActiveIMMIME_SendIMCA(IActiveIMMIME* This,HWND hWnd,UINT uMsg,WPARAM wParam,LPARAM lParam,LRESULT *plResult) {
    return This->lpVtbl->SendIMCA(This,hWnd,uMsg,wParam,lParam,plResult);
}
static inline HRESULT IActiveIMMIME_SendIMCW(IActiveIMMIME* This,HWND hWnd,UINT uMsg,WPARAM wParam,LPARAM lParam,LRESULT *plResult) {
    return This->lpVtbl->SendIMCW(This,hWnd,uMsg,wParam,lParam,plResult);
}
static inline HRESULT IActiveIMMIME_IsSleeping(IActiveIMMIME* This) {
    return This->lpVtbl->IsSleeping(This);
}
#endif
#endif

#endif


#endif  /* __IActiveIMMIME_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveIME interface
 */
#ifndef __IActiveIME_INTERFACE_DEFINED__
#define __IActiveIME_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveIME, 0x6fe20962, 0xd077, 0x11d0, 0x8f,0xe7, 0x00,0xaa,0x00,0x6b,0xcc,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6fe20962-d077-11d0-8fe7-00aa006bcc59")
IActiveIME : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Inquire(
        DWORD dwSystemInfoFlags,
        IMEINFO *pIMEInfo,
        LPWSTR szWndClass,
        DWORD *pdwPrivate) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConversionList(
        HIMC hIMC,
        LPWSTR szSource,
        UINT uFlag,
        UINT uBufLen,
        CANDIDATELIST *pDest,
        UINT *puCopied) = 0;

    virtual HRESULT STDMETHODCALLTYPE Configure(
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDW *pRegisterWord) = 0;

    virtual HRESULT STDMETHODCALLTYPE Destroy(
        UINT uReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE Escape(
        HIMC hIMC,
        UINT uEscape,
        void *pData,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetActiveContext(
        HIMC hIMC,
        WINBOOL fFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessKey(
        HIMC hIMC,
        UINT uVirKey,
        DWORD lParam,
        BYTE *pbKeyState) = 0;

    virtual HRESULT STDMETHODCALLTYPE Notify(
        HIMC hIMC,
        DWORD dwAction,
        DWORD dwIndex,
        DWORD dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Select(
        HIMC hIMC,
        WINBOOL fSelect) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompositionString(
        HIMC hIMC,
        DWORD dwIndex,
        void *pComp,
        DWORD dwCompLen,
        void *pRead,
        DWORD dwReadLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE ToAsciiEx(
        UINT uVirKey,
        UINT uScanCode,
        BYTE *pbKeyState,
        UINT fuState,
        HIMC hIMC,
        DWORD *pdwTransBuf,
        UINT *puSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterWord(
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szString) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterWord(
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRegisterWordStyle(
        UINT nItem,
        STYLEBUFW *pStyleBuf,
        UINT *puBufSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRegisterWord(
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordW **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodePageA(
        UINT *uCodePage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLangId(
        LANGID *plid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveIME, 0x6fe20962, 0xd077, 0x11d0, 0x8f,0xe7, 0x00,0xaa,0x00,0x6b,0xcc,0x59)
#endif
#else
typedef struct IActiveIMEVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveIME *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveIME *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveIME *This);

    /*** IActiveIME methods ***/
    HRESULT (STDMETHODCALLTYPE *Inquire)(
        IActiveIME *This,
        DWORD dwSystemInfoFlags,
        IMEINFO *pIMEInfo,
        LPWSTR szWndClass,
        DWORD *pdwPrivate);

    HRESULT (STDMETHODCALLTYPE *ConversionList)(
        IActiveIME *This,
        HIMC hIMC,
        LPWSTR szSource,
        UINT uFlag,
        UINT uBufLen,
        CANDIDATELIST *pDest,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *Configure)(
        IActiveIME *This,
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDW *pRegisterWord);

    HRESULT (STDMETHODCALLTYPE *Destroy)(
        IActiveIME *This,
        UINT uReserved);

    HRESULT (STDMETHODCALLTYPE *Escape)(
        IActiveIME *This,
        HIMC hIMC,
        UINT uEscape,
        void *pData,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *SetActiveContext)(
        IActiveIME *This,
        HIMC hIMC,
        WINBOOL fFlag);

    HRESULT (STDMETHODCALLTYPE *ProcessKey)(
        IActiveIME *This,
        HIMC hIMC,
        UINT uVirKey,
        DWORD lParam,
        BYTE *pbKeyState);

    HRESULT (STDMETHODCALLTYPE *Notify)(
        IActiveIME *This,
        HIMC hIMC,
        DWORD dwAction,
        DWORD dwIndex,
        DWORD dwValue);

    HRESULT (STDMETHODCALLTYPE *Select)(
        IActiveIME *This,
        HIMC hIMC,
        WINBOOL fSelect);

    HRESULT (STDMETHODCALLTYPE *SetCompositionString)(
        IActiveIME *This,
        HIMC hIMC,
        DWORD dwIndex,
        void *pComp,
        DWORD dwCompLen,
        void *pRead,
        DWORD dwReadLen);

    HRESULT (STDMETHODCALLTYPE *ToAsciiEx)(
        IActiveIME *This,
        UINT uVirKey,
        UINT uScanCode,
        BYTE *pbKeyState,
        UINT fuState,
        HIMC hIMC,
        DWORD *pdwTransBuf,
        UINT *puSize);

    HRESULT (STDMETHODCALLTYPE *RegisterWord)(
        IActiveIME *This,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szString);

    HRESULT (STDMETHODCALLTYPE *UnregisterWord)(
        IActiveIME *This,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szString);

    HRESULT (STDMETHODCALLTYPE *GetRegisterWordStyle)(
        IActiveIME *This,
        UINT nItem,
        STYLEBUFW *pStyleBuf,
        UINT *puBufSize);

    HRESULT (STDMETHODCALLTYPE *EnumRegisterWord)(
        IActiveIME *This,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordW **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetCodePageA)(
        IActiveIME *This,
        UINT *uCodePage);

    HRESULT (STDMETHODCALLTYPE *GetLangId)(
        IActiveIME *This,
        LANGID *plid);

    END_INTERFACE
} IActiveIMEVtbl;

interface IActiveIME {
    CONST_VTBL IActiveIMEVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveIME_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveIME_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveIME_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveIME methods ***/
#define IActiveIME_Inquire(This,dwSystemInfoFlags,pIMEInfo,szWndClass,pdwPrivate) (This)->lpVtbl->Inquire(This,dwSystemInfoFlags,pIMEInfo,szWndClass,pdwPrivate)
#define IActiveIME_ConversionList(This,hIMC,szSource,uFlag,uBufLen,pDest,puCopied) (This)->lpVtbl->ConversionList(This,hIMC,szSource,uFlag,uBufLen,pDest,puCopied)
#define IActiveIME_Configure(This,hKL,hWnd,dwMode,pRegisterWord) (This)->lpVtbl->Configure(This,hKL,hWnd,dwMode,pRegisterWord)
#define IActiveIME_Destroy(This,uReserved) (This)->lpVtbl->Destroy(This,uReserved)
#define IActiveIME_Escape(This,hIMC,uEscape,pData,plResult) (This)->lpVtbl->Escape(This,hIMC,uEscape,pData,plResult)
#define IActiveIME_SetActiveContext(This,hIMC,fFlag) (This)->lpVtbl->SetActiveContext(This,hIMC,fFlag)
#define IActiveIME_ProcessKey(This,hIMC,uVirKey,lParam,pbKeyState) (This)->lpVtbl->ProcessKey(This,hIMC,uVirKey,lParam,pbKeyState)
#define IActiveIME_Notify(This,hIMC,dwAction,dwIndex,dwValue) (This)->lpVtbl->Notify(This,hIMC,dwAction,dwIndex,dwValue)
#define IActiveIME_Select(This,hIMC,fSelect) (This)->lpVtbl->Select(This,hIMC,fSelect)
#define IActiveIME_SetCompositionString(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen) (This)->lpVtbl->SetCompositionString(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen)
#define IActiveIME_ToAsciiEx(This,uVirKey,uScanCode,pbKeyState,fuState,hIMC,pdwTransBuf,puSize) (This)->lpVtbl->ToAsciiEx(This,uVirKey,uScanCode,pbKeyState,fuState,hIMC,pdwTransBuf,puSize)
#define IActiveIME_RegisterWord(This,szReading,dwStyle,szString) (This)->lpVtbl->RegisterWord(This,szReading,dwStyle,szString)
#define IActiveIME_UnregisterWord(This,szReading,dwStyle,szString) (This)->lpVtbl->UnregisterWord(This,szReading,dwStyle,szString)
#define IActiveIME_GetRegisterWordStyle(This,nItem,pStyleBuf,puBufSize) (This)->lpVtbl->GetRegisterWordStyle(This,nItem,pStyleBuf,puBufSize)
#define IActiveIME_EnumRegisterWord(This,szReading,dwStyle,szRegister,pData,ppEnum) (This)->lpVtbl->EnumRegisterWord(This,szReading,dwStyle,szRegister,pData,ppEnum)
#define IActiveIME_GetCodePageA(This,uCodePage) (This)->lpVtbl->GetCodePageA(This,uCodePage)
#define IActiveIME_GetLangId(This,plid) (This)->lpVtbl->GetLangId(This,plid)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveIME_QueryInterface(IActiveIME* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveIME_AddRef(IActiveIME* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveIME_Release(IActiveIME* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveIME methods ***/
static inline HRESULT IActiveIME_Inquire(IActiveIME* This,DWORD dwSystemInfoFlags,IMEINFO *pIMEInfo,LPWSTR szWndClass,DWORD *pdwPrivate) {
    return This->lpVtbl->Inquire(This,dwSystemInfoFlags,pIMEInfo,szWndClass,pdwPrivate);
}
static inline HRESULT IActiveIME_ConversionList(IActiveIME* This,HIMC hIMC,LPWSTR szSource,UINT uFlag,UINT uBufLen,CANDIDATELIST *pDest,UINT *puCopied) {
    return This->lpVtbl->ConversionList(This,hIMC,szSource,uFlag,uBufLen,pDest,puCopied);
}
static inline HRESULT IActiveIME_Configure(IActiveIME* This,HKL hKL,HWND hWnd,DWORD dwMode,REGISTERWORDW *pRegisterWord) {
    return This->lpVtbl->Configure(This,hKL,hWnd,dwMode,pRegisterWord);
}
static inline HRESULT IActiveIME_Destroy(IActiveIME* This,UINT uReserved) {
    return This->lpVtbl->Destroy(This,uReserved);
}
static inline HRESULT IActiveIME_Escape(IActiveIME* This,HIMC hIMC,UINT uEscape,void *pData,LRESULT *plResult) {
    return This->lpVtbl->Escape(This,hIMC,uEscape,pData,plResult);
}
static inline HRESULT IActiveIME_SetActiveContext(IActiveIME* This,HIMC hIMC,WINBOOL fFlag) {
    return This->lpVtbl->SetActiveContext(This,hIMC,fFlag);
}
static inline HRESULT IActiveIME_ProcessKey(IActiveIME* This,HIMC hIMC,UINT uVirKey,DWORD lParam,BYTE *pbKeyState) {
    return This->lpVtbl->ProcessKey(This,hIMC,uVirKey,lParam,pbKeyState);
}
static inline HRESULT IActiveIME_Notify(IActiveIME* This,HIMC hIMC,DWORD dwAction,DWORD dwIndex,DWORD dwValue) {
    return This->lpVtbl->Notify(This,hIMC,dwAction,dwIndex,dwValue);
}
static inline HRESULT IActiveIME_Select(IActiveIME* This,HIMC hIMC,WINBOOL fSelect) {
    return This->lpVtbl->Select(This,hIMC,fSelect);
}
static inline HRESULT IActiveIME_SetCompositionString(IActiveIME* This,HIMC hIMC,DWORD dwIndex,void *pComp,DWORD dwCompLen,void *pRead,DWORD dwReadLen) {
    return This->lpVtbl->SetCompositionString(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen);
}
static inline HRESULT IActiveIME_ToAsciiEx(IActiveIME* This,UINT uVirKey,UINT uScanCode,BYTE *pbKeyState,UINT fuState,HIMC hIMC,DWORD *pdwTransBuf,UINT *puSize) {
    return This->lpVtbl->ToAsciiEx(This,uVirKey,uScanCode,pbKeyState,fuState,hIMC,pdwTransBuf,puSize);
}
static inline HRESULT IActiveIME_RegisterWord(IActiveIME* This,LPWSTR szReading,DWORD dwStyle,LPWSTR szString) {
    return This->lpVtbl->RegisterWord(This,szReading,dwStyle,szString);
}
static inline HRESULT IActiveIME_UnregisterWord(IActiveIME* This,LPWSTR szReading,DWORD dwStyle,LPWSTR szString) {
    return This->lpVtbl->UnregisterWord(This,szReading,dwStyle,szString);
}
static inline HRESULT IActiveIME_GetRegisterWordStyle(IActiveIME* This,UINT nItem,STYLEBUFW *pStyleBuf,UINT *puBufSize) {
    return This->lpVtbl->GetRegisterWordStyle(This,nItem,pStyleBuf,puBufSize);
}
static inline HRESULT IActiveIME_EnumRegisterWord(IActiveIME* This,LPWSTR szReading,DWORD dwStyle,LPWSTR szRegister,LPVOID pData,IEnumRegisterWordW **ppEnum) {
    return This->lpVtbl->EnumRegisterWord(This,szReading,dwStyle,szRegister,pData,ppEnum);
}
static inline HRESULT IActiveIME_GetCodePageA(IActiveIME* This,UINT *uCodePage) {
    return This->lpVtbl->GetCodePageA(This,uCodePage);
}
static inline HRESULT IActiveIME_GetLangId(IActiveIME* This,LANGID *plid) {
    return This->lpVtbl->GetLangId(This,plid);
}
#endif
#endif

#endif


#endif  /* __IActiveIME_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveIME2 interface
 */
#ifndef __IActiveIME2_INTERFACE_DEFINED__
#define __IActiveIME2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveIME2, 0xe1c4bf0e, 0x2d53, 0x11d2, 0x93,0xe1, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e1c4bf0e-2d53-11d2-93e1-0060b067b86e")
IActiveIME2 : public IActiveIME
{
    virtual HRESULT STDMETHODCALLTYPE Sleep(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unsleep(
        WINBOOL fDead) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveIME2, 0xe1c4bf0e, 0x2d53, 0x11d2, 0x93,0xe1, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct IActiveIME2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveIME2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveIME2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveIME2 *This);

    /*** IActiveIME methods ***/
    HRESULT (STDMETHODCALLTYPE *Inquire)(
        IActiveIME2 *This,
        DWORD dwSystemInfoFlags,
        IMEINFO *pIMEInfo,
        LPWSTR szWndClass,
        DWORD *pdwPrivate);

    HRESULT (STDMETHODCALLTYPE *ConversionList)(
        IActiveIME2 *This,
        HIMC hIMC,
        LPWSTR szSource,
        UINT uFlag,
        UINT uBufLen,
        CANDIDATELIST *pDest,
        UINT *puCopied);

    HRESULT (STDMETHODCALLTYPE *Configure)(
        IActiveIME2 *This,
        HKL hKL,
        HWND hWnd,
        DWORD dwMode,
        REGISTERWORDW *pRegisterWord);

    HRESULT (STDMETHODCALLTYPE *Destroy)(
        IActiveIME2 *This,
        UINT uReserved);

    HRESULT (STDMETHODCALLTYPE *Escape)(
        IActiveIME2 *This,
        HIMC hIMC,
        UINT uEscape,
        void *pData,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *SetActiveContext)(
        IActiveIME2 *This,
        HIMC hIMC,
        WINBOOL fFlag);

    HRESULT (STDMETHODCALLTYPE *ProcessKey)(
        IActiveIME2 *This,
        HIMC hIMC,
        UINT uVirKey,
        DWORD lParam,
        BYTE *pbKeyState);

    HRESULT (STDMETHODCALLTYPE *Notify)(
        IActiveIME2 *This,
        HIMC hIMC,
        DWORD dwAction,
        DWORD dwIndex,
        DWORD dwValue);

    HRESULT (STDMETHODCALLTYPE *Select)(
        IActiveIME2 *This,
        HIMC hIMC,
        WINBOOL fSelect);

    HRESULT (STDMETHODCALLTYPE *SetCompositionString)(
        IActiveIME2 *This,
        HIMC hIMC,
        DWORD dwIndex,
        void *pComp,
        DWORD dwCompLen,
        void *pRead,
        DWORD dwReadLen);

    HRESULT (STDMETHODCALLTYPE *ToAsciiEx)(
        IActiveIME2 *This,
        UINT uVirKey,
        UINT uScanCode,
        BYTE *pbKeyState,
        UINT fuState,
        HIMC hIMC,
        DWORD *pdwTransBuf,
        UINT *puSize);

    HRESULT (STDMETHODCALLTYPE *RegisterWord)(
        IActiveIME2 *This,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szString);

    HRESULT (STDMETHODCALLTYPE *UnregisterWord)(
        IActiveIME2 *This,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szString);

    HRESULT (STDMETHODCALLTYPE *GetRegisterWordStyle)(
        IActiveIME2 *This,
        UINT nItem,
        STYLEBUFW *pStyleBuf,
        UINT *puBufSize);

    HRESULT (STDMETHODCALLTYPE *EnumRegisterWord)(
        IActiveIME2 *This,
        LPWSTR szReading,
        DWORD dwStyle,
        LPWSTR szRegister,
        LPVOID pData,
        IEnumRegisterWordW **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetCodePageA)(
        IActiveIME2 *This,
        UINT *uCodePage);

    HRESULT (STDMETHODCALLTYPE *GetLangId)(
        IActiveIME2 *This,
        LANGID *plid);

    /*** IActiveIME2 methods ***/
    HRESULT (STDMETHODCALLTYPE *Sleep)(
        IActiveIME2 *This);

    HRESULT (STDMETHODCALLTYPE *Unsleep)(
        IActiveIME2 *This,
        WINBOOL fDead);

    END_INTERFACE
} IActiveIME2Vtbl;

interface IActiveIME2 {
    CONST_VTBL IActiveIME2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveIME2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveIME2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveIME2_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveIME methods ***/
#define IActiveIME2_Inquire(This,dwSystemInfoFlags,pIMEInfo,szWndClass,pdwPrivate) (This)->lpVtbl->Inquire(This,dwSystemInfoFlags,pIMEInfo,szWndClass,pdwPrivate)
#define IActiveIME2_ConversionList(This,hIMC,szSource,uFlag,uBufLen,pDest,puCopied) (This)->lpVtbl->ConversionList(This,hIMC,szSource,uFlag,uBufLen,pDest,puCopied)
#define IActiveIME2_Configure(This,hKL,hWnd,dwMode,pRegisterWord) (This)->lpVtbl->Configure(This,hKL,hWnd,dwMode,pRegisterWord)
#define IActiveIME2_Destroy(This,uReserved) (This)->lpVtbl->Destroy(This,uReserved)
#define IActiveIME2_Escape(This,hIMC,uEscape,pData,plResult) (This)->lpVtbl->Escape(This,hIMC,uEscape,pData,plResult)
#define IActiveIME2_SetActiveContext(This,hIMC,fFlag) (This)->lpVtbl->SetActiveContext(This,hIMC,fFlag)
#define IActiveIME2_ProcessKey(This,hIMC,uVirKey,lParam,pbKeyState) (This)->lpVtbl->ProcessKey(This,hIMC,uVirKey,lParam,pbKeyState)
#define IActiveIME2_Notify(This,hIMC,dwAction,dwIndex,dwValue) (This)->lpVtbl->Notify(This,hIMC,dwAction,dwIndex,dwValue)
#define IActiveIME2_Select(This,hIMC,fSelect) (This)->lpVtbl->Select(This,hIMC,fSelect)
#define IActiveIME2_SetCompositionString(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen) (This)->lpVtbl->SetCompositionString(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen)
#define IActiveIME2_ToAsciiEx(This,uVirKey,uScanCode,pbKeyState,fuState,hIMC,pdwTransBuf,puSize) (This)->lpVtbl->ToAsciiEx(This,uVirKey,uScanCode,pbKeyState,fuState,hIMC,pdwTransBuf,puSize)
#define IActiveIME2_RegisterWord(This,szReading,dwStyle,szString) (This)->lpVtbl->RegisterWord(This,szReading,dwStyle,szString)
#define IActiveIME2_UnregisterWord(This,szReading,dwStyle,szString) (This)->lpVtbl->UnregisterWord(This,szReading,dwStyle,szString)
#define IActiveIME2_GetRegisterWordStyle(This,nItem,pStyleBuf,puBufSize) (This)->lpVtbl->GetRegisterWordStyle(This,nItem,pStyleBuf,puBufSize)
#define IActiveIME2_EnumRegisterWord(This,szReading,dwStyle,szRegister,pData,ppEnum) (This)->lpVtbl->EnumRegisterWord(This,szReading,dwStyle,szRegister,pData,ppEnum)
#define IActiveIME2_GetCodePageA(This,uCodePage) (This)->lpVtbl->GetCodePageA(This,uCodePage)
#define IActiveIME2_GetLangId(This,plid) (This)->lpVtbl->GetLangId(This,plid)
/*** IActiveIME2 methods ***/
#define IActiveIME2_Sleep(This) (This)->lpVtbl->Sleep(This)
#define IActiveIME2_Unsleep(This,fDead) (This)->lpVtbl->Unsleep(This,fDead)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveIME2_QueryInterface(IActiveIME2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveIME2_AddRef(IActiveIME2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveIME2_Release(IActiveIME2* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveIME methods ***/
static inline HRESULT IActiveIME2_Inquire(IActiveIME2* This,DWORD dwSystemInfoFlags,IMEINFO *pIMEInfo,LPWSTR szWndClass,DWORD *pdwPrivate) {
    return This->lpVtbl->Inquire(This,dwSystemInfoFlags,pIMEInfo,szWndClass,pdwPrivate);
}
static inline HRESULT IActiveIME2_ConversionList(IActiveIME2* This,HIMC hIMC,LPWSTR szSource,UINT uFlag,UINT uBufLen,CANDIDATELIST *pDest,UINT *puCopied) {
    return This->lpVtbl->ConversionList(This,hIMC,szSource,uFlag,uBufLen,pDest,puCopied);
}
static inline HRESULT IActiveIME2_Configure(IActiveIME2* This,HKL hKL,HWND hWnd,DWORD dwMode,REGISTERWORDW *pRegisterWord) {
    return This->lpVtbl->Configure(This,hKL,hWnd,dwMode,pRegisterWord);
}
static inline HRESULT IActiveIME2_Destroy(IActiveIME2* This,UINT uReserved) {
    return This->lpVtbl->Destroy(This,uReserved);
}
static inline HRESULT IActiveIME2_Escape(IActiveIME2* This,HIMC hIMC,UINT uEscape,void *pData,LRESULT *plResult) {
    return This->lpVtbl->Escape(This,hIMC,uEscape,pData,plResult);
}
static inline HRESULT IActiveIME2_SetActiveContext(IActiveIME2* This,HIMC hIMC,WINBOOL fFlag) {
    return This->lpVtbl->SetActiveContext(This,hIMC,fFlag);
}
static inline HRESULT IActiveIME2_ProcessKey(IActiveIME2* This,HIMC hIMC,UINT uVirKey,DWORD lParam,BYTE *pbKeyState) {
    return This->lpVtbl->ProcessKey(This,hIMC,uVirKey,lParam,pbKeyState);
}
static inline HRESULT IActiveIME2_Notify(IActiveIME2* This,HIMC hIMC,DWORD dwAction,DWORD dwIndex,DWORD dwValue) {
    return This->lpVtbl->Notify(This,hIMC,dwAction,dwIndex,dwValue);
}
static inline HRESULT IActiveIME2_Select(IActiveIME2* This,HIMC hIMC,WINBOOL fSelect) {
    return This->lpVtbl->Select(This,hIMC,fSelect);
}
static inline HRESULT IActiveIME2_SetCompositionString(IActiveIME2* This,HIMC hIMC,DWORD dwIndex,void *pComp,DWORD dwCompLen,void *pRead,DWORD dwReadLen) {
    return This->lpVtbl->SetCompositionString(This,hIMC,dwIndex,pComp,dwCompLen,pRead,dwReadLen);
}
static inline HRESULT IActiveIME2_ToAsciiEx(IActiveIME2* This,UINT uVirKey,UINT uScanCode,BYTE *pbKeyState,UINT fuState,HIMC hIMC,DWORD *pdwTransBuf,UINT *puSize) {
    return This->lpVtbl->ToAsciiEx(This,uVirKey,uScanCode,pbKeyState,fuState,hIMC,pdwTransBuf,puSize);
}
static inline HRESULT IActiveIME2_RegisterWord(IActiveIME2* This,LPWSTR szReading,DWORD dwStyle,LPWSTR szString) {
    return This->lpVtbl->RegisterWord(This,szReading,dwStyle,szString);
}
static inline HRESULT IActiveIME2_UnregisterWord(IActiveIME2* This,LPWSTR szReading,DWORD dwStyle,LPWSTR szString) {
    return This->lpVtbl->UnregisterWord(This,szReading,dwStyle,szString);
}
static inline HRESULT IActiveIME2_GetRegisterWordStyle(IActiveIME2* This,UINT nItem,STYLEBUFW *pStyleBuf,UINT *puBufSize) {
    return This->lpVtbl->GetRegisterWordStyle(This,nItem,pStyleBuf,puBufSize);
}
static inline HRESULT IActiveIME2_EnumRegisterWord(IActiveIME2* This,LPWSTR szReading,DWORD dwStyle,LPWSTR szRegister,LPVOID pData,IEnumRegisterWordW **ppEnum) {
    return This->lpVtbl->EnumRegisterWord(This,szReading,dwStyle,szRegister,pData,ppEnum);
}
static inline HRESULT IActiveIME2_GetCodePageA(IActiveIME2* This,UINT *uCodePage) {
    return This->lpVtbl->GetCodePageA(This,uCodePage);
}
static inline HRESULT IActiveIME2_GetLangId(IActiveIME2* This,LANGID *plid) {
    return This->lpVtbl->GetLangId(This,plid);
}
/*** IActiveIME2 methods ***/
static inline HRESULT IActiveIME2_Sleep(IActiveIME2* This) {
    return This->lpVtbl->Sleep(This);
}
static inline HRESULT IActiveIME2_Unsleep(IActiveIME2* This,WINBOOL fDead) {
    return This->lpVtbl->Unsleep(This,fDead);
}
#endif
#endif

#endif


#endif  /* __IActiveIME2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumRegisterWordA interface
 */
#ifndef __IEnumRegisterWordA_INTERFACE_DEFINED__
#define __IEnumRegisterWordA_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumRegisterWordA, 0x08c03412, 0xf96b, 0x11d0, 0xa4,0x75, 0x00,0xaa,0x00,0x6b,0xcc,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("08c03412-f96b-11d0-a475-00aa006bcc59")
IEnumRegisterWordA : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumRegisterWordA **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        REGISTERWORDA *rgRegisterWord,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumRegisterWordA, 0x08c03412, 0xf96b, 0x11d0, 0xa4,0x75, 0x00,0xaa,0x00,0x6b,0xcc,0x59)
#endif
#else
typedef struct IEnumRegisterWordAVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumRegisterWordA *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumRegisterWordA *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumRegisterWordA *This);

    /*** IEnumRegisterWordA methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumRegisterWordA *This,
        IEnumRegisterWordA **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumRegisterWordA *This,
        ULONG ulCount,
        REGISTERWORDA *rgRegisterWord,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumRegisterWordA *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumRegisterWordA *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumRegisterWordAVtbl;

interface IEnumRegisterWordA {
    CONST_VTBL IEnumRegisterWordAVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumRegisterWordA_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumRegisterWordA_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumRegisterWordA_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumRegisterWordA methods ***/
#define IEnumRegisterWordA_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumRegisterWordA_Next(This,ulCount,rgRegisterWord,pcFetched) (This)->lpVtbl->Next(This,ulCount,rgRegisterWord,pcFetched)
#define IEnumRegisterWordA_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumRegisterWordA_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumRegisterWordA_QueryInterface(IEnumRegisterWordA* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumRegisterWordA_AddRef(IEnumRegisterWordA* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumRegisterWordA_Release(IEnumRegisterWordA* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumRegisterWordA methods ***/
static inline HRESULT IEnumRegisterWordA_Clone(IEnumRegisterWordA* This,IEnumRegisterWordA **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumRegisterWordA_Next(IEnumRegisterWordA* This,ULONG ulCount,REGISTERWORDA *rgRegisterWord,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,rgRegisterWord,pcFetched);
}
static inline HRESULT IEnumRegisterWordA_Reset(IEnumRegisterWordA* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumRegisterWordA_Skip(IEnumRegisterWordA* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumRegisterWordA_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumRegisterWordW interface
 */
#ifndef __IEnumRegisterWordW_INTERFACE_DEFINED__
#define __IEnumRegisterWordW_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumRegisterWordW, 0x4955dd31, 0xb159, 0x11d0, 0x8f,0xcf, 0x00,0xaa,0x00,0x6b,0xcc,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4955dd31-b159-11d0-8fcf-00aa006bcc59")
IEnumRegisterWordW : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumRegisterWordW **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        REGISTERWORDW *rgRegisterWord,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumRegisterWordW, 0x4955dd31, 0xb159, 0x11d0, 0x8f,0xcf, 0x00,0xaa,0x00,0x6b,0xcc,0x59)
#endif
#else
typedef struct IEnumRegisterWordWVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumRegisterWordW *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumRegisterWordW *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumRegisterWordW *This);

    /*** IEnumRegisterWordW methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumRegisterWordW *This,
        IEnumRegisterWordW **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumRegisterWordW *This,
        ULONG ulCount,
        REGISTERWORDW *rgRegisterWord,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumRegisterWordW *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumRegisterWordW *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumRegisterWordWVtbl;

interface IEnumRegisterWordW {
    CONST_VTBL IEnumRegisterWordWVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumRegisterWordW_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumRegisterWordW_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumRegisterWordW_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumRegisterWordW methods ***/
#define IEnumRegisterWordW_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumRegisterWordW_Next(This,ulCount,rgRegisterWord,pcFetched) (This)->lpVtbl->Next(This,ulCount,rgRegisterWord,pcFetched)
#define IEnumRegisterWordW_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumRegisterWordW_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumRegisterWordW_QueryInterface(IEnumRegisterWordW* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumRegisterWordW_AddRef(IEnumRegisterWordW* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumRegisterWordW_Release(IEnumRegisterWordW* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumRegisterWordW methods ***/
static inline HRESULT IEnumRegisterWordW_Clone(IEnumRegisterWordW* This,IEnumRegisterWordW **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumRegisterWordW_Next(IEnumRegisterWordW* This,ULONG ulCount,REGISTERWORDW *rgRegisterWord,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,rgRegisterWord,pcFetched);
}
static inline HRESULT IEnumRegisterWordW_Reset(IEnumRegisterWordW* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumRegisterWordW_Skip(IEnumRegisterWordW* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumRegisterWordW_INTERFACE_DEFINED__ */

/*****************************************************************************
 * CActiveIMM coclass
 */

DEFINE_GUID(CLSID_CActiveIMM, 0x4955dd33, 0xb159, 0x11d0, 0x8f,0xcf, 0x00,0xaa,0x00,0x6b,0xcc,0x59);

#ifdef __cplusplus
class DECLSPEC_UUID("4955dd33-b159-11d0-8fcf-00aa006bcc59") CActiveIMM;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CActiveIMM, 0x4955dd33, 0xb159, 0x11d0, 0x8f,0xcf, 0x00,0xaa,0x00,0x6b,0xcc,0x59)
#endif
#endif

#endif /* __ActiveIMM_LIBRARY_DEFINED__ */
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER HBITMAP_UserSize     (ULONG *, ULONG, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserMarshal  (ULONG *, unsigned char *, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserUnmarshal(ULONG *, unsigned char *, HBITMAP *);
void            __RPC_USER HBITMAP_UserFree     (ULONG *, HBITMAP *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dimm_h__ */
