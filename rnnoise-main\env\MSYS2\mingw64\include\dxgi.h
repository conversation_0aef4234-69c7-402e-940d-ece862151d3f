/*** Autogenerated by WIDL 10.12 from include/dxgi.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dxgi_h__
#define __dxgi_h__

/* Forward declarations */

#ifndef __IDXGIObject_FWD_DEFINED__
#define __IDXGIObject_FWD_DEFINED__
typedef interface IDXGIObject IDXGIObject;
#ifdef __cplusplus
interface IDXGIObject;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIDeviceSubObject_FWD_DEFINED__
#define __IDXGIDeviceSubObject_FWD_DEFINED__
typedef interface IDXGIDeviceSubObject IDXGIDeviceSubObject;
#ifdef __cplusplus
interface IDXGIDeviceSubObject;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIResource_FWD_DEFINED__
#define __IDXGIResource_FWD_DEFINED__
typedef interface IDXGIResource IDXGIResource;
#ifdef __cplusplus
interface IDXGIResource;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIKeyedMutex_FWD_DEFINED__
#define __IDXGIKeyedMutex_FWD_DEFINED__
typedef interface IDXGIKeyedMutex IDXGIKeyedMutex;
#ifdef __cplusplus
interface IDXGIKeyedMutex;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISurface_FWD_DEFINED__
#define __IDXGISurface_FWD_DEFINED__
typedef interface IDXGISurface IDXGISurface;
#ifdef __cplusplus
interface IDXGISurface;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISurface1_FWD_DEFINED__
#define __IDXGISurface1_FWD_DEFINED__
typedef interface IDXGISurface1 IDXGISurface1;
#ifdef __cplusplus
interface IDXGISurface1;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIOutput_FWD_DEFINED__
#define __IDXGIOutput_FWD_DEFINED__
typedef interface IDXGIOutput IDXGIOutput;
#ifdef __cplusplus
interface IDXGIOutput;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIAdapter_FWD_DEFINED__
#define __IDXGIAdapter_FWD_DEFINED__
typedef interface IDXGIAdapter IDXGIAdapter;
#ifdef __cplusplus
interface IDXGIAdapter;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISwapChain_FWD_DEFINED__
#define __IDXGISwapChain_FWD_DEFINED__
typedef interface IDXGISwapChain IDXGISwapChain;
#ifdef __cplusplus
interface IDXGISwapChain;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory_FWD_DEFINED__
#define __IDXGIFactory_FWD_DEFINED__
typedef interface IDXGIFactory IDXGIFactory;
#ifdef __cplusplus
interface IDXGIFactory;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIDevice_FWD_DEFINED__
#define __IDXGIDevice_FWD_DEFINED__
typedef interface IDXGIDevice IDXGIDevice;
#ifdef __cplusplus
interface IDXGIDevice;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIAdapter1_FWD_DEFINED__
#define __IDXGIAdapter1_FWD_DEFINED__
typedef interface IDXGIAdapter1 IDXGIAdapter1;
#ifdef __cplusplus
interface IDXGIAdapter1;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIDevice1_FWD_DEFINED__
#define __IDXGIDevice1_FWD_DEFINED__
typedef interface IDXGIDevice1 IDXGIDevice1;
#ifdef __cplusplus
interface IDXGIDevice1;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory1_FWD_DEFINED__
#define __IDXGIFactory1_FWD_DEFINED__
typedef interface IDXGIFactory1 IDXGIFactory1;
#ifdef __cplusplus
interface IDXGIFactory1;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <dxgitype.h>

#ifdef __cplusplus
extern "C" {
#endif

#define _FACDXGI (0x87a)

#define MAKE_DXGI_STATUS(x)                         MAKE_HRESULT(0, _FACDXGI, x)
#define MAKE_DXGI_HRESULT(x)                        MAKE_HRESULT(1, _FACDXGI, x)
#if 0
typedef HANDLE HMONITOR;
typedef struct _LUID {
    DWORD LowPart;
    LONG HighPart;
} LUID;
typedef struct _LUID *PLUID;
#endif
#define DXGI_CPU_ACCESS_NONE (0)

#define DXGI_CPU_ACCESS_DYNAMIC (1)

#define DXGI_CPU_ACCESS_READ_WRITE (2)

#define DXGI_CPU_ACCESS_SCRATCH (3)

#define DXGI_CPU_ACCESS_FIELD (15)

typedef UINT DXGI_USAGE;
#define DXGI_USAGE_SHADER_INPUT (0x10)

#define DXGI_USAGE_RENDER_TARGET_OUTPUT (0x20)

#define DXGI_USAGE_BACK_BUFFER (0x40)

#define DXGI_USAGE_SHARED (0x80)

#define DXGI_USAGE_READ_ONLY (0x100)

#define DXGI_USAGE_DISCARD_ON_PRESENT (0x200)

#define DXGI_USAGE_UNORDERED_ACCESS (0x400)

#define DXGI_ENUM_MODES_INTERLACED (1)

#define DXGI_ENUM_MODES_SCALING (2)

#define DXGI_RESOURCE_PRIORITY_MINIMUM (0x28000000)

#define DXGI_RESOURCE_PRIORITY_LOW (0x50000000)

#define DXGI_RESOURCE_PRIORITY_NORMAL (0x78000000)

#define DXGI_RESOURCE_PRIORITY_HIGH (0xa0000000)

#define DXGI_RESOURCE_PRIORITY_MAXIMUM (0xc8000000)

#define DXGI_MAP_READ (0x1)

#define DXGI_MAP_WRITE (0x2)

#define DXGI_MAP_DISCARD (0x4)

typedef enum DXGI_SWAP_EFFECT {
    DXGI_SWAP_EFFECT_DISCARD = 0,
    DXGI_SWAP_EFFECT_SEQUENTIAL = 1,
    DXGI_SWAP_EFFECT_FLIP_SEQUENTIAL = 3,
    DXGI_SWAP_EFFECT_FLIP_DISCARD = 4
} DXGI_SWAP_EFFECT;
typedef enum DXGI_RESIDENCY {
    DXGI_RESIDENCY_FULLY_RESIDENT = 1,
    DXGI_RESIDENCY_RESIDENT_IN_SHARED_MEMORY = 2,
    DXGI_RESIDENCY_EVICTED_TO_DISK = 3
} DXGI_RESIDENCY;
typedef struct DXGI_SURFACE_DESC {
    UINT Width;
    UINT Height;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
} DXGI_SURFACE_DESC;
typedef struct DXGI_MAPPED_RECT {
    INT Pitch;
    BYTE *pBits;
} DXGI_MAPPED_RECT;
typedef struct DXGI_OUTPUT_DESC {
    WCHAR DeviceName[32];
    RECT DesktopCoordinates;
    WINBOOL AttachedToDesktop;
    DXGI_MODE_ROTATION Rotation;
    HMONITOR Monitor;
} DXGI_OUTPUT_DESC;
typedef struct DXGI_FRAME_STATISTICS {
    UINT PresentCount;
    UINT PresentRefreshCount;
    UINT SyncRefreshCount;
    LARGE_INTEGER SyncQPCTime;
    LARGE_INTEGER SyncGPUTime;
} DXGI_FRAME_STATISTICS;
typedef struct DXGI_ADAPTER_DESC {
    WCHAR Description[128];
    UINT VendorId;
    UINT DeviceId;
    UINT SubSysId;
    UINT Revision;
    SIZE_T DedicatedVideoMemory;
    SIZE_T DedicatedSystemMemory;
    SIZE_T SharedSystemMemory;
    LUID AdapterLuid;
} DXGI_ADAPTER_DESC;
typedef enum DXGI_SWAP_CHAIN_FLAG {
    DXGI_SWAP_CHAIN_FLAG_NONPREROTATED = 0x1,
    DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH = 0x2,
    DXGI_SWAP_CHAIN_FLAG_GDI_COMPATIBLE = 0x4,
    DXGI_SWAP_CHAIN_FLAG_RESTRICTED_CONTENT = 0x8,
    DXGI_SWAP_CHAIN_FLAG_RESTRICT_SHARED_RESOURCE_DRIVER = 0x10,
    DXGI_SWAP_CHAIN_FLAG_DISPLAY_ONLY = 0x20,
    DXGI_SWAP_CHAIN_FLAG_FRAME_LATENCY_WAITABLE_OBJECT = 0x40,
    DXGI_SWAP_CHAIN_FLAG_FOREGROUND_LAYER = 0x80,
    DXGI_SWAP_CHAIN_FLAG_FULLSCREEN_VIDEO = 0x100,
    DXGI_SWAP_CHAIN_FLAG_YUV_VIDEO = 0x200,
    DXGI_SWAP_CHAIN_FLAG_HW_PROTECTED = 0x400,
    DXGI_SWAP_CHAIN_FLAG_ALLOW_TEARING = 0x800,
    DXGI_SWAP_CHAIN_FLAG_RESTRICTED_TO_ALL_HOLOGRAPHIC_DISPLAYS = 0x1000
} DXGI_SWAP_CHAIN_FLAG;
typedef struct DXGI_SWAP_CHAIN_DESC {
    DXGI_MODE_DESC BufferDesc;
    DXGI_SAMPLE_DESC SampleDesc;
    DXGI_USAGE BufferUsage;
    UINT BufferCount;
    HWND OutputWindow;
    WINBOOL Windowed;
    DXGI_SWAP_EFFECT SwapEffect;
    UINT Flags;
} DXGI_SWAP_CHAIN_DESC;
typedef struct DXGI_SHARED_RESOURCE {
    HANDLE Handle;
} DXGI_SHARED_RESOURCE;
/*****************************************************************************
 * IDXGIObject interface
 */
#ifndef __IDXGIObject_INTERFACE_DEFINED__
#define __IDXGIObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIObject, 0xaec22fb8, 0x76f3, 0x4639, 0x9b,0xe0, 0x28,0xeb,0x43,0xa6,0x7a,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aec22fb8-76f3-4639-9be0-28eb43a67a2e")
IDXGIObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetPrivateData(
        REFGUID guid,
        UINT data_size,
        const void *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrivateDataInterface(
        REFGUID guid,
        const IUnknown *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrivateData(
        REFGUID guid,
        UINT *data_size,
        void *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParent(
        REFIID riid,
        void **parent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIObject, 0xaec22fb8, 0x76f3, 0x4639, 0x9b,0xe0, 0x28,0xeb,0x43,0xa6,0x7a,0x2e)
#endif
#else
typedef struct IDXGIObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIObject *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIObject *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIObject *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIObject *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIObject *This,
        REFIID riid,
        void **parent);

    END_INTERFACE
} IDXGIObjectVtbl;

interface IDXGIObject {
    CONST_VTBL IDXGIObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIObject_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIObject_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIObject_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIObject_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIObject_QueryInterface(IDXGIObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIObject_AddRef(IDXGIObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIObject_Release(IDXGIObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIObject_SetPrivateData(IDXGIObject* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIObject_SetPrivateDataInterface(IDXGIObject* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIObject_GetPrivateData(IDXGIObject* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIObject_GetParent(IDXGIObject* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
#endif
#endif

#endif


#endif  /* __IDXGIObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIDeviceSubObject interface
 */
#ifndef __IDXGIDeviceSubObject_INTERFACE_DEFINED__
#define __IDXGIDeviceSubObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDeviceSubObject, 0x3d3e0379, 0xf9de, 0x4d58, 0xbb,0x6c, 0x18,0xd6,0x29,0x92,0xf1,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3d3e0379-f9de-4d58-bb6c-18d62992f1a6")
IDXGIDeviceSubObject : public IDXGIObject
{
    virtual HRESULT STDMETHODCALLTYPE GetDevice(
        REFIID riid,
        void **device) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDeviceSubObject, 0x3d3e0379, 0xf9de, 0x4d58, 0xbb,0x6c, 0x18,0xd6,0x29,0x92,0xf1,0xa6)
#endif
#else
typedef struct IDXGIDeviceSubObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDeviceSubObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDeviceSubObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDeviceSubObject *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIDeviceSubObject *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIDeviceSubObject *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIDeviceSubObject *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIDeviceSubObject *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGIDeviceSubObject *This,
        REFIID riid,
        void **device);

    END_INTERFACE
} IDXGIDeviceSubObjectVtbl;

interface IDXGIDeviceSubObject {
    CONST_VTBL IDXGIDeviceSubObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDeviceSubObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDeviceSubObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDeviceSubObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIDeviceSubObject_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIDeviceSubObject_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIDeviceSubObject_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIDeviceSubObject_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGIDeviceSubObject_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIDeviceSubObject_QueryInterface(IDXGIDeviceSubObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIDeviceSubObject_AddRef(IDXGIDeviceSubObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIDeviceSubObject_Release(IDXGIDeviceSubObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIDeviceSubObject_SetPrivateData(IDXGIDeviceSubObject* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDeviceSubObject_SetPrivateDataInterface(IDXGIDeviceSubObject* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIDeviceSubObject_GetPrivateData(IDXGIDeviceSubObject* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDeviceSubObject_GetParent(IDXGIDeviceSubObject* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGIDeviceSubObject_GetDevice(IDXGIDeviceSubObject* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
#endif
#endif

#endif


#endif  /* __IDXGIDeviceSubObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIResource interface
 */
#ifndef __IDXGIResource_INTERFACE_DEFINED__
#define __IDXGIResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIResource, 0x035f3ab4, 0x482e, 0x4e50, 0xb4,0x1f, 0x8a,0x7f,0x8b,0xd8,0x96,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("035f3ab4-482e-4e50-b41f-8a7f8bd8960b")
IDXGIResource : public IDXGIDeviceSubObject
{
    virtual HRESULT STDMETHODCALLTYPE GetSharedHandle(
        HANDLE *pSharedHandle) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUsage(
        DXGI_USAGE *pUsage) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEvictionPriority(
        UINT EvictionPriority) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEvictionPriority(
        UINT *pEvictionPriority) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIResource, 0x035f3ab4, 0x482e, 0x4e50, 0xb4,0x1f, 0x8a,0x7f,0x8b,0xd8,0x96,0x0b)
#endif
#else
typedef struct IDXGIResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIResource *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIResource *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIResource *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIResource *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIResource *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGIResource *This,
        REFIID riid,
        void **device);

    /*** IDXGIResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSharedHandle)(
        IDXGIResource *This,
        HANDLE *pSharedHandle);

    HRESULT (STDMETHODCALLTYPE *GetUsage)(
        IDXGIResource *This,
        DXGI_USAGE *pUsage);

    HRESULT (STDMETHODCALLTYPE *SetEvictionPriority)(
        IDXGIResource *This,
        UINT EvictionPriority);

    HRESULT (STDMETHODCALLTYPE *GetEvictionPriority)(
        IDXGIResource *This,
        UINT *pEvictionPriority);

    END_INTERFACE
} IDXGIResourceVtbl;

interface IDXGIResource {
    CONST_VTBL IDXGIResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIResource_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIResource_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIResource_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIResource_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIResource_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGIResource_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGIResource methods ***/
#define IDXGIResource_GetSharedHandle(This,pSharedHandle) (This)->lpVtbl->GetSharedHandle(This,pSharedHandle)
#define IDXGIResource_GetUsage(This,pUsage) (This)->lpVtbl->GetUsage(This,pUsage)
#define IDXGIResource_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define IDXGIResource_GetEvictionPriority(This,pEvictionPriority) (This)->lpVtbl->GetEvictionPriority(This,pEvictionPriority)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIResource_QueryInterface(IDXGIResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIResource_AddRef(IDXGIResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIResource_Release(IDXGIResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIResource_SetPrivateData(IDXGIResource* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIResource_SetPrivateDataInterface(IDXGIResource* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIResource_GetPrivateData(IDXGIResource* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIResource_GetParent(IDXGIResource* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGIResource_GetDevice(IDXGIResource* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGIResource methods ***/
static inline HRESULT IDXGIResource_GetSharedHandle(IDXGIResource* This,HANDLE *pSharedHandle) {
    return This->lpVtbl->GetSharedHandle(This,pSharedHandle);
}
static inline HRESULT IDXGIResource_GetUsage(IDXGIResource* This,DXGI_USAGE *pUsage) {
    return This->lpVtbl->GetUsage(This,pUsage);
}
static inline HRESULT IDXGIResource_SetEvictionPriority(IDXGIResource* This,UINT EvictionPriority) {
    return This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static inline HRESULT IDXGIResource_GetEvictionPriority(IDXGIResource* This,UINT *pEvictionPriority) {
    return This->lpVtbl->GetEvictionPriority(This,pEvictionPriority);
}
#endif
#endif

#endif


#endif  /* __IDXGIResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIKeyedMutex interface
 */
#ifndef __IDXGIKeyedMutex_INTERFACE_DEFINED__
#define __IDXGIKeyedMutex_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIKeyedMutex, 0x9d8e1289, 0xd7b3, 0x465f, 0x81,0x26, 0x25,0x0e,0x34,0x9a,0xf8,0x5d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9d8e1289-d7b3-465f-8126-250e349af85d")
IDXGIKeyedMutex : public IDXGIDeviceSubObject
{
    virtual HRESULT STDMETHODCALLTYPE AcquireSync(
        UINT64 Key,
        DWORD dwMilliseconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseSync(
        UINT64 Key) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIKeyedMutex, 0x9d8e1289, 0xd7b3, 0x465f, 0x81,0x26, 0x25,0x0e,0x34,0x9a,0xf8,0x5d)
#endif
#else
typedef struct IDXGIKeyedMutexVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIKeyedMutex *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIKeyedMutex *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIKeyedMutex *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIKeyedMutex *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIKeyedMutex *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIKeyedMutex *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIKeyedMutex *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGIKeyedMutex *This,
        REFIID riid,
        void **device);

    /*** IDXGIKeyedMutex methods ***/
    HRESULT (STDMETHODCALLTYPE *AcquireSync)(
        IDXGIKeyedMutex *This,
        UINT64 Key,
        DWORD dwMilliseconds);

    HRESULT (STDMETHODCALLTYPE *ReleaseSync)(
        IDXGIKeyedMutex *This,
        UINT64 Key);

    END_INTERFACE
} IDXGIKeyedMutexVtbl;

interface IDXGIKeyedMutex {
    CONST_VTBL IDXGIKeyedMutexVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIKeyedMutex_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIKeyedMutex_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIKeyedMutex_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIKeyedMutex_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIKeyedMutex_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIKeyedMutex_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIKeyedMutex_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGIKeyedMutex_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGIKeyedMutex methods ***/
#define IDXGIKeyedMutex_AcquireSync(This,Key,dwMilliseconds) (This)->lpVtbl->AcquireSync(This,Key,dwMilliseconds)
#define IDXGIKeyedMutex_ReleaseSync(This,Key) (This)->lpVtbl->ReleaseSync(This,Key)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIKeyedMutex_QueryInterface(IDXGIKeyedMutex* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIKeyedMutex_AddRef(IDXGIKeyedMutex* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIKeyedMutex_Release(IDXGIKeyedMutex* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIKeyedMutex_SetPrivateData(IDXGIKeyedMutex* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIKeyedMutex_SetPrivateDataInterface(IDXGIKeyedMutex* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIKeyedMutex_GetPrivateData(IDXGIKeyedMutex* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIKeyedMutex_GetParent(IDXGIKeyedMutex* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGIKeyedMutex_GetDevice(IDXGIKeyedMutex* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGIKeyedMutex methods ***/
static inline HRESULT IDXGIKeyedMutex_AcquireSync(IDXGIKeyedMutex* This,UINT64 Key,DWORD dwMilliseconds) {
    return This->lpVtbl->AcquireSync(This,Key,dwMilliseconds);
}
static inline HRESULT IDXGIKeyedMutex_ReleaseSync(IDXGIKeyedMutex* This,UINT64 Key) {
    return This->lpVtbl->ReleaseSync(This,Key);
}
#endif
#endif

#endif


#endif  /* __IDXGIKeyedMutex_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGISurface interface
 */
#ifndef __IDXGISurface_INTERFACE_DEFINED__
#define __IDXGISurface_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISurface, 0xcafcb56c, 0x6ac3, 0x4889, 0xbf,0x47, 0x9e,0x23,0xbb,0xd2,0x60,0xec);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cafcb56c-6ac3-4889-bf47-9e23bbd260ec")
IDXGISurface : public IDXGIDeviceSubObject
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        DXGI_SURFACE_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE Map(
        DXGI_MAPPED_RECT *mapped_rect,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unmap(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISurface, 0xcafcb56c, 0x6ac3, 0x4889, 0xbf,0x47, 0x9e,0x23,0xbb,0xd2,0x60,0xec)
#endif
#else
typedef struct IDXGISurfaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISurface *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISurface *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISurface *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISurface *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISurface *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISurface *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISurface *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISurface *This,
        REFIID riid,
        void **device);

    /*** IDXGISurface methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISurface *This,
        DXGI_SURFACE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *Map)(
        IDXGISurface *This,
        DXGI_MAPPED_RECT *mapped_rect,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *Unmap)(
        IDXGISurface *This);

    END_INTERFACE
} IDXGISurfaceVtbl;

interface IDXGISurface {
    CONST_VTBL IDXGISurfaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISurface_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISurface_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISurface_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISurface_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISurface_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISurface_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISurface_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISurface_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISurface methods ***/
#define IDXGISurface_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISurface_Map(This,mapped_rect,flags) (This)->lpVtbl->Map(This,mapped_rect,flags)
#define IDXGISurface_Unmap(This) (This)->lpVtbl->Unmap(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGISurface_QueryInterface(IDXGISurface* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGISurface_AddRef(IDXGISurface* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGISurface_Release(IDXGISurface* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGISurface_SetPrivateData(IDXGISurface* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISurface_SetPrivateDataInterface(IDXGISurface* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGISurface_GetPrivateData(IDXGISurface* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISurface_GetParent(IDXGISurface* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGISurface_GetDevice(IDXGISurface* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISurface methods ***/
static inline HRESULT IDXGISurface_GetDesc(IDXGISurface* This,DXGI_SURFACE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGISurface_Map(IDXGISurface* This,DXGI_MAPPED_RECT *mapped_rect,UINT flags) {
    return This->lpVtbl->Map(This,mapped_rect,flags);
}
static inline HRESULT IDXGISurface_Unmap(IDXGISurface* This) {
    return This->lpVtbl->Unmap(This);
}
#endif
#endif

#endif


#endif  /* __IDXGISurface_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGISurface1 interface
 */
#ifndef __IDXGISurface1_INTERFACE_DEFINED__
#define __IDXGISurface1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISurface1, 0x4ae63092, 0x6327, 0x4c1b, 0x80,0xae, 0xbf,0xe1,0x2e,0xa3,0x2b,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4ae63092-6327-4c1b-80ae-bfe12ea32b86")
IDXGISurface1 : public IDXGISurface
{
    virtual HRESULT STDMETHODCALLTYPE GetDC(
        WINBOOL discard,
        HDC *hdc) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseDC(
        RECT *dirty_rect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISurface1, 0x4ae63092, 0x6327, 0x4c1b, 0x80,0xae, 0xbf,0xe1,0x2e,0xa3,0x2b,0x86)
#endif
#else
typedef struct IDXGISurface1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISurface1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISurface1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISurface1 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISurface1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISurface1 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISurface1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISurface1 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISurface1 *This,
        REFIID riid,
        void **device);

    /*** IDXGISurface methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISurface1 *This,
        DXGI_SURFACE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *Map)(
        IDXGISurface1 *This,
        DXGI_MAPPED_RECT *mapped_rect,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *Unmap)(
        IDXGISurface1 *This);

    /*** IDXGISurface1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDC)(
        IDXGISurface1 *This,
        WINBOOL discard,
        HDC *hdc);

    HRESULT (STDMETHODCALLTYPE *ReleaseDC)(
        IDXGISurface1 *This,
        RECT *dirty_rect);

    END_INTERFACE
} IDXGISurface1Vtbl;

interface IDXGISurface1 {
    CONST_VTBL IDXGISurface1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISurface1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISurface1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISurface1_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISurface1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISurface1_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISurface1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISurface1_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISurface1_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISurface methods ***/
#define IDXGISurface1_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISurface1_Map(This,mapped_rect,flags) (This)->lpVtbl->Map(This,mapped_rect,flags)
#define IDXGISurface1_Unmap(This) (This)->lpVtbl->Unmap(This)
/*** IDXGISurface1 methods ***/
#define IDXGISurface1_GetDC(This,discard,hdc) (This)->lpVtbl->GetDC(This,discard,hdc)
#define IDXGISurface1_ReleaseDC(This,dirty_rect) (This)->lpVtbl->ReleaseDC(This,dirty_rect)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGISurface1_QueryInterface(IDXGISurface1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGISurface1_AddRef(IDXGISurface1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGISurface1_Release(IDXGISurface1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGISurface1_SetPrivateData(IDXGISurface1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISurface1_SetPrivateDataInterface(IDXGISurface1* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGISurface1_GetPrivateData(IDXGISurface1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISurface1_GetParent(IDXGISurface1* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGISurface1_GetDevice(IDXGISurface1* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISurface methods ***/
static inline HRESULT IDXGISurface1_GetDesc(IDXGISurface1* This,DXGI_SURFACE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGISurface1_Map(IDXGISurface1* This,DXGI_MAPPED_RECT *mapped_rect,UINT flags) {
    return This->lpVtbl->Map(This,mapped_rect,flags);
}
static inline HRESULT IDXGISurface1_Unmap(IDXGISurface1* This) {
    return This->lpVtbl->Unmap(This);
}
/*** IDXGISurface1 methods ***/
static inline HRESULT IDXGISurface1_GetDC(IDXGISurface1* This,WINBOOL discard,HDC *hdc) {
    return This->lpVtbl->GetDC(This,discard,hdc);
}
static inline HRESULT IDXGISurface1_ReleaseDC(IDXGISurface1* This,RECT *dirty_rect) {
    return This->lpVtbl->ReleaseDC(This,dirty_rect);
}
#endif
#endif

#endif


#endif  /* __IDXGISurface1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIOutput interface
 */
#ifndef __IDXGIOutput_INTERFACE_DEFINED__
#define __IDXGIOutput_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutput, 0xae02eedb, 0xc735, 0x4690, 0x8d,0x52, 0x5a,0x8d,0xc2,0x02,0x13,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae02eedb-c735-4690-8d52-5a8dc20213aa")
IDXGIOutput : public IDXGIObject
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        DXGI_OUTPUT_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayModeList(
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindClosestMatchingMode(
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitForVBlank(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE TakeOwnership(
        IUnknown *device,
        WINBOOL exclusive) = 0;

    virtual void STDMETHODCALLTYPE ReleaseOwnership(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGammaControlCapabilities(
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGammaControl(
        const DXGI_GAMMA_CONTROL *gamma_control) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGammaControl(
        DXGI_GAMMA_CONTROL *gamma_control) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDisplaySurface(
        IDXGISurface *surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplaySurfaceData(
        IDXGISurface *surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrameStatistics(
        DXGI_FRAME_STATISTICS *stats) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutput, 0xae02eedb, 0xc735, 0x4690, 0x8d,0x52, 0x5a,0x8d,0xc2,0x02,0x13,0xaa)
#endif
#else
typedef struct IDXGIOutputVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutput *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutput *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutput *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutput *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutput *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutput *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutput *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutput methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutput *This,
        DXGI_OUTPUT_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList)(
        IDXGIOutput *This,
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode)(
        IDXGIOutput *This,
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device);

    HRESULT (STDMETHODCALLTYPE *WaitForVBlank)(
        IDXGIOutput *This);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IDXGIOutput *This,
        IUnknown *device,
        WINBOOL exclusive);

    void (STDMETHODCALLTYPE *ReleaseOwnership)(
        IDXGIOutput *This);

    HRESULT (STDMETHODCALLTYPE *GetGammaControlCapabilities)(
        IDXGIOutput *This,
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps);

    HRESULT (STDMETHODCALLTYPE *SetGammaControl)(
        IDXGIOutput *This,
        const DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *GetGammaControl)(
        IDXGIOutput *This,
        DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *SetDisplaySurface)(
        IDXGIOutput *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData)(
        IDXGIOutput *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGIOutput *This,
        DXGI_FRAME_STATISTICS *stats);

    END_INTERFACE
} IDXGIOutputVtbl;

interface IDXGIOutput {
    CONST_VTBL IDXGIOutputVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutput_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutput_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutput_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutput_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutput_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutput_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutput_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutput methods ***/
#define IDXGIOutput_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutput_GetDisplayModeList(This,format,flags,mode_count,desc) (This)->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc)
#define IDXGIOutput_FindClosestMatchingMode(This,mode,closest_match,device) (This)->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device)
#define IDXGIOutput_WaitForVBlank(This) (This)->lpVtbl->WaitForVBlank(This)
#define IDXGIOutput_TakeOwnership(This,device,exclusive) (This)->lpVtbl->TakeOwnership(This,device,exclusive)
#define IDXGIOutput_ReleaseOwnership(This) (This)->lpVtbl->ReleaseOwnership(This)
#define IDXGIOutput_GetGammaControlCapabilities(This,gamma_caps) (This)->lpVtbl->GetGammaControlCapabilities(This,gamma_caps)
#define IDXGIOutput_SetGammaControl(This,gamma_control) (This)->lpVtbl->SetGammaControl(This,gamma_control)
#define IDXGIOutput_GetGammaControl(This,gamma_control) (This)->lpVtbl->GetGammaControl(This,gamma_control)
#define IDXGIOutput_SetDisplaySurface(This,surface) (This)->lpVtbl->SetDisplaySurface(This,surface)
#define IDXGIOutput_GetDisplaySurfaceData(This,surface) (This)->lpVtbl->GetDisplaySurfaceData(This,surface)
#define IDXGIOutput_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIOutput_QueryInterface(IDXGIOutput* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIOutput_AddRef(IDXGIOutput* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIOutput_Release(IDXGIOutput* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIOutput_SetPrivateData(IDXGIOutput* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput_SetPrivateDataInterface(IDXGIOutput* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIOutput_GetPrivateData(IDXGIOutput* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput_GetParent(IDXGIOutput* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutput methods ***/
static inline HRESULT IDXGIOutput_GetDesc(IDXGIOutput* This,DXGI_OUTPUT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIOutput_GetDisplayModeList(IDXGIOutput* This,DXGI_FORMAT format,UINT flags,UINT *mode_count,DXGI_MODE_DESC *desc) {
    return This->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc);
}
static inline HRESULT IDXGIOutput_FindClosestMatchingMode(IDXGIOutput* This,const DXGI_MODE_DESC *mode,DXGI_MODE_DESC *closest_match,IUnknown *device) {
    return This->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device);
}
static inline HRESULT IDXGIOutput_WaitForVBlank(IDXGIOutput* This) {
    return This->lpVtbl->WaitForVBlank(This);
}
static inline HRESULT IDXGIOutput_TakeOwnership(IDXGIOutput* This,IUnknown *device,WINBOOL exclusive) {
    return This->lpVtbl->TakeOwnership(This,device,exclusive);
}
static inline void IDXGIOutput_ReleaseOwnership(IDXGIOutput* This) {
    This->lpVtbl->ReleaseOwnership(This);
}
static inline HRESULT IDXGIOutput_GetGammaControlCapabilities(IDXGIOutput* This,DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) {
    return This->lpVtbl->GetGammaControlCapabilities(This,gamma_caps);
}
static inline HRESULT IDXGIOutput_SetGammaControl(IDXGIOutput* This,const DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->SetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput_GetGammaControl(IDXGIOutput* This,DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->GetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput_SetDisplaySurface(IDXGIOutput* This,IDXGISurface *surface) {
    return This->lpVtbl->SetDisplaySurface(This,surface);
}
static inline HRESULT IDXGIOutput_GetDisplaySurfaceData(IDXGIOutput* This,IDXGISurface *surface) {
    return This->lpVtbl->GetDisplaySurfaceData(This,surface);
}
static inline HRESULT IDXGIOutput_GetFrameStatistics(IDXGIOutput* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutput_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIAdapter interface
 */
#ifndef __IDXGIAdapter_INTERFACE_DEFINED__
#define __IDXGIAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIAdapter, 0x2411e7e1, 0x12ac, 0x4ccf, 0xbd,0x14, 0x97,0x98,0xe8,0x53,0x4d,0xc0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2411e7e1-12ac-4ccf-bd14-9798e8534dc0")
IDXGIAdapter : public IDXGIObject
{
    virtual HRESULT STDMETHODCALLTYPE EnumOutputs(
        UINT output_idx,
        IDXGIOutput **output) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        DXGI_ADAPTER_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckInterfaceSupport(
        REFGUID guid,
        LARGE_INTEGER *umd_version) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIAdapter, 0x2411e7e1, 0x12ac, 0x4ccf, 0xbd,0x14, 0x97,0x98,0xe8,0x53,0x4d,0xc0)
#endif
#else
typedef struct IDXGIAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIAdapter *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIAdapter *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIAdapter *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIAdapter *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIAdapter *This,
        REFIID riid,
        void **parent);

    /*** IDXGIAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumOutputs)(
        IDXGIAdapter *This,
        UINT output_idx,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIAdapter *This,
        DXGI_ADAPTER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *CheckInterfaceSupport)(
        IDXGIAdapter *This,
        REFGUID guid,
        LARGE_INTEGER *umd_version);

    END_INTERFACE
} IDXGIAdapterVtbl;

interface IDXGIAdapter {
    CONST_VTBL IDXGIAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIAdapter_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIAdapter_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIAdapter methods ***/
#define IDXGIAdapter_EnumOutputs(This,output_idx,output) (This)->lpVtbl->EnumOutputs(This,output_idx,output)
#define IDXGIAdapter_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIAdapter_CheckInterfaceSupport(This,guid,umd_version) (This)->lpVtbl->CheckInterfaceSupport(This,guid,umd_version)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIAdapter_QueryInterface(IDXGIAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIAdapter_AddRef(IDXGIAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIAdapter_Release(IDXGIAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIAdapter_SetPrivateData(IDXGIAdapter* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter_SetPrivateDataInterface(IDXGIAdapter* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIAdapter_GetPrivateData(IDXGIAdapter* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter_GetParent(IDXGIAdapter* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIAdapter methods ***/
static inline HRESULT IDXGIAdapter_EnumOutputs(IDXGIAdapter* This,UINT output_idx,IDXGIOutput **output) {
    return This->lpVtbl->EnumOutputs(This,output_idx,output);
}
static inline HRESULT IDXGIAdapter_GetDesc(IDXGIAdapter* This,DXGI_ADAPTER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIAdapter_CheckInterfaceSupport(IDXGIAdapter* This,REFGUID guid,LARGE_INTEGER *umd_version) {
    return This->lpVtbl->CheckInterfaceSupport(This,guid,umd_version);
}
#endif
#endif

#endif


#endif  /* __IDXGIAdapter_INTERFACE_DEFINED__ */

#define DXGI_MAX_SWAP_CHAIN_BUFFERS  (16)
#define DXGI_PRESENT_TEST                   __MSABI_LONG(0x00000001U)
#define DXGI_PRESENT_DO_NOT_SEQUENCE        __MSABI_LONG(0x00000002U)
#define DXGI_PRESENT_RESTART                __MSABI_LONG(0x00000004U)
#define DXGI_PRESENT_DO_NOT_WAIT            __MSABI_LONG(0x00000008U)
#define DXGI_PRESENT_STEREO_PREFER_RIGHT    __MSABI_LONG(0x00000010U)
#define DXGI_PRESENT_STEREO_TEMPORARY_MONO  __MSABI_LONG(0x00000020U)
#define DXGI_PRESENT_RESTRICT_TO_OUTPUT     __MSABI_LONG(0x00000040U)
#define DXGI_PRESENT_USE_DURATION           __MSABI_LONG(0x00000100U)
#define DXGI_PRESENT_ALLOW_TEARING          __MSABI_LONG(0x00000200U)
/*****************************************************************************
 * IDXGISwapChain interface
 */
#ifndef __IDXGISwapChain_INTERFACE_DEFINED__
#define __IDXGISwapChain_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISwapChain, 0x310d36a0, 0xd2e7, 0x4c0a, 0xaa,0x04, 0x6a,0x9d,0x23,0xb8,0x88,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("310d36a0-d2e7-4c0a-aa04-6a9d23b8886a")
IDXGISwapChain : public IDXGIDeviceSubObject
{
    virtual HRESULT STDMETHODCALLTYPE Present(
        UINT sync_interval,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBuffer(
        UINT buffer_idx,
        REFIID riid,
        void **surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFullscreenState(
        WINBOOL fullscreen,
        IDXGIOutput *target) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFullscreenState(
        WINBOOL *fullscreen,
        IDXGIOutput **target) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        DXGI_SWAP_CHAIN_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResizeBuffers(
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResizeTarget(
        const DXGI_MODE_DESC *target_mode_desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContainingOutput(
        IDXGIOutput **output) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrameStatistics(
        DXGI_FRAME_STATISTICS *stats) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastPresentCount(
        UINT *last_present_count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISwapChain, 0x310d36a0, 0xd2e7, 0x4c0a, 0xaa,0x04, 0x6a,0x9d,0x23,0xb8,0x88,0x6a)
#endif
#else
typedef struct IDXGISwapChainVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISwapChain *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISwapChain *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISwapChain *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISwapChain *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISwapChain *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISwapChain *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISwapChain *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISwapChain *This,
        REFIID riid,
        void **device);

    /*** IDXGISwapChain methods ***/
    HRESULT (STDMETHODCALLTYPE *Present)(
        IDXGISwapChain *This,
        UINT sync_interval,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IDXGISwapChain *This,
        UINT buffer_idx,
        REFIID riid,
        void **surface);

    HRESULT (STDMETHODCALLTYPE *SetFullscreenState)(
        IDXGISwapChain *This,
        WINBOOL fullscreen,
        IDXGIOutput *target);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenState)(
        IDXGISwapChain *This,
        WINBOOL *fullscreen,
        IDXGIOutput **target);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISwapChain *This,
        DXGI_SWAP_CHAIN_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *ResizeBuffers)(
        IDXGISwapChain *This,
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTarget)(
        IDXGISwapChain *This,
        const DXGI_MODE_DESC *target_mode_desc);

    HRESULT (STDMETHODCALLTYPE *GetContainingOutput)(
        IDXGISwapChain *This,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGISwapChain *This,
        DXGI_FRAME_STATISTICS *stats);

    HRESULT (STDMETHODCALLTYPE *GetLastPresentCount)(
        IDXGISwapChain *This,
        UINT *last_present_count);

    END_INTERFACE
} IDXGISwapChainVtbl;

interface IDXGISwapChain {
    CONST_VTBL IDXGISwapChainVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISwapChain_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISwapChain_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISwapChain_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISwapChain_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISwapChain_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISwapChain_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISwapChain methods ***/
#define IDXGISwapChain_Present(This,sync_interval,flags) (This)->lpVtbl->Present(This,sync_interval,flags)
#define IDXGISwapChain_GetBuffer(This,buffer_idx,riid,surface) (This)->lpVtbl->GetBuffer(This,buffer_idx,riid,surface)
#define IDXGISwapChain_SetFullscreenState(This,fullscreen,target) (This)->lpVtbl->SetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain_GetFullscreenState(This,fullscreen,target) (This)->lpVtbl->GetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISwapChain_ResizeBuffers(This,buffer_count,width,height,format,flags) (This)->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags)
#define IDXGISwapChain_ResizeTarget(This,target_mode_desc) (This)->lpVtbl->ResizeTarget(This,target_mode_desc)
#define IDXGISwapChain_GetContainingOutput(This,output) (This)->lpVtbl->GetContainingOutput(This,output)
#define IDXGISwapChain_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
#define IDXGISwapChain_GetLastPresentCount(This,last_present_count) (This)->lpVtbl->GetLastPresentCount(This,last_present_count)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGISwapChain_QueryInterface(IDXGISwapChain* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGISwapChain_AddRef(IDXGISwapChain* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGISwapChain_Release(IDXGISwapChain* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGISwapChain_SetPrivateData(IDXGISwapChain* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain_SetPrivateDataInterface(IDXGISwapChain* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGISwapChain_GetPrivateData(IDXGISwapChain* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain_GetParent(IDXGISwapChain* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGISwapChain_GetDevice(IDXGISwapChain* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISwapChain methods ***/
static inline HRESULT IDXGISwapChain_Present(IDXGISwapChain* This,UINT sync_interval,UINT flags) {
    return This->lpVtbl->Present(This,sync_interval,flags);
}
static inline HRESULT IDXGISwapChain_GetBuffer(IDXGISwapChain* This,UINT buffer_idx,REFIID riid,void **surface) {
    return This->lpVtbl->GetBuffer(This,buffer_idx,riid,surface);
}
static inline HRESULT IDXGISwapChain_SetFullscreenState(IDXGISwapChain* This,WINBOOL fullscreen,IDXGIOutput *target) {
    return This->lpVtbl->SetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain_GetFullscreenState(IDXGISwapChain* This,WINBOOL *fullscreen,IDXGIOutput **target) {
    return This->lpVtbl->GetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain_GetDesc(IDXGISwapChain* This,DXGI_SWAP_CHAIN_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGISwapChain_ResizeBuffers(IDXGISwapChain* This,UINT buffer_count,UINT width,UINT height,DXGI_FORMAT format,UINT flags) {
    return This->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags);
}
static inline HRESULT IDXGISwapChain_ResizeTarget(IDXGISwapChain* This,const DXGI_MODE_DESC *target_mode_desc) {
    return This->lpVtbl->ResizeTarget(This,target_mode_desc);
}
static inline HRESULT IDXGISwapChain_GetContainingOutput(IDXGISwapChain* This,IDXGIOutput **output) {
    return This->lpVtbl->GetContainingOutput(This,output);
}
static inline HRESULT IDXGISwapChain_GetFrameStatistics(IDXGISwapChain* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
static inline HRESULT IDXGISwapChain_GetLastPresentCount(IDXGISwapChain* This,UINT *last_present_count) {
    return This->lpVtbl->GetLastPresentCount(This,last_present_count);
}
#endif
#endif

#endif


#endif  /* __IDXGISwapChain_INTERFACE_DEFINED__ */

#define DXGI_MWA_NO_WINDOW_CHANGES  0x1
#define DXGI_MWA_NO_ALT_ENTER       0x2
#define DXGI_MWA_NO_PRINT_SCREEN    0x4
#define DXGI_MWA_VALID              0x7
/*****************************************************************************
 * IDXGIFactory interface
 */
#ifndef __IDXGIFactory_INTERFACE_DEFINED__
#define __IDXGIFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory, 0x7b7166ec, 0x21c7, 0x44ae, 0xb2,0x1a, 0xc9,0xae,0x32,0x1a,0xe3,0x69);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7b7166ec-21c7-44ae-b21a-c9ae321ae369")
IDXGIFactory : public IDXGIObject
{
    virtual HRESULT STDMETHODCALLTYPE EnumAdapters(
        UINT adapter_idx,
        IDXGIAdapter **adapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE MakeWindowAssociation(
        HWND window,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWindowAssociation(
        HWND *window) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSwapChain(
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSoftwareAdapter(
        HMODULE swrast,
        IDXGIAdapter **adapter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory, 0x7b7166ec, 0x21c7, 0x44ae, 0xb2,0x1a, 0xc9,0xae,0x32,0x1a,0xe3,0x69)
#endif
#else
typedef struct IDXGIFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    END_INTERFACE
} IDXGIFactoryVtbl;

interface IDXGIFactory {
    CONST_VTBL IDXGIFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactory_QueryInterface(IDXGIFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactory_AddRef(IDXGIFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactory_Release(IDXGIFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIFactory_SetPrivateData(IDXGIFactory* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory_SetPrivateDataInterface(IDXGIFactory* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIFactory_GetPrivateData(IDXGIFactory* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory_GetParent(IDXGIFactory* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static inline HRESULT IDXGIFactory_EnumAdapters(IDXGIFactory* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static inline HRESULT IDXGIFactory_MakeWindowAssociation(IDXGIFactory* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static inline HRESULT IDXGIFactory_GetWindowAssociation(IDXGIFactory* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static inline HRESULT IDXGIFactory_CreateSwapChain(IDXGIFactory* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static inline HRESULT IDXGIFactory_CreateSoftwareAdapter(IDXGIFactory* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory_INTERFACE_DEFINED__ */

HRESULT __stdcall  CreateDXGIFactory(REFIID riid,void **factory);

HRESULT __stdcall  CreateDXGIFactory1(REFIID riid,void **factory);

/*****************************************************************************
 * IDXGIDevice interface
 */
#ifndef __IDXGIDevice_INTERFACE_DEFINED__
#define __IDXGIDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDevice, 0x54ec77fa, 0x1377, 0x44e6, 0x8c,0x32, 0x88,0xfd,0x5f,0x44,0xc8,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("54ec77fa-1377-44e6-8c32-88fd5f44c84c")
IDXGIDevice : public IDXGIObject
{
    virtual HRESULT STDMETHODCALLTYPE GetAdapter(
        IDXGIAdapter **adapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSurface(
        const DXGI_SURFACE_DESC *desc,
        UINT surface_count,
        DXGI_USAGE usage,
        const DXGI_SHARED_RESOURCE *shared_resource,
        IDXGISurface **surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryResourceResidency(
        IUnknown *const *resources,
        DXGI_RESIDENCY *residency,
        UINT resource_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGPUThreadPriority(
        INT priority) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGPUThreadPriority(
        INT *priority) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDevice, 0x54ec77fa, 0x1377, 0x44e6, 0x8c,0x32, 0x88,0xfd,0x5f,0x44,0xc8,0x4c)
#endif
#else
typedef struct IDXGIDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDevice *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIDevice *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIDevice *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIDevice *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIDevice *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAdapter)(
        IDXGIDevice *This,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *CreateSurface)(
        IDXGIDevice *This,
        const DXGI_SURFACE_DESC *desc,
        UINT surface_count,
        DXGI_USAGE usage,
        const DXGI_SHARED_RESOURCE *shared_resource,
        IDXGISurface **surface);

    HRESULT (STDMETHODCALLTYPE *QueryResourceResidency)(
        IDXGIDevice *This,
        IUnknown *const *resources,
        DXGI_RESIDENCY *residency,
        UINT resource_count);

    HRESULT (STDMETHODCALLTYPE *SetGPUThreadPriority)(
        IDXGIDevice *This,
        INT priority);

    HRESULT (STDMETHODCALLTYPE *GetGPUThreadPriority)(
        IDXGIDevice *This,
        INT *priority);

    END_INTERFACE
} IDXGIDeviceVtbl;

interface IDXGIDevice {
    CONST_VTBL IDXGIDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIDevice_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIDevice_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIDevice_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIDevice_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDevice methods ***/
#define IDXGIDevice_GetAdapter(This,adapter) (This)->lpVtbl->GetAdapter(This,adapter)
#define IDXGIDevice_CreateSurface(This,desc,surface_count,usage,shared_resource,surface) (This)->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface)
#define IDXGIDevice_QueryResourceResidency(This,resources,residency,resource_count) (This)->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count)
#define IDXGIDevice_SetGPUThreadPriority(This,priority) (This)->lpVtbl->SetGPUThreadPriority(This,priority)
#define IDXGIDevice_GetGPUThreadPriority(This,priority) (This)->lpVtbl->GetGPUThreadPriority(This,priority)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIDevice_QueryInterface(IDXGIDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIDevice_AddRef(IDXGIDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIDevice_Release(IDXGIDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIDevice_SetPrivateData(IDXGIDevice* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice_SetPrivateDataInterface(IDXGIDevice* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIDevice_GetPrivateData(IDXGIDevice* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice_GetParent(IDXGIDevice* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDevice methods ***/
static inline HRESULT IDXGIDevice_GetAdapter(IDXGIDevice* This,IDXGIAdapter **adapter) {
    return This->lpVtbl->GetAdapter(This,adapter);
}
static inline HRESULT IDXGIDevice_CreateSurface(IDXGIDevice* This,const DXGI_SURFACE_DESC *desc,UINT surface_count,DXGI_USAGE usage,const DXGI_SHARED_RESOURCE *shared_resource,IDXGISurface **surface) {
    return This->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface);
}
static inline HRESULT IDXGIDevice_QueryResourceResidency(IDXGIDevice* This,IUnknown *const *resources,DXGI_RESIDENCY *residency,UINT resource_count) {
    return This->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count);
}
static inline HRESULT IDXGIDevice_SetGPUThreadPriority(IDXGIDevice* This,INT priority) {
    return This->lpVtbl->SetGPUThreadPriority(This,priority);
}
static inline HRESULT IDXGIDevice_GetGPUThreadPriority(IDXGIDevice* This,INT *priority) {
    return This->lpVtbl->GetGPUThreadPriority(This,priority);
}
#endif
#endif

#endif


#endif  /* __IDXGIDevice_INTERFACE_DEFINED__ */

typedef enum DXGI_ADAPTER_FLAG {
    DXGI_ADAPTER_FLAG_NONE = 0,
    DXGI_ADAPTER_FLAG_REMOTE = 1,
    DXGI_ADAPTER_FLAG_SOFTWARE = 2,
    DXGI_ADAPTER_FLAG_FORCE_DWORD = 0xffffffff
} DXGI_ADAPTER_FLAG;
typedef struct DXGI_ADAPTER_DESC1 {
    WCHAR Description[128];
    UINT VendorId;
    UINT DeviceId;
    UINT SubSysId;
    UINT Revision;
    SIZE_T DedicatedVideoMemory;
    SIZE_T DedicatedSystemMemory;
    SIZE_T SharedSystemMemory;
    LUID AdapterLuid;
    UINT Flags;
} DXGI_ADAPTER_DESC1;
typedef struct DXGI_DISPLAY_COLOR_SPACE {
    FLOAT PrimaryCoordinates[8][2];
    FLOAT WhitePoints[16][2];
} DXGI_DISPLAY_COLOR_SPACE;
/*****************************************************************************
 * IDXGIAdapter1 interface
 */
#ifndef __IDXGIAdapter1_INTERFACE_DEFINED__
#define __IDXGIAdapter1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIAdapter1, 0x29038f61, 0x3839, 0x4626, 0x91,0xfd, 0x08,0x68,0x79,0x01,0x1a,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("29038f61-3839-4626-91fd-086879011a05")
IDXGIAdapter1 : public IDXGIAdapter
{
    virtual HRESULT STDMETHODCALLTYPE GetDesc1(
        DXGI_ADAPTER_DESC1 *pDesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIAdapter1, 0x29038f61, 0x3839, 0x4626, 0x91,0xfd, 0x08,0x68,0x79,0x01,0x1a,0x05)
#endif
#else
typedef struct IDXGIAdapter1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIAdapter1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIAdapter1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIAdapter1 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIAdapter1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIAdapter1 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIAdapter1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIAdapter1 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumOutputs)(
        IDXGIAdapter1 *This,
        UINT output_idx,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIAdapter1 *This,
        DXGI_ADAPTER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *CheckInterfaceSupport)(
        IDXGIAdapter1 *This,
        REFGUID guid,
        LARGE_INTEGER *umd_version);

    /*** IDXGIAdapter1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGIAdapter1 *This,
        DXGI_ADAPTER_DESC1 *pDesc);

    END_INTERFACE
} IDXGIAdapter1Vtbl;

interface IDXGIAdapter1 {
    CONST_VTBL IDXGIAdapter1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIAdapter1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIAdapter1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIAdapter1_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIAdapter1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter1_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIAdapter1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter1_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIAdapter methods ***/
#define IDXGIAdapter1_EnumOutputs(This,output_idx,output) (This)->lpVtbl->EnumOutputs(This,output_idx,output)
#define IDXGIAdapter1_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIAdapter1_CheckInterfaceSupport(This,guid,umd_version) (This)->lpVtbl->CheckInterfaceSupport(This,guid,umd_version)
/*** IDXGIAdapter1 methods ***/
#define IDXGIAdapter1_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIAdapter1_QueryInterface(IDXGIAdapter1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIAdapter1_AddRef(IDXGIAdapter1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIAdapter1_Release(IDXGIAdapter1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIAdapter1_SetPrivateData(IDXGIAdapter1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter1_SetPrivateDataInterface(IDXGIAdapter1* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIAdapter1_GetPrivateData(IDXGIAdapter1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter1_GetParent(IDXGIAdapter1* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIAdapter methods ***/
static inline HRESULT IDXGIAdapter1_EnumOutputs(IDXGIAdapter1* This,UINT output_idx,IDXGIOutput **output) {
    return This->lpVtbl->EnumOutputs(This,output_idx,output);
}
static inline HRESULT IDXGIAdapter1_GetDesc(IDXGIAdapter1* This,DXGI_ADAPTER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIAdapter1_CheckInterfaceSupport(IDXGIAdapter1* This,REFGUID guid,LARGE_INTEGER *umd_version) {
    return This->lpVtbl->CheckInterfaceSupport(This,guid,umd_version);
}
/*** IDXGIAdapter1 methods ***/
static inline HRESULT IDXGIAdapter1_GetDesc1(IDXGIAdapter1* This,DXGI_ADAPTER_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
#endif
#endif

#endif


#endif  /* __IDXGIAdapter1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIDevice1 interface
 */
#ifndef __IDXGIDevice1_INTERFACE_DEFINED__
#define __IDXGIDevice1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDevice1, 0x77db970f, 0x6276, 0x48ba, 0xba,0x28, 0x07,0x01,0x43,0xb4,0x39,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("77db970f-6276-48ba-ba28-070143b4392c")
IDXGIDevice1 : public IDXGIDevice
{
    virtual HRESULT STDMETHODCALLTYPE SetMaximumFrameLatency(
        UINT MaxLatency) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaximumFrameLatency(
        UINT *pMaxLatency) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDevice1, 0x77db970f, 0x6276, 0x48ba, 0xba,0x28, 0x07,0x01,0x43,0xb4,0x39,0x2c)
#endif
#else
typedef struct IDXGIDevice1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDevice1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDevice1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDevice1 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIDevice1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIDevice1 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIDevice1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIDevice1 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAdapter)(
        IDXGIDevice1 *This,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *CreateSurface)(
        IDXGIDevice1 *This,
        const DXGI_SURFACE_DESC *desc,
        UINT surface_count,
        DXGI_USAGE usage,
        const DXGI_SHARED_RESOURCE *shared_resource,
        IDXGISurface **surface);

    HRESULT (STDMETHODCALLTYPE *QueryResourceResidency)(
        IDXGIDevice1 *This,
        IUnknown *const *resources,
        DXGI_RESIDENCY *residency,
        UINT resource_count);

    HRESULT (STDMETHODCALLTYPE *SetGPUThreadPriority)(
        IDXGIDevice1 *This,
        INT priority);

    HRESULT (STDMETHODCALLTYPE *GetGPUThreadPriority)(
        IDXGIDevice1 *This,
        INT *priority);

    /*** IDXGIDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMaximumFrameLatency)(
        IDXGIDevice1 *This,
        UINT MaxLatency);

    HRESULT (STDMETHODCALLTYPE *GetMaximumFrameLatency)(
        IDXGIDevice1 *This,
        UINT *pMaxLatency);

    END_INTERFACE
} IDXGIDevice1Vtbl;

interface IDXGIDevice1 {
    CONST_VTBL IDXGIDevice1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDevice1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDevice1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDevice1_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIDevice1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIDevice1_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIDevice1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIDevice1_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDevice methods ***/
#define IDXGIDevice1_GetAdapter(This,adapter) (This)->lpVtbl->GetAdapter(This,adapter)
#define IDXGIDevice1_CreateSurface(This,desc,surface_count,usage,shared_resource,surface) (This)->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface)
#define IDXGIDevice1_QueryResourceResidency(This,resources,residency,resource_count) (This)->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count)
#define IDXGIDevice1_SetGPUThreadPriority(This,priority) (This)->lpVtbl->SetGPUThreadPriority(This,priority)
#define IDXGIDevice1_GetGPUThreadPriority(This,priority) (This)->lpVtbl->GetGPUThreadPriority(This,priority)
/*** IDXGIDevice1 methods ***/
#define IDXGIDevice1_SetMaximumFrameLatency(This,MaxLatency) (This)->lpVtbl->SetMaximumFrameLatency(This,MaxLatency)
#define IDXGIDevice1_GetMaximumFrameLatency(This,pMaxLatency) (This)->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIDevice1_QueryInterface(IDXGIDevice1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIDevice1_AddRef(IDXGIDevice1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIDevice1_Release(IDXGIDevice1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIDevice1_SetPrivateData(IDXGIDevice1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice1_SetPrivateDataInterface(IDXGIDevice1* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIDevice1_GetPrivateData(IDXGIDevice1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice1_GetParent(IDXGIDevice1* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDevice methods ***/
static inline HRESULT IDXGIDevice1_GetAdapter(IDXGIDevice1* This,IDXGIAdapter **adapter) {
    return This->lpVtbl->GetAdapter(This,adapter);
}
static inline HRESULT IDXGIDevice1_CreateSurface(IDXGIDevice1* This,const DXGI_SURFACE_DESC *desc,UINT surface_count,DXGI_USAGE usage,const DXGI_SHARED_RESOURCE *shared_resource,IDXGISurface **surface) {
    return This->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface);
}
static inline HRESULT IDXGIDevice1_QueryResourceResidency(IDXGIDevice1* This,IUnknown *const *resources,DXGI_RESIDENCY *residency,UINT resource_count) {
    return This->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count);
}
static inline HRESULT IDXGIDevice1_SetGPUThreadPriority(IDXGIDevice1* This,INT priority) {
    return This->lpVtbl->SetGPUThreadPriority(This,priority);
}
static inline HRESULT IDXGIDevice1_GetGPUThreadPriority(IDXGIDevice1* This,INT *priority) {
    return This->lpVtbl->GetGPUThreadPriority(This,priority);
}
/*** IDXGIDevice1 methods ***/
static inline HRESULT IDXGIDevice1_SetMaximumFrameLatency(IDXGIDevice1* This,UINT MaxLatency) {
    return This->lpVtbl->SetMaximumFrameLatency(This,MaxLatency);
}
static inline HRESULT IDXGIDevice1_GetMaximumFrameLatency(IDXGIDevice1* This,UINT *pMaxLatency) {
    return This->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency);
}
#endif
#endif

#endif


#endif  /* __IDXGIDevice1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactory1 interface
 */
#ifndef __IDXGIFactory1_INTERFACE_DEFINED__
#define __IDXGIFactory1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory1, 0x770aae78, 0xf26f, 0x4dba, 0xa8,0x29, 0x25,0x3c,0x83,0xd1,0xb3,0x87);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("770aae78-f26f-4dba-a829-253c83d1b387")
IDXGIFactory1 : public IDXGIFactory
{
    virtual HRESULT STDMETHODCALLTYPE EnumAdapters1(
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsCurrent(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory1, 0x770aae78, 0xf26f, 0x4dba, 0xa8,0x29, 0x25,0x3c,0x83,0xd1,0xb3,0x87)
#endif
#else
typedef struct IDXGIFactory1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory1 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory1 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory1 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory1 *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory1 *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory1 *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory1 *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory1 *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    /*** IDXGIFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters1)(
        IDXGIFactory1 *This,
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter);

    WINBOOL (STDMETHODCALLTYPE *IsCurrent)(
        IDXGIFactory1 *This);

    END_INTERFACE
} IDXGIFactory1Vtbl;

interface IDXGIFactory1 {
    CONST_VTBL IDXGIFactory1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory1_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory1_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory1_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory1_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory1_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory1_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory1_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory1_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
/*** IDXGIFactory1 methods ***/
#define IDXGIFactory1_EnumAdapters1(This,Adapter,ppAdapter) (This)->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter)
#define IDXGIFactory1_IsCurrent(This) (This)->lpVtbl->IsCurrent(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactory1_QueryInterface(IDXGIFactory1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactory1_AddRef(IDXGIFactory1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactory1_Release(IDXGIFactory1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIFactory1_SetPrivateData(IDXGIFactory1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory1_SetPrivateDataInterface(IDXGIFactory1* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIFactory1_GetPrivateData(IDXGIFactory1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory1_GetParent(IDXGIFactory1* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static inline HRESULT IDXGIFactory1_EnumAdapters(IDXGIFactory1* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static inline HRESULT IDXGIFactory1_MakeWindowAssociation(IDXGIFactory1* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static inline HRESULT IDXGIFactory1_GetWindowAssociation(IDXGIFactory1* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static inline HRESULT IDXGIFactory1_CreateSwapChain(IDXGIFactory1* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static inline HRESULT IDXGIFactory1_CreateSoftwareAdapter(IDXGIFactory1* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
/*** IDXGIFactory1 methods ***/
static inline HRESULT IDXGIFactory1_EnumAdapters1(IDXGIFactory1* This,UINT Adapter,IDXGIAdapter1 **ppAdapter) {
    return This->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter);
}
static inline WINBOOL IDXGIFactory1_IsCurrent(IDXGIFactory1* This) {
    return This->lpVtbl->IsCurrent(This);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory1_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dxgi_h__ */
