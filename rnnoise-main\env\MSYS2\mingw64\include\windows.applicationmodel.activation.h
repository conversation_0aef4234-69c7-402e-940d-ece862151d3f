/*** Autogenerated by WIDL 10.12 from include/windows.applicationmodel.activation.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_applicationmodel_activation_h__
#define __windows_applicationmodel_activation_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                interface IActivatedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs ABI::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                interface IBackgroundActivatedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen ABI::Windows::ApplicationModel::Activation::ISplashScreen
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                interface ISplashScreen;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CBackgroundActivatedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                class BackgroundActivatedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CActivation_CBackgroundActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CBackgroundActivatedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CActivation_CBackgroundActivatedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CSplashScreen_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CSplashScreen_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                class SplashScreen;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CActivation_CSplashScreen __x_ABI_CWindows_CApplicationModel_CActivation_CSplashScreen;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CActivation_CSplashScreen_FWD_DEFINED__ */

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Activation::SplashScreen*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.applicationmodel.background.h>
#include <windows.applicationmodel.core.h>
#include <windows.storage.h>
#include <windows.system.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance ABI::Windows::ApplicationModel::Background::IBackgroundTaskInstance
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskInstance;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind_ENUM_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                enum ActivationKind {
                    ActivationKind_Launch = 0,
                    ActivationKind_Search = 1,
                    ActivationKind_ShareTarget = 2,
                    ActivationKind_File = 3,
                    ActivationKind_Protocol = 4,
                    ActivationKind_FileOpenPicker = 5,
                    ActivationKind_FileSavePicker = 6,
                    ActivationKind_CachedFileUpdater = 7,
                    ActivationKind_ContactPicker = 8,
                    ActivationKind_Device = 9,
                    ActivationKind_PrintTaskSettings = 10,
                    ActivationKind_CameraSettings = 11,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_RestrictedLaunch = 12,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_AppointmentsProvider = 13,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_Contact = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_LockScreenCall = 15,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_VoiceCommand = 16,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_LockScreen = 17,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_PickerReturned = 1000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_WalletAction = 1001,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_PickFileContinuation = 1002,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_PickSaveFileContinuation = 1003,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_PickFolderContinuation = 1004,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_WebAuthenticationBrokerContinuation = 1005,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_WebAccountProvider = 1006,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_ComponentUI = 1007,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_ProtocolForResults = 1009,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_ToastNotification = 1010,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
                    ActivationKind_Print3DWorkflow = 1011,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    ActivationKind_DialReceiver = 1012,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
                    ActivationKind_DevicePairing = 1013,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    ActivationKind_UserDataAccountsProvider = 1014,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    ActivationKind_FilePickerExperience = 1015,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
                    ActivationKind_LockScreenComponent = 1016,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
                    ActivationKind_ContactPanel = 1017,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
                    ActivationKind_PrintWorkflowForegroundTask = 1018,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
                    ActivationKind_GameUIProvider = 1019,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
                    ActivationKind_StartupTask = 1020,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
                    ActivationKind_CommandLineLaunch = 1021,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
                    ActivationKind_BarcodeScannerProvider = 1022
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind {
    ActivationKind_Launch = 0,
    ActivationKind_Search = 1,
    ActivationKind_ShareTarget = 2,
    ActivationKind_File = 3,
    ActivationKind_Protocol = 4,
    ActivationKind_FileOpenPicker = 5,
    ActivationKind_FileSavePicker = 6,
    ActivationKind_CachedFileUpdater = 7,
    ActivationKind_ContactPicker = 8,
    ActivationKind_Device = 9,
    ActivationKind_PrintTaskSettings = 10,
    ActivationKind_CameraSettings = 11,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_RestrictedLaunch = 12,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_AppointmentsProvider = 13,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_Contact = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_LockScreenCall = 15,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_VoiceCommand = 16,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_LockScreen = 17,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_PickerReturned = 1000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_WalletAction = 1001,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_PickFileContinuation = 1002,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_PickSaveFileContinuation = 1003,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_PickFolderContinuation = 1004,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_WebAuthenticationBrokerContinuation = 1005,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_WebAccountProvider = 1006,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_ComponentUI = 1007,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_ProtocolForResults = 1009,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_ToastNotification = 1010,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
    ActivationKind_Print3DWorkflow = 1011,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    ActivationKind_DialReceiver = 1012,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
    ActivationKind_DevicePairing = 1013,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    ActivationKind_UserDataAccountsProvider = 1014,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    ActivationKind_FilePickerExperience = 1015,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
    ActivationKind_LockScreenComponent = 1016,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
    ActivationKind_ContactPanel = 1017,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
    ActivationKind_PrintWorkflowForegroundTask = 1018,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
    ActivationKind_GameUIProvider = 1019,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
    ActivationKind_StartupTask = 1020,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
    ActivationKind_CommandLineLaunch = 1021,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
    ActivationKind_BarcodeScannerProvider = 1022
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
};
#ifdef WIDL_using_Windows_ApplicationModel_Activation
#define ActivationKind __x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind
#endif /* WIDL_using_Windows_ApplicationModel_Activation */
#endif

#endif /* ____x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind __x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState_ENUM_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                enum ApplicationExecutionState {
                    ApplicationExecutionState_NotRunning = 0,
                    ApplicationExecutionState_Running = 1,
                    ApplicationExecutionState_Suspended = 2,
                    ApplicationExecutionState_Terminated = 3,
                    ApplicationExecutionState_ClosedByUser = 4
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState {
    ApplicationExecutionState_NotRunning = 0,
    ApplicationExecutionState_Running = 1,
    ApplicationExecutionState_Suspended = 2,
    ApplicationExecutionState_Terminated = 3,
    ApplicationExecutionState_ClosedByUser = 4
};
#ifdef WIDL_using_Windows_ApplicationModel_Activation
#define ApplicationExecutionState __x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState
#endif /* WIDL_using_Windows_ApplicationModel_Activation */
#endif

#endif /* ____x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState __x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                interface IActivatedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs ABI::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                interface IBackgroundActivatedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen ABI::Windows::ApplicationModel::Activation::ISplashScreen
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                interface ISplashScreen;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IActivatedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs, 0xcf651713, 0xcd08, 0x4fd8, 0xb6,0x97, 0xa2,0x81,0xb6,0x54,0x4e,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                MIDL_INTERFACE("cf651713-cd08-4fd8-b697-a281b6544e2e")
                IActivatedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Kind(
                        ABI::Windows::ApplicationModel::Activation::ActivationKind *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PreviousExecutionState(
                        ABI::Windows::ApplicationModel::Activation::ApplicationExecutionState *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SplashScreen(
                        ABI::Windows::ApplicationModel::Activation::ISplashScreen **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs, 0xcf651713, 0xcd08, 0x4fd8, 0xb6,0x97, 0xa2,0x81,0xb6,0x54,0x4e,0x2e)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IActivatedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Kind)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind *value);

    HRESULT (STDMETHODCALLTYPE *get_PreviousExecutionState)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState *value);

    HRESULT (STDMETHODCALLTYPE *get_SplashScreen)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IActivatedEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_Kind(This,value) (This)->lpVtbl->get_Kind(This,value)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_PreviousExecutionState(This,value) (This)->lpVtbl->get_PreviousExecutionState(This,value)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_SplashScreen(This,value) (This)->lpVtbl->get_SplashScreen(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IActivatedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_Kind(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CActivation_CActivationKind *value) {
    return This->lpVtbl->get_Kind(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_PreviousExecutionState(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CActivation_CApplicationExecutionState *value) {
    return This->lpVtbl->get_PreviousExecutionState(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_SplashScreen(__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen **value) {
    return This->lpVtbl->get_SplashScreen(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Activation
#define IID_IActivatedEventArgs IID___x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs
#define IActivatedEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgsVtbl
#define IActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs
#define IActivatedEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_QueryInterface
#define IActivatedEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_AddRef
#define IActivatedEventArgs_Release __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_Release
#define IActivatedEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetIids
#define IActivatedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetRuntimeClassName
#define IActivatedEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_GetTrustLevel
#define IActivatedEventArgs_get_Kind __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_Kind
#define IActivatedEventArgs_get_PreviousExecutionState __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_PreviousExecutionState
#define IActivatedEventArgs_get_SplashScreen __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_get_SplashScreen
#endif /* WIDL_using_Windows_ApplicationModel_Activation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundActivatedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs, 0xab14bee0, 0xe760, 0x440e, 0xa9,0x1c, 0x44,0x79,0x6d,0xe3,0xa9,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                MIDL_INTERFACE("ab14bee0-e760-440e-a91c-44796de3a92d")
                IBackgroundActivatedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_TaskInstance(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskInstance **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs, 0xab14bee0, 0xe760, 0x440e, 0xa9,0x1c, 0x44,0x79,0x6d,0xe3,0xa9,0x2d)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IBackgroundActivatedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TaskInstance)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundActivatedEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_get_TaskInstance(This,value) (This)->lpVtbl->get_TaskInstance(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundActivatedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_get_TaskInstance(__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance **value) {
    return This->lpVtbl->get_TaskInstance(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Activation
#define IID_IBackgroundActivatedEventArgs IID___x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs
#define IBackgroundActivatedEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgsVtbl
#define IBackgroundActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs
#define IBackgroundActivatedEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_QueryInterface
#define IBackgroundActivatedEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_AddRef
#define IBackgroundActivatedEventArgs_Release __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_Release
#define IBackgroundActivatedEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetIids
#define IBackgroundActivatedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetRuntimeClassName
#define IBackgroundActivatedEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_GetTrustLevel
#define IBackgroundActivatedEventArgs_get_TaskInstance __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_get_TaskInstance
#endif /* WIDL_using_Windows_ApplicationModel_Activation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * ISplashScreen interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen, 0xca4d975c, 0xd4d6, 0x43f0, 0x97,0xc0, 0x08,0x33,0xc6,0x39,0x1c,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                MIDL_INTERFACE("ca4d975c-d4d6-43f0-97c0-0833c6391c24")
                ISplashScreen : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ImageLocation(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Dismissed(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Activation::SplashScreen*,IInspectable* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Dismissed(
                        EventRegistrationToken cookie) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen, 0xca4d975c, 0xd4d6, 0x43f0, 0x97,0xc0, 0x08,0x33,0xc6,0x39,0x1c,0x24)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreenVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This,
        TrustLevel *trustLevel);

    /*** ISplashScreen methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ImageLocation)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *add_Dismissed)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This,
        __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_Dismissed)(
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *This,
        EventRegistrationToken cookie);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreenVtbl;

interface __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreenVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISplashScreen methods ***/
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_get_ImageLocation(This,value) (This)->lpVtbl->get_ImageLocation(This,value)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_add_Dismissed(This,handler,cookie) (This)->lpVtbl->add_Dismissed(This,handler,cookie)
#define __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_remove_Dismissed(This,cookie) (This)->lpVtbl->remove_Dismissed(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_QueryInterface(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_AddRef(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_Release(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetIids(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISplashScreen methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_get_ImageLocation(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_ImageLocation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_add_Dismissed(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This,__FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_Dismissed(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_remove_Dismissed(__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_Dismissed(This,cookie);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Activation
#define IID_ISplashScreen IID___x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen
#define ISplashScreenVtbl __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreenVtbl
#define ISplashScreen __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen
#define ISplashScreen_QueryInterface __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_QueryInterface
#define ISplashScreen_AddRef __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_AddRef
#define ISplashScreen_Release __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_Release
#define ISplashScreen_GetIids __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetIids
#define ISplashScreen_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetRuntimeClassName
#define ISplashScreen_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_GetTrustLevel
#define ISplashScreen_get_ImageLocation __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_get_ImageLocation
#define ISplashScreen_add_Dismissed __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_add_Dismissed
#define ISplashScreen_remove_Dismissed __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_remove_Dismissed
#endif /* WIDL_using_Windows_ApplicationModel_Activation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Activation.BackgroundActivatedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Activation_BackgroundActivatedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Activation_BackgroundActivatedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Activation_BackgroundActivatedEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','c','t','i','v','a','t','i','o','n','.','B','a','c','k','g','r','o','u','n','d','A','c','t','i','v','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Activation_BackgroundActivatedEventArgs[] = L"Windows.ApplicationModel.Activation.BackgroundActivatedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Activation_BackgroundActivatedEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','c','t','i','v','a','t','i','o','n','.','B','a','c','k','g','r','o','u','n','d','A','c','t','i','v','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Activation_BackgroundActivatedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.ApplicationModel.Activation.SplashScreen
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Activation_SplashScreen_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Activation_SplashScreen_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Activation_SplashScreen[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','c','t','i','v','a','t','i','o','n','.','S','p','l','a','s','h','S','c','r','e','e','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Activation_SplashScreen[] = L"Windows.ApplicationModel.Activation.SplashScreen";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Activation_SplashScreen[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','c','t','i','v','a','t','i','o','n','.','S','p','l','a','s','h','S','c','r','e','e','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Activation_SplashScreen_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0x49a07732, 0xe7b8, 0x5c5b, 0x9d,0xe7, 0x22,0xe3,0x3c,0xb9,0x70,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("49a07732-e7b8-5c5b-9de7-22e33cb97004")
            IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs*, ABI::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0x49a07732, 0xe7b8, 0x5c5b, 0x9d,0xe7, 0x22,0xe3,0x3c,0xb9,0x70,0x04)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_BackgroundActivatedEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define IEventHandler_BackgroundActivatedEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl
#define IEventHandler_BackgroundActivatedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define IEventHandler_BackgroundActivatedEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface
#define IEventHandler_BackgroundActivatedEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef
#define IEventHandler_BackgroundActivatedEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release
#define IEventHandler_BackgroundActivatedEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::ApplicationModel::Activation::SplashScreen*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable, 0x7725b2a5, 0x287d, 0x5ed2, 0xa7,0x89, 0x2a,0x6a,0x26,0x73,0xc7,0xfe);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("7725b2a5-287d-5ed2-a789-2a6a2673c7fe")
            ITypedEventHandler<ABI::Windows::ApplicationModel::Activation::SplashScreen*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Activation::SplashScreen*, ABI::Windows::ApplicationModel::Activation::ISplashScreen* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable, 0x7725b2a5, 0x287d, 0x5ed2, 0xa7,0x89, 0x2a,0x6a,0x26,0x73,0xc7,0xfe)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::ApplicationModel::Activation::SplashScreen*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable *This,
        __x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Activation::SplashScreen*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_Release(__FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Activation::SplashScreen*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable* This,__x_ABI_CWindows_CApplicationModel_CActivation_CISplashScreen *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SplashScreen_IInspectable IID___FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable
#define ITypedEventHandler_SplashScreen_IInspectableVtbl __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectableVtbl
#define ITypedEventHandler_SplashScreen_IInspectable __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable
#define ITypedEventHandler_SplashScreen_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_QueryInterface
#define ITypedEventHandler_SplashScreen_IInspectable_AddRef __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_AddRef
#define ITypedEventHandler_SplashScreen_IInspectable_Release __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_Release
#define ITypedEventHandler_SplashScreen_IInspectable_Invoke __FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CApplicationModel__CActivation__CSplashScreen_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0xd4f89768, 0x688f, 0x59ec, 0xbf,0x24, 0xc2,0xaf,0x6a,0x31,0x0f,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d4f89768-688f-59ec-bf24-c2af6a310fa4")
            ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*, ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs*, ABI::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0xd4f89768, 0x688f, 0x59ec, 0xbf,0x24, 0xc2,0xaf,0x6a,0x31,0x0f,0xa4)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *sender,
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *sender,__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs IID___FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgsVtbl __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_AddRef __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_Release __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_Invoke __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs, 0xcf193a96, 0xeb13, 0x5e3b, 0x8b,0xdf, 0x87,0xb6,0xef,0xae,0x83,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("cf193a96-eb13-5e3b-8bdf-87b6efae8339")
            ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::CoreApplicationView*, ABI::Windows::ApplicationModel::Core::ICoreApplicationView* >, ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs, 0xcf193a96, 0xeb13, 0x5e3b, 0x8b,0xdf, 0x87,0xb6,0xef,0xae,0x83,0x39)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *sender,
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Release(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *sender,__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreApplicationView_IActivatedEventArgs IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgsVtbl __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_QueryInterface
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_AddRef __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_AddRef
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_Release __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Release
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_Invoke __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_applicationmodel_activation_h__ */
