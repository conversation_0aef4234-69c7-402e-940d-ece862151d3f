/*** Autogenerated by WIDL 10.12 from include/devicetopology.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __devicetopology_h__
#define __devicetopology_h__

/* Forward declarations */

#ifndef __IKsControl_FWD_DEFINED__
#define __IKsControl_FWD_DEFINED__
typedef interface IKsControl IKsControl;
#ifdef __cplusplus
interface IKsControl;
#endif /* __cplusplus */
#endif

#ifndef __IPerChannelDbLevel_FWD_DEFINED__
#define __IPerChannelDbLevel_FWD_DEFINED__
typedef interface IPerChannelDbLevel IPerChannelDbLevel;
#ifdef __cplusplus
interface IPerChannelDbLevel;
#endif /* __cplusplus */
#endif

#ifndef __IAudioVolumeLevel_FWD_DEFINED__
#define __IAudioVolumeLevel_FWD_DEFINED__
typedef interface IAudioVolumeLevel IAudioVolumeLevel;
#ifdef __cplusplus
interface IAudioVolumeLevel;
#endif /* __cplusplus */
#endif

#ifndef __IAudioChannelConfig_FWD_DEFINED__
#define __IAudioChannelConfig_FWD_DEFINED__
typedef interface IAudioChannelConfig IAudioChannelConfig;
#ifdef __cplusplus
interface IAudioChannelConfig;
#endif /* __cplusplus */
#endif

#ifndef __IAudioLoudness_FWD_DEFINED__
#define __IAudioLoudness_FWD_DEFINED__
typedef interface IAudioLoudness IAudioLoudness;
#ifdef __cplusplus
interface IAudioLoudness;
#endif /* __cplusplus */
#endif

#ifndef __IAudioInputSelector_FWD_DEFINED__
#define __IAudioInputSelector_FWD_DEFINED__
typedef interface IAudioInputSelector IAudioInputSelector;
#ifdef __cplusplus
interface IAudioInputSelector;
#endif /* __cplusplus */
#endif

#ifndef __IAudioOutputSelector_FWD_DEFINED__
#define __IAudioOutputSelector_FWD_DEFINED__
typedef interface IAudioOutputSelector IAudioOutputSelector;
#ifdef __cplusplus
interface IAudioOutputSelector;
#endif /* __cplusplus */
#endif

#ifndef __IAudioMute_FWD_DEFINED__
#define __IAudioMute_FWD_DEFINED__
typedef interface IAudioMute IAudioMute;
#ifdef __cplusplus
interface IAudioMute;
#endif /* __cplusplus */
#endif

#ifndef __IAudioBass_FWD_DEFINED__
#define __IAudioBass_FWD_DEFINED__
typedef interface IAudioBass IAudioBass;
#ifdef __cplusplus
interface IAudioBass;
#endif /* __cplusplus */
#endif

#ifndef __IAudioMidRange_FWD_DEFINED__
#define __IAudioMidRange_FWD_DEFINED__
typedef interface IAudioMidRange IAudioMidRange;
#ifdef __cplusplus
interface IAudioMidRange;
#endif /* __cplusplus */
#endif

#ifndef __IAudioTreble_FWD_DEFINED__
#define __IAudioTreble_FWD_DEFINED__
typedef interface IAudioTreble IAudioTreble;
#ifdef __cplusplus
interface IAudioTreble;
#endif /* __cplusplus */
#endif

#ifndef __IAudioAutoGainControl_FWD_DEFINED__
#define __IAudioAutoGainControl_FWD_DEFINED__
typedef interface IAudioAutoGainControl IAudioAutoGainControl;
#ifdef __cplusplus
interface IAudioAutoGainControl;
#endif /* __cplusplus */
#endif

#ifndef __IAudioPeakMeter_FWD_DEFINED__
#define __IAudioPeakMeter_FWD_DEFINED__
typedef interface IAudioPeakMeter IAudioPeakMeter;
#ifdef __cplusplus
interface IAudioPeakMeter;
#endif /* __cplusplus */
#endif

#ifndef __IDeviceSpecificProperty_FWD_DEFINED__
#define __IDeviceSpecificProperty_FWD_DEFINED__
typedef interface IDeviceSpecificProperty IDeviceSpecificProperty;
#ifdef __cplusplus
interface IDeviceSpecificProperty;
#endif /* __cplusplus */
#endif

#ifndef __IKsFormatSupport_FWD_DEFINED__
#define __IKsFormatSupport_FWD_DEFINED__
typedef interface IKsFormatSupport IKsFormatSupport;
#ifdef __cplusplus
interface IKsFormatSupport;
#endif /* __cplusplus */
#endif

#ifndef __IKsJackDescription_FWD_DEFINED__
#define __IKsJackDescription_FWD_DEFINED__
typedef interface IKsJackDescription IKsJackDescription;
#ifdef __cplusplus
interface IKsJackDescription;
#endif /* __cplusplus */
#endif

#ifndef __IKsJackDescription2_FWD_DEFINED__
#define __IKsJackDescription2_FWD_DEFINED__
typedef interface IKsJackDescription2 IKsJackDescription2;
#ifdef __cplusplus
interface IKsJackDescription2;
#endif /* __cplusplus */
#endif

#ifndef __IKsJackSinkInformation_FWD_DEFINED__
#define __IKsJackSinkInformation_FWD_DEFINED__
typedef interface IKsJackSinkInformation IKsJackSinkInformation;
#ifdef __cplusplus
interface IKsJackSinkInformation;
#endif /* __cplusplus */
#endif

#ifndef __IPartsList_FWD_DEFINED__
#define __IPartsList_FWD_DEFINED__
typedef interface IPartsList IPartsList;
#ifdef __cplusplus
interface IPartsList;
#endif /* __cplusplus */
#endif

#ifndef __IPart_FWD_DEFINED__
#define __IPart_FWD_DEFINED__
typedef interface IPart IPart;
#ifdef __cplusplus
interface IPart;
#endif /* __cplusplus */
#endif

#ifndef __IConnector_FWD_DEFINED__
#define __IConnector_FWD_DEFINED__
typedef interface IConnector IConnector;
#ifdef __cplusplus
interface IConnector;
#endif /* __cplusplus */
#endif

#ifndef __ISubUnit_FWD_DEFINED__
#define __ISubUnit_FWD_DEFINED__
typedef interface ISubUnit ISubUnit;
#ifdef __cplusplus
interface ISubUnit;
#endif /* __cplusplus */
#endif

#ifndef __IControlInterface_FWD_DEFINED__
#define __IControlInterface_FWD_DEFINED__
typedef interface IControlInterface IControlInterface;
#ifdef __cplusplus
interface IControlInterface;
#endif /* __cplusplus */
#endif

#ifndef __IControlChangeNotify_FWD_DEFINED__
#define __IControlChangeNotify_FWD_DEFINED__
typedef interface IControlChangeNotify IControlChangeNotify;
#ifdef __cplusplus
interface IControlChangeNotify;
#endif /* __cplusplus */
#endif

#ifndef __IDeviceTopology_FWD_DEFINED__
#define __IDeviceTopology_FWD_DEFINED__
typedef interface IDeviceTopology IDeviceTopology;
#ifdef __cplusplus
interface IDeviceTopology;
#endif /* __cplusplus */
#endif

#ifndef __DeviceTopology_FWD_DEFINED__
#define __DeviceTopology_FWD_DEFINED__
#ifdef __cplusplus
typedef class DeviceTopology DeviceTopology;
#else
typedef struct DeviceTopology DeviceTopology;
#endif /* defined __cplusplus */
#endif /* defined __DeviceTopology_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <propidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __IPart_FWD_DEFINED__
#define __IPart_FWD_DEFINED__
typedef interface IPart IPart;
#ifdef __cplusplus
interface IPart;
#endif /* __cplusplus */
#endif

#ifndef __IControlInterface_FWD_DEFINED__
#define __IControlInterface_FWD_DEFINED__
typedef interface IControlInterface IControlInterface;
#ifdef __cplusplus
interface IControlInterface;
#endif /* __cplusplus */
#endif

#ifndef __IDeviceTopology_FWD_DEFINED__
#define __IDeviceTopology_FWD_DEFINED__
typedef interface IDeviceTopology IDeviceTopology;
#ifdef __cplusplus
interface IDeviceTopology;
#endif /* __cplusplus */
#endif

#ifndef __IControlChangeNotify_FWD_DEFINED__
#define __IControlChangeNotify_FWD_DEFINED__
typedef interface IControlChangeNotify IControlChangeNotify;
#ifdef __cplusplus
interface IControlChangeNotify;
#endif /* __cplusplus */
#endif

#ifndef E_NOTFOUND
#define E_NOTFOUND HRESULT_FROM_WIN32(ERROR_NOT_FOUND)
#endif
#define DEVTOPO_HARDWARE_INITIATED_EVENTCONTEXT 0x64726148 /* 'draH' */
DEFINE_GUID(EVENTCONTEXT_VOLUMESLIDER, 0xe2c2e9de, 0x09b1, 0x4b04,0x84,0xe5, 0x07, 0x93, 0x12, 0x25, 0xee, 0x04);
#define _IKsControl_
#include <ks.h>
#include <ksmedia.h>
#ifndef _KS_
typedef struct __WIDL_devicetopology_generated_name_00000020 {
    ULONG FormatSize;
    ULONG Flags;
    ULONG SampleSize;
    ULONG Reserved;
    GUID MajorFormat;
    GUID SubFormat;
    GUID Specifier;
} KSDATAFORMAT;
typedef KSDATAFORMAT *PKSDATAFORMAT;
typedef struct __WIDL_devicetopology_generated_name_00000021 {
    __C89_NAMELESS union {
        __C89_NAMELESS struct {
            GUID Set;
            ULONG Id;
            ULONG Flags;
        } __C89_NAMELESSSTRUCTNAME;
        LONGLONG Alignment;
    } __C89_NAMELESSUNIONNAME;
} KSIDENTIFIER;
typedef KSIDENTIFIER KSPROPERTY;
typedef KSIDENTIFIER *PKSPROPERTY;
typedef KSIDENTIFIER KSMETHOD;
typedef KSIDENTIFIER *PKSMETHOD;
typedef KSIDENTIFIER KSEVENT;
typedef KSIDENTIFIER *PKSEVENT;
typedef enum __WIDL_devicetopology_generated_name_00000022 {
    eConnTypeUnknown = 0,
    eConnType3Point5mm = 1,
    eConnTypeQuarter = 2,
    eConnTypeAtapiInternal = 3,
    eConnTypeRCA = 4,
    eConnTypeOptical = 5,
    eConnTypeOtherDigital = 6,
    eConnTypeOtherAnalog = 7,
    eConnTypeMultichannelAnalogDIN = 8,
    eConnTypeXlrProfessional = 9,
    eConnTypeRJ11Modem = 10,
    eConnTypeCombination = 11
} EPcxConnectionType;
typedef enum __WIDL_devicetopology_generated_name_00000023 {
    eGeoLocRear = 1,
    eGeoLocFront = 2,
    eGeoLocLeft = 3,
    eGeoLocRight = 4,
    eGeoLocTop = 5,
    eGeoLocBottom = 6,
    eGeoLocRearPanel = 7,
    eGeoLocRiser = 8,
    eGeoLocInsideMobileLid = 9,
    eGeoLocDrivebay = 10,
    eGeoLocHDMI = 11,
    eGeoLocOutsideMobileLid = 12,
    eGeoLocATAPI = 13,
    eGeoLocReserved5 = 14,
    eGeoLocReserved6 = 15
} EPcxGeoLocation;
typedef enum __WIDL_devicetopology_generated_name_00000024 {
    eGenLocPrimaryBox = 0,
    eGenLocInternal = 1,
    eGenLocSeparate = 2,
    eGenLocOther = 3
} EPcxGenLocation;
typedef enum __WIDL_devicetopology_generated_name_00000025 {
    ePortConnJack = 0,
    ePortConnIntegratedDevice = 1,
    ePortConnBothIntegratedAndJack = 2,
    ePortConnUnknown = 3
} EPxcPortConnection;
typedef struct __WIDL_devicetopology_generated_name_00000026 {
    DWORD ChannelMapping;
    COLORREF Color;
    EPcxConnectionType ConnectionType;
    EPcxGeoLocation GeoLocation;
    EPcxGenLocation GenLocation;
    EPxcPortConnection PortConnection;
    WINBOOL IsConnected;
} KSJACK_DESCRIPTION;
typedef KSJACK_DESCRIPTION *PKSJACK_DESCRIPTION;
typedef struct _LUID {
    DWORD LowPart;
    LONG HighPart;
} LUID;
typedef struct _LUID *PLUID;
typedef enum __WIDL_devicetopology_generated_name_00000027 {
    KSJACK_SINK_CONNECTIONTYPE_HDMI = 0,
    KSJACK_SINK_CONNECTIONTYPE_DISPLAYPORT = 1
} KSJACK_SINK_CONNECTIONTYPE;
typedef struct _tagKSJACK_SINK_INFORMATION {
    KSJACK_SINK_CONNECTIONTYPE ConnType;
    WORD ManufacturerId;
    WORD ProductId;
    WORD AudioLatency;
    WINBOOL HDCPCapable;
    WINBOOL AICapable;
    UCHAR SinkDescriptionLength;
    WCHAR SinkDescription[32];
    LUID PortId;
} KSJACK_SINK_INFORMATION;
typedef struct _tagKSJACK_DESCRIPTION2 {
    DWORD DeviceStateInfo;
    DWORD JackCapabilities;
} KSJACK_DESCRIPTION2;
typedef struct _tagKSJACK_DESCRIPTION2 *PKSJACK_DESCRIPTION2;
#endif
typedef enum __WIDL_devicetopology_generated_name_00000028 {
    In = 0,
    Out = 1
} DataFlow;
typedef enum __WIDL_devicetopology_generated_name_00000029 {
    Connector = 0,
    Subunit = 1
} PartType;
typedef enum __WIDL_devicetopology_generated_name_0000002A {
    Unknown_Connector = 0,
    Physical_Internal = 1,
    Physical_External = 2,
    Software_IO = 3,
    Software_Fixed = 4,
    Network = 5
} ConnectorType;
/*****************************************************************************
 * IKsControl interface
 */
#ifndef __IKsControl_INTERFACE_DEFINED__
#define __IKsControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IKsControl, 0x28f54685, 0x06fd, 0x11d2, 0xb2,0x7a, 0x00,0xa0,0xc9,0x22,0x31,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("28f54685-06fd-11d2-b27a-00a0c9223196")
IKsControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE KsProperty(
        PKSPROPERTY Property,
        ULONG PropertyLength,
        void *PropertyData,
        ULONG DataLength,
        ULONG *BytesReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE KsMethod(
        PKSMETHOD Method,
        ULONG MethodLength,
        void *MethodData,
        ULONG DataLength,
        ULONG *BytesReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE KsEvent(
        PKSEVENT Event,
        ULONG EventLength,
        void *EventData,
        ULONG DataLength,
        ULONG *BytesReturned) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IKsControl, 0x28f54685, 0x06fd, 0x11d2, 0xb2,0x7a, 0x00,0xa0,0xc9,0x22,0x31,0x96)
#endif
#else
typedef struct IKsControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IKsControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IKsControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IKsControl *This);

    /*** IKsControl methods ***/
    HRESULT (STDMETHODCALLTYPE *KsProperty)(
        IKsControl *This,
        PKSPROPERTY Property,
        ULONG PropertyLength,
        void *PropertyData,
        ULONG DataLength,
        ULONG *BytesReturned);

    HRESULT (STDMETHODCALLTYPE *KsMethod)(
        IKsControl *This,
        PKSMETHOD Method,
        ULONG MethodLength,
        void *MethodData,
        ULONG DataLength,
        ULONG *BytesReturned);

    HRESULT (STDMETHODCALLTYPE *KsEvent)(
        IKsControl *This,
        PKSEVENT Event,
        ULONG EventLength,
        void *EventData,
        ULONG DataLength,
        ULONG *BytesReturned);

    END_INTERFACE
} IKsControlVtbl;

interface IKsControl {
    CONST_VTBL IKsControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IKsControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IKsControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IKsControl_Release(This) (This)->lpVtbl->Release(This)
/*** IKsControl methods ***/
#define IKsControl_KsProperty(This,Property,PropertyLength,PropertyData,DataLength,BytesReturned) (This)->lpVtbl->KsProperty(This,Property,PropertyLength,PropertyData,DataLength,BytesReturned)
#define IKsControl_KsMethod(This,Method,MethodLength,MethodData,DataLength,BytesReturned) (This)->lpVtbl->KsMethod(This,Method,MethodLength,MethodData,DataLength,BytesReturned)
#define IKsControl_KsEvent(This,Event,EventLength,EventData,DataLength,BytesReturned) (This)->lpVtbl->KsEvent(This,Event,EventLength,EventData,DataLength,BytesReturned)
#else
/*** IUnknown methods ***/
static inline HRESULT IKsControl_QueryInterface(IKsControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IKsControl_AddRef(IKsControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IKsControl_Release(IKsControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IKsControl methods ***/
static inline HRESULT IKsControl_KsProperty(IKsControl* This,PKSPROPERTY Property,ULONG PropertyLength,void *PropertyData,ULONG DataLength,ULONG *BytesReturned) {
    return This->lpVtbl->KsProperty(This,Property,PropertyLength,PropertyData,DataLength,BytesReturned);
}
static inline HRESULT IKsControl_KsMethod(IKsControl* This,PKSMETHOD Method,ULONG MethodLength,void *MethodData,ULONG DataLength,ULONG *BytesReturned) {
    return This->lpVtbl->KsMethod(This,Method,MethodLength,MethodData,DataLength,BytesReturned);
}
static inline HRESULT IKsControl_KsEvent(IKsControl* This,PKSEVENT Event,ULONG EventLength,void *EventData,ULONG DataLength,ULONG *BytesReturned) {
    return This->lpVtbl->KsEvent(This,Event,EventLength,EventData,DataLength,BytesReturned);
}
#endif
#endif

#endif


#endif  /* __IKsControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPerChannelDbLevel interface
 */
#ifndef __IPerChannelDbLevel_INTERFACE_DEFINED__
#define __IPerChannelDbLevel_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPerChannelDbLevel, 0xc2f8e001, 0xf205, 0x4bc9, 0x99,0xbc, 0xc1,0x3b,0x1e,0x04,0x8c,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c2f8e001-f205-4bc9-99bc-c13b1e048ccb")
IPerChannelDbLevel : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetChannelCount(
        UINT *pcChannels) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLevelRange(
        UINT nChannel,
        float *pfMinLevelDB,
        float *pfMaxLevelDB,
        float *pfStepping) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLevel(
        UINT nChannel,
        float *pfLevelDB) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLevel(
        UINT nChannel,
        float fLevelDB,
        LPCGUID pguidEventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLevelUniform(
        float fLevelDB,
        LPCGUID pguidEventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLevelAllChannels(
        float *aLevelsDB,
        ULONG cChannels,
        LPCGUID pguidEventContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPerChannelDbLevel, 0xc2f8e001, 0xf205, 0x4bc9, 0x99,0xbc, 0xc1,0x3b,0x1e,0x04,0x8c,0xcb)
#endif
#else
typedef struct IPerChannelDbLevelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPerChannelDbLevel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPerChannelDbLevel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPerChannelDbLevel *This);

    /*** IPerChannelDbLevel methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IPerChannelDbLevel *This,
        UINT *pcChannels);

    HRESULT (STDMETHODCALLTYPE *GetLevelRange)(
        IPerChannelDbLevel *This,
        UINT nChannel,
        float *pfMinLevelDB,
        float *pfMaxLevelDB,
        float *pfStepping);

    HRESULT (STDMETHODCALLTYPE *GetLevel)(
        IPerChannelDbLevel *This,
        UINT nChannel,
        float *pfLevelDB);

    HRESULT (STDMETHODCALLTYPE *SetLevel)(
        IPerChannelDbLevel *This,
        UINT nChannel,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelUniform)(
        IPerChannelDbLevel *This,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelAllChannels)(
        IPerChannelDbLevel *This,
        float *aLevelsDB,
        ULONG cChannels,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IPerChannelDbLevelVtbl;

interface IPerChannelDbLevel {
    CONST_VTBL IPerChannelDbLevelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPerChannelDbLevel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPerChannelDbLevel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPerChannelDbLevel_Release(This) (This)->lpVtbl->Release(This)
/*** IPerChannelDbLevel methods ***/
#define IPerChannelDbLevel_GetChannelCount(This,pcChannels) (This)->lpVtbl->GetChannelCount(This,pcChannels)
#define IPerChannelDbLevel_GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping) (This)->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping)
#define IPerChannelDbLevel_GetLevel(This,nChannel,pfLevelDB) (This)->lpVtbl->GetLevel(This,nChannel,pfLevelDB)
#define IPerChannelDbLevel_SetLevel(This,nChannel,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext)
#define IPerChannelDbLevel_SetLevelUniform(This,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext)
#define IPerChannelDbLevel_SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext) (This)->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IPerChannelDbLevel_QueryInterface(IPerChannelDbLevel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPerChannelDbLevel_AddRef(IPerChannelDbLevel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPerChannelDbLevel_Release(IPerChannelDbLevel* This) {
    return This->lpVtbl->Release(This);
}
/*** IPerChannelDbLevel methods ***/
static inline HRESULT IPerChannelDbLevel_GetChannelCount(IPerChannelDbLevel* This,UINT *pcChannels) {
    return This->lpVtbl->GetChannelCount(This,pcChannels);
}
static inline HRESULT IPerChannelDbLevel_GetLevelRange(IPerChannelDbLevel* This,UINT nChannel,float *pfMinLevelDB,float *pfMaxLevelDB,float *pfStepping) {
    return This->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping);
}
static inline HRESULT IPerChannelDbLevel_GetLevel(IPerChannelDbLevel* This,UINT nChannel,float *pfLevelDB) {
    return This->lpVtbl->GetLevel(This,nChannel,pfLevelDB);
}
static inline HRESULT IPerChannelDbLevel_SetLevel(IPerChannelDbLevel* This,UINT nChannel,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext);
}
static inline HRESULT IPerChannelDbLevel_SetLevelUniform(IPerChannelDbLevel* This,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext);
}
static inline HRESULT IPerChannelDbLevel_SetLevelAllChannels(IPerChannelDbLevel* This,float *aLevelsDB,ULONG cChannels,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IPerChannelDbLevel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioVolumeLevel interface
 */
#ifndef __IAudioVolumeLevel_INTERFACE_DEFINED__
#define __IAudioVolumeLevel_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioVolumeLevel, 0x7fb7b48f, 0x531d, 0x44a2, 0xbc,0xb3, 0x5a,0xd5,0xa1,0x34,0xb3,0xdc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7fb7b48f-531d-44a2-bcb3-5ad5a134b3dc")
IAudioVolumeLevel : public IPerChannelDbLevel
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioVolumeLevel, 0x7fb7b48f, 0x531d, 0x44a2, 0xbc,0xb3, 0x5a,0xd5,0xa1,0x34,0xb3,0xdc)
#endif
#else
typedef struct IAudioVolumeLevelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioVolumeLevel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioVolumeLevel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioVolumeLevel *This);

    /*** IPerChannelDbLevel methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IAudioVolumeLevel *This,
        UINT *pcChannels);

    HRESULT (STDMETHODCALLTYPE *GetLevelRange)(
        IAudioVolumeLevel *This,
        UINT nChannel,
        float *pfMinLevelDB,
        float *pfMaxLevelDB,
        float *pfStepping);

    HRESULT (STDMETHODCALLTYPE *GetLevel)(
        IAudioVolumeLevel *This,
        UINT nChannel,
        float *pfLevelDB);

    HRESULT (STDMETHODCALLTYPE *SetLevel)(
        IAudioVolumeLevel *This,
        UINT nChannel,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelUniform)(
        IAudioVolumeLevel *This,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelAllChannels)(
        IAudioVolumeLevel *This,
        float *aLevelsDB,
        ULONG cChannels,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IAudioVolumeLevelVtbl;

interface IAudioVolumeLevel {
    CONST_VTBL IAudioVolumeLevelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioVolumeLevel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioVolumeLevel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioVolumeLevel_Release(This) (This)->lpVtbl->Release(This)
/*** IPerChannelDbLevel methods ***/
#define IAudioVolumeLevel_GetChannelCount(This,pcChannels) (This)->lpVtbl->GetChannelCount(This,pcChannels)
#define IAudioVolumeLevel_GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping) (This)->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping)
#define IAudioVolumeLevel_GetLevel(This,nChannel,pfLevelDB) (This)->lpVtbl->GetLevel(This,nChannel,pfLevelDB)
#define IAudioVolumeLevel_SetLevel(This,nChannel,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext)
#define IAudioVolumeLevel_SetLevelUniform(This,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext)
#define IAudioVolumeLevel_SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext) (This)->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioVolumeLevel_QueryInterface(IAudioVolumeLevel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioVolumeLevel_AddRef(IAudioVolumeLevel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioVolumeLevel_Release(IAudioVolumeLevel* This) {
    return This->lpVtbl->Release(This);
}
/*** IPerChannelDbLevel methods ***/
static inline HRESULT IAudioVolumeLevel_GetChannelCount(IAudioVolumeLevel* This,UINT *pcChannels) {
    return This->lpVtbl->GetChannelCount(This,pcChannels);
}
static inline HRESULT IAudioVolumeLevel_GetLevelRange(IAudioVolumeLevel* This,UINT nChannel,float *pfMinLevelDB,float *pfMaxLevelDB,float *pfStepping) {
    return This->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping);
}
static inline HRESULT IAudioVolumeLevel_GetLevel(IAudioVolumeLevel* This,UINT nChannel,float *pfLevelDB) {
    return This->lpVtbl->GetLevel(This,nChannel,pfLevelDB);
}
static inline HRESULT IAudioVolumeLevel_SetLevel(IAudioVolumeLevel* This,UINT nChannel,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioVolumeLevel_SetLevelUniform(IAudioVolumeLevel* This,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioVolumeLevel_SetLevelAllChannels(IAudioVolumeLevel* This,float *aLevelsDB,ULONG cChannels,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IAudioVolumeLevel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioChannelConfig interface
 */
#ifndef __IAudioChannelConfig_INTERFACE_DEFINED__
#define __IAudioChannelConfig_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioChannelConfig, 0xbb11c46f, 0xec28, 0x493c, 0xb8,0x8a, 0x5d,0xb8,0x80,0x62,0xce,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb11c46f-ec28-493c-b88a-5db88062ce98")
IAudioChannelConfig : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetChannelConfig(
        DWORD dwConfig,
        LPCGUID pguidEventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelConfig(
        DWORD dwConfig,
        DWORD *pdwConfig) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioChannelConfig, 0xbb11c46f, 0xec28, 0x493c, 0xb8,0x8a, 0x5d,0xb8,0x80,0x62,0xce,0x98)
#endif
#else
typedef struct IAudioChannelConfigVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioChannelConfig *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioChannelConfig *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioChannelConfig *This);

    /*** IAudioChannelConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *SetChannelConfig)(
        IAudioChannelConfig *This,
        DWORD dwConfig,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *GetChannelConfig)(
        IAudioChannelConfig *This,
        DWORD dwConfig,
        DWORD *pdwConfig);

    END_INTERFACE
} IAudioChannelConfigVtbl;

interface IAudioChannelConfig {
    CONST_VTBL IAudioChannelConfigVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioChannelConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioChannelConfig_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioChannelConfig_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioChannelConfig methods ***/
#define IAudioChannelConfig_SetChannelConfig(This,dwConfig,pguidEventContext) (This)->lpVtbl->SetChannelConfig(This,dwConfig,pguidEventContext)
#define IAudioChannelConfig_GetChannelConfig(This,dwConfig,pdwConfig) (This)->lpVtbl->GetChannelConfig(This,dwConfig,pdwConfig)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioChannelConfig_QueryInterface(IAudioChannelConfig* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioChannelConfig_AddRef(IAudioChannelConfig* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioChannelConfig_Release(IAudioChannelConfig* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioChannelConfig methods ***/
static inline HRESULT IAudioChannelConfig_SetChannelConfig(IAudioChannelConfig* This,DWORD dwConfig,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetChannelConfig(This,dwConfig,pguidEventContext);
}
static inline HRESULT IAudioChannelConfig_GetChannelConfig(IAudioChannelConfig* This,DWORD dwConfig,DWORD *pdwConfig) {
    return This->lpVtbl->GetChannelConfig(This,dwConfig,pdwConfig);
}
#endif
#endif

#endif


#endif  /* __IAudioChannelConfig_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioLoudness interface
 */
#ifndef __IAudioLoudness_INTERFACE_DEFINED__
#define __IAudioLoudness_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioLoudness, 0x7d8b1437, 0xdd53, 0x4350, 0x9c,0x1b, 0x1e,0xe2,0x89,0x0b,0xd9,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7d8b1437-dd53-4350-9c1b-1ee2890bd938")
IAudioLoudness : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEnabled(
        WINBOOL *pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnabled(
        WINBOOL bEnabled,
        LPCGUID pguidEventContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioLoudness, 0x7d8b1437, 0xdd53, 0x4350, 0x9c,0x1b, 0x1e,0xe2,0x89,0x0b,0xd9,0x38)
#endif
#else
typedef struct IAudioLoudnessVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioLoudness *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioLoudness *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioLoudness *This);

    /*** IAudioLoudness methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEnabled)(
        IAudioLoudness *This,
        WINBOOL *pbEnabled);

    HRESULT (STDMETHODCALLTYPE *SetEnabled)(
        IAudioLoudness *This,
        WINBOOL bEnabled,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IAudioLoudnessVtbl;

interface IAudioLoudness {
    CONST_VTBL IAudioLoudnessVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioLoudness_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioLoudness_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioLoudness_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioLoudness methods ***/
#define IAudioLoudness_GetEnabled(This,pbEnabled) (This)->lpVtbl->GetEnabled(This,pbEnabled)
#define IAudioLoudness_SetEnabled(This,bEnabled,pguidEventContext) (This)->lpVtbl->SetEnabled(This,bEnabled,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioLoudness_QueryInterface(IAudioLoudness* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioLoudness_AddRef(IAudioLoudness* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioLoudness_Release(IAudioLoudness* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioLoudness methods ***/
static inline HRESULT IAudioLoudness_GetEnabled(IAudioLoudness* This,WINBOOL *pbEnabled) {
    return This->lpVtbl->GetEnabled(This,pbEnabled);
}
static inline HRESULT IAudioLoudness_SetEnabled(IAudioLoudness* This,WINBOOL bEnabled,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetEnabled(This,bEnabled,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IAudioLoudness_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioInputSelector interface
 */
#ifndef __IAudioInputSelector_INTERFACE_DEFINED__
#define __IAudioInputSelector_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioInputSelector, 0x4f03dc02, 0x5e6e, 0x4653, 0x8f,0x72, 0xa0,0x30,0xc1,0x23,0xd5,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4f03dc02-5e6e-4653-8f72-a030c123d598")
IAudioInputSelector : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSelection(
        UINT *pnIdSelected) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSelection(
        UINT nIdSelect,
        LPCGUID pguidEventContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioInputSelector, 0x4f03dc02, 0x5e6e, 0x4653, 0x8f,0x72, 0xa0,0x30,0xc1,0x23,0xd5,0x98)
#endif
#else
typedef struct IAudioInputSelectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioInputSelector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioInputSelector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioInputSelector *This);

    /*** IAudioInputSelector methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        IAudioInputSelector *This,
        UINT *pnIdSelected);

    HRESULT (STDMETHODCALLTYPE *SetSelection)(
        IAudioInputSelector *This,
        UINT nIdSelect,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IAudioInputSelectorVtbl;

interface IAudioInputSelector {
    CONST_VTBL IAudioInputSelectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioInputSelector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioInputSelector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioInputSelector_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioInputSelector methods ***/
#define IAudioInputSelector_GetSelection(This,pnIdSelected) (This)->lpVtbl->GetSelection(This,pnIdSelected)
#define IAudioInputSelector_SetSelection(This,nIdSelect,pguidEventContext) (This)->lpVtbl->SetSelection(This,nIdSelect,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioInputSelector_QueryInterface(IAudioInputSelector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioInputSelector_AddRef(IAudioInputSelector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioInputSelector_Release(IAudioInputSelector* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioInputSelector methods ***/
static inline HRESULT IAudioInputSelector_GetSelection(IAudioInputSelector* This,UINT *pnIdSelected) {
    return This->lpVtbl->GetSelection(This,pnIdSelected);
}
static inline HRESULT IAudioInputSelector_SetSelection(IAudioInputSelector* This,UINT nIdSelect,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetSelection(This,nIdSelect,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IAudioInputSelector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioOutputSelector interface
 */
#ifndef __IAudioOutputSelector_INTERFACE_DEFINED__
#define __IAudioOutputSelector_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioOutputSelector, 0xbb515f69, 0x94a7, 0x429e, 0x8b,0x9c, 0x27,0x1b,0x3f,0x11,0xa3,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb515f69-94a7-429e-8b9c-271b3f11a3ab")
IAudioOutputSelector : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSelection(
        UINT *pnIdSelected) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSelection(
        UINT nIdSelect,
        LPCGUID pguidEventContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioOutputSelector, 0xbb515f69, 0x94a7, 0x429e, 0x8b,0x9c, 0x27,0x1b,0x3f,0x11,0xa3,0xab)
#endif
#else
typedef struct IAudioOutputSelectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioOutputSelector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioOutputSelector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioOutputSelector *This);

    /*** IAudioOutputSelector methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        IAudioOutputSelector *This,
        UINT *pnIdSelected);

    HRESULT (STDMETHODCALLTYPE *SetSelection)(
        IAudioOutputSelector *This,
        UINT nIdSelect,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IAudioOutputSelectorVtbl;

interface IAudioOutputSelector {
    CONST_VTBL IAudioOutputSelectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioOutputSelector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioOutputSelector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioOutputSelector_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioOutputSelector methods ***/
#define IAudioOutputSelector_GetSelection(This,pnIdSelected) (This)->lpVtbl->GetSelection(This,pnIdSelected)
#define IAudioOutputSelector_SetSelection(This,nIdSelect,pguidEventContext) (This)->lpVtbl->SetSelection(This,nIdSelect,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioOutputSelector_QueryInterface(IAudioOutputSelector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioOutputSelector_AddRef(IAudioOutputSelector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioOutputSelector_Release(IAudioOutputSelector* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioOutputSelector methods ***/
static inline HRESULT IAudioOutputSelector_GetSelection(IAudioOutputSelector* This,UINT *pnIdSelected) {
    return This->lpVtbl->GetSelection(This,pnIdSelected);
}
static inline HRESULT IAudioOutputSelector_SetSelection(IAudioOutputSelector* This,UINT nIdSelect,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetSelection(This,nIdSelect,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IAudioOutputSelector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioMute interface
 */
#ifndef __IAudioMute_INTERFACE_DEFINED__
#define __IAudioMute_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioMute, 0xdf45aeea, 0xb74a, 0x4b6b, 0xaf,0xad, 0x23,0x66,0xb6,0xaa,0x01,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("df45aeea-b74a-4b6b-afad-2366b6aa012e")
IAudioMute : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetMute(
        WINBOOL bMute,
        LPCGUID pguidEventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMute(
        WINBOOL *pbMute) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioMute, 0xdf45aeea, 0xb74a, 0x4b6b, 0xaf,0xad, 0x23,0x66,0xb6,0xaa,0x01,0x2e)
#endif
#else
typedef struct IAudioMuteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioMute *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioMute *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioMute *This);

    /*** IAudioMute methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMute)(
        IAudioMute *This,
        WINBOOL bMute,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *GetMute)(
        IAudioMute *This,
        WINBOOL *pbMute);

    END_INTERFACE
} IAudioMuteVtbl;

interface IAudioMute {
    CONST_VTBL IAudioMuteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioMute_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioMute_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioMute_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioMute methods ***/
#define IAudioMute_SetMute(This,bMute,pguidEventContext) (This)->lpVtbl->SetMute(This,bMute,pguidEventContext)
#define IAudioMute_GetMute(This,pbMute) (This)->lpVtbl->GetMute(This,pbMute)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioMute_QueryInterface(IAudioMute* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioMute_AddRef(IAudioMute* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioMute_Release(IAudioMute* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioMute methods ***/
static inline HRESULT IAudioMute_SetMute(IAudioMute* This,WINBOOL bMute,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetMute(This,bMute,pguidEventContext);
}
static inline HRESULT IAudioMute_GetMute(IAudioMute* This,WINBOOL *pbMute) {
    return This->lpVtbl->GetMute(This,pbMute);
}
#endif
#endif

#endif


#endif  /* __IAudioMute_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioBass interface
 */
#ifndef __IAudioBass_INTERFACE_DEFINED__
#define __IAudioBass_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioBass, 0xa2b1a1d9, 0x4db3, 0x425d, 0xa2,0xb2, 0xbd,0x33,0x5c,0xb3,0xe2,0xe5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a2b1a1d9-4db3-425d-a2b2-bd335cb3e2e5")
IAudioBass : public IPerChannelDbLevel
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioBass, 0xa2b1a1d9, 0x4db3, 0x425d, 0xa2,0xb2, 0xbd,0x33,0x5c,0xb3,0xe2,0xe5)
#endif
#else
typedef struct IAudioBassVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioBass *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioBass *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioBass *This);

    /*** IPerChannelDbLevel methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IAudioBass *This,
        UINT *pcChannels);

    HRESULT (STDMETHODCALLTYPE *GetLevelRange)(
        IAudioBass *This,
        UINT nChannel,
        float *pfMinLevelDB,
        float *pfMaxLevelDB,
        float *pfStepping);

    HRESULT (STDMETHODCALLTYPE *GetLevel)(
        IAudioBass *This,
        UINT nChannel,
        float *pfLevelDB);

    HRESULT (STDMETHODCALLTYPE *SetLevel)(
        IAudioBass *This,
        UINT nChannel,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelUniform)(
        IAudioBass *This,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelAllChannels)(
        IAudioBass *This,
        float *aLevelsDB,
        ULONG cChannels,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IAudioBassVtbl;

interface IAudioBass {
    CONST_VTBL IAudioBassVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioBass_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioBass_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioBass_Release(This) (This)->lpVtbl->Release(This)
/*** IPerChannelDbLevel methods ***/
#define IAudioBass_GetChannelCount(This,pcChannels) (This)->lpVtbl->GetChannelCount(This,pcChannels)
#define IAudioBass_GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping) (This)->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping)
#define IAudioBass_GetLevel(This,nChannel,pfLevelDB) (This)->lpVtbl->GetLevel(This,nChannel,pfLevelDB)
#define IAudioBass_SetLevel(This,nChannel,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext)
#define IAudioBass_SetLevelUniform(This,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext)
#define IAudioBass_SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext) (This)->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioBass_QueryInterface(IAudioBass* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioBass_AddRef(IAudioBass* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioBass_Release(IAudioBass* This) {
    return This->lpVtbl->Release(This);
}
/*** IPerChannelDbLevel methods ***/
static inline HRESULT IAudioBass_GetChannelCount(IAudioBass* This,UINT *pcChannels) {
    return This->lpVtbl->GetChannelCount(This,pcChannels);
}
static inline HRESULT IAudioBass_GetLevelRange(IAudioBass* This,UINT nChannel,float *pfMinLevelDB,float *pfMaxLevelDB,float *pfStepping) {
    return This->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping);
}
static inline HRESULT IAudioBass_GetLevel(IAudioBass* This,UINT nChannel,float *pfLevelDB) {
    return This->lpVtbl->GetLevel(This,nChannel,pfLevelDB);
}
static inline HRESULT IAudioBass_SetLevel(IAudioBass* This,UINT nChannel,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioBass_SetLevelUniform(IAudioBass* This,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioBass_SetLevelAllChannels(IAudioBass* This,float *aLevelsDB,ULONG cChannels,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IAudioBass_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioMidRange interface
 */
#ifndef __IAudioMidRange_INTERFACE_DEFINED__
#define __IAudioMidRange_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioMidRange, 0x5e54b6d7, 0xb44b, 0x40d9, 0x9a,0x9e, 0xe6,0x91,0xd9,0xce,0x6e,0xdf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e54b6d7-b44b-40d9-9a9e-e691d9ce6edf")
IAudioMidRange : public IPerChannelDbLevel
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioMidRange, 0x5e54b6d7, 0xb44b, 0x40d9, 0x9a,0x9e, 0xe6,0x91,0xd9,0xce,0x6e,0xdf)
#endif
#else
typedef struct IAudioMidRangeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioMidRange *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioMidRange *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioMidRange *This);

    /*** IPerChannelDbLevel methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IAudioMidRange *This,
        UINT *pcChannels);

    HRESULT (STDMETHODCALLTYPE *GetLevelRange)(
        IAudioMidRange *This,
        UINT nChannel,
        float *pfMinLevelDB,
        float *pfMaxLevelDB,
        float *pfStepping);

    HRESULT (STDMETHODCALLTYPE *GetLevel)(
        IAudioMidRange *This,
        UINT nChannel,
        float *pfLevelDB);

    HRESULT (STDMETHODCALLTYPE *SetLevel)(
        IAudioMidRange *This,
        UINT nChannel,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelUniform)(
        IAudioMidRange *This,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelAllChannels)(
        IAudioMidRange *This,
        float *aLevelsDB,
        ULONG cChannels,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IAudioMidRangeVtbl;

interface IAudioMidRange {
    CONST_VTBL IAudioMidRangeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioMidRange_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioMidRange_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioMidRange_Release(This) (This)->lpVtbl->Release(This)
/*** IPerChannelDbLevel methods ***/
#define IAudioMidRange_GetChannelCount(This,pcChannels) (This)->lpVtbl->GetChannelCount(This,pcChannels)
#define IAudioMidRange_GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping) (This)->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping)
#define IAudioMidRange_GetLevel(This,nChannel,pfLevelDB) (This)->lpVtbl->GetLevel(This,nChannel,pfLevelDB)
#define IAudioMidRange_SetLevel(This,nChannel,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext)
#define IAudioMidRange_SetLevelUniform(This,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext)
#define IAudioMidRange_SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext) (This)->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioMidRange_QueryInterface(IAudioMidRange* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioMidRange_AddRef(IAudioMidRange* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioMidRange_Release(IAudioMidRange* This) {
    return This->lpVtbl->Release(This);
}
/*** IPerChannelDbLevel methods ***/
static inline HRESULT IAudioMidRange_GetChannelCount(IAudioMidRange* This,UINT *pcChannels) {
    return This->lpVtbl->GetChannelCount(This,pcChannels);
}
static inline HRESULT IAudioMidRange_GetLevelRange(IAudioMidRange* This,UINT nChannel,float *pfMinLevelDB,float *pfMaxLevelDB,float *pfStepping) {
    return This->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping);
}
static inline HRESULT IAudioMidRange_GetLevel(IAudioMidRange* This,UINT nChannel,float *pfLevelDB) {
    return This->lpVtbl->GetLevel(This,nChannel,pfLevelDB);
}
static inline HRESULT IAudioMidRange_SetLevel(IAudioMidRange* This,UINT nChannel,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioMidRange_SetLevelUniform(IAudioMidRange* This,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioMidRange_SetLevelAllChannels(IAudioMidRange* This,float *aLevelsDB,ULONG cChannels,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IAudioMidRange_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioTreble interface
 */
#ifndef __IAudioTreble_INTERFACE_DEFINED__
#define __IAudioTreble_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioTreble, 0x0a717812, 0x694e, 0x4907, 0xb7,0x4b, 0xba,0xfa,0x5c,0xfd,0xca,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0a717812-694e-4907-b74b-bafa5cfdca7b")
IAudioTreble : public IPerChannelDbLevel
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioTreble, 0x0a717812, 0x694e, 0x4907, 0xb7,0x4b, 0xba,0xfa,0x5c,0xfd,0xca,0x7b)
#endif
#else
typedef struct IAudioTrebleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioTreble *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioTreble *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioTreble *This);

    /*** IPerChannelDbLevel methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IAudioTreble *This,
        UINT *pcChannels);

    HRESULT (STDMETHODCALLTYPE *GetLevelRange)(
        IAudioTreble *This,
        UINT nChannel,
        float *pfMinLevelDB,
        float *pfMaxLevelDB,
        float *pfStepping);

    HRESULT (STDMETHODCALLTYPE *GetLevel)(
        IAudioTreble *This,
        UINT nChannel,
        float *pfLevelDB);

    HRESULT (STDMETHODCALLTYPE *SetLevel)(
        IAudioTreble *This,
        UINT nChannel,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelUniform)(
        IAudioTreble *This,
        float fLevelDB,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *SetLevelAllChannels)(
        IAudioTreble *This,
        float *aLevelsDB,
        ULONG cChannels,
        LPCGUID pguidEventContext);

    END_INTERFACE
} IAudioTrebleVtbl;

interface IAudioTreble {
    CONST_VTBL IAudioTrebleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioTreble_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioTreble_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioTreble_Release(This) (This)->lpVtbl->Release(This)
/*** IPerChannelDbLevel methods ***/
#define IAudioTreble_GetChannelCount(This,pcChannels) (This)->lpVtbl->GetChannelCount(This,pcChannels)
#define IAudioTreble_GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping) (This)->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping)
#define IAudioTreble_GetLevel(This,nChannel,pfLevelDB) (This)->lpVtbl->GetLevel(This,nChannel,pfLevelDB)
#define IAudioTreble_SetLevel(This,nChannel,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext)
#define IAudioTreble_SetLevelUniform(This,fLevelDB,pguidEventContext) (This)->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext)
#define IAudioTreble_SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext) (This)->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioTreble_QueryInterface(IAudioTreble* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioTreble_AddRef(IAudioTreble* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioTreble_Release(IAudioTreble* This) {
    return This->lpVtbl->Release(This);
}
/*** IPerChannelDbLevel methods ***/
static inline HRESULT IAudioTreble_GetChannelCount(IAudioTreble* This,UINT *pcChannels) {
    return This->lpVtbl->GetChannelCount(This,pcChannels);
}
static inline HRESULT IAudioTreble_GetLevelRange(IAudioTreble* This,UINT nChannel,float *pfMinLevelDB,float *pfMaxLevelDB,float *pfStepping) {
    return This->lpVtbl->GetLevelRange(This,nChannel,pfMinLevelDB,pfMaxLevelDB,pfStepping);
}
static inline HRESULT IAudioTreble_GetLevel(IAudioTreble* This,UINT nChannel,float *pfLevelDB) {
    return This->lpVtbl->GetLevel(This,nChannel,pfLevelDB);
}
static inline HRESULT IAudioTreble_SetLevel(IAudioTreble* This,UINT nChannel,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevel(This,nChannel,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioTreble_SetLevelUniform(IAudioTreble* This,float fLevelDB,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelUniform(This,fLevelDB,pguidEventContext);
}
static inline HRESULT IAudioTreble_SetLevelAllChannels(IAudioTreble* This,float *aLevelsDB,ULONG cChannels,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetLevelAllChannels(This,aLevelsDB,cChannels,pguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IAudioTreble_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioAutoGainControl interface
 */
#ifndef __IAudioAutoGainControl_INTERFACE_DEFINED__
#define __IAudioAutoGainControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioAutoGainControl, 0x85401fd4, 0x6de4, 0x4b9d, 0x98,0x69, 0x2d,0x67,0x53,0xa8,0x2f,0x3c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("85401fd4-6de4-4b9d-9869-2d6753a82f3c")
IAudioAutoGainControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEnabled(
        WINBOOL bEnabled,
        LPCGUID pguidEventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMute(
        WINBOOL *pbEnabled) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioAutoGainControl, 0x85401fd4, 0x6de4, 0x4b9d, 0x98,0x69, 0x2d,0x67,0x53,0xa8,0x2f,0x3c)
#endif
#else
typedef struct IAudioAutoGainControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioAutoGainControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioAutoGainControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioAutoGainControl *This);

    /*** IAudioAutoGainControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEnabled)(
        IAudioAutoGainControl *This,
        WINBOOL bEnabled,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *GetMute)(
        IAudioAutoGainControl *This,
        WINBOOL *pbEnabled);

    END_INTERFACE
} IAudioAutoGainControlVtbl;

interface IAudioAutoGainControl {
    CONST_VTBL IAudioAutoGainControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioAutoGainControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioAutoGainControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioAutoGainControl_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioAutoGainControl methods ***/
#define IAudioAutoGainControl_GetEnabled(This,bEnabled,pguidEventContext) (This)->lpVtbl->GetEnabled(This,bEnabled,pguidEventContext)
#define IAudioAutoGainControl_GetMute(This,pbEnabled) (This)->lpVtbl->GetMute(This,pbEnabled)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioAutoGainControl_QueryInterface(IAudioAutoGainControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioAutoGainControl_AddRef(IAudioAutoGainControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioAutoGainControl_Release(IAudioAutoGainControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioAutoGainControl methods ***/
static inline HRESULT IAudioAutoGainControl_GetEnabled(IAudioAutoGainControl* This,WINBOOL bEnabled,LPCGUID pguidEventContext) {
    return This->lpVtbl->GetEnabled(This,bEnabled,pguidEventContext);
}
static inline HRESULT IAudioAutoGainControl_GetMute(IAudioAutoGainControl* This,WINBOOL *pbEnabled) {
    return This->lpVtbl->GetMute(This,pbEnabled);
}
#endif
#endif

#endif


#endif  /* __IAudioAutoGainControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioPeakMeter interface
 */
#ifndef __IAudioPeakMeter_INTERFACE_DEFINED__
#define __IAudioPeakMeter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioPeakMeter, 0xdd79923c, 0x0599, 0x45e0, 0xb8,0xb6, 0xc8,0xdf,0x7d,0xb6,0xe7,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dd79923c-0599-45e0-b8b6-c8df7db6e796")
IAudioPeakMeter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetChannelCount(
        UINT *pcChannels) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLevel(
        UINT nChannel,
        float *pfLevel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioPeakMeter, 0xdd79923c, 0x0599, 0x45e0, 0xb8,0xb6, 0xc8,0xdf,0x7d,0xb6,0xe7,0x96)
#endif
#else
typedef struct IAudioPeakMeterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioPeakMeter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioPeakMeter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioPeakMeter *This);

    /*** IAudioPeakMeter methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IAudioPeakMeter *This,
        UINT *pcChannels);

    HRESULT (STDMETHODCALLTYPE *GetLevel)(
        IAudioPeakMeter *This,
        UINT nChannel,
        float *pfLevel);

    END_INTERFACE
} IAudioPeakMeterVtbl;

interface IAudioPeakMeter {
    CONST_VTBL IAudioPeakMeterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioPeakMeter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioPeakMeter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioPeakMeter_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioPeakMeter methods ***/
#define IAudioPeakMeter_GetChannelCount(This,pcChannels) (This)->lpVtbl->GetChannelCount(This,pcChannels)
#define IAudioPeakMeter_GetLevel(This,nChannel,pfLevel) (This)->lpVtbl->GetLevel(This,nChannel,pfLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioPeakMeter_QueryInterface(IAudioPeakMeter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioPeakMeter_AddRef(IAudioPeakMeter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioPeakMeter_Release(IAudioPeakMeter* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioPeakMeter methods ***/
static inline HRESULT IAudioPeakMeter_GetChannelCount(IAudioPeakMeter* This,UINT *pcChannels) {
    return This->lpVtbl->GetChannelCount(This,pcChannels);
}
static inline HRESULT IAudioPeakMeter_GetLevel(IAudioPeakMeter* This,UINT nChannel,float *pfLevel) {
    return This->lpVtbl->GetLevel(This,nChannel,pfLevel);
}
#endif
#endif

#endif


#endif  /* __IAudioPeakMeter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDeviceSpecificProperty interface
 */
#ifndef __IDeviceSpecificProperty_INTERFACE_DEFINED__
#define __IDeviceSpecificProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDeviceSpecificProperty, 0x3b22bcbf, 0x2586, 0x4af0, 0x85,0x83, 0x20,0x5d,0x39,0x1b,0x80,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3b22bcbf-2586-4af0-8583-205d391b807c")
IDeviceSpecificProperty : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        VARTYPE *pVType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        VARTYPE *pvType,
        DWORD *pcbValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        void *pvValue,
        DWORD cbValue,
        LPCGUID pguidEventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE Get4BRange(
        LONG *plMin,
        LONG *plMax,
        LONG *plStepping) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDeviceSpecificProperty, 0x3b22bcbf, 0x2586, 0x4af0, 0x85,0x83, 0x20,0x5d,0x39,0x1b,0x80,0x7c)
#endif
#else
typedef struct IDeviceSpecificPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDeviceSpecificProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDeviceSpecificProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDeviceSpecificProperty *This);

    /*** IDeviceSpecificProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IDeviceSpecificProperty *This,
        VARTYPE *pVType);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IDeviceSpecificProperty *This,
        VARTYPE *pvType,
        DWORD *pcbValue);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IDeviceSpecificProperty *This,
        void *pvValue,
        DWORD cbValue,
        LPCGUID pguidEventContext);

    HRESULT (STDMETHODCALLTYPE *Get4BRange)(
        IDeviceSpecificProperty *This,
        LONG *plMin,
        LONG *plMax,
        LONG *plStepping);

    END_INTERFACE
} IDeviceSpecificPropertyVtbl;

interface IDeviceSpecificProperty {
    CONST_VTBL IDeviceSpecificPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDeviceSpecificProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDeviceSpecificProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDeviceSpecificProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IDeviceSpecificProperty methods ***/
#define IDeviceSpecificProperty_GetType(This,pVType) (This)->lpVtbl->GetType(This,pVType)
#define IDeviceSpecificProperty_GetValue(This,pvType,pcbValue) (This)->lpVtbl->GetValue(This,pvType,pcbValue)
#define IDeviceSpecificProperty_SetValue(This,pvValue,cbValue,pguidEventContext) (This)->lpVtbl->SetValue(This,pvValue,cbValue,pguidEventContext)
#define IDeviceSpecificProperty_Get4BRange(This,plMin,plMax,plStepping) (This)->lpVtbl->Get4BRange(This,plMin,plMax,plStepping)
#else
/*** IUnknown methods ***/
static inline HRESULT IDeviceSpecificProperty_QueryInterface(IDeviceSpecificProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDeviceSpecificProperty_AddRef(IDeviceSpecificProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDeviceSpecificProperty_Release(IDeviceSpecificProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IDeviceSpecificProperty methods ***/
static inline HRESULT IDeviceSpecificProperty_GetType(IDeviceSpecificProperty* This,VARTYPE *pVType) {
    return This->lpVtbl->GetType(This,pVType);
}
static inline HRESULT IDeviceSpecificProperty_GetValue(IDeviceSpecificProperty* This,VARTYPE *pvType,DWORD *pcbValue) {
    return This->lpVtbl->GetValue(This,pvType,pcbValue);
}
static inline HRESULT IDeviceSpecificProperty_SetValue(IDeviceSpecificProperty* This,void *pvValue,DWORD cbValue,LPCGUID pguidEventContext) {
    return This->lpVtbl->SetValue(This,pvValue,cbValue,pguidEventContext);
}
static inline HRESULT IDeviceSpecificProperty_Get4BRange(IDeviceSpecificProperty* This,LONG *plMin,LONG *plMax,LONG *plStepping) {
    return This->lpVtbl->Get4BRange(This,plMin,plMax,plStepping);
}
#endif
#endif

#endif


#endif  /* __IDeviceSpecificProperty_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IKsFormatSupport interface
 */
#ifndef __IKsFormatSupport_INTERFACE_DEFINED__
#define __IKsFormatSupport_INTERFACE_DEFINED__

DEFINE_GUID(IID_IKsFormatSupport, 0x3cb4a69d, 0xbb6f, 0x4d2b, 0x95,0xb7, 0x45,0x2d,0x2c,0x15,0x5d,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3cb4a69d-bb6f-4d2b-95b7-452d2c155db5")
IKsFormatSupport : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsFormatSupported(
        PKSDATAFORMAT pKsFormat,
        DWORD cbFormat,
        WINBOOL *pbSupported) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDevicePreferredFormat(
        PKSDATAFORMAT *ppKsFormat) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IKsFormatSupport, 0x3cb4a69d, 0xbb6f, 0x4d2b, 0x95,0xb7, 0x45,0x2d,0x2c,0x15,0x5d,0xb5)
#endif
#else
typedef struct IKsFormatSupportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IKsFormatSupport *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IKsFormatSupport *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IKsFormatSupport *This);

    /*** IKsFormatSupport methods ***/
    HRESULT (STDMETHODCALLTYPE *IsFormatSupported)(
        IKsFormatSupport *This,
        PKSDATAFORMAT pKsFormat,
        DWORD cbFormat,
        WINBOOL *pbSupported);

    HRESULT (STDMETHODCALLTYPE *GetDevicePreferredFormat)(
        IKsFormatSupport *This,
        PKSDATAFORMAT *ppKsFormat);

    END_INTERFACE
} IKsFormatSupportVtbl;

interface IKsFormatSupport {
    CONST_VTBL IKsFormatSupportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IKsFormatSupport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IKsFormatSupport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IKsFormatSupport_Release(This) (This)->lpVtbl->Release(This)
/*** IKsFormatSupport methods ***/
#define IKsFormatSupport_IsFormatSupported(This,pKsFormat,cbFormat,pbSupported) (This)->lpVtbl->IsFormatSupported(This,pKsFormat,cbFormat,pbSupported)
#define IKsFormatSupport_GetDevicePreferredFormat(This,ppKsFormat) (This)->lpVtbl->GetDevicePreferredFormat(This,ppKsFormat)
#else
/*** IUnknown methods ***/
static inline HRESULT IKsFormatSupport_QueryInterface(IKsFormatSupport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IKsFormatSupport_AddRef(IKsFormatSupport* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IKsFormatSupport_Release(IKsFormatSupport* This) {
    return This->lpVtbl->Release(This);
}
/*** IKsFormatSupport methods ***/
static inline HRESULT IKsFormatSupport_IsFormatSupported(IKsFormatSupport* This,PKSDATAFORMAT pKsFormat,DWORD cbFormat,WINBOOL *pbSupported) {
    return This->lpVtbl->IsFormatSupported(This,pKsFormat,cbFormat,pbSupported);
}
static inline HRESULT IKsFormatSupport_GetDevicePreferredFormat(IKsFormatSupport* This,PKSDATAFORMAT *ppKsFormat) {
    return This->lpVtbl->GetDevicePreferredFormat(This,ppKsFormat);
}
#endif
#endif

#endif


#endif  /* __IKsFormatSupport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IKsJackDescription interface
 */
#ifndef __IKsJackDescription_INTERFACE_DEFINED__
#define __IKsJackDescription_INTERFACE_DEFINED__

DEFINE_GUID(IID_IKsJackDescription, 0x4509f757, 0x2d46, 0x4637, 0x8e,0x62, 0xce,0x7d,0xb9,0x44,0xf5,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4509f757-2d46-4637-8e62-ce7db944f57b")
IKsJackDescription : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetJackCount(
        UINT *pcJacks) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetJackDescription(
        UINT nJack,
        KSJACK_DESCRIPTION *pDescription) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IKsJackDescription, 0x4509f757, 0x2d46, 0x4637, 0x8e,0x62, 0xce,0x7d,0xb9,0x44,0xf5,0x7b)
#endif
#else
typedef struct IKsJackDescriptionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IKsJackDescription *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IKsJackDescription *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IKsJackDescription *This);

    /*** IKsJackDescription methods ***/
    HRESULT (STDMETHODCALLTYPE *GetJackCount)(
        IKsJackDescription *This,
        UINT *pcJacks);

    HRESULT (STDMETHODCALLTYPE *GetJackDescription)(
        IKsJackDescription *This,
        UINT nJack,
        KSJACK_DESCRIPTION *pDescription);

    END_INTERFACE
} IKsJackDescriptionVtbl;

interface IKsJackDescription {
    CONST_VTBL IKsJackDescriptionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IKsJackDescription_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IKsJackDescription_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IKsJackDescription_Release(This) (This)->lpVtbl->Release(This)
/*** IKsJackDescription methods ***/
#define IKsJackDescription_GetJackCount(This,pcJacks) (This)->lpVtbl->GetJackCount(This,pcJacks)
#define IKsJackDescription_GetJackDescription(This,nJack,pDescription) (This)->lpVtbl->GetJackDescription(This,nJack,pDescription)
#else
/*** IUnknown methods ***/
static inline HRESULT IKsJackDescription_QueryInterface(IKsJackDescription* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IKsJackDescription_AddRef(IKsJackDescription* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IKsJackDescription_Release(IKsJackDescription* This) {
    return This->lpVtbl->Release(This);
}
/*** IKsJackDescription methods ***/
static inline HRESULT IKsJackDescription_GetJackCount(IKsJackDescription* This,UINT *pcJacks) {
    return This->lpVtbl->GetJackCount(This,pcJacks);
}
static inline HRESULT IKsJackDescription_GetJackDescription(IKsJackDescription* This,UINT nJack,KSJACK_DESCRIPTION *pDescription) {
    return This->lpVtbl->GetJackDescription(This,nJack,pDescription);
}
#endif
#endif

#endif


#endif  /* __IKsJackDescription_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IKsJackDescription2 interface
 */
#ifndef __IKsJackDescription2_INTERFACE_DEFINED__
#define __IKsJackDescription2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IKsJackDescription2, 0x478f3a9b, 0xe0c9, 0x4827, 0x92,0x28, 0x6f,0x55,0x05,0xff,0xe7,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("478f3a9b-e0c9-4827-9228-6f5505ffe76a")
IKsJackDescription2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetJackCount(
        UINT *pcJacks) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetJackDescription2(
        UINT nJack,
        KSJACK_DESCRIPTION2 *pDescription2) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IKsJackDescription2, 0x478f3a9b, 0xe0c9, 0x4827, 0x92,0x28, 0x6f,0x55,0x05,0xff,0xe7,0x6a)
#endif
#else
typedef struct IKsJackDescription2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IKsJackDescription2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IKsJackDescription2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IKsJackDescription2 *This);

    /*** IKsJackDescription2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetJackCount)(
        IKsJackDescription2 *This,
        UINT *pcJacks);

    HRESULT (STDMETHODCALLTYPE *GetJackDescription2)(
        IKsJackDescription2 *This,
        UINT nJack,
        KSJACK_DESCRIPTION2 *pDescription2);

    END_INTERFACE
} IKsJackDescription2Vtbl;

interface IKsJackDescription2 {
    CONST_VTBL IKsJackDescription2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IKsJackDescription2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IKsJackDescription2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IKsJackDescription2_Release(This) (This)->lpVtbl->Release(This)
/*** IKsJackDescription2 methods ***/
#define IKsJackDescription2_GetJackCount(This,pcJacks) (This)->lpVtbl->GetJackCount(This,pcJacks)
#define IKsJackDescription2_GetJackDescription2(This,nJack,pDescription2) (This)->lpVtbl->GetJackDescription2(This,nJack,pDescription2)
#else
/*** IUnknown methods ***/
static inline HRESULT IKsJackDescription2_QueryInterface(IKsJackDescription2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IKsJackDescription2_AddRef(IKsJackDescription2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IKsJackDescription2_Release(IKsJackDescription2* This) {
    return This->lpVtbl->Release(This);
}
/*** IKsJackDescription2 methods ***/
static inline HRESULT IKsJackDescription2_GetJackCount(IKsJackDescription2* This,UINT *pcJacks) {
    return This->lpVtbl->GetJackCount(This,pcJacks);
}
static inline HRESULT IKsJackDescription2_GetJackDescription2(IKsJackDescription2* This,UINT nJack,KSJACK_DESCRIPTION2 *pDescription2) {
    return This->lpVtbl->GetJackDescription2(This,nJack,pDescription2);
}
#endif
#endif

#endif


#endif  /* __IKsJackDescription2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IKsJackSinkInformation interface
 */
#ifndef __IKsJackSinkInformation_INTERFACE_DEFINED__
#define __IKsJackSinkInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IKsJackSinkInformation, 0xd9bd72ed, 0x290f, 0x4581, 0x9f,0xf3, 0x61,0x02,0x7a,0x8f,0xe5,0x32);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d9bd72ed-290f-4581-9ff3-61027a8fe532")
IKsJackSinkInformation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetJackSinkInformation(
        KSJACK_SINK_INFORMATION *pJackSinkInformation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IKsJackSinkInformation, 0xd9bd72ed, 0x290f, 0x4581, 0x9f,0xf3, 0x61,0x02,0x7a,0x8f,0xe5,0x32)
#endif
#else
typedef struct IKsJackSinkInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IKsJackSinkInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IKsJackSinkInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IKsJackSinkInformation *This);

    /*** IKsJackSinkInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *GetJackSinkInformation)(
        IKsJackSinkInformation *This,
        KSJACK_SINK_INFORMATION *pJackSinkInformation);

    END_INTERFACE
} IKsJackSinkInformationVtbl;

interface IKsJackSinkInformation {
    CONST_VTBL IKsJackSinkInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IKsJackSinkInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IKsJackSinkInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IKsJackSinkInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IKsJackSinkInformation methods ***/
#define IKsJackSinkInformation_GetJackSinkInformation(This,pJackSinkInformation) (This)->lpVtbl->GetJackSinkInformation(This,pJackSinkInformation)
#else
/*** IUnknown methods ***/
static inline HRESULT IKsJackSinkInformation_QueryInterface(IKsJackSinkInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IKsJackSinkInformation_AddRef(IKsJackSinkInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IKsJackSinkInformation_Release(IKsJackSinkInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IKsJackSinkInformation methods ***/
static inline HRESULT IKsJackSinkInformation_GetJackSinkInformation(IKsJackSinkInformation* This,KSJACK_SINK_INFORMATION *pJackSinkInformation) {
    return This->lpVtbl->GetJackSinkInformation(This,pJackSinkInformation);
}
#endif
#endif

#endif


#endif  /* __IKsJackSinkInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPartsList interface
 */
#ifndef __IPartsList_INTERFACE_DEFINED__
#define __IPartsList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPartsList, 0x6daa848c, 0x5eb0, 0x45cc, 0xae,0xa5, 0x99,0x8a,0x2c,0xda,0x1f,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6daa848c-5eb0-45cc-aea5-998a2cda1ffb")
IPartsList : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT *pCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPart(
        UINT nIndex,
        IPart **ppPart) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPartsList, 0x6daa848c, 0x5eb0, 0x45cc, 0xae,0xa5, 0x99,0x8a,0x2c,0xda,0x1f,0xfb)
#endif
#else
typedef struct IPartsListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPartsList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPartsList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPartsList *This);

    /*** IPartsList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPartsList *This,
        UINT *pCount);

    HRESULT (STDMETHODCALLTYPE *GetPart)(
        IPartsList *This,
        UINT nIndex,
        IPart **ppPart);

    END_INTERFACE
} IPartsListVtbl;

interface IPartsList {
    CONST_VTBL IPartsListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPartsList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPartsList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPartsList_Release(This) (This)->lpVtbl->Release(This)
/*** IPartsList methods ***/
#define IPartsList_GetCount(This,pCount) (This)->lpVtbl->GetCount(This,pCount)
#define IPartsList_GetPart(This,nIndex,ppPart) (This)->lpVtbl->GetPart(This,nIndex,ppPart)
#else
/*** IUnknown methods ***/
static inline HRESULT IPartsList_QueryInterface(IPartsList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPartsList_AddRef(IPartsList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPartsList_Release(IPartsList* This) {
    return This->lpVtbl->Release(This);
}
/*** IPartsList methods ***/
static inline HRESULT IPartsList_GetCount(IPartsList* This,UINT *pCount) {
    return This->lpVtbl->GetCount(This,pCount);
}
static inline HRESULT IPartsList_GetPart(IPartsList* This,UINT nIndex,IPart **ppPart) {
    return This->lpVtbl->GetPart(This,nIndex,ppPart);
}
#endif
#endif

#endif


#endif  /* __IPartsList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPart interface
 */
#ifndef __IPart_INTERFACE_DEFINED__
#define __IPart_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPart, 0xae2de0e4, 0x5bca, 0x4f2d, 0xaa,0x46, 0x5d,0x13,0xf8,0xfd,0xb3,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae2de0e4-5bca-4f2d-aa46-5d13f8fdb3a9")
IPart : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetName(
        LPWSTR *ppwstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocalId(
        UINT *pnId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlobalId(
        LPWSTR *ppwstrGlobalId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPartType(
        PartType *pPartType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubType(
        GUID *pSubType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetControlInterfaceCount(
        UINT *pCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetControlInterface(
        UINT nIndex,
        IControlInterface **ppInterfaceDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumPartsIncoming(
        IPartsList **ppParts) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumPartsOutgoing(
        IPartsList **ppParts) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTopologyObjects(
        IDeviceTopology **ppTopology) = 0;

    virtual HRESULT STDMETHODCALLTYPE Activate(
        DWORD dwClsContext,
        REFIID refiid,
        void **ppvObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterControlChangeCallback(
        REFGUID riid,
        IControlChangeNotify *pNotify) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterControlChangeCallback(
        IControlChangeNotify *pNotify) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPart, 0xae2de0e4, 0x5bca, 0x4f2d, 0xaa,0x46, 0x5d,0x13,0xf8,0xfd,0xb3,0xa9)
#endif
#else
typedef struct IPartVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPart *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPart *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPart *This);

    /*** IPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IPart *This,
        LPWSTR *ppwstrName);

    HRESULT (STDMETHODCALLTYPE *GetLocalId)(
        IPart *This,
        UINT *pnId);

    HRESULT (STDMETHODCALLTYPE *GetGlobalId)(
        IPart *This,
        LPWSTR *ppwstrGlobalId);

    HRESULT (STDMETHODCALLTYPE *GetPartType)(
        IPart *This,
        PartType *pPartType);

    HRESULT (STDMETHODCALLTYPE *GetSubType)(
        IPart *This,
        GUID *pSubType);

    HRESULT (STDMETHODCALLTYPE *GetControlInterfaceCount)(
        IPart *This,
        UINT *pCount);

    HRESULT (STDMETHODCALLTYPE *GetControlInterface)(
        IPart *This,
        UINT nIndex,
        IControlInterface **ppInterfaceDesc);

    HRESULT (STDMETHODCALLTYPE *EnumPartsIncoming)(
        IPart *This,
        IPartsList **ppParts);

    HRESULT (STDMETHODCALLTYPE *EnumPartsOutgoing)(
        IPart *This,
        IPartsList **ppParts);

    HRESULT (STDMETHODCALLTYPE *GetTopologyObjects)(
        IPart *This,
        IDeviceTopology **ppTopology);

    HRESULT (STDMETHODCALLTYPE *Activate)(
        IPart *This,
        DWORD dwClsContext,
        REFIID refiid,
        void **ppvObject);

    HRESULT (STDMETHODCALLTYPE *RegisterControlChangeCallback)(
        IPart *This,
        REFGUID riid,
        IControlChangeNotify *pNotify);

    HRESULT (STDMETHODCALLTYPE *UnregisterControlChangeCallback)(
        IPart *This,
        IControlChangeNotify *pNotify);

    END_INTERFACE
} IPartVtbl;

interface IPart {
    CONST_VTBL IPartVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPart_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPart_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPart_Release(This) (This)->lpVtbl->Release(This)
/*** IPart methods ***/
#define IPart_GetName(This,ppwstrName) (This)->lpVtbl->GetName(This,ppwstrName)
#define IPart_GetLocalId(This,pnId) (This)->lpVtbl->GetLocalId(This,pnId)
#define IPart_GetGlobalId(This,ppwstrGlobalId) (This)->lpVtbl->GetGlobalId(This,ppwstrGlobalId)
#define IPart_GetPartType(This,pPartType) (This)->lpVtbl->GetPartType(This,pPartType)
#define IPart_GetSubType(This,pSubType) (This)->lpVtbl->GetSubType(This,pSubType)
#define IPart_GetControlInterfaceCount(This,pCount) (This)->lpVtbl->GetControlInterfaceCount(This,pCount)
#define IPart_GetControlInterface(This,nIndex,ppInterfaceDesc) (This)->lpVtbl->GetControlInterface(This,nIndex,ppInterfaceDesc)
#define IPart_EnumPartsIncoming(This,ppParts) (This)->lpVtbl->EnumPartsIncoming(This,ppParts)
#define IPart_EnumPartsOutgoing(This,ppParts) (This)->lpVtbl->EnumPartsOutgoing(This,ppParts)
#define IPart_GetTopologyObjects(This,ppTopology) (This)->lpVtbl->GetTopologyObjects(This,ppTopology)
#define IPart_Activate(This,dwClsContext,refiid,ppvObject) (This)->lpVtbl->Activate(This,dwClsContext,refiid,ppvObject)
#define IPart_RegisterControlChangeCallback(This,riid,pNotify) (This)->lpVtbl->RegisterControlChangeCallback(This,riid,pNotify)
#define IPart_UnregisterControlChangeCallback(This,pNotify) (This)->lpVtbl->UnregisterControlChangeCallback(This,pNotify)
#else
/*** IUnknown methods ***/
static inline HRESULT IPart_QueryInterface(IPart* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPart_AddRef(IPart* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPart_Release(IPart* This) {
    return This->lpVtbl->Release(This);
}
/*** IPart methods ***/
static inline HRESULT IPart_GetName(IPart* This,LPWSTR *ppwstrName) {
    return This->lpVtbl->GetName(This,ppwstrName);
}
static inline HRESULT IPart_GetLocalId(IPart* This,UINT *pnId) {
    return This->lpVtbl->GetLocalId(This,pnId);
}
static inline HRESULT IPart_GetGlobalId(IPart* This,LPWSTR *ppwstrGlobalId) {
    return This->lpVtbl->GetGlobalId(This,ppwstrGlobalId);
}
static inline HRESULT IPart_GetPartType(IPart* This,PartType *pPartType) {
    return This->lpVtbl->GetPartType(This,pPartType);
}
static inline HRESULT IPart_GetSubType(IPart* This,GUID *pSubType) {
    return This->lpVtbl->GetSubType(This,pSubType);
}
static inline HRESULT IPart_GetControlInterfaceCount(IPart* This,UINT *pCount) {
    return This->lpVtbl->GetControlInterfaceCount(This,pCount);
}
static inline HRESULT IPart_GetControlInterface(IPart* This,UINT nIndex,IControlInterface **ppInterfaceDesc) {
    return This->lpVtbl->GetControlInterface(This,nIndex,ppInterfaceDesc);
}
static inline HRESULT IPart_EnumPartsIncoming(IPart* This,IPartsList **ppParts) {
    return This->lpVtbl->EnumPartsIncoming(This,ppParts);
}
static inline HRESULT IPart_EnumPartsOutgoing(IPart* This,IPartsList **ppParts) {
    return This->lpVtbl->EnumPartsOutgoing(This,ppParts);
}
static inline HRESULT IPart_GetTopologyObjects(IPart* This,IDeviceTopology **ppTopology) {
    return This->lpVtbl->GetTopologyObjects(This,ppTopology);
}
static inline HRESULT IPart_Activate(IPart* This,DWORD dwClsContext,REFIID refiid,void **ppvObject) {
    return This->lpVtbl->Activate(This,dwClsContext,refiid,ppvObject);
}
static inline HRESULT IPart_RegisterControlChangeCallback(IPart* This,REFGUID riid,IControlChangeNotify *pNotify) {
    return This->lpVtbl->RegisterControlChangeCallback(This,riid,pNotify);
}
static inline HRESULT IPart_UnregisterControlChangeCallback(IPart* This,IControlChangeNotify *pNotify) {
    return This->lpVtbl->UnregisterControlChangeCallback(This,pNotify);
}
#endif
#endif

#endif


#endif  /* __IPart_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IConnector interface
 */
#ifndef __IConnector_INTERFACE_DEFINED__
#define __IConnector_INTERFACE_DEFINED__

DEFINE_GUID(IID_IConnector, 0x9c2c4058, 0x23f5, 0x41de, 0x87,0x7a, 0xdf,0x3a,0xf2,0x36,0xa0,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c2c4058-23f5-41de-877a-df3af236a09e")
IConnector : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        ConnectorType *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDataFlow(
        DataFlow *pFlow) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConnectTo(
        IConnector *pConnectTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE Disconnect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsConnected(
        WINBOOL *pbConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectedTo(
        IConnector **ppConTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectorIdConnectedTo(
        LPWSTR *ppwstrConnectorId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceIdConnectedTo(
        LPWSTR *ppwstrDeviceId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IConnector, 0x9c2c4058, 0x23f5, 0x41de, 0x87,0x7a, 0xdf,0x3a,0xf2,0x36,0xa0,0x9e)
#endif
#else
typedef struct IConnectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IConnector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IConnector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IConnector *This);

    /*** IConnector methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IConnector *This,
        ConnectorType *pType);

    HRESULT (STDMETHODCALLTYPE *GetDataFlow)(
        IConnector *This,
        DataFlow *pFlow);

    HRESULT (STDMETHODCALLTYPE *ConnectTo)(
        IConnector *This,
        IConnector *pConnectTo);

    HRESULT (STDMETHODCALLTYPE *Disconnect)(
        IConnector *This);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IConnector *This,
        WINBOOL *pbConnected);

    HRESULT (STDMETHODCALLTYPE *GetConnectedTo)(
        IConnector *This,
        IConnector **ppConTo);

    HRESULT (STDMETHODCALLTYPE *GetConnectorIdConnectedTo)(
        IConnector *This,
        LPWSTR *ppwstrConnectorId);

    HRESULT (STDMETHODCALLTYPE *GetDeviceIdConnectedTo)(
        IConnector *This,
        LPWSTR *ppwstrDeviceId);

    END_INTERFACE
} IConnectorVtbl;

interface IConnector {
    CONST_VTBL IConnectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IConnector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConnector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConnector_Release(This) (This)->lpVtbl->Release(This)
/*** IConnector methods ***/
#define IConnector_GetType(This,pType) (This)->lpVtbl->GetType(This,pType)
#define IConnector_GetDataFlow(This,pFlow) (This)->lpVtbl->GetDataFlow(This,pFlow)
#define IConnector_ConnectTo(This,pConnectTo) (This)->lpVtbl->ConnectTo(This,pConnectTo)
#define IConnector_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#define IConnector_IsConnected(This,pbConnected) (This)->lpVtbl->IsConnected(This,pbConnected)
#define IConnector_GetConnectedTo(This,ppConTo) (This)->lpVtbl->GetConnectedTo(This,ppConTo)
#define IConnector_GetConnectorIdConnectedTo(This,ppwstrConnectorId) (This)->lpVtbl->GetConnectorIdConnectedTo(This,ppwstrConnectorId)
#define IConnector_GetDeviceIdConnectedTo(This,ppwstrDeviceId) (This)->lpVtbl->GetDeviceIdConnectedTo(This,ppwstrDeviceId)
#else
/*** IUnknown methods ***/
static inline HRESULT IConnector_QueryInterface(IConnector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IConnector_AddRef(IConnector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IConnector_Release(IConnector* This) {
    return This->lpVtbl->Release(This);
}
/*** IConnector methods ***/
static inline HRESULT IConnector_GetType(IConnector* This,ConnectorType *pType) {
    return This->lpVtbl->GetType(This,pType);
}
static inline HRESULT IConnector_GetDataFlow(IConnector* This,DataFlow *pFlow) {
    return This->lpVtbl->GetDataFlow(This,pFlow);
}
static inline HRESULT IConnector_ConnectTo(IConnector* This,IConnector *pConnectTo) {
    return This->lpVtbl->ConnectTo(This,pConnectTo);
}
static inline HRESULT IConnector_Disconnect(IConnector* This) {
    return This->lpVtbl->Disconnect(This);
}
static inline HRESULT IConnector_IsConnected(IConnector* This,WINBOOL *pbConnected) {
    return This->lpVtbl->IsConnected(This,pbConnected);
}
static inline HRESULT IConnector_GetConnectedTo(IConnector* This,IConnector **ppConTo) {
    return This->lpVtbl->GetConnectedTo(This,ppConTo);
}
static inline HRESULT IConnector_GetConnectorIdConnectedTo(IConnector* This,LPWSTR *ppwstrConnectorId) {
    return This->lpVtbl->GetConnectorIdConnectedTo(This,ppwstrConnectorId);
}
static inline HRESULT IConnector_GetDeviceIdConnectedTo(IConnector* This,LPWSTR *ppwstrDeviceId) {
    return This->lpVtbl->GetDeviceIdConnectedTo(This,ppwstrDeviceId);
}
#endif
#endif

#endif


#endif  /* __IConnector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISubUnit interface
 */
#ifndef __ISubUnit_INTERFACE_DEFINED__
#define __ISubUnit_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISubUnit, 0x82149a85, 0xdba6, 0x4487, 0x86,0xbb, 0xea,0x8f,0x7f,0xef,0xcc,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("82149a85-dba6-4487-86bb-ea8f7fefcc71")
ISubUnit : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISubUnit, 0x82149a85, 0xdba6, 0x4487, 0x86,0xbb, 0xea,0x8f,0x7f,0xef,0xcc,0x71)
#endif
#else
typedef struct ISubUnitVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISubUnit *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISubUnit *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISubUnit *This);

    END_INTERFACE
} ISubUnitVtbl;

interface ISubUnit {
    CONST_VTBL ISubUnitVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISubUnit_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISubUnit_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISubUnit_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISubUnit_QueryInterface(ISubUnit* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISubUnit_AddRef(ISubUnit* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISubUnit_Release(ISubUnit* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __ISubUnit_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IControlInterface interface
 */
#ifndef __IControlInterface_INTERFACE_DEFINED__
#define __IControlInterface_INTERFACE_DEFINED__

DEFINE_GUID(IID_IControlInterface, 0x45d37c3f, 0x5140, 0x444a, 0xae,0x24, 0x40,0x07,0x89,0xf3,0xcb,0xf3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("45d37c3f-5140-444a-ae24-400789f3cbf3")
IControlInterface : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetName(
        LPWSTR *ppwstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIID(
        GUID *pIID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IControlInterface, 0x45d37c3f, 0x5140, 0x444a, 0xae,0x24, 0x40,0x07,0x89,0xf3,0xcb,0xf3)
#endif
#else
typedef struct IControlInterfaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IControlInterface *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IControlInterface *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IControlInterface *This);

    /*** IControlInterface methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IControlInterface *This,
        LPWSTR *ppwstrName);

    HRESULT (STDMETHODCALLTYPE *GetIID)(
        IControlInterface *This,
        GUID *pIID);

    END_INTERFACE
} IControlInterfaceVtbl;

interface IControlInterface {
    CONST_VTBL IControlInterfaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IControlInterface_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IControlInterface_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IControlInterface_Release(This) (This)->lpVtbl->Release(This)
/*** IControlInterface methods ***/
#define IControlInterface_GetName(This,ppwstrName) (This)->lpVtbl->GetName(This,ppwstrName)
#define IControlInterface_GetIID(This,pIID) (This)->lpVtbl->GetIID(This,pIID)
#else
/*** IUnknown methods ***/
static inline HRESULT IControlInterface_QueryInterface(IControlInterface* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IControlInterface_AddRef(IControlInterface* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IControlInterface_Release(IControlInterface* This) {
    return This->lpVtbl->Release(This);
}
/*** IControlInterface methods ***/
static inline HRESULT IControlInterface_GetName(IControlInterface* This,LPWSTR *ppwstrName) {
    return This->lpVtbl->GetName(This,ppwstrName);
}
static inline HRESULT IControlInterface_GetIID(IControlInterface* This,GUID *pIID) {
    return This->lpVtbl->GetIID(This,pIID);
}
#endif
#endif

#endif


#endif  /* __IControlInterface_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IControlChangeNotify interface
 */
#ifndef __IControlChangeNotify_INTERFACE_DEFINED__
#define __IControlChangeNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IControlChangeNotify, 0xa09513ed, 0xc709, 0x4d21, 0xbd,0x7b, 0x5f,0x34,0xc4,0x7f,0x39,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a09513ed-c709-4d21-bd7b-5f34c47f3947")
IControlChangeNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnNotify(
        DWORD dwSenderProcessId,
        LPCGUID ppguidEventContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IControlChangeNotify, 0xa09513ed, 0xc709, 0x4d21, 0xbd,0x7b, 0x5f,0x34,0xc4,0x7f,0x39,0x47)
#endif
#else
typedef struct IControlChangeNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IControlChangeNotify *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IControlChangeNotify *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IControlChangeNotify *This);

    /*** IControlChangeNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *OnNotify)(
        IControlChangeNotify *This,
        DWORD dwSenderProcessId,
        LPCGUID ppguidEventContext);

    END_INTERFACE
} IControlChangeNotifyVtbl;

interface IControlChangeNotify {
    CONST_VTBL IControlChangeNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IControlChangeNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IControlChangeNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IControlChangeNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IControlChangeNotify methods ***/
#define IControlChangeNotify_OnNotify(This,dwSenderProcessId,ppguidEventContext) (This)->lpVtbl->OnNotify(This,dwSenderProcessId,ppguidEventContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IControlChangeNotify_QueryInterface(IControlChangeNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IControlChangeNotify_AddRef(IControlChangeNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IControlChangeNotify_Release(IControlChangeNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IControlChangeNotify methods ***/
static inline HRESULT IControlChangeNotify_OnNotify(IControlChangeNotify* This,DWORD dwSenderProcessId,LPCGUID ppguidEventContext) {
    return This->lpVtbl->OnNotify(This,dwSenderProcessId,ppguidEventContext);
}
#endif
#endif

#endif


#endif  /* __IControlChangeNotify_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDeviceTopology interface
 */
#ifndef __IDeviceTopology_INTERFACE_DEFINED__
#define __IDeviceTopology_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDeviceTopology, 0x2a07407e, 0x6497, 0x4a18, 0x97,0x87, 0x32,0xf7,0x9b,0xd0,0xd9,0x8f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2a07407e-6497-4a18-9787-32f79bd0d98f")
IDeviceTopology : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetConnectorCount(
        UINT *pCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnector(
        UINT nIndex,
        IConnector **ppConnector) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubunitCount(
        UINT *pCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubunit(
        UINT nIndex,
        ISubUnit **ppConnector) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPartById(
        UINT nId,
        IPart **ppPart) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceId(
        LPWSTR *ppwstrDeviceId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignalPath(
        IPart *pIPartFrom,
        IPart *pIPartTo,
        WINBOOL bRejectMixedPaths,
        IPartsList **ppParts) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDeviceTopology, 0x2a07407e, 0x6497, 0x4a18, 0x97,0x87, 0x32,0xf7,0x9b,0xd0,0xd9,0x8f)
#endif
#else
typedef struct IDeviceTopologyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDeviceTopology *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDeviceTopology *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDeviceTopology *This);

    /*** IDeviceTopology methods ***/
    HRESULT (STDMETHODCALLTYPE *GetConnectorCount)(
        IDeviceTopology *This,
        UINT *pCount);

    HRESULT (STDMETHODCALLTYPE *GetConnector)(
        IDeviceTopology *This,
        UINT nIndex,
        IConnector **ppConnector);

    HRESULT (STDMETHODCALLTYPE *GetSubunitCount)(
        IDeviceTopology *This,
        UINT *pCount);

    HRESULT (STDMETHODCALLTYPE *GetSubunit)(
        IDeviceTopology *This,
        UINT nIndex,
        ISubUnit **ppConnector);

    HRESULT (STDMETHODCALLTYPE *GetPartById)(
        IDeviceTopology *This,
        UINT nId,
        IPart **ppPart);

    HRESULT (STDMETHODCALLTYPE *GetDeviceId)(
        IDeviceTopology *This,
        LPWSTR *ppwstrDeviceId);

    HRESULT (STDMETHODCALLTYPE *GetSignalPath)(
        IDeviceTopology *This,
        IPart *pIPartFrom,
        IPart *pIPartTo,
        WINBOOL bRejectMixedPaths,
        IPartsList **ppParts);

    END_INTERFACE
} IDeviceTopologyVtbl;

interface IDeviceTopology {
    CONST_VTBL IDeviceTopologyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDeviceTopology_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDeviceTopology_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDeviceTopology_Release(This) (This)->lpVtbl->Release(This)
/*** IDeviceTopology methods ***/
#define IDeviceTopology_GetConnectorCount(This,pCount) (This)->lpVtbl->GetConnectorCount(This,pCount)
#define IDeviceTopology_GetConnector(This,nIndex,ppConnector) (This)->lpVtbl->GetConnector(This,nIndex,ppConnector)
#define IDeviceTopology_GetSubunitCount(This,pCount) (This)->lpVtbl->GetSubunitCount(This,pCount)
#define IDeviceTopology_GetSubunit(This,nIndex,ppConnector) (This)->lpVtbl->GetSubunit(This,nIndex,ppConnector)
#define IDeviceTopology_GetPartById(This,nId,ppPart) (This)->lpVtbl->GetPartById(This,nId,ppPart)
#define IDeviceTopology_GetDeviceId(This,ppwstrDeviceId) (This)->lpVtbl->GetDeviceId(This,ppwstrDeviceId)
#define IDeviceTopology_GetSignalPath(This,pIPartFrom,pIPartTo,bRejectMixedPaths,ppParts) (This)->lpVtbl->GetSignalPath(This,pIPartFrom,pIPartTo,bRejectMixedPaths,ppParts)
#else
/*** IUnknown methods ***/
static inline HRESULT IDeviceTopology_QueryInterface(IDeviceTopology* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDeviceTopology_AddRef(IDeviceTopology* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDeviceTopology_Release(IDeviceTopology* This) {
    return This->lpVtbl->Release(This);
}
/*** IDeviceTopology methods ***/
static inline HRESULT IDeviceTopology_GetConnectorCount(IDeviceTopology* This,UINT *pCount) {
    return This->lpVtbl->GetConnectorCount(This,pCount);
}
static inline HRESULT IDeviceTopology_GetConnector(IDeviceTopology* This,UINT nIndex,IConnector **ppConnector) {
    return This->lpVtbl->GetConnector(This,nIndex,ppConnector);
}
static inline HRESULT IDeviceTopology_GetSubunitCount(IDeviceTopology* This,UINT *pCount) {
    return This->lpVtbl->GetSubunitCount(This,pCount);
}
static inline HRESULT IDeviceTopology_GetSubunit(IDeviceTopology* This,UINT nIndex,ISubUnit **ppConnector) {
    return This->lpVtbl->GetSubunit(This,nIndex,ppConnector);
}
static inline HRESULT IDeviceTopology_GetPartById(IDeviceTopology* This,UINT nId,IPart **ppPart) {
    return This->lpVtbl->GetPartById(This,nId,ppPart);
}
static inline HRESULT IDeviceTopology_GetDeviceId(IDeviceTopology* This,LPWSTR *ppwstrDeviceId) {
    return This->lpVtbl->GetDeviceId(This,ppwstrDeviceId);
}
static inline HRESULT IDeviceTopology_GetSignalPath(IDeviceTopology* This,IPart *pIPartFrom,IPart *pIPartTo,WINBOOL bRejectMixedPaths,IPartsList **ppParts) {
    return This->lpVtbl->GetSignalPath(This,pIPartFrom,pIPartTo,bRejectMixedPaths,ppParts);
}
#endif
#endif

#endif


#endif  /* __IDeviceTopology_INTERFACE_DEFINED__ */

#ifndef __DevTopologyLib_LIBRARY_DEFINED__
#define __DevTopologyLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_DevTopologyLib, 0x51b9a01d, 0x8181, 0x4363, 0xb5,0x9c, 0xe6,0x78,0xf4,0x76,0xdd,0x0e);

/*****************************************************************************
 * DeviceTopology coclass
 */

DEFINE_GUID(CLSID_DeviceTopology, 0x1df639d0, 0x5ec1, 0x47aa, 0x93,0x79, 0x82,0x8d,0xc1,0xaa,0x8c,0x59);

#ifdef __cplusplus
class DECLSPEC_UUID("1df639d0-5ec1-47aa-9379-828dc1aa8c59") DeviceTopology;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DeviceTopology, 0x1df639d0, 0x5ec1, 0x47aa, 0x93,0x79, 0x82,0x8d,0xc1,0xaa,0x8c,0x59)
#endif
#endif

#endif /* __DevTopologyLib_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __devicetopology_h__ */
