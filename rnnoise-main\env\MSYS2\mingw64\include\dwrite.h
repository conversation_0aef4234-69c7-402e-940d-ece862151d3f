/*** Autogenerated by WIDL 10.12 from include/dwrite.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dwrite_h__
#define __dwrite_h__

/* Forward declarations */

#ifndef __IDWriteFontFileStream_FWD_DEFINED__
#define __IDWriteFontFileStream_FWD_DEFINED__
typedef interface IDWriteFontFileStream IDWriteFontFileStream;
#ifdef __cplusplus
interface IDWriteFontFileStream;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFileLoader_FWD_DEFINED__
#define __IDWriteFontFileLoader_FWD_DEFINED__
typedef interface IDWriteFontFileLoader IDWriteFontFileLoader;
#ifdef __cplusplus
interface IDWriteFontFileLoader;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteLocalFontFileLoader_FWD_DEFINED__
#define __IDWriteLocalFontFileLoader_FWD_DEFINED__
typedef interface IDWriteLocalFontFileLoader IDWriteLocalFontFileLoader;
#ifdef __cplusplus
interface IDWriteLocalFontFileLoader;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFile_FWD_DEFINED__
#define __IDWriteFontFile_FWD_DEFINED__
typedef interface IDWriteFontFile IDWriteFontFile;
#ifdef __cplusplus
interface IDWriteFontFile;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFileEnumerator_FWD_DEFINED__
#define __IDWriteFontFileEnumerator_FWD_DEFINED__
typedef interface IDWriteFontFileEnumerator IDWriteFontFileEnumerator;
#ifdef __cplusplus
interface IDWriteFontFileEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontCollectionLoader_FWD_DEFINED__
#define __IDWriteFontCollectionLoader_FWD_DEFINED__
typedef interface IDWriteFontCollectionLoader IDWriteFontCollectionLoader;
#ifdef __cplusplus
interface IDWriteFontCollectionLoader;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteLocalizedStrings_FWD_DEFINED__
#define __IDWriteLocalizedStrings_FWD_DEFINED__
typedef interface IDWriteLocalizedStrings IDWriteLocalizedStrings;
#ifdef __cplusplus
interface IDWriteLocalizedStrings;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteRenderingParams_FWD_DEFINED__
#define __IDWriteRenderingParams_FWD_DEFINED__
typedef interface IDWriteRenderingParams IDWriteRenderingParams;
#ifdef __cplusplus
interface IDWriteRenderingParams;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFace_FWD_DEFINED__
#define __IDWriteFontFace_FWD_DEFINED__
typedef interface IDWriteFontFace IDWriteFontFace;
#ifdef __cplusplus
interface IDWriteFontFace;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFont_FWD_DEFINED__
#define __IDWriteFont_FWD_DEFINED__
typedef interface IDWriteFont IDWriteFont;
#ifdef __cplusplus
interface IDWriteFont;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontList_FWD_DEFINED__
#define __IDWriteFontList_FWD_DEFINED__
typedef interface IDWriteFontList IDWriteFontList;
#ifdef __cplusplus
interface IDWriteFontList;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFamily_FWD_DEFINED__
#define __IDWriteFontFamily_FWD_DEFINED__
typedef interface IDWriteFontFamily IDWriteFontFamily;
#ifdef __cplusplus
interface IDWriteFontFamily;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontCollection_FWD_DEFINED__
#define __IDWriteFontCollection_FWD_DEFINED__
typedef interface IDWriteFontCollection IDWriteFontCollection;
#ifdef __cplusplus
interface IDWriteFontCollection;
#endif /* __cplusplus */
#endif

#ifndef __IDWritePixelSnapping_FWD_DEFINED__
#define __IDWritePixelSnapping_FWD_DEFINED__
typedef interface IDWritePixelSnapping IDWritePixelSnapping;
#ifdef __cplusplus
interface IDWritePixelSnapping;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextRenderer_FWD_DEFINED__
#define __IDWriteTextRenderer_FWD_DEFINED__
typedef interface IDWriteTextRenderer IDWriteTextRenderer;
#ifdef __cplusplus
interface IDWriteTextRenderer;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteInlineObject_FWD_DEFINED__
#define __IDWriteInlineObject_FWD_DEFINED__
typedef interface IDWriteInlineObject IDWriteInlineObject;
#ifdef __cplusplus
interface IDWriteInlineObject;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextFormat_FWD_DEFINED__
#define __IDWriteTextFormat_FWD_DEFINED__
typedef interface IDWriteTextFormat IDWriteTextFormat;
#ifdef __cplusplus
interface IDWriteTextFormat;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTypography_FWD_DEFINED__
#define __IDWriteTypography_FWD_DEFINED__
typedef interface IDWriteTypography IDWriteTypography;
#ifdef __cplusplus
interface IDWriteTypography;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteBitmapRenderTarget_FWD_DEFINED__
#define __IDWriteBitmapRenderTarget_FWD_DEFINED__
typedef interface IDWriteBitmapRenderTarget IDWriteBitmapRenderTarget;
#ifdef __cplusplus
interface IDWriteBitmapRenderTarget;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteGdiInterop_FWD_DEFINED__
#define __IDWriteGdiInterop_FWD_DEFINED__
typedef interface IDWriteGdiInterop IDWriteGdiInterop;
#ifdef __cplusplus
interface IDWriteGdiInterop;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextLayout_FWD_DEFINED__
#define __IDWriteTextLayout_FWD_DEFINED__
typedef interface IDWriteTextLayout IDWriteTextLayout;
#ifdef __cplusplus
interface IDWriteTextLayout;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteNumberSubstitution_FWD_DEFINED__
#define __IDWriteNumberSubstitution_FWD_DEFINED__
typedef interface IDWriteNumberSubstitution IDWriteNumberSubstitution;
#ifdef __cplusplus
interface IDWriteNumberSubstitution;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalysisSource_FWD_DEFINED__
#define __IDWriteTextAnalysisSource_FWD_DEFINED__
typedef interface IDWriteTextAnalysisSource IDWriteTextAnalysisSource;
#ifdef __cplusplus
interface IDWriteTextAnalysisSource;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalysisSink_FWD_DEFINED__
#define __IDWriteTextAnalysisSink_FWD_DEFINED__
typedef interface IDWriteTextAnalysisSink IDWriteTextAnalysisSink;
#ifdef __cplusplus
interface IDWriteTextAnalysisSink;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalyzer_FWD_DEFINED__
#define __IDWriteTextAnalyzer_FWD_DEFINED__
typedef interface IDWriteTextAnalyzer IDWriteTextAnalyzer;
#ifdef __cplusplus
interface IDWriteTextAnalyzer;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteGlyphRunAnalysis_FWD_DEFINED__
#define __IDWriteGlyphRunAnalysis_FWD_DEFINED__
typedef interface IDWriteGlyphRunAnalysis IDWriteGlyphRunAnalysis;
#ifdef __cplusplus
interface IDWriteGlyphRunAnalysis;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFactory_FWD_DEFINED__
#define __IDWriteFactory_FWD_DEFINED__
typedef interface IDWriteFactory IDWriteFactory;
#ifdef __cplusplus
interface IDWriteFactory;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <dcommon.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __IDWriteFactory_FWD_DEFINED__
#define __IDWriteFactory_FWD_DEFINED__
typedef interface IDWriteFactory IDWriteFactory;
#ifdef __cplusplus
interface IDWriteFactory;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontCollection_FWD_DEFINED__
#define __IDWriteFontCollection_FWD_DEFINED__
typedef interface IDWriteFontCollection IDWriteFontCollection;
#ifdef __cplusplus
interface IDWriteFontCollection;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFamily_FWD_DEFINED__
#define __IDWriteFontFamily_FWD_DEFINED__
typedef interface IDWriteFontFamily IDWriteFontFamily;
#ifdef __cplusplus
interface IDWriteFontFamily;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFace_FWD_DEFINED__
#define __IDWriteFontFace_FWD_DEFINED__
typedef interface IDWriteFontFace IDWriteFontFace;
#ifdef __cplusplus
interface IDWriteFontFace;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteInlineObject_FWD_DEFINED__
#define __IDWriteInlineObject_FWD_DEFINED__
typedef interface IDWriteInlineObject IDWriteInlineObject;
#ifdef __cplusplus
interface IDWriteInlineObject;
#endif /* __cplusplus */
#endif

#ifndef __ID2D1SimplifiedGeometrySink_FWD_DEFINED__
#define __ID2D1SimplifiedGeometrySink_FWD_DEFINED__
typedef interface ID2D1SimplifiedGeometrySink ID2D1SimplifiedGeometrySink;
#ifdef __cplusplus
interface ID2D1SimplifiedGeometrySink;
#endif /* __cplusplus */
#endif

typedef ID2D1SimplifiedGeometrySink IDWriteGeometrySink;
#ifndef _WINDEF_
typedef void *HMONITOR;
#endif /* _WINDEF_ */
#ifdef WINE_NO_UNICODE_MACROS
#undef GetGlyphIndices
#endif
typedef enum DWRITE_FACTORY_TYPE {
    DWRITE_FACTORY_TYPE_SHARED = 0,
    DWRITE_FACTORY_TYPE_ISOLATED = 1
} DWRITE_FACTORY_TYPE;
typedef enum DWRITE_FONT_FILE_TYPE {
    DWRITE_FONT_FILE_TYPE_UNKNOWN = 0,
    DWRITE_FONT_FILE_TYPE_CFF = 1,
    DWRITE_FONT_FILE_TYPE_TRUETYPE = 2,
    DWRITE_FONT_FILE_TYPE_OPENTYPE_COLLECTION = 3,
    DWRITE_FONT_FILE_TYPE_TYPE1_PFM = 4,
    DWRITE_FONT_FILE_TYPE_TYPE1_PFB = 5,
    DWRITE_FONT_FILE_TYPE_VECTOR = 6,
    DWRITE_FONT_FILE_TYPE_BITMAP = 7,
    DWRITE_FONT_FILE_TYPE_TRUETYPE_COLLECTION = DWRITE_FONT_FILE_TYPE_OPENTYPE_COLLECTION
} DWRITE_FONT_FILE_TYPE;
typedef enum DWRITE_FONT_FACE_TYPE {
    DWRITE_FONT_FACE_TYPE_CFF = 0,
    DWRITE_FONT_FACE_TYPE_TRUETYPE = 1,
    DWRITE_FONT_FACE_TYPE_OPENTYPE_COLLECTION = 2,
    DWRITE_FONT_FACE_TYPE_TYPE1 = 3,
    DWRITE_FONT_FACE_TYPE_VECTOR = 4,
    DWRITE_FONT_FACE_TYPE_BITMAP = 5,
    DWRITE_FONT_FACE_TYPE_UNKNOWN = 6,
    DWRITE_FONT_FACE_TYPE_RAW_CFF = 7,
    DWRITE_FONT_FACE_TYPE_TRUETYPE_COLLECTION = DWRITE_FONT_FACE_TYPE_OPENTYPE_COLLECTION
} DWRITE_FONT_FACE_TYPE;
typedef enum DWRITE_FONT_WEIGHT {
    DWRITE_FONT_WEIGHT_THIN = 100,
    DWRITE_FONT_WEIGHT_EXTRA_LIGHT = 200,
    DWRITE_FONT_WEIGHT_ULTRA_LIGHT = 200,
    DWRITE_FONT_WEIGHT_LIGHT = 300,
    DWRITE_FONT_WEIGHT_SEMI_LIGHT = 350,
    DWRITE_FONT_WEIGHT_NORMAL = 400,
    DWRITE_FONT_WEIGHT_REGULAR = 400,
    DWRITE_FONT_WEIGHT_MEDIUM = 500,
    DWRITE_FONT_WEIGHT_DEMI_BOLD = 600,
    DWRITE_FONT_WEIGHT_SEMI_BOLD = 600,
    DWRITE_FONT_WEIGHT_BOLD = 700,
    DWRITE_FONT_WEIGHT_EXTRA_BOLD = 800,
    DWRITE_FONT_WEIGHT_ULTRA_BOLD = 800,
    DWRITE_FONT_WEIGHT_BLACK = 900,
    DWRITE_FONT_WEIGHT_HEAVY = 900,
    DWRITE_FONT_WEIGHT_EXTRA_BLACK = 950,
    DWRITE_FONT_WEIGHT_ULTRA_BLACK = 950
} DWRITE_FONT_WEIGHT;
typedef enum DWRITE_FONT_STRETCH {
    DWRITE_FONT_STRETCH_UNDEFINED = 0,
    DWRITE_FONT_STRETCH_ULTRA_CONDENSED = 1,
    DWRITE_FONT_STRETCH_EXTRA_CONDENSED = 2,
    DWRITE_FONT_STRETCH_CONDENSED = 3,
    DWRITE_FONT_STRETCH_SEMI_CONDENSED = 4,
    DWRITE_FONT_STRETCH_NORMAL = 5,
    DWRITE_FONT_STRETCH_MEDIUM = 5,
    DWRITE_FONT_STRETCH_SEMI_EXPANDED = 6,
    DWRITE_FONT_STRETCH_EXPANDED = 7,
    DWRITE_FONT_STRETCH_EXTRA_EXPANDED = 8,
    DWRITE_FONT_STRETCH_ULTRA_EXPANDED = 9
} DWRITE_FONT_STRETCH;
typedef enum DWRITE_FONT_STYLE {
    DWRITE_FONT_STYLE_NORMAL = 0,
    DWRITE_FONT_STYLE_OBLIQUE = 1,
    DWRITE_FONT_STYLE_ITALIC = 2
} DWRITE_FONT_STYLE;
typedef enum DWRITE_INFORMATIONAL_STRING_ID {
    DWRITE_INFORMATIONAL_STRING_NONE = 0,
    DWRITE_INFORMATIONAL_STRING_COPYRIGHT_NOTICE = 1,
    DWRITE_INFORMATIONAL_STRING_VERSION_STRINGS = 2,
    DWRITE_INFORMATIONAL_STRING_TRADEMARK = 3,
    DWRITE_INFORMATIONAL_STRING_MANUFACTURER = 4,
    DWRITE_INFORMATIONAL_STRING_DESIGNER = 5,
    DWRITE_INFORMATIONAL_STRING_DESIGNER_URL = 6,
    DWRITE_INFORMATIONAL_STRING_DESCRIPTION = 7,
    DWRITE_INFORMATIONAL_STRING_FONT_VENDOR_URL = 8,
    DWRITE_INFORMATIONAL_STRING_LICENSE_DESCRIPTION = 9,
    DWRITE_INFORMATIONAL_STRING_LICENSE_INFO_URL = 10,
    DWRITE_INFORMATIONAL_STRING_WIN32_FAMILY_NAMES = 11,
    DWRITE_INFORMATIONAL_STRING_WIN32_SUBFAMILY_NAMES = 12,
    DWRITE_INFORMATIONAL_STRING_TYPOGRAPHIC_FAMILY_NAMES = 13,
    DWRITE_INFORMATIONAL_STRING_TYPOGRAPHIC_SUBFAMILY_NAMES = 14,
    DWRITE_INFORMATIONAL_STRING_SAMPLE_TEXT = 15,
    DWRITE_INFORMATIONAL_STRING_FULL_NAME = 16,
    DWRITE_INFORMATIONAL_STRING_POSTSCRIPT_NAME = 17,
    DWRITE_INFORMATIONAL_STRING_POSTSCRIPT_CID_NAME = 18,
    DWRITE_INFORMATIONAL_STRING_WEIGHT_STRETCH_STYLE_FAMILY_NAME = 19,
    DWRITE_INFORMATIONAL_STRING_DESIGN_SCRIPT_LANGUAGE_TAG = 20,
    DWRITE_INFORMATIONAL_STRING_SUPPORTED_SCRIPT_LANGUAGE_TAG = 21,
    DWRITE_INFORMATIONAL_STRING_PREFERRED_FAMILY_NAMES = DWRITE_INFORMATIONAL_STRING_TYPOGRAPHIC_FAMILY_NAMES,
    DWRITE_INFORMATIONAL_STRING_PREFERRED_SUBFAMILY_NAMES = DWRITE_INFORMATIONAL_STRING_TYPOGRAPHIC_SUBFAMILY_NAMES,
    DWRITE_INFORMATIONAL_STRING_WWS_FAMILY_NAME = DWRITE_INFORMATIONAL_STRING_WEIGHT_STRETCH_STYLE_FAMILY_NAME
} DWRITE_INFORMATIONAL_STRING_ID;
typedef enum DWRITE_FONT_SIMULATIONS {
    DWRITE_FONT_SIMULATIONS_NONE = 0,
    DWRITE_FONT_SIMULATIONS_BOLD = 1,
    DWRITE_FONT_SIMULATIONS_OBLIQUE = 2
} DWRITE_FONT_SIMULATIONS;
DEFINE_ENUM_FLAG_OPERATORS(DWRITE_FONT_SIMULATIONS);
typedef enum DWRITE_PIXEL_GEOMETRY {
    DWRITE_PIXEL_GEOMETRY_FLAT = 0,
    DWRITE_PIXEL_GEOMETRY_RGB = 1,
    DWRITE_PIXEL_GEOMETRY_BGR = 2
} DWRITE_PIXEL_GEOMETRY;
typedef enum DWRITE_RENDERING_MODE {
    DWRITE_RENDERING_MODE_DEFAULT = 0,
    DWRITE_RENDERING_MODE_ALIASED = 1,
    DWRITE_RENDERING_MODE_GDI_CLASSIC = 2,
    DWRITE_RENDERING_MODE_GDI_NATURAL = 3,
    DWRITE_RENDERING_MODE_NATURAL = 4,
    DWRITE_RENDERING_MODE_NATURAL_SYMMETRIC = 5,
    DWRITE_RENDERING_MODE_OUTLINE = 6,
    DWRITE_RENDERING_MODE_CLEARTYPE_GDI_CLASSIC = DWRITE_RENDERING_MODE_GDI_CLASSIC,
    DWRITE_RENDERING_MODE_CLEARTYPE_GDI_NATURAL = DWRITE_RENDERING_MODE_GDI_NATURAL,
    DWRITE_RENDERING_MODE_CLEARTYPE_NATURAL = DWRITE_RENDERING_MODE_NATURAL,
    DWRITE_RENDERING_MODE_CLEARTYPE_NATURAL_SYMMETRIC = DWRITE_RENDERING_MODE_NATURAL_SYMMETRIC
} DWRITE_RENDERING_MODE;
typedef enum DWRITE_TEXT_ALIGNMENT {
    DWRITE_TEXT_ALIGNMENT_LEADING = 0,
    DWRITE_TEXT_ALIGNMENT_TRAILING = 1,
    DWRITE_TEXT_ALIGNMENT_CENTER = 2,
    DWRITE_TEXT_ALIGNMENT_JUSTIFIED = 3
} DWRITE_TEXT_ALIGNMENT;
typedef enum DWRITE_PARAGRAPH_ALIGNMENT {
    DWRITE_PARAGRAPH_ALIGNMENT_NEAR = 0,
    DWRITE_PARAGRAPH_ALIGNMENT_FAR = 1,
    DWRITE_PARAGRAPH_ALIGNMENT_CENTER = 2
} DWRITE_PARAGRAPH_ALIGNMENT;
typedef enum DWRITE_WORD_WRAPPING {
    DWRITE_WORD_WRAPPING_WRAP = 0,
    DWRITE_WORD_WRAPPING_NO_WRAP = 1,
    DWRITE_WORD_WRAPPING_EMERGENCY_BREAK = 2,
    DWRITE_WORD_WRAPPING_WHOLE_WORD = 3,
    DWRITE_WORD_WRAPPING_CHARACTER = 4
} DWRITE_WORD_WRAPPING;
typedef enum DWRITE_READING_DIRECTION {
    DWRITE_READING_DIRECTION_LEFT_TO_RIGHT = 0,
    DWRITE_READING_DIRECTION_RIGHT_TO_LEFT = 1,
    DWRITE_READING_DIRECTION_TOP_TO_BOTTOM = 2,
    DWRITE_READING_DIRECTION_BOTTOM_TO_TOP = 3
} DWRITE_READING_DIRECTION;
typedef enum DWRITE_FLOW_DIRECTION {
    DWRITE_FLOW_DIRECTION_TOP_TO_BOTTOM = 0,
    DWRITE_FLOW_DIRECTION_BOTTOM_TO_TOP = 1,
    DWRITE_FLOW_DIRECTION_LEFT_TO_RIGHT = 2,
    DWRITE_FLOW_DIRECTION_RIGHT_TO_LEFT = 3
} DWRITE_FLOW_DIRECTION;
typedef enum DWRITE_TRIMMING_GRANULARITY {
    DWRITE_TRIMMING_GRANULARITY_NONE = 0,
    DWRITE_TRIMMING_GRANULARITY_CHARACTER = 1,
    DWRITE_TRIMMING_GRANULARITY_WORD = 2
} DWRITE_TRIMMING_GRANULARITY;
typedef enum DWRITE_BREAK_CONDITION {
    DWRITE_BREAK_CONDITION_NEUTRAL = 0,
    DWRITE_BREAK_CONDITION_CAN_BREAK = 1,
    DWRITE_BREAK_CONDITION_MAY_NOT_BREAK = 2,
    DWRITE_BREAK_CONDITION_MUST_BREAK = 3
} DWRITE_BREAK_CONDITION;
typedef enum DWRITE_LINE_SPACING_METHOD {
    DWRITE_LINE_SPACING_METHOD_DEFAULT = 0,
    DWRITE_LINE_SPACING_METHOD_UNIFORM = 1,
    DWRITE_LINE_SPACING_METHOD_PROPORTIONAL = 2
} DWRITE_LINE_SPACING_METHOD;
#define DWRITE_MAKE_OPENTYPE_TAG(a,b,c,d) ( \
    ((UINT32)(UINT8)(d) << 24) | \
    ((UINT32)(UINT8)(c) << 16) | \
    ((UINT32)(UINT8)(b) <<  8) | \
     (UINT32)(UINT8)(a))
typedef enum DWRITE_FONT_FEATURE_TAG {
    DWRITE_FONT_FEATURE_TAG_ALTERNATIVE_FRACTIONS = 0x63726661,
    DWRITE_FONT_FEATURE_TAG_PETITE_CAPITALS_FROM_CAPITALS = 0x63703263,
    DWRITE_FONT_FEATURE_TAG_SMALL_CAPITALS_FROM_CAPITALS = 0x63733263,
    DWRITE_FONT_FEATURE_TAG_CONTEXTUAL_ALTERNATES = 0x746c6163,
    DWRITE_FONT_FEATURE_TAG_CASE_SENSITIVE_FORMS = 0x65736163,
    DWRITE_FONT_FEATURE_TAG_GLYPH_COMPOSITION_DECOMPOSITION = 0x706d6363,
    DWRITE_FONT_FEATURE_TAG_CONTEXTUAL_LIGATURES = 0x67696c63,
    DWRITE_FONT_FEATURE_TAG_CAPITAL_SPACING = 0x70737063,
    DWRITE_FONT_FEATURE_TAG_CONTEXTUAL_SWASH = 0x68777363,
    DWRITE_FONT_FEATURE_TAG_CURSIVE_POSITIONING = 0x73727563,
    DWRITE_FONT_FEATURE_TAG_DEFAULT = 0x746c6664,
    DWRITE_FONT_FEATURE_TAG_DISCRETIONARY_LIGATURES = 0x67696c64,
    DWRITE_FONT_FEATURE_TAG_EXPERT_FORMS = 0x74707865,
    DWRITE_FONT_FEATURE_TAG_FRACTIONS = 0x63617266,
    DWRITE_FONT_FEATURE_TAG_FULL_WIDTH = 0x64697766,
    DWRITE_FONT_FEATURE_TAG_HALF_FORMS = 0x666c6168,
    DWRITE_FONT_FEATURE_TAG_HALANT_FORMS = 0x6e6c6168,
    DWRITE_FONT_FEATURE_TAG_ALTERNATE_HALF_WIDTH = 0x746c6168,
    DWRITE_FONT_FEATURE_TAG_HISTORICAL_FORMS = 0x74736968,
    DWRITE_FONT_FEATURE_TAG_HORIZONTAL_KANA_ALTERNATES = 0x616e6b68,
    DWRITE_FONT_FEATURE_TAG_HISTORICAL_LIGATURES = 0x67696c68,
    DWRITE_FONT_FEATURE_TAG_HALF_WIDTH = 0x64697768,
    DWRITE_FONT_FEATURE_TAG_HOJO_KANJI_FORMS = 0x6f6a6f68,
    DWRITE_FONT_FEATURE_TAG_JIS04_FORMS = 0x3430706a,
    DWRITE_FONT_FEATURE_TAG_JIS78_FORMS = 0x3837706a,
    DWRITE_FONT_FEATURE_TAG_JIS83_FORMS = 0x3338706a,
    DWRITE_FONT_FEATURE_TAG_JIS90_FORMS = 0x3039706a,
    DWRITE_FONT_FEATURE_TAG_KERNING = 0x6e72656b,
    DWRITE_FONT_FEATURE_TAG_STANDARD_LIGATURES = 0x6167696c,
    DWRITE_FONT_FEATURE_TAG_LINING_FIGURES = 0x6d756e6c,
    DWRITE_FONT_FEATURE_TAG_LOCALIZED_FORMS = 0x6c636f6c,
    DWRITE_FONT_FEATURE_TAG_MARK_POSITIONING = 0x6b72616d,
    DWRITE_FONT_FEATURE_TAG_MATHEMATICAL_GREEK = 0x6b72676d,
    DWRITE_FONT_FEATURE_TAG_MARK_TO_MARK_POSITIONING = 0x6b6d6b6d,
    DWRITE_FONT_FEATURE_TAG_ALTERNATE_ANNOTATION_FORMS = 0x746c616e,
    DWRITE_FONT_FEATURE_TAG_NLC_KANJI_FORMS = 0x6b636c6e,
    DWRITE_FONT_FEATURE_TAG_OLD_STYLE_FIGURES = 0x6d756e6f,
    DWRITE_FONT_FEATURE_TAG_ORDINALS = 0x6e64726f,
    DWRITE_FONT_FEATURE_TAG_PROPORTIONAL_ALTERNATE_WIDTH = 0x746c6170,
    DWRITE_FONT_FEATURE_TAG_PETITE_CAPITALS = 0x70616370,
    DWRITE_FONT_FEATURE_TAG_PROPORTIONAL_FIGURES = 0x6d756e70,
    DWRITE_FONT_FEATURE_TAG_PROPORTIONAL_WIDTHS = 0x64697770,
    DWRITE_FONT_FEATURE_TAG_QUARTER_WIDTHS = 0x64697771,
    DWRITE_FONT_FEATURE_TAG_REQUIRED_LIGATURES = 0x67696c72,
    DWRITE_FONT_FEATURE_TAG_RUBY_NOTATION_FORMS = 0x79627572,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_ALTERNATES = 0x746c6173,
    DWRITE_FONT_FEATURE_TAG_SCIENTIFIC_INFERIORS = 0x666e6973,
    DWRITE_FONT_FEATURE_TAG_SMALL_CAPITALS = 0x70636d73,
    DWRITE_FONT_FEATURE_TAG_SIMPLIFIED_FORMS = 0x6c706d73,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_1 = 0x31307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_2 = 0x32307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_3 = 0x33307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_4 = 0x34307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_5 = 0x35307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_6 = 0x36307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_7 = 0x37307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_8 = 0x38307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_9 = 0x39307373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_10 = 0x30317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_11 = 0x31317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_12 = 0x32317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_13 = 0x33317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_14 = 0x34317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_15 = 0x35317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_16 = 0x36317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_17 = 0x37317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_18 = 0x38317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_19 = 0x39317373,
    DWRITE_FONT_FEATURE_TAG_STYLISTIC_SET_20 = 0x30327373,
    DWRITE_FONT_FEATURE_TAG_SUBSCRIPT = 0x73627573,
    DWRITE_FONT_FEATURE_TAG_SUPERSCRIPT = 0x73707573,
    DWRITE_FONT_FEATURE_TAG_SWASH = 0x68737773,
    DWRITE_FONT_FEATURE_TAG_TITLING = 0x6c746974,
    DWRITE_FONT_FEATURE_TAG_TRADITIONAL_NAME_FORMS = 0x6d616e74,
    DWRITE_FONT_FEATURE_TAG_TABULAR_FIGURES = 0x6d756e74,
    DWRITE_FONT_FEATURE_TAG_TRADITIONAL_FORMS = 0x64617274,
    DWRITE_FONT_FEATURE_TAG_THIRD_WIDTHS = 0x64697774,
    DWRITE_FONT_FEATURE_TAG_UNICASE = 0x63696e75,
    DWRITE_FONT_FEATURE_TAG_VERTICAL_WRITING = 0x74726576,
    DWRITE_FONT_FEATURE_TAG_VERTICAL_ALTERNATES_AND_ROTATION = 0x32747276,
    DWRITE_FONT_FEATURE_TAG_SLASHED_ZERO = 0x6f72657a
} DWRITE_FONT_FEATURE_TAG;
typedef enum DWRITE_SCRIPT_SHAPES {
    DWRITE_SCRIPT_SHAPES_DEFAULT = 0,
    DWRITE_SCRIPT_SHAPES_NO_VISUAL = 1
} DWRITE_SCRIPT_SHAPES;
DEFINE_ENUM_FLAG_OPERATORS(DWRITE_SCRIPT_SHAPES);
typedef enum DWRITE_NUMBER_SUBSTITUTION_METHOD {
    DWRITE_NUMBER_SUBSTITUTION_METHOD_FROM_CULTURE = 0,
    DWRITE_NUMBER_SUBSTITUTION_METHOD_CONTEXTUAL = 1,
    DWRITE_NUMBER_SUBSTITUTION_METHOD_NONE = 2,
    DWRITE_NUMBER_SUBSTITUTION_METHOD_NATIONAL = 3,
    DWRITE_NUMBER_SUBSTITUTION_METHOD_TRADITIONAL = 4
} DWRITE_NUMBER_SUBSTITUTION_METHOD;
#define DWRITE_ALPHA_MAX 255
typedef enum DWRITE_TEXTURE_TYPE {
    DWRITE_TEXTURE_ALIASED_1x1 = 0,
    DWRITE_TEXTURE_CLEARTYPE_3x1 = 1
} DWRITE_TEXTURE_TYPE;
typedef struct DWRITE_FONT_METRICS {
    UINT16 designUnitsPerEm;
    UINT16 ascent;
    UINT16 descent;
    INT16 lineGap;
    UINT16 capHeight;
    UINT16 xHeight;
    INT16 underlinePosition;
    UINT16 underlineThickness;
    INT16 strikethroughPosition;
    UINT16 strikethroughThickness;
} DWRITE_FONT_METRICS;
typedef struct DWRITE_GLYPH_METRICS {
    INT32 leftSideBearing;
    UINT32 advanceWidth;
    INT32 rightSideBearing;
    INT32 topSideBearing;
    UINT32 advanceHeight;
    INT32 bottomSideBearing;
    INT32 verticalOriginY;
} DWRITE_GLYPH_METRICS;
typedef struct DWRITE_GLYPH_OFFSET {
    FLOAT advanceOffset;
    FLOAT ascenderOffset;
} DWRITE_GLYPH_OFFSET;
typedef struct DWRITE_MATRIX {
    FLOAT m11;
    FLOAT m12;
    FLOAT m21;
    FLOAT m22;
    FLOAT dx;
    FLOAT dy;
} DWRITE_MATRIX;
typedef struct DWRITE_TRIMMING {
    DWRITE_TRIMMING_GRANULARITY granularity;
    UINT32 delimiter;
    UINT32 delimiterCount;
} DWRITE_TRIMMING;
#ifndef __d2d1_h__
typedef struct DWRITE_GLYPH_RUN DWRITE_GLYPH_RUN;
#endif /* __d2d1_h__ */
struct DWRITE_GLYPH_RUN {
    IDWriteFontFace *fontFace;
    FLOAT fontEmSize;
    UINT32 glyphCount;
    const UINT16 *glyphIndices;
    const FLOAT *glyphAdvances;
    const DWRITE_GLYPH_OFFSET *glyphOffsets;
    WINBOOL isSideways;
    UINT32 bidiLevel;
};
#ifndef __d2d1_1_h__
typedef struct DWRITE_GLYPH_RUN_DESCRIPTION DWRITE_GLYPH_RUN_DESCRIPTION;
#endif /* __d2d1_1_h__ */
struct DWRITE_GLYPH_RUN_DESCRIPTION {
    const WCHAR *localeName;
    const WCHAR *string;
    UINT32 stringLength;
    const UINT16 *clusterMap;
    UINT32 textPosition;
};
typedef struct DWRITE_UNDERLINE {
    FLOAT width;
    FLOAT thickness;
    FLOAT offset;
    FLOAT runHeight;
    DWRITE_READING_DIRECTION readingDirection;
    DWRITE_FLOW_DIRECTION flowDirection;
    const WCHAR *localeName;
    DWRITE_MEASURING_MODE measuringMode;
} DWRITE_UNDERLINE;
typedef struct DWRITE_STRIKETHROUGH {
    FLOAT width;
    FLOAT thickness;
    FLOAT offset;
    DWRITE_READING_DIRECTION readingDirection;
    DWRITE_FLOW_DIRECTION flowDirection;
    const WCHAR *localeName;
    DWRITE_MEASURING_MODE measuringMode;
} DWRITE_STRIKETHROUGH;
typedef struct DWRITE_INLINE_OBJECT_METRICS {
    FLOAT width;
    FLOAT height;
    FLOAT baseline;
    WINBOOL supportsSideways;
} DWRITE_INLINE_OBJECT_METRICS;
typedef struct DWRITE_OVERHANG_METRICS {
    FLOAT left;
    FLOAT top;
    FLOAT right;
    FLOAT bottom;
} DWRITE_OVERHANG_METRICS;
typedef struct DWRITE_FONT_FEATURE {
    DWRITE_FONT_FEATURE_TAG nameTag;
    UINT32 parameter;
} DWRITE_FONT_FEATURE;
typedef struct DWRITE_TEXT_RANGE {
    UINT32 startPosition;
    UINT32 length;
} DWRITE_TEXT_RANGE;
typedef struct DWRITE_LINE_METRICS {
    UINT32 length;
    UINT32 trailingWhitespaceLength;
    UINT32 newlineLength;
    FLOAT height;
    FLOAT baseline;
    WINBOOL isTrimmed;
} DWRITE_LINE_METRICS;
typedef struct DWRITE_TEXT_METRICS {
    FLOAT left;
    FLOAT top;
    FLOAT width;
    FLOAT widthIncludingTrailingWhitespace;
    FLOAT height;
    FLOAT layoutWidth;
    FLOAT layoutHeight;
    UINT32 maxBidiReorderingDepth;
    UINT32 lineCount;
} DWRITE_TEXT_METRICS;
typedef struct DWRITE_CLUSTER_METRICS {
    FLOAT width;
    UINT16 length;
    UINT16 canWrapLineAfter : 1;
    UINT16 isWhitespace : 1;
    UINT16 isNewline : 1;
    UINT16 isSoftHyphen : 1;
    UINT16 isRightToLeft : 1;
    UINT16 padding : 11;
} DWRITE_CLUSTER_METRICS;
typedef struct DWRITE_HIT_TEST_METRICS {
    UINT32 textPosition;
    UINT32 length;
    FLOAT left;
    FLOAT top;
    FLOAT width;
    FLOAT height;
    UINT32 bidiLevel;
    WINBOOL isText;
    WINBOOL isTrimmed;
} DWRITE_HIT_TEST_METRICS;
typedef struct DWRITE_SCRIPT_ANALYSIS {
    UINT16 script;
    DWRITE_SCRIPT_SHAPES shapes;
} DWRITE_SCRIPT_ANALYSIS;
typedef struct DWRITE_LINE_BREAKPOINT {
    UINT8 breakConditionBefore : 2;
    UINT8 breakConditionAfter : 2;
    UINT8 isWhitespace : 1;
    UINT8 isSoftHyphen : 1;
    UINT8 padding : 2;
} DWRITE_LINE_BREAKPOINT;
typedef struct DWRITE_TYPOGRAPHIC_FEATURES {
    DWRITE_FONT_FEATURE *features;
    UINT32 featureCount;
} DWRITE_TYPOGRAPHIC_FEATURES;
typedef struct DWRITE_SHAPING_TEXT_PROPERTIES {
    UINT16 isShapedAlone : 1;
    UINT16 reserved1 : 1;
    UINT16 canBreakShapingAfter : 1;
    UINT16 reserved : 13;
} DWRITE_SHAPING_TEXT_PROPERTIES;
typedef struct DWRITE_SHAPING_GLYPH_PROPERTIES {
    UINT16 justification : 4;
    UINT16 isClusterStart : 1;
    UINT16 isDiacritic : 1;
    UINT16 isZeroWidthSpace : 1;
    UINT16 reserved : 9;
} DWRITE_SHAPING_GLYPH_PROPERTIES;
/*****************************************************************************
 * IDWriteFontFileStream interface
 */
#ifndef __IDWriteFontFileStream_INTERFACE_DEFINED__
#define __IDWriteFontFileStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFileStream, 0x6d4865fe, 0x0ab8, 0x4d91, 0x8f,0x62, 0x5d,0xd6,0xbe,0x34,0xa3,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6d4865fe-0ab8-4d91-8f62-5dd6be34a3e0")
IDWriteFontFileStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ReadFileFragment(
        const void **fragment_start,
        UINT64 offset,
        UINT64 fragment_size,
        void **fragment_context) = 0;

    virtual void STDMETHODCALLTYPE ReleaseFileFragment(
        void *fragment_context) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileSize(
        UINT64 *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastWriteTime(
        UINT64 *last_writetime) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFileStream, 0x6d4865fe, 0x0ab8, 0x4d91, 0x8f,0x62, 0x5d,0xd6,0xbe,0x34,0xa3,0xe0)
#endif
#else
typedef struct IDWriteFontFileStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFileStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFileStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFileStream *This);

    /*** IDWriteFontFileStream methods ***/
    HRESULT (STDMETHODCALLTYPE *ReadFileFragment)(
        IDWriteFontFileStream *This,
        const void **fragment_start,
        UINT64 offset,
        UINT64 fragment_size,
        void **fragment_context);

    void (STDMETHODCALLTYPE *ReleaseFileFragment)(
        IDWriteFontFileStream *This,
        void *fragment_context);

    HRESULT (STDMETHODCALLTYPE *GetFileSize)(
        IDWriteFontFileStream *This,
        UINT64 *size);

    HRESULT (STDMETHODCALLTYPE *GetLastWriteTime)(
        IDWriteFontFileStream *This,
        UINT64 *last_writetime);

    END_INTERFACE
} IDWriteFontFileStreamVtbl;

interface IDWriteFontFileStream {
    CONST_VTBL IDWriteFontFileStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFileStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFileStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFileStream_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFileStream methods ***/
#define IDWriteFontFileStream_ReadFileFragment(This,fragment_start,offset,fragment_size,fragment_context) (This)->lpVtbl->ReadFileFragment(This,fragment_start,offset,fragment_size,fragment_context)
#define IDWriteFontFileStream_ReleaseFileFragment(This,fragment_context) (This)->lpVtbl->ReleaseFileFragment(This,fragment_context)
#define IDWriteFontFileStream_GetFileSize(This,size) (This)->lpVtbl->GetFileSize(This,size)
#define IDWriteFontFileStream_GetLastWriteTime(This,last_writetime) (This)->lpVtbl->GetLastWriteTime(This,last_writetime)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFileStream_QueryInterface(IDWriteFontFileStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFileStream_AddRef(IDWriteFontFileStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFileStream_Release(IDWriteFontFileStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFileStream methods ***/
static inline HRESULT IDWriteFontFileStream_ReadFileFragment(IDWriteFontFileStream* This,const void **fragment_start,UINT64 offset,UINT64 fragment_size,void **fragment_context) {
    return This->lpVtbl->ReadFileFragment(This,fragment_start,offset,fragment_size,fragment_context);
}
static inline void IDWriteFontFileStream_ReleaseFileFragment(IDWriteFontFileStream* This,void *fragment_context) {
    This->lpVtbl->ReleaseFileFragment(This,fragment_context);
}
static inline HRESULT IDWriteFontFileStream_GetFileSize(IDWriteFontFileStream* This,UINT64 *size) {
    return This->lpVtbl->GetFileSize(This,size);
}
static inline HRESULT IDWriteFontFileStream_GetLastWriteTime(IDWriteFontFileStream* This,UINT64 *last_writetime) {
    return This->lpVtbl->GetLastWriteTime(This,last_writetime);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFileStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFileLoader interface
 */
#ifndef __IDWriteFontFileLoader_INTERFACE_DEFINED__
#define __IDWriteFontFileLoader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFileLoader, 0x727cad4e, 0xd6af, 0x4c9e, 0x8a,0x08, 0xd6,0x95,0xb1,0x1c,0xaa,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("727cad4e-d6af-4c9e-8a08-d695b11caa49")
IDWriteFontFileLoader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateStreamFromKey(
        const void *key,
        UINT32 key_size,
        IDWriteFontFileStream **stream) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFileLoader, 0x727cad4e, 0xd6af, 0x4c9e, 0x8a,0x08, 0xd6,0x95,0xb1,0x1c,0xaa,0x49)
#endif
#else
typedef struct IDWriteFontFileLoaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFileLoader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFileLoader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFileLoader *This);

    /*** IDWriteFontFileLoader methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateStreamFromKey)(
        IDWriteFontFileLoader *This,
        const void *key,
        UINT32 key_size,
        IDWriteFontFileStream **stream);

    END_INTERFACE
} IDWriteFontFileLoaderVtbl;

interface IDWriteFontFileLoader {
    CONST_VTBL IDWriteFontFileLoaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFileLoader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFileLoader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFileLoader_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFileLoader methods ***/
#define IDWriteFontFileLoader_CreateStreamFromKey(This,key,key_size,stream) (This)->lpVtbl->CreateStreamFromKey(This,key,key_size,stream)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFileLoader_QueryInterface(IDWriteFontFileLoader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFileLoader_AddRef(IDWriteFontFileLoader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFileLoader_Release(IDWriteFontFileLoader* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFileLoader methods ***/
static inline HRESULT IDWriteFontFileLoader_CreateStreamFromKey(IDWriteFontFileLoader* This,const void *key,UINT32 key_size,IDWriteFontFileStream **stream) {
    return This->lpVtbl->CreateStreamFromKey(This,key,key_size,stream);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFileLoader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteLocalFontFileLoader interface
 */
#ifndef __IDWriteLocalFontFileLoader_INTERFACE_DEFINED__
#define __IDWriteLocalFontFileLoader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteLocalFontFileLoader, 0xb2d9f3ec, 0xc9fe, 0x4a11, 0xa2,0xec, 0xd8,0x62,0x08,0xf7,0xc0,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b2d9f3ec-c9fe-4a11-a2ec-d86208f7c0a2")
IDWriteLocalFontFileLoader : public IDWriteFontFileLoader
{
    virtual HRESULT STDMETHODCALLTYPE GetFilePathLengthFromKey(
        const void *key,
        UINT32 key_size,
        UINT32 *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFilePathFromKey(
        const void *key,
        UINT32 key_size,
        WCHAR *path,
        UINT32 length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastWriteTimeFromKey(
        const void *key,
        UINT32 key_size,
        FILETIME *writetime) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteLocalFontFileLoader, 0xb2d9f3ec, 0xc9fe, 0x4a11, 0xa2,0xec, 0xd8,0x62,0x08,0xf7,0xc0,0xa2)
#endif
#else
typedef struct IDWriteLocalFontFileLoaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteLocalFontFileLoader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteLocalFontFileLoader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteLocalFontFileLoader *This);

    /*** IDWriteFontFileLoader methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateStreamFromKey)(
        IDWriteLocalFontFileLoader *This,
        const void *key,
        UINT32 key_size,
        IDWriteFontFileStream **stream);

    /*** IDWriteLocalFontFileLoader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFilePathLengthFromKey)(
        IDWriteLocalFontFileLoader *This,
        const void *key,
        UINT32 key_size,
        UINT32 *length);

    HRESULT (STDMETHODCALLTYPE *GetFilePathFromKey)(
        IDWriteLocalFontFileLoader *This,
        const void *key,
        UINT32 key_size,
        WCHAR *path,
        UINT32 length);

    HRESULT (STDMETHODCALLTYPE *GetLastWriteTimeFromKey)(
        IDWriteLocalFontFileLoader *This,
        const void *key,
        UINT32 key_size,
        FILETIME *writetime);

    END_INTERFACE
} IDWriteLocalFontFileLoaderVtbl;

interface IDWriteLocalFontFileLoader {
    CONST_VTBL IDWriteLocalFontFileLoaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteLocalFontFileLoader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteLocalFontFileLoader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteLocalFontFileLoader_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFileLoader methods ***/
#define IDWriteLocalFontFileLoader_CreateStreamFromKey(This,key,key_size,stream) (This)->lpVtbl->CreateStreamFromKey(This,key,key_size,stream)
/*** IDWriteLocalFontFileLoader methods ***/
#define IDWriteLocalFontFileLoader_GetFilePathLengthFromKey(This,key,key_size,length) (This)->lpVtbl->GetFilePathLengthFromKey(This,key,key_size,length)
#define IDWriteLocalFontFileLoader_GetFilePathFromKey(This,key,key_size,path,length) (This)->lpVtbl->GetFilePathFromKey(This,key,key_size,path,length)
#define IDWriteLocalFontFileLoader_GetLastWriteTimeFromKey(This,key,key_size,writetime) (This)->lpVtbl->GetLastWriteTimeFromKey(This,key,key_size,writetime)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteLocalFontFileLoader_QueryInterface(IDWriteLocalFontFileLoader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteLocalFontFileLoader_AddRef(IDWriteLocalFontFileLoader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteLocalFontFileLoader_Release(IDWriteLocalFontFileLoader* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFileLoader methods ***/
static inline HRESULT IDWriteLocalFontFileLoader_CreateStreamFromKey(IDWriteLocalFontFileLoader* This,const void *key,UINT32 key_size,IDWriteFontFileStream **stream) {
    return This->lpVtbl->CreateStreamFromKey(This,key,key_size,stream);
}
/*** IDWriteLocalFontFileLoader methods ***/
static inline HRESULT IDWriteLocalFontFileLoader_GetFilePathLengthFromKey(IDWriteLocalFontFileLoader* This,const void *key,UINT32 key_size,UINT32 *length) {
    return This->lpVtbl->GetFilePathLengthFromKey(This,key,key_size,length);
}
static inline HRESULT IDWriteLocalFontFileLoader_GetFilePathFromKey(IDWriteLocalFontFileLoader* This,const void *key,UINT32 key_size,WCHAR *path,UINT32 length) {
    return This->lpVtbl->GetFilePathFromKey(This,key,key_size,path,length);
}
static inline HRESULT IDWriteLocalFontFileLoader_GetLastWriteTimeFromKey(IDWriteLocalFontFileLoader* This,const void *key,UINT32 key_size,FILETIME *writetime) {
    return This->lpVtbl->GetLastWriteTimeFromKey(This,key,key_size,writetime);
}
#endif
#endif

#endif


#endif  /* __IDWriteLocalFontFileLoader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFile interface
 */
#ifndef __IDWriteFontFile_INTERFACE_DEFINED__
#define __IDWriteFontFile_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFile, 0x739d886a, 0xcef5, 0x47dc, 0x87,0x69, 0x1a,0x8b,0x41,0xbe,0xbb,0xb0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("739d886a-cef5-47dc-8769-1a8b41bebbb0")
IDWriteFontFile : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetReferenceKey(
        const void **key,
        UINT32 *key_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLoader(
        IDWriteFontFileLoader **loader) = 0;

    virtual HRESULT STDMETHODCALLTYPE Analyze(
        WINBOOL *is_supported_fonttype,
        DWRITE_FONT_FILE_TYPE *file_type,
        DWRITE_FONT_FACE_TYPE *face_type,
        UINT32 *faces_num) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFile, 0x739d886a, 0xcef5, 0x47dc, 0x87,0x69, 0x1a,0x8b,0x41,0xbe,0xbb,0xb0)
#endif
#else
typedef struct IDWriteFontFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFile *This);

    /*** IDWriteFontFile methods ***/
    HRESULT (STDMETHODCALLTYPE *GetReferenceKey)(
        IDWriteFontFile *This,
        const void **key,
        UINT32 *key_size);

    HRESULT (STDMETHODCALLTYPE *GetLoader)(
        IDWriteFontFile *This,
        IDWriteFontFileLoader **loader);

    HRESULT (STDMETHODCALLTYPE *Analyze)(
        IDWriteFontFile *This,
        WINBOOL *is_supported_fonttype,
        DWRITE_FONT_FILE_TYPE *file_type,
        DWRITE_FONT_FACE_TYPE *face_type,
        UINT32 *faces_num);

    END_INTERFACE
} IDWriteFontFileVtbl;

interface IDWriteFontFile {
    CONST_VTBL IDWriteFontFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFile_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFile methods ***/
#define IDWriteFontFile_GetReferenceKey(This,key,key_size) (This)->lpVtbl->GetReferenceKey(This,key,key_size)
#define IDWriteFontFile_GetLoader(This,loader) (This)->lpVtbl->GetLoader(This,loader)
#define IDWriteFontFile_Analyze(This,is_supported_fonttype,file_type,face_type,faces_num) (This)->lpVtbl->Analyze(This,is_supported_fonttype,file_type,face_type,faces_num)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFile_QueryInterface(IDWriteFontFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFile_AddRef(IDWriteFontFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFile_Release(IDWriteFontFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFile methods ***/
static inline HRESULT IDWriteFontFile_GetReferenceKey(IDWriteFontFile* This,const void **key,UINT32 *key_size) {
    return This->lpVtbl->GetReferenceKey(This,key,key_size);
}
static inline HRESULT IDWriteFontFile_GetLoader(IDWriteFontFile* This,IDWriteFontFileLoader **loader) {
    return This->lpVtbl->GetLoader(This,loader);
}
static inline HRESULT IDWriteFontFile_Analyze(IDWriteFontFile* This,WINBOOL *is_supported_fonttype,DWRITE_FONT_FILE_TYPE *file_type,DWRITE_FONT_FACE_TYPE *face_type,UINT32 *faces_num) {
    return This->lpVtbl->Analyze(This,is_supported_fonttype,file_type,face_type,faces_num);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFileEnumerator interface
 */
#ifndef __IDWriteFontFileEnumerator_INTERFACE_DEFINED__
#define __IDWriteFontFileEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFileEnumerator, 0x72755049, 0x5ff7, 0x435d, 0x83,0x48, 0x4b,0xe9,0x7c,0xfa,0x6c,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72755049-5ff7-435d-8348-4be97cfa6c7c")
IDWriteFontFileEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *has_current_file) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentFontFile(
        IDWriteFontFile **font_file) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFileEnumerator, 0x72755049, 0x5ff7, 0x435d, 0x83,0x48, 0x4b,0xe9,0x7c,0xfa,0x6c,0x7c)
#endif
#else
typedef struct IDWriteFontFileEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFileEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFileEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFileEnumerator *This);

    /*** IDWriteFontFileEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IDWriteFontFileEnumerator *This,
        WINBOOL *has_current_file);

    HRESULT (STDMETHODCALLTYPE *GetCurrentFontFile)(
        IDWriteFontFileEnumerator *This,
        IDWriteFontFile **font_file);

    END_INTERFACE
} IDWriteFontFileEnumeratorVtbl;

interface IDWriteFontFileEnumerator {
    CONST_VTBL IDWriteFontFileEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFileEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFileEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFileEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFileEnumerator methods ***/
#define IDWriteFontFileEnumerator_MoveNext(This,has_current_file) (This)->lpVtbl->MoveNext(This,has_current_file)
#define IDWriteFontFileEnumerator_GetCurrentFontFile(This,font_file) (This)->lpVtbl->GetCurrentFontFile(This,font_file)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFileEnumerator_QueryInterface(IDWriteFontFileEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFileEnumerator_AddRef(IDWriteFontFileEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFileEnumerator_Release(IDWriteFontFileEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFileEnumerator methods ***/
static inline HRESULT IDWriteFontFileEnumerator_MoveNext(IDWriteFontFileEnumerator* This,WINBOOL *has_current_file) {
    return This->lpVtbl->MoveNext(This,has_current_file);
}
static inline HRESULT IDWriteFontFileEnumerator_GetCurrentFontFile(IDWriteFontFileEnumerator* This,IDWriteFontFile **font_file) {
    return This->lpVtbl->GetCurrentFontFile(This,font_file);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFileEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontCollectionLoader interface
 */
#ifndef __IDWriteFontCollectionLoader_INTERFACE_DEFINED__
#define __IDWriteFontCollectionLoader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontCollectionLoader, 0xcca920e4, 0x52f0, 0x492b, 0xbf,0xa8, 0x29,0xc7,0x2e,0xe0,0xa4,0x68);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cca920e4-52f0-492b-bfa8-29c72ee0a468")
IDWriteFontCollectionLoader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateEnumeratorFromKey(
        IDWriteFactory *factory,
        const void *key,
        UINT32 key_size,
        IDWriteFontFileEnumerator **enumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontCollectionLoader, 0xcca920e4, 0x52f0, 0x492b, 0xbf,0xa8, 0x29,0xc7,0x2e,0xe0,0xa4,0x68)
#endif
#else
typedef struct IDWriteFontCollectionLoaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontCollectionLoader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontCollectionLoader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontCollectionLoader *This);

    /*** IDWriteFontCollectionLoader methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateEnumeratorFromKey)(
        IDWriteFontCollectionLoader *This,
        IDWriteFactory *factory,
        const void *key,
        UINT32 key_size,
        IDWriteFontFileEnumerator **enumerator);

    END_INTERFACE
} IDWriteFontCollectionLoaderVtbl;

interface IDWriteFontCollectionLoader {
    CONST_VTBL IDWriteFontCollectionLoaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontCollectionLoader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontCollectionLoader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontCollectionLoader_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontCollectionLoader methods ***/
#define IDWriteFontCollectionLoader_CreateEnumeratorFromKey(This,factory,key,key_size,enumerator) (This)->lpVtbl->CreateEnumeratorFromKey(This,factory,key,key_size,enumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontCollectionLoader_QueryInterface(IDWriteFontCollectionLoader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontCollectionLoader_AddRef(IDWriteFontCollectionLoader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontCollectionLoader_Release(IDWriteFontCollectionLoader* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontCollectionLoader methods ***/
static inline HRESULT IDWriteFontCollectionLoader_CreateEnumeratorFromKey(IDWriteFontCollectionLoader* This,IDWriteFactory *factory,const void *key,UINT32 key_size,IDWriteFontFileEnumerator **enumerator) {
    return This->lpVtbl->CreateEnumeratorFromKey(This,factory,key,key_size,enumerator);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontCollectionLoader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteLocalizedStrings interface
 */
#ifndef __IDWriteLocalizedStrings_INTERFACE_DEFINED__
#define __IDWriteLocalizedStrings_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteLocalizedStrings, 0x08256209, 0x099a, 0x4b34, 0xb8,0x6d, 0xc2,0x2b,0x11,0x0e,0x77,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("08256209-099a-4b34-b86d-c22b110e7771")
IDWriteLocalizedStrings : public IUnknown
{
    virtual UINT32 STDMETHODCALLTYPE GetCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindLocaleName(
        const WCHAR *locale_name,
        UINT32 *index,
        WINBOOL *exists) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocaleNameLength(
        UINT32 index,
        UINT32 *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocaleName(
        UINT32 index,
        WCHAR *locale_name,
        UINT32 size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringLength(
        UINT32 index,
        UINT32 *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetString(
        UINT32 index,
        WCHAR *buffer,
        UINT32 size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteLocalizedStrings, 0x08256209, 0x099a, 0x4b34, 0xb8,0x6d, 0xc2,0x2b,0x11,0x0e,0x77,0x71)
#endif
#else
typedef struct IDWriteLocalizedStringsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteLocalizedStrings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteLocalizedStrings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteLocalizedStrings *This);

    /*** IDWriteLocalizedStrings methods ***/
    UINT32 (STDMETHODCALLTYPE *GetCount)(
        IDWriteLocalizedStrings *This);

    HRESULT (STDMETHODCALLTYPE *FindLocaleName)(
        IDWriteLocalizedStrings *This,
        const WCHAR *locale_name,
        UINT32 *index,
        WINBOOL *exists);

    HRESULT (STDMETHODCALLTYPE *GetLocaleNameLength)(
        IDWriteLocalizedStrings *This,
        UINT32 index,
        UINT32 *length);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteLocalizedStrings *This,
        UINT32 index,
        WCHAR *locale_name,
        UINT32 size);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IDWriteLocalizedStrings *This,
        UINT32 index,
        UINT32 *length);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IDWriteLocalizedStrings *This,
        UINT32 index,
        WCHAR *buffer,
        UINT32 size);

    END_INTERFACE
} IDWriteLocalizedStringsVtbl;

interface IDWriteLocalizedStrings {
    CONST_VTBL IDWriteLocalizedStringsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteLocalizedStrings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteLocalizedStrings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteLocalizedStrings_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteLocalizedStrings methods ***/
#define IDWriteLocalizedStrings_GetCount(This) (This)->lpVtbl->GetCount(This)
#define IDWriteLocalizedStrings_FindLocaleName(This,locale_name,index,exists) (This)->lpVtbl->FindLocaleName(This,locale_name,index,exists)
#define IDWriteLocalizedStrings_GetLocaleNameLength(This,index,length) (This)->lpVtbl->GetLocaleNameLength(This,index,length)
#define IDWriteLocalizedStrings_GetLocaleName(This,index,locale_name,size) (This)->lpVtbl->GetLocaleName(This,index,locale_name,size)
#define IDWriteLocalizedStrings_GetStringLength(This,index,length) (This)->lpVtbl->GetStringLength(This,index,length)
#define IDWriteLocalizedStrings_GetString(This,index,buffer,size) (This)->lpVtbl->GetString(This,index,buffer,size)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteLocalizedStrings_QueryInterface(IDWriteLocalizedStrings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteLocalizedStrings_AddRef(IDWriteLocalizedStrings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteLocalizedStrings_Release(IDWriteLocalizedStrings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteLocalizedStrings methods ***/
static inline UINT32 IDWriteLocalizedStrings_GetCount(IDWriteLocalizedStrings* This) {
    return This->lpVtbl->GetCount(This);
}
static inline HRESULT IDWriteLocalizedStrings_FindLocaleName(IDWriteLocalizedStrings* This,const WCHAR *locale_name,UINT32 *index,WINBOOL *exists) {
    return This->lpVtbl->FindLocaleName(This,locale_name,index,exists);
}
static inline HRESULT IDWriteLocalizedStrings_GetLocaleNameLength(IDWriteLocalizedStrings* This,UINT32 index,UINT32 *length) {
    return This->lpVtbl->GetLocaleNameLength(This,index,length);
}
static inline HRESULT IDWriteLocalizedStrings_GetLocaleName(IDWriteLocalizedStrings* This,UINT32 index,WCHAR *locale_name,UINT32 size) {
    return This->lpVtbl->GetLocaleName(This,index,locale_name,size);
}
static inline HRESULT IDWriteLocalizedStrings_GetStringLength(IDWriteLocalizedStrings* This,UINT32 index,UINT32 *length) {
    return This->lpVtbl->GetStringLength(This,index,length);
}
static inline HRESULT IDWriteLocalizedStrings_GetString(IDWriteLocalizedStrings* This,UINT32 index,WCHAR *buffer,UINT32 size) {
    return This->lpVtbl->GetString(This,index,buffer,size);
}
#endif
#endif

#endif


#endif  /* __IDWriteLocalizedStrings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteRenderingParams interface
 */
#ifndef __IDWriteRenderingParams_INTERFACE_DEFINED__
#define __IDWriteRenderingParams_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteRenderingParams, 0x2f0da53a, 0x2add, 0x47cd, 0x82,0xee, 0xd9,0xec,0x34,0x68,0x8e,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2f0da53a-2add-47cd-82ee-d9ec34688e75")
IDWriteRenderingParams : public IUnknown
{
    virtual FLOAT STDMETHODCALLTYPE GetGamma(
        ) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetEnhancedContrast(
        ) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetClearTypeLevel(
        ) = 0;

    virtual DWRITE_PIXEL_GEOMETRY STDMETHODCALLTYPE GetPixelGeometry(
        ) = 0;

    virtual DWRITE_RENDERING_MODE STDMETHODCALLTYPE GetRenderingMode(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteRenderingParams, 0x2f0da53a, 0x2add, 0x47cd, 0x82,0xee, 0xd9,0xec,0x34,0x68,0x8e,0x75)
#endif
#else
typedef struct IDWriteRenderingParamsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteRenderingParams *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteRenderingParams *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteRenderingParams *This);

    /*** IDWriteRenderingParams methods ***/
    FLOAT (STDMETHODCALLTYPE *GetGamma)(
        IDWriteRenderingParams *This);

    FLOAT (STDMETHODCALLTYPE *GetEnhancedContrast)(
        IDWriteRenderingParams *This);

    FLOAT (STDMETHODCALLTYPE *GetClearTypeLevel)(
        IDWriteRenderingParams *This);

    DWRITE_PIXEL_GEOMETRY (STDMETHODCALLTYPE *GetPixelGeometry)(
        IDWriteRenderingParams *This);

    DWRITE_RENDERING_MODE (STDMETHODCALLTYPE *GetRenderingMode)(
        IDWriteRenderingParams *This);

    END_INTERFACE
} IDWriteRenderingParamsVtbl;

interface IDWriteRenderingParams {
    CONST_VTBL IDWriteRenderingParamsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteRenderingParams_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteRenderingParams_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteRenderingParams_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteRenderingParams methods ***/
#define IDWriteRenderingParams_GetGamma(This) (This)->lpVtbl->GetGamma(This)
#define IDWriteRenderingParams_GetEnhancedContrast(This) (This)->lpVtbl->GetEnhancedContrast(This)
#define IDWriteRenderingParams_GetClearTypeLevel(This) (This)->lpVtbl->GetClearTypeLevel(This)
#define IDWriteRenderingParams_GetPixelGeometry(This) (This)->lpVtbl->GetPixelGeometry(This)
#define IDWriteRenderingParams_GetRenderingMode(This) (This)->lpVtbl->GetRenderingMode(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteRenderingParams_QueryInterface(IDWriteRenderingParams* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteRenderingParams_AddRef(IDWriteRenderingParams* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteRenderingParams_Release(IDWriteRenderingParams* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteRenderingParams methods ***/
static inline FLOAT IDWriteRenderingParams_GetGamma(IDWriteRenderingParams* This) {
    return This->lpVtbl->GetGamma(This);
}
static inline FLOAT IDWriteRenderingParams_GetEnhancedContrast(IDWriteRenderingParams* This) {
    return This->lpVtbl->GetEnhancedContrast(This);
}
static inline FLOAT IDWriteRenderingParams_GetClearTypeLevel(IDWriteRenderingParams* This) {
    return This->lpVtbl->GetClearTypeLevel(This);
}
static inline DWRITE_PIXEL_GEOMETRY IDWriteRenderingParams_GetPixelGeometry(IDWriteRenderingParams* This) {
    return This->lpVtbl->GetPixelGeometry(This);
}
static inline DWRITE_RENDERING_MODE IDWriteRenderingParams_GetRenderingMode(IDWriteRenderingParams* This) {
    return This->lpVtbl->GetRenderingMode(This);
}
#endif
#endif

#endif


#endif  /* __IDWriteRenderingParams_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFace interface
 */
#ifndef __IDWriteFontFace_INTERFACE_DEFINED__
#define __IDWriteFontFace_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFace, 0x5f49804d, 0x7024, 0x4d43, 0xbf,0xa9, 0xd2,0x59,0x84,0xf5,0x38,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5f49804d-7024-4d43-bfa9-d25984f53849")
IDWriteFontFace : public IUnknown
{
    virtual DWRITE_FONT_FACE_TYPE STDMETHODCALLTYPE GetType(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFiles(
        UINT32 *number_of_files,
        IDWriteFontFile **fontfiles) = 0;

    virtual UINT32 STDMETHODCALLTYPE GetIndex(
        ) = 0;

    virtual DWRITE_FONT_SIMULATIONS STDMETHODCALLTYPE GetSimulations(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsSymbolFont(
        ) = 0;

    virtual void STDMETHODCALLTYPE GetMetrics(
        DWRITE_FONT_METRICS *metrics) = 0;

    virtual UINT16 STDMETHODCALLTYPE GetGlyphCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesignGlyphMetrics(
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphIndices(
        const UINT32 *codepoints,
        UINT32 count,
        UINT16 *glyph_indices) = 0;

    virtual HRESULT STDMETHODCALLTYPE TryGetFontTable(
        UINT32 table_tag,
        const void **table_data,
        UINT32 *table_size,
        void **context,
        WINBOOL *exists) = 0;

    virtual void STDMETHODCALLTYPE ReleaseFontTable(
        void *table_context) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphRunOutline(
        FLOAT emSize,
        const UINT16 *glyph_indices,
        const FLOAT *glyph_advances,
        const DWRITE_GLYPH_OFFSET *glyph_offsets,
        UINT32 glyph_count,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IDWriteGeometrySink *geometrysink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRecommendedRenderingMode(
        FLOAT emSize,
        FLOAT pixels_per_dip,
        DWRITE_MEASURING_MODE mode,
        IDWriteRenderingParams *params,
        DWRITE_RENDERING_MODE *rendering_mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGdiCompatibleMetrics(
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_FONT_METRICS *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGdiCompatibleGlyphMetrics(
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways = FALSE) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFace, 0x5f49804d, 0x7024, 0x4d43, 0xbf,0xa9, 0xd2,0x59,0x84,0xf5,0x38,0x49)
#endif
#else
typedef struct IDWriteFontFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFace *This);

    /*** IDWriteFontFace methods ***/
    DWRITE_FONT_FACE_TYPE (STDMETHODCALLTYPE *GetType)(
        IDWriteFontFace *This);

    HRESULT (STDMETHODCALLTYPE *GetFiles)(
        IDWriteFontFace *This,
        UINT32 *number_of_files,
        IDWriteFontFile **fontfiles);

    UINT32 (STDMETHODCALLTYPE *GetIndex)(
        IDWriteFontFace *This);

    DWRITE_FONT_SIMULATIONS (STDMETHODCALLTYPE *GetSimulations)(
        IDWriteFontFace *This);

    WINBOOL (STDMETHODCALLTYPE *IsSymbolFont)(
        IDWriteFontFace *This);

    void (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteFontFace *This,
        DWRITE_FONT_METRICS *metrics);

    UINT16 (STDMETHODCALLTYPE *GetGlyphCount)(
        IDWriteFontFace *This);

    HRESULT (STDMETHODCALLTYPE *GetDesignGlyphMetrics)(
        IDWriteFontFace *This,
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways);

    HRESULT (STDMETHODCALLTYPE *GetGlyphIndices)(
        IDWriteFontFace *This,
        const UINT32 *codepoints,
        UINT32 count,
        UINT16 *glyph_indices);

    HRESULT (STDMETHODCALLTYPE *TryGetFontTable)(
        IDWriteFontFace *This,
        UINT32 table_tag,
        const void **table_data,
        UINT32 *table_size,
        void **context,
        WINBOOL *exists);

    void (STDMETHODCALLTYPE *ReleaseFontTable)(
        IDWriteFontFace *This,
        void *table_context);

    HRESULT (STDMETHODCALLTYPE *GetGlyphRunOutline)(
        IDWriteFontFace *This,
        FLOAT emSize,
        const UINT16 *glyph_indices,
        const FLOAT *glyph_advances,
        const DWRITE_GLYPH_OFFSET *glyph_offsets,
        UINT32 glyph_count,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IDWriteGeometrySink *geometrysink);

    HRESULT (STDMETHODCALLTYPE *GetRecommendedRenderingMode)(
        IDWriteFontFace *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        DWRITE_MEASURING_MODE mode,
        IDWriteRenderingParams *params,
        DWRITE_RENDERING_MODE *rendering_mode);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleMetrics)(
        IDWriteFontFace *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_FONT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphMetrics)(
        IDWriteFontFace *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways);

    END_INTERFACE
} IDWriteFontFaceVtbl;

interface IDWriteFontFace {
    CONST_VTBL IDWriteFontFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFace_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFace methods ***/
#define IDWriteFontFace_GetType(This) (This)->lpVtbl->GetType(This)
#define IDWriteFontFace_GetFiles(This,number_of_files,fontfiles) (This)->lpVtbl->GetFiles(This,number_of_files,fontfiles)
#define IDWriteFontFace_GetIndex(This) (This)->lpVtbl->GetIndex(This)
#define IDWriteFontFace_GetSimulations(This) (This)->lpVtbl->GetSimulations(This)
#define IDWriteFontFace_IsSymbolFont(This) (This)->lpVtbl->IsSymbolFont(This)
#define IDWriteFontFace_GetMetrics(This,metrics) (This)->lpVtbl->GetMetrics(This,metrics)
#define IDWriteFontFace_GetGlyphCount(This) (This)->lpVtbl->GetGlyphCount(This)
#define IDWriteFontFace_GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways) (This)->lpVtbl->GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways)
#define IDWriteFontFace_GetGlyphIndices(This,codepoints,count,glyph_indices) (This)->lpVtbl->GetGlyphIndices(This,codepoints,count,glyph_indices)
#define IDWriteFontFace_TryGetFontTable(This,table_tag,table_data,table_size,context,exists) (This)->lpVtbl->TryGetFontTable(This,table_tag,table_data,table_size,context,exists)
#define IDWriteFontFace_ReleaseFontTable(This,table_context) (This)->lpVtbl->ReleaseFontTable(This,table_context)
#define IDWriteFontFace_GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink) (This)->lpVtbl->GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink)
#define IDWriteFontFace_GetRecommendedRenderingMode(This,emSize,pixels_per_dip,mode,params,rendering_mode) (This)->lpVtbl->GetRecommendedRenderingMode(This,emSize,pixels_per_dip,mode,params,rendering_mode)
#define IDWriteFontFace_GetGdiCompatibleMetrics(This,emSize,pixels_per_dip,transform,metrics) (This)->lpVtbl->GetGdiCompatibleMetrics(This,emSize,pixels_per_dip,transform,metrics)
#define IDWriteFontFace_GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways) (This)->lpVtbl->GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFace_QueryInterface(IDWriteFontFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFace_AddRef(IDWriteFontFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFace_Release(IDWriteFontFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFace methods ***/
static inline DWRITE_FONT_FACE_TYPE IDWriteFontFace_GetType(IDWriteFontFace* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT IDWriteFontFace_GetFiles(IDWriteFontFace* This,UINT32 *number_of_files,IDWriteFontFile **fontfiles) {
    return This->lpVtbl->GetFiles(This,number_of_files,fontfiles);
}
static inline UINT32 IDWriteFontFace_GetIndex(IDWriteFontFace* This) {
    return This->lpVtbl->GetIndex(This);
}
static inline DWRITE_FONT_SIMULATIONS IDWriteFontFace_GetSimulations(IDWriteFontFace* This) {
    return This->lpVtbl->GetSimulations(This);
}
static inline WINBOOL IDWriteFontFace_IsSymbolFont(IDWriteFontFace* This) {
    return This->lpVtbl->IsSymbolFont(This);
}
static inline void IDWriteFontFace_GetMetrics(IDWriteFontFace* This,DWRITE_FONT_METRICS *metrics) {
    This->lpVtbl->GetMetrics(This,metrics);
}
static inline UINT16 IDWriteFontFace_GetGlyphCount(IDWriteFontFace* This) {
    return This->lpVtbl->GetGlyphCount(This);
}
static inline HRESULT IDWriteFontFace_GetDesignGlyphMetrics(IDWriteFontFace* This,const UINT16 *glyph_indices,UINT32 glyph_count,DWRITE_GLYPH_METRICS *metrics,WINBOOL is_sideways) {
    return This->lpVtbl->GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways);
}
static inline HRESULT IDWriteFontFace_GetGlyphIndices(IDWriteFontFace* This,const UINT32 *codepoints,UINT32 count,UINT16 *glyph_indices) {
    return This->lpVtbl->GetGlyphIndices(This,codepoints,count,glyph_indices);
}
static inline HRESULT IDWriteFontFace_TryGetFontTable(IDWriteFontFace* This,UINT32 table_tag,const void **table_data,UINT32 *table_size,void **context,WINBOOL *exists) {
    return This->lpVtbl->TryGetFontTable(This,table_tag,table_data,table_size,context,exists);
}
static inline void IDWriteFontFace_ReleaseFontTable(IDWriteFontFace* This,void *table_context) {
    This->lpVtbl->ReleaseFontTable(This,table_context);
}
static inline HRESULT IDWriteFontFace_GetGlyphRunOutline(IDWriteFontFace* This,FLOAT emSize,const UINT16 *glyph_indices,const FLOAT *glyph_advances,const DWRITE_GLYPH_OFFSET *glyph_offsets,UINT32 glyph_count,WINBOOL is_sideways,WINBOOL is_rtl,IDWriteGeometrySink *geometrysink) {
    return This->lpVtbl->GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink);
}
static inline HRESULT IDWriteFontFace_GetRecommendedRenderingMode(IDWriteFontFace* This,FLOAT emSize,FLOAT pixels_per_dip,DWRITE_MEASURING_MODE mode,IDWriteRenderingParams *params,DWRITE_RENDERING_MODE *rendering_mode) {
    return This->lpVtbl->GetRecommendedRenderingMode(This,emSize,pixels_per_dip,mode,params,rendering_mode);
}
static inline HRESULT IDWriteFontFace_GetGdiCompatibleMetrics(IDWriteFontFace* This,FLOAT emSize,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,DWRITE_FONT_METRICS *metrics) {
    return This->lpVtbl->GetGdiCompatibleMetrics(This,emSize,pixels_per_dip,transform,metrics);
}
static inline HRESULT IDWriteFontFace_GetGdiCompatibleGlyphMetrics(IDWriteFontFace* This,FLOAT emSize,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,const UINT16 *glyph_indices,UINT32 glyph_count,DWRITE_GLYPH_METRICS *metrics,WINBOOL is_sideways) {
    return This->lpVtbl->GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFont interface
 */
#ifndef __IDWriteFont_INTERFACE_DEFINED__
#define __IDWriteFont_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFont, 0xacd16696, 0x8c14, 0x4f5d, 0x87,0x7e, 0xfe,0x3f,0xc1,0xd3,0x27,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("acd16696-8c14-4f5d-877e-fe3fc1d32737")
IDWriteFont : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetFontFamily(
        IDWriteFontFamily **family) = 0;

    virtual DWRITE_FONT_WEIGHT STDMETHODCALLTYPE GetWeight(
        ) = 0;

    virtual DWRITE_FONT_STRETCH STDMETHODCALLTYPE GetStretch(
        ) = 0;

    virtual DWRITE_FONT_STYLE STDMETHODCALLTYPE GetStyle(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsSymbolFont(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFaceNames(
        IDWriteLocalizedStrings **names) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInformationalStrings(
        DWRITE_INFORMATIONAL_STRING_ID stringid,
        IDWriteLocalizedStrings **strings,
        WINBOOL *exists) = 0;

    virtual DWRITE_FONT_SIMULATIONS STDMETHODCALLTYPE GetSimulations(
        ) = 0;

    virtual void STDMETHODCALLTYPE GetMetrics(
        DWRITE_FONT_METRICS *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasCharacter(
        UINT32 value,
        WINBOOL *exists) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFontFace(
        IDWriteFontFace **face) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFont, 0xacd16696, 0x8c14, 0x4f5d, 0x87,0x7e, 0xfe,0x3f,0xc1,0xd3,0x27,0x37)
#endif
#else
typedef struct IDWriteFontVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFont *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFont *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFont *This);

    /*** IDWriteFont methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFontFamily)(
        IDWriteFont *This,
        IDWriteFontFamily **family);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetWeight)(
        IDWriteFont *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetStretch)(
        IDWriteFont *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetStyle)(
        IDWriteFont *This);

    WINBOOL (STDMETHODCALLTYPE *IsSymbolFont)(
        IDWriteFont *This);

    HRESULT (STDMETHODCALLTYPE *GetFaceNames)(
        IDWriteFont *This,
        IDWriteLocalizedStrings **names);

    HRESULT (STDMETHODCALLTYPE *GetInformationalStrings)(
        IDWriteFont *This,
        DWRITE_INFORMATIONAL_STRING_ID stringid,
        IDWriteLocalizedStrings **strings,
        WINBOOL *exists);

    DWRITE_FONT_SIMULATIONS (STDMETHODCALLTYPE *GetSimulations)(
        IDWriteFont *This);

    void (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteFont *This,
        DWRITE_FONT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HasCharacter)(
        IDWriteFont *This,
        UINT32 value,
        WINBOOL *exists);

    HRESULT (STDMETHODCALLTYPE *CreateFontFace)(
        IDWriteFont *This,
        IDWriteFontFace **face);

    END_INTERFACE
} IDWriteFontVtbl;

interface IDWriteFont {
    CONST_VTBL IDWriteFontVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFont_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFont_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFont_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFont methods ***/
#define IDWriteFont_GetFontFamily(This,family) (This)->lpVtbl->GetFontFamily(This,family)
#define IDWriteFont_GetWeight(This) (This)->lpVtbl->GetWeight(This)
#define IDWriteFont_GetStretch(This) (This)->lpVtbl->GetStretch(This)
#define IDWriteFont_GetStyle(This) (This)->lpVtbl->GetStyle(This)
#define IDWriteFont_IsSymbolFont(This) (This)->lpVtbl->IsSymbolFont(This)
#define IDWriteFont_GetFaceNames(This,names) (This)->lpVtbl->GetFaceNames(This,names)
#define IDWriteFont_GetInformationalStrings(This,stringid,strings,exists) (This)->lpVtbl->GetInformationalStrings(This,stringid,strings,exists)
#define IDWriteFont_GetSimulations(This) (This)->lpVtbl->GetSimulations(This)
#define IDWriteFont_GetMetrics(This,metrics) (This)->lpVtbl->GetMetrics(This,metrics)
#define IDWriteFont_HasCharacter(This,value,exists) (This)->lpVtbl->HasCharacter(This,value,exists)
#define IDWriteFont_CreateFontFace(This,face) (This)->lpVtbl->CreateFontFace(This,face)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFont_QueryInterface(IDWriteFont* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFont_AddRef(IDWriteFont* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFont_Release(IDWriteFont* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFont methods ***/
static inline HRESULT IDWriteFont_GetFontFamily(IDWriteFont* This,IDWriteFontFamily **family) {
    return This->lpVtbl->GetFontFamily(This,family);
}
static inline DWRITE_FONT_WEIGHT IDWriteFont_GetWeight(IDWriteFont* This) {
    return This->lpVtbl->GetWeight(This);
}
static inline DWRITE_FONT_STRETCH IDWriteFont_GetStretch(IDWriteFont* This) {
    return This->lpVtbl->GetStretch(This);
}
static inline DWRITE_FONT_STYLE IDWriteFont_GetStyle(IDWriteFont* This) {
    return This->lpVtbl->GetStyle(This);
}
static inline WINBOOL IDWriteFont_IsSymbolFont(IDWriteFont* This) {
    return This->lpVtbl->IsSymbolFont(This);
}
static inline HRESULT IDWriteFont_GetFaceNames(IDWriteFont* This,IDWriteLocalizedStrings **names) {
    return This->lpVtbl->GetFaceNames(This,names);
}
static inline HRESULT IDWriteFont_GetInformationalStrings(IDWriteFont* This,DWRITE_INFORMATIONAL_STRING_ID stringid,IDWriteLocalizedStrings **strings,WINBOOL *exists) {
    return This->lpVtbl->GetInformationalStrings(This,stringid,strings,exists);
}
static inline DWRITE_FONT_SIMULATIONS IDWriteFont_GetSimulations(IDWriteFont* This) {
    return This->lpVtbl->GetSimulations(This);
}
static inline void IDWriteFont_GetMetrics(IDWriteFont* This,DWRITE_FONT_METRICS *metrics) {
    This->lpVtbl->GetMetrics(This,metrics);
}
static inline HRESULT IDWriteFont_HasCharacter(IDWriteFont* This,UINT32 value,WINBOOL *exists) {
    return This->lpVtbl->HasCharacter(This,value,exists);
}
static inline HRESULT IDWriteFont_CreateFontFace(IDWriteFont* This,IDWriteFontFace **face) {
    return This->lpVtbl->CreateFontFace(This,face);
}
#endif
#endif

#endif


#endif  /* __IDWriteFont_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontList interface
 */
#ifndef __IDWriteFontList_INTERFACE_DEFINED__
#define __IDWriteFontList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontList, 0x1a0d8438, 0x1d97, 0x4ec1, 0xae,0xf9, 0xa2,0xfb,0x86,0xed,0x6a,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1a0d8438-1d97-4ec1-aef9-a2fb86ed6acb")
IDWriteFontList : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetFontCollection(
        IDWriteFontCollection **collection) = 0;

    virtual UINT32 STDMETHODCALLTYPE GetFontCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFont(
        UINT32 index,
        IDWriteFont **font) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontList, 0x1a0d8438, 0x1d97, 0x4ec1, 0xae,0xf9, 0xa2,0xfb,0x86,0xed,0x6a,0xcb)
#endif
#else
typedef struct IDWriteFontListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontList *This);

    /*** IDWriteFontList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFontCollection)(
        IDWriteFontList *This,
        IDWriteFontCollection **collection);

    UINT32 (STDMETHODCALLTYPE *GetFontCount)(
        IDWriteFontList *This);

    HRESULT (STDMETHODCALLTYPE *GetFont)(
        IDWriteFontList *This,
        UINT32 index,
        IDWriteFont **font);

    END_INTERFACE
} IDWriteFontListVtbl;

interface IDWriteFontList {
    CONST_VTBL IDWriteFontListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontList_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontList methods ***/
#define IDWriteFontList_GetFontCollection(This,collection) (This)->lpVtbl->GetFontCollection(This,collection)
#define IDWriteFontList_GetFontCount(This) (This)->lpVtbl->GetFontCount(This)
#define IDWriteFontList_GetFont(This,index,font) (This)->lpVtbl->GetFont(This,index,font)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontList_QueryInterface(IDWriteFontList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontList_AddRef(IDWriteFontList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontList_Release(IDWriteFontList* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontList methods ***/
static inline HRESULT IDWriteFontList_GetFontCollection(IDWriteFontList* This,IDWriteFontCollection **collection) {
    return This->lpVtbl->GetFontCollection(This,collection);
}
static inline UINT32 IDWriteFontList_GetFontCount(IDWriteFontList* This) {
    return This->lpVtbl->GetFontCount(This);
}
static inline HRESULT IDWriteFontList_GetFont(IDWriteFontList* This,UINT32 index,IDWriteFont **font) {
    return This->lpVtbl->GetFont(This,index,font);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFamily interface
 */
#ifndef __IDWriteFontFamily_INTERFACE_DEFINED__
#define __IDWriteFontFamily_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFamily, 0xda20d8ef, 0x812a, 0x4c43, 0x98,0x02, 0x62,0xec,0x4a,0xbd,0x7a,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("da20d8ef-812a-4c43-9802-62ec4abd7add")
IDWriteFontFamily : public IDWriteFontList
{
    virtual HRESULT STDMETHODCALLTYPE GetFamilyNames(
        IDWriteLocalizedStrings **names) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFirstMatchingFont(
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STRETCH stretch,
        DWRITE_FONT_STYLE style,
        IDWriteFont **font) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMatchingFonts(
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STRETCH stretch,
        DWRITE_FONT_STYLE style,
        IDWriteFontList **fonts) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFamily, 0xda20d8ef, 0x812a, 0x4c43, 0x98,0x02, 0x62,0xec,0x4a,0xbd,0x7a,0xdd)
#endif
#else
typedef struct IDWriteFontFamilyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFamily *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFamily *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFamily *This);

    /*** IDWriteFontList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFontCollection)(
        IDWriteFontFamily *This,
        IDWriteFontCollection **collection);

    UINT32 (STDMETHODCALLTYPE *GetFontCount)(
        IDWriteFontFamily *This);

    HRESULT (STDMETHODCALLTYPE *GetFont)(
        IDWriteFontFamily *This,
        UINT32 index,
        IDWriteFont **font);

    /*** IDWriteFontFamily methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFamilyNames)(
        IDWriteFontFamily *This,
        IDWriteLocalizedStrings **names);

    HRESULT (STDMETHODCALLTYPE *GetFirstMatchingFont)(
        IDWriteFontFamily *This,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STRETCH stretch,
        DWRITE_FONT_STYLE style,
        IDWriteFont **font);

    HRESULT (STDMETHODCALLTYPE *GetMatchingFonts)(
        IDWriteFontFamily *This,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STRETCH stretch,
        DWRITE_FONT_STYLE style,
        IDWriteFontList **fonts);

    END_INTERFACE
} IDWriteFontFamilyVtbl;

interface IDWriteFontFamily {
    CONST_VTBL IDWriteFontFamilyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFamily_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFamily_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFamily_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontList methods ***/
#define IDWriteFontFamily_GetFontCollection(This,collection) (This)->lpVtbl->GetFontCollection(This,collection)
#define IDWriteFontFamily_GetFontCount(This) (This)->lpVtbl->GetFontCount(This)
#define IDWriteFontFamily_GetFont(This,index,font) (This)->lpVtbl->GetFont(This,index,font)
/*** IDWriteFontFamily methods ***/
#define IDWriteFontFamily_GetFamilyNames(This,names) (This)->lpVtbl->GetFamilyNames(This,names)
#define IDWriteFontFamily_GetFirstMatchingFont(This,weight,stretch,style,font) (This)->lpVtbl->GetFirstMatchingFont(This,weight,stretch,style,font)
#define IDWriteFontFamily_GetMatchingFonts(This,weight,stretch,style,fonts) (This)->lpVtbl->GetMatchingFonts(This,weight,stretch,style,fonts)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFamily_QueryInterface(IDWriteFontFamily* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFamily_AddRef(IDWriteFontFamily* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFamily_Release(IDWriteFontFamily* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontList methods ***/
static inline HRESULT IDWriteFontFamily_GetFontCollection(IDWriteFontFamily* This,IDWriteFontCollection **collection) {
    return This->lpVtbl->GetFontCollection(This,collection);
}
static inline UINT32 IDWriteFontFamily_GetFontCount(IDWriteFontFamily* This) {
    return This->lpVtbl->GetFontCount(This);
}
static inline HRESULT IDWriteFontFamily_GetFont(IDWriteFontFamily* This,UINT32 index,IDWriteFont **font) {
    return This->lpVtbl->GetFont(This,index,font);
}
/*** IDWriteFontFamily methods ***/
static inline HRESULT IDWriteFontFamily_GetFamilyNames(IDWriteFontFamily* This,IDWriteLocalizedStrings **names) {
    return This->lpVtbl->GetFamilyNames(This,names);
}
static inline HRESULT IDWriteFontFamily_GetFirstMatchingFont(IDWriteFontFamily* This,DWRITE_FONT_WEIGHT weight,DWRITE_FONT_STRETCH stretch,DWRITE_FONT_STYLE style,IDWriteFont **font) {
    return This->lpVtbl->GetFirstMatchingFont(This,weight,stretch,style,font);
}
static inline HRESULT IDWriteFontFamily_GetMatchingFonts(IDWriteFontFamily* This,DWRITE_FONT_WEIGHT weight,DWRITE_FONT_STRETCH stretch,DWRITE_FONT_STYLE style,IDWriteFontList **fonts) {
    return This->lpVtbl->GetMatchingFonts(This,weight,stretch,style,fonts);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFamily_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontCollection interface
 */
#ifndef __IDWriteFontCollection_INTERFACE_DEFINED__
#define __IDWriteFontCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontCollection, 0xa84cee02, 0x3eea, 0x4eee, 0xa8,0x27, 0x87,0xc1,0xa0,0x2a,0x0f,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a84cee02-3eea-4eee-a827-87c1a02a0fcc")
IDWriteFontCollection : public IUnknown
{
    virtual UINT32 STDMETHODCALLTYPE GetFontFamilyCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFamily(
        UINT32 index,
        IDWriteFontFamily **family) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindFamilyName(
        const WCHAR *name,
        UINT32 *index,
        WINBOOL *exists) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFromFontFace(
        IDWriteFontFace *face,
        IDWriteFont **font) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontCollection, 0xa84cee02, 0x3eea, 0x4eee, 0xa8,0x27, 0x87,0xc1,0xa0,0x2a,0x0f,0xcc)
#endif
#else
typedef struct IDWriteFontCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontCollection *This);

    /*** IDWriteFontCollection methods ***/
    UINT32 (STDMETHODCALLTYPE *GetFontFamilyCount)(
        IDWriteFontCollection *This);

    HRESULT (STDMETHODCALLTYPE *GetFontFamily)(
        IDWriteFontCollection *This,
        UINT32 index,
        IDWriteFontFamily **family);

    HRESULT (STDMETHODCALLTYPE *FindFamilyName)(
        IDWriteFontCollection *This,
        const WCHAR *name,
        UINT32 *index,
        WINBOOL *exists);

    HRESULT (STDMETHODCALLTYPE *GetFontFromFontFace)(
        IDWriteFontCollection *This,
        IDWriteFontFace *face,
        IDWriteFont **font);

    END_INTERFACE
} IDWriteFontCollectionVtbl;

interface IDWriteFontCollection {
    CONST_VTBL IDWriteFontCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontCollection methods ***/
#define IDWriteFontCollection_GetFontFamilyCount(This) (This)->lpVtbl->GetFontFamilyCount(This)
#define IDWriteFontCollection_GetFontFamily(This,index,family) (This)->lpVtbl->GetFontFamily(This,index,family)
#define IDWriteFontCollection_FindFamilyName(This,name,index,exists) (This)->lpVtbl->FindFamilyName(This,name,index,exists)
#define IDWriteFontCollection_GetFontFromFontFace(This,face,font) (This)->lpVtbl->GetFontFromFontFace(This,face,font)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontCollection_QueryInterface(IDWriteFontCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontCollection_AddRef(IDWriteFontCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontCollection_Release(IDWriteFontCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontCollection methods ***/
static inline UINT32 IDWriteFontCollection_GetFontFamilyCount(IDWriteFontCollection* This) {
    return This->lpVtbl->GetFontFamilyCount(This);
}
static inline HRESULT IDWriteFontCollection_GetFontFamily(IDWriteFontCollection* This,UINT32 index,IDWriteFontFamily **family) {
    return This->lpVtbl->GetFontFamily(This,index,family);
}
static inline HRESULT IDWriteFontCollection_FindFamilyName(IDWriteFontCollection* This,const WCHAR *name,UINT32 *index,WINBOOL *exists) {
    return This->lpVtbl->FindFamilyName(This,name,index,exists);
}
static inline HRESULT IDWriteFontCollection_GetFontFromFontFace(IDWriteFontCollection* This,IDWriteFontFace *face,IDWriteFont **font) {
    return This->lpVtbl->GetFontFromFontFace(This,face,font);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWritePixelSnapping interface
 */
#ifndef __IDWritePixelSnapping_INTERFACE_DEFINED__
#define __IDWritePixelSnapping_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWritePixelSnapping, 0xeaf3a2da, 0xecf4, 0x4d24, 0xb6,0x44, 0xb3,0x4f,0x68,0x42,0x02,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eaf3a2da-ecf4-4d24-b644-b34f6842024b")
IDWritePixelSnapping : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsPixelSnappingDisabled(
        void *client_drawingcontext,
        WINBOOL *disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentTransform(
        void *client_drawingcontext,
        DWRITE_MATRIX *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPixelsPerDip(
        void *client_drawingcontext,
        FLOAT *pixels_per_dip) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWritePixelSnapping, 0xeaf3a2da, 0xecf4, 0x4d24, 0xb6,0x44, 0xb3,0x4f,0x68,0x42,0x02,0x4b)
#endif
#else
typedef struct IDWritePixelSnappingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWritePixelSnapping *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWritePixelSnapping *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWritePixelSnapping *This);

    /*** IDWritePixelSnapping methods ***/
    HRESULT (STDMETHODCALLTYPE *IsPixelSnappingDisabled)(
        IDWritePixelSnapping *This,
        void *client_drawingcontext,
        WINBOOL *disabled);

    HRESULT (STDMETHODCALLTYPE *GetCurrentTransform)(
        IDWritePixelSnapping *This,
        void *client_drawingcontext,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetPixelsPerDip)(
        IDWritePixelSnapping *This,
        void *client_drawingcontext,
        FLOAT *pixels_per_dip);

    END_INTERFACE
} IDWritePixelSnappingVtbl;

interface IDWritePixelSnapping {
    CONST_VTBL IDWritePixelSnappingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWritePixelSnapping_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWritePixelSnapping_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWritePixelSnapping_Release(This) (This)->lpVtbl->Release(This)
/*** IDWritePixelSnapping methods ***/
#define IDWritePixelSnapping_IsPixelSnappingDisabled(This,client_drawingcontext,disabled) (This)->lpVtbl->IsPixelSnappingDisabled(This,client_drawingcontext,disabled)
#define IDWritePixelSnapping_GetCurrentTransform(This,client_drawingcontext,transform) (This)->lpVtbl->GetCurrentTransform(This,client_drawingcontext,transform)
#define IDWritePixelSnapping_GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip) (This)->lpVtbl->GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWritePixelSnapping_QueryInterface(IDWritePixelSnapping* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWritePixelSnapping_AddRef(IDWritePixelSnapping* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWritePixelSnapping_Release(IDWritePixelSnapping* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWritePixelSnapping methods ***/
static inline HRESULT IDWritePixelSnapping_IsPixelSnappingDisabled(IDWritePixelSnapping* This,void *client_drawingcontext,WINBOOL *disabled) {
    return This->lpVtbl->IsPixelSnappingDisabled(This,client_drawingcontext,disabled);
}
static inline HRESULT IDWritePixelSnapping_GetCurrentTransform(IDWritePixelSnapping* This,void *client_drawingcontext,DWRITE_MATRIX *transform) {
    return This->lpVtbl->GetCurrentTransform(This,client_drawingcontext,transform);
}
static inline HRESULT IDWritePixelSnapping_GetPixelsPerDip(IDWritePixelSnapping* This,void *client_drawingcontext,FLOAT *pixels_per_dip) {
    return This->lpVtbl->GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip);
}
#endif
#endif

#endif


#endif  /* __IDWritePixelSnapping_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextRenderer interface
 */
#ifndef __IDWriteTextRenderer_INTERFACE_DEFINED__
#define __IDWriteTextRenderer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextRenderer, 0xef8a8135, 0x5cc6, 0x45fe, 0x88,0x25, 0xc5,0xa0,0x72,0x4e,0xb8,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ef8a8135-5cc6-45fe-8825-c5a0724eb819")
IDWriteTextRenderer : public IDWritePixelSnapping
{
    virtual HRESULT STDMETHODCALLTYPE DrawGlyphRun(
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        DWRITE_MEASURING_MODE mode,
        const DWRITE_GLYPH_RUN *glyph_run,
        const DWRITE_GLYPH_RUN_DESCRIPTION *run_descr,
        IUnknown *drawing_effect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DrawUnderline(
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        const DWRITE_UNDERLINE *underline,
        IUnknown *drawing_effect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DrawStrikethrough(
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        const DWRITE_STRIKETHROUGH *strikethrough,
        IUnknown *drawing_effect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DrawInlineObject(
        void *client_drawingcontext,
        FLOAT originX,
        FLOAT originY,
        IDWriteInlineObject *object,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IUnknown *drawing_effect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextRenderer, 0xef8a8135, 0x5cc6, 0x45fe, 0x88,0x25, 0xc5,0xa0,0x72,0x4e,0xb8,0x19)
#endif
#else
typedef struct IDWriteTextRendererVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextRenderer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextRenderer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextRenderer *This);

    /*** IDWritePixelSnapping methods ***/
    HRESULT (STDMETHODCALLTYPE *IsPixelSnappingDisabled)(
        IDWriteTextRenderer *This,
        void *client_drawingcontext,
        WINBOOL *disabled);

    HRESULT (STDMETHODCALLTYPE *GetCurrentTransform)(
        IDWriteTextRenderer *This,
        void *client_drawingcontext,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetPixelsPerDip)(
        IDWriteTextRenderer *This,
        void *client_drawingcontext,
        FLOAT *pixels_per_dip);

    /*** IDWriteTextRenderer methods ***/
    HRESULT (STDMETHODCALLTYPE *DrawGlyphRun)(
        IDWriteTextRenderer *This,
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        DWRITE_MEASURING_MODE mode,
        const DWRITE_GLYPH_RUN *glyph_run,
        const DWRITE_GLYPH_RUN_DESCRIPTION *run_descr,
        IUnknown *drawing_effect);

    HRESULT (STDMETHODCALLTYPE *DrawUnderline)(
        IDWriteTextRenderer *This,
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        const DWRITE_UNDERLINE *underline,
        IUnknown *drawing_effect);

    HRESULT (STDMETHODCALLTYPE *DrawStrikethrough)(
        IDWriteTextRenderer *This,
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        const DWRITE_STRIKETHROUGH *strikethrough,
        IUnknown *drawing_effect);

    HRESULT (STDMETHODCALLTYPE *DrawInlineObject)(
        IDWriteTextRenderer *This,
        void *client_drawingcontext,
        FLOAT originX,
        FLOAT originY,
        IDWriteInlineObject *object,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IUnknown *drawing_effect);

    END_INTERFACE
} IDWriteTextRendererVtbl;

interface IDWriteTextRenderer {
    CONST_VTBL IDWriteTextRendererVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextRenderer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextRenderer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextRenderer_Release(This) (This)->lpVtbl->Release(This)
/*** IDWritePixelSnapping methods ***/
#define IDWriteTextRenderer_IsPixelSnappingDisabled(This,client_drawingcontext,disabled) (This)->lpVtbl->IsPixelSnappingDisabled(This,client_drawingcontext,disabled)
#define IDWriteTextRenderer_GetCurrentTransform(This,client_drawingcontext,transform) (This)->lpVtbl->GetCurrentTransform(This,client_drawingcontext,transform)
#define IDWriteTextRenderer_GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip) (This)->lpVtbl->GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip)
/*** IDWriteTextRenderer methods ***/
#define IDWriteTextRenderer_DrawGlyphRun(This,client_drawingcontext,baselineOriginX,baselineOriginY,mode,glyph_run,run_descr,drawing_effect) (This)->lpVtbl->DrawGlyphRun(This,client_drawingcontext,baselineOriginX,baselineOriginY,mode,glyph_run,run_descr,drawing_effect)
#define IDWriteTextRenderer_DrawUnderline(This,client_drawingcontext,baselineOriginX,baselineOriginY,underline,drawing_effect) (This)->lpVtbl->DrawUnderline(This,client_drawingcontext,baselineOriginX,baselineOriginY,underline,drawing_effect)
#define IDWriteTextRenderer_DrawStrikethrough(This,client_drawingcontext,baselineOriginX,baselineOriginY,strikethrough,drawing_effect) (This)->lpVtbl->DrawStrikethrough(This,client_drawingcontext,baselineOriginX,baselineOriginY,strikethrough,drawing_effect)
#define IDWriteTextRenderer_DrawInlineObject(This,client_drawingcontext,originX,originY,object,is_sideways,is_rtl,drawing_effect) (This)->lpVtbl->DrawInlineObject(This,client_drawingcontext,originX,originY,object,is_sideways,is_rtl,drawing_effect)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextRenderer_QueryInterface(IDWriteTextRenderer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextRenderer_AddRef(IDWriteTextRenderer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextRenderer_Release(IDWriteTextRenderer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWritePixelSnapping methods ***/
static inline HRESULT IDWriteTextRenderer_IsPixelSnappingDisabled(IDWriteTextRenderer* This,void *client_drawingcontext,WINBOOL *disabled) {
    return This->lpVtbl->IsPixelSnappingDisabled(This,client_drawingcontext,disabled);
}
static inline HRESULT IDWriteTextRenderer_GetCurrentTransform(IDWriteTextRenderer* This,void *client_drawingcontext,DWRITE_MATRIX *transform) {
    return This->lpVtbl->GetCurrentTransform(This,client_drawingcontext,transform);
}
static inline HRESULT IDWriteTextRenderer_GetPixelsPerDip(IDWriteTextRenderer* This,void *client_drawingcontext,FLOAT *pixels_per_dip) {
    return This->lpVtbl->GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip);
}
/*** IDWriteTextRenderer methods ***/
static inline HRESULT IDWriteTextRenderer_DrawGlyphRun(IDWriteTextRenderer* This,void *client_drawingcontext,FLOAT baselineOriginX,FLOAT baselineOriginY,DWRITE_MEASURING_MODE mode,const DWRITE_GLYPH_RUN *glyph_run,const DWRITE_GLYPH_RUN_DESCRIPTION *run_descr,IUnknown *drawing_effect) {
    return This->lpVtbl->DrawGlyphRun(This,client_drawingcontext,baselineOriginX,baselineOriginY,mode,glyph_run,run_descr,drawing_effect);
}
static inline HRESULT IDWriteTextRenderer_DrawUnderline(IDWriteTextRenderer* This,void *client_drawingcontext,FLOAT baselineOriginX,FLOAT baselineOriginY,const DWRITE_UNDERLINE *underline,IUnknown *drawing_effect) {
    return This->lpVtbl->DrawUnderline(This,client_drawingcontext,baselineOriginX,baselineOriginY,underline,drawing_effect);
}
static inline HRESULT IDWriteTextRenderer_DrawStrikethrough(IDWriteTextRenderer* This,void *client_drawingcontext,FLOAT baselineOriginX,FLOAT baselineOriginY,const DWRITE_STRIKETHROUGH *strikethrough,IUnknown *drawing_effect) {
    return This->lpVtbl->DrawStrikethrough(This,client_drawingcontext,baselineOriginX,baselineOriginY,strikethrough,drawing_effect);
}
static inline HRESULT IDWriteTextRenderer_DrawInlineObject(IDWriteTextRenderer* This,void *client_drawingcontext,FLOAT originX,FLOAT originY,IDWriteInlineObject *object,WINBOOL is_sideways,WINBOOL is_rtl,IUnknown *drawing_effect) {
    return This->lpVtbl->DrawInlineObject(This,client_drawingcontext,originX,originY,object,is_sideways,is_rtl,drawing_effect);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextRenderer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteInlineObject interface
 */
#ifndef __IDWriteInlineObject_INTERFACE_DEFINED__
#define __IDWriteInlineObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteInlineObject, 0x8339fde3, 0x106f, 0x47ab, 0x83,0x73, 0x1c,0x62,0x95,0xeb,0x10,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8339fde3-106f-47ab-8373-1c6295eb10b3")
IDWriteInlineObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Draw(
        void *client_drawingontext,
        IDWriteTextRenderer *renderer,
        FLOAT originX,
        FLOAT originY,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IUnknown *drawing_effect) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetrics(
        DWRITE_INLINE_OBJECT_METRICS *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOverhangMetrics(
        DWRITE_OVERHANG_METRICS *overhangs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBreakConditions(
        DWRITE_BREAK_CONDITION *condition_before,
        DWRITE_BREAK_CONDITION *condition_after) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteInlineObject, 0x8339fde3, 0x106f, 0x47ab, 0x83,0x73, 0x1c,0x62,0x95,0xeb,0x10,0xb3)
#endif
#else
typedef struct IDWriteInlineObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteInlineObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteInlineObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteInlineObject *This);

    /*** IDWriteInlineObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Draw)(
        IDWriteInlineObject *This,
        void *client_drawingontext,
        IDWriteTextRenderer *renderer,
        FLOAT originX,
        FLOAT originY,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IUnknown *drawing_effect);

    HRESULT (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteInlineObject *This,
        DWRITE_INLINE_OBJECT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetOverhangMetrics)(
        IDWriteInlineObject *This,
        DWRITE_OVERHANG_METRICS *overhangs);

    HRESULT (STDMETHODCALLTYPE *GetBreakConditions)(
        IDWriteInlineObject *This,
        DWRITE_BREAK_CONDITION *condition_before,
        DWRITE_BREAK_CONDITION *condition_after);

    END_INTERFACE
} IDWriteInlineObjectVtbl;

interface IDWriteInlineObject {
    CONST_VTBL IDWriteInlineObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteInlineObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteInlineObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteInlineObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteInlineObject methods ***/
#define IDWriteInlineObject_Draw(This,client_drawingontext,renderer,originX,originY,is_sideways,is_rtl,drawing_effect) (This)->lpVtbl->Draw(This,client_drawingontext,renderer,originX,originY,is_sideways,is_rtl,drawing_effect)
#define IDWriteInlineObject_GetMetrics(This,metrics) (This)->lpVtbl->GetMetrics(This,metrics)
#define IDWriteInlineObject_GetOverhangMetrics(This,overhangs) (This)->lpVtbl->GetOverhangMetrics(This,overhangs)
#define IDWriteInlineObject_GetBreakConditions(This,condition_before,condition_after) (This)->lpVtbl->GetBreakConditions(This,condition_before,condition_after)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteInlineObject_QueryInterface(IDWriteInlineObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteInlineObject_AddRef(IDWriteInlineObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteInlineObject_Release(IDWriteInlineObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteInlineObject methods ***/
static inline HRESULT IDWriteInlineObject_Draw(IDWriteInlineObject* This,void *client_drawingontext,IDWriteTextRenderer *renderer,FLOAT originX,FLOAT originY,WINBOOL is_sideways,WINBOOL is_rtl,IUnknown *drawing_effect) {
    return This->lpVtbl->Draw(This,client_drawingontext,renderer,originX,originY,is_sideways,is_rtl,drawing_effect);
}
static inline HRESULT IDWriteInlineObject_GetMetrics(IDWriteInlineObject* This,DWRITE_INLINE_OBJECT_METRICS *metrics) {
    return This->lpVtbl->GetMetrics(This,metrics);
}
static inline HRESULT IDWriteInlineObject_GetOverhangMetrics(IDWriteInlineObject* This,DWRITE_OVERHANG_METRICS *overhangs) {
    return This->lpVtbl->GetOverhangMetrics(This,overhangs);
}
static inline HRESULT IDWriteInlineObject_GetBreakConditions(IDWriteInlineObject* This,DWRITE_BREAK_CONDITION *condition_before,DWRITE_BREAK_CONDITION *condition_after) {
    return This->lpVtbl->GetBreakConditions(This,condition_before,condition_after);
}
#endif
#endif

#endif


#endif  /* __IDWriteInlineObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextFormat interface
 */
#ifndef __IDWriteTextFormat_INTERFACE_DEFINED__
#define __IDWriteTextFormat_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextFormat, 0x9c906818, 0x31d7, 0x4fd3, 0xa1,0x51, 0x7c,0x5e,0x22,0x5d,0xb5,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c906818-31d7-4fd3-a151-7c5e225db55a")
IDWriteTextFormat : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetTextAlignment(
        DWRITE_TEXT_ALIGNMENT alignment) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetParagraphAlignment(
        DWRITE_PARAGRAPH_ALIGNMENT alignment) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetWordWrapping(
        DWRITE_WORD_WRAPPING wrapping) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetReadingDirection(
        DWRITE_READING_DIRECTION direction) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFlowDirection(
        DWRITE_FLOW_DIRECTION direction) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIncrementalTabStop(
        FLOAT tabstop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTrimming(
        const DWRITE_TRIMMING *trimming,
        IDWriteInlineObject *trimming_sign) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLineSpacing(
        DWRITE_LINE_SPACING_METHOD spacing,
        FLOAT line_spacing,
        FLOAT baseline) = 0;

    virtual DWRITE_TEXT_ALIGNMENT STDMETHODCALLTYPE GetTextAlignment(
        ) = 0;

    virtual DWRITE_PARAGRAPH_ALIGNMENT STDMETHODCALLTYPE GetParagraphAlignment(
        ) = 0;

    virtual DWRITE_WORD_WRAPPING STDMETHODCALLTYPE GetWordWrapping(
        ) = 0;

    virtual DWRITE_READING_DIRECTION STDMETHODCALLTYPE GetReadingDirection(
        ) = 0;

    virtual DWRITE_FLOW_DIRECTION STDMETHODCALLTYPE GetFlowDirection(
        ) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetIncrementalTabStop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTrimming(
        DWRITE_TRIMMING *options,
        IDWriteInlineObject **trimming_sign) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLineSpacing(
        DWRITE_LINE_SPACING_METHOD *method,
        FLOAT *spacing,
        FLOAT *baseline) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontCollection(
        IDWriteFontCollection **collection) = 0;

    virtual UINT32 STDMETHODCALLTYPE GetFontFamilyNameLength(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFamilyName(
        WCHAR *name,
        UINT32 size) = 0;

    virtual DWRITE_FONT_WEIGHT STDMETHODCALLTYPE GetFontWeight(
        ) = 0;

    virtual DWRITE_FONT_STYLE STDMETHODCALLTYPE GetFontStyle(
        ) = 0;

    virtual DWRITE_FONT_STRETCH STDMETHODCALLTYPE GetFontStretch(
        ) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetFontSize(
        ) = 0;

    virtual UINT32 STDMETHODCALLTYPE GetLocaleNameLength(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocaleName(
        WCHAR *name,
        UINT32 size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextFormat, 0x9c906818, 0x31d7, 0x4fd3, 0xa1,0x51, 0x7c,0x5e,0x22,0x5d,0xb5,0x5a)
#endif
#else
typedef struct IDWriteTextFormatVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextFormat *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextFormat *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextFormat *This);

    /*** IDWriteTextFormat methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTextAlignment)(
        IDWriteTextFormat *This,
        DWRITE_TEXT_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetParagraphAlignment)(
        IDWriteTextFormat *This,
        DWRITE_PARAGRAPH_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetWordWrapping)(
        IDWriteTextFormat *This,
        DWRITE_WORD_WRAPPING wrapping);

    HRESULT (STDMETHODCALLTYPE *SetReadingDirection)(
        IDWriteTextFormat *This,
        DWRITE_READING_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetFlowDirection)(
        IDWriteTextFormat *This,
        DWRITE_FLOW_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetIncrementalTabStop)(
        IDWriteTextFormat *This,
        FLOAT tabstop);

    HRESULT (STDMETHODCALLTYPE *SetTrimming)(
        IDWriteTextFormat *This,
        const DWRITE_TRIMMING *trimming,
        IDWriteInlineObject *trimming_sign);

    HRESULT (STDMETHODCALLTYPE *SetLineSpacing)(
        IDWriteTextFormat *This,
        DWRITE_LINE_SPACING_METHOD spacing,
        FLOAT line_spacing,
        FLOAT baseline);

    DWRITE_TEXT_ALIGNMENT (STDMETHODCALLTYPE *GetTextAlignment)(
        IDWriteTextFormat *This);

    DWRITE_PARAGRAPH_ALIGNMENT (STDMETHODCALLTYPE *GetParagraphAlignment)(
        IDWriteTextFormat *This);

    DWRITE_WORD_WRAPPING (STDMETHODCALLTYPE *GetWordWrapping)(
        IDWriteTextFormat *This);

    DWRITE_READING_DIRECTION (STDMETHODCALLTYPE *GetReadingDirection)(
        IDWriteTextFormat *This);

    DWRITE_FLOW_DIRECTION (STDMETHODCALLTYPE *GetFlowDirection)(
        IDWriteTextFormat *This);

    FLOAT (STDMETHODCALLTYPE *GetIncrementalTabStop)(
        IDWriteTextFormat *This);

    HRESULT (STDMETHODCALLTYPE *GetTrimming)(
        IDWriteTextFormat *This,
        DWRITE_TRIMMING *options,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *GetLineSpacing)(
        IDWriteTextFormat *This,
        DWRITE_LINE_SPACING_METHOD *method,
        FLOAT *spacing,
        FLOAT *baseline);

    HRESULT (STDMETHODCALLTYPE *GetFontCollection)(
        IDWriteTextFormat *This,
        IDWriteFontCollection **collection);

    UINT32 (STDMETHODCALLTYPE *GetFontFamilyNameLength)(
        IDWriteTextFormat *This);

    HRESULT (STDMETHODCALLTYPE *GetFontFamilyName)(
        IDWriteTextFormat *This,
        WCHAR *name,
        UINT32 size);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetFontWeight)(
        IDWriteTextFormat *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetFontStyle)(
        IDWriteTextFormat *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetFontStretch)(
        IDWriteTextFormat *This);

    FLOAT (STDMETHODCALLTYPE *GetFontSize)(
        IDWriteTextFormat *This);

    UINT32 (STDMETHODCALLTYPE *GetLocaleNameLength)(
        IDWriteTextFormat *This);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteTextFormat *This,
        WCHAR *name,
        UINT32 size);

    END_INTERFACE
} IDWriteTextFormatVtbl;

interface IDWriteTextFormat {
    CONST_VTBL IDWriteTextFormatVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextFormat_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextFormat_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextFormat_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextFormat methods ***/
#define IDWriteTextFormat_SetTextAlignment(This,alignment) (This)->lpVtbl->SetTextAlignment(This,alignment)
#define IDWriteTextFormat_SetParagraphAlignment(This,alignment) (This)->lpVtbl->SetParagraphAlignment(This,alignment)
#define IDWriteTextFormat_SetWordWrapping(This,wrapping) (This)->lpVtbl->SetWordWrapping(This,wrapping)
#define IDWriteTextFormat_SetReadingDirection(This,direction) (This)->lpVtbl->SetReadingDirection(This,direction)
#define IDWriteTextFormat_SetFlowDirection(This,direction) (This)->lpVtbl->SetFlowDirection(This,direction)
#define IDWriteTextFormat_SetIncrementalTabStop(This,tabstop) (This)->lpVtbl->SetIncrementalTabStop(This,tabstop)
#define IDWriteTextFormat_SetTrimming(This,trimming,trimming_sign) (This)->lpVtbl->SetTrimming(This,trimming,trimming_sign)
#define IDWriteTextFormat_SetLineSpacing(This,spacing,line_spacing,baseline) (This)->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline)
#define IDWriteTextFormat_GetTextAlignment(This) (This)->lpVtbl->GetTextAlignment(This)
#define IDWriteTextFormat_GetParagraphAlignment(This) (This)->lpVtbl->GetParagraphAlignment(This)
#define IDWriteTextFormat_GetWordWrapping(This) (This)->lpVtbl->GetWordWrapping(This)
#define IDWriteTextFormat_GetReadingDirection(This) (This)->lpVtbl->GetReadingDirection(This)
#define IDWriteTextFormat_GetFlowDirection(This) (This)->lpVtbl->GetFlowDirection(This)
#define IDWriteTextFormat_GetIncrementalTabStop(This) (This)->lpVtbl->GetIncrementalTabStop(This)
#define IDWriteTextFormat_GetTrimming(This,options,trimming_sign) (This)->lpVtbl->GetTrimming(This,options,trimming_sign)
#define IDWriteTextFormat_GetLineSpacing(This,method,spacing,baseline) (This)->lpVtbl->GetLineSpacing(This,method,spacing,baseline)
#define IDWriteTextFormat_GetFontCollection(This,collection) (This)->lpVtbl->GetFontCollection(This,collection)
#define IDWriteTextFormat_GetFontFamilyNameLength(This) (This)->lpVtbl->GetFontFamilyNameLength(This)
#define IDWriteTextFormat_GetFontFamilyName(This,name,size) (This)->lpVtbl->GetFontFamilyName(This,name,size)
#define IDWriteTextFormat_GetFontWeight(This) (This)->lpVtbl->GetFontWeight(This)
#define IDWriteTextFormat_GetFontStyle(This) (This)->lpVtbl->GetFontStyle(This)
#define IDWriteTextFormat_GetFontStretch(This) (This)->lpVtbl->GetFontStretch(This)
#define IDWriteTextFormat_GetFontSize(This) (This)->lpVtbl->GetFontSize(This)
#define IDWriteTextFormat_GetLocaleNameLength(This) (This)->lpVtbl->GetLocaleNameLength(This)
#define IDWriteTextFormat_GetLocaleName(This,name,size) (This)->lpVtbl->GetLocaleName(This,name,size)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextFormat_QueryInterface(IDWriteTextFormat* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextFormat_AddRef(IDWriteTextFormat* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextFormat_Release(IDWriteTextFormat* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextFormat methods ***/
static inline HRESULT IDWriteTextFormat_SetTextAlignment(IDWriteTextFormat* This,DWRITE_TEXT_ALIGNMENT alignment) {
    return This->lpVtbl->SetTextAlignment(This,alignment);
}
static inline HRESULT IDWriteTextFormat_SetParagraphAlignment(IDWriteTextFormat* This,DWRITE_PARAGRAPH_ALIGNMENT alignment) {
    return This->lpVtbl->SetParagraphAlignment(This,alignment);
}
static inline HRESULT IDWriteTextFormat_SetWordWrapping(IDWriteTextFormat* This,DWRITE_WORD_WRAPPING wrapping) {
    return This->lpVtbl->SetWordWrapping(This,wrapping);
}
static inline HRESULT IDWriteTextFormat_SetReadingDirection(IDWriteTextFormat* This,DWRITE_READING_DIRECTION direction) {
    return This->lpVtbl->SetReadingDirection(This,direction);
}
static inline HRESULT IDWriteTextFormat_SetFlowDirection(IDWriteTextFormat* This,DWRITE_FLOW_DIRECTION direction) {
    return This->lpVtbl->SetFlowDirection(This,direction);
}
static inline HRESULT IDWriteTextFormat_SetIncrementalTabStop(IDWriteTextFormat* This,FLOAT tabstop) {
    return This->lpVtbl->SetIncrementalTabStop(This,tabstop);
}
static inline HRESULT IDWriteTextFormat_SetTrimming(IDWriteTextFormat* This,const DWRITE_TRIMMING *trimming,IDWriteInlineObject *trimming_sign) {
    return This->lpVtbl->SetTrimming(This,trimming,trimming_sign);
}
static inline HRESULT IDWriteTextFormat_SetLineSpacing(IDWriteTextFormat* This,DWRITE_LINE_SPACING_METHOD spacing,FLOAT line_spacing,FLOAT baseline) {
    return This->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline);
}
static inline DWRITE_TEXT_ALIGNMENT IDWriteTextFormat_GetTextAlignment(IDWriteTextFormat* This) {
    return This->lpVtbl->GetTextAlignment(This);
}
static inline DWRITE_PARAGRAPH_ALIGNMENT IDWriteTextFormat_GetParagraphAlignment(IDWriteTextFormat* This) {
    return This->lpVtbl->GetParagraphAlignment(This);
}
static inline DWRITE_WORD_WRAPPING IDWriteTextFormat_GetWordWrapping(IDWriteTextFormat* This) {
    return This->lpVtbl->GetWordWrapping(This);
}
static inline DWRITE_READING_DIRECTION IDWriteTextFormat_GetReadingDirection(IDWriteTextFormat* This) {
    return This->lpVtbl->GetReadingDirection(This);
}
static inline DWRITE_FLOW_DIRECTION IDWriteTextFormat_GetFlowDirection(IDWriteTextFormat* This) {
    return This->lpVtbl->GetFlowDirection(This);
}
static inline FLOAT IDWriteTextFormat_GetIncrementalTabStop(IDWriteTextFormat* This) {
    return This->lpVtbl->GetIncrementalTabStop(This);
}
static inline HRESULT IDWriteTextFormat_GetTrimming(IDWriteTextFormat* This,DWRITE_TRIMMING *options,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->GetTrimming(This,options,trimming_sign);
}
static inline HRESULT IDWriteTextFormat_GetLineSpacing(IDWriteTextFormat* This,DWRITE_LINE_SPACING_METHOD *method,FLOAT *spacing,FLOAT *baseline) {
    return This->lpVtbl->GetLineSpacing(This,method,spacing,baseline);
}
static inline HRESULT IDWriteTextFormat_GetFontCollection(IDWriteTextFormat* This,IDWriteFontCollection **collection) {
    return This->lpVtbl->GetFontCollection(This,collection);
}
static inline UINT32 IDWriteTextFormat_GetFontFamilyNameLength(IDWriteTextFormat* This) {
    return This->lpVtbl->GetFontFamilyNameLength(This);
}
static inline HRESULT IDWriteTextFormat_GetFontFamilyName(IDWriteTextFormat* This,WCHAR *name,UINT32 size) {
    return This->lpVtbl->GetFontFamilyName(This,name,size);
}
static inline DWRITE_FONT_WEIGHT IDWriteTextFormat_GetFontWeight(IDWriteTextFormat* This) {
    return This->lpVtbl->GetFontWeight(This);
}
static inline DWRITE_FONT_STYLE IDWriteTextFormat_GetFontStyle(IDWriteTextFormat* This) {
    return This->lpVtbl->GetFontStyle(This);
}
static inline DWRITE_FONT_STRETCH IDWriteTextFormat_GetFontStretch(IDWriteTextFormat* This) {
    return This->lpVtbl->GetFontStretch(This);
}
static inline FLOAT IDWriteTextFormat_GetFontSize(IDWriteTextFormat* This) {
    return This->lpVtbl->GetFontSize(This);
}
static inline UINT32 IDWriteTextFormat_GetLocaleNameLength(IDWriteTextFormat* This) {
    return This->lpVtbl->GetLocaleNameLength(This);
}
static inline HRESULT IDWriteTextFormat_GetLocaleName(IDWriteTextFormat* This,WCHAR *name,UINT32 size) {
    return This->lpVtbl->GetLocaleName(This,name,size);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextFormat_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTypography interface
 */
#ifndef __IDWriteTypography_INTERFACE_DEFINED__
#define __IDWriteTypography_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTypography, 0x55f1112b, 0x1dc2, 0x4b3c, 0x95,0x41, 0xf4,0x68,0x94,0xed,0x85,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("55f1112b-1dc2-4b3c-9541-f46894ed85b6")
IDWriteTypography : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddFontFeature(
        DWRITE_FONT_FEATURE feature) = 0;

    virtual UINT32 STDMETHODCALLTYPE GetFontFeatureCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFeature(
        UINT32 index,
        DWRITE_FONT_FEATURE *feature) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTypography, 0x55f1112b, 0x1dc2, 0x4b3c, 0x95,0x41, 0xf4,0x68,0x94,0xed,0x85,0xb6)
#endif
#else
typedef struct IDWriteTypographyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTypography *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTypography *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTypography *This);

    /*** IDWriteTypography methods ***/
    HRESULT (STDMETHODCALLTYPE *AddFontFeature)(
        IDWriteTypography *This,
        DWRITE_FONT_FEATURE feature);

    UINT32 (STDMETHODCALLTYPE *GetFontFeatureCount)(
        IDWriteTypography *This);

    HRESULT (STDMETHODCALLTYPE *GetFontFeature)(
        IDWriteTypography *This,
        UINT32 index,
        DWRITE_FONT_FEATURE *feature);

    END_INTERFACE
} IDWriteTypographyVtbl;

interface IDWriteTypography {
    CONST_VTBL IDWriteTypographyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTypography_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTypography_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTypography_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTypography methods ***/
#define IDWriteTypography_AddFontFeature(This,feature) (This)->lpVtbl->AddFontFeature(This,feature)
#define IDWriteTypography_GetFontFeatureCount(This) (This)->lpVtbl->GetFontFeatureCount(This)
#define IDWriteTypography_GetFontFeature(This,index,feature) (This)->lpVtbl->GetFontFeature(This,index,feature)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTypography_QueryInterface(IDWriteTypography* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTypography_AddRef(IDWriteTypography* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTypography_Release(IDWriteTypography* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTypography methods ***/
static inline HRESULT IDWriteTypography_AddFontFeature(IDWriteTypography* This,DWRITE_FONT_FEATURE feature) {
    return This->lpVtbl->AddFontFeature(This,feature);
}
static inline UINT32 IDWriteTypography_GetFontFeatureCount(IDWriteTypography* This) {
    return This->lpVtbl->GetFontFeatureCount(This);
}
static inline HRESULT IDWriteTypography_GetFontFeature(IDWriteTypography* This,UINT32 index,DWRITE_FONT_FEATURE *feature) {
    return This->lpVtbl->GetFontFeature(This,index,feature);
}
#endif
#endif

#endif


#endif  /* __IDWriteTypography_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteBitmapRenderTarget interface
 */
#ifndef __IDWriteBitmapRenderTarget_INTERFACE_DEFINED__
#define __IDWriteBitmapRenderTarget_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteBitmapRenderTarget, 0x5e5a32a3, 0x8dff, 0x4773, 0x9f,0xf6, 0x06,0x96,0xea,0xb7,0x72,0x67);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e5a32a3-8dff-4773-9ff6-0696eab77267")
IDWriteBitmapRenderTarget : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE DrawGlyphRun(
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        DWRITE_MEASURING_MODE measuring_mode,
        const DWRITE_GLYPH_RUN *glyph_run,
        IDWriteRenderingParams *params,
        COLORREF textColor,
        RECT *blackbox_rect = 0) = 0;

    virtual HDC STDMETHODCALLTYPE GetMemoryDC(
        ) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetPixelsPerDip(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPixelsPerDip(
        FLOAT pixels_per_dip) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentTransform(
        DWRITE_MATRIX *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentTransform(
        const DWRITE_MATRIX *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSize(
        SIZE *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resize(
        UINT32 width,
        UINT32 height) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteBitmapRenderTarget, 0x5e5a32a3, 0x8dff, 0x4773, 0x9f,0xf6, 0x06,0x96,0xea,0xb7,0x72,0x67)
#endif
#else
typedef struct IDWriteBitmapRenderTargetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteBitmapRenderTarget *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteBitmapRenderTarget *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteBitmapRenderTarget *This);

    /*** IDWriteBitmapRenderTarget methods ***/
    HRESULT (STDMETHODCALLTYPE *DrawGlyphRun)(
        IDWriteBitmapRenderTarget *This,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        DWRITE_MEASURING_MODE measuring_mode,
        const DWRITE_GLYPH_RUN *glyph_run,
        IDWriteRenderingParams *params,
        COLORREF textColor,
        RECT *blackbox_rect);

    HDC (STDMETHODCALLTYPE *GetMemoryDC)(
        IDWriteBitmapRenderTarget *This);

    FLOAT (STDMETHODCALLTYPE *GetPixelsPerDip)(
        IDWriteBitmapRenderTarget *This);

    HRESULT (STDMETHODCALLTYPE *SetPixelsPerDip)(
        IDWriteBitmapRenderTarget *This,
        FLOAT pixels_per_dip);

    HRESULT (STDMETHODCALLTYPE *GetCurrentTransform)(
        IDWriteBitmapRenderTarget *This,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *SetCurrentTransform)(
        IDWriteBitmapRenderTarget *This,
        const DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IDWriteBitmapRenderTarget *This,
        SIZE *size);

    HRESULT (STDMETHODCALLTYPE *Resize)(
        IDWriteBitmapRenderTarget *This,
        UINT32 width,
        UINT32 height);

    END_INTERFACE
} IDWriteBitmapRenderTargetVtbl;

interface IDWriteBitmapRenderTarget {
    CONST_VTBL IDWriteBitmapRenderTargetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteBitmapRenderTarget_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteBitmapRenderTarget_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteBitmapRenderTarget_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteBitmapRenderTarget methods ***/
#define IDWriteBitmapRenderTarget_DrawGlyphRun(This,baselineOriginX,baselineOriginY,measuring_mode,glyph_run,params,textColor,blackbox_rect) (This)->lpVtbl->DrawGlyphRun(This,baselineOriginX,baselineOriginY,measuring_mode,glyph_run,params,textColor,blackbox_rect)
#define IDWriteBitmapRenderTarget_GetMemoryDC(This) (This)->lpVtbl->GetMemoryDC(This)
#define IDWriteBitmapRenderTarget_GetPixelsPerDip(This) (This)->lpVtbl->GetPixelsPerDip(This)
#define IDWriteBitmapRenderTarget_SetPixelsPerDip(This,pixels_per_dip) (This)->lpVtbl->SetPixelsPerDip(This,pixels_per_dip)
#define IDWriteBitmapRenderTarget_GetCurrentTransform(This,transform) (This)->lpVtbl->GetCurrentTransform(This,transform)
#define IDWriteBitmapRenderTarget_SetCurrentTransform(This,transform) (This)->lpVtbl->SetCurrentTransform(This,transform)
#define IDWriteBitmapRenderTarget_GetSize(This,size) (This)->lpVtbl->GetSize(This,size)
#define IDWriteBitmapRenderTarget_Resize(This,width,height) (This)->lpVtbl->Resize(This,width,height)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteBitmapRenderTarget_QueryInterface(IDWriteBitmapRenderTarget* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteBitmapRenderTarget_AddRef(IDWriteBitmapRenderTarget* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteBitmapRenderTarget_Release(IDWriteBitmapRenderTarget* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteBitmapRenderTarget methods ***/
static inline HRESULT IDWriteBitmapRenderTarget_DrawGlyphRun(IDWriteBitmapRenderTarget* This,FLOAT baselineOriginX,FLOAT baselineOriginY,DWRITE_MEASURING_MODE measuring_mode,const DWRITE_GLYPH_RUN *glyph_run,IDWriteRenderingParams *params,COLORREF textColor,RECT *blackbox_rect) {
    return This->lpVtbl->DrawGlyphRun(This,baselineOriginX,baselineOriginY,measuring_mode,glyph_run,params,textColor,blackbox_rect);
}
static inline HDC IDWriteBitmapRenderTarget_GetMemoryDC(IDWriteBitmapRenderTarget* This) {
    return This->lpVtbl->GetMemoryDC(This);
}
static inline FLOAT IDWriteBitmapRenderTarget_GetPixelsPerDip(IDWriteBitmapRenderTarget* This) {
    return This->lpVtbl->GetPixelsPerDip(This);
}
static inline HRESULT IDWriteBitmapRenderTarget_SetPixelsPerDip(IDWriteBitmapRenderTarget* This,FLOAT pixels_per_dip) {
    return This->lpVtbl->SetPixelsPerDip(This,pixels_per_dip);
}
static inline HRESULT IDWriteBitmapRenderTarget_GetCurrentTransform(IDWriteBitmapRenderTarget* This,DWRITE_MATRIX *transform) {
    return This->lpVtbl->GetCurrentTransform(This,transform);
}
static inline HRESULT IDWriteBitmapRenderTarget_SetCurrentTransform(IDWriteBitmapRenderTarget* This,const DWRITE_MATRIX *transform) {
    return This->lpVtbl->SetCurrentTransform(This,transform);
}
static inline HRESULT IDWriteBitmapRenderTarget_GetSize(IDWriteBitmapRenderTarget* This,SIZE *size) {
    return This->lpVtbl->GetSize(This,size);
}
static inline HRESULT IDWriteBitmapRenderTarget_Resize(IDWriteBitmapRenderTarget* This,UINT32 width,UINT32 height) {
    return This->lpVtbl->Resize(This,width,height);
}
#endif
#endif

#endif


#endif  /* __IDWriteBitmapRenderTarget_INTERFACE_DEFINED__ */

#ifndef _WINGDI_
typedef struct tagLOGFONTW {
    LONG lfHeight;
    LONG lfWidth;
    LONG lfEscapement;
    LONG lfOrientation;
    LONG lfWeight;
    BYTE lfItalic;
    BYTE lfUnderline;
    BYTE lfStrikeOut;
    BYTE lfCharSet;
    BYTE lfOutPrecision;
    BYTE lfClipPrecision;
    BYTE lfQuality;
    BYTE lfPitchAndFamily;
    WCHAR lfFaceName[32];
} LOGFONTW;
typedef struct tagLOGFONTW *PLOGFONTW;
typedef struct tagLOGFONTW *LPLOGFONTW;
#endif /* _WINGDI_ */
/*****************************************************************************
 * IDWriteGdiInterop interface
 */
#ifndef __IDWriteGdiInterop_INTERFACE_DEFINED__
#define __IDWriteGdiInterop_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteGdiInterop, 0x1edd9491, 0x9853, 0x4299, 0x89,0x8f, 0x64,0x32,0x98,0x3b,0x6f,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1edd9491-9853-4299-898f-6432983b6f3a")
IDWriteGdiInterop : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateFontFromLOGFONT(
        const LOGFONTW *logfont,
        IDWriteFont **font) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConvertFontToLOGFONT(
        IDWriteFont *font,
        LOGFONTW *logfont,
        WINBOOL *is_systemfont) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConvertFontFaceToLOGFONT(
        IDWriteFontFace *font,
        LOGFONTW *logfont) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFontFaceFromHdc(
        HDC hdc,
        IDWriteFontFace **fontface) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapRenderTarget(
        HDC hdc,
        UINT32 width,
        UINT32 height,
        IDWriteBitmapRenderTarget **target) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteGdiInterop, 0x1edd9491, 0x9853, 0x4299, 0x89,0x8f, 0x64,0x32,0x98,0x3b,0x6f,0x3a)
#endif
#else
typedef struct IDWriteGdiInteropVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteGdiInterop *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteGdiInterop *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteGdiInterop *This);

    /*** IDWriteGdiInterop methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFontFromLOGFONT)(
        IDWriteGdiInterop *This,
        const LOGFONTW *logfont,
        IDWriteFont **font);

    HRESULT (STDMETHODCALLTYPE *ConvertFontToLOGFONT)(
        IDWriteGdiInterop *This,
        IDWriteFont *font,
        LOGFONTW *logfont,
        WINBOOL *is_systemfont);

    HRESULT (STDMETHODCALLTYPE *ConvertFontFaceToLOGFONT)(
        IDWriteGdiInterop *This,
        IDWriteFontFace *font,
        LOGFONTW *logfont);

    HRESULT (STDMETHODCALLTYPE *CreateFontFaceFromHdc)(
        IDWriteGdiInterop *This,
        HDC hdc,
        IDWriteFontFace **fontface);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapRenderTarget)(
        IDWriteGdiInterop *This,
        HDC hdc,
        UINT32 width,
        UINT32 height,
        IDWriteBitmapRenderTarget **target);

    END_INTERFACE
} IDWriteGdiInteropVtbl;

interface IDWriteGdiInterop {
    CONST_VTBL IDWriteGdiInteropVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteGdiInterop_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteGdiInterop_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteGdiInterop_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteGdiInterop methods ***/
#define IDWriteGdiInterop_CreateFontFromLOGFONT(This,logfont,font) (This)->lpVtbl->CreateFontFromLOGFONT(This,logfont,font)
#define IDWriteGdiInterop_ConvertFontToLOGFONT(This,font,logfont,is_systemfont) (This)->lpVtbl->ConvertFontToLOGFONT(This,font,logfont,is_systemfont)
#define IDWriteGdiInterop_ConvertFontFaceToLOGFONT(This,font,logfont) (This)->lpVtbl->ConvertFontFaceToLOGFONT(This,font,logfont)
#define IDWriteGdiInterop_CreateFontFaceFromHdc(This,hdc,fontface) (This)->lpVtbl->CreateFontFaceFromHdc(This,hdc,fontface)
#define IDWriteGdiInterop_CreateBitmapRenderTarget(This,hdc,width,height,target) (This)->lpVtbl->CreateBitmapRenderTarget(This,hdc,width,height,target)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteGdiInterop_QueryInterface(IDWriteGdiInterop* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteGdiInterop_AddRef(IDWriteGdiInterop* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteGdiInterop_Release(IDWriteGdiInterop* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteGdiInterop methods ***/
static inline HRESULT IDWriteGdiInterop_CreateFontFromLOGFONT(IDWriteGdiInterop* This,const LOGFONTW *logfont,IDWriteFont **font) {
    return This->lpVtbl->CreateFontFromLOGFONT(This,logfont,font);
}
static inline HRESULT IDWriteGdiInterop_ConvertFontToLOGFONT(IDWriteGdiInterop* This,IDWriteFont *font,LOGFONTW *logfont,WINBOOL *is_systemfont) {
    return This->lpVtbl->ConvertFontToLOGFONT(This,font,logfont,is_systemfont);
}
static inline HRESULT IDWriteGdiInterop_ConvertFontFaceToLOGFONT(IDWriteGdiInterop* This,IDWriteFontFace *font,LOGFONTW *logfont) {
    return This->lpVtbl->ConvertFontFaceToLOGFONT(This,font,logfont);
}
static inline HRESULT IDWriteGdiInterop_CreateFontFaceFromHdc(IDWriteGdiInterop* This,HDC hdc,IDWriteFontFace **fontface) {
    return This->lpVtbl->CreateFontFaceFromHdc(This,hdc,fontface);
}
static inline HRESULT IDWriteGdiInterop_CreateBitmapRenderTarget(IDWriteGdiInterop* This,HDC hdc,UINT32 width,UINT32 height,IDWriteBitmapRenderTarget **target) {
    return This->lpVtbl->CreateBitmapRenderTarget(This,hdc,width,height,target);
}
#endif
#endif

#endif


#endif  /* __IDWriteGdiInterop_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextLayout interface
 */
#ifndef __IDWriteTextLayout_INTERFACE_DEFINED__
#define __IDWriteTextLayout_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextLayout, 0x53737037, 0x6d14, 0x410b, 0x9b,0xfe, 0x0b,0x18,0x2b,0xb7,0x09,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("53737037-6d14-410b-9bfe-0b182bb70961")
IDWriteTextLayout : public IDWriteTextFormat
{
    virtual HRESULT STDMETHODCALLTYPE SetMaxWidth(
        FLOAT maxWidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMaxHeight(
        FLOAT maxHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontCollection(
        IDWriteFontCollection *collection,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontFamilyName(
        const WCHAR *name,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontWeight(
        DWRITE_FONT_WEIGHT weight,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontStyle(
        DWRITE_FONT_STYLE style,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontStretch(
        DWRITE_FONT_STRETCH stretch,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontSize(
        FLOAT size,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnderline(
        WINBOOL underline,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrikethrough(
        WINBOOL strikethrough,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDrawingEffect(
        IUnknown *effect,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInlineObject(
        IDWriteInlineObject *object,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTypography(
        IDWriteTypography *typography,
        DWRITE_TEXT_RANGE range) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLocaleName(
        const WCHAR *locale,
        DWRITE_TEXT_RANGE range) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetMaxWidth(
        ) = 0;

    virtual FLOAT STDMETHODCALLTYPE GetMaxHeight(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontCollection(
        UINT32 pos,
        IDWriteFontCollection **collection,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFamilyNameLength(
        UINT32 pos,
        UINT32 *len,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFamilyName(
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontWeight(
        UINT32 position,
        DWRITE_FONT_WEIGHT *weight,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontStyle(
        UINT32 currentPosition,
        DWRITE_FONT_STYLE *style,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontStretch(
        UINT32 position,
        DWRITE_FONT_STRETCH *stretch,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontSize(
        UINT32 position,
        FLOAT *size,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnderline(
        UINT32 position,
        WINBOOL *has_underline,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrikethrough(
        UINT32 position,
        WINBOOL *has_strikethrough,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDrawingEffect(
        UINT32 position,
        IUnknown **effect,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInlineObject(
        UINT32 position,
        IDWriteInlineObject **object,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypography(
        UINT32 position,
        IDWriteTypography **typography,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocaleNameLength(
        UINT32 position,
        UINT32 *length,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocaleName(
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Draw(
        void *context,
        IDWriteTextRenderer *renderer,
        FLOAT originX,
        FLOAT originY) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLineMetrics(
        DWRITE_LINE_METRICS *metrics,
        UINT32 max_count,
        UINT32 *actual_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetrics(
        DWRITE_TEXT_METRICS *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOverhangMetrics(
        DWRITE_OVERHANG_METRICS *overhangs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClusterMetrics(
        DWRITE_CLUSTER_METRICS *metrics,
        UINT32 max_count,
        UINT32 *act_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE DetermineMinWidth(
        FLOAT *min_width) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTestPoint(
        FLOAT pointX,
        FLOAT pointY,
        WINBOOL *is_trailinghit,
        WINBOOL *is_inside,
        DWRITE_HIT_TEST_METRICS *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTestTextPosition(
        UINT32 textPosition,
        WINBOOL is_trailinghit,
        FLOAT *pointX,
        FLOAT *pointY,
        DWRITE_HIT_TEST_METRICS *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTestTextRange(
        UINT32 textPosition,
        UINT32 textLength,
        FLOAT originX,
        FLOAT originY,
        DWRITE_HIT_TEST_METRICS *metrics,
        UINT32 max_metricscount,
        UINT32 *actual_metricscount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextLayout, 0x53737037, 0x6d14, 0x410b, 0x9b,0xfe, 0x0b,0x18,0x2b,0xb7,0x09,0x61)
#endif
#else
typedef struct IDWriteTextLayoutVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextLayout *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextLayout *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextLayout *This);

    /*** IDWriteTextFormat methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTextAlignment)(
        IDWriteTextLayout *This,
        DWRITE_TEXT_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetParagraphAlignment)(
        IDWriteTextLayout *This,
        DWRITE_PARAGRAPH_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetWordWrapping)(
        IDWriteTextLayout *This,
        DWRITE_WORD_WRAPPING wrapping);

    HRESULT (STDMETHODCALLTYPE *SetReadingDirection)(
        IDWriteTextLayout *This,
        DWRITE_READING_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetFlowDirection)(
        IDWriteTextLayout *This,
        DWRITE_FLOW_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetIncrementalTabStop)(
        IDWriteTextLayout *This,
        FLOAT tabstop);

    HRESULT (STDMETHODCALLTYPE *SetTrimming)(
        IDWriteTextLayout *This,
        const DWRITE_TRIMMING *trimming,
        IDWriteInlineObject *trimming_sign);

    HRESULT (STDMETHODCALLTYPE *SetLineSpacing)(
        IDWriteTextLayout *This,
        DWRITE_LINE_SPACING_METHOD spacing,
        FLOAT line_spacing,
        FLOAT baseline);

    DWRITE_TEXT_ALIGNMENT (STDMETHODCALLTYPE *GetTextAlignment)(
        IDWriteTextLayout *This);

    DWRITE_PARAGRAPH_ALIGNMENT (STDMETHODCALLTYPE *GetParagraphAlignment)(
        IDWriteTextLayout *This);

    DWRITE_WORD_WRAPPING (STDMETHODCALLTYPE *GetWordWrapping)(
        IDWriteTextLayout *This);

    DWRITE_READING_DIRECTION (STDMETHODCALLTYPE *GetReadingDirection)(
        IDWriteTextLayout *This);

    DWRITE_FLOW_DIRECTION (STDMETHODCALLTYPE *GetFlowDirection)(
        IDWriteTextLayout *This);

    FLOAT (STDMETHODCALLTYPE *GetIncrementalTabStop)(
        IDWriteTextLayout *This);

    HRESULT (STDMETHODCALLTYPE *GetTrimming)(
        IDWriteTextLayout *This,
        DWRITE_TRIMMING *options,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *GetLineSpacing)(
        IDWriteTextLayout *This,
        DWRITE_LINE_SPACING_METHOD *method,
        FLOAT *spacing,
        FLOAT *baseline);

    HRESULT (STDMETHODCALLTYPE *GetFontCollection)(
        IDWriteTextLayout *This,
        IDWriteFontCollection **collection);

    UINT32 (STDMETHODCALLTYPE *GetFontFamilyNameLength)(
        IDWriteTextLayout *This);

    HRESULT (STDMETHODCALLTYPE *GetFontFamilyName)(
        IDWriteTextLayout *This,
        WCHAR *name,
        UINT32 size);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetFontWeight)(
        IDWriteTextLayout *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetFontStyle)(
        IDWriteTextLayout *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetFontStretch)(
        IDWriteTextLayout *This);

    FLOAT (STDMETHODCALLTYPE *GetFontSize)(
        IDWriteTextLayout *This);

    UINT32 (STDMETHODCALLTYPE *GetLocaleNameLength)(
        IDWriteTextLayout *This);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteTextLayout *This,
        WCHAR *name,
        UINT32 size);

    /*** IDWriteTextLayout methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMaxWidth)(
        IDWriteTextLayout *This,
        FLOAT maxWidth);

    HRESULT (STDMETHODCALLTYPE *SetMaxHeight)(
        IDWriteTextLayout *This,
        FLOAT maxHeight);

    HRESULT (STDMETHODCALLTYPE *SetFontCollection)(
        IDWriteTextLayout *This,
        IDWriteFontCollection *collection,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontFamilyName)(
        IDWriteTextLayout *This,
        const WCHAR *name,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontWeight)(
        IDWriteTextLayout *This,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontStyle)(
        IDWriteTextLayout *This,
        DWRITE_FONT_STYLE style,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontStretch)(
        IDWriteTextLayout *This,
        DWRITE_FONT_STRETCH stretch,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontSize)(
        IDWriteTextLayout *This,
        FLOAT size,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetUnderline)(
        IDWriteTextLayout *This,
        WINBOOL underline,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetStrikethrough)(
        IDWriteTextLayout *This,
        WINBOOL strikethrough,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetDrawingEffect)(
        IDWriteTextLayout *This,
        IUnknown *effect,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetInlineObject)(
        IDWriteTextLayout *This,
        IDWriteInlineObject *object,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetTypography)(
        IDWriteTextLayout *This,
        IDWriteTypography *typography,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetLocaleName)(
        IDWriteTextLayout *This,
        const WCHAR *locale,
        DWRITE_TEXT_RANGE range);

    FLOAT (STDMETHODCALLTYPE *GetMaxWidth)(
        IDWriteTextLayout *This);

    FLOAT (STDMETHODCALLTYPE *GetMaxHeight)(
        IDWriteTextLayout *This);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontCollection)(
        IDWriteTextLayout *This,
        UINT32 pos,
        IDWriteFontCollection **collection,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontFamilyNameLength)(
        IDWriteTextLayout *This,
        UINT32 pos,
        UINT32 *len,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontFamilyName)(
        IDWriteTextLayout *This,
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontWeight)(
        IDWriteTextLayout *This,
        UINT32 position,
        DWRITE_FONT_WEIGHT *weight,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontStyle)(
        IDWriteTextLayout *This,
        UINT32 currentPosition,
        DWRITE_FONT_STYLE *style,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontStretch)(
        IDWriteTextLayout *This,
        UINT32 position,
        DWRITE_FONT_STRETCH *stretch,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontSize)(
        IDWriteTextLayout *This,
        UINT32 position,
        FLOAT *size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetUnderline)(
        IDWriteTextLayout *This,
        UINT32 position,
        WINBOOL *has_underline,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetStrikethrough)(
        IDWriteTextLayout *This,
        UINT32 position,
        WINBOOL *has_strikethrough,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetDrawingEffect)(
        IDWriteTextLayout *This,
        UINT32 position,
        IUnknown **effect,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetInlineObject)(
        IDWriteTextLayout *This,
        UINT32 position,
        IDWriteInlineObject **object,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetTypography)(
        IDWriteTextLayout *This,
        UINT32 position,
        IDWriteTypography **typography,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetLocaleNameLength)(
        IDWriteTextLayout *This,
        UINT32 position,
        UINT32 *length,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetLocaleName)(
        IDWriteTextLayout *This,
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *Draw)(
        IDWriteTextLayout *This,
        void *context,
        IDWriteTextRenderer *renderer,
        FLOAT originX,
        FLOAT originY);

    HRESULT (STDMETHODCALLTYPE *GetLineMetrics)(
        IDWriteTextLayout *This,
        DWRITE_LINE_METRICS *metrics,
        UINT32 max_count,
        UINT32 *actual_count);

    HRESULT (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteTextLayout *This,
        DWRITE_TEXT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetOverhangMetrics)(
        IDWriteTextLayout *This,
        DWRITE_OVERHANG_METRICS *overhangs);

    HRESULT (STDMETHODCALLTYPE *GetClusterMetrics)(
        IDWriteTextLayout *This,
        DWRITE_CLUSTER_METRICS *metrics,
        UINT32 max_count,
        UINT32 *act_count);

    HRESULT (STDMETHODCALLTYPE *DetermineMinWidth)(
        IDWriteTextLayout *This,
        FLOAT *min_width);

    HRESULT (STDMETHODCALLTYPE *HitTestPoint)(
        IDWriteTextLayout *This,
        FLOAT pointX,
        FLOAT pointY,
        WINBOOL *is_trailinghit,
        WINBOOL *is_inside,
        DWRITE_HIT_TEST_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HitTestTextPosition)(
        IDWriteTextLayout *This,
        UINT32 textPosition,
        WINBOOL is_trailinghit,
        FLOAT *pointX,
        FLOAT *pointY,
        DWRITE_HIT_TEST_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HitTestTextRange)(
        IDWriteTextLayout *This,
        UINT32 textPosition,
        UINT32 textLength,
        FLOAT originX,
        FLOAT originY,
        DWRITE_HIT_TEST_METRICS *metrics,
        UINT32 max_metricscount,
        UINT32 *actual_metricscount);

    END_INTERFACE
} IDWriteTextLayoutVtbl;

interface IDWriteTextLayout {
    CONST_VTBL IDWriteTextLayoutVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextLayout_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextLayout_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextLayout_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextFormat methods ***/
#define IDWriteTextLayout_SetTextAlignment(This,alignment) (This)->lpVtbl->SetTextAlignment(This,alignment)
#define IDWriteTextLayout_SetParagraphAlignment(This,alignment) (This)->lpVtbl->SetParagraphAlignment(This,alignment)
#define IDWriteTextLayout_SetWordWrapping(This,wrapping) (This)->lpVtbl->SetWordWrapping(This,wrapping)
#define IDWriteTextLayout_SetReadingDirection(This,direction) (This)->lpVtbl->SetReadingDirection(This,direction)
#define IDWriteTextLayout_SetFlowDirection(This,direction) (This)->lpVtbl->SetFlowDirection(This,direction)
#define IDWriteTextLayout_SetIncrementalTabStop(This,tabstop) (This)->lpVtbl->SetIncrementalTabStop(This,tabstop)
#define IDWriteTextLayout_SetTrimming(This,trimming,trimming_sign) (This)->lpVtbl->SetTrimming(This,trimming,trimming_sign)
#define IDWriteTextLayout_SetLineSpacing(This,spacing,line_spacing,baseline) (This)->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline)
#define IDWriteTextLayout_GetTextAlignment(This) (This)->lpVtbl->GetTextAlignment(This)
#define IDWriteTextLayout_GetParagraphAlignment(This) (This)->lpVtbl->GetParagraphAlignment(This)
#define IDWriteTextLayout_GetWordWrapping(This) (This)->lpVtbl->GetWordWrapping(This)
#define IDWriteTextLayout_GetReadingDirection(This) (This)->lpVtbl->GetReadingDirection(This)
#define IDWriteTextLayout_GetFlowDirection(This) (This)->lpVtbl->GetFlowDirection(This)
#define IDWriteTextLayout_GetIncrementalTabStop(This) (This)->lpVtbl->GetIncrementalTabStop(This)
#define IDWriteTextLayout_GetTrimming(This,options,trimming_sign) (This)->lpVtbl->GetTrimming(This,options,trimming_sign)
#define IDWriteTextLayout_GetLineSpacing(This,method,spacing,baseline) (This)->lpVtbl->GetLineSpacing(This,method,spacing,baseline)
/*** IDWriteTextLayout methods ***/
#define IDWriteTextLayout_SetMaxWidth(This,maxWidth) (This)->lpVtbl->SetMaxWidth(This,maxWidth)
#define IDWriteTextLayout_SetMaxHeight(This,maxHeight) (This)->lpVtbl->SetMaxHeight(This,maxHeight)
#define IDWriteTextLayout_SetFontCollection(This,collection,range) (This)->lpVtbl->SetFontCollection(This,collection,range)
#define IDWriteTextLayout_SetFontFamilyName(This,name,range) (This)->lpVtbl->SetFontFamilyName(This,name,range)
#define IDWriteTextLayout_SetFontWeight(This,weight,range) (This)->lpVtbl->SetFontWeight(This,weight,range)
#define IDWriteTextLayout_SetFontStyle(This,style,range) (This)->lpVtbl->SetFontStyle(This,style,range)
#define IDWriteTextLayout_SetFontStretch(This,stretch,range) (This)->lpVtbl->SetFontStretch(This,stretch,range)
#define IDWriteTextLayout_SetFontSize(This,size,range) (This)->lpVtbl->SetFontSize(This,size,range)
#define IDWriteTextLayout_SetUnderline(This,underline,range) (This)->lpVtbl->SetUnderline(This,underline,range)
#define IDWriteTextLayout_SetStrikethrough(This,strikethrough,range) (This)->lpVtbl->SetStrikethrough(This,strikethrough,range)
#define IDWriteTextLayout_SetDrawingEffect(This,effect,range) (This)->lpVtbl->SetDrawingEffect(This,effect,range)
#define IDWriteTextLayout_SetInlineObject(This,object,range) (This)->lpVtbl->SetInlineObject(This,object,range)
#define IDWriteTextLayout_SetTypography(This,typography,range) (This)->lpVtbl->SetTypography(This,typography,range)
#define IDWriteTextLayout_SetLocaleName(This,locale,range) (This)->lpVtbl->SetLocaleName(This,locale,range)
#define IDWriteTextLayout_GetMaxWidth(This) (This)->lpVtbl->GetMaxWidth(This)
#define IDWriteTextLayout_GetMaxHeight(This) (This)->lpVtbl->GetMaxHeight(This)
#define IDWriteTextLayout_GetFontCollection(This,pos,collection,range) (This)->lpVtbl->IDWriteTextLayout_GetFontCollection(This,pos,collection,range)
#define IDWriteTextLayout_GetFontFamilyNameLength(This,pos,len,range) (This)->lpVtbl->IDWriteTextLayout_GetFontFamilyNameLength(This,pos,len,range)
#define IDWriteTextLayout_GetFontFamilyName(This,position,name,name_size,range) (This)->lpVtbl->IDWriteTextLayout_GetFontFamilyName(This,position,name,name_size,range)
#define IDWriteTextLayout_GetFontWeight(This,position,weight,range) (This)->lpVtbl->IDWriteTextLayout_GetFontWeight(This,position,weight,range)
#define IDWriteTextLayout_GetFontStyle(This,currentPosition,style,range) (This)->lpVtbl->IDWriteTextLayout_GetFontStyle(This,currentPosition,style,range)
#define IDWriteTextLayout_GetFontStretch(This,position,stretch,range) (This)->lpVtbl->IDWriteTextLayout_GetFontStretch(This,position,stretch,range)
#define IDWriteTextLayout_GetFontSize(This,position,size,range) (This)->lpVtbl->IDWriteTextLayout_GetFontSize(This,position,size,range)
#define IDWriteTextLayout_GetUnderline(This,position,has_underline,range) (This)->lpVtbl->GetUnderline(This,position,has_underline,range)
#define IDWriteTextLayout_GetStrikethrough(This,position,has_strikethrough,range) (This)->lpVtbl->GetStrikethrough(This,position,has_strikethrough,range)
#define IDWriteTextLayout_GetDrawingEffect(This,position,effect,range) (This)->lpVtbl->GetDrawingEffect(This,position,effect,range)
#define IDWriteTextLayout_GetInlineObject(This,position,object,range) (This)->lpVtbl->GetInlineObject(This,position,object,range)
#define IDWriteTextLayout_GetTypography(This,position,typography,range) (This)->lpVtbl->GetTypography(This,position,typography,range)
#define IDWriteTextLayout_GetLocaleNameLength(This,position,length,range) (This)->lpVtbl->IDWriteTextLayout_GetLocaleNameLength(This,position,length,range)
#define IDWriteTextLayout_GetLocaleName(This,position,name,name_size,range) (This)->lpVtbl->IDWriteTextLayout_GetLocaleName(This,position,name,name_size,range)
#define IDWriteTextLayout_Draw(This,context,renderer,originX,originY) (This)->lpVtbl->Draw(This,context,renderer,originX,originY)
#define IDWriteTextLayout_GetLineMetrics(This,metrics,max_count,actual_count) (This)->lpVtbl->GetLineMetrics(This,metrics,max_count,actual_count)
#define IDWriteTextLayout_GetMetrics(This,metrics) (This)->lpVtbl->GetMetrics(This,metrics)
#define IDWriteTextLayout_GetOverhangMetrics(This,overhangs) (This)->lpVtbl->GetOverhangMetrics(This,overhangs)
#define IDWriteTextLayout_GetClusterMetrics(This,metrics,max_count,act_count) (This)->lpVtbl->GetClusterMetrics(This,metrics,max_count,act_count)
#define IDWriteTextLayout_DetermineMinWidth(This,min_width) (This)->lpVtbl->DetermineMinWidth(This,min_width)
#define IDWriteTextLayout_HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics) (This)->lpVtbl->HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics)
#define IDWriteTextLayout_HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics) (This)->lpVtbl->HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics)
#define IDWriteTextLayout_HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount) (This)->lpVtbl->HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextLayout_QueryInterface(IDWriteTextLayout* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextLayout_AddRef(IDWriteTextLayout* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextLayout_Release(IDWriteTextLayout* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextFormat methods ***/
static inline HRESULT IDWriteTextLayout_SetTextAlignment(IDWriteTextLayout* This,DWRITE_TEXT_ALIGNMENT alignment) {
    return This->lpVtbl->SetTextAlignment(This,alignment);
}
static inline HRESULT IDWriteTextLayout_SetParagraphAlignment(IDWriteTextLayout* This,DWRITE_PARAGRAPH_ALIGNMENT alignment) {
    return This->lpVtbl->SetParagraphAlignment(This,alignment);
}
static inline HRESULT IDWriteTextLayout_SetWordWrapping(IDWriteTextLayout* This,DWRITE_WORD_WRAPPING wrapping) {
    return This->lpVtbl->SetWordWrapping(This,wrapping);
}
static inline HRESULT IDWriteTextLayout_SetReadingDirection(IDWriteTextLayout* This,DWRITE_READING_DIRECTION direction) {
    return This->lpVtbl->SetReadingDirection(This,direction);
}
static inline HRESULT IDWriteTextLayout_SetFlowDirection(IDWriteTextLayout* This,DWRITE_FLOW_DIRECTION direction) {
    return This->lpVtbl->SetFlowDirection(This,direction);
}
static inline HRESULT IDWriteTextLayout_SetIncrementalTabStop(IDWriteTextLayout* This,FLOAT tabstop) {
    return This->lpVtbl->SetIncrementalTabStop(This,tabstop);
}
static inline HRESULT IDWriteTextLayout_SetTrimming(IDWriteTextLayout* This,const DWRITE_TRIMMING *trimming,IDWriteInlineObject *trimming_sign) {
    return This->lpVtbl->SetTrimming(This,trimming,trimming_sign);
}
static inline HRESULT IDWriteTextLayout_SetLineSpacing(IDWriteTextLayout* This,DWRITE_LINE_SPACING_METHOD spacing,FLOAT line_spacing,FLOAT baseline) {
    return This->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline);
}
static inline DWRITE_TEXT_ALIGNMENT IDWriteTextLayout_GetTextAlignment(IDWriteTextLayout* This) {
    return This->lpVtbl->GetTextAlignment(This);
}
static inline DWRITE_PARAGRAPH_ALIGNMENT IDWriteTextLayout_GetParagraphAlignment(IDWriteTextLayout* This) {
    return This->lpVtbl->GetParagraphAlignment(This);
}
static inline DWRITE_WORD_WRAPPING IDWriteTextLayout_GetWordWrapping(IDWriteTextLayout* This) {
    return This->lpVtbl->GetWordWrapping(This);
}
static inline DWRITE_READING_DIRECTION IDWriteTextLayout_GetReadingDirection(IDWriteTextLayout* This) {
    return This->lpVtbl->GetReadingDirection(This);
}
static inline DWRITE_FLOW_DIRECTION IDWriteTextLayout_GetFlowDirection(IDWriteTextLayout* This) {
    return This->lpVtbl->GetFlowDirection(This);
}
static inline FLOAT IDWriteTextLayout_GetIncrementalTabStop(IDWriteTextLayout* This) {
    return This->lpVtbl->GetIncrementalTabStop(This);
}
static inline HRESULT IDWriteTextLayout_GetTrimming(IDWriteTextLayout* This,DWRITE_TRIMMING *options,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->GetTrimming(This,options,trimming_sign);
}
static inline HRESULT IDWriteTextLayout_GetLineSpacing(IDWriteTextLayout* This,DWRITE_LINE_SPACING_METHOD *method,FLOAT *spacing,FLOAT *baseline) {
    return This->lpVtbl->GetLineSpacing(This,method,spacing,baseline);
}
/*** IDWriteTextLayout methods ***/
static inline HRESULT IDWriteTextLayout_SetMaxWidth(IDWriteTextLayout* This,FLOAT maxWidth) {
    return This->lpVtbl->SetMaxWidth(This,maxWidth);
}
static inline HRESULT IDWriteTextLayout_SetMaxHeight(IDWriteTextLayout* This,FLOAT maxHeight) {
    return This->lpVtbl->SetMaxHeight(This,maxHeight);
}
static inline HRESULT IDWriteTextLayout_SetFontCollection(IDWriteTextLayout* This,IDWriteFontCollection *collection,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontCollection(This,collection,range);
}
static inline HRESULT IDWriteTextLayout_SetFontFamilyName(IDWriteTextLayout* This,const WCHAR *name,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontFamilyName(This,name,range);
}
static inline HRESULT IDWriteTextLayout_SetFontWeight(IDWriteTextLayout* This,DWRITE_FONT_WEIGHT weight,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontWeight(This,weight,range);
}
static inline HRESULT IDWriteTextLayout_SetFontStyle(IDWriteTextLayout* This,DWRITE_FONT_STYLE style,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontStyle(This,style,range);
}
static inline HRESULT IDWriteTextLayout_SetFontStretch(IDWriteTextLayout* This,DWRITE_FONT_STRETCH stretch,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontStretch(This,stretch,range);
}
static inline HRESULT IDWriteTextLayout_SetFontSize(IDWriteTextLayout* This,FLOAT size,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontSize(This,size,range);
}
static inline HRESULT IDWriteTextLayout_SetUnderline(IDWriteTextLayout* This,WINBOOL underline,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetUnderline(This,underline,range);
}
static inline HRESULT IDWriteTextLayout_SetStrikethrough(IDWriteTextLayout* This,WINBOOL strikethrough,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetStrikethrough(This,strikethrough,range);
}
static inline HRESULT IDWriteTextLayout_SetDrawingEffect(IDWriteTextLayout* This,IUnknown *effect,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetDrawingEffect(This,effect,range);
}
static inline HRESULT IDWriteTextLayout_SetInlineObject(IDWriteTextLayout* This,IDWriteInlineObject *object,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetInlineObject(This,object,range);
}
static inline HRESULT IDWriteTextLayout_SetTypography(IDWriteTextLayout* This,IDWriteTypography *typography,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetTypography(This,typography,range);
}
static inline HRESULT IDWriteTextLayout_SetLocaleName(IDWriteTextLayout* This,const WCHAR *locale,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetLocaleName(This,locale,range);
}
static inline FLOAT IDWriteTextLayout_GetMaxWidth(IDWriteTextLayout* This) {
    return This->lpVtbl->GetMaxWidth(This);
}
static inline FLOAT IDWriteTextLayout_GetMaxHeight(IDWriteTextLayout* This) {
    return This->lpVtbl->GetMaxHeight(This);
}
static inline HRESULT IDWriteTextLayout_GetFontCollection(IDWriteTextLayout* This,UINT32 pos,IDWriteFontCollection **collection,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontCollection(This,pos,collection,range);
}
static inline HRESULT IDWriteTextLayout_GetFontFamilyNameLength(IDWriteTextLayout* This,UINT32 pos,UINT32 *len,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontFamilyNameLength(This,pos,len,range);
}
static inline HRESULT IDWriteTextLayout_GetFontFamilyName(IDWriteTextLayout* This,UINT32 position,WCHAR *name,UINT32 name_size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontFamilyName(This,position,name,name_size,range);
}
static inline HRESULT IDWriteTextLayout_GetFontWeight(IDWriteTextLayout* This,UINT32 position,DWRITE_FONT_WEIGHT *weight,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontWeight(This,position,weight,range);
}
static inline HRESULT IDWriteTextLayout_GetFontStyle(IDWriteTextLayout* This,UINT32 currentPosition,DWRITE_FONT_STYLE *style,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontStyle(This,currentPosition,style,range);
}
static inline HRESULT IDWriteTextLayout_GetFontStretch(IDWriteTextLayout* This,UINT32 position,DWRITE_FONT_STRETCH *stretch,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontStretch(This,position,stretch,range);
}
static inline HRESULT IDWriteTextLayout_GetFontSize(IDWriteTextLayout* This,UINT32 position,FLOAT *size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontSize(This,position,size,range);
}
static inline HRESULT IDWriteTextLayout_GetUnderline(IDWriteTextLayout* This,UINT32 position,WINBOOL *has_underline,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetUnderline(This,position,has_underline,range);
}
static inline HRESULT IDWriteTextLayout_GetStrikethrough(IDWriteTextLayout* This,UINT32 position,WINBOOL *has_strikethrough,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetStrikethrough(This,position,has_strikethrough,range);
}
static inline HRESULT IDWriteTextLayout_GetDrawingEffect(IDWriteTextLayout* This,UINT32 position,IUnknown **effect,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetDrawingEffect(This,position,effect,range);
}
static inline HRESULT IDWriteTextLayout_GetInlineObject(IDWriteTextLayout* This,UINT32 position,IDWriteInlineObject **object,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetInlineObject(This,position,object,range);
}
static inline HRESULT IDWriteTextLayout_GetTypography(IDWriteTextLayout* This,UINT32 position,IDWriteTypography **typography,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetTypography(This,position,typography,range);
}
static inline HRESULT IDWriteTextLayout_GetLocaleNameLength(IDWriteTextLayout* This,UINT32 position,UINT32 *length,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetLocaleNameLength(This,position,length,range);
}
static inline HRESULT IDWriteTextLayout_GetLocaleName(IDWriteTextLayout* This,UINT32 position,WCHAR *name,UINT32 name_size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetLocaleName(This,position,name,name_size,range);
}
static inline HRESULT IDWriteTextLayout_Draw(IDWriteTextLayout* This,void *context,IDWriteTextRenderer *renderer,FLOAT originX,FLOAT originY) {
    return This->lpVtbl->Draw(This,context,renderer,originX,originY);
}
static inline HRESULT IDWriteTextLayout_GetLineMetrics(IDWriteTextLayout* This,DWRITE_LINE_METRICS *metrics,UINT32 max_count,UINT32 *actual_count) {
    return This->lpVtbl->GetLineMetrics(This,metrics,max_count,actual_count);
}
static inline HRESULT IDWriteTextLayout_GetMetrics(IDWriteTextLayout* This,DWRITE_TEXT_METRICS *metrics) {
    return This->lpVtbl->GetMetrics(This,metrics);
}
static inline HRESULT IDWriteTextLayout_GetOverhangMetrics(IDWriteTextLayout* This,DWRITE_OVERHANG_METRICS *overhangs) {
    return This->lpVtbl->GetOverhangMetrics(This,overhangs);
}
static inline HRESULT IDWriteTextLayout_GetClusterMetrics(IDWriteTextLayout* This,DWRITE_CLUSTER_METRICS *metrics,UINT32 max_count,UINT32 *act_count) {
    return This->lpVtbl->GetClusterMetrics(This,metrics,max_count,act_count);
}
static inline HRESULT IDWriteTextLayout_DetermineMinWidth(IDWriteTextLayout* This,FLOAT *min_width) {
    return This->lpVtbl->DetermineMinWidth(This,min_width);
}
static inline HRESULT IDWriteTextLayout_HitTestPoint(IDWriteTextLayout* This,FLOAT pointX,FLOAT pointY,WINBOOL *is_trailinghit,WINBOOL *is_inside,DWRITE_HIT_TEST_METRICS *metrics) {
    return This->lpVtbl->HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics);
}
static inline HRESULT IDWriteTextLayout_HitTestTextPosition(IDWriteTextLayout* This,UINT32 textPosition,WINBOOL is_trailinghit,FLOAT *pointX,FLOAT *pointY,DWRITE_HIT_TEST_METRICS *metrics) {
    return This->lpVtbl->HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics);
}
static inline HRESULT IDWriteTextLayout_HitTestTextRange(IDWriteTextLayout* This,UINT32 textPosition,UINT32 textLength,FLOAT originX,FLOAT originY,DWRITE_HIT_TEST_METRICS *metrics,UINT32 max_metricscount,UINT32 *actual_metricscount) {
    return This->lpVtbl->HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextLayout_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteNumberSubstitution interface
 */
#ifndef __IDWriteNumberSubstitution_INTERFACE_DEFINED__
#define __IDWriteNumberSubstitution_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteNumberSubstitution, 0x14885cc9, 0xbab0, 0x4f90, 0xb6,0xed, 0x5c,0x36,0x6a,0x2c,0xd0,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("14885cc9-bab0-4f90-b6ed-5c366a2cd03d")
IDWriteNumberSubstitution : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteNumberSubstitution, 0x14885cc9, 0xbab0, 0x4f90, 0xb6,0xed, 0x5c,0x36,0x6a,0x2c,0xd0,0x3d)
#endif
#else
typedef struct IDWriteNumberSubstitutionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteNumberSubstitution *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteNumberSubstitution *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteNumberSubstitution *This);

    END_INTERFACE
} IDWriteNumberSubstitutionVtbl;

interface IDWriteNumberSubstitution {
    CONST_VTBL IDWriteNumberSubstitutionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteNumberSubstitution_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteNumberSubstitution_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteNumberSubstitution_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteNumberSubstitution_QueryInterface(IDWriteNumberSubstitution* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteNumberSubstitution_AddRef(IDWriteNumberSubstitution* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteNumberSubstitution_Release(IDWriteNumberSubstitution* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IDWriteNumberSubstitution_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextAnalysisSource interface
 */
#ifndef __IDWriteTextAnalysisSource_INTERFACE_DEFINED__
#define __IDWriteTextAnalysisSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextAnalysisSource, 0x688e1a58, 0x5094, 0x47c8, 0xad,0xc8, 0xfb,0xce,0xa6,0x0a,0xe9,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("688e1a58-5094-47c8-adc8-fbcea60ae92b")
IDWriteTextAnalysisSource : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetTextAtPosition(
        UINT32 position,
        const WCHAR **text,
        UINT32 *text_len) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTextBeforePosition(
        UINT32 position,
        const WCHAR **text,
        UINT32 *text_len) = 0;

    virtual DWRITE_READING_DIRECTION STDMETHODCALLTYPE GetParagraphReadingDirection(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocaleName(
        UINT32 position,
        UINT32 *text_len,
        const WCHAR **locale) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberSubstitution(
        UINT32 position,
        UINT32 *text_len,
        IDWriteNumberSubstitution **substitution) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextAnalysisSource, 0x688e1a58, 0x5094, 0x47c8, 0xad,0xc8, 0xfb,0xce,0xa6,0x0a,0xe9,0x2b)
#endif
#else
typedef struct IDWriteTextAnalysisSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextAnalysisSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextAnalysisSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextAnalysisSource *This);

    /*** IDWriteTextAnalysisSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTextAtPosition)(
        IDWriteTextAnalysisSource *This,
        UINT32 position,
        const WCHAR **text,
        UINT32 *text_len);

    HRESULT (STDMETHODCALLTYPE *GetTextBeforePosition)(
        IDWriteTextAnalysisSource *This,
        UINT32 position,
        const WCHAR **text,
        UINT32 *text_len);

    DWRITE_READING_DIRECTION (STDMETHODCALLTYPE *GetParagraphReadingDirection)(
        IDWriteTextAnalysisSource *This);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteTextAnalysisSource *This,
        UINT32 position,
        UINT32 *text_len,
        const WCHAR **locale);

    HRESULT (STDMETHODCALLTYPE *GetNumberSubstitution)(
        IDWriteTextAnalysisSource *This,
        UINT32 position,
        UINT32 *text_len,
        IDWriteNumberSubstitution **substitution);

    END_INTERFACE
} IDWriteTextAnalysisSourceVtbl;

interface IDWriteTextAnalysisSource {
    CONST_VTBL IDWriteTextAnalysisSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextAnalysisSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextAnalysisSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextAnalysisSource_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextAnalysisSource methods ***/
#define IDWriteTextAnalysisSource_GetTextAtPosition(This,position,text,text_len) (This)->lpVtbl->GetTextAtPosition(This,position,text,text_len)
#define IDWriteTextAnalysisSource_GetTextBeforePosition(This,position,text,text_len) (This)->lpVtbl->GetTextBeforePosition(This,position,text,text_len)
#define IDWriteTextAnalysisSource_GetParagraphReadingDirection(This) (This)->lpVtbl->GetParagraphReadingDirection(This)
#define IDWriteTextAnalysisSource_GetLocaleName(This,position,text_len,locale) (This)->lpVtbl->GetLocaleName(This,position,text_len,locale)
#define IDWriteTextAnalysisSource_GetNumberSubstitution(This,position,text_len,substitution) (This)->lpVtbl->GetNumberSubstitution(This,position,text_len,substitution)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextAnalysisSource_QueryInterface(IDWriteTextAnalysisSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextAnalysisSource_AddRef(IDWriteTextAnalysisSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextAnalysisSource_Release(IDWriteTextAnalysisSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextAnalysisSource methods ***/
static inline HRESULT IDWriteTextAnalysisSource_GetTextAtPosition(IDWriteTextAnalysisSource* This,UINT32 position,const WCHAR **text,UINT32 *text_len) {
    return This->lpVtbl->GetTextAtPosition(This,position,text,text_len);
}
static inline HRESULT IDWriteTextAnalysisSource_GetTextBeforePosition(IDWriteTextAnalysisSource* This,UINT32 position,const WCHAR **text,UINT32 *text_len) {
    return This->lpVtbl->GetTextBeforePosition(This,position,text,text_len);
}
static inline DWRITE_READING_DIRECTION IDWriteTextAnalysisSource_GetParagraphReadingDirection(IDWriteTextAnalysisSource* This) {
    return This->lpVtbl->GetParagraphReadingDirection(This);
}
static inline HRESULT IDWriteTextAnalysisSource_GetLocaleName(IDWriteTextAnalysisSource* This,UINT32 position,UINT32 *text_len,const WCHAR **locale) {
    return This->lpVtbl->GetLocaleName(This,position,text_len,locale);
}
static inline HRESULT IDWriteTextAnalysisSource_GetNumberSubstitution(IDWriteTextAnalysisSource* This,UINT32 position,UINT32 *text_len,IDWriteNumberSubstitution **substitution) {
    return This->lpVtbl->GetNumberSubstitution(This,position,text_len,substitution);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextAnalysisSource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextAnalysisSink interface
 */
#ifndef __IDWriteTextAnalysisSink_INTERFACE_DEFINED__
#define __IDWriteTextAnalysisSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextAnalysisSink, 0x5810cd44, 0x0ca0, 0x4701, 0xb3,0xfa, 0xbe,0xc5,0x18,0x2a,0xe4,0xf6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5810cd44-0ca0-4701-b3fa-bec5182ae4f6")
IDWriteTextAnalysisSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetScriptAnalysis(
        UINT32 position,
        UINT32 length,
        const DWRITE_SCRIPT_ANALYSIS *scriptanalysis) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLineBreakpoints(
        UINT32 position,
        UINT32 length,
        const DWRITE_LINE_BREAKPOINT *breakpoints) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBidiLevel(
        UINT32 position,
        UINT32 length,
        UINT8 explicitLevel,
        UINT8 resolvedLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNumberSubstitution(
        UINT32 position,
        UINT32 length,
        IDWriteNumberSubstitution *substitution) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextAnalysisSink, 0x5810cd44, 0x0ca0, 0x4701, 0xb3,0xfa, 0xbe,0xc5,0x18,0x2a,0xe4,0xf6)
#endif
#else
typedef struct IDWriteTextAnalysisSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextAnalysisSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextAnalysisSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextAnalysisSink *This);

    /*** IDWriteTextAnalysisSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetScriptAnalysis)(
        IDWriteTextAnalysisSink *This,
        UINT32 position,
        UINT32 length,
        const DWRITE_SCRIPT_ANALYSIS *scriptanalysis);

    HRESULT (STDMETHODCALLTYPE *SetLineBreakpoints)(
        IDWriteTextAnalysisSink *This,
        UINT32 position,
        UINT32 length,
        const DWRITE_LINE_BREAKPOINT *breakpoints);

    HRESULT (STDMETHODCALLTYPE *SetBidiLevel)(
        IDWriteTextAnalysisSink *This,
        UINT32 position,
        UINT32 length,
        UINT8 explicitLevel,
        UINT8 resolvedLevel);

    HRESULT (STDMETHODCALLTYPE *SetNumberSubstitution)(
        IDWriteTextAnalysisSink *This,
        UINT32 position,
        UINT32 length,
        IDWriteNumberSubstitution *substitution);

    END_INTERFACE
} IDWriteTextAnalysisSinkVtbl;

interface IDWriteTextAnalysisSink {
    CONST_VTBL IDWriteTextAnalysisSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextAnalysisSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextAnalysisSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextAnalysisSink_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextAnalysisSink methods ***/
#define IDWriteTextAnalysisSink_SetScriptAnalysis(This,position,length,scriptanalysis) (This)->lpVtbl->SetScriptAnalysis(This,position,length,scriptanalysis)
#define IDWriteTextAnalysisSink_SetLineBreakpoints(This,position,length,breakpoints) (This)->lpVtbl->SetLineBreakpoints(This,position,length,breakpoints)
#define IDWriteTextAnalysisSink_SetBidiLevel(This,position,length,explicitLevel,resolvedLevel) (This)->lpVtbl->SetBidiLevel(This,position,length,explicitLevel,resolvedLevel)
#define IDWriteTextAnalysisSink_SetNumberSubstitution(This,position,length,substitution) (This)->lpVtbl->SetNumberSubstitution(This,position,length,substitution)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextAnalysisSink_QueryInterface(IDWriteTextAnalysisSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextAnalysisSink_AddRef(IDWriteTextAnalysisSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextAnalysisSink_Release(IDWriteTextAnalysisSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextAnalysisSink methods ***/
static inline HRESULT IDWriteTextAnalysisSink_SetScriptAnalysis(IDWriteTextAnalysisSink* This,UINT32 position,UINT32 length,const DWRITE_SCRIPT_ANALYSIS *scriptanalysis) {
    return This->lpVtbl->SetScriptAnalysis(This,position,length,scriptanalysis);
}
static inline HRESULT IDWriteTextAnalysisSink_SetLineBreakpoints(IDWriteTextAnalysisSink* This,UINT32 position,UINT32 length,const DWRITE_LINE_BREAKPOINT *breakpoints) {
    return This->lpVtbl->SetLineBreakpoints(This,position,length,breakpoints);
}
static inline HRESULT IDWriteTextAnalysisSink_SetBidiLevel(IDWriteTextAnalysisSink* This,UINT32 position,UINT32 length,UINT8 explicitLevel,UINT8 resolvedLevel) {
    return This->lpVtbl->SetBidiLevel(This,position,length,explicitLevel,resolvedLevel);
}
static inline HRESULT IDWriteTextAnalysisSink_SetNumberSubstitution(IDWriteTextAnalysisSink* This,UINT32 position,UINT32 length,IDWriteNumberSubstitution *substitution) {
    return This->lpVtbl->SetNumberSubstitution(This,position,length,substitution);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextAnalysisSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextAnalyzer interface
 */
#ifndef __IDWriteTextAnalyzer_INTERFACE_DEFINED__
#define __IDWriteTextAnalyzer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextAnalyzer, 0xb7e6163e, 0x7f46, 0x43b4, 0x84,0xb3, 0xe4,0xe6,0x24,0x9c,0x36,0x5d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b7e6163e-7f46-43b4-84b3-e4e6249c365d")
IDWriteTextAnalyzer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AnalyzeScript(
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink) = 0;

    virtual HRESULT STDMETHODCALLTYPE AnalyzeBidi(
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink) = 0;

    virtual HRESULT STDMETHODCALLTYPE AnalyzeNumberSubstitution(
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink) = 0;

    virtual HRESULT STDMETHODCALLTYPE AnalyzeLineBreakpoints(
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphs(
        const WCHAR *text,
        UINT32 length,
        IDWriteFontFace *font_face,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        IDWriteNumberSubstitution *substitution,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        UINT32 max_glyph_count,
        UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *text_props,
        UINT16 *glyph_indices,
        DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 *actual_glyph_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphPlacements(
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGdiCompatibleGlyphPlacements(
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_lengths,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextAnalyzer, 0xb7e6163e, 0x7f46, 0x43b4, 0x84,0xb3, 0xe4,0xe6,0x24,0x9c,0x36,0x5d)
#endif
#else
typedef struct IDWriteTextAnalyzerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextAnalyzer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextAnalyzer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextAnalyzer *This);

    /*** IDWriteTextAnalyzer methods ***/
    HRESULT (STDMETHODCALLTYPE *AnalyzeScript)(
        IDWriteTextAnalyzer *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeBidi)(
        IDWriteTextAnalyzer *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeNumberSubstitution)(
        IDWriteTextAnalyzer *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeLineBreakpoints)(
        IDWriteTextAnalyzer *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *GetGlyphs)(
        IDWriteTextAnalyzer *This,
        const WCHAR *text,
        UINT32 length,
        IDWriteFontFace *font_face,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        IDWriteNumberSubstitution *substitution,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        UINT32 max_glyph_count,
        UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *text_props,
        UINT16 *glyph_indices,
        DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 *actual_glyph_count);

    HRESULT (STDMETHODCALLTYPE *GetGlyphPlacements)(
        IDWriteTextAnalyzer *This,
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphPlacements)(
        IDWriteTextAnalyzer *This,
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_lengths,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets);

    END_INTERFACE
} IDWriteTextAnalyzerVtbl;

interface IDWriteTextAnalyzer {
    CONST_VTBL IDWriteTextAnalyzerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextAnalyzer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextAnalyzer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextAnalyzer_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextAnalyzer methods ***/
#define IDWriteTextAnalyzer_AnalyzeScript(This,source,position,length,sink) (This)->lpVtbl->AnalyzeScript(This,source,position,length,sink)
#define IDWriteTextAnalyzer_AnalyzeBidi(This,source,position,length,sink) (This)->lpVtbl->AnalyzeBidi(This,source,position,length,sink)
#define IDWriteTextAnalyzer_AnalyzeNumberSubstitution(This,source,position,length,sink) (This)->lpVtbl->AnalyzeNumberSubstitution(This,source,position,length,sink)
#define IDWriteTextAnalyzer_AnalyzeLineBreakpoints(This,source,position,length,sink) (This)->lpVtbl->AnalyzeLineBreakpoints(This,source,position,length,sink)
#define IDWriteTextAnalyzer_GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count) (This)->lpVtbl->GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count)
#define IDWriteTextAnalyzer_GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets) (This)->lpVtbl->GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets)
#define IDWriteTextAnalyzer_GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets) (This)->lpVtbl->GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextAnalyzer_QueryInterface(IDWriteTextAnalyzer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextAnalyzer_AddRef(IDWriteTextAnalyzer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextAnalyzer_Release(IDWriteTextAnalyzer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextAnalyzer methods ***/
static inline HRESULT IDWriteTextAnalyzer_AnalyzeScript(IDWriteTextAnalyzer* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeScript(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer_AnalyzeBidi(IDWriteTextAnalyzer* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeBidi(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer_AnalyzeNumberSubstitution(IDWriteTextAnalyzer* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeNumberSubstitution(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer_AnalyzeLineBreakpoints(IDWriteTextAnalyzer* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeLineBreakpoints(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer_GetGlyphs(IDWriteTextAnalyzer* This,const WCHAR *text,UINT32 length,IDWriteFontFace *font_face,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,IDWriteNumberSubstitution *substitution,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_len,UINT32 feature_ranges,UINT32 max_glyph_count,UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *text_props,UINT16 *glyph_indices,DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 *actual_glyph_count) {
    return This->lpVtbl->GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count);
}
static inline HRESULT IDWriteTextAnalyzer_GetGlyphPlacements(IDWriteTextAnalyzer* This,const WCHAR *text,const UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *props,UINT32 text_len,const UINT16 *glyph_indices,const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 glyph_count,IDWriteFontFace *font_face,FLOAT fontEmSize,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_len,UINT32 feature_ranges,FLOAT *glyph_advances,DWRITE_GLYPH_OFFSET *glyph_offsets) {
    return This->lpVtbl->GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets);
}
static inline HRESULT IDWriteTextAnalyzer_GetGdiCompatibleGlyphPlacements(IDWriteTextAnalyzer* This,const WCHAR *text,const UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *props,UINT32 text_len,const UINT16 *glyph_indices,const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 glyph_count,IDWriteFontFace *font_face,FLOAT fontEmSize,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_lengths,UINT32 feature_ranges,FLOAT *glyph_advances,DWRITE_GLYPH_OFFSET *glyph_offsets) {
    return This->lpVtbl->GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextAnalyzer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteGlyphRunAnalysis interface
 */
#ifndef __IDWriteGlyphRunAnalysis_INTERFACE_DEFINED__
#define __IDWriteGlyphRunAnalysis_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteGlyphRunAnalysis, 0x7d97dbf7, 0xe085, 0x42d4, 0x81,0xe3, 0x6a,0x88,0x3b,0xde,0xd1,0x18);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7d97dbf7-e085-42d4-81e3-6a883bded118")
IDWriteGlyphRunAnalysis : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetAlphaTextureBounds(
        DWRITE_TEXTURE_TYPE type,
        RECT *bounds) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAlphaTexture(
        DWRITE_TEXTURE_TYPE type,
        const RECT *bounds,
        BYTE *alphaValues,
        UINT32 bufferSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlphaBlendParams(
        IDWriteRenderingParams *renderingParams,
        FLOAT *blendGamma,
        FLOAT *blendEnhancedContrast,
        FLOAT *blendClearTypeLevel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteGlyphRunAnalysis, 0x7d97dbf7, 0xe085, 0x42d4, 0x81,0xe3, 0x6a,0x88,0x3b,0xde,0xd1,0x18)
#endif
#else
typedef struct IDWriteGlyphRunAnalysisVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteGlyphRunAnalysis *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteGlyphRunAnalysis *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteGlyphRunAnalysis *This);

    /*** IDWriteGlyphRunAnalysis methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAlphaTextureBounds)(
        IDWriteGlyphRunAnalysis *This,
        DWRITE_TEXTURE_TYPE type,
        RECT *bounds);

    HRESULT (STDMETHODCALLTYPE *CreateAlphaTexture)(
        IDWriteGlyphRunAnalysis *This,
        DWRITE_TEXTURE_TYPE type,
        const RECT *bounds,
        BYTE *alphaValues,
        UINT32 bufferSize);

    HRESULT (STDMETHODCALLTYPE *GetAlphaBlendParams)(
        IDWriteGlyphRunAnalysis *This,
        IDWriteRenderingParams *renderingParams,
        FLOAT *blendGamma,
        FLOAT *blendEnhancedContrast,
        FLOAT *blendClearTypeLevel);

    END_INTERFACE
} IDWriteGlyphRunAnalysisVtbl;

interface IDWriteGlyphRunAnalysis {
    CONST_VTBL IDWriteGlyphRunAnalysisVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteGlyphRunAnalysis_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteGlyphRunAnalysis_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteGlyphRunAnalysis_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteGlyphRunAnalysis methods ***/
#define IDWriteGlyphRunAnalysis_GetAlphaTextureBounds(This,type,bounds) (This)->lpVtbl->GetAlphaTextureBounds(This,type,bounds)
#define IDWriteGlyphRunAnalysis_CreateAlphaTexture(This,type,bounds,alphaValues,bufferSize) (This)->lpVtbl->CreateAlphaTexture(This,type,bounds,alphaValues,bufferSize)
#define IDWriteGlyphRunAnalysis_GetAlphaBlendParams(This,renderingParams,blendGamma,blendEnhancedContrast,blendClearTypeLevel) (This)->lpVtbl->GetAlphaBlendParams(This,renderingParams,blendGamma,blendEnhancedContrast,blendClearTypeLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteGlyphRunAnalysis_QueryInterface(IDWriteGlyphRunAnalysis* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteGlyphRunAnalysis_AddRef(IDWriteGlyphRunAnalysis* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteGlyphRunAnalysis_Release(IDWriteGlyphRunAnalysis* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteGlyphRunAnalysis methods ***/
static inline HRESULT IDWriteGlyphRunAnalysis_GetAlphaTextureBounds(IDWriteGlyphRunAnalysis* This,DWRITE_TEXTURE_TYPE type,RECT *bounds) {
    return This->lpVtbl->GetAlphaTextureBounds(This,type,bounds);
}
static inline HRESULT IDWriteGlyphRunAnalysis_CreateAlphaTexture(IDWriteGlyphRunAnalysis* This,DWRITE_TEXTURE_TYPE type,const RECT *bounds,BYTE *alphaValues,UINT32 bufferSize) {
    return This->lpVtbl->CreateAlphaTexture(This,type,bounds,alphaValues,bufferSize);
}
static inline HRESULT IDWriteGlyphRunAnalysis_GetAlphaBlendParams(IDWriteGlyphRunAnalysis* This,IDWriteRenderingParams *renderingParams,FLOAT *blendGamma,FLOAT *blendEnhancedContrast,FLOAT *blendClearTypeLevel) {
    return This->lpVtbl->GetAlphaBlendParams(This,renderingParams,blendGamma,blendEnhancedContrast,blendClearTypeLevel);
}
#endif
#endif

#endif


#endif  /* __IDWriteGlyphRunAnalysis_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFactory interface
 */
#ifndef __IDWriteFactory_INTERFACE_DEFINED__
#define __IDWriteFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFactory, 0xb859ee5a, 0xd838, 0x4b5b, 0xa2,0xe8, 0x1a,0xdc,0x7d,0x93,0xdb,0x48);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b859ee5a-d838-4b5b-a2e8-1adc7d93db48")
IDWriteFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSystemFontCollection(
        IDWriteFontCollection **collection,
        WINBOOL check_for_updates = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCustomFontCollection(
        IDWriteFontCollectionLoader *loader,
        const void *key,
        UINT32 key_size,
        IDWriteFontCollection **collection) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterFontCollectionLoader(
        IDWriteFontCollectionLoader *loader) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterFontCollectionLoader(
        IDWriteFontCollectionLoader *loader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFontFileReference(
        const WCHAR *path,
        const FILETIME *writetime,
        IDWriteFontFile **font_file) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCustomFontFileReference(
        const void *reference_key,
        UINT32 key_size,
        IDWriteFontFileLoader *loader,
        IDWriteFontFile **font_file) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFontFace(
        DWRITE_FONT_FACE_TYPE facetype,
        UINT32 files_number,
        IDWriteFontFile *const *font_files,
        UINT32 index,
        DWRITE_FONT_SIMULATIONS sim_flags,
        IDWriteFontFace **font_face) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRenderingParams(
        IDWriteRenderingParams **params) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMonitorRenderingParams(
        HMONITOR monitor,
        IDWriteRenderingParams **params) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCustomRenderingParams(
        FLOAT gamma,
        FLOAT enhancedContrast,
        FLOAT cleartype_level,
        DWRITE_PIXEL_GEOMETRY geometry,
        DWRITE_RENDERING_MODE mode,
        IDWriteRenderingParams **params) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterFontFileLoader(
        IDWriteFontFileLoader *loader) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterFontFileLoader(
        IDWriteFontFileLoader *loader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTextFormat(
        const WCHAR *family_name,
        IDWriteFontCollection *collection,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STYLE style,
        DWRITE_FONT_STRETCH stretch,
        FLOAT size,
        const WCHAR *locale,
        IDWriteTextFormat **format) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTypography(
        IDWriteTypography **typography) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGdiInterop(
        IDWriteGdiInterop **gdi_interop) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTextLayout(
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT max_width,
        FLOAT max_height,
        IDWriteTextLayout **layout) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGdiCompatibleTextLayout(
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT layout_width,
        FLOAT layout_height,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        IDWriteTextLayout **layout) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateEllipsisTrimmingSign(
        IDWriteTextFormat *format,
        IDWriteInlineObject **trimming_sign) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTextAnalyzer(
        IDWriteTextAnalyzer **analyzer) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNumberSubstitution(
        DWRITE_NUMBER_SUBSTITUTION_METHOD method,
        const WCHAR *locale,
        WINBOOL ignore_user_override,
        IDWriteNumberSubstitution **substitution) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGlyphRunAnalysis(
        const DWRITE_GLYPH_RUN *glyph_run,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_RENDERING_MODE rendering_mode,
        DWRITE_MEASURING_MODE measuring_mode,
        FLOAT baseline_x,
        FLOAT baseline_y,
        IDWriteGlyphRunAnalysis **analysis) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFactory, 0xb859ee5a, 0xd838, 0x4b5b, 0xa2,0xe8, 0x1a,0xdc,0x7d,0x93,0xdb,0x48)
#endif
#else
typedef struct IDWriteFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFactory *This);

    /*** IDWriteFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemFontCollection)(
        IDWriteFactory *This,
        IDWriteFontCollection **collection,
        WINBOOL check_for_updates);

    HRESULT (STDMETHODCALLTYPE *CreateCustomFontCollection)(
        IDWriteFactory *This,
        IDWriteFontCollectionLoader *loader,
        const void *key,
        UINT32 key_size,
        IDWriteFontCollection **collection);

    HRESULT (STDMETHODCALLTYPE *RegisterFontCollectionLoader)(
        IDWriteFactory *This,
        IDWriteFontCollectionLoader *loader);

    HRESULT (STDMETHODCALLTYPE *UnregisterFontCollectionLoader)(
        IDWriteFactory *This,
        IDWriteFontCollectionLoader *loader);

    HRESULT (STDMETHODCALLTYPE *CreateFontFileReference)(
        IDWriteFactory *This,
        const WCHAR *path,
        const FILETIME *writetime,
        IDWriteFontFile **font_file);

    HRESULT (STDMETHODCALLTYPE *CreateCustomFontFileReference)(
        IDWriteFactory *This,
        const void *reference_key,
        UINT32 key_size,
        IDWriteFontFileLoader *loader,
        IDWriteFontFile **font_file);

    HRESULT (STDMETHODCALLTYPE *CreateFontFace)(
        IDWriteFactory *This,
        DWRITE_FONT_FACE_TYPE facetype,
        UINT32 files_number,
        IDWriteFontFile *const *font_files,
        UINT32 index,
        DWRITE_FONT_SIMULATIONS sim_flags,
        IDWriteFontFace **font_face);

    HRESULT (STDMETHODCALLTYPE *CreateRenderingParams)(
        IDWriteFactory *This,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *CreateMonitorRenderingParams)(
        IDWriteFactory *This,
        HMONITOR monitor,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *CreateCustomRenderingParams)(
        IDWriteFactory *This,
        FLOAT gamma,
        FLOAT enhancedContrast,
        FLOAT cleartype_level,
        DWRITE_PIXEL_GEOMETRY geometry,
        DWRITE_RENDERING_MODE mode,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *RegisterFontFileLoader)(
        IDWriteFactory *This,
        IDWriteFontFileLoader *loader);

    HRESULT (STDMETHODCALLTYPE *UnregisterFontFileLoader)(
        IDWriteFactory *This,
        IDWriteFontFileLoader *loader);

    HRESULT (STDMETHODCALLTYPE *CreateTextFormat)(
        IDWriteFactory *This,
        const WCHAR *family_name,
        IDWriteFontCollection *collection,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STYLE style,
        DWRITE_FONT_STRETCH stretch,
        FLOAT size,
        const WCHAR *locale,
        IDWriteTextFormat **format);

    HRESULT (STDMETHODCALLTYPE *CreateTypography)(
        IDWriteFactory *This,
        IDWriteTypography **typography);

    HRESULT (STDMETHODCALLTYPE *GetGdiInterop)(
        IDWriteFactory *This,
        IDWriteGdiInterop **gdi_interop);

    HRESULT (STDMETHODCALLTYPE *CreateTextLayout)(
        IDWriteFactory *This,
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT max_width,
        FLOAT max_height,
        IDWriteTextLayout **layout);

    HRESULT (STDMETHODCALLTYPE *CreateGdiCompatibleTextLayout)(
        IDWriteFactory *This,
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT layout_width,
        FLOAT layout_height,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        IDWriteTextLayout **layout);

    HRESULT (STDMETHODCALLTYPE *CreateEllipsisTrimmingSign)(
        IDWriteFactory *This,
        IDWriteTextFormat *format,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *CreateTextAnalyzer)(
        IDWriteFactory *This,
        IDWriteTextAnalyzer **analyzer);

    HRESULT (STDMETHODCALLTYPE *CreateNumberSubstitution)(
        IDWriteFactory *This,
        DWRITE_NUMBER_SUBSTITUTION_METHOD method,
        const WCHAR *locale,
        WINBOOL ignore_user_override,
        IDWriteNumberSubstitution **substitution);

    HRESULT (STDMETHODCALLTYPE *CreateGlyphRunAnalysis)(
        IDWriteFactory *This,
        const DWRITE_GLYPH_RUN *glyph_run,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_RENDERING_MODE rendering_mode,
        DWRITE_MEASURING_MODE measuring_mode,
        FLOAT baseline_x,
        FLOAT baseline_y,
        IDWriteGlyphRunAnalysis **analysis);

    END_INTERFACE
} IDWriteFactoryVtbl;

interface IDWriteFactory {
    CONST_VTBL IDWriteFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFactory methods ***/
#define IDWriteFactory_GetSystemFontCollection(This,collection,check_for_updates) (This)->lpVtbl->GetSystemFontCollection(This,collection,check_for_updates)
#define IDWriteFactory_CreateCustomFontCollection(This,loader,key,key_size,collection) (This)->lpVtbl->CreateCustomFontCollection(This,loader,key,key_size,collection)
#define IDWriteFactory_RegisterFontCollectionLoader(This,loader) (This)->lpVtbl->RegisterFontCollectionLoader(This,loader)
#define IDWriteFactory_UnregisterFontCollectionLoader(This,loader) (This)->lpVtbl->UnregisterFontCollectionLoader(This,loader)
#define IDWriteFactory_CreateFontFileReference(This,path,writetime,font_file) (This)->lpVtbl->CreateFontFileReference(This,path,writetime,font_file)
#define IDWriteFactory_CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file) (This)->lpVtbl->CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file)
#define IDWriteFactory_CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face) (This)->lpVtbl->CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face)
#define IDWriteFactory_CreateRenderingParams(This,params) (This)->lpVtbl->CreateRenderingParams(This,params)
#define IDWriteFactory_CreateMonitorRenderingParams(This,monitor,params) (This)->lpVtbl->CreateMonitorRenderingParams(This,monitor,params)
#define IDWriteFactory_CreateCustomRenderingParams(This,gamma,enhancedContrast,cleartype_level,geometry,mode,params) (This)->lpVtbl->CreateCustomRenderingParams(This,gamma,enhancedContrast,cleartype_level,geometry,mode,params)
#define IDWriteFactory_RegisterFontFileLoader(This,loader) (This)->lpVtbl->RegisterFontFileLoader(This,loader)
#define IDWriteFactory_UnregisterFontFileLoader(This,loader) (This)->lpVtbl->UnregisterFontFileLoader(This,loader)
#define IDWriteFactory_CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format) (This)->lpVtbl->CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format)
#define IDWriteFactory_CreateTypography(This,typography) (This)->lpVtbl->CreateTypography(This,typography)
#define IDWriteFactory_GetGdiInterop(This,gdi_interop) (This)->lpVtbl->GetGdiInterop(This,gdi_interop)
#define IDWriteFactory_CreateTextLayout(This,string,len,format,max_width,max_height,layout) (This)->lpVtbl->CreateTextLayout(This,string,len,format,max_width,max_height,layout)
#define IDWriteFactory_CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout) (This)->lpVtbl->CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout)
#define IDWriteFactory_CreateEllipsisTrimmingSign(This,format,trimming_sign) (This)->lpVtbl->CreateEllipsisTrimmingSign(This,format,trimming_sign)
#define IDWriteFactory_CreateTextAnalyzer(This,analyzer) (This)->lpVtbl->CreateTextAnalyzer(This,analyzer)
#define IDWriteFactory_CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution) (This)->lpVtbl->CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution)
#define IDWriteFactory_CreateGlyphRunAnalysis(This,glyph_run,pixels_per_dip,transform,rendering_mode,measuring_mode,baseline_x,baseline_y,analysis) (This)->lpVtbl->CreateGlyphRunAnalysis(This,glyph_run,pixels_per_dip,transform,rendering_mode,measuring_mode,baseline_x,baseline_y,analysis)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFactory_QueryInterface(IDWriteFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFactory_AddRef(IDWriteFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFactory_Release(IDWriteFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFactory methods ***/
static inline HRESULT IDWriteFactory_GetSystemFontCollection(IDWriteFactory* This,IDWriteFontCollection **collection,WINBOOL check_for_updates) {
    return This->lpVtbl->GetSystemFontCollection(This,collection,check_for_updates);
}
static inline HRESULT IDWriteFactory_CreateCustomFontCollection(IDWriteFactory* This,IDWriteFontCollectionLoader *loader,const void *key,UINT32 key_size,IDWriteFontCollection **collection) {
    return This->lpVtbl->CreateCustomFontCollection(This,loader,key,key_size,collection);
}
static inline HRESULT IDWriteFactory_RegisterFontCollectionLoader(IDWriteFactory* This,IDWriteFontCollectionLoader *loader) {
    return This->lpVtbl->RegisterFontCollectionLoader(This,loader);
}
static inline HRESULT IDWriteFactory_UnregisterFontCollectionLoader(IDWriteFactory* This,IDWriteFontCollectionLoader *loader) {
    return This->lpVtbl->UnregisterFontCollectionLoader(This,loader);
}
static inline HRESULT IDWriteFactory_CreateFontFileReference(IDWriteFactory* This,const WCHAR *path,const FILETIME *writetime,IDWriteFontFile **font_file) {
    return This->lpVtbl->CreateFontFileReference(This,path,writetime,font_file);
}
static inline HRESULT IDWriteFactory_CreateCustomFontFileReference(IDWriteFactory* This,const void *reference_key,UINT32 key_size,IDWriteFontFileLoader *loader,IDWriteFontFile **font_file) {
    return This->lpVtbl->CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file);
}
static inline HRESULT IDWriteFactory_CreateFontFace(IDWriteFactory* This,DWRITE_FONT_FACE_TYPE facetype,UINT32 files_number,IDWriteFontFile *const *font_files,UINT32 index,DWRITE_FONT_SIMULATIONS sim_flags,IDWriteFontFace **font_face) {
    return This->lpVtbl->CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face);
}
static inline HRESULT IDWriteFactory_CreateRenderingParams(IDWriteFactory* This,IDWriteRenderingParams **params) {
    return This->lpVtbl->CreateRenderingParams(This,params);
}
static inline HRESULT IDWriteFactory_CreateMonitorRenderingParams(IDWriteFactory* This,HMONITOR monitor,IDWriteRenderingParams **params) {
    return This->lpVtbl->CreateMonitorRenderingParams(This,monitor,params);
}
static inline HRESULT IDWriteFactory_CreateCustomRenderingParams(IDWriteFactory* This,FLOAT gamma,FLOAT enhancedContrast,FLOAT cleartype_level,DWRITE_PIXEL_GEOMETRY geometry,DWRITE_RENDERING_MODE mode,IDWriteRenderingParams **params) {
    return This->lpVtbl->CreateCustomRenderingParams(This,gamma,enhancedContrast,cleartype_level,geometry,mode,params);
}
static inline HRESULT IDWriteFactory_RegisterFontFileLoader(IDWriteFactory* This,IDWriteFontFileLoader *loader) {
    return This->lpVtbl->RegisterFontFileLoader(This,loader);
}
static inline HRESULT IDWriteFactory_UnregisterFontFileLoader(IDWriteFactory* This,IDWriteFontFileLoader *loader) {
    return This->lpVtbl->UnregisterFontFileLoader(This,loader);
}
static inline HRESULT IDWriteFactory_CreateTextFormat(IDWriteFactory* This,const WCHAR *family_name,IDWriteFontCollection *collection,DWRITE_FONT_WEIGHT weight,DWRITE_FONT_STYLE style,DWRITE_FONT_STRETCH stretch,FLOAT size,const WCHAR *locale,IDWriteTextFormat **format) {
    return This->lpVtbl->CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format);
}
static inline HRESULT IDWriteFactory_CreateTypography(IDWriteFactory* This,IDWriteTypography **typography) {
    return This->lpVtbl->CreateTypography(This,typography);
}
static inline HRESULT IDWriteFactory_GetGdiInterop(IDWriteFactory* This,IDWriteGdiInterop **gdi_interop) {
    return This->lpVtbl->GetGdiInterop(This,gdi_interop);
}
static inline HRESULT IDWriteFactory_CreateTextLayout(IDWriteFactory* This,const WCHAR *string,UINT32 len,IDWriteTextFormat *format,FLOAT max_width,FLOAT max_height,IDWriteTextLayout **layout) {
    return This->lpVtbl->CreateTextLayout(This,string,len,format,max_width,max_height,layout);
}
static inline HRESULT IDWriteFactory_CreateGdiCompatibleTextLayout(IDWriteFactory* This,const WCHAR *string,UINT32 len,IDWriteTextFormat *format,FLOAT layout_width,FLOAT layout_height,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,IDWriteTextLayout **layout) {
    return This->lpVtbl->CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout);
}
static inline HRESULT IDWriteFactory_CreateEllipsisTrimmingSign(IDWriteFactory* This,IDWriteTextFormat *format,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->CreateEllipsisTrimmingSign(This,format,trimming_sign);
}
static inline HRESULT IDWriteFactory_CreateTextAnalyzer(IDWriteFactory* This,IDWriteTextAnalyzer **analyzer) {
    return This->lpVtbl->CreateTextAnalyzer(This,analyzer);
}
static inline HRESULT IDWriteFactory_CreateNumberSubstitution(IDWriteFactory* This,DWRITE_NUMBER_SUBSTITUTION_METHOD method,const WCHAR *locale,WINBOOL ignore_user_override,IDWriteNumberSubstitution **substitution) {
    return This->lpVtbl->CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution);
}
static inline HRESULT IDWriteFactory_CreateGlyphRunAnalysis(IDWriteFactory* This,const DWRITE_GLYPH_RUN *glyph_run,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,DWRITE_RENDERING_MODE rendering_mode,DWRITE_MEASURING_MODE measuring_mode,FLOAT baseline_x,FLOAT baseline_y,IDWriteGlyphRunAnalysis **analysis) {
    return This->lpVtbl->CreateGlyphRunAnalysis(This,glyph_run,pixels_per_dip,transform,rendering_mode,measuring_mode,baseline_x,baseline_y,analysis);
}
#endif
#endif

#endif


#endif  /* __IDWriteFactory_INTERFACE_DEFINED__ */

HRESULT WINAPI DWriteCreateFactory(DWRITE_FACTORY_TYPE,REFIID,IUnknown**);
#define FACILITY_DWRITE 0x898
#define DWRITE_ERR_BASE 0x5000
#define MAKE_DWRITE_HR(severity, code) MAKE_HRESULT(severity, FACILITY_DWRITE, (DWRITE_ERR_BASE + code))
#define MAKE_DWRITE_HR_ERR(code) MAKE_DWRITE_HR(SEVERITY_ERROR, code)
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dwrite_h__ */
