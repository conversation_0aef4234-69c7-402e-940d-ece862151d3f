/*** Autogenerated by WIDL 10.12 from include/d3d11sdklayers.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d11sdklayers_h__
#define __d3d11sdklayers_h__

/* Forward declarations */

#ifndef __ID3D11Debug_FWD_DEFINED__
#define __ID3D11Debug_FWD_DEFINED__
typedef interface ID3D11Debug ID3D11Debug;
#ifdef __cplusplus
interface ID3D11Debug;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11SwitchToRef_FWD_DEFINED__
#define __ID3D11SwitchToRef_FWD_DEFINED__
typedef interface ID3D11SwitchToRef ID3D11SwitchToRef;
#ifdef __cplusplus
interface ID3D11SwitchToRef;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11InfoQueue_FWD_DEFINED__
#define __ID3D11InfoQueue_FWD_DEFINED__
typedef interface ID3D11InfoQueue ID3D11InfoQueue;
#ifdef __cplusplus
interface ID3D11InfoQueue;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <d3d11.h>

#ifdef __cplusplus
extern "C" {
#endif

#define D3D11_DEBUG_FEATURE_FLUSH_PER_RENDER_OP (0x1)

#define D3D11_DEBUG_FEATURE_FINISH_PER_RENDER_OP (0x2)

#define D3D11_DEBUG_FEATURE_PRESENT_PER_RENDER_OP (0x4)

#define D3D11_DEBUG_FEATURE_ALWAYS_DISCARD_OFFERED_RESOURCE (0x8)

#define D3D11_DEBUG_FEATURE_NEVER_DISCARD_OFFERED_RESOURCE (0x10)

#define D3D11_DEBUG_FEATURE_AVOID_BEHAVIOR_CHANGING_DEBUG_AIDS (0x40)

#define D3D11_DEBUG_FEATURE_DISABLE_TILED_RESOURCE_MAPPING_TRACKING_AND_VALIDATION (0x80)

DEFINE_GUID(DXGI_DEBUG_D3D11, 0x4b99317b, 0xac39, 0x4aa6, 0xbb, 0xb, 0xba, 0xa0, 0x47, 0x84, 0x79, 0x8f);
typedef enum D3D11_MESSAGE_CATEGORY {
    D3D11_MESSAGE_CATEGORY_APPLICATION_DEFINED = 0,
    D3D11_MESSAGE_CATEGORY_MISCELLANEOUS = 1,
    D3D11_MESSAGE_CATEGORY_INITIALIZATION = 2,
    D3D11_MESSAGE_CATEGORY_CLEANUP = 3,
    D3D11_MESSAGE_CATEGORY_COMPILATION = 4,
    D3D11_MESSAGE_CATEGORY_STATE_CREATION = 5,
    D3D11_MESSAGE_CATEGORY_STATE_SETTING = 6,
    D3D11_MESSAGE_CATEGORY_STATE_GETTING = 7,
    D3D11_MESSAGE_CATEGORY_RESOURCE_MANIPULATION = 8,
    D3D11_MESSAGE_CATEGORY_EXECUTION = 9,
    D3D11_MESSAGE_CATEGORY_SHADER = 10
} D3D11_MESSAGE_CATEGORY;
typedef enum D3D11_MESSAGE_SEVERITY {
    D3D11_MESSAGE_SEVERITY_CORRUPTION = 0,
    D3D11_MESSAGE_SEVERITY_ERROR = 1,
    D3D11_MESSAGE_SEVERITY_WARNING = 2,
    D3D11_MESSAGE_SEVERITY_INFO = 3,
    D3D11_MESSAGE_SEVERITY_MESSAGE = 4
} D3D11_MESSAGE_SEVERITY;
typedef enum D3D11_MESSAGE_ID {
    D3D11_MESSAGE_ID_UNKNOWN = 0,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_HAZARD = 1,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_HAZARD = 2,
    D3D11_MESSAGE_ID_DEVICE_VSSETSHADERRESOURCES_HAZARD = 3,
    D3D11_MESSAGE_ID_DEVICE_VSSETCONSTANTBUFFERS_HAZARD = 4,
    D3D11_MESSAGE_ID_DEVICE_GSSETSHADERRESOURCES_HAZARD = 5,
    D3D11_MESSAGE_ID_DEVICE_GSSETCONSTANTBUFFERS_HAZARD = 6,
    D3D11_MESSAGE_ID_DEVICE_PSSETSHADERRESOURCES_HAZARD = 7,
    D3D11_MESSAGE_ID_DEVICE_PSSETCONSTANTBUFFERS_HAZARD = 8,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETS_HAZARD = 9,
    D3D11_MESSAGE_ID_DEVICE_SOSETTARGETS_HAZARD = 10,
    D3D11_MESSAGE_ID_STRING_FROM_APPLICATION = 11,
    D3D11_MESSAGE_ID_CORRUPTED_THIS = 12,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER1 = 13,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER2 = 14,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER3 = 15,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER4 = 16,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER5 = 17,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER6 = 18,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER7 = 19,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER8 = 20,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER9 = 21,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER10 = 22,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER11 = 23,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER12 = 24,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER13 = 25,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER14 = 26,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER15 = 27,
    D3D11_MESSAGE_ID_CORRUPTED_MULTITHREADING = 28,
    D3D11_MESSAGE_ID_MESSAGE_REPORTING_OUTOFMEMORY = 29,
    D3D11_MESSAGE_ID_IASETINPUTLAYOUT_UNBINDDELETINGOBJECT = 30,
    D3D11_MESSAGE_ID_IASETVERTEXBUFFERS_UNBINDDELETINGOBJECT = 31,
    D3D11_MESSAGE_ID_IASETINDEXBUFFER_UNBINDDELETINGOBJECT = 32,
    D3D11_MESSAGE_ID_VSSETSHADER_UNBINDDELETINGOBJECT = 33,
    D3D11_MESSAGE_ID_VSSETSHADERRESOURCES_UNBINDDELETINGOBJECT = 34,
    D3D11_MESSAGE_ID_VSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT = 35,
    D3D11_MESSAGE_ID_VSSETSAMPLERS_UNBINDDELETINGOBJECT = 36,
    D3D11_MESSAGE_ID_GSSETSHADER_UNBINDDELETINGOBJECT = 37,
    D3D11_MESSAGE_ID_GSSETSHADERRESOURCES_UNBINDDELETINGOBJECT = 38,
    D3D11_MESSAGE_ID_GSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT = 39,
    D3D11_MESSAGE_ID_GSSETSAMPLERS_UNBINDDELETINGOBJECT = 40,
    D3D11_MESSAGE_ID_SOSETTARGETS_UNBINDDELETINGOBJECT = 41,
    D3D11_MESSAGE_ID_PSSETSHADER_UNBINDDELETINGOBJECT = 42,
    D3D11_MESSAGE_ID_PSSETSHADERRESOURCES_UNBINDDELETINGOBJECT = 43,
    D3D11_MESSAGE_ID_PSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT = 44,
    D3D11_MESSAGE_ID_PSSETSAMPLERS_UNBINDDELETINGOBJECT = 45,
    D3D11_MESSAGE_ID_RSSETSTATE_UNBINDDELETINGOBJECT = 46,
    D3D11_MESSAGE_ID_OMSETBLENDSTATE_UNBINDDELETINGOBJECT = 47,
    D3D11_MESSAGE_ID_OMSETDEPTHSTENCILSTATE_UNBINDDELETINGOBJECT = 48,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_UNBINDDELETINGOBJECT = 49,
    D3D11_MESSAGE_ID_SETPREDICATION_UNBINDDELETINGOBJECT = 50,
    D3D11_MESSAGE_ID_GETPRIVATEDATA_MOREDATA = 51,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_INVALIDFREEDATA = 52,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_INVALIDIUNKNOWN = 53,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_INVALIDFLAGS = 54,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_CHANGINGPARAMS = 55,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_OUTOFMEMORY = 56,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDFORMAT = 57,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDSAMPLES = 58,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDUSAGE = 59,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDBINDFLAGS = 60,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDCPUACCESSFLAGS = 61,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDMISCFLAGS = 62,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDCPUACCESSFLAGS = 63,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDBINDFLAGS = 64,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDINITIALDATA = 65,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDDIMENSIONS = 66,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDMIPLEVELS = 67,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDMISCFLAGS = 68,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDARG_RETURN = 69,
    D3D11_MESSAGE_ID_CREATEBUFFER_OUTOFMEMORY_RETURN = 70,
    D3D11_MESSAGE_ID_CREATEBUFFER_NULLDESC = 71,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDCONSTANTBUFFERBINDINGS = 72,
    D3D11_MESSAGE_ID_CREATEBUFFER_LARGEALLOCATION = 73,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDFORMAT = 74,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNSUPPORTEDFORMAT = 75,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDSAMPLES = 76,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDUSAGE = 77,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDBINDFLAGS = 78,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDCPUACCESSFLAGS = 79,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDMISCFLAGS = 80,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDCPUACCESSFLAGS = 81,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDBINDFLAGS = 82,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDINITIALDATA = 83,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDDIMENSIONS = 84,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDMIPLEVELS = 85,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDMISCFLAGS = 86,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDARG_RETURN = 87,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_OUTOFMEMORY_RETURN = 88,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_NULLDESC = 89,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_LARGEALLOCATION = 90,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDFORMAT = 91,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNSUPPORTEDFORMAT = 92,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDSAMPLES = 93,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDUSAGE = 94,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDBINDFLAGS = 95,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDCPUACCESSFLAGS = 96,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDMISCFLAGS = 97,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDCPUACCESSFLAGS = 98,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDBINDFLAGS = 99,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDINITIALDATA = 100,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDDIMENSIONS = 101,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDMIPLEVELS = 102,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDMISCFLAGS = 103,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDARG_RETURN = 104,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_OUTOFMEMORY_RETURN = 105,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_NULLDESC = 106,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_LARGEALLOCATION = 107,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDFORMAT = 108,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNSUPPORTEDFORMAT = 109,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDSAMPLES = 110,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDUSAGE = 111,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDBINDFLAGS = 112,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDCPUACCESSFLAGS = 113,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDMISCFLAGS = 114,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDCPUACCESSFLAGS = 115,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDBINDFLAGS = 116,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDINITIALDATA = 117,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDDIMENSIONS = 118,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDMIPLEVELS = 119,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDMISCFLAGS = 120,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDARG_RETURN = 121,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_OUTOFMEMORY_RETURN = 122,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_NULLDESC = 123,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_LARGEALLOCATION = 124,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_UNRECOGNIZEDFORMAT = 125,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDESC = 126,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDFORMAT = 127,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDIMENSIONS = 128,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDRESOURCE = 129,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_TOOMANYOBJECTS = 130,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDARG_RETURN = 131,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_OUTOFMEMORY_RETURN = 132,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_UNRECOGNIZEDFORMAT = 133,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_UNSUPPORTEDFORMAT = 134,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDESC = 135,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDFORMAT = 136,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDIMENSIONS = 137,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDRESOURCE = 138,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_TOOMANYOBJECTS = 139,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDARG_RETURN = 140,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_OUTOFMEMORY_RETURN = 141,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_UNRECOGNIZEDFORMAT = 142,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDESC = 143,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFORMAT = 144,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDIMENSIONS = 145,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDRESOURCE = 146,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_TOOMANYOBJECTS = 147,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDARG_RETURN = 148,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_OUTOFMEMORY_RETURN = 149,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_OUTOFMEMORY = 150,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_TOOMANYELEMENTS = 151,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDFORMAT = 152,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INCOMPATIBLEFORMAT = 153,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOT = 154,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDINPUTSLOTCLASS = 155,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_STEPRATESLOTCLASSMISMATCH = 156,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOTCLASSCHANGE = 157,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSTEPRATECHANGE = 158,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDALIGNMENT = 159,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_DUPLICATESEMANTIC = 160,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_UNPARSEABLEINPUTSIGNATURE = 161,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_NULLSEMANTIC = 162,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_MISSINGELEMENT = 163,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_NULLDESC = 164,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_OUTOFMEMORY = 165,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERBYTECODE = 166,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERTYPE = 167,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_OUTOFMEMORY = 168,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERBYTECODE = 169,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERTYPE = 170,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTOFMEMORY = 171,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERBYTECODE = 172,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE = 173,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMENTRIES = 174,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSTREAMSTRIDEUNUSED = 175,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDDECL = 176,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_EXPECTEDDECL = 177,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSLOT0EXPECTED = 178,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSLOT = 179,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_ONLYONEELEMENTPERSLOT = 180,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDCOMPONENTCOUNT = 181,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTARTCOMPONENTANDCOMPONENTCOUNT = 182,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDGAPDEFINITION = 183,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_REPEATEDOUTPUT = 184,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSTREAMSTRIDE = 185,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGSEMANTIC = 186,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MASKMISMATCH = 187,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_CANTHAVEONLYGAPS = 188,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DECLTOOCOMPLEX = 189,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGOUTPUTSIGNATURE = 190,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_OUTOFMEMORY = 191,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERBYTECODE = 192,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERTYPE = 193,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFILLMODE = 194,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDCULLMODE = 195,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDDEPTHBIASCLAMP = 196,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDSLOPESCALEDDEPTHBIAS = 197,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_TOOMANYOBJECTS = 198,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_NULLDESC = 199,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHWRITEMASK = 200,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHFUNC = 201,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFAILOP = 202,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILZFAILOP = 203,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILPASSOP = 204,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFUNC = 205,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFAILOP = 206,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILZFAILOP = 207,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILPASSOP = 208,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFUNC = 209,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_TOOMANYOBJECTS = 210,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_NULLDESC = 211,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLEND = 212,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLEND = 213,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOP = 214,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLENDALPHA = 215,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLENDALPHA = 216,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOPALPHA = 217,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDRENDERTARGETWRITEMASK = 218,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_TOOMANYOBJECTS = 219,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NULLDESC = 220,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDFILTER = 221,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDADDRESSU = 222,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDADDRESSV = 223,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDADDRESSW = 224,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMIPLODBIAS = 225,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMAXANISOTROPY = 226,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDCOMPARISONFUNC = 227,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMINLOD = 228,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMAXLOD = 229,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_TOOMANYOBJECTS = 230,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_NULLDESC = 231,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_INVALIDQUERY = 232,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_INVALIDMISCFLAGS = 233,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_UNEXPECTEDMISCFLAG = 234,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_NULLDESC = 235,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_TOPOLOGY_UNRECOGNIZED = 236,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_TOPOLOGY_UNDEFINED = 237,
    D3D11_MESSAGE_ID_IASETVERTEXBUFFERS_INVALIDBUFFER = 238,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_OFFSET_TOO_LARGE = 239,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_BUFFERS_EMPTY = 240,
    D3D11_MESSAGE_ID_IASETINDEXBUFFER_INVALIDBUFFER = 241,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_FORMAT_INVALID = 242,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_OFFSET_TOO_LARGE = 243,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_OFFSET_UNALIGNED = 244,
    D3D11_MESSAGE_ID_DEVICE_VSSETSHADERRESOURCES_VIEWS_EMPTY = 245,
    D3D11_MESSAGE_ID_VSSETCONSTANTBUFFERS_INVALIDBUFFER = 246,
    D3D11_MESSAGE_ID_DEVICE_VSSETCONSTANTBUFFERS_BUFFERS_EMPTY = 247,
    D3D11_MESSAGE_ID_DEVICE_VSSETSAMPLERS_SAMPLERS_EMPTY = 248,
    D3D11_MESSAGE_ID_DEVICE_GSSETSHADERRESOURCES_VIEWS_EMPTY = 249,
    D3D11_MESSAGE_ID_GSSETCONSTANTBUFFERS_INVALIDBUFFER = 250,
    D3D11_MESSAGE_ID_DEVICE_GSSETCONSTANTBUFFERS_BUFFERS_EMPTY = 251,
    D3D11_MESSAGE_ID_DEVICE_GSSETSAMPLERS_SAMPLERS_EMPTY = 252,
    D3D11_MESSAGE_ID_SOSETTARGETS_INVALIDBUFFER = 253,
    D3D11_MESSAGE_ID_DEVICE_SOSETTARGETS_OFFSET_UNALIGNED = 254,
    D3D11_MESSAGE_ID_DEVICE_PSSETSHADERRESOURCES_VIEWS_EMPTY = 255,
    D3D11_MESSAGE_ID_PSSETCONSTANTBUFFERS_INVALIDBUFFER = 256,
    D3D11_MESSAGE_ID_DEVICE_PSSETCONSTANTBUFFERS_BUFFERS_EMPTY = 257,
    D3D11_MESSAGE_ID_DEVICE_PSSETSAMPLERS_SAMPLERS_EMPTY = 258,
    D3D11_MESSAGE_ID_DEVICE_RSSETVIEWPORTS_INVALIDVIEWPORT = 259,
    D3D11_MESSAGE_ID_DEVICE_RSSETSCISSORRECTS_INVALIDSCISSOR = 260,
    D3D11_MESSAGE_ID_CLEARRENDERTARGETVIEW_DENORMFLUSH = 261,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_DENORMFLUSH = 262,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_INVALID = 263,
    D3D11_MESSAGE_ID_DEVICE_IAGETVERTEXBUFFERS_BUFFERS_EMPTY = 264,
    D3D11_MESSAGE_ID_DEVICE_VSGETSHADERRESOURCES_VIEWS_EMPTY = 265,
    D3D11_MESSAGE_ID_DEVICE_VSGETCONSTANTBUFFERS_BUFFERS_EMPTY = 266,
    D3D11_MESSAGE_ID_DEVICE_VSGETSAMPLERS_SAMPLERS_EMPTY = 267,
    D3D11_MESSAGE_ID_DEVICE_GSGETSHADERRESOURCES_VIEWS_EMPTY = 268,
    D3D11_MESSAGE_ID_DEVICE_GSGETCONSTANTBUFFERS_BUFFERS_EMPTY = 269,
    D3D11_MESSAGE_ID_DEVICE_GSGETSAMPLERS_SAMPLERS_EMPTY = 270,
    D3D11_MESSAGE_ID_DEVICE_SOGETTARGETS_BUFFERS_EMPTY = 271,
    D3D11_MESSAGE_ID_DEVICE_PSGETSHADERRESOURCES_VIEWS_EMPTY = 272,
    D3D11_MESSAGE_ID_DEVICE_PSGETCONSTANTBUFFERS_BUFFERS_EMPTY = 273,
    D3D11_MESSAGE_ID_DEVICE_PSGETSAMPLERS_SAMPLERS_EMPTY = 274,
    D3D11_MESSAGE_ID_DEVICE_RSGETVIEWPORTS_VIEWPORTS_EMPTY = 275,
    D3D11_MESSAGE_ID_DEVICE_RSGETSCISSORRECTS_RECTS_EMPTY = 276,
    D3D11_MESSAGE_ID_DEVICE_GENERATEMIPS_RESOURCE_INVALID = 277,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDDESTINATIONSUBRESOURCE = 278,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCESUBRESOURCE = 279,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCEBOX = 280,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCE = 281,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDDESTINATIONSTATE = 282,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCESTATE = 283,
    D3D11_MESSAGE_ID_COPYRESOURCE_INVALIDSOURCE = 284,
    D3D11_MESSAGE_ID_COPYRESOURCE_INVALIDDESTINATIONSTATE = 285,
    D3D11_MESSAGE_ID_COPYRESOURCE_INVALIDSOURCESTATE = 286,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_INVALIDDESTINATIONSUBRESOURCE = 287,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_INVALIDDESTINATIONBOX = 288,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_INVALIDDESTINATIONSTATE = 289,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_DESTINATION_INVALID = 290,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_DESTINATION_SUBRESOURCE_INVALID = 291,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_SOURCE_INVALID = 292,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_SOURCE_SUBRESOURCE_INVALID = 293,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_FORMAT_INVALID = 294,
    D3D11_MESSAGE_ID_BUFFER_MAP_INVALIDMAPTYPE = 295,
    D3D11_MESSAGE_ID_BUFFER_MAP_INVALIDFLAGS = 296,
    D3D11_MESSAGE_ID_BUFFER_MAP_ALREADYMAPPED = 297,
    D3D11_MESSAGE_ID_BUFFER_MAP_DEVICEREMOVED_RETURN = 298,
    D3D11_MESSAGE_ID_BUFFER_UNMAP_NOTMAPPED = 299,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_INVALIDMAPTYPE = 300,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_INVALIDSUBRESOURCE = 301,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_INVALIDFLAGS = 302,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_ALREADYMAPPED = 303,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_DEVICEREMOVED_RETURN = 304,
    D3D11_MESSAGE_ID_TEXTURE1D_UNMAP_INVALIDSUBRESOURCE = 305,
    D3D11_MESSAGE_ID_TEXTURE1D_UNMAP_NOTMAPPED = 306,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_INVALIDMAPTYPE = 307,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_INVALIDSUBRESOURCE = 308,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_INVALIDFLAGS = 309,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_ALREADYMAPPED = 310,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_DEVICEREMOVED_RETURN = 311,
    D3D11_MESSAGE_ID_TEXTURE2D_UNMAP_INVALIDSUBRESOURCE = 312,
    D3D11_MESSAGE_ID_TEXTURE2D_UNMAP_NOTMAPPED = 313,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_INVALIDMAPTYPE = 314,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_INVALIDSUBRESOURCE = 315,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_INVALIDFLAGS = 316,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_ALREADYMAPPED = 317,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_DEVICEREMOVED_RETURN = 318,
    D3D11_MESSAGE_ID_TEXTURE3D_UNMAP_INVALIDSUBRESOURCE = 319,
    D3D11_MESSAGE_ID_TEXTURE3D_UNMAP_NOTMAPPED = 320,
    D3D11_MESSAGE_ID_CHECKFORMATSUPPORT_FORMAT_DEPRECATED = 321,
    D3D11_MESSAGE_ID_CHECKMULTISAMPLEQUALITYLEVELS_FORMAT_DEPRECATED = 322,
    D3D11_MESSAGE_ID_SETEXCEPTIONMODE_UNRECOGNIZEDFLAGS = 323,
    D3D11_MESSAGE_ID_SETEXCEPTIONMODE_INVALIDARG_RETURN = 324,
    D3D11_MESSAGE_ID_SETEXCEPTIONMODE_DEVICEREMOVED_RETURN = 325,
    D3D11_MESSAGE_ID_REF_SIMULATING_INFINITELY_FAST_HARDWARE = 326,
    D3D11_MESSAGE_ID_REF_THREADING_MODE = 327,
    D3D11_MESSAGE_ID_REF_UMDRIVER_EXCEPTION = 328,
    D3D11_MESSAGE_ID_REF_KMDRIVER_EXCEPTION = 329,
    D3D11_MESSAGE_ID_REF_HARDWARE_EXCEPTION = 330,
    D3D11_MESSAGE_ID_REF_ACCESSING_INDEXABLE_TEMP_OUT_OF_RANGE = 331,
    D3D11_MESSAGE_ID_REF_PROBLEM_PARSING_SHADER = 332,
    D3D11_MESSAGE_ID_REF_OUT_OF_MEMORY = 333,
    D3D11_MESSAGE_ID_REF_INFO = 334,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEXPOS_OVERFLOW = 335,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDEXED_INDEXPOS_OVERFLOW = 336,
    D3D11_MESSAGE_ID_DEVICE_DRAWINSTANCED_VERTEXPOS_OVERFLOW = 337,
    D3D11_MESSAGE_ID_DEVICE_DRAWINSTANCED_INSTANCEPOS_OVERFLOW = 338,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDEXEDINSTANCED_INSTANCEPOS_OVERFLOW = 339,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDEXEDINSTANCED_INDEXPOS_OVERFLOW = 340,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_SHADER_NOT_SET = 341,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_SEMANTICNAME_NOT_FOUND = 342,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_REGISTERINDEX = 343,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_COMPONENTTYPE = 344,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_REGISTERMASK = 345,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_SYSTEMVALUE = 346,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_NEVERWRITTEN_ALWAYSREADS = 347,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_BUFFER_NOT_SET = 348,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INPUTLAYOUT_NOT_SET = 349,
    D3D11_MESSAGE_ID_DEVICE_DRAW_CONSTANT_BUFFER_NOT_SET = 350,
    D3D11_MESSAGE_ID_DEVICE_DRAW_CONSTANT_BUFFER_TOO_SMALL = 351,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SAMPLER_NOT_SET = 352,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SHADERRESOURCEVIEW_NOT_SET = 353,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VIEW_DIMENSION_MISMATCH = 354,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_BUFFER_STRIDE_TOO_SMALL = 355,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_BUFFER_TOO_SMALL = 356,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_BUFFER_NOT_SET = 357,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_BUFFER_FORMAT_INVALID = 358,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_BUFFER_TOO_SMALL = 359,
    D3D11_MESSAGE_ID_DEVICE_DRAW_GS_INPUT_PRIMITIVE_MISMATCH = 360,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_RETURN_TYPE_MISMATCH = 361,
    D3D11_MESSAGE_ID_DEVICE_DRAW_POSITION_NOT_PRESENT = 362,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OUTPUT_STREAM_NOT_SET = 363,
    D3D11_MESSAGE_ID_DEVICE_DRAW_BOUND_RESOURCE_MAPPED = 364,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INVALID_PRIMITIVETOPOLOGY = 365,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_OFFSET_UNALIGNED = 366,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_STRIDE_UNALIGNED = 367,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_OFFSET_UNALIGNED = 368,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OUTPUT_STREAM_OFFSET_UNALIGNED = 369,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_LD_UNSUPPORTED = 370,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_SAMPLE_UNSUPPORTED = 371,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_SAMPLE_C_UNSUPPORTED = 372,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_MULTISAMPLE_UNSUPPORTED = 373,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SO_TARGETS_BOUND_WITHOUT_SOURCE = 374,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SO_STRIDE_LARGER_THAN_BUFFER = 375,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OM_RENDER_TARGET_DOES_NOT_SUPPORT_BLENDING = 376,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OM_DUAL_SOURCE_BLENDING_CAN_ONLY_HAVE_RENDER_TARGET_0 = 377,
    D3D11_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_AT_FAULT = 378,
    D3D11_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_POSSIBLY_AT_FAULT = 379,
    D3D11_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_NOT_AT_FAULT = 380,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_INVALIDARG_RETURN = 381,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_OUTOFMEMORY_RETURN = 382,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_BADINTERFACE_RETURN = 383,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VIEWPORT_NOT_SET = 384,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_TRAILING_DIGIT_IN_SEMANTIC = 385,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_TRAILING_DIGIT_IN_SEMANTIC = 386,
    D3D11_MESSAGE_ID_DEVICE_RSSETVIEWPORTS_DENORMFLUSH = 387,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_INVALIDVIEW = 388,
    D3D11_MESSAGE_ID_DEVICE_SETTEXTFILTERSIZE_INVALIDDIMENSIONS = 389,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SAMPLER_MISMATCH = 390,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_TYPE_MISMATCH = 391,
    D3D11_MESSAGE_ID_BLENDSTATE_GETDESC_LEGACY = 392,
    D3D11_MESSAGE_ID_SHADERRESOURCEVIEW_GETDESC_LEGACY = 393,
    D3D11_MESSAGE_ID_CREATEQUERY_OUTOFMEMORY_RETURN = 394,
    D3D11_MESSAGE_ID_CREATEPREDICATE_OUTOFMEMORY_RETURN = 395,
    D3D11_MESSAGE_ID_CREATECOUNTER_OUTOFRANGE_COUNTER = 396,
    D3D11_MESSAGE_ID_CREATECOUNTER_SIMULTANEOUS_ACTIVE_COUNTERS_EXHAUSTED = 397,
    D3D11_MESSAGE_ID_CREATECOUNTER_UNSUPPORTED_WELLKNOWN_COUNTER = 398,
    D3D11_MESSAGE_ID_CREATECOUNTER_OUTOFMEMORY_RETURN = 399,
    D3D11_MESSAGE_ID_CREATECOUNTER_NONEXCLUSIVE_RETURN = 400,
    D3D11_MESSAGE_ID_CREATECOUNTER_NULLDESC = 401,
    D3D11_MESSAGE_ID_CHECKCOUNTER_OUTOFRANGE_COUNTER = 402,
    D3D11_MESSAGE_ID_CHECKCOUNTER_UNSUPPORTED_WELLKNOWN_COUNTER = 403,
    D3D11_MESSAGE_ID_SETPREDICATION_INVALID_PREDICATE_STATE = 404,
    D3D11_MESSAGE_ID_QUERY_BEGIN_UNSUPPORTED = 405,
    D3D11_MESSAGE_ID_PREDICATE_BEGIN_DURING_PREDICATION = 406,
    D3D11_MESSAGE_ID_QUERY_BEGIN_DUPLICATE = 407,
    D3D11_MESSAGE_ID_QUERY_BEGIN_ABANDONING_PREVIOUS_RESULTS = 408,
    D3D11_MESSAGE_ID_PREDICATE_END_DURING_PREDICATION = 409,
    D3D11_MESSAGE_ID_QUERY_END_ABANDONING_PREVIOUS_RESULTS = 410,
    D3D11_MESSAGE_ID_QUERY_END_WITHOUT_BEGIN = 411,
    D3D11_MESSAGE_ID_QUERY_GETDATA_INVALID_DATASIZE = 412,
    D3D11_MESSAGE_ID_QUERY_GETDATA_INVALID_FLAGS = 413,
    D3D11_MESSAGE_ID_QUERY_GETDATA_INVALID_CALL = 414,
    D3D11_MESSAGE_ID_DEVICE_DRAW_PS_OUTPUT_TYPE_MISMATCH = 415,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_GATHER_UNSUPPORTED = 416,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INVALID_USE_OF_CENTER_MULTISAMPLE_PATTERN = 417,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_STRIDE_TOO_LARGE = 418,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_INVALIDRANGE = 419,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_EMPTY_LAYOUT = 420,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_SAMPLE_COUNT_MISMATCH = 421,
    D3D11_MESSAGE_ID_LIVE_OBJECT_SUMMARY = 422,
    D3D11_MESSAGE_ID_LIVE_BUFFER = 423,
    D3D11_MESSAGE_ID_LIVE_TEXTURE1D = 424,
    D3D11_MESSAGE_ID_LIVE_TEXTURE2D = 425,
    D3D11_MESSAGE_ID_LIVE_TEXTURE3D = 426,
    D3D11_MESSAGE_ID_LIVE_SHADERRESOURCEVIEW = 427,
    D3D11_MESSAGE_ID_LIVE_RENDERTARGETVIEW = 428,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILVIEW = 429,
    D3D11_MESSAGE_ID_LIVE_VERTEXSHADER = 430,
    D3D11_MESSAGE_ID_LIVE_GEOMETRYSHADER = 431,
    D3D11_MESSAGE_ID_LIVE_PIXELSHADER = 432,
    D3D11_MESSAGE_ID_LIVE_INPUTLAYOUT = 433,
    D3D11_MESSAGE_ID_LIVE_SAMPLER = 434,
    D3D11_MESSAGE_ID_LIVE_BLENDSTATE = 435,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILSTATE = 436,
    D3D11_MESSAGE_ID_LIVE_RASTERIZERSTATE = 437,
    D3D11_MESSAGE_ID_LIVE_QUERY = 438,
    D3D11_MESSAGE_ID_LIVE_PREDICATE = 439,
    D3D11_MESSAGE_ID_LIVE_COUNTER = 440,
    D3D11_MESSAGE_ID_LIVE_DEVICE = 441,
    D3D11_MESSAGE_ID_LIVE_SWAPCHAIN = 442,
    D3D11_MESSAGE_ID_D3D10_MESSAGES_END = 443,
    D3D11_MESSAGE_ID_D3D10L9_MESSAGES_START = 0x100000,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_STENCIL_NO_TWO_SIDED = 0x100001,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_DepthBiasClamp_NOT_SUPPORTED = 0x100002,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_NO_COMPARISON_SUPPORT = 0x100003,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_EXCESSIVE_ANISOTROPY = 0x100004,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_BORDER_OUT_OF_RANGE = 0x100005,
    D3D11_MESSAGE_ID_VSSETSAMPLERS_NOT_SUPPORTED = 0x100006,
    D3D11_MESSAGE_ID_VSSETSAMPLERS_TOO_MANY_SAMPLERS = 0x100007,
    D3D11_MESSAGE_ID_PSSETSAMPLERS_TOO_MANY_SAMPLERS = 0x100008,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_ARRAYS = 0x100009,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_VB_AND_IB_BIND = 0x10000a,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_TEXTURE_1D = 0x10000b,
    D3D11_MESSAGE_ID_CREATERESOURCE_DIMENSION_OUT_OF_RANGE = 0x10000c,
    D3D11_MESSAGE_ID_CREATERESOURCE_NOT_BINDABLE_AS_SHADER_RESOURCE = 0x10000d,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_TOO_MANY_RENDER_TARGETS = 0x10000e,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_NO_DIFFERING_BIT_DEPTHS = 0x10000f,
    D3D11_MESSAGE_ID_IASETVERTEXBUFFERS_BAD_BUFFER_INDEX = 0x100010,
    D3D11_MESSAGE_ID_DEVICE_RSSETVIEWPORTS_TOO_MANY_VIEWPORTS = 0x100011,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_ADJACENCY_UNSUPPORTED = 0x100012,
    D3D11_MESSAGE_ID_DEVICE_RSSETSCISSORRECTS_TOO_MANY_SCISSORS = 0x100013,
    D3D11_MESSAGE_ID_COPYRESOURCE_ONLY_TEXTURE_2D_WITHIN_GPU_MEMORY = 0x100014,
    D3D11_MESSAGE_ID_COPYRESOURCE_NO_TEXTURE_3D_READBACK = 0x100015,
    D3D11_MESSAGE_ID_COPYRESOURCE_NO_TEXTURE_ONLY_READBACK = 0x100016,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_UNSUPPORTED_FORMAT = 0x100017,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_ALPHA_TO_COVERAGE = 0x100018,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_DepthClipEnable_MUST_BE_TRUE = 0x100019,
    D3D11_MESSAGE_ID_DRAWINDEXED_STARTINDEXLOCATION_MUST_BE_POSITIVE = 0x10001a,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_MUST_USE_LOWEST_LOD = 0x10001b,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_MINLOD_MUST_NOT_BE_FRACTIONAL = 0x10001c,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_MAXLOD_MUST_BE_FLT_MAX = 0x10001d,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_FIRSTARRAYSLICE_MUST_BE_ZERO = 0x10001e,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_CUBES_MUST_HAVE_6_SIDES = 0x10001f,
    D3D11_MESSAGE_ID_CREATERESOURCE_NOT_BINDABLE_AS_RENDER_TARGET = 0x100020,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_DWORD_INDEX_BUFFER = 0x100021,
    D3D11_MESSAGE_ID_CREATERESOURCE_MSAA_PRECLUDES_SHADER_RESOURCE = 0x100022,
    D3D11_MESSAGE_ID_CREATERESOURCE_PRESENTATION_PRECLUDES_SHADER_RESOURCE = 0x100023,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_INDEPENDENT_BLEND_ENABLE = 0x100024,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_INDEPENDENT_WRITE_MASKS = 0x100025,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_STREAM_OUT = 0x100026,
    D3D11_MESSAGE_ID_CREATERESOURCE_ONLY_VB_IB_FOR_BUFFERS = 0x100027,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_AUTOGEN_FOR_VOLUMES = 0x100028,
    D3D11_MESSAGE_ID_CREATERESOURCE_DXGI_FORMAT_R8G8B8A8_CANNOT_BE_SHARED = 0x100029,
    D3D11_MESSAGE_ID_VSSHADERRESOURCES_NOT_SUPPORTED = 0x10002a,
    D3D11_MESSAGE_ID_GEOMETRY_SHADER_NOT_SUPPORTED = 0x10002b,
    D3D11_MESSAGE_ID_STREAM_OUT_NOT_SUPPORTED = 0x10002c,
    D3D11_MESSAGE_ID_TEXT_FILTER_NOT_SUPPORTED = 0x10002d,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_SEPARATE_ALPHA_BLEND = 0x10002e,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_MRT_BLEND = 0x10002f,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_OPERATION_NOT_SUPPORTED = 0x100030,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_NO_MIRRORONCE = 0x100031,
    D3D11_MESSAGE_ID_DRAWINSTANCED_NOT_SUPPORTED = 0x100032,
    D3D11_MESSAGE_ID_DRAWINDEXEDINSTANCED_NOT_SUPPORTED_BELOW_9_3 = 0x100033,
    D3D11_MESSAGE_ID_DRAWINDEXED_POINTLIST_UNSUPPORTED = 0x100034,
    D3D11_MESSAGE_ID_SETBLENDSTATE_SAMPLE_MASK_CANNOT_BE_ZERO = 0x100035,
    D3D11_MESSAGE_ID_CREATERESOURCE_DIMENSION_EXCEEDS_FEATURE_LEVEL_DEFINITION = 0x100036,
    D3D11_MESSAGE_ID_CREATERESOURCE_ONLY_SINGLE_MIP_LEVEL_DEPTH_STENCIL_SUPPORTED = 0x100037,
    D3D11_MESSAGE_ID_DEVICE_RSSETSCISSORRECTS_NEGATIVESCISSOR = 0x100038,
    D3D11_MESSAGE_ID_SLOT_ZERO_MUST_BE_D3D10_INPUT_PER_VERTEX_DATA = 0x100039,
    D3D11_MESSAGE_ID_CREATERESOURCE_NON_POW_2_MIPMAP = 0x10003a,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_BORDER_NOT_SUPPORTED = 0x10003b,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_NO_SRGB_MRT = 0x10003c,
    D3D11_MESSAGE_ID_COPYRESOURCE_NO_3D_MISMATCHED_UPDATES = 0x10003d,
    D3D11_MESSAGE_ID_D3D10L9_MESSAGES_END = 0x10003e,
    D3D11_MESSAGE_ID_D3D11_MESSAGES_START = 0x200000,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFLAGS = 0x200001,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDCLASSLINKAGE = 0x200002,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDCLASSLINKAGE = 0x200003,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMSTREAMS = 0x200004,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAMTORASTERIZER = 0x200005,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDSTREAMS = 0x200006,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDCLASSLINKAGE = 0x200007,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_INVALIDCLASSLINKAGE = 0x200008,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_INVALID_COMMANDLISTFLAGS = 0x200009,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_SINGLETHREADED = 0x20000a,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_INVALIDARG_RETURN = 0x20000b,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_INVALID_CALL_RETURN = 0x20000c,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_OUTOFMEMORY_RETURN = 0x20000d,
    D3D11_MESSAGE_ID_FINISHDISPLAYLIST_ONIMMEDIATECONTEXT = 0x20000e,
    D3D11_MESSAGE_ID_FINISHDISPLAYLIST_OUTOFMEMORY_RETURN = 0x20000f,
    D3D11_MESSAGE_ID_FINISHDISPLAYLIST_INVALID_CALL_RETURN = 0x200010,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAM = 0x200011,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDENTRIES = 0x200012,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDSTRIDES = 0x200013,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMSTRIDES = 0x200014,
    D3D11_MESSAGE_ID_DEVICE_HSSETSHADERRESOURCES_HAZARD = 0x200015,
    D3D11_MESSAGE_ID_DEVICE_HSSETCONSTANTBUFFERS_HAZARD = 0x200016,
    D3D11_MESSAGE_ID_HSSETSHADERRESOURCES_UNBINDDELETINGOBJECT = 0x200017,
    D3D11_MESSAGE_ID_HSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT = 0x200018,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDCALL = 0x200019,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_OUTOFMEMORY = 0x20001a,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERBYTECODE = 0x20001b,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERTYPE = 0x20001c,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDCLASSLINKAGE = 0x20001d,
    D3D11_MESSAGE_ID_DEVICE_HSSETSHADERRESOURCES_VIEWS_EMPTY = 0x20001e,
    D3D11_MESSAGE_ID_HSSETCONSTANTBUFFERS_INVALIDBUFFER = 0x20001f,
    D3D11_MESSAGE_ID_DEVICE_HSSETCONSTANTBUFFERS_BUFFERS_EMPTY = 0x200020,
    D3D11_MESSAGE_ID_DEVICE_HSSETSAMPLERS_SAMPLERS_EMPTY = 0x200021,
    D3D11_MESSAGE_ID_DEVICE_HSGETSHADERRESOURCES_VIEWS_EMPTY = 0x200022,
    D3D11_MESSAGE_ID_DEVICE_HSGETCONSTANTBUFFERS_BUFFERS_EMPTY = 0x200023,
    D3D11_MESSAGE_ID_DEVICE_HSGETSAMPLERS_SAMPLERS_EMPTY = 0x200024,
    D3D11_MESSAGE_ID_DEVICE_DSSETSHADERRESOURCES_HAZARD = 0x200025,
    D3D11_MESSAGE_ID_DEVICE_DSSETCONSTANTBUFFERS_HAZARD = 0x200026,
    D3D11_MESSAGE_ID_DSSETSHADERRESOURCES_UNBINDDELETINGOBJECT = 0x200027,
    D3D11_MESSAGE_ID_DSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT = 0x200028,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDCALL = 0x200029,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_OUTOFMEMORY = 0x20002a,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERBYTECODE = 0x20002b,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERTYPE = 0x20002c,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDCLASSLINKAGE = 0x20002d,
    D3D11_MESSAGE_ID_DEVICE_DSSETSHADERRESOURCES_VIEWS_EMPTY = 0x20002e,
    D3D11_MESSAGE_ID_DSSETCONSTANTBUFFERS_INVALIDBUFFER = 0x20002f,
    D3D11_MESSAGE_ID_DEVICE_DSSETCONSTANTBUFFERS_BUFFERS_EMPTY = 0x200030,
    D3D11_MESSAGE_ID_DEVICE_DSSETSAMPLERS_SAMPLERS_EMPTY = 0x200031,
    D3D11_MESSAGE_ID_DEVICE_DSGETSHADERRESOURCES_VIEWS_EMPTY = 0x200032,
    D3D11_MESSAGE_ID_DEVICE_DSGETCONSTANTBUFFERS_BUFFERS_EMPTY = 0x200033,
    D3D11_MESSAGE_ID_DEVICE_DSGETSAMPLERS_SAMPLERS_EMPTY = 0x200034,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_XOR_DS_MISMATCH = 0x200035,
    D3D11_MESSAGE_ID_DEFERRED_CONTEXT_REMOVAL_PROCESS_AT_FAULT = 0x200036,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDIRECT_INVALID_ARG_BUFFER = 0x200037,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDIRECT_OFFSET_UNALIGNED = 0x200038,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDIRECT_OFFSET_OVERFLOW = 0x200039,
    D3D11_MESSAGE_ID_RESOURCE_MAP_INVALIDMAPTYPE = 0x20003a,
    D3D11_MESSAGE_ID_RESOURCE_MAP_INVALIDSUBRESOURCE = 0x20003b,
    D3D11_MESSAGE_ID_RESOURCE_MAP_INVALIDFLAGS = 0x20003c,
    D3D11_MESSAGE_ID_RESOURCE_MAP_ALREADYMAPPED = 0x20003d,
    D3D11_MESSAGE_ID_RESOURCE_MAP_DEVICEREMOVED_RETURN = 0x20003e,
    D3D11_MESSAGE_ID_RESOURCE_MAP_OUTOFMEMORY_RETURN = 0x20003f,
    D3D11_MESSAGE_ID_RESOURCE_MAP_WITHOUT_INITIAL_DISCARD = 0x200040,
    D3D11_MESSAGE_ID_RESOURCE_UNMAP_INVALIDSUBRESOURCE = 0x200041,
    D3D11_MESSAGE_ID_RESOURCE_UNMAP_NOTMAPPED = 0x200042,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RASTERIZING_CONTROL_POINTS = 0x200043,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_TOPOLOGY_UNSUPPORTED = 0x200044,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_DS_SIGNATURE_MISMATCH = 0x200045,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HULL_SHADER_INPUT_TOPOLOGY_MISMATCH = 0x200046,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_DS_CONTROL_POINT_COUNT_MISMATCH = 0x200047,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_DS_TESSELLATOR_DOMAIN_MISMATCH = 0x200048,
    D3D11_MESSAGE_ID_CREATE_CONTEXT = 0x200049,
    D3D11_MESSAGE_ID_LIVE_CONTEXT = 0x20004a,
    D3D11_MESSAGE_ID_DESTROY_CONTEXT = 0x20004b,
    D3D11_MESSAGE_ID_CREATE_BUFFER = 0x20004c,
    D3D11_MESSAGE_ID_LIVE_BUFFER_WIN7 = 0x20004d,
    D3D11_MESSAGE_ID_DESTROY_BUFFER = 0x20004e,
    D3D11_MESSAGE_ID_CREATE_TEXTURE1D = 0x20004f,
    D3D11_MESSAGE_ID_LIVE_TEXTURE1D_WIN7 = 0x200050,
    D3D11_MESSAGE_ID_DESTROY_TEXTURE1D = 0x200051,
    D3D11_MESSAGE_ID_CREATE_TEXTURE2D = 0x200052,
    D3D11_MESSAGE_ID_LIVE_TEXTURE2D_WIN7 = 0x200053,
    D3D11_MESSAGE_ID_DESTROY_TEXTURE2D = 0x200054,
    D3D11_MESSAGE_ID_CREATE_TEXTURE3D = 0x200055,
    D3D11_MESSAGE_ID_LIVE_TEXTURE3D_WIN7 = 0x200056,
    D3D11_MESSAGE_ID_DESTROY_TEXTURE3D = 0x200057,
    D3D11_MESSAGE_ID_CREATE_SHADERRESOURCEVIEW = 0x200058,
    D3D11_MESSAGE_ID_LIVE_SHADERRESOURCEVIEW_WIN7 = 0x200059,
    D3D11_MESSAGE_ID_DESTROY_SHADERRESOURCEVIEW = 0x20005a,
    D3D11_MESSAGE_ID_CREATE_RENDERTARGETVIEW = 0x20005b,
    D3D11_MESSAGE_ID_LIVE_RENDERTARGETVIEW_WIN7 = 0x20005c,
    D3D11_MESSAGE_ID_DESTROY_RENDERTARGETVIEW = 0x20005d,
    D3D11_MESSAGE_ID_CREATE_DEPTHSTENCILVIEW = 0x20005e,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILVIEW_WIN7 = 0x20005f,
    D3D11_MESSAGE_ID_DESTROY_DEPTHSTENCILVIEW = 0x200060,
    D3D11_MESSAGE_ID_CREATE_VERTEXSHADER = 0x200061,
    D3D11_MESSAGE_ID_LIVE_VERTEXSHADER_WIN7 = 0x200062,
    D3D11_MESSAGE_ID_DESTROY_VERTEXSHADER = 0x200063,
    D3D11_MESSAGE_ID_CREATE_HULLSHADER = 0x200064,
    D3D11_MESSAGE_ID_LIVE_HULLSHADER = 0x200065,
    D3D11_MESSAGE_ID_DESTROY_HULLSHADER = 0x200066,
    D3D11_MESSAGE_ID_CREATE_DOMAINSHADER = 0x200067,
    D3D11_MESSAGE_ID_LIVE_DOMAINSHADER = 0x200068,
    D3D11_MESSAGE_ID_DESTROY_DOMAINSHADER = 0x200069,
    D3D11_MESSAGE_ID_CREATE_GEOMETRYSHADER = 0x20006a,
    D3D11_MESSAGE_ID_LIVE_GEOMETRYSHADER_WIN7 = 0x20006b,
    D3D11_MESSAGE_ID_DESTROY_GEOMETRYSHADER = 0x20006c,
    D3D11_MESSAGE_ID_CREATE_PIXELSHADER = 0x20006d,
    D3D11_MESSAGE_ID_LIVE_PIXELSHADER_WIN7 = 0x20006e,
    D3D11_MESSAGE_ID_DESTROY_PIXELSHADER = 0x20006f,
    D3D11_MESSAGE_ID_CREATE_INPUTLAYOUT = 0x200070,
    D3D11_MESSAGE_ID_LIVE_INPUTLAYOUT_WIN7 = 0x200071,
    D3D11_MESSAGE_ID_DESTROY_INPUTLAYOUT = 0x200072,
    D3D11_MESSAGE_ID_CREATE_SAMPLER = 0x200073,
    D3D11_MESSAGE_ID_LIVE_SAMPLER_WIN7 = 0x200074,
    D3D11_MESSAGE_ID_DESTROY_SAMPLER = 0x200075,
    D3D11_MESSAGE_ID_CREATE_BLENDSTATE = 0x200076,
    D3D11_MESSAGE_ID_LIVE_BLENDSTATE_WIN7 = 0x200077,
    D3D11_MESSAGE_ID_DESTROY_BLENDSTATE = 0x200078,
    D3D11_MESSAGE_ID_CREATE_DEPTHSTENCILSTATE = 0x200079,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILSTATE_WIN7 = 0x20007a,
    D3D11_MESSAGE_ID_DESTROY_DEPTHSTENCILSTATE = 0x20007b,
    D3D11_MESSAGE_ID_CREATE_RASTERIZERSTATE = 0x20007c,
    D3D11_MESSAGE_ID_LIVE_RASTERIZERSTATE_WIN7 = 0x20007d,
    D3D11_MESSAGE_ID_DESTROY_RASTERIZERSTATE = 0x20007e,
    D3D11_MESSAGE_ID_CREATE_QUERY = 0x20007f,
    D3D11_MESSAGE_ID_LIVE_QUERY_WIN7 = 0x200080,
    D3D11_MESSAGE_ID_DESTROY_QUERY = 0x200081,
    D3D11_MESSAGE_ID_CREATE_PREDICATE = 0x200082,
    D3D11_MESSAGE_ID_LIVE_PREDICATE_WIN7 = 0x200083,
    D3D11_MESSAGE_ID_DESTROY_PREDICATE = 0x200084,
    D3D11_MESSAGE_ID_CREATE_COUNTER = 0x200085,
    D3D11_MESSAGE_ID_DESTROY_COUNTER = 0x200086,
    D3D11_MESSAGE_ID_CREATE_COMMANDLIST = 0x200087,
    D3D11_MESSAGE_ID_LIVE_COMMANDLIST = 0x200088,
    D3D11_MESSAGE_ID_DESTROY_COMMANDLIST = 0x200089,
    D3D11_MESSAGE_ID_CREATE_CLASSINSTANCE = 0x20008a,
    D3D11_MESSAGE_ID_LIVE_CLASSINSTANCE = 0x20008b,
    D3D11_MESSAGE_ID_DESTROY_CLASSINSTANCE = 0x20008c,
    D3D11_MESSAGE_ID_CREATE_CLASSLINKAGE = 0x20008d,
    D3D11_MESSAGE_ID_LIVE_CLASSLINKAGE = 0x20008e,
    D3D11_MESSAGE_ID_DESTROY_CLASSLINKAGE = 0x20008f,
    D3D11_MESSAGE_ID_LIVE_DEVICE_WIN7 = 0x200090,
    D3D11_MESSAGE_ID_LIVE_OBJECT_SUMMARY_WIN7 = 0x200091,
    D3D11_MESSAGE_ID_CREATE_COMPUTESHADER = 0x200092,
    D3D11_MESSAGE_ID_LIVE_COMPUTESHADER = 0x200093,
    D3D11_MESSAGE_ID_DESTROY_COMPUTESHADER = 0x200094,
    D3D11_MESSAGE_ID_CREATE_UNORDEREDACCESSVIEW = 0x200095,
    D3D11_MESSAGE_ID_LIVE_UNORDEREDACCESSVIEW = 0x200096,
    D3D11_MESSAGE_ID_DESTROY_UNORDEREDACCESSVIEW = 0x200097,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INTERFACES_FEATURELEVEL = 0x200098,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INTERFACE_COUNT_MISMATCH = 0x200099,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE = 0x20009a,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE_INDEX = 0x20009b,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE_TYPE = 0x20009c,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE_DATA = 0x20009d,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_UNBOUND_INSTANCE_DATA = 0x20009e,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INSTANCE_DATA_BINDINGS = 0x20009f,
    D3D11_MESSAGE_ID_DEVICE_CREATESHADER_CLASSLINKAGE_FULL = 0x2000a0,
    D3D11_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_UNRECOGNIZED_FEATURE = 0x2000a1,
    D3D11_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_MISMATCHED_DATA_SIZE = 0x2000a2,
    D3D11_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_INVALIDARG_RETURN = 0x2000a3,
    D3D11_MESSAGE_ID_DEVICE_CSSETSHADERRESOURCES_HAZARD = 0x2000a4,
    D3D11_MESSAGE_ID_DEVICE_CSSETCONSTANTBUFFERS_HAZARD = 0x2000a5,
    D3D11_MESSAGE_ID_CSSETSHADERRESOURCES_UNBINDDELETINGOBJECT = 0x2000a6,
    D3D11_MESSAGE_ID_CSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT = 0x2000a7,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDCALL = 0x2000a8,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_OUTOFMEMORY = 0x2000a9,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDSHADERBYTECODE = 0x2000aa,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDSHADERTYPE = 0x2000ab,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDCLASSLINKAGE = 0x2000ac,
    D3D11_MESSAGE_ID_DEVICE_CSSETSHADERRESOURCES_VIEWS_EMPTY = 0x2000ad,
    D3D11_MESSAGE_ID_CSSETCONSTANTBUFFERS_INVALIDBUFFER = 0x2000ae,
    D3D11_MESSAGE_ID_DEVICE_CSSETCONSTANTBUFFERS_BUFFERS_EMPTY = 0x2000af,
    D3D11_MESSAGE_ID_DEVICE_CSSETSAMPLERS_SAMPLERS_EMPTY = 0x2000b0,
    D3D11_MESSAGE_ID_DEVICE_CSGETSHADERRESOURCES_VIEWS_EMPTY = 0x2000b1,
    D3D11_MESSAGE_ID_DEVICE_CSGETCONSTANTBUFFERS_BUFFERS_EMPTY = 0x2000b2,
    D3D11_MESSAGE_ID_DEVICE_CSGETSAMPLERS_SAMPLERS_EMPTY = 0x2000b3,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x2000b4,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x2000b5,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x2000b6,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x2000b7,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEFLOATOPSNOTSUPPORTED = 0x2000b8,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x2000b9,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x2000ba,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDSTRUCTURESTRIDE = 0x2000bb,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDFLAGS = 0x2000bc,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDRESOURCE = 0x2000bd,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDESC = 0x2000be,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFORMAT = 0x2000bf,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDIMENSIONS = 0x2000c0,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_UNRECOGNIZEDFORMAT = 0x2000c1,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_HAZARD = 0x2000c2,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_OVERLAPPING_OLD_SLOTS = 0x2000c3,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_NO_OP = 0x2000c4,
    D3D11_MESSAGE_ID_CSSETUNORDEREDACCESSVIEWS_UNBINDDELETINGOBJECT = 0x2000c5,
    D3D11_MESSAGE_ID_PSSETUNORDEREDACCESSVIEWS_UNBINDDELETINGOBJECT = 0x2000c6,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDARG_RETURN = 0x2000c7,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_OUTOFMEMORY_RETURN = 0x2000c8,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_TOOMANYOBJECTS = 0x2000c9,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_HAZARD = 0x2000ca,
    D3D11_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_DENORMFLUSH = 0x2000cb,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSS_VIEWS_EMPTY = 0x2000cc,
    D3D11_MESSAGE_ID_DEVICE_CSGETUNORDEREDACCESSS_VIEWS_EMPTY = 0x2000cd,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFLAGS = 0x2000ce,
    D3D11_MESSAGE_ID_CREATESHADERRESESOURCEVIEW_TOOMANYOBJECTS = 0x2000cf,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_INVALID_ARG_BUFFER = 0x2000d0,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_OFFSET_UNALIGNED = 0x2000d1,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_OFFSET_OVERFLOW = 0x2000d2,
    D3D11_MESSAGE_ID_DEVICE_SETRESOURCEMINLOD_INVALIDCONTEXT = 0x2000d3,
    D3D11_MESSAGE_ID_DEVICE_SETRESOURCEMINLOD_INVALIDRESOURCE = 0x2000d4,
    D3D11_MESSAGE_ID_DEVICE_SETRESOURCEMINLOD_INVALIDMINLOD = 0x2000d5,
    D3D11_MESSAGE_ID_DEVICE_GETRESOURCEMINLOD_INVALIDCONTEXT = 0x2000d6,
    D3D11_MESSAGE_ID_DEVICE_GETRESOURCEMINLOD_INVALIDRESOURCE = 0x2000d7,
    D3D11_MESSAGE_ID_OMSETDEPTHSTENCIL_UNBINDDELETINGOBJECT = 0x2000d8,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_DEPTH_READONLY = 0x2000d9,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_STENCIL_READONLY = 0x2000da,
    D3D11_MESSAGE_ID_CHECKFEATURESUPPORT_FORMAT_DEPRECATED = 0x2000db,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_RETURN_TYPE_MISMATCH = 0x2000dc,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_NOT_SET = 0x2000dd,
    D3D11_MESSAGE_ID_DEVICE_DRAW_UNORDEREDACCESSVIEW_RENDERTARGETVIEW_OVERLAP = 0x2000de,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_DIMENSION_MISMATCH = 0x2000df,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_APPEND_UNSUPPORTED = 0x2000e0,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMICS_UNSUPPORTED = 0x2000e1,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_STRUCTURE_STRIDE_MISMATCH = 0x2000e2,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_BUFFER_TYPE_MISMATCH = 0x2000e3,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_RAW_UNSUPPORTED = 0x2000e4,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_FORMAT_LD_UNSUPPORTED = 0x2000e5,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_FORMAT_STORE_UNSUPPORTED = 0x2000e6,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_ADD_UNSUPPORTED = 0x2000e7,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_BITWISE_OPS_UNSUPPORTED = 0x2000e8,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_CMPSTORE_CMPEXCHANGE_UNSUPPORTED = 0x2000e9,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_EXCHANGE_UNSUPPORTED = 0x2000ea,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_SIGNED_MINMAX_UNSUPPORTED = 0x2000eb,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_UNSIGNED_MINMAX_UNSUPPORTED = 0x2000ec,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_BOUND_RESOURCE_MAPPED = 0x2000ed,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_THREADGROUPCOUNT_OVERFLOW = 0x2000ee,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_THREADGROUPCOUNT_ZERO = 0x2000ef,
    D3D11_MESSAGE_ID_DEVICE_SHADERRESOURCEVIEW_STRUCTURE_STRIDE_MISMATCH = 0x2000f0,
    D3D11_MESSAGE_ID_DEVICE_SHADERRESOURCEVIEW_BUFFER_TYPE_MISMATCH = 0x2000f1,
    D3D11_MESSAGE_ID_DEVICE_SHADERRESOURCEVIEW_RAW_UNSUPPORTED = 0x2000f2,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_UNSUPPORTED = 0x2000f3,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_UNSUPPORTED = 0x2000f4,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_INVALIDOFFSET = 0x2000f5,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_LARGEOFFSET = 0x2000f6,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_INVALIDDESTINATIONSTATE = 0x2000f7,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_INVALIDSOURCESTATE = 0x2000f8,
    D3D11_MESSAGE_ID_CHECKFORMATSUPPORT_FORMAT_NOT_SUPPORTED = 0x2000f9,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_INVALIDVIEW = 0x2000fa,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_INVALIDOFFSET = 0x2000fb,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_TOOMANYVIEWS = 0x2000fc,
    D3D11_MESSAGE_ID_CLEARUNORDEREDACCESSVIEWFLOAT_INVALIDFORMAT = 0x2000fd,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_COUNTER_UNSUPPORTED = 0x2000fe,
    D3D11_MESSAGE_ID_REF_WARNING = 0x2000ff,
    D3D11_MESSAGE_ID_DEVICE_DRAW_PIXEL_SHADER_WITHOUT_RTV_OR_DSV = 0x200100,
    D3D11_MESSAGE_ID_SHADER_ABORT = 0x200101,
    D3D11_MESSAGE_ID_SHADER_MESSAGE = 0x200102,
    D3D11_MESSAGE_ID_SHADER_ERROR = 0x200103,
    D3D11_MESSAGE_ID_OFFERRESOURCES_INVALIDRESOURCE = 0x200104,
    D3D11_MESSAGE_ID_HSSETSAMPLERS_UNBINDDELETINGOBJECT = 0x200105,
    D3D11_MESSAGE_ID_DSSETSAMPLERS_UNBINDDELETINGOBJECT = 0x200106,
    D3D11_MESSAGE_ID_CSSETSAMPLERS_UNBINDDELETINGOBJECT = 0x200107,
    D3D11_MESSAGE_ID_HSSETSHADER_UNBINDDELETINGOBJECT = 0x200108,
    D3D11_MESSAGE_ID_DSSETSHADER_UNBINDDELETINGOBJECT = 0x200109,
    D3D11_MESSAGE_ID_CSSETSHADER_UNBINDDELETINGOBJECT = 0x20010a,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_INVALIDARG_RETURN = 0x20010b,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_OUTOFMEMORY_RETURN = 0x20010c,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_ACCESSDENIED_RETURN = 0x20010d,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_NUMUAVS_INVALIDRANGE = 0x20010e,
    D3D11_MESSAGE_ID_D3D11_MESSAGES_END = 0x20010f,
    D3D11_MESSAGE_ID_D3D11_1_MESSAGES_START = 0x300000,
    D3D11_MESSAGE_ID_CREATE_VIDEODECODER = 0x300001,
    D3D11_MESSAGE_ID_CREATE_VIDEOPROCESSORENUM = 0x300002,
    D3D11_MESSAGE_ID_CREATE_VIDEOPROCESSOR = 0x300003,
    D3D11_MESSAGE_ID_CREATE_DECODEROUTPUTVIEW = 0x300004,
    D3D11_MESSAGE_ID_CREATE_PROCESSORINPUTVIEW = 0x300005,
    D3D11_MESSAGE_ID_CREATE_PROCESSOROUTPUTVIEW = 0x300006,
    D3D11_MESSAGE_ID_CREATE_DEVICECONTEXTSTATE = 0x300007,
    D3D11_MESSAGE_ID_LIVE_VIDEODECODER = 0x300008,
    D3D11_MESSAGE_ID_LIVE_VIDEOPROCESSORENUM = 0x300009,
    D3D11_MESSAGE_ID_LIVE_VIDEOPROCESSOR = 0x30000a,
    D3D11_MESSAGE_ID_LIVE_DECODEROUTPUTVIEW = 0x30000b,
    D3D11_MESSAGE_ID_LIVE_PROCESSORINPUTVIEW = 0x30000c,
    D3D11_MESSAGE_ID_LIVE_PROCESSOROUTPUTVIEW = 0x30000d,
    D3D11_MESSAGE_ID_LIVE_DEVICECONTEXTSTATE = 0x30000e,
    D3D11_MESSAGE_ID_DESTROY_VIDEODECODER = 0x30000f,
    D3D11_MESSAGE_ID_DESTROY_VIDEOPROCESSORENUM = 0x300010,
    D3D11_MESSAGE_ID_DESTROY_VIDEOPROCESSOR = 0x300011,
    D3D11_MESSAGE_ID_DESTROY_DECODEROUTPUTVIEW = 0x300012,
    D3D11_MESSAGE_ID_DESTROY_PROCESSORINPUTVIEW = 0x300013,
    D3D11_MESSAGE_ID_DESTROY_PROCESSOROUTPUTVIEW = 0x300014,
    D3D11_MESSAGE_ID_DESTROY_DEVICECONTEXTSTATE = 0x300015,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_INVALIDFLAGS = 0x300016,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_INVALIDFEATURELEVEL = 0x300017,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_FEATURELEVELS_NOT_SUPPORTED = 0x300018,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_INVALIDREFIID = 0x300019,
    D3D11_MESSAGE_ID_DEVICE_DISCARDVIEW_INVALIDVIEW = 0x30001a,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION1_INVALIDCOPYFLAGS = 0x30001b,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE1_INVALIDCOPYFLAGS = 0x30001c,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFORCEDSAMPLECOUNT = 0x30001d,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_OUTOFMEMORY_RETURN = 0x30001e,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_NULLPARAM = 0x30001f,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_INVALIDFORMAT = 0x300020,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_ZEROWIDTHHEIGHT = 0x300021,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_DRIVER_INVALIDBUFFERSIZE = 0x300022,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_DRIVER_INVALIDBUFFERUSAGE = 0x300023,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILECOUNT_OUTOFMEMORY = 0x300024,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILE_NULLPARAM = 0x300025,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILE_INVALIDINDEX = 0x300026,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILE_OUTOFMEMORY_RETURN = 0x300027,
    D3D11_MESSAGE_ID_CHECKVIDEODECODERFORMAT_NULLPARAM = 0x300028,
    D3D11_MESSAGE_ID_CHECKVIDEODECODERFORMAT_OUTOFMEMORY_RETURN = 0x300029,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIGCOUNT_NULLPARAM = 0x30002a,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIGCOUNT_OUTOFMEMORY_RETURN = 0x30002b,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIG_NULLPARAM = 0x30002c,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIG_INVALIDINDEX = 0x30002d,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIG_OUTOFMEMORY_RETURN = 0x30002e,
    D3D11_MESSAGE_ID_GETDECODERCREATIONPARAMS_NULLPARAM = 0x30002f,
    D3D11_MESSAGE_ID_GETDECODERDRIVERHANDLE_NULLPARAM = 0x300030,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_NULLPARAM = 0x300031,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_INVALIDBUFFER = 0x300032,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_INVALIDTYPE = 0x300033,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_LOCKED = 0x300034,
    D3D11_MESSAGE_ID_RELEASEDECODERBUFFER_NULLPARAM = 0x300035,
    D3D11_MESSAGE_ID_RELEASEDECODERBUFFER_INVALIDTYPE = 0x300036,
    D3D11_MESSAGE_ID_RELEASEDECODERBUFFER_NOTLOCKED = 0x300037,
    D3D11_MESSAGE_ID_DECODERBEGINFRAME_NULLPARAM = 0x300038,
    D3D11_MESSAGE_ID_DECODERBEGINFRAME_HAZARD = 0x300039,
    D3D11_MESSAGE_ID_DECODERENDFRAME_NULLPARAM = 0x30003a,
    D3D11_MESSAGE_ID_SUBMITDECODERBUFFERS_NULLPARAM = 0x30003b,
    D3D11_MESSAGE_ID_SUBMITDECODERBUFFERS_INVALIDTYPE = 0x30003c,
    D3D11_MESSAGE_ID_DECODEREXTENSION_NULLPARAM = 0x30003d,
    D3D11_MESSAGE_ID_DECODEREXTENSION_INVALIDRESOURCE = 0x30003e,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_OUTOFMEMORY_RETURN = 0x30003f,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_NULLPARAM = 0x300040,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDFRAMEFORMAT = 0x300041,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDUSAGE = 0x300042,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDINPUTFRAMERATE = 0x300043,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDOUTPUTFRAMERATE = 0x300044,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDWIDTHHEIGHT = 0x300045,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCONTENTDESC_NULLPARAM = 0x300046,
    D3D11_MESSAGE_ID_CHECKVIDEOPROCESSORFORMAT_NULLPARAM = 0x300047,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCAPS_NULLPARAM = 0x300048,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORRATECONVERSIONCAPS_NULLPARAM = 0x300049,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORRATECONVERSIONCAPS_INVALIDINDEX = 0x30004a,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCUSTOMRATE_NULLPARAM = 0x30004b,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCUSTOMRATE_INVALIDINDEX = 0x30004c,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORFILTERRANGE_NULLPARAM = 0x30004d,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORFILTERRANGE_UNSUPPORTED = 0x30004e,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOR_OUTOFMEMORY_RETURN = 0x30004f,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOR_NULLPARAM = 0x300050,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTTARGETRECT_NULLPARAM = 0x300051,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTBACKGROUNDCOLOR_NULLPARAM = 0x300052,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTBACKGROUNDCOLOR_INVALIDALPHA = 0x300053,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCOLORSPACE_NULLPARAM = 0x300054,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_NULLPARAM = 0x300055,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_UNSUPPORTED = 0x300056,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_INVALIDSTREAM = 0x300057,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_INVALIDFILLMODE = 0x300058,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCONSTRICTION_NULLPARAM = 0x300059,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTSTEREOMODE_NULLPARAM = 0x30005a,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTSTEREOMODE_UNSUPPORTED = 0x30005b,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTEXTENSION_NULLPARAM = 0x30005c,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTTARGETRECT_NULLPARAM = 0x30005d,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTBACKGROUNDCOLOR_NULLPARAM = 0x30005e,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTCOLORSPACE_NULLPARAM = 0x30005f,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTALPHAFILLMODE_NULLPARAM = 0x300060,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTCONSTRICTION_NULLPARAM = 0x300061,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCONSTRICTION_UNSUPPORTED = 0x300062,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCONSTRICTION_INVALIDSIZE = 0x300063,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTSTEREOMODE_NULLPARAM = 0x300064,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTEXTENSION_NULLPARAM = 0x300065,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFRAMEFORMAT_NULLPARAM = 0x300066,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFRAMEFORMAT_INVALIDFORMAT = 0x300067,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFRAMEFORMAT_INVALIDSTREAM = 0x300068,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMCOLORSPACE_NULLPARAM = 0x300069,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMCOLORSPACE_INVALIDSTREAM = 0x30006a,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_NULLPARAM = 0x30006b,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_INVALIDRATE = 0x30006c,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_INVALIDFLAG = 0x30006d,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_INVALIDSTREAM = 0x30006e,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSOURCERECT_NULLPARAM = 0x30006f,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSOURCERECT_INVALIDSTREAM = 0x300070,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSOURCERECT_INVALIDRECT = 0x300071,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMDESTRECT_NULLPARAM = 0x300072,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMDESTRECT_INVALIDSTREAM = 0x300073,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMDESTRECT_INVALIDRECT = 0x300074,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_NULLPARAM = 0x300075,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_INVALIDSTREAM = 0x300076,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_INVALIDALPHA = 0x300077,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_NULLPARAM = 0x300078,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_INVALIDSTREAM = 0x300079,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_INVALIDCOUNT = 0x30007a,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_INVALIDALPHA = 0x30007b,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_NULLPARAM = 0x30007c,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_INVALIDSTREAM = 0x30007d,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_INVALIDRATIO = 0x30007e,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_NULLPARAM = 0x30007f,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_INVALIDSTREAM = 0x300080,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_INVALIDRANGE = 0x300081,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_UNSUPPORTED = 0x300082,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_NULLPARAM = 0x300083,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_INVALIDSTREAM = 0x300084,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_UNSUPPORTED = 0x300085,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_FLIPUNSUPPORTED = 0x300086,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_MONOOFFSETUNSUPPORTED = 0x300087,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_FORMATUNSUPPORTED = 0x300088,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_INVALIDFORMAT = 0x300089,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMAUTOPROCESSINGMODE_NULLPARAM = 0x30008a,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMAUTOPROCESSINGMODE_INVALIDSTREAM = 0x30008b,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_NULLPARAM = 0x30008c,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_INVALIDSTREAM = 0x30008d,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_INVALIDFILTER = 0x30008e,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_UNSUPPORTED = 0x30008f,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_INVALIDLEVEL = 0x300090,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMEXTENSION_NULLPARAM = 0x300091,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMEXTENSION_INVALIDSTREAM = 0x300092,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMFRAMEFORMAT_NULLPARAM = 0x300093,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMCOLORSPACE_NULLPARAM = 0x300094,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMOUTPUTRATE_NULLPARAM = 0x300095,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMSOURCERECT_NULLPARAM = 0x300096,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMDESTRECT_NULLPARAM = 0x300097,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMALPHA_NULLPARAM = 0x300098,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMPALETTE_NULLPARAM = 0x300099,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMPIXELASPECTRATIO_NULLPARAM = 0x30009a,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMLUMAKEY_NULLPARAM = 0x30009b,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMSTEREOFORMAT_NULLPARAM = 0x30009c,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMAUTOPROCESSINGMODE_NULLPARAM = 0x30009d,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMFILTER_NULLPARAM = 0x30009e,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMEXTENSION_NULLPARAM = 0x30009f,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMEXTENSION_INVALIDSTREAM = 0x3000a0,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_NULLPARAM = 0x3000a1,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDSTREAMCOUNT = 0x3000a2,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_TARGETRECT = 0x3000a3,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDOUTPUT = 0x3000a4,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDPASTFRAMES = 0x3000a5,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDFUTUREFRAMES = 0x3000a6,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDSOURCERECT = 0x3000a7,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDDESTRECT = 0x3000a8,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDINPUTRESOURCE = 0x3000a9,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDARRAYSIZE = 0x3000aa,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDARRAY = 0x3000ab,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_RIGHTEXPECTED = 0x3000ac,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_RIGHTNOTEXPECTED = 0x3000ad,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_STEREONOTENABLED = 0x3000ae,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDRIGHTRESOURCE = 0x3000af,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_NOSTEREOSTREAMS = 0x3000b0,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INPUTHAZARD = 0x3000b1,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_OUTPUTHAZARD = 0x3000b2,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_OUTOFMEMORY_RETURN = 0x3000b3,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_NULLPARAM = 0x3000b4,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDTYPE = 0x3000b5,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDBIND = 0x3000b6,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_UNSUPPORTEDFORMAT = 0x3000b7,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDMIP = 0x3000b8,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_UNSUPPORTEMIP = 0x3000b9,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDARRAYSIZE = 0x3000ba,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDARRAY = 0x3000bb,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDDIMENSION = 0x3000bc,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_OUTOFMEMORY_RETURN = 0x3000bd,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_NULLPARAM = 0x3000be,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDTYPE = 0x3000bf,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDBIND = 0x3000c0,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDMISC = 0x3000c1,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDUSAGE = 0x3000c2,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDFORMAT = 0x3000c3,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDFOURCC = 0x3000c4,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDMIP = 0x3000c5,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_UNSUPPORTEDMIP = 0x3000c6,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDARRAYSIZE = 0x3000c7,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDARRAY = 0x3000c8,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDDIMENSION = 0x3000c9,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_OUTOFMEMORY_RETURN = 0x3000ca,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_NULLPARAM = 0x3000cb,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDTYPE = 0x3000cc,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDBIND = 0x3000cd,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDFORMAT = 0x3000ce,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDMIP = 0x3000cf,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_UNSUPPORTEDMIP = 0x3000d0,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_UNSUPPORTEDARRAY = 0x3000d1,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDARRAY = 0x3000d2,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDDIMENSION = 0x3000d3,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INVALID_USE_OF_FORCED_SAMPLE_COUNT = 0x3000d4,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDLOGICOPS = 0x3000d5,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDARRAYWITHDECODER = 0x3000d6,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDARRAYWITHDECODER = 0x3000d7,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDARRAYWITHDECODER = 0x3000d8,
    D3D11_MESSAGE_ID_DEVICE_LOCKEDOUT_INTERFACE = 0x3000d9,
    D3D11_MESSAGE_ID_REF_WARNING_ATOMIC_INCONSISTENT = 0x3000da,
    D3D11_MESSAGE_ID_REF_WARNING_READING_UNINITIALIZED_RESOURCE = 0x3000db,
    D3D11_MESSAGE_ID_REF_WARNING_RAW_HAZARD = 0x3000dc,
    D3D11_MESSAGE_ID_REF_WARNING_WAR_HAZARD = 0x3000dd,
    D3D11_MESSAGE_ID_REF_WARNING_WAW_HAZARD = 0x3000de,
    D3D11_MESSAGE_ID_CREATECRYPTOSESSION_NULLPARAM = 0x3000df,
    D3D11_MESSAGE_ID_CREATECRYPTOSESSION_OUTOFMEMORY_RETURN = 0x3000e0,
    D3D11_MESSAGE_ID_GETCRYPTOTYPE_NULLPARAM = 0x3000e1,
    D3D11_MESSAGE_ID_GETDECODERPROFILE_NULLPARAM = 0x3000e2,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONCERTIFICATESIZE_NULLPARAM = 0x3000e3,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONCERTIFICATE_NULLPARAM = 0x3000e4,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONCERTIFICATE_WRONGSIZE = 0x3000e5,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONHANDLE_WRONGSIZE = 0x3000e6,
    D3D11_MESSAGE_ID_NEGOTIATECRPYTOSESSIONKEYEXCHANGE_NULLPARAM = 0x3000e7,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_UNSUPPORTED = 0x3000e8,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_NULLPARAM = 0x3000e9,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_WRONGDEVICE = 0x3000ea,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_WRONGDEVICE = 0x3000eb,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_FORMAT_MISMATCH = 0x3000ec,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SIZE_MISMATCH = 0x3000ed,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_MULTISAMPLED = 0x3000ee,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_NOT_STAGING = 0x3000ef,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_MAPPED = 0x3000f0,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_MAPPED = 0x3000f1,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_OFFERED = 0x3000f2,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_OFFERED = 0x3000f3,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_CONTENT_UNDEFINED = 0x3000f4,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_UNSUPPORTED = 0x3000f5,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_NULLPARAM = 0x3000f6,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_WRONGDEVICE = 0x3000f7,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_WRONGDEVICE = 0x3000f8,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_FORMAT_MISMATCH = 0x3000f9,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SIZE_MISMATCH = 0x3000fa,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_MULTISAMPLED = 0x3000fb,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_NOT_STAGING = 0x3000fc,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_NOT_RENDER_TARGET = 0x3000fd,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_MAPPED = 0x3000fe,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_MAPPED = 0x3000ff,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_OFFERED = 0x300100,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_OFFERED = 0x300101,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_CONTENT_UNDEFINED = 0x300102,
    D3D11_MESSAGE_ID_STARTSESSIONKEYREFRESH_NULLPARAM = 0x300103,
    D3D11_MESSAGE_ID_STARTSESSIONKEYREFRESH_INVALIDSIZE = 0x300104,
    D3D11_MESSAGE_ID_FINISHSESSIONKEYREFRESH_NULLPARAM = 0x300105,
    D3D11_MESSAGE_ID_GETENCRYPTIONBLTKEY_NULLPARAM = 0x300106,
    D3D11_MESSAGE_ID_GETENCRYPTIONBLTKEY_INVALIDSIZE = 0x300107,
    D3D11_MESSAGE_ID_GETCONTENTPROTECTIONCAPS_NULLPARAM = 0x300108,
    D3D11_MESSAGE_ID_CHECKCRYPTOKEYEXCHANGE_NULLPARAM = 0x300109,
    D3D11_MESSAGE_ID_CHECKCRYPTOKEYEXCHANGE_INVALIDINDEX = 0x30010a,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_NULLPARAM = 0x30010b,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_UNSUPPORTED = 0x30010c,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_INVALIDTYPE = 0x30010d,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_OUTOFMEMORY_RETURN = 0x30010e,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATESIZE_INVALIDCHANNEL = 0x30010f,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATESIZE_NULLPARAM = 0x300110,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATE_INVALIDCHANNEL = 0x300111,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATE_NULLPARAM = 0x300112,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATE_WRONGSIZE = 0x300113,
    D3D11_MESSAGE_ID_NEGOTIATEAUTHENTICATEDCHANNELKEYEXCHANGE_INVALIDCHANNEL = 0x300114,
    D3D11_MESSAGE_ID_NEGOTIATEAUTHENTICATEDCHANNELKEYEXCHANGE_NULLPARAM = 0x300115,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_NULLPARAM = 0x300116,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_WRONGCHANNEL = 0x300117,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_UNSUPPORTEDQUERY = 0x300118,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_WRONGSIZE = 0x300119,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_INVALIDPROCESSINDEX = 0x30011a,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_NULLPARAM = 0x30011b,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_WRONGCHANNEL = 0x30011c,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_UNSUPPORTEDCONFIGURE = 0x30011d,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_WRONGSIZE = 0x30011e,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_INVALIDPROCESSIDTYPE = 0x30011f,
    D3D11_MESSAGE_ID_VSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT = 0x300120,
    D3D11_MESSAGE_ID_DSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT = 0x300121,
    D3D11_MESSAGE_ID_HSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT = 0x300122,
    D3D11_MESSAGE_ID_GSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT = 0x300123,
    D3D11_MESSAGE_ID_PSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT = 0x300124,
    D3D11_MESSAGE_ID_CSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT = 0x300125,
    D3D11_MESSAGE_ID_NEGOTIATECRPYTOSESSIONKEYEXCHANGE_INVALIDSIZE = 0x300126,
    D3D11_MESSAGE_ID_NEGOTIATEAUTHENTICATEDCHANNELKEYEXCHANGE_INVALIDSIZE = 0x300127,
    D3D11_MESSAGE_ID_OFFERRESOURCES_INVALIDPRIORITY = 0x300128,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONHANDLE_OUTOFMEMORY = 0x300129,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_NULLPARAM = 0x30012a,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_INVALIDTYPE = 0x30012b,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_INVALIDBIND = 0x30012c,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_INVALIDARRAY = 0x30012d,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_NULLPARAM = 0x30012e,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_INVALIDSTREAM = 0x30012f,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_INVALID = 0x300130,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_UNSUPPORTED = 0x300131,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMROTATION_NULLPARAM = 0x300132,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDVIEW = 0x300133,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x300134,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_SHADEREXTENSIONSNOTSUPPORTED = 0x300135,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x300136,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_SHADEREXTENSIONSNOTSUPPORTED = 0x300137,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x300138,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_SHADEREXTENSIONSNOTSUPPORTED = 0x300139,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x30013a,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_SHADEREXTENSIONSNOTSUPPORTED = 0x30013b,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEEXTENSIONSNOTSUPPORTED = 0x30013c,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_SHADEREXTENSIONSNOTSUPPORTED = 0x30013d,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x30013e,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_SHADEREXTENSIONSNOTSUPPORTED = 0x30013f,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x300140,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_SHADEREXTENSIONSNOTSUPPORTED = 0x300141,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_MINPRECISION = 0x300142,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_UNSUPPORTED = 0x300143,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_UNSUPPORTED = 0x300144,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_UAVSNOTSUPPORTED = 0x300145,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_UAVSNOTSUPPORTED = 0x300146,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_UAVSNOTSUPPORTED = 0x300147,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_UAVSNOTSUPPORTED = 0x300148,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UAVSNOTSUPPORTED = 0x300149,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_UAVSNOTSUPPORTED = 0x30014a,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_UAVSNOTSUPPORTED = 0x30014b,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_INVALIDOFFSET = 0x30014c,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_TOOMANYVIEWS = 0x30014d,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_NOTSUPPORTED = 0x30014e,
    D3D11_MESSAGE_ID_SWAPDEVICECONTEXTSTATE_NOTSUPPORTED = 0x30014f,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_PREFERUPDATESUBRESOURCE1 = 0x300150,
    D3D11_MESSAGE_ID_GETDC_INACCESSIBLE = 0x300151,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDRECT = 0x300152,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SAMPLE_MASK_IGNORED_ON_FL9 = 0x300153,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE1_NOT_SUPPORTED = 0x300154,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_BY_NAME_NOT_SUPPORTED = 0x300155,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_NOT_SUPPORTED = 0x300156,
    D3D11_MESSAGE_ID_OFFERRELEASE_NOT_SUPPORTED = 0x300157,
    D3D11_MESSAGE_ID_OFFERRESOURCES_INACCESSIBLE = 0x300158,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDMSAA = 0x300159,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDMSAA = 0x30015a,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDSOURCERECT = 0x30015b,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_EMPTYRECT = 0x30015c,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_EMPTYDESTBOX = 0x30015d,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_EMPTYSOURCEBOX = 0x30015e,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OM_RENDER_TARGET_DOES_NOT_SUPPORT_LOGIC_OPS = 0x30015f,
    D3D11_MESSAGE_ID_DEVICE_DRAW_DEPTHSTENCILVIEW_NOT_SET = 0x300160,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RENDERTARGETVIEW_NOT_SET = 0x300161,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RENDERTARGETVIEW_NOT_SET_DUE_TO_FLIP_PRESENT = 0x300162,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_NOT_SET_DUE_TO_FLIP_PRESENT = 0x300163,
    D3D11_MESSAGE_ID_D3D11_1_MESSAGES_END = 0x300164
} D3D11_MESSAGE_ID;
typedef enum D3D11_RLDO_FLAGS {
    D3D11_RLDO_SUMMARY = 1,
    D3D11_RLDO_DETAIL = 2
} D3D11_RLDO_FLAGS;
typedef struct D3D11_MESSAGE {
    D3D11_MESSAGE_CATEGORY Category;
    D3D11_MESSAGE_SEVERITY Severity;
    D3D11_MESSAGE_ID ID;
    const char *pDescription;
    SIZE_T DescriptionByteLength;
} D3D11_MESSAGE;
typedef struct D3D11_INFO_QUEUE_FILTER_DESC {
    UINT NumCategories;
    D3D11_MESSAGE_CATEGORY *pCategoryList;
    UINT NumSeverities;
    D3D11_MESSAGE_SEVERITY *pSeverityList;
    UINT NumIDs;
    D3D11_MESSAGE_ID *pIDList;
} D3D11_INFO_QUEUE_FILTER_DESC;
typedef struct D3D11_INFO_QUEUE_FILTER {
    D3D11_INFO_QUEUE_FILTER_DESC AllowList;
    D3D11_INFO_QUEUE_FILTER_DESC DenyList;
} D3D11_INFO_QUEUE_FILTER;
#define D3D11_INFO_QUEUE_DEFAULT_MESSAGE_COUNT_LIMIT 1024
/*****************************************************************************
 * ID3D11Debug interface
 */
#ifndef __ID3D11Debug_INTERFACE_DEFINED__
#define __ID3D11Debug_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Debug, 0x79cf2233, 0x7536, 0x4948, 0x9d,0x36, 0x1e,0x46,0x92,0xdc,0x57,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79cf2233-7536-4948-9d36-1e4692dc5760")
ID3D11Debug : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetFeatureMask(
        UINT Mask) = 0;

    virtual UINT STDMETHODCALLTYPE GetFeatureMask(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPresentPerRenderOpDelay(
        UINT Milliseconds) = 0;

    virtual UINT STDMETHODCALLTYPE GetPresentPerRenderOpDelay(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSwapChain(
        IDXGISwapChain *pSwapChain) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSwapChain(
        IDXGISwapChain **ppSwapChain) = 0;

    virtual HRESULT STDMETHODCALLTYPE ValidateContext(
        ID3D11DeviceContext *pContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReportLiveDeviceObjects(
        D3D11_RLDO_FLAGS Flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ValidateContextForDispatch(
        ID3D11DeviceContext *pContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Debug, 0x79cf2233, 0x7536, 0x4948, 0x9d,0x36, 0x1e,0x46,0x92,0xdc,0x57,0x60)
#endif
#else
typedef struct ID3D11DebugVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Debug *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Debug *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Debug *This);

    /*** ID3D11Debug methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFeatureMask)(
        ID3D11Debug *This,
        UINT Mask);

    UINT (STDMETHODCALLTYPE *GetFeatureMask)(
        ID3D11Debug *This);

    HRESULT (STDMETHODCALLTYPE *SetPresentPerRenderOpDelay)(
        ID3D11Debug *This,
        UINT Milliseconds);

    UINT (STDMETHODCALLTYPE *GetPresentPerRenderOpDelay)(
        ID3D11Debug *This);

    HRESULT (STDMETHODCALLTYPE *SetSwapChain)(
        ID3D11Debug *This,
        IDXGISwapChain *pSwapChain);

    HRESULT (STDMETHODCALLTYPE *GetSwapChain)(
        ID3D11Debug *This,
        IDXGISwapChain **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *ValidateContext)(
        ID3D11Debug *This,
        ID3D11DeviceContext *pContext);

    HRESULT (STDMETHODCALLTYPE *ReportLiveDeviceObjects)(
        ID3D11Debug *This,
        D3D11_RLDO_FLAGS Flags);

    HRESULT (STDMETHODCALLTYPE *ValidateContextForDispatch)(
        ID3D11Debug *This,
        ID3D11DeviceContext *pContext);

    END_INTERFACE
} ID3D11DebugVtbl;

interface ID3D11Debug {
    CONST_VTBL ID3D11DebugVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Debug_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Debug_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Debug_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Debug methods ***/
#define ID3D11Debug_SetFeatureMask(This,Mask) (This)->lpVtbl->SetFeatureMask(This,Mask)
#define ID3D11Debug_GetFeatureMask(This) (This)->lpVtbl->GetFeatureMask(This)
#define ID3D11Debug_SetPresentPerRenderOpDelay(This,Milliseconds) (This)->lpVtbl->SetPresentPerRenderOpDelay(This,Milliseconds)
#define ID3D11Debug_GetPresentPerRenderOpDelay(This) (This)->lpVtbl->GetPresentPerRenderOpDelay(This)
#define ID3D11Debug_SetSwapChain(This,pSwapChain) (This)->lpVtbl->SetSwapChain(This,pSwapChain)
#define ID3D11Debug_GetSwapChain(This,ppSwapChain) (This)->lpVtbl->GetSwapChain(This,ppSwapChain)
#define ID3D11Debug_ValidateContext(This,pContext) (This)->lpVtbl->ValidateContext(This,pContext)
#define ID3D11Debug_ReportLiveDeviceObjects(This,Flags) (This)->lpVtbl->ReportLiveDeviceObjects(This,Flags)
#define ID3D11Debug_ValidateContextForDispatch(This,pContext) (This)->lpVtbl->ValidateContextForDispatch(This,pContext)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Debug_QueryInterface(ID3D11Debug* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Debug_AddRef(ID3D11Debug* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Debug_Release(ID3D11Debug* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Debug methods ***/
static inline HRESULT ID3D11Debug_SetFeatureMask(ID3D11Debug* This,UINT Mask) {
    return This->lpVtbl->SetFeatureMask(This,Mask);
}
static inline UINT ID3D11Debug_GetFeatureMask(ID3D11Debug* This) {
    return This->lpVtbl->GetFeatureMask(This);
}
static inline HRESULT ID3D11Debug_SetPresentPerRenderOpDelay(ID3D11Debug* This,UINT Milliseconds) {
    return This->lpVtbl->SetPresentPerRenderOpDelay(This,Milliseconds);
}
static inline UINT ID3D11Debug_GetPresentPerRenderOpDelay(ID3D11Debug* This) {
    return This->lpVtbl->GetPresentPerRenderOpDelay(This);
}
static inline HRESULT ID3D11Debug_SetSwapChain(ID3D11Debug* This,IDXGISwapChain *pSwapChain) {
    return This->lpVtbl->SetSwapChain(This,pSwapChain);
}
static inline HRESULT ID3D11Debug_GetSwapChain(ID3D11Debug* This,IDXGISwapChain **ppSwapChain) {
    return This->lpVtbl->GetSwapChain(This,ppSwapChain);
}
static inline HRESULT ID3D11Debug_ValidateContext(ID3D11Debug* This,ID3D11DeviceContext *pContext) {
    return This->lpVtbl->ValidateContext(This,pContext);
}
static inline HRESULT ID3D11Debug_ReportLiveDeviceObjects(ID3D11Debug* This,D3D11_RLDO_FLAGS Flags) {
    return This->lpVtbl->ReportLiveDeviceObjects(This,Flags);
}
static inline HRESULT ID3D11Debug_ValidateContextForDispatch(ID3D11Debug* This,ID3D11DeviceContext *pContext) {
    return This->lpVtbl->ValidateContextForDispatch(This,pContext);
}
#endif
#endif

#endif


#endif  /* __ID3D11Debug_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11SwitchToRef interface
 */
#ifndef __ID3D11SwitchToRef_INTERFACE_DEFINED__
#define __ID3D11SwitchToRef_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11SwitchToRef, 0x1ef337e3, 0x58e7, 0x4f83, 0xa6,0x92, 0xdb,0x22,0x1f,0x5e,0xd4,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1ef337e3-58e7-4f83-a692-db221f5ed47e")
ID3D11SwitchToRef : public IUnknown
{
    virtual WINBOOL STDMETHODCALLTYPE SetUseRef(
        WINBOOL useref) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetUseRef(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11SwitchToRef, 0x1ef337e3, 0x58e7, 0x4f83, 0xa6,0x92, 0xdb,0x22,0x1f,0x5e,0xd4,0x7e)
#endif
#else
typedef struct ID3D11SwitchToRefVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11SwitchToRef *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11SwitchToRef *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11SwitchToRef *This);

    /*** ID3D11SwitchToRef methods ***/
    WINBOOL (STDMETHODCALLTYPE *SetUseRef)(
        ID3D11SwitchToRef *This,
        WINBOOL useref);

    WINBOOL (STDMETHODCALLTYPE *GetUseRef)(
        ID3D11SwitchToRef *This);

    END_INTERFACE
} ID3D11SwitchToRefVtbl;

interface ID3D11SwitchToRef {
    CONST_VTBL ID3D11SwitchToRefVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11SwitchToRef_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11SwitchToRef_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11SwitchToRef_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11SwitchToRef methods ***/
#define ID3D11SwitchToRef_SetUseRef(This,useref) (This)->lpVtbl->SetUseRef(This,useref)
#define ID3D11SwitchToRef_GetUseRef(This) (This)->lpVtbl->GetUseRef(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11SwitchToRef_QueryInterface(ID3D11SwitchToRef* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11SwitchToRef_AddRef(ID3D11SwitchToRef* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11SwitchToRef_Release(ID3D11SwitchToRef* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11SwitchToRef methods ***/
static inline WINBOOL ID3D11SwitchToRef_SetUseRef(ID3D11SwitchToRef* This,WINBOOL useref) {
    return This->lpVtbl->SetUseRef(This,useref);
}
static inline WINBOOL ID3D11SwitchToRef_GetUseRef(ID3D11SwitchToRef* This) {
    return This->lpVtbl->GetUseRef(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11SwitchToRef_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11InfoQueue interface
 */
#ifndef __ID3D11InfoQueue_INTERFACE_DEFINED__
#define __ID3D11InfoQueue_INTERFACE_DEFINED__

#ifdef WINE_NO_UNICODE_MACROS
#undef GetMessage
#endif
DEFINE_GUID(IID_ID3D11InfoQueue, 0x6543dbb6, 0x1b48, 0x42f5, 0xab,0x82, 0xe9,0x7e,0xc7,0x43,0x26,0xf6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6543dbb6-1b48-42f5-ab82-e97ec74326f6")
ID3D11InfoQueue : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetMessageCountLimit(
        UINT64 MessageCountLimit) = 0;

    virtual void STDMETHODCALLTYPE ClearStoredMessages(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMessage(
        UINT64 MessageIndex,
        D3D11_MESSAGE *pMessage,
        SIZE_T *pMessageByteLength) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumMessagesAllowedByStorageFilter(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumMessagesDeniedByStorageFilter(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumStoredMessages(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumStoredMessagesAllowedByRetrievalFilter(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumMessagesDiscardedByMessageCountLimit(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetMessageCountLimit(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStorageFilterEntries(
        D3D11_INFO_QUEUE_FILTER *pFilter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStorageFilter(
        D3D11_INFO_QUEUE_FILTER *pFilter,
        SIZE_T *pFilterByteLength) = 0;

    virtual void STDMETHODCALLTYPE ClearStorageFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushEmptyStorageFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushCopyOfStorageFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushStorageFilter(
        D3D11_INFO_QUEUE_FILTER *pFilter) = 0;

    virtual void STDMETHODCALLTYPE PopStorageFilter(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetStorageFilterStackSize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRetrievalFilterEntries(
        D3D11_INFO_QUEUE_FILTER *pFilter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRetrievalFilter(
        D3D11_INFO_QUEUE_FILTER *pFilter,
        SIZE_T *pFilterByteLength) = 0;

    virtual void STDMETHODCALLTYPE ClearRetrievalFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushEmptyRetrievalFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushCopyOfRetrievalFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushRetrievalFilter(
        D3D11_INFO_QUEUE_FILTER *pFilter) = 0;

    virtual void STDMETHODCALLTYPE PopRetrievalFilter(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetRetrievalFilterStackSize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddMessage(
        D3D11_MESSAGE_CATEGORY Category,
        D3D11_MESSAGE_SEVERITY Severity,
        D3D11_MESSAGE_ID ID,
        LPCSTR pDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddApplicationMessage(
        D3D11_MESSAGE_SEVERITY Severity,
        LPCSTR pDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBreakOnCategory(
        D3D11_MESSAGE_CATEGORY Category,
        WINBOOL bEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBreakOnSeverity(
        D3D11_MESSAGE_SEVERITY Severity,
        WINBOOL bEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBreakOnID(
        D3D11_MESSAGE_ID ID,
        WINBOOL bEnable) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetBreakOnCategory(
        D3D11_MESSAGE_CATEGORY Category) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetBreakOnSeverity(
        D3D11_MESSAGE_SEVERITY Severity) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetBreakOnID(
        D3D11_MESSAGE_ID ID) = 0;

    virtual void STDMETHODCALLTYPE SetMuteDebugOutput(
        WINBOOL bMute) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetMuteDebugOutput(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11InfoQueue, 0x6543dbb6, 0x1b48, 0x42f5, 0xab,0x82, 0xe9,0x7e,0xc7,0x43,0x26,0xf6)
#endif
#else
typedef struct ID3D11InfoQueueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11InfoQueue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11InfoQueue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11InfoQueue *This);

    /*** ID3D11InfoQueue methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMessageCountLimit)(
        ID3D11InfoQueue *This,
        UINT64 MessageCountLimit);

    void (STDMETHODCALLTYPE *ClearStoredMessages)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *GetMessage)(
        ID3D11InfoQueue *This,
        UINT64 MessageIndex,
        D3D11_MESSAGE *pMessage,
        SIZE_T *pMessageByteLength);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesAllowedByStorageFilter)(
        ID3D11InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesDeniedByStorageFilter)(
        ID3D11InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumStoredMessages)(
        ID3D11InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumStoredMessagesAllowedByRetrievalFilter)(
        ID3D11InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesDiscardedByMessageCountLimit)(
        ID3D11InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetMessageCountLimit)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *AddStorageFilterEntries)(
        ID3D11InfoQueue *This,
        D3D11_INFO_QUEUE_FILTER *pFilter);

    HRESULT (STDMETHODCALLTYPE *GetStorageFilter)(
        ID3D11InfoQueue *This,
        D3D11_INFO_QUEUE_FILTER *pFilter,
        SIZE_T *pFilterByteLength);

    void (STDMETHODCALLTYPE *ClearStorageFilter)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushEmptyStorageFilter)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushCopyOfStorageFilter)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushStorageFilter)(
        ID3D11InfoQueue *This,
        D3D11_INFO_QUEUE_FILTER *pFilter);

    void (STDMETHODCALLTYPE *PopStorageFilter)(
        ID3D11InfoQueue *This);

    UINT (STDMETHODCALLTYPE *GetStorageFilterStackSize)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *AddRetrievalFilterEntries)(
        ID3D11InfoQueue *This,
        D3D11_INFO_QUEUE_FILTER *pFilter);

    HRESULT (STDMETHODCALLTYPE *GetRetrievalFilter)(
        ID3D11InfoQueue *This,
        D3D11_INFO_QUEUE_FILTER *pFilter,
        SIZE_T *pFilterByteLength);

    void (STDMETHODCALLTYPE *ClearRetrievalFilter)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushEmptyRetrievalFilter)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushCopyOfRetrievalFilter)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushRetrievalFilter)(
        ID3D11InfoQueue *This,
        D3D11_INFO_QUEUE_FILTER *pFilter);

    void (STDMETHODCALLTYPE *PopRetrievalFilter)(
        ID3D11InfoQueue *This);

    UINT (STDMETHODCALLTYPE *GetRetrievalFilterStackSize)(
        ID3D11InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *AddMessage)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_CATEGORY Category,
        D3D11_MESSAGE_SEVERITY Severity,
        D3D11_MESSAGE_ID ID,
        LPCSTR pDescription);

    HRESULT (STDMETHODCALLTYPE *AddApplicationMessage)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_SEVERITY Severity,
        LPCSTR pDescription);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnCategory)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_CATEGORY Category,
        WINBOOL bEnable);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnSeverity)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_SEVERITY Severity,
        WINBOOL bEnable);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnID)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_ID ID,
        WINBOOL bEnable);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnCategory)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_CATEGORY Category);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnSeverity)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_SEVERITY Severity);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnID)(
        ID3D11InfoQueue *This,
        D3D11_MESSAGE_ID ID);

    void (STDMETHODCALLTYPE *SetMuteDebugOutput)(
        ID3D11InfoQueue *This,
        WINBOOL bMute);

    WINBOOL (STDMETHODCALLTYPE *GetMuteDebugOutput)(
        ID3D11InfoQueue *This);

    END_INTERFACE
} ID3D11InfoQueueVtbl;

interface ID3D11InfoQueue {
    CONST_VTBL ID3D11InfoQueueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11InfoQueue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11InfoQueue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11InfoQueue_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11InfoQueue methods ***/
#define ID3D11InfoQueue_SetMessageCountLimit(This,MessageCountLimit) (This)->lpVtbl->SetMessageCountLimit(This,MessageCountLimit)
#define ID3D11InfoQueue_ClearStoredMessages(This) (This)->lpVtbl->ClearStoredMessages(This)
#define ID3D11InfoQueue_GetMessage(This,MessageIndex,pMessage,pMessageByteLength) (This)->lpVtbl->GetMessage(This,MessageIndex,pMessage,pMessageByteLength)
#define ID3D11InfoQueue_GetNumMessagesAllowedByStorageFilter(This) (This)->lpVtbl->GetNumMessagesAllowedByStorageFilter(This)
#define ID3D11InfoQueue_GetNumMessagesDeniedByStorageFilter(This) (This)->lpVtbl->GetNumMessagesDeniedByStorageFilter(This)
#define ID3D11InfoQueue_GetNumStoredMessages(This) (This)->lpVtbl->GetNumStoredMessages(This)
#define ID3D11InfoQueue_GetNumStoredMessagesAllowedByRetrievalFilter(This) (This)->lpVtbl->GetNumStoredMessagesAllowedByRetrievalFilter(This)
#define ID3D11InfoQueue_GetNumMessagesDiscardedByMessageCountLimit(This) (This)->lpVtbl->GetNumMessagesDiscardedByMessageCountLimit(This)
#define ID3D11InfoQueue_GetMessageCountLimit(This) (This)->lpVtbl->GetMessageCountLimit(This)
#define ID3D11InfoQueue_AddStorageFilterEntries(This,pFilter) (This)->lpVtbl->AddStorageFilterEntries(This,pFilter)
#define ID3D11InfoQueue_GetStorageFilter(This,pFilter,pFilterByteLength) (This)->lpVtbl->GetStorageFilter(This,pFilter,pFilterByteLength)
#define ID3D11InfoQueue_ClearStorageFilter(This) (This)->lpVtbl->ClearStorageFilter(This)
#define ID3D11InfoQueue_PushEmptyStorageFilter(This) (This)->lpVtbl->PushEmptyStorageFilter(This)
#define ID3D11InfoQueue_PushCopyOfStorageFilter(This) (This)->lpVtbl->PushCopyOfStorageFilter(This)
#define ID3D11InfoQueue_PushStorageFilter(This,pFilter) (This)->lpVtbl->PushStorageFilter(This,pFilter)
#define ID3D11InfoQueue_PopStorageFilter(This) (This)->lpVtbl->PopStorageFilter(This)
#define ID3D11InfoQueue_GetStorageFilterStackSize(This) (This)->lpVtbl->GetStorageFilterStackSize(This)
#define ID3D11InfoQueue_AddRetrievalFilterEntries(This,pFilter) (This)->lpVtbl->AddRetrievalFilterEntries(This,pFilter)
#define ID3D11InfoQueue_GetRetrievalFilter(This,pFilter,pFilterByteLength) (This)->lpVtbl->GetRetrievalFilter(This,pFilter,pFilterByteLength)
#define ID3D11InfoQueue_ClearRetrievalFilter(This) (This)->lpVtbl->ClearRetrievalFilter(This)
#define ID3D11InfoQueue_PushEmptyRetrievalFilter(This) (This)->lpVtbl->PushEmptyRetrievalFilter(This)
#define ID3D11InfoQueue_PushCopyOfRetrievalFilter(This) (This)->lpVtbl->PushCopyOfRetrievalFilter(This)
#define ID3D11InfoQueue_PushRetrievalFilter(This,pFilter) (This)->lpVtbl->PushRetrievalFilter(This,pFilter)
#define ID3D11InfoQueue_PopRetrievalFilter(This) (This)->lpVtbl->PopRetrievalFilter(This)
#define ID3D11InfoQueue_GetRetrievalFilterStackSize(This) (This)->lpVtbl->GetRetrievalFilterStackSize(This)
#define ID3D11InfoQueue_AddMessage(This,Category,Severity,ID,pDescription) (This)->lpVtbl->AddMessage(This,Category,Severity,ID,pDescription)
#define ID3D11InfoQueue_AddApplicationMessage(This,Severity,pDescription) (This)->lpVtbl->AddApplicationMessage(This,Severity,pDescription)
#define ID3D11InfoQueue_SetBreakOnCategory(This,Category,bEnable) (This)->lpVtbl->SetBreakOnCategory(This,Category,bEnable)
#define ID3D11InfoQueue_SetBreakOnSeverity(This,Severity,bEnable) (This)->lpVtbl->SetBreakOnSeverity(This,Severity,bEnable)
#define ID3D11InfoQueue_SetBreakOnID(This,ID,bEnable) (This)->lpVtbl->SetBreakOnID(This,ID,bEnable)
#define ID3D11InfoQueue_GetBreakOnCategory(This,Category) (This)->lpVtbl->GetBreakOnCategory(This,Category)
#define ID3D11InfoQueue_GetBreakOnSeverity(This,Severity) (This)->lpVtbl->GetBreakOnSeverity(This,Severity)
#define ID3D11InfoQueue_GetBreakOnID(This,ID) (This)->lpVtbl->GetBreakOnID(This,ID)
#define ID3D11InfoQueue_SetMuteDebugOutput(This,bMute) (This)->lpVtbl->SetMuteDebugOutput(This,bMute)
#define ID3D11InfoQueue_GetMuteDebugOutput(This) (This)->lpVtbl->GetMuteDebugOutput(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11InfoQueue_QueryInterface(ID3D11InfoQueue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11InfoQueue_AddRef(ID3D11InfoQueue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11InfoQueue_Release(ID3D11InfoQueue* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11InfoQueue methods ***/
static inline HRESULT ID3D11InfoQueue_SetMessageCountLimit(ID3D11InfoQueue* This,UINT64 MessageCountLimit) {
    return This->lpVtbl->SetMessageCountLimit(This,MessageCountLimit);
}
static inline void ID3D11InfoQueue_ClearStoredMessages(ID3D11InfoQueue* This) {
    This->lpVtbl->ClearStoredMessages(This);
}
static inline HRESULT ID3D11InfoQueue_GetMessage(ID3D11InfoQueue* This,UINT64 MessageIndex,D3D11_MESSAGE *pMessage,SIZE_T *pMessageByteLength) {
    return This->lpVtbl->GetMessage(This,MessageIndex,pMessage,pMessageByteLength);
}
static inline UINT64 ID3D11InfoQueue_GetNumMessagesAllowedByStorageFilter(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetNumMessagesAllowedByStorageFilter(This);
}
static inline UINT64 ID3D11InfoQueue_GetNumMessagesDeniedByStorageFilter(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetNumMessagesDeniedByStorageFilter(This);
}
static inline UINT64 ID3D11InfoQueue_GetNumStoredMessages(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetNumStoredMessages(This);
}
static inline UINT64 ID3D11InfoQueue_GetNumStoredMessagesAllowedByRetrievalFilter(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetNumStoredMessagesAllowedByRetrievalFilter(This);
}
static inline UINT64 ID3D11InfoQueue_GetNumMessagesDiscardedByMessageCountLimit(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetNumMessagesDiscardedByMessageCountLimit(This);
}
static inline UINT64 ID3D11InfoQueue_GetMessageCountLimit(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetMessageCountLimit(This);
}
static inline HRESULT ID3D11InfoQueue_AddStorageFilterEntries(ID3D11InfoQueue* This,D3D11_INFO_QUEUE_FILTER *pFilter) {
    return This->lpVtbl->AddStorageFilterEntries(This,pFilter);
}
static inline HRESULT ID3D11InfoQueue_GetStorageFilter(ID3D11InfoQueue* This,D3D11_INFO_QUEUE_FILTER *pFilter,SIZE_T *pFilterByteLength) {
    return This->lpVtbl->GetStorageFilter(This,pFilter,pFilterByteLength);
}
static inline void ID3D11InfoQueue_ClearStorageFilter(ID3D11InfoQueue* This) {
    This->lpVtbl->ClearStorageFilter(This);
}
static inline HRESULT ID3D11InfoQueue_PushEmptyStorageFilter(ID3D11InfoQueue* This) {
    return This->lpVtbl->PushEmptyStorageFilter(This);
}
static inline HRESULT ID3D11InfoQueue_PushCopyOfStorageFilter(ID3D11InfoQueue* This) {
    return This->lpVtbl->PushCopyOfStorageFilter(This);
}
static inline HRESULT ID3D11InfoQueue_PushStorageFilter(ID3D11InfoQueue* This,D3D11_INFO_QUEUE_FILTER *pFilter) {
    return This->lpVtbl->PushStorageFilter(This,pFilter);
}
static inline void ID3D11InfoQueue_PopStorageFilter(ID3D11InfoQueue* This) {
    This->lpVtbl->PopStorageFilter(This);
}
static inline UINT ID3D11InfoQueue_GetStorageFilterStackSize(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetStorageFilterStackSize(This);
}
static inline HRESULT ID3D11InfoQueue_AddRetrievalFilterEntries(ID3D11InfoQueue* This,D3D11_INFO_QUEUE_FILTER *pFilter) {
    return This->lpVtbl->AddRetrievalFilterEntries(This,pFilter);
}
static inline HRESULT ID3D11InfoQueue_GetRetrievalFilter(ID3D11InfoQueue* This,D3D11_INFO_QUEUE_FILTER *pFilter,SIZE_T *pFilterByteLength) {
    return This->lpVtbl->GetRetrievalFilter(This,pFilter,pFilterByteLength);
}
static inline void ID3D11InfoQueue_ClearRetrievalFilter(ID3D11InfoQueue* This) {
    This->lpVtbl->ClearRetrievalFilter(This);
}
static inline HRESULT ID3D11InfoQueue_PushEmptyRetrievalFilter(ID3D11InfoQueue* This) {
    return This->lpVtbl->PushEmptyRetrievalFilter(This);
}
static inline HRESULT ID3D11InfoQueue_PushCopyOfRetrievalFilter(ID3D11InfoQueue* This) {
    return This->lpVtbl->PushCopyOfRetrievalFilter(This);
}
static inline HRESULT ID3D11InfoQueue_PushRetrievalFilter(ID3D11InfoQueue* This,D3D11_INFO_QUEUE_FILTER *pFilter) {
    return This->lpVtbl->PushRetrievalFilter(This,pFilter);
}
static inline void ID3D11InfoQueue_PopRetrievalFilter(ID3D11InfoQueue* This) {
    This->lpVtbl->PopRetrievalFilter(This);
}
static inline UINT ID3D11InfoQueue_GetRetrievalFilterStackSize(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetRetrievalFilterStackSize(This);
}
static inline HRESULT ID3D11InfoQueue_AddMessage(ID3D11InfoQueue* This,D3D11_MESSAGE_CATEGORY Category,D3D11_MESSAGE_SEVERITY Severity,D3D11_MESSAGE_ID ID,LPCSTR pDescription) {
    return This->lpVtbl->AddMessage(This,Category,Severity,ID,pDescription);
}
static inline HRESULT ID3D11InfoQueue_AddApplicationMessage(ID3D11InfoQueue* This,D3D11_MESSAGE_SEVERITY Severity,LPCSTR pDescription) {
    return This->lpVtbl->AddApplicationMessage(This,Severity,pDescription);
}
static inline HRESULT ID3D11InfoQueue_SetBreakOnCategory(ID3D11InfoQueue* This,D3D11_MESSAGE_CATEGORY Category,WINBOOL bEnable) {
    return This->lpVtbl->SetBreakOnCategory(This,Category,bEnable);
}
static inline HRESULT ID3D11InfoQueue_SetBreakOnSeverity(ID3D11InfoQueue* This,D3D11_MESSAGE_SEVERITY Severity,WINBOOL bEnable) {
    return This->lpVtbl->SetBreakOnSeverity(This,Severity,bEnable);
}
static inline HRESULT ID3D11InfoQueue_SetBreakOnID(ID3D11InfoQueue* This,D3D11_MESSAGE_ID ID,WINBOOL bEnable) {
    return This->lpVtbl->SetBreakOnID(This,ID,bEnable);
}
static inline WINBOOL ID3D11InfoQueue_GetBreakOnCategory(ID3D11InfoQueue* This,D3D11_MESSAGE_CATEGORY Category) {
    return This->lpVtbl->GetBreakOnCategory(This,Category);
}
static inline WINBOOL ID3D11InfoQueue_GetBreakOnSeverity(ID3D11InfoQueue* This,D3D11_MESSAGE_SEVERITY Severity) {
    return This->lpVtbl->GetBreakOnSeverity(This,Severity);
}
static inline WINBOOL ID3D11InfoQueue_GetBreakOnID(ID3D11InfoQueue* This,D3D11_MESSAGE_ID ID) {
    return This->lpVtbl->GetBreakOnID(This,ID);
}
static inline void ID3D11InfoQueue_SetMuteDebugOutput(ID3D11InfoQueue* This,WINBOOL bMute) {
    This->lpVtbl->SetMuteDebugOutput(This,bMute);
}
static inline WINBOOL ID3D11InfoQueue_GetMuteDebugOutput(ID3D11InfoQueue* This) {
    return This->lpVtbl->GetMuteDebugOutput(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11InfoQueue_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d11sdklayers_h__ */
