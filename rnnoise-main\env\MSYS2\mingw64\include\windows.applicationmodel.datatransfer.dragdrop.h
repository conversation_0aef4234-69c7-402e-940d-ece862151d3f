/*** Autogenerated by WIDL 10.12 from include/windows.applicationmodel.datatransfer.dragdrop.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_applicationmodel_datatransfer_dragdrop_h__
#define __windows_applicationmodel_datatransfer_dragdrop_h__

/* Forward declarations */

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CDataTransfer_CDragDrop_CDragDropModifiers_ENUM_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CDataTransfer_CDragDrop_CDragDropModifiers_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace DataTransfer {
                namespace DragDrop {
                    enum DragDropModifiers {
                        DragDropModifiers_None = 0x0,
                        DragDropModifiers_Shift = 0x1,
                        DragDropModifiers_Control = 0x2,
                        DragDropModifiers_Alt = 0x4,
                        DragDropModifiers_LeftButton = 0x8,
                        DragDropModifiers_MiddleButton = 0x10,
                        DragDropModifiers_RightButton = 0x20
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CApplicationModel_CDataTransfer_CDragDrop_CDragDropModifiers {
    DragDropModifiers_None = 0x0,
    DragDropModifiers_Shift = 0x1,
    DragDropModifiers_Control = 0x2,
    DragDropModifiers_Alt = 0x4,
    DragDropModifiers_LeftButton = 0x8,
    DragDropModifiers_MiddleButton = 0x10,
    DragDropModifiers_RightButton = 0x20
};
#ifdef WIDL_using_Windows_ApplicationModel_DataTransfer_DragDrop
#define DragDropModifiers __x_ABI_CWindows_CApplicationModel_CDataTransfer_CDragDrop_CDragDropModifiers
#endif /* WIDL_using_Windows_ApplicationModel_DataTransfer_DragDrop */
#endif

#endif /* ____x_ABI_CWindows_CApplicationModel_CDataTransfer_CDragDrop_CDragDropModifiers_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CApplicationModel_CDataTransfer_CDragDrop_CDragDropModifiers __x_ABI_CWindows_CApplicationModel_CDataTransfer_CDragDrop_CDragDropModifiers;
#endif /* __cplusplus */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_applicationmodel_datatransfer_dragdrop_h__ */
