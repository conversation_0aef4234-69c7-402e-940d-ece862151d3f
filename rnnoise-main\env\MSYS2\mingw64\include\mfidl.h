/*** Autogenerated by WIDL 10.12 from include/mfidl.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mfidl_h__
#define __mfidl_h__

/* Forward declarations */

#ifndef __IMFTopologyNode_FWD_DEFINED__
#define __IMFTopologyNode_FWD_DEFINED__
typedef interface IMFTopologyNode IMFTopologyNode;
#ifdef __cplusplus
interface IMFTopologyNode;
#endif /* __cplusplus */
#endif

#ifndef __IMFTopology_FWD_DEFINED__
#define __IMFTopology_FWD_DEFINED__
typedef interface IMFTopology IMFTopology;
#ifdef __cplusplus
interface IMFTopology;
#endif /* __cplusplus */
#endif

#ifndef __IMFGetService_FWD_DEFINED__
#define __IMFGetService_FWD_DEFINED__
typedef interface IMFGetService IMFGetService;
#ifdef __cplusplus
interface IMFGetService;
#endif /* __cplusplus */
#endif

#ifndef __IMFClock_FWD_DEFINED__
#define __IMFClock_FWD_DEFINED__
typedef interface IMFClock IMFClock;
#ifdef __cplusplus
interface IMFClock;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaSession_FWD_DEFINED__
#define __IMFMediaSession_FWD_DEFINED__
typedef interface IMFMediaSession IMFMediaSession;
#ifdef __cplusplus
interface IMFMediaSession;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaTypeHandler_FWD_DEFINED__
#define __IMFMediaTypeHandler_FWD_DEFINED__
typedef interface IMFMediaTypeHandler IMFMediaTypeHandler;
#ifdef __cplusplus
interface IMFMediaTypeHandler;
#endif /* __cplusplus */
#endif

#ifndef __IMFStreamDescriptor_FWD_DEFINED__
#define __IMFStreamDescriptor_FWD_DEFINED__
typedef interface IMFStreamDescriptor IMFStreamDescriptor;
#ifdef __cplusplus
interface IMFStreamDescriptor;
#endif /* __cplusplus */
#endif

#ifndef __IMFPresentationDescriptor_FWD_DEFINED__
#define __IMFPresentationDescriptor_FWD_DEFINED__
typedef interface IMFPresentationDescriptor IMFPresentationDescriptor;
#ifdef __cplusplus
interface IMFPresentationDescriptor;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaSource_FWD_DEFINED__
#define __IMFMediaSource_FWD_DEFINED__
typedef interface IMFMediaSource IMFMediaSource;
#ifdef __cplusplus
interface IMFMediaSource;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaSourceEx_FWD_DEFINED__
#define __IMFMediaSourceEx_FWD_DEFINED__
typedef interface IMFMediaSourceEx IMFMediaSourceEx;
#ifdef __cplusplus
interface IMFMediaSourceEx;
#endif /* __cplusplus */
#endif

#ifndef __IMFByteStreamBuffering_FWD_DEFINED__
#define __IMFByteStreamBuffering_FWD_DEFINED__
typedef interface IMFByteStreamBuffering IMFByteStreamBuffering;
#ifdef __cplusplus
interface IMFByteStreamBuffering;
#endif /* __cplusplus */
#endif

#ifndef __IMFClockStateSink_FWD_DEFINED__
#define __IMFClockStateSink_FWD_DEFINED__
typedef interface IMFClockStateSink IMFClockStateSink;
#ifdef __cplusplus
interface IMFClockStateSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFAudioStreamVolume_FWD_DEFINED__
#define __IMFAudioStreamVolume_FWD_DEFINED__
typedef interface IMFAudioStreamVolume IMFAudioStreamVolume;
#ifdef __cplusplus
interface IMFAudioStreamVolume;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaSink_FWD_DEFINED__
#define __IMFMediaSink_FWD_DEFINED__
typedef interface IMFMediaSink IMFMediaSink;
#ifdef __cplusplus
interface IMFMediaSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFFinalizableMediaSink_FWD_DEFINED__
#define __IMFFinalizableMediaSink_FWD_DEFINED__
typedef interface IMFFinalizableMediaSink IMFFinalizableMediaSink;
#ifdef __cplusplus
interface IMFFinalizableMediaSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaSinkPreroll_FWD_DEFINED__
#define __IMFMediaSinkPreroll_FWD_DEFINED__
typedef interface IMFMediaSinkPreroll IMFMediaSinkPreroll;
#ifdef __cplusplus
interface IMFMediaSinkPreroll;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaStream_FWD_DEFINED__
#define __IMFMediaStream_FWD_DEFINED__
typedef interface IMFMediaStream IMFMediaStream;
#ifdef __cplusplus
interface IMFMediaStream;
#endif /* __cplusplus */
#endif

#ifndef __IMFMetadata_FWD_DEFINED__
#define __IMFMetadata_FWD_DEFINED__
typedef interface IMFMetadata IMFMetadata;
#ifdef __cplusplus
interface IMFMetadata;
#endif /* __cplusplus */
#endif

#ifndef __IMFMetadataProvider_FWD_DEFINED__
#define __IMFMetadataProvider_FWD_DEFINED__
typedef interface IMFMetadataProvider IMFMetadataProvider;
#ifdef __cplusplus
interface IMFMetadataProvider;
#endif /* __cplusplus */
#endif

#ifndef __IMFPresentationTimeSource_FWD_DEFINED__
#define __IMFPresentationTimeSource_FWD_DEFINED__
typedef interface IMFPresentationTimeSource IMFPresentationTimeSource;
#ifdef __cplusplus
interface IMFPresentationTimeSource;
#endif /* __cplusplus */
#endif

#ifndef __IMFPresentationClock_FWD_DEFINED__
#define __IMFPresentationClock_FWD_DEFINED__
typedef interface IMFPresentationClock IMFPresentationClock;
#ifdef __cplusplus
interface IMFPresentationClock;
#endif /* __cplusplus */
#endif

#ifndef __IMFRateControl_FWD_DEFINED__
#define __IMFRateControl_FWD_DEFINED__
typedef interface IMFRateControl IMFRateControl;
#ifdef __cplusplus
interface IMFRateControl;
#endif /* __cplusplus */
#endif

#ifndef __IMFRateSupport_FWD_DEFINED__
#define __IMFRateSupport_FWD_DEFINED__
typedef interface IMFRateSupport IMFRateSupport;
#ifdef __cplusplus
interface IMFRateSupport;
#endif /* __cplusplus */
#endif

#ifndef __IMFSampleGrabberSinkCallback_FWD_DEFINED__
#define __IMFSampleGrabberSinkCallback_FWD_DEFINED__
typedef interface IMFSampleGrabberSinkCallback IMFSampleGrabberSinkCallback;
#ifdef __cplusplus
interface IMFSampleGrabberSinkCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFShutdown_FWD_DEFINED__
#define __IMFShutdown_FWD_DEFINED__
typedef interface IMFShutdown IMFShutdown;
#ifdef __cplusplus
interface IMFShutdown;
#endif /* __cplusplus */
#endif

#ifndef __IMFSimpleAudioVolume_FWD_DEFINED__
#define __IMFSimpleAudioVolume_FWD_DEFINED__
typedef interface IMFSimpleAudioVolume IMFSimpleAudioVolume;
#ifdef __cplusplus
interface IMFSimpleAudioVolume;
#endif /* __cplusplus */
#endif

#ifndef __IMFSourceResolver_FWD_DEFINED__
#define __IMFSourceResolver_FWD_DEFINED__
typedef interface IMFSourceResolver IMFSourceResolver;
#ifdef __cplusplus
interface IMFSourceResolver;
#endif /* __cplusplus */
#endif

#ifndef __IMFStreamSink_FWD_DEFINED__
#define __IMFStreamSink_FWD_DEFINED__
typedef interface IMFStreamSink IMFStreamSink;
#ifdef __cplusplus
interface IMFStreamSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFTimer_FWD_DEFINED__
#define __IMFTimer_FWD_DEFINED__
typedef interface IMFTimer IMFTimer;
#ifdef __cplusplus
interface IMFTimer;
#endif /* __cplusplus */
#endif

#ifndef __IMFTopoLoader_FWD_DEFINED__
#define __IMFTopoLoader_FWD_DEFINED__
typedef interface IMFTopoLoader IMFTopoLoader;
#ifdef __cplusplus
interface IMFTopoLoader;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoSampleAllocator_FWD_DEFINED__
#define __IMFVideoSampleAllocator_FWD_DEFINED__
typedef interface IMFVideoSampleAllocator IMFVideoSampleAllocator;
#ifdef __cplusplus
interface IMFVideoSampleAllocator;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoSampleAllocatorNotify_FWD_DEFINED__
#define __IMFVideoSampleAllocatorNotify_FWD_DEFINED__
typedef interface IMFVideoSampleAllocatorNotify IMFVideoSampleAllocatorNotify;
#ifdef __cplusplus
interface IMFVideoSampleAllocatorNotify;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoSampleAllocatorNotifyEx_FWD_DEFINED__
#define __IMFVideoSampleAllocatorNotifyEx_FWD_DEFINED__
typedef interface IMFVideoSampleAllocatorNotifyEx IMFVideoSampleAllocatorNotifyEx;
#ifdef __cplusplus
interface IMFVideoSampleAllocatorNotifyEx;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoSampleAllocatorCallback_FWD_DEFINED__
#define __IMFVideoSampleAllocatorCallback_FWD_DEFINED__
typedef interface IMFVideoSampleAllocatorCallback IMFVideoSampleAllocatorCallback;
#ifdef __cplusplus
interface IMFVideoSampleAllocatorCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoSampleAllocatorEx_FWD_DEFINED__
#define __IMFVideoSampleAllocatorEx_FWD_DEFINED__
typedef interface IMFVideoSampleAllocatorEx IMFVideoSampleAllocatorEx;
#ifdef __cplusplus
interface IMFVideoSampleAllocatorEx;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoProcessorControl_FWD_DEFINED__
#define __IMFVideoProcessorControl_FWD_DEFINED__
typedef interface IMFVideoProcessorControl IMFVideoProcessorControl;
#ifdef __cplusplus
interface IMFVideoProcessorControl;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoProcessorControl2_FWD_DEFINED__
#define __IMFVideoProcessorControl2_FWD_DEFINED__
typedef interface IMFVideoProcessorControl2 IMFVideoProcessorControl2;
#ifdef __cplusplus
interface IMFVideoProcessorControl2;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoProcessorControl3_FWD_DEFINED__
#define __IMFVideoProcessorControl3_FWD_DEFINED__
typedef interface IMFVideoProcessorControl3 IMFVideoProcessorControl3;
#ifdef __cplusplus
interface IMFVideoProcessorControl3;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoRendererEffectControl_FWD_DEFINED__
#define __IMFVideoRendererEffectControl_FWD_DEFINED__
typedef interface IMFVideoRendererEffectControl IMFVideoRendererEffectControl;
#ifdef __cplusplus
interface IMFVideoRendererEffectControl;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <mfobjects.h>
#include <mftransform.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <windef.h>
typedef enum MFSESSION_SETTOPOLOGY_FLAGS {
    MFSESSION_SETTOPOLOGY_IMMEDIATE = 0x1,
    MFSESSION_SETTOPOLOGY_NORESOLUTION = 0x2,
    MFSESSION_SETTOPOLOGY_CLEAR_CURRENT = 0x4
} MFSESSION_SETTOPOLOGY_FLAGS;
typedef enum MFSESSION_GETFULLTOPOLOGY_FLAGS {
    MFSESSION_GETFULLTOPOLOGY_CURRENT = 0x1
} MFSESSION_GETFULLTOPOLOGY_FLAGS;
typedef enum MFPMPSESSION_CREATION_FLAGS {
    MFPMPSESSION_UNPROTECTED_PROCESS = 0x1,
    MFPMPSESSION_IN_PROCESS = 0x2
} MFPMPSESSION_CREATION_FLAGS;
typedef UINT64 TOPOID;
typedef enum MF_TOPOLOGY_TYPE {
    MF_TOPOLOGY_OUTPUT_NODE = 0,
    MF_TOPOLOGY_SOURCESTREAM_NODE = 1,
    MF_TOPOLOGY_TRANSFORM_NODE = 2,
    MF_TOPOLOGY_TEE_NODE = 3,
    MF_TOPOLOGY_MAX = 0xffffffff
} MF_TOPOLOGY_TYPE;
/*****************************************************************************
 * IMFTopologyNode interface
 */
#ifndef __IMFTopologyNode_INTERFACE_DEFINED__
#define __IMFTopologyNode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTopologyNode, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83cf873a-f6da-4bc8-823f-bacfd55dc430")
IMFTopologyNode : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE SetObject(
        IUnknown *pObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObject(
        IUnknown **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeType(
        MF_TOPOLOGY_TYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTopoNodeID(
        TOPOID *pID) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTopoNodeID(
        TOPOID ullTopoID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputCount(
        DWORD *pcInputs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputCount(
        DWORD *pcOutputs) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConnectOutput(
        DWORD dwOutputIndex,
        IMFTopologyNode *pDownstreamNode,
        DWORD dwInputIndexOnDownstreamNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisconnectOutput(
        DWORD dwOutputIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInput(
        DWORD dwInputIndex,
        IMFTopologyNode **ppUpstreamNode,
        DWORD *pdwOutputIndexOnUpstreamNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutput(
        DWORD dwOutputIndex,
        IMFTopologyNode **ppDownstreamNode,
        DWORD *pdwInputIndexOnDownstreamNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputPrefType(
        DWORD dwOutputIndex,
        IMFMediaType *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputPrefType(
        DWORD dwOutputIndex,
        IMFMediaType **ppType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInputPrefType(
        DWORD dwInputIndex,
        IMFMediaType *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputPrefType(
        DWORD dwInputIndex,
        IMFMediaType **ppType) = 0;

    virtual HRESULT STDMETHODCALLTYPE CloneFrom(
        IMFTopologyNode *pNode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTopologyNode, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x30)
#endif
#else
typedef struct IMFTopologyNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTopologyNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTopologyNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTopologyNode *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFTopologyNode *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFTopologyNode *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFTopologyNode *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFTopologyNode *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFTopologyNode *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFTopologyNode *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFTopologyNode *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFTopologyNode *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFTopologyNode *This,
        IMFAttributes *pDest);

    /*** IMFTopologyNode methods ***/
    HRESULT (STDMETHODCALLTYPE *SetObject)(
        IMFTopologyNode *This,
        IUnknown *pObject);

    HRESULT (STDMETHODCALLTYPE *GetObject)(
        IMFTopologyNode *This,
        IUnknown **ppObject);

    HRESULT (STDMETHODCALLTYPE *GetNodeType)(
        IMFTopologyNode *This,
        MF_TOPOLOGY_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *GetTopoNodeID)(
        IMFTopologyNode *This,
        TOPOID *pID);

    HRESULT (STDMETHODCALLTYPE *SetTopoNodeID)(
        IMFTopologyNode *This,
        TOPOID ullTopoID);

    HRESULT (STDMETHODCALLTYPE *GetInputCount)(
        IMFTopologyNode *This,
        DWORD *pcInputs);

    HRESULT (STDMETHODCALLTYPE *GetOutputCount)(
        IMFTopologyNode *This,
        DWORD *pcOutputs);

    HRESULT (STDMETHODCALLTYPE *ConnectOutput)(
        IMFTopologyNode *This,
        DWORD dwOutputIndex,
        IMFTopologyNode *pDownstreamNode,
        DWORD dwInputIndexOnDownstreamNode);

    HRESULT (STDMETHODCALLTYPE *DisconnectOutput)(
        IMFTopologyNode *This,
        DWORD dwOutputIndex);

    HRESULT (STDMETHODCALLTYPE *GetInput)(
        IMFTopologyNode *This,
        DWORD dwInputIndex,
        IMFTopologyNode **ppUpstreamNode,
        DWORD *pdwOutputIndexOnUpstreamNode);

    HRESULT (STDMETHODCALLTYPE *GetOutput)(
        IMFTopologyNode *This,
        DWORD dwOutputIndex,
        IMFTopologyNode **ppDownstreamNode,
        DWORD *pdwInputIndexOnDownstreamNode);

    HRESULT (STDMETHODCALLTYPE *SetOutputPrefType)(
        IMFTopologyNode *This,
        DWORD dwOutputIndex,
        IMFMediaType *pType);

    HRESULT (STDMETHODCALLTYPE *GetOutputPrefType)(
        IMFTopologyNode *This,
        DWORD dwOutputIndex,
        IMFMediaType **ppType);

    HRESULT (STDMETHODCALLTYPE *SetInputPrefType)(
        IMFTopologyNode *This,
        DWORD dwInputIndex,
        IMFMediaType *pType);

    HRESULT (STDMETHODCALLTYPE *GetInputPrefType)(
        IMFTopologyNode *This,
        DWORD dwInputIndex,
        IMFMediaType **ppType);

    HRESULT (STDMETHODCALLTYPE *CloneFrom)(
        IMFTopologyNode *This,
        IMFTopologyNode *pNode);

    END_INTERFACE
} IMFTopologyNodeVtbl;

interface IMFTopologyNode {
    CONST_VTBL IMFTopologyNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTopologyNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTopologyNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTopologyNode_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFTopologyNode_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFTopologyNode_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFTopologyNode_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFTopologyNode_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFTopologyNode_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFTopologyNode_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFTopologyNode_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFTopologyNode_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFTopologyNode_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFTopologyNode_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFTopologyNode_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFTopologyNode_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFTopologyNode_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFTopologyNode_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFTopologyNode_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFTopologyNode_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFTopologyNode_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFTopologyNode_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFTopologyNode_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFTopologyNode_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFTopologyNode_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFTopologyNode_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFTopologyNode_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFTopologyNode_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFTopologyNode_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFTopologyNode_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFTopologyNode_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFTopologyNode_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFTopologyNode_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFTopologyNode_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFTopologyNode methods ***/
#define IMFTopologyNode_SetObject(This,pObject) (This)->lpVtbl->SetObject(This,pObject)
#define IMFTopologyNode_GetObject(This,ppObject) (This)->lpVtbl->GetObject(This,ppObject)
#define IMFTopologyNode_GetNodeType(This,pType) (This)->lpVtbl->GetNodeType(This,pType)
#define IMFTopologyNode_GetTopoNodeID(This,pID) (This)->lpVtbl->GetTopoNodeID(This,pID)
#define IMFTopologyNode_SetTopoNodeID(This,ullTopoID) (This)->lpVtbl->SetTopoNodeID(This,ullTopoID)
#define IMFTopologyNode_GetInputCount(This,pcInputs) (This)->lpVtbl->GetInputCount(This,pcInputs)
#define IMFTopologyNode_GetOutputCount(This,pcOutputs) (This)->lpVtbl->GetOutputCount(This,pcOutputs)
#define IMFTopologyNode_ConnectOutput(This,dwOutputIndex,pDownstreamNode,dwInputIndexOnDownstreamNode) (This)->lpVtbl->ConnectOutput(This,dwOutputIndex,pDownstreamNode,dwInputIndexOnDownstreamNode)
#define IMFTopologyNode_DisconnectOutput(This,dwOutputIndex) (This)->lpVtbl->DisconnectOutput(This,dwOutputIndex)
#define IMFTopologyNode_GetInput(This,dwInputIndex,ppUpstreamNode,pdwOutputIndexOnUpstreamNode) (This)->lpVtbl->GetInput(This,dwInputIndex,ppUpstreamNode,pdwOutputIndexOnUpstreamNode)
#define IMFTopologyNode_GetOutput(This,dwOutputIndex,ppDownstreamNode,pdwInputIndexOnDownstreamNode) (This)->lpVtbl->GetOutput(This,dwOutputIndex,ppDownstreamNode,pdwInputIndexOnDownstreamNode)
#define IMFTopologyNode_SetOutputPrefType(This,dwOutputIndex,pType) (This)->lpVtbl->SetOutputPrefType(This,dwOutputIndex,pType)
#define IMFTopologyNode_GetOutputPrefType(This,dwOutputIndex,ppType) (This)->lpVtbl->GetOutputPrefType(This,dwOutputIndex,ppType)
#define IMFTopologyNode_SetInputPrefType(This,dwInputIndex,pType) (This)->lpVtbl->SetInputPrefType(This,dwInputIndex,pType)
#define IMFTopologyNode_GetInputPrefType(This,dwInputIndex,ppType) (This)->lpVtbl->GetInputPrefType(This,dwInputIndex,ppType)
#define IMFTopologyNode_CloneFrom(This,pNode) (This)->lpVtbl->CloneFrom(This,pNode)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFTopologyNode_QueryInterface(IMFTopologyNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFTopologyNode_AddRef(IMFTopologyNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFTopologyNode_Release(IMFTopologyNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFTopologyNode_GetItem(IMFTopologyNode* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFTopologyNode_GetItemType(IMFTopologyNode* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFTopologyNode_CompareItem(IMFTopologyNode* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFTopologyNode_Compare(IMFTopologyNode* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFTopologyNode_GetUINT32(IMFTopologyNode* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFTopologyNode_GetUINT64(IMFTopologyNode* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFTopologyNode_GetDouble(IMFTopologyNode* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFTopologyNode_GetGUID(IMFTopologyNode* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFTopologyNode_GetStringLength(IMFTopologyNode* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFTopologyNode_GetString(IMFTopologyNode* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFTopologyNode_GetAllocatedString(IMFTopologyNode* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFTopologyNode_GetBlobSize(IMFTopologyNode* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFTopologyNode_GetBlob(IMFTopologyNode* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFTopologyNode_GetAllocatedBlob(IMFTopologyNode* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFTopologyNode_GetUnknown(IMFTopologyNode* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFTopologyNode_SetItem(IMFTopologyNode* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFTopologyNode_DeleteItem(IMFTopologyNode* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFTopologyNode_DeleteAllItems(IMFTopologyNode* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFTopologyNode_SetUINT32(IMFTopologyNode* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFTopologyNode_SetUINT64(IMFTopologyNode* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFTopologyNode_SetDouble(IMFTopologyNode* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFTopologyNode_SetGUID(IMFTopologyNode* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFTopologyNode_SetString(IMFTopologyNode* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFTopologyNode_SetBlob(IMFTopologyNode* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFTopologyNode_SetUnknown(IMFTopologyNode* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFTopologyNode_LockStore(IMFTopologyNode* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFTopologyNode_UnlockStore(IMFTopologyNode* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFTopologyNode_GetCount(IMFTopologyNode* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFTopologyNode_GetItemByIndex(IMFTopologyNode* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFTopologyNode_CopyAllItems(IMFTopologyNode* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFTopologyNode methods ***/
static inline HRESULT IMFTopologyNode_SetObject(IMFTopologyNode* This,IUnknown *pObject) {
    return This->lpVtbl->SetObject(This,pObject);
}
static inline HRESULT IMFTopologyNode_GetObject(IMFTopologyNode* This,IUnknown **ppObject) {
    return This->lpVtbl->GetObject(This,ppObject);
}
static inline HRESULT IMFTopologyNode_GetNodeType(IMFTopologyNode* This,MF_TOPOLOGY_TYPE *pType) {
    return This->lpVtbl->GetNodeType(This,pType);
}
static inline HRESULT IMFTopologyNode_GetTopoNodeID(IMFTopologyNode* This,TOPOID *pID) {
    return This->lpVtbl->GetTopoNodeID(This,pID);
}
static inline HRESULT IMFTopologyNode_SetTopoNodeID(IMFTopologyNode* This,TOPOID ullTopoID) {
    return This->lpVtbl->SetTopoNodeID(This,ullTopoID);
}
static inline HRESULT IMFTopologyNode_GetInputCount(IMFTopologyNode* This,DWORD *pcInputs) {
    return This->lpVtbl->GetInputCount(This,pcInputs);
}
static inline HRESULT IMFTopologyNode_GetOutputCount(IMFTopologyNode* This,DWORD *pcOutputs) {
    return This->lpVtbl->GetOutputCount(This,pcOutputs);
}
static inline HRESULT IMFTopologyNode_ConnectOutput(IMFTopologyNode* This,DWORD dwOutputIndex,IMFTopologyNode *pDownstreamNode,DWORD dwInputIndexOnDownstreamNode) {
    return This->lpVtbl->ConnectOutput(This,dwOutputIndex,pDownstreamNode,dwInputIndexOnDownstreamNode);
}
static inline HRESULT IMFTopologyNode_DisconnectOutput(IMFTopologyNode* This,DWORD dwOutputIndex) {
    return This->lpVtbl->DisconnectOutput(This,dwOutputIndex);
}
static inline HRESULT IMFTopologyNode_GetInput(IMFTopologyNode* This,DWORD dwInputIndex,IMFTopologyNode **ppUpstreamNode,DWORD *pdwOutputIndexOnUpstreamNode) {
    return This->lpVtbl->GetInput(This,dwInputIndex,ppUpstreamNode,pdwOutputIndexOnUpstreamNode);
}
static inline HRESULT IMFTopologyNode_GetOutput(IMFTopologyNode* This,DWORD dwOutputIndex,IMFTopologyNode **ppDownstreamNode,DWORD *pdwInputIndexOnDownstreamNode) {
    return This->lpVtbl->GetOutput(This,dwOutputIndex,ppDownstreamNode,pdwInputIndexOnDownstreamNode);
}
static inline HRESULT IMFTopologyNode_SetOutputPrefType(IMFTopologyNode* This,DWORD dwOutputIndex,IMFMediaType *pType) {
    return This->lpVtbl->SetOutputPrefType(This,dwOutputIndex,pType);
}
static inline HRESULT IMFTopologyNode_GetOutputPrefType(IMFTopologyNode* This,DWORD dwOutputIndex,IMFMediaType **ppType) {
    return This->lpVtbl->GetOutputPrefType(This,dwOutputIndex,ppType);
}
static inline HRESULT IMFTopologyNode_SetInputPrefType(IMFTopologyNode* This,DWORD dwInputIndex,IMFMediaType *pType) {
    return This->lpVtbl->SetInputPrefType(This,dwInputIndex,pType);
}
static inline HRESULT IMFTopologyNode_GetInputPrefType(IMFTopologyNode* This,DWORD dwInputIndex,IMFMediaType **ppType) {
    return This->lpVtbl->GetInputPrefType(This,dwInputIndex,ppType);
}
static inline HRESULT IMFTopologyNode_CloneFrom(IMFTopologyNode* This,IMFTopologyNode *pNode) {
    return This->lpVtbl->CloneFrom(This,pNode);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFTopologyNode_RemoteGetOutputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwOutputIndex,
    DWORD *pcbData,
    BYTE **ppbData);
void __RPC_STUB IMFTopologyNode_RemoteGetOutputPrefType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFTopologyNode_RemoteGetInputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwInputIndex,
    DWORD *pcbData,
    BYTE **ppbData);
void __RPC_STUB IMFTopologyNode_RemoteGetInputPrefType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFTopologyNode_GetOutputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwOutputIndex,
    IMFMediaType **ppType);
HRESULT __RPC_STUB IMFTopologyNode_GetOutputPrefType_Stub(
    IMFTopologyNode* This,
    DWORD dwOutputIndex,
    DWORD *pcbData,
    BYTE **ppbData);
HRESULT CALLBACK IMFTopologyNode_GetInputPrefType_Proxy(
    IMFTopologyNode* This,
    DWORD dwInputIndex,
    IMFMediaType **ppType);
HRESULT __RPC_STUB IMFTopologyNode_GetInputPrefType_Stub(
    IMFTopologyNode* This,
    DWORD dwInputIndex,
    DWORD *pcbData,
    BYTE **ppbData);

#endif  /* __IMFTopologyNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFTopology interface
 */
#ifndef __IMFTopology_INTERFACE_DEFINED__
#define __IMFTopology_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTopology, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83cf873a-f6da-4bc8-823f-bacfd55dc433")
IMFTopology : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetTopologyID(
        TOPOID *pID) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddNode(
        IMFTopologyNode *pNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveNode(
        IMFTopologyNode *pNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeCount(
        WORD *pwNodes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNode(
        WORD wIndex,
        IMFTopologyNode **ppNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CloneFrom(
        IMFTopology *pTopology) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeByID(
        TOPOID qwTopoNodeID,
        IMFTopologyNode **ppNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceNodeCollection(
        IMFCollection **ppCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputNodeCollection(
        IMFCollection **ppCollection) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTopology, 0x83cf873a, 0xf6da, 0x4bc8, 0x82,0x3f, 0xba,0xcf,0xd5,0x5d,0xc4,0x33)
#endif
#else
typedef struct IMFTopologyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTopology *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTopology *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTopology *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFTopology *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFTopology *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFTopology *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFTopology *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFTopology *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFTopology *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFTopology *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFTopology *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFTopology *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFTopology *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFTopology *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFTopology *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFTopology *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFTopology *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFTopology *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFTopology *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFTopology *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFTopology *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFTopology *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFTopology *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFTopology *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFTopology *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFTopology *This,
        IMFAttributes *pDest);

    /*** IMFTopology methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTopologyID)(
        IMFTopology *This,
        TOPOID *pID);

    HRESULT (STDMETHODCALLTYPE *AddNode)(
        IMFTopology *This,
        IMFTopologyNode *pNode);

    HRESULT (STDMETHODCALLTYPE *RemoveNode)(
        IMFTopology *This,
        IMFTopologyNode *pNode);

    HRESULT (STDMETHODCALLTYPE *GetNodeCount)(
        IMFTopology *This,
        WORD *pwNodes);

    HRESULT (STDMETHODCALLTYPE *GetNode)(
        IMFTopology *This,
        WORD wIndex,
        IMFTopologyNode **ppNode);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IMFTopology *This);

    HRESULT (STDMETHODCALLTYPE *CloneFrom)(
        IMFTopology *This,
        IMFTopology *pTopology);

    HRESULT (STDMETHODCALLTYPE *GetNodeByID)(
        IMFTopology *This,
        TOPOID qwTopoNodeID,
        IMFTopologyNode **ppNode);

    HRESULT (STDMETHODCALLTYPE *GetSourceNodeCollection)(
        IMFTopology *This,
        IMFCollection **ppCollection);

    HRESULT (STDMETHODCALLTYPE *GetOutputNodeCollection)(
        IMFTopology *This,
        IMFCollection **ppCollection);

    END_INTERFACE
} IMFTopologyVtbl;

interface IMFTopology {
    CONST_VTBL IMFTopologyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTopology_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTopology_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTopology_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFTopology_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFTopology_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFTopology_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFTopology_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFTopology_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFTopology_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFTopology_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFTopology_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFTopology_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFTopology_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFTopology_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFTopology_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFTopology_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFTopology_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFTopology_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFTopology_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFTopology_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFTopology_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFTopology_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFTopology_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFTopology_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFTopology_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFTopology_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFTopology_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFTopology_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFTopology_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFTopology_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFTopology_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFTopology_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFTopology_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFTopology methods ***/
#define IMFTopology_GetTopologyID(This,pID) (This)->lpVtbl->GetTopologyID(This,pID)
#define IMFTopology_AddNode(This,pNode) (This)->lpVtbl->AddNode(This,pNode)
#define IMFTopology_RemoveNode(This,pNode) (This)->lpVtbl->RemoveNode(This,pNode)
#define IMFTopology_GetNodeCount(This,pwNodes) (This)->lpVtbl->GetNodeCount(This,pwNodes)
#define IMFTopology_GetNode(This,wIndex,ppNode) (This)->lpVtbl->GetNode(This,wIndex,ppNode)
#define IMFTopology_Clear(This) (This)->lpVtbl->Clear(This)
#define IMFTopology_CloneFrom(This,pTopology) (This)->lpVtbl->CloneFrom(This,pTopology)
#define IMFTopology_GetNodeByID(This,qwTopoNodeID,ppNode) (This)->lpVtbl->GetNodeByID(This,qwTopoNodeID,ppNode)
#define IMFTopology_GetSourceNodeCollection(This,ppCollection) (This)->lpVtbl->GetSourceNodeCollection(This,ppCollection)
#define IMFTopology_GetOutputNodeCollection(This,ppCollection) (This)->lpVtbl->GetOutputNodeCollection(This,ppCollection)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFTopology_QueryInterface(IMFTopology* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFTopology_AddRef(IMFTopology* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFTopology_Release(IMFTopology* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFTopology_GetItem(IMFTopology* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFTopology_GetItemType(IMFTopology* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFTopology_CompareItem(IMFTopology* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFTopology_Compare(IMFTopology* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFTopology_GetUINT32(IMFTopology* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFTopology_GetUINT64(IMFTopology* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFTopology_GetDouble(IMFTopology* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFTopology_GetGUID(IMFTopology* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFTopology_GetStringLength(IMFTopology* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFTopology_GetString(IMFTopology* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFTopology_GetAllocatedString(IMFTopology* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFTopology_GetBlobSize(IMFTopology* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFTopology_GetBlob(IMFTopology* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFTopology_GetAllocatedBlob(IMFTopology* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFTopology_GetUnknown(IMFTopology* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFTopology_SetItem(IMFTopology* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFTopology_DeleteItem(IMFTopology* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFTopology_DeleteAllItems(IMFTopology* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFTopology_SetUINT32(IMFTopology* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFTopology_SetUINT64(IMFTopology* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFTopology_SetDouble(IMFTopology* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFTopology_SetGUID(IMFTopology* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFTopology_SetString(IMFTopology* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFTopology_SetBlob(IMFTopology* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFTopology_SetUnknown(IMFTopology* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFTopology_LockStore(IMFTopology* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFTopology_UnlockStore(IMFTopology* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFTopology_GetCount(IMFTopology* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFTopology_GetItemByIndex(IMFTopology* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFTopology_CopyAllItems(IMFTopology* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFTopology methods ***/
static inline HRESULT IMFTopology_GetTopologyID(IMFTopology* This,TOPOID *pID) {
    return This->lpVtbl->GetTopologyID(This,pID);
}
static inline HRESULT IMFTopology_AddNode(IMFTopology* This,IMFTopologyNode *pNode) {
    return This->lpVtbl->AddNode(This,pNode);
}
static inline HRESULT IMFTopology_RemoveNode(IMFTopology* This,IMFTopologyNode *pNode) {
    return This->lpVtbl->RemoveNode(This,pNode);
}
static inline HRESULT IMFTopology_GetNodeCount(IMFTopology* This,WORD *pwNodes) {
    return This->lpVtbl->GetNodeCount(This,pwNodes);
}
static inline HRESULT IMFTopology_GetNode(IMFTopology* This,WORD wIndex,IMFTopologyNode **ppNode) {
    return This->lpVtbl->GetNode(This,wIndex,ppNode);
}
static inline HRESULT IMFTopology_Clear(IMFTopology* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IMFTopology_CloneFrom(IMFTopology* This,IMFTopology *pTopology) {
    return This->lpVtbl->CloneFrom(This,pTopology);
}
static inline HRESULT IMFTopology_GetNodeByID(IMFTopology* This,TOPOID qwTopoNodeID,IMFTopologyNode **ppNode) {
    return This->lpVtbl->GetNodeByID(This,qwTopoNodeID,ppNode);
}
static inline HRESULT IMFTopology_GetSourceNodeCollection(IMFTopology* This,IMFCollection **ppCollection) {
    return This->lpVtbl->GetSourceNodeCollection(This,ppCollection);
}
static inline HRESULT IMFTopology_GetOutputNodeCollection(IMFTopology* This,IMFCollection **ppCollection) {
    return This->lpVtbl->GetOutputNodeCollection(This,ppCollection);
}
#endif
#endif

#endif


#endif  /* __IMFTopology_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFGetService interface
 */
#ifndef __IMFGetService_INTERFACE_DEFINED__
#define __IMFGetService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFGetService, 0xfa993888, 0x4383, 0x415a, 0xa9,0x30, 0xdd,0x47,0x2a,0x8c,0xf6,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa993888-4383-415a-a930-dd472a8cf6f7")
IMFGetService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetService(
        REFGUID guidService,
        REFIID riid,
        LPVOID *ppvObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFGetService, 0xfa993888, 0x4383, 0x415a, 0xa9,0x30, 0xdd,0x47,0x2a,0x8c,0xf6,0xf7)
#endif
#else
typedef struct IMFGetServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFGetService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFGetService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFGetService *This);

    /*** IMFGetService methods ***/
    HRESULT (STDMETHODCALLTYPE *GetService)(
        IMFGetService *This,
        REFGUID guidService,
        REFIID riid,
        LPVOID *ppvObject);

    END_INTERFACE
} IMFGetServiceVtbl;

interface IMFGetService {
    CONST_VTBL IMFGetServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFGetService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFGetService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFGetService_Release(This) (This)->lpVtbl->Release(This)
/*** IMFGetService methods ***/
#define IMFGetService_GetService(This,guidService,riid,ppvObject) (This)->lpVtbl->GetService(This,guidService,riid,ppvObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFGetService_QueryInterface(IMFGetService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFGetService_AddRef(IMFGetService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFGetService_Release(IMFGetService* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFGetService methods ***/
static inline HRESULT IMFGetService_GetService(IMFGetService* This,REFGUID guidService,REFIID riid,LPVOID *ppvObject) {
    return This->lpVtbl->GetService(This,guidService,riid,ppvObject);
}
#endif
#endif

#endif


#endif  /* __IMFGetService_INTERFACE_DEFINED__ */

typedef LONGLONG MFTIME;
typedef enum _MF_CLOCK_STATE {
    MFCLOCK_STATE_INVALID = 0,
    MFCLOCK_STATE_RUNNING = 1,
    MFCLOCK_STATE_STOPPED = 2,
    MFCLOCK_STATE_PAUSED = 3
} MF_CLOCK_STATE;
typedef enum _MF_CLOCK_STATE MFCLOCK_STATE;
typedef struct _MFCLOCK_PROPERTIES {
    UINT64 qwCorrelationRate;
    GUID guidClockId;
    DWORD dwClockFlags;
    UINT64 qwClockFrequency;
    DWORD dwClockTolerance;
    DWORD dwClockJitter;
} MFCLOCK_PROPERTIES;
/*****************************************************************************
 * IMFClock interface
 */
#ifndef __IMFClock_INTERFACE_DEFINED__
#define __IMFClock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFClock, 0x2eb1e945, 0x18b8, 0x4139, 0x9b,0x1a, 0xd5,0xd5,0x84,0x81,0x85,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2eb1e945-18b8-4139-9b1a-d5d584818530")
IMFClock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClockCharacteristics(
        DWORD *pdwCharacteristics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCorrelatedTime(
        DWORD dwReserved,
        LONGLONG *pllClockTime,
        MFTIME *phnsSystemTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContinuityKey(
        DWORD *pdwContinuityKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        DWORD dwReserved,
        MFCLOCK_STATE *peClockState) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperties(
        MFCLOCK_PROPERTIES *pClockProperties) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFClock, 0x2eb1e945, 0x18b8, 0x4139, 0x9b,0x1a, 0xd5,0xd5,0x84,0x81,0x85,0x30)
#endif
#else
typedef struct IMFClockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFClock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFClock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFClock *This);

    /*** IMFClock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClockCharacteristics)(
        IMFClock *This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *GetCorrelatedTime)(
        IMFClock *This,
        DWORD dwReserved,
        LONGLONG *pllClockTime,
        MFTIME *phnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *GetContinuityKey)(
        IMFClock *This,
        DWORD *pdwContinuityKey);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IMFClock *This,
        DWORD dwReserved,
        MFCLOCK_STATE *peClockState);

    HRESULT (STDMETHODCALLTYPE *GetProperties)(
        IMFClock *This,
        MFCLOCK_PROPERTIES *pClockProperties);

    END_INTERFACE
} IMFClockVtbl;

interface IMFClock {
    CONST_VTBL IMFClockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFClock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFClock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFClock_Release(This) (This)->lpVtbl->Release(This)
/*** IMFClock methods ***/
#define IMFClock_GetClockCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics)
#define IMFClock_GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime) (This)->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime)
#define IMFClock_GetContinuityKey(This,pdwContinuityKey) (This)->lpVtbl->GetContinuityKey(This,pdwContinuityKey)
#define IMFClock_GetState(This,dwReserved,peClockState) (This)->lpVtbl->GetState(This,dwReserved,peClockState)
#define IMFClock_GetProperties(This,pClockProperties) (This)->lpVtbl->GetProperties(This,pClockProperties)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFClock_QueryInterface(IMFClock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFClock_AddRef(IMFClock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFClock_Release(IMFClock* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFClock methods ***/
static inline HRESULT IMFClock_GetClockCharacteristics(IMFClock* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT IMFClock_GetCorrelatedTime(IMFClock* This,DWORD dwReserved,LONGLONG *pllClockTime,MFTIME *phnsSystemTime) {
    return This->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime);
}
static inline HRESULT IMFClock_GetContinuityKey(IMFClock* This,DWORD *pdwContinuityKey) {
    return This->lpVtbl->GetContinuityKey(This,pdwContinuityKey);
}
static inline HRESULT IMFClock_GetState(IMFClock* This,DWORD dwReserved,MFCLOCK_STATE *peClockState) {
    return This->lpVtbl->GetState(This,dwReserved,peClockState);
}
static inline HRESULT IMFClock_GetProperties(IMFClock* This,MFCLOCK_PROPERTIES *pClockProperties) {
    return This->lpVtbl->GetProperties(This,pClockProperties);
}
#endif
#endif

#endif


#endif  /* __IMFClock_INTERFACE_DEFINED__ */

#define SHA_HASH_LEN 20
#define STR_HASH_LEN (SHA_HASH_LEN*2+3)
typedef struct _MFRR_COMPONENT_HASH_INFO {
  DWORD ulReason;
  WCHAR rgHeaderHash[STR_HASH_LEN];
  WCHAR rgPublicKeyHash[STR_HASH_LEN];
  WCHAR wszName[MAX_PATH];
} MFRR_COMPONENT_HASH_INFO, *PMFRR_COMPONENT_HASH_INFO;
EXTERN_GUID(MF_PD_DURATION, 0x6c990d33,0xbb8e,0x477a,0x85,0x98,0xd,0x5d,0x96,0xfc,0xd8,0x8a);
typedef enum _MF_CONNECT_METHOD {
    MF_CONNECT_DIRECT = 0x0,
    MF_CONNECT_ALLOW_CONVERTER = 0x1,
    MF_CONNECT_ALLOW_DECODER = 0x3,
    MF_CONNECT_RESOLVE_INDEPENDENT_OUTPUTTYPES = 0x4,
    MF_CONNECT_AS_OPTIONAL = 0x10000,
    MF_CONNECT_AS_OPTIONAL_BRANCH = 0x20000
} MF_CONNECT_METHOD;
/*****************************************************************************
 * IMFMediaSession interface
 */
#ifndef __IMFMediaSession_INTERFACE_DEFINED__
#define __IMFMediaSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaSession, 0x90377834, 0x21d0, 0x4dee, 0x82,0x14, 0xba,0x2e,0x3e,0x6c,0x11,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("90377834-21d0-4dee-8214-ba2e3e6c1127")
IMFMediaSession : public IMFMediaEventGenerator
{
    virtual HRESULT STDMETHODCALLTYPE SetTopology(
        DWORD dwSetTopologyFlags,
        IMFTopology *pTopology) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearTopologies(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClock(
        IMFClock **ppClock) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionCapabilities(
        DWORD *pdwCaps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFullTopology(
        DWORD dwGetFullTopologyFlags,
        TOPOID TopoId,
        IMFTopology **ppFullTopology) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaSession, 0x90377834, 0x21d0, 0x4dee, 0x82,0x14, 0xba,0x2e,0x3e,0x6c,0x11,0x27)
#endif
#else
typedef struct IMFMediaSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaSession *This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaSession *This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaSession *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaSession *This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaSession *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    /*** IMFMediaSession methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTopology)(
        IMFMediaSession *This,
        DWORD dwSetTopologyFlags,
        IMFTopology *pTopology);

    HRESULT (STDMETHODCALLTYPE *ClearTopologies)(
        IMFMediaSession *This);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IMFMediaSession *This,
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFMediaSession *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMFMediaSession *This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IMFMediaSession *This);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaSession *This);

    HRESULT (STDMETHODCALLTYPE *GetClock)(
        IMFMediaSession *This,
        IMFClock **ppClock);

    HRESULT (STDMETHODCALLTYPE *GetSessionCapabilities)(
        IMFMediaSession *This,
        DWORD *pdwCaps);

    HRESULT (STDMETHODCALLTYPE *GetFullTopology)(
        IMFMediaSession *This,
        DWORD dwGetFullTopologyFlags,
        TOPOID TopoId,
        IMFTopology **ppFullTopology);

    END_INTERFACE
} IMFMediaSessionVtbl;

interface IMFMediaSession {
    CONST_VTBL IMFMediaSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaSession_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFMediaSession_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaSession_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaSession_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaSession_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
/*** IMFMediaSession methods ***/
#define IMFMediaSession_SetTopology(This,dwSetTopologyFlags,pTopology) (This)->lpVtbl->SetTopology(This,dwSetTopologyFlags,pTopology)
#define IMFMediaSession_ClearTopologies(This) (This)->lpVtbl->ClearTopologies(This)
#define IMFMediaSession_Start(This,pguidTimeFormat,pvarStartPosition) (This)->lpVtbl->Start(This,pguidTimeFormat,pvarStartPosition)
#define IMFMediaSession_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFMediaSession_Stop(This) (This)->lpVtbl->Stop(This)
#define IMFMediaSession_Close(This) (This)->lpVtbl->Close(This)
#define IMFMediaSession_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#define IMFMediaSession_GetClock(This,ppClock) (This)->lpVtbl->GetClock(This,ppClock)
#define IMFMediaSession_GetSessionCapabilities(This,pdwCaps) (This)->lpVtbl->GetSessionCapabilities(This,pdwCaps)
#define IMFMediaSession_GetFullTopology(This,dwGetFullTopologyFlags,TopoId,ppFullTopology) (This)->lpVtbl->GetFullTopology(This,dwGetFullTopologyFlags,TopoId,ppFullTopology)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaSession_QueryInterface(IMFMediaSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaSession_AddRef(IMFMediaSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaSession_Release(IMFMediaSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static inline HRESULT IMFMediaSession_GetEvent(IMFMediaSession* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static inline HRESULT IMFMediaSession_BeginGetEvent(IMFMediaSession* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static inline HRESULT IMFMediaSession_EndGetEvent(IMFMediaSession* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static inline HRESULT IMFMediaSession_QueueEvent(IMFMediaSession* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
/*** IMFMediaSession methods ***/
static inline HRESULT IMFMediaSession_SetTopology(IMFMediaSession* This,DWORD dwSetTopologyFlags,IMFTopology *pTopology) {
    return This->lpVtbl->SetTopology(This,dwSetTopologyFlags,pTopology);
}
static inline HRESULT IMFMediaSession_ClearTopologies(IMFMediaSession* This) {
    return This->lpVtbl->ClearTopologies(This);
}
static inline HRESULT IMFMediaSession_Start(IMFMediaSession* This,const GUID *pguidTimeFormat,const PROPVARIANT *pvarStartPosition) {
    return This->lpVtbl->Start(This,pguidTimeFormat,pvarStartPosition);
}
static inline HRESULT IMFMediaSession_Pause(IMFMediaSession* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IMFMediaSession_Stop(IMFMediaSession* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IMFMediaSession_Close(IMFMediaSession* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IMFMediaSession_Shutdown(IMFMediaSession* This) {
    return This->lpVtbl->Shutdown(This);
}
static inline HRESULT IMFMediaSession_GetClock(IMFMediaSession* This,IMFClock **ppClock) {
    return This->lpVtbl->GetClock(This,ppClock);
}
static inline HRESULT IMFMediaSession_GetSessionCapabilities(IMFMediaSession* This,DWORD *pdwCaps) {
    return This->lpVtbl->GetSessionCapabilities(This,pdwCaps);
}
static inline HRESULT IMFMediaSession_GetFullTopology(IMFMediaSession* This,DWORD dwGetFullTopologyFlags,TOPOID TopoId,IMFTopology **ppFullTopology) {
    return This->lpVtbl->GetFullTopology(This,dwGetFullTopologyFlags,TopoId,ppFullTopology);
}
#endif
#endif

#endif


#endif  /* __IMFMediaSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaTypeHandler interface
 */
#ifndef __IMFMediaTypeHandler_INTERFACE_DEFINED__
#define __IMFMediaTypeHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaTypeHandler, 0xe93dcf6c, 0x4b07, 0x4e1e, 0x81,0x23, 0xaa,0x16,0xed,0x6e,0xad,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e93dcf6c-4b07-4e1e-8123-aa16ed6eadf5")
IMFMediaTypeHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsMediaTypeSupported(
        IMFMediaType *pMediaType,
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTypeCount(
        DWORD *pdwTypeCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTypeByIndex(
        DWORD dwIndex,
        IMFMediaType **ppType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentMediaType(
        IMFMediaType *pMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentMediaType(
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMajorType(
        GUID *pguidMajorType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaTypeHandler, 0xe93dcf6c, 0x4b07, 0x4e1e, 0x81,0x23, 0xaa,0x16,0xed,0x6e,0xad,0xf5)
#endif
#else
typedef struct IMFMediaTypeHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaTypeHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaTypeHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaTypeHandler *This);

    /*** IMFMediaTypeHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *IsMediaTypeSupported)(
        IMFMediaTypeHandler *This,
        IMFMediaType *pMediaType,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetMediaTypeCount)(
        IMFMediaTypeHandler *This,
        DWORD *pdwTypeCount);

    HRESULT (STDMETHODCALLTYPE *GetMediaTypeByIndex)(
        IMFMediaTypeHandler *This,
        DWORD dwIndex,
        IMFMediaType **ppType);

    HRESULT (STDMETHODCALLTYPE *SetCurrentMediaType)(
        IMFMediaTypeHandler *This,
        IMFMediaType *pMediaType);

    HRESULT (STDMETHODCALLTYPE *GetCurrentMediaType)(
        IMFMediaTypeHandler *This,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetMajorType)(
        IMFMediaTypeHandler *This,
        GUID *pguidMajorType);

    END_INTERFACE
} IMFMediaTypeHandlerVtbl;

interface IMFMediaTypeHandler {
    CONST_VTBL IMFMediaTypeHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaTypeHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaTypeHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaTypeHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaTypeHandler methods ***/
#define IMFMediaTypeHandler_IsMediaTypeSupported(This,pMediaType,ppMediaType) (This)->lpVtbl->IsMediaTypeSupported(This,pMediaType,ppMediaType)
#define IMFMediaTypeHandler_GetMediaTypeCount(This,pdwTypeCount) (This)->lpVtbl->GetMediaTypeCount(This,pdwTypeCount)
#define IMFMediaTypeHandler_GetMediaTypeByIndex(This,dwIndex,ppType) (This)->lpVtbl->GetMediaTypeByIndex(This,dwIndex,ppType)
#define IMFMediaTypeHandler_SetCurrentMediaType(This,pMediaType) (This)->lpVtbl->SetCurrentMediaType(This,pMediaType)
#define IMFMediaTypeHandler_GetCurrentMediaType(This,ppMediaType) (This)->lpVtbl->GetCurrentMediaType(This,ppMediaType)
#define IMFMediaTypeHandler_GetMajorType(This,pguidMajorType) (This)->lpVtbl->GetMajorType(This,pguidMajorType)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaTypeHandler_QueryInterface(IMFMediaTypeHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaTypeHandler_AddRef(IMFMediaTypeHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaTypeHandler_Release(IMFMediaTypeHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaTypeHandler methods ***/
static inline HRESULT IMFMediaTypeHandler_IsMediaTypeSupported(IMFMediaTypeHandler* This,IMFMediaType *pMediaType,IMFMediaType **ppMediaType) {
    return This->lpVtbl->IsMediaTypeSupported(This,pMediaType,ppMediaType);
}
static inline HRESULT IMFMediaTypeHandler_GetMediaTypeCount(IMFMediaTypeHandler* This,DWORD *pdwTypeCount) {
    return This->lpVtbl->GetMediaTypeCount(This,pdwTypeCount);
}
static inline HRESULT IMFMediaTypeHandler_GetMediaTypeByIndex(IMFMediaTypeHandler* This,DWORD dwIndex,IMFMediaType **ppType) {
    return This->lpVtbl->GetMediaTypeByIndex(This,dwIndex,ppType);
}
static inline HRESULT IMFMediaTypeHandler_SetCurrentMediaType(IMFMediaTypeHandler* This,IMFMediaType *pMediaType) {
    return This->lpVtbl->SetCurrentMediaType(This,pMediaType);
}
static inline HRESULT IMFMediaTypeHandler_GetCurrentMediaType(IMFMediaTypeHandler* This,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetCurrentMediaType(This,ppMediaType);
}
static inline HRESULT IMFMediaTypeHandler_GetMajorType(IMFMediaTypeHandler* This,GUID *pguidMajorType) {
    return This->lpVtbl->GetMajorType(This,pguidMajorType);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFMediaTypeHandler_RemoteGetCurrentMediaType_Proxy(
    IMFMediaTypeHandler* This,
    BYTE **ppbData,
    DWORD *pcbData);
void __RPC_STUB IMFMediaTypeHandler_RemoteGetCurrentMediaType_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFMediaTypeHandler_GetCurrentMediaType_Proxy(
    IMFMediaTypeHandler* This,
    IMFMediaType **ppMediaType);
HRESULT __RPC_STUB IMFMediaTypeHandler_GetCurrentMediaType_Stub(
    IMFMediaTypeHandler* This,
    BYTE **ppbData,
    DWORD *pcbData);

#endif  /* __IMFMediaTypeHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFStreamDescriptor interface
 */
#ifndef __IMFStreamDescriptor_INTERFACE_DEFINED__
#define __IMFStreamDescriptor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFStreamDescriptor, 0x56c03d9c, 0x9dbb, 0x45f5, 0xab,0x4b, 0xd8,0x0f,0x47,0xc0,0x59,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56c03d9c-9dbb-45f5-ab4b-d80f47c05938")
IMFStreamDescriptor : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamIdentifier(
        DWORD *pdwStreamIdentifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTypeHandler(
        IMFMediaTypeHandler **ppMediaTypeHandler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFStreamDescriptor, 0x56c03d9c, 0x9dbb, 0x45f5, 0xab,0x4b, 0xd8,0x0f,0x47,0xc0,0x59,0x38)
#endif
#else
typedef struct IMFStreamDescriptorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFStreamDescriptor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFStreamDescriptor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFStreamDescriptor *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFStreamDescriptor *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFStreamDescriptor *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFStreamDescriptor *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFStreamDescriptor *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFStreamDescriptor *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFStreamDescriptor *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFStreamDescriptor *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFStreamDescriptor *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFStreamDescriptor *This,
        IMFAttributes *pDest);

    /*** IMFStreamDescriptor methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamIdentifier)(
        IMFStreamDescriptor *This,
        DWORD *pdwStreamIdentifier);

    HRESULT (STDMETHODCALLTYPE *GetMediaTypeHandler)(
        IMFStreamDescriptor *This,
        IMFMediaTypeHandler **ppMediaTypeHandler);

    END_INTERFACE
} IMFStreamDescriptorVtbl;

interface IMFStreamDescriptor {
    CONST_VTBL IMFStreamDescriptorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFStreamDescriptor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFStreamDescriptor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFStreamDescriptor_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFStreamDescriptor_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFStreamDescriptor_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFStreamDescriptor_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFStreamDescriptor_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFStreamDescriptor_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFStreamDescriptor_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFStreamDescriptor_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFStreamDescriptor_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFStreamDescriptor_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFStreamDescriptor_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFStreamDescriptor_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFStreamDescriptor_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFStreamDescriptor_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFStreamDescriptor_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFStreamDescriptor_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFStreamDescriptor_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFStreamDescriptor_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFStreamDescriptor_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFStreamDescriptor_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFStreamDescriptor_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFStreamDescriptor_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFStreamDescriptor_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFStreamDescriptor_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFStreamDescriptor_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFStreamDescriptor_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFStreamDescriptor_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFStreamDescriptor_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFStreamDescriptor_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFStreamDescriptor_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFStreamDescriptor_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFStreamDescriptor methods ***/
#define IMFStreamDescriptor_GetStreamIdentifier(This,pdwStreamIdentifier) (This)->lpVtbl->GetStreamIdentifier(This,pdwStreamIdentifier)
#define IMFStreamDescriptor_GetMediaTypeHandler(This,ppMediaTypeHandler) (This)->lpVtbl->GetMediaTypeHandler(This,ppMediaTypeHandler)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFStreamDescriptor_QueryInterface(IMFStreamDescriptor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFStreamDescriptor_AddRef(IMFStreamDescriptor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFStreamDescriptor_Release(IMFStreamDescriptor* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFStreamDescriptor_GetItem(IMFStreamDescriptor* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFStreamDescriptor_GetItemType(IMFStreamDescriptor* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFStreamDescriptor_CompareItem(IMFStreamDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFStreamDescriptor_Compare(IMFStreamDescriptor* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFStreamDescriptor_GetUINT32(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFStreamDescriptor_GetUINT64(IMFStreamDescriptor* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFStreamDescriptor_GetDouble(IMFStreamDescriptor* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFStreamDescriptor_GetGUID(IMFStreamDescriptor* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFStreamDescriptor_GetStringLength(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFStreamDescriptor_GetString(IMFStreamDescriptor* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFStreamDescriptor_GetAllocatedString(IMFStreamDescriptor* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFStreamDescriptor_GetBlobSize(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFStreamDescriptor_GetBlob(IMFStreamDescriptor* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFStreamDescriptor_GetAllocatedBlob(IMFStreamDescriptor* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFStreamDescriptor_GetUnknown(IMFStreamDescriptor* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFStreamDescriptor_SetItem(IMFStreamDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFStreamDescriptor_DeleteItem(IMFStreamDescriptor* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFStreamDescriptor_DeleteAllItems(IMFStreamDescriptor* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFStreamDescriptor_SetUINT32(IMFStreamDescriptor* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFStreamDescriptor_SetUINT64(IMFStreamDescriptor* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFStreamDescriptor_SetDouble(IMFStreamDescriptor* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFStreamDescriptor_SetGUID(IMFStreamDescriptor* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFStreamDescriptor_SetString(IMFStreamDescriptor* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFStreamDescriptor_SetBlob(IMFStreamDescriptor* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFStreamDescriptor_SetUnknown(IMFStreamDescriptor* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFStreamDescriptor_LockStore(IMFStreamDescriptor* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFStreamDescriptor_UnlockStore(IMFStreamDescriptor* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFStreamDescriptor_GetCount(IMFStreamDescriptor* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFStreamDescriptor_GetItemByIndex(IMFStreamDescriptor* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFStreamDescriptor_CopyAllItems(IMFStreamDescriptor* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFStreamDescriptor methods ***/
static inline HRESULT IMFStreamDescriptor_GetStreamIdentifier(IMFStreamDescriptor* This,DWORD *pdwStreamIdentifier) {
    return This->lpVtbl->GetStreamIdentifier(This,pdwStreamIdentifier);
}
static inline HRESULT IMFStreamDescriptor_GetMediaTypeHandler(IMFStreamDescriptor* This,IMFMediaTypeHandler **ppMediaTypeHandler) {
    return This->lpVtbl->GetMediaTypeHandler(This,ppMediaTypeHandler);
}
#endif
#endif

#endif


#endif  /* __IMFStreamDescriptor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFPresentationDescriptor interface
 */
#ifndef __IMFPresentationDescriptor_INTERFACE_DEFINED__
#define __IMFPresentationDescriptor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPresentationDescriptor, 0x03cb2711, 0x24d7, 0x4db6, 0xa1,0x7f, 0xf3,0xa7,0xa4,0x79,0xa5,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("03cb2711-24d7-4db6-a17f-f3a7a479a536")
IMFPresentationDescriptor : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamDescriptorCount(
        DWORD *pdwDescriptorCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamDescriptorByIndex(
        DWORD dwIndex,
        WINBOOL *pfSelected,
        IMFStreamDescriptor **ppDescriptor) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectStream(
        DWORD dwDescriptorIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeselectStream(
        DWORD dwDescriptorIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IMFPresentationDescriptor **ppPresentationDescriptor) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPresentationDescriptor, 0x03cb2711, 0x24d7, 0x4db6, 0xa1,0x7f, 0xf3,0xa7,0xa4,0x79,0xa5,0x36)
#endif
#else
typedef struct IMFPresentationDescriptorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPresentationDescriptor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPresentationDescriptor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPresentationDescriptor *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFPresentationDescriptor *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFPresentationDescriptor *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFPresentationDescriptor *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFPresentationDescriptor *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFPresentationDescriptor *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFPresentationDescriptor *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFPresentationDescriptor *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFPresentationDescriptor *This,
        IMFAttributes *pDest);

    /*** IMFPresentationDescriptor methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamDescriptorCount)(
        IMFPresentationDescriptor *This,
        DWORD *pdwDescriptorCount);

    HRESULT (STDMETHODCALLTYPE *GetStreamDescriptorByIndex)(
        IMFPresentationDescriptor *This,
        DWORD dwIndex,
        WINBOOL *pfSelected,
        IMFStreamDescriptor **ppDescriptor);

    HRESULT (STDMETHODCALLTYPE *SelectStream)(
        IMFPresentationDescriptor *This,
        DWORD dwDescriptorIndex);

    HRESULT (STDMETHODCALLTYPE *DeselectStream)(
        IMFPresentationDescriptor *This,
        DWORD dwDescriptorIndex);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IMFPresentationDescriptor *This,
        IMFPresentationDescriptor **ppPresentationDescriptor);

    END_INTERFACE
} IMFPresentationDescriptorVtbl;

interface IMFPresentationDescriptor {
    CONST_VTBL IMFPresentationDescriptorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPresentationDescriptor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPresentationDescriptor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPresentationDescriptor_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFPresentationDescriptor_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFPresentationDescriptor_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFPresentationDescriptor_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFPresentationDescriptor_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFPresentationDescriptor_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFPresentationDescriptor_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFPresentationDescriptor_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFPresentationDescriptor_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFPresentationDescriptor_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFPresentationDescriptor_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFPresentationDescriptor_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFPresentationDescriptor_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFPresentationDescriptor_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFPresentationDescriptor_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFPresentationDescriptor_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFPresentationDescriptor_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFPresentationDescriptor_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFPresentationDescriptor_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFPresentationDescriptor_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFPresentationDescriptor_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFPresentationDescriptor_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFPresentationDescriptor_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFPresentationDescriptor_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFPresentationDescriptor_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFPresentationDescriptor_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFPresentationDescriptor_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFPresentationDescriptor_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFPresentationDescriptor_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFPresentationDescriptor_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFPresentationDescriptor_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFPresentationDescriptor methods ***/
#define IMFPresentationDescriptor_GetStreamDescriptorCount(This,pdwDescriptorCount) (This)->lpVtbl->GetStreamDescriptorCount(This,pdwDescriptorCount)
#define IMFPresentationDescriptor_GetStreamDescriptorByIndex(This,dwIndex,pfSelected,ppDescriptor) (This)->lpVtbl->GetStreamDescriptorByIndex(This,dwIndex,pfSelected,ppDescriptor)
#define IMFPresentationDescriptor_SelectStream(This,dwDescriptorIndex) (This)->lpVtbl->SelectStream(This,dwDescriptorIndex)
#define IMFPresentationDescriptor_DeselectStream(This,dwDescriptorIndex) (This)->lpVtbl->DeselectStream(This,dwDescriptorIndex)
#define IMFPresentationDescriptor_Clone(This,ppPresentationDescriptor) (This)->lpVtbl->Clone(This,ppPresentationDescriptor)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPresentationDescriptor_QueryInterface(IMFPresentationDescriptor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPresentationDescriptor_AddRef(IMFPresentationDescriptor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPresentationDescriptor_Release(IMFPresentationDescriptor* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFPresentationDescriptor_GetItem(IMFPresentationDescriptor* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFPresentationDescriptor_GetItemType(IMFPresentationDescriptor* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFPresentationDescriptor_CompareItem(IMFPresentationDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFPresentationDescriptor_Compare(IMFPresentationDescriptor* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFPresentationDescriptor_GetUINT32(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFPresentationDescriptor_GetUINT64(IMFPresentationDescriptor* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFPresentationDescriptor_GetDouble(IMFPresentationDescriptor* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFPresentationDescriptor_GetGUID(IMFPresentationDescriptor* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFPresentationDescriptor_GetStringLength(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFPresentationDescriptor_GetString(IMFPresentationDescriptor* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFPresentationDescriptor_GetAllocatedString(IMFPresentationDescriptor* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFPresentationDescriptor_GetBlobSize(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFPresentationDescriptor_GetBlob(IMFPresentationDescriptor* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFPresentationDescriptor_GetAllocatedBlob(IMFPresentationDescriptor* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFPresentationDescriptor_GetUnknown(IMFPresentationDescriptor* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFPresentationDescriptor_SetItem(IMFPresentationDescriptor* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFPresentationDescriptor_DeleteItem(IMFPresentationDescriptor* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFPresentationDescriptor_DeleteAllItems(IMFPresentationDescriptor* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFPresentationDescriptor_SetUINT32(IMFPresentationDescriptor* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFPresentationDescriptor_SetUINT64(IMFPresentationDescriptor* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFPresentationDescriptor_SetDouble(IMFPresentationDescriptor* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFPresentationDescriptor_SetGUID(IMFPresentationDescriptor* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFPresentationDescriptor_SetString(IMFPresentationDescriptor* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFPresentationDescriptor_SetBlob(IMFPresentationDescriptor* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFPresentationDescriptor_SetUnknown(IMFPresentationDescriptor* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFPresentationDescriptor_LockStore(IMFPresentationDescriptor* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFPresentationDescriptor_UnlockStore(IMFPresentationDescriptor* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFPresentationDescriptor_GetCount(IMFPresentationDescriptor* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFPresentationDescriptor_GetItemByIndex(IMFPresentationDescriptor* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFPresentationDescriptor_CopyAllItems(IMFPresentationDescriptor* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFPresentationDescriptor methods ***/
static inline HRESULT IMFPresentationDescriptor_GetStreamDescriptorCount(IMFPresentationDescriptor* This,DWORD *pdwDescriptorCount) {
    return This->lpVtbl->GetStreamDescriptorCount(This,pdwDescriptorCount);
}
static inline HRESULT IMFPresentationDescriptor_GetStreamDescriptorByIndex(IMFPresentationDescriptor* This,DWORD dwIndex,WINBOOL *pfSelected,IMFStreamDescriptor **ppDescriptor) {
    return This->lpVtbl->GetStreamDescriptorByIndex(This,dwIndex,pfSelected,ppDescriptor);
}
static inline HRESULT IMFPresentationDescriptor_SelectStream(IMFPresentationDescriptor* This,DWORD dwDescriptorIndex) {
    return This->lpVtbl->SelectStream(This,dwDescriptorIndex);
}
static inline HRESULT IMFPresentationDescriptor_DeselectStream(IMFPresentationDescriptor* This,DWORD dwDescriptorIndex) {
    return This->lpVtbl->DeselectStream(This,dwDescriptorIndex);
}
static inline HRESULT IMFPresentationDescriptor_Clone(IMFPresentationDescriptor* This,IMFPresentationDescriptor **ppPresentationDescriptor) {
    return This->lpVtbl->Clone(This,ppPresentationDescriptor);
}
#endif
#endif

#endif


#endif  /* __IMFPresentationDescriptor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaSource interface
 */
#ifndef __IMFMediaSource_INTERFACE_DEFINED__
#define __IMFMediaSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaSource, 0x279a808d, 0xaec7, 0x40c8, 0x9c,0x6b, 0xa6,0xb4,0x92,0xc7,0x8a,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("279a808d-aec7-40c8-9c6b-a6b492c78a66")
IMFMediaSource : public IMFMediaEventGenerator
{
    virtual HRESULT STDMETHODCALLTYPE GetCharacteristics(
        DWORD *pdwCharacteristics) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePresentationDescriptor(
        IMFPresentationDescriptor **ppPresentationDescriptor) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        IMFPresentationDescriptor *pPresentationDescriptor,
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaSource, 0x279a808d, 0xaec7, 0x40c8, 0x9c,0x6b, 0xa6,0xb4,0x92,0xc7,0x8a,0x66)
#endif
#else
typedef struct IMFMediaSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaSource *This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaSource *This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaSource *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaSource *This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaSource *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    /*** IMFMediaSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        IMFMediaSource *This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *CreatePresentationDescriptor)(
        IMFMediaSource *This,
        IMFPresentationDescriptor **ppPresentationDescriptor);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IMFMediaSource *This,
        IMFPresentationDescriptor *pPresentationDescriptor,
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMFMediaSource *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFMediaSource *This);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaSource *This);

    END_INTERFACE
} IMFMediaSourceVtbl;

interface IMFMediaSource {
    CONST_VTBL IMFMediaSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaSource_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFMediaSource_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaSource_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaSource_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaSource_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
/*** IMFMediaSource methods ***/
#define IMFMediaSource_GetCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pdwCharacteristics)
#define IMFMediaSource_CreatePresentationDescriptor(This,ppPresentationDescriptor) (This)->lpVtbl->CreatePresentationDescriptor(This,ppPresentationDescriptor)
#define IMFMediaSource_Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition) (This)->lpVtbl->Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition)
#define IMFMediaSource_Stop(This) (This)->lpVtbl->Stop(This)
#define IMFMediaSource_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFMediaSource_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaSource_QueryInterface(IMFMediaSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaSource_AddRef(IMFMediaSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaSource_Release(IMFMediaSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static inline HRESULT IMFMediaSource_GetEvent(IMFMediaSource* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static inline HRESULT IMFMediaSource_BeginGetEvent(IMFMediaSource* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static inline HRESULT IMFMediaSource_EndGetEvent(IMFMediaSource* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static inline HRESULT IMFMediaSource_QueueEvent(IMFMediaSource* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
/*** IMFMediaSource methods ***/
static inline HRESULT IMFMediaSource_GetCharacteristics(IMFMediaSource* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT IMFMediaSource_CreatePresentationDescriptor(IMFMediaSource* This,IMFPresentationDescriptor **ppPresentationDescriptor) {
    return This->lpVtbl->CreatePresentationDescriptor(This,ppPresentationDescriptor);
}
static inline HRESULT IMFMediaSource_Start(IMFMediaSource* This,IMFPresentationDescriptor *pPresentationDescriptor,const GUID *pguidTimeFormat,const PROPVARIANT *pvarStartPosition) {
    return This->lpVtbl->Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition);
}
static inline HRESULT IMFMediaSource_Stop(IMFMediaSource* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IMFMediaSource_Pause(IMFMediaSource* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IMFMediaSource_Shutdown(IMFMediaSource* This) {
    return This->lpVtbl->Shutdown(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFMediaSource_RemoteCreatePresentationDescriptor_Proxy(
    IMFMediaSource* This,
    DWORD *pcbPD,
    BYTE **pbPD,
    IMFPresentationDescriptor **ppRemotePD);
void __RPC_STUB IMFMediaSource_RemoteCreatePresentationDescriptor_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFMediaSource_CreatePresentationDescriptor_Proxy(
    IMFMediaSource* This,
    IMFPresentationDescriptor **ppPresentationDescriptor);
HRESULT __RPC_STUB IMFMediaSource_CreatePresentationDescriptor_Stub(
    IMFMediaSource* This,
    DWORD *pcbPD,
    BYTE **pbPD,
    IMFPresentationDescriptor **ppRemotePD);

#endif  /* __IMFMediaSource_INTERFACE_DEFINED__ */

#if (WINVER >= _WIN32_WINNT_WIN8)
/*****************************************************************************
 * IMFMediaSourceEx interface
 */
#ifndef __IMFMediaSourceEx_INTERFACE_DEFINED__
#define __IMFMediaSourceEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaSourceEx, 0x3c9b2eb9, 0x86d5, 0x4514, 0xa3,0x94, 0xf5,0x66,0x64,0xf9,0xf0,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3c9b2eb9-86d5-4514-a394-f56664f9f0d8")
IMFMediaSourceEx : public IMFMediaSource
{
    virtual HRESULT STDMETHODCALLTYPE GetSourceAttributes(
        IMFAttributes **ppAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamAttributes(
        DWORD dwStreamIdentifier,
        IMFAttributes **ppAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetD3DManager(
        IUnknown *pManager) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaSourceEx, 0x3c9b2eb9, 0x86d5, 0x4514, 0xa3,0x94, 0xf5,0x66,0x64,0xf9,0xf0,0xd8)
#endif
#else
typedef struct IMFMediaSourceExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaSourceEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaSourceEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaSourceEx *This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaSourceEx *This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaSourceEx *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaSourceEx *This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaSourceEx *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    /*** IMFMediaSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        IMFMediaSourceEx *This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *CreatePresentationDescriptor)(
        IMFMediaSourceEx *This,
        IMFPresentationDescriptor **ppPresentationDescriptor);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IMFMediaSourceEx *This,
        IMFPresentationDescriptor *pPresentationDescriptor,
        const GUID *pguidTimeFormat,
        const PROPVARIANT *pvarStartPosition);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMFMediaSourceEx *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFMediaSourceEx *This);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaSourceEx *This);

    /*** IMFMediaSourceEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSourceAttributes)(
        IMFMediaSourceEx *This,
        IMFAttributes **ppAttributes);

    HRESULT (STDMETHODCALLTYPE *GetStreamAttributes)(
        IMFMediaSourceEx *This,
        DWORD dwStreamIdentifier,
        IMFAttributes **ppAttributes);

    HRESULT (STDMETHODCALLTYPE *SetD3DManager)(
        IMFMediaSourceEx *This,
        IUnknown *pManager);

    END_INTERFACE
} IMFMediaSourceExVtbl;

interface IMFMediaSourceEx {
    CONST_VTBL IMFMediaSourceExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaSourceEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaSourceEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaSourceEx_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFMediaSourceEx_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaSourceEx_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaSourceEx_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaSourceEx_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
/*** IMFMediaSource methods ***/
#define IMFMediaSourceEx_GetCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pdwCharacteristics)
#define IMFMediaSourceEx_CreatePresentationDescriptor(This,ppPresentationDescriptor) (This)->lpVtbl->CreatePresentationDescriptor(This,ppPresentationDescriptor)
#define IMFMediaSourceEx_Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition) (This)->lpVtbl->Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition)
#define IMFMediaSourceEx_Stop(This) (This)->lpVtbl->Stop(This)
#define IMFMediaSourceEx_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFMediaSourceEx_Shutdown(This) (This)->lpVtbl->Shutdown(This)
/*** IMFMediaSourceEx methods ***/
#define IMFMediaSourceEx_GetSourceAttributes(This,ppAttributes) (This)->lpVtbl->GetSourceAttributes(This,ppAttributes)
#define IMFMediaSourceEx_GetStreamAttributes(This,dwStreamIdentifier,ppAttributes) (This)->lpVtbl->GetStreamAttributes(This,dwStreamIdentifier,ppAttributes)
#define IMFMediaSourceEx_SetD3DManager(This,pManager) (This)->lpVtbl->SetD3DManager(This,pManager)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaSourceEx_QueryInterface(IMFMediaSourceEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaSourceEx_AddRef(IMFMediaSourceEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaSourceEx_Release(IMFMediaSourceEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static inline HRESULT IMFMediaSourceEx_GetEvent(IMFMediaSourceEx* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static inline HRESULT IMFMediaSourceEx_BeginGetEvent(IMFMediaSourceEx* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static inline HRESULT IMFMediaSourceEx_EndGetEvent(IMFMediaSourceEx* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static inline HRESULT IMFMediaSourceEx_QueueEvent(IMFMediaSourceEx* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
/*** IMFMediaSource methods ***/
static inline HRESULT IMFMediaSourceEx_GetCharacteristics(IMFMediaSourceEx* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT IMFMediaSourceEx_CreatePresentationDescriptor(IMFMediaSourceEx* This,IMFPresentationDescriptor **ppPresentationDescriptor) {
    return This->lpVtbl->CreatePresentationDescriptor(This,ppPresentationDescriptor);
}
static inline HRESULT IMFMediaSourceEx_Start(IMFMediaSourceEx* This,IMFPresentationDescriptor *pPresentationDescriptor,const GUID *pguidTimeFormat,const PROPVARIANT *pvarStartPosition) {
    return This->lpVtbl->Start(This,pPresentationDescriptor,pguidTimeFormat,pvarStartPosition);
}
static inline HRESULT IMFMediaSourceEx_Stop(IMFMediaSourceEx* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IMFMediaSourceEx_Pause(IMFMediaSourceEx* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IMFMediaSourceEx_Shutdown(IMFMediaSourceEx* This) {
    return This->lpVtbl->Shutdown(This);
}
/*** IMFMediaSourceEx methods ***/
static inline HRESULT IMFMediaSourceEx_GetSourceAttributes(IMFMediaSourceEx* This,IMFAttributes **ppAttributes) {
    return This->lpVtbl->GetSourceAttributes(This,ppAttributes);
}
static inline HRESULT IMFMediaSourceEx_GetStreamAttributes(IMFMediaSourceEx* This,DWORD dwStreamIdentifier,IMFAttributes **ppAttributes) {
    return This->lpVtbl->GetStreamAttributes(This,dwStreamIdentifier,ppAttributes);
}
static inline HRESULT IMFMediaSourceEx_SetD3DManager(IMFMediaSourceEx* This,IUnknown *pManager) {
    return This->lpVtbl->SetD3DManager(This,pManager);
}
#endif
#endif

#endif


#endif  /* __IMFMediaSourceEx_INTERFACE_DEFINED__ */

EXTERN_GUID(MF_SOURCE_STREAM_SUPPORTS_HW_CONNECTION, 0xa38253aa, 0x6314, 0x42fd, 0xa3, 0xce, 0xbb, 0x27, 0xb6, 0x85, 0x99, 0x46);
#endif /* (WINVER >= _WIN32_WINNT_WIN8) */
typedef struct _MF_LEAKY_BUCKET_PAIR {
    DWORD dwBitrate;
    DWORD msBufferWindow;
} MF_LEAKY_BUCKET_PAIR;
typedef struct _MFBYTESTREAM_BUFFERING_PARAMS {
    QWORD cbTotalFileSize;
    QWORD cbPlayableDataSize;
    MF_LEAKY_BUCKET_PAIR *prgBuckets;
    DWORD cBuckets;
    QWORD qwNetBufferingTime;
    QWORD qwExtraBufferingTimeDuringSeek;
    QWORD qwPlayDuration;
    float dRate;
} MFBYTESTREAM_BUFFERING_PARAMS;
/*****************************************************************************
 * IMFByteStreamBuffering interface
 */
#ifndef __IMFByteStreamBuffering_INTERFACE_DEFINED__
#define __IMFByteStreamBuffering_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFByteStreamBuffering, 0x6d66d782, 0x1d4f, 0x4db7, 0x8c,0x63, 0xcb,0x8c,0x77,0xf1,0xef,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6d66d782-1d4f-4db7-8c63-cb8c77f1ef5e")
IMFByteStreamBuffering : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetBufferingParams(
        MFBYTESTREAM_BUFFERING_PARAMS *pParams) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableBuffering(
        WINBOOL fEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopBuffering(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFByteStreamBuffering, 0x6d66d782, 0x1d4f, 0x4db7, 0x8c,0x63, 0xcb,0x8c,0x77,0xf1,0xef,0x5e)
#endif
#else
typedef struct IMFByteStreamBufferingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFByteStreamBuffering *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFByteStreamBuffering *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFByteStreamBuffering *This);

    /*** IMFByteStreamBuffering methods ***/
    HRESULT (STDMETHODCALLTYPE *SetBufferingParams)(
        IMFByteStreamBuffering *This,
        MFBYTESTREAM_BUFFERING_PARAMS *pParams);

    HRESULT (STDMETHODCALLTYPE *EnableBuffering)(
        IMFByteStreamBuffering *This,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *StopBuffering)(
        IMFByteStreamBuffering *This);

    END_INTERFACE
} IMFByteStreamBufferingVtbl;

interface IMFByteStreamBuffering {
    CONST_VTBL IMFByteStreamBufferingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFByteStreamBuffering_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFByteStreamBuffering_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFByteStreamBuffering_Release(This) (This)->lpVtbl->Release(This)
/*** IMFByteStreamBuffering methods ***/
#define IMFByteStreamBuffering_SetBufferingParams(This,pParams) (This)->lpVtbl->SetBufferingParams(This,pParams)
#define IMFByteStreamBuffering_EnableBuffering(This,fEnable) (This)->lpVtbl->EnableBuffering(This,fEnable)
#define IMFByteStreamBuffering_StopBuffering(This) (This)->lpVtbl->StopBuffering(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFByteStreamBuffering_QueryInterface(IMFByteStreamBuffering* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFByteStreamBuffering_AddRef(IMFByteStreamBuffering* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFByteStreamBuffering_Release(IMFByteStreamBuffering* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFByteStreamBuffering methods ***/
static inline HRESULT IMFByteStreamBuffering_SetBufferingParams(IMFByteStreamBuffering* This,MFBYTESTREAM_BUFFERING_PARAMS *pParams) {
    return This->lpVtbl->SetBufferingParams(This,pParams);
}
static inline HRESULT IMFByteStreamBuffering_EnableBuffering(IMFByteStreamBuffering* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableBuffering(This,fEnable);
}
static inline HRESULT IMFByteStreamBuffering_StopBuffering(IMFByteStreamBuffering* This) {
    return This->lpVtbl->StopBuffering(This);
}
#endif
#endif

#endif


#endif  /* __IMFByteStreamBuffering_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFClockStateSink interface
 */
#ifndef __IMFClockStateSink_INTERFACE_DEFINED__
#define __IMFClockStateSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFClockStateSink, 0xf6696e82, 0x74f7, 0x4f3d, 0xa1,0x78, 0x8a,0x5e,0x09,0xc3,0x65,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f6696e82-74f7-4f3d-a178-8a5e09c3659f")
IMFClockStateSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnClockStart(
        MFTIME hnsSystemTime,
        LONGLONG llClockStartOffset) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnClockStop(
        MFTIME hnsSystemTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnClockPause(
        MFTIME hnsSystemTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnClockRestart(
        MFTIME hnsSystemTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnClockSetRate(
        MFTIME hnsSystemTime,
        float flRate) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFClockStateSink, 0xf6696e82, 0x74f7, 0x4f3d, 0xa1,0x78, 0x8a,0x5e,0x09,0xc3,0x65,0x9f)
#endif
#else
typedef struct IMFClockStateSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFClockStateSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFClockStateSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFClockStateSink *This);

    /*** IMFClockStateSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnClockStart)(
        IMFClockStateSink *This,
        MFTIME hnsSystemTime,
        LONGLONG llClockStartOffset);

    HRESULT (STDMETHODCALLTYPE *OnClockStop)(
        IMFClockStateSink *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockPause)(
        IMFClockStateSink *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockRestart)(
        IMFClockStateSink *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockSetRate)(
        IMFClockStateSink *This,
        MFTIME hnsSystemTime,
        float flRate);

    END_INTERFACE
} IMFClockStateSinkVtbl;

interface IMFClockStateSink {
    CONST_VTBL IMFClockStateSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFClockStateSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFClockStateSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFClockStateSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFClockStateSink methods ***/
#define IMFClockStateSink_OnClockStart(This,hnsSystemTime,llClockStartOffset) (This)->lpVtbl->OnClockStart(This,hnsSystemTime,llClockStartOffset)
#define IMFClockStateSink_OnClockStop(This,hnsSystemTime) (This)->lpVtbl->OnClockStop(This,hnsSystemTime)
#define IMFClockStateSink_OnClockPause(This,hnsSystemTime) (This)->lpVtbl->OnClockPause(This,hnsSystemTime)
#define IMFClockStateSink_OnClockRestart(This,hnsSystemTime) (This)->lpVtbl->OnClockRestart(This,hnsSystemTime)
#define IMFClockStateSink_OnClockSetRate(This,hnsSystemTime,flRate) (This)->lpVtbl->OnClockSetRate(This,hnsSystemTime,flRate)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFClockStateSink_QueryInterface(IMFClockStateSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFClockStateSink_AddRef(IMFClockStateSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFClockStateSink_Release(IMFClockStateSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFClockStateSink methods ***/
static inline HRESULT IMFClockStateSink_OnClockStart(IMFClockStateSink* This,MFTIME hnsSystemTime,LONGLONG llClockStartOffset) {
    return This->lpVtbl->OnClockStart(This,hnsSystemTime,llClockStartOffset);
}
static inline HRESULT IMFClockStateSink_OnClockStop(IMFClockStateSink* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockStop(This,hnsSystemTime);
}
static inline HRESULT IMFClockStateSink_OnClockPause(IMFClockStateSink* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockPause(This,hnsSystemTime);
}
static inline HRESULT IMFClockStateSink_OnClockRestart(IMFClockStateSink* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockRestart(This,hnsSystemTime);
}
static inline HRESULT IMFClockStateSink_OnClockSetRate(IMFClockStateSink* This,MFTIME hnsSystemTime,float flRate) {
    return This->lpVtbl->OnClockSetRate(This,hnsSystemTime,flRate);
}
#endif
#endif

#endif


#endif  /* __IMFClockStateSink_INTERFACE_DEFINED__ */

HRESULT WINAPI MFRequireProtectedEnvironment(IMFPresentationDescriptor *pPresentationDescriptor);
HRESULT WINAPI MFSerializePresentationDescriptor(IMFPresentationDescriptor *pPD,DWORD *pcbData,BYTE **ppbData);
typedef DWORD MFSequencerElementId;
typedef enum _MFCLOCK_CHARACTERISTICS_FLAGS {
    MFCLOCK_CHARACTERISTICS_FLAG_FREQUENCY_10MHZ = 0x2,
    MFCLOCK_CHARACTERISTICS_FLAG_ALWAYS_RUNNING = 0x4,
    MFCLOCK_CHARACTERISTICS_FLAG_IS_SYSTEM_CLOCK = 0x8
} MFCLOCK_CHARACTERISTICS_FLAGS;
#if (WINVER >= 0x0601)
typedef enum _MF_QUALITY_ADVISE_FLAGS {
    MF_QUALITY_CANNOT_KEEP_UP = 0x1
} MF_QUALITY_ADVISE_FLAGS;
#endif /*(WINVER >= 0x0601)*/
typedef enum _MF_QUALITY_DROP_MODE {
    MF_DROP_MODE_NONE = 0x0,
    MF_DROP_MODE_1 = 0x1,
    MF_DROP_MODE_2 = 0x2,
    MF_DROP_MODE_3 = 0x3,
    MF_DROP_MODE_4 = 0x4,
    MF_DROP_MODE_5 = 0x5,
    MF_NUM_DROP_MODES = 0x6
} MF_QUALITY_DROP_MODE;
typedef enum _MF_QUALITY_LEVEL {
    MF_QUALITY_NORMAL = 0x0,
    MF_QUALITY_NORMAL_MINUS_1 = 0x1,
    MF_QUALITY_NORMAL_MINUS_2 = 0x2,
    MF_QUALITY_NORMAL_MINUS_3 = 0x3,
    MF_QUALITY_NORMAL_MINUS_4 = 0x4,
    MF_QUALITY_NORMAL_MINUS_5 = 0x5,
    MF_NUM_QUALITY_LEVELS = 0x6
} MF_QUALITY_LEVEL;
typedef enum _MF_TOPOLOGY_RESOLUTION_STATUS_FLAGS {
    MF_TOPOLOGY_RESOLUTION_SUCCEEDED = 0x0,
    MF_OPTIONAL_NODE_REJECTED_MEDIA_TYPE = 0x1,
    MF_OPTIONAL_NODE_REJECTED_PROTECTED_PROCESS = 0x2
} MF_TOPOLOGY_RESOLUTION_STATUS_FLAGS;
typedef enum _MF_TOPONODE_DRAIN_MODE {
    MF_TOPONODE_DRAIN_DEFAULT = 0,
    MF_TOPONODE_DRAIN_ALWAYS = 1,
    MF_TOPONODE_DRAIN_NEVER = 2
} MF_TOPONODE_DRAIN_MODE;
typedef enum _MF_TOPONODE_FLUSH_MODE {
    MF_TOPONODE_FLUSH_ALWAYS = 0,
    MF_TOPONODE_FLUSH_SEEK = 1,
    MF_TOPONODE_FLUSH_NEVER = 2
} MF_TOPONODE_FLUSH_MODE;
#if (WINVER >= 0x0601)
typedef enum _MF_TRANSCODE_TOPOLOGY_MODE_FLAGS {
    MF_TRANSCODE_TOPOLOGYMODE_SOFTWARE_ONLY = 0,
    MF_TRANSCODE_TOPOLOGYMODE_HARDWARE_ALLOWED = 1
} MF_TRANSCODE_TOPOLOGYMODE_FLAGS;
#endif
EXTERN_GUID(MF_TRANSCODE_CONTAINERTYPE, 0x150ff23f, 0x4abc, 0x478b, 0xac, 0x4f, 0xe1, 0x91, 0x6f, 0xba, 0x1c, 0xca);
EXTERN_GUID(MFTranscodeContainerType_ASF, 0x430f6f6e, 0xb6bf, 0x4fc1, 0xa0, 0xbd, 0x9e, 0xe4, 0x6e, 0xee, 0x2a, 0xfb);
EXTERN_GUID(MFTranscodeContainerType_MPEG4, 0xdc6cd05d, 0xb9d0, 0x40ef, 0xbd, 0x35, 0xfa, 0x62, 0x2c, 0x1a, 0xb2, 0x8a);
EXTERN_GUID(MFTranscodeContainerType_MP3, 0xe438b912, 0x83f1, 0x4de6, 0x9e, 0x3a, 0x9f, 0xfb, 0xc6, 0xdd, 0x24, 0xd1);
EXTERN_GUID(MFTranscodeContainerType_FLAC, 0x31344aa3, 0x05a9, 0x42b5, 0x90, 0x1b, 0x8e, 0x9d, 0x42, 0x57, 0xf7, 0x5e);
EXTERN_GUID(MFTranscodeContainerType_3GP, 0x34c50167, 0x4472, 0x4f34, 0x9e, 0xa0, 0xc4, 0x9f, 0xba, 0xcf, 0x03, 0x7d);
EXTERN_GUID(MFTranscodeContainerType_AC3, 0x6d8d91c3, 0x8c91, 0x4ed1, 0x87, 0x42, 0x8c, 0x34, 0x7d, 0x5b, 0x44, 0xd0);
EXTERN_GUID(MFTranscodeContainerType_ADTS, 0x132fd27d, 0x0f02, 0x43de, 0xa3, 0x01, 0x38, 0xfb, 0xbb, 0xb3, 0x83, 0x4e);
EXTERN_GUID(MFTranscodeContainerType_MPEG2, 0xbfc2dbf9, 0x7bb4, 0x4f8f, 0xaf, 0xde, 0xe1, 0x12, 0xc4, 0x4b, 0xa8, 0x82);
EXTERN_GUID(MFTranscodeContainerType_WAVE, 0x64c3453c, 0x0f26, 0x4741, 0xbe, 0x63, 0x87, 0xbd, 0xf8, 0xbb, 0x93, 0x5b);
EXTERN_GUID(MFTranscodeContainerType_AVI, 0x7edfe8af, 0x402f, 0x4d76, 0xa3, 0x3c, 0x61, 0x9f, 0xd1, 0x57, 0xd0, 0xf1);
#if (WINVER >= _WIN32_WINNT_WIN8)
EXTERN_GUID(MFTranscodeContainerType_FMPEG4, 0x9ba876f1, 0x419f, 0x4b77, 0xa1, 0xe0, 0x35, 0x95, 0x9d, 0x9d, 0x40, 0x4);
#endif /* (WINVER >= _WIN32_WINNT_WIN8) */
EXTERN_GUID(MFTranscodeContainerType_AMR, 0x25d5ad3, 0x621a, 0x475b, 0x96, 0x4d, 0x66, 0xb1, 0xc8, 0x24, 0xf0, 0x79);
typedef enum __WIDL_mfidl_generated_name_00000027 {
    MF_LICENSE_URL_UNTRUSTED = 0,
    MF_LICENSE_URL_TRUSTED = 1,
    MF_LICENSE_URL_TAMPERED = 2
} MF_URL_TRUST_STATUS;
typedef enum _MFCLOCK_RELATIONAL_FLAGS {
    MFCLOCK_RELATIONAL_FLAG_JITTER_NEVER_AHEAD = 0x1
} MFCLOCK_RELATIONAL_FLAGS;
typedef enum _MFMEDIASOURCE_CHARACTERISTICS {
    MFMEDIASOURCE_IS_LIVE = 0x1,
    MFMEDIASOURCE_CAN_SEEK = 0x2,
    MFMEDIASOURCE_CAN_PAUSE = 0x4,
    MFMEDIASOURCE_HAS_SLOW_SEEK = 0x8,
    MFMEDIASOURCE_HAS_MULTIPLE_PRESENTATIONS = 0x10,
    MFMEDIASOURCE_CAN_SKIPFORWARD = 0x20,
    MFMEDIASOURCE_CAN_SKIPBACKWARD = 0x40
} MFMEDIASOURCE_CHARACTERISTICS;
typedef enum _MFNET_PROXYSETTINGS {
    MFNET_PROXYSETTING_NONE = 0,
    MFNET_PROXYSETTING_MANUAL = 1,
    MFNET_PROXYSETTING_AUTO = 2,
    MFNET_PROXYSETTING_BROWSER = 3
} MFNET_PROXYSETTINGS;
typedef enum _MFNetAuthenticationFlags {
    MFNET_AUTHENTICATION_PROXY = 0x1,
    MFNET_AUTHENTICATION_CLEAR_TEXT = 0x2,
    MFNET_AUTHENTICATION_LOGGED_ON_USER = 0x4
} MFNetAuthenticationFlags;
typedef enum _MFNetCredentialOptions {
    MFNET_CREDENTIAL_SAVE = 0x1,
    MFNET_CREDENTIAL_DONT_CACHE = 0x2,
    MFNET_CREDENTIAL_ALLOW_CLEAR_TEXT = 0x4
} MFNetCredentialOptions;
typedef enum _MFNetCredentialRequirements {
    REQUIRE_PROMPT = 0x1,
    REQUIRE_SAVE_SELECTED = 0x2
} MFNetCredentialRequirements;
typedef enum _MFNETSOURCE_CACHE_STATE {
    MFNETSOURCE_CACHE_UNAVAILABLE = 0,
    MFNETSOURCE_CACHE_ACTIVE_WRITING = 1,
    MFNETSOURCE_CACHE_ACTIVE_COMPLETE = 2
} MFNETSOURCE_CACHE_STATE;
typedef enum _MFNETSOURCE_PROTOCOL_TYPE {
    MFNETSOURCE_UNDEFINED = 0x0,
    MFNETSOURCE_HTTP = 0x1,
    MFNETSOURCE_RTSP = 0x2,
    MFNETSOURCE_FILE = 0x3,
    MFNETSOURCE_MULTICAST = 0x4
} MFNETSOURCE_PROTOCOL_TYPE;
typedef enum _MFNETSOURCE_STATISTICS_IDS {
    MFNETSOURCE_RECVPACKETS_ID = 0,
    MFNETSOURCE_LOSTPACKETS_ID = 1,
    MFNETSOURCE_RESENDSREQUESTED_ID = 2,
    MFNETSOURCE_RESENDSRECEIVED_ID = 3,
    MFNETSOURCE_RECOVEREDBYECCPACKETS_ID = 4,
    MFNETSOURCE_RECOVEREDBYRTXPACKETS_ID = 5,
    MFNETSOURCE_OUTPACKETS_ID = 6,
    MFNETSOURCE_RECVRATE_ID = 7,
    MFNETSOURCE_AVGBANDWIDTHBPS_ID = 8,
    MFNETSOURCE_BYTESRECEIVED_ID = 9,
    MFNETSOURCE_PROTOCOL_ID = 10,
    MFNETSOURCE_TRANSPORT_ID = 11,
    MFNETSOURCE_CACHE_STATE_ID = 12,
    MFNETSOURCE_LINKBANDWIDTH_ID = 13,
    MFNETSOURCE_CONTENTBITRATE_ID = 14,
    MFNETSOURCE_SPEEDFACTOR_ID = 15,
    MFNETSOURCE_BUFFERSIZE_ID = 16,
    MFNETSOURCE_BUFFERPROGRESS_ID = 17,
    MFNETSOURCE_LASTBWSWITCHTS_ID = 18,
    MFNETSOURCE_SEEKRANGESTART_ID = 19,
    MFNETSOURCE_SEEKRANGEEND_ID = 20,
    MFNETSOURCE_BUFFERINGCOUNT_ID = 21,
    MFNETSOURCE_INCORRECTLYSIGNEDPACKETS_ID = 22,
    MFNETSOURCE_SIGNEDSESSION_ID = 23,
    MFNETSOURCE_MAXBITRATE_ID = 24,
    MFNETSOURCE_RECEPTION_QUALITY_ID = 25,
    MFNETSOURCE_RECOVEREDPACKETS_ID = 26,
    MFNETSOURCE_VBR_ID = 27,
    MFNETSOURCE_DOWNLOADPROGRESS_ID = 28
} MFNETSOURCE_STATISTICS_IDS;
typedef enum _MFNETSOURCE_TRANSPORT_TYPE {
    MFNETSOURCE_UDP = 0,
    MFNETSOURCE_TCP = 1
} MFNETSOURCE_TRANSPORT_TYPE;
typedef enum MF_OBJECT_TYPE {
    MF_OBJECT_MEDIASOURCE = 0,
    MF_OBJECT_BYTESTREAM = 1,
    MF_OBJECT_INVALID = 2
} MF_OBJECT_TYPE;
typedef enum _MFPOLICYMANAGER_ACTION {
    PEACTION_NO = 0,
    PEACTION_PLAY = 1,
    PEACTION_COPY = 2,
    PEACTION_EXPORT = 3,
    PEACTION_EXTRACT = 4,
    PEACTION_RESERVED1 = 5,
    PEACTION_RESERVED2 = 6,
    PEACTION_RESERVED3 = 7,
    PEACTION_LAST = 7
} MFPOLICYMANAGER_ACTION;
typedef enum _MFRATE_DIRECTION {
    MFRATE_FORWARD = 0,
    MFRATE_REVERSE = 1
} MFRATE_DIRECTION;
typedef enum _MFSequencerTopologyFlags {
    SequencerTopologyFlags_Last = 0x1
} MFSequencerTopologyFlags;
typedef enum _MFSHUTDOWN_STATUS {
    MFSHUTDOWN_INITIATED = 0,
    MFSHUTDOWN_COMPLETED = 1
} MFSHUTDOWN_STATUS;
typedef enum MFSTREAMSINK_MARKER_TYPE {
    MFSTREAMSINK_MARKER_DEFAULT = 0,
    MFSTREAMSINK_MARKER_ENDOFSEGMENT = 1,
    MFSTREAMSINK_MARKER_TICK = 2,
    MFSTREAMSINK_MARKER_EVENT = 3
} MFSTREAMSINK_MARKER_TYPE;
typedef enum MFTIMER_FLAGS {
    MFTIMER_RELATIVE = 0x1
} MFTIMER_FLAGS;
#if (WINVER >= 0x0601)
typedef enum MFTOPOLOGY_DXVA_MODE {
    MFTOPOLOGY_DXVA_DEFAULT = 0,
    MFTOPOLOGY_DXVA_NONE = 1,
    MFTOPOLOGY_DXVA_FULL = 2
} MFTOPOLOGY_DXVA_MODE;
typedef enum MFTOPOLOGY_HARDWARE_MODE {
    MFTOPOLOGY_HWMODE_SOFTWARE_ONLY = 0,
    MFTOPOLOGY_HWMODE_USE_HARDWARE = 1
} MFTOPOLOGY_HARDWARE_MODE;
typedef struct _MFT_REGISTRATION_INFO {
    CLSID clsid;
    GUID guidCategory;
    UINT32 uiFlags;
    LPCWSTR pszName;
    DWORD cInTypes;
    MFT_REGISTER_TYPE_INFO *pInTypes;
    DWORD cOutTypes;
    MFT_REGISTER_TYPE_INFO *pOutTypes;
} MFT_REGISTRATION_INFO;
#endif /*(WINVER >= 0x0601)*/
typedef struct _ASFFlatPicture {
  BYTE  bPictureType;
  DWORD dwDataLen;
} ASF_FLAT_PICTURE;

typedef struct _ASFFlatSynchronisedLyrics {
  BYTE  bTimeStampFormat;
  BYTE  bContentType;
  DWORD dwLyricsLen;
} ASF_FLAT_SYNCHRONISED_LYRICS;
typedef enum SAMPLE_PROTECTION_VERSION {
    SAMPLE_PROTECTION_VERSION_NO = 0,
    SAMPLE_PROTECTION_VERSION_BASIC_LOKI = 1,
    SAMPLE_PROTECTION_VERSION_SCATTER = 2,
    SAMPLE_PROTECTION_VERSION_RC4 = 3
} SAMPLE_PROTECTION_VERSION;
typedef struct _MFINPUTTRUSTAUTHORITY_ACTION {
    MFPOLICYMANAGER_ACTION Action;
    BYTE *pbTicket;
    DWORD cbTicket;
} MFINPUTTRUSTAUTHORITY_ACCESS_ACTION;
typedef struct _MFINPUTTRUSTAUTHORITY_ACCESS_PARAMS {
    DWORD dwSize;
    DWORD dwVer;
    DWORD cbSignatureOffset;
    DWORD cbSignatureSize;
    DWORD cbExtensionOffset;
    DWORD cbExtensionSize;
    DWORD cActions;
    MFINPUTTRUSTAUTHORITY_ACCESS_ACTION rgOutputActions[1];
} MFINPUTTRUSTAUTHORITY_ACCESS_PARAMS;
typedef struct _MFNetCredentialManagerGetParam {
    HRESULT hrOp;
    WINBOOL fAllowLoggedOnUser;
    WINBOOL fClearTextPackage;
    LPCWSTR pszUrl;
    LPCWSTR pszSite;
    LPCWSTR pszRealm;
    LPCWSTR pszPackage;
    LONG nRetries;
} MFNetCredentialManagerGetParam;
#define MEDIASINK_FIXED_STREAMS                 0x00000001
#define MEDIASINK_CANNOT_MATCH_CLOCK            0x00000002
#define MEDIASINK_RATELESS                      0x00000004
#define MEDIASINK_CLOCK_REQUIRED                0x00000008
#define MEDIASINK_CAN_PREROLL                   0x00000010
#define MEDIASINK_REQUIRE_REFERENCE_MEDIATYPE   0x00000020
enum {
    MF_RESOLUTION_MEDIASOURCE = 0x1,
    MF_RESOLUTION_BYTESTREAM = 0x2,
    MF_RESOLUTION_CONTENT_DOES_NOT_HAVE_TO_MATCH_EXTENSION_OR_MIME_TYPE = 0x10,
    MF_RESOLUTION_KEEP_BYTE_STREAM_ALIVE_ON_FAIL = 0x20,
    MF_RESOLUTION_READ = 0x10000,
    MF_RESOLUTION_WRITE = 0x20000
};
#ifndef __IMFASFContentInfo_FWD_DEFINED__
#define __IMFASFContentInfo_FWD_DEFINED__
typedef interface IMFASFContentInfo IMFASFContentInfo;
#ifdef __cplusplus
interface IMFASFContentInfo;
#endif /* __cplusplus */
#endif

#ifndef __IMFASFIndexer_FWD_DEFINED__
#define __IMFASFIndexer_FWD_DEFINED__
typedef interface IMFASFIndexer IMFASFIndexer;
#ifdef __cplusplus
interface IMFASFIndexer;
#endif /* __cplusplus */
#endif

#ifndef __IMFASFMultiplexer_FWD_DEFINED__
#define __IMFASFMultiplexer_FWD_DEFINED__
typedef interface IMFASFMultiplexer IMFASFMultiplexer;
#ifdef __cplusplus
interface IMFASFMultiplexer;
#endif /* __cplusplus */
#endif

#ifndef __IMFASFProfile_FWD_DEFINED__
#define __IMFASFProfile_FWD_DEFINED__
typedef interface IMFASFProfile IMFASFProfile;
#ifdef __cplusplus
interface IMFASFProfile;
#endif /* __cplusplus */
#endif

#ifndef __IMFASFSplitter_FWD_DEFINED__
#define __IMFASFSplitter_FWD_DEFINED__
typedef interface IMFASFSplitter IMFASFSplitter;
#ifdef __cplusplus
interface IMFASFSplitter;
#endif /* __cplusplus */
#endif

#ifndef __IMFPMPServer_FWD_DEFINED__
#define __IMFPMPServer_FWD_DEFINED__
typedef interface IMFPMPServer IMFPMPServer;
#ifdef __cplusplus
interface IMFPMPServer;
#endif /* __cplusplus */
#endif

#ifndef __IMFPresentationClock_FWD_DEFINED__
#define __IMFPresentationClock_FWD_DEFINED__
typedef interface IMFPresentationClock IMFPresentationClock;
#ifdef __cplusplus
interface IMFPresentationClock;
#endif /* __cplusplus */
#endif

#ifndef __IMFNetProxyLocator_FWD_DEFINED__
#define __IMFNetProxyLocator_FWD_DEFINED__
typedef interface IMFNetProxyLocator IMFNetProxyLocator;
#ifdef __cplusplus
interface IMFNetProxyLocator;
#endif /* __cplusplus */
#endif

#ifndef __IMFRemoteDesktopPlugin_FWD_DEFINED__
#define __IMFRemoteDesktopPlugin_FWD_DEFINED__
typedef interface IMFRemoteDesktopPlugin IMFRemoteDesktopPlugin;
#ifdef __cplusplus
interface IMFRemoteDesktopPlugin;
#endif /* __cplusplus */
#endif

#ifndef __IMFTransform_FWD_DEFINED__
#define __IMFTransform_FWD_DEFINED__
typedef interface IMFTransform IMFTransform;
#ifdef __cplusplus
interface IMFTransform;
#endif /* __cplusplus */
#endif

#ifndef __IMFSequencerSource_FWD_DEFINED__
#define __IMFSequencerSource_FWD_DEFINED__
typedef interface IMFSequencerSource IMFSequencerSource;
#ifdef __cplusplus
interface IMFSequencerSource;
#endif /* __cplusplus */
#endif

#ifndef __IMFStreamSink_FWD_DEFINED__
#define __IMFStreamSink_FWD_DEFINED__
typedef interface IMFStreamSink IMFStreamSink;
#ifdef __cplusplus
interface IMFStreamSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFQualityManager_FWD_DEFINED__
#define __IMFQualityManager_FWD_DEFINED__
typedef interface IMFQualityManager IMFQualityManager;
#ifdef __cplusplus
interface IMFQualityManager;
#endif /* __cplusplus */
#endif

#ifndef __IMFTranscodeProfile_FWD_DEFINED__
#define __IMFTranscodeProfile_FWD_DEFINED__
typedef interface IMFTranscodeProfile IMFTranscodeProfile;
#ifdef __cplusplus
interface IMFTranscodeProfile;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IMFAudioStreamVolume interface
 */
#ifndef __IMFAudioStreamVolume_INTERFACE_DEFINED__
#define __IMFAudioStreamVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFAudioStreamVolume, 0x76b1bbdb, 0x4ec8, 0x4f36, 0xb1,0x06, 0x70,0xa9,0x31,0x6d,0xf5,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("76b1bbdb-4ec8-4f36-b106-70a9316df593")
IMFAudioStreamVolume : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetChannelCount(
        UINT32 *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetChannelVolume(
        UINT32 dwIndex,
        const float fLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelVolume(
        UINT32 dwIndex,
        float *pfLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllVolumes(
        UINT32 dwCount,
        const float *pfVolumes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllVolumes(
        UINT32 dwCount,
        float *pfVolumes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFAudioStreamVolume, 0x76b1bbdb, 0x4ec8, 0x4f36, 0xb1,0x06, 0x70,0xa9,0x31,0x6d,0xf5,0x93)
#endif
#else
typedef struct IMFAudioStreamVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFAudioStreamVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFAudioStreamVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFAudioStreamVolume *This);

    /*** IMFAudioStreamVolume methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IMFAudioStreamVolume *This,
        UINT32 *pdwCount);

    HRESULT (STDMETHODCALLTYPE *SetChannelVolume)(
        IMFAudioStreamVolume *This,
        UINT32 dwIndex,
        const float fLevel);

    HRESULT (STDMETHODCALLTYPE *GetChannelVolume)(
        IMFAudioStreamVolume *This,
        UINT32 dwIndex,
        float *pfLevel);

    HRESULT (STDMETHODCALLTYPE *SetAllVolumes)(
        IMFAudioStreamVolume *This,
        UINT32 dwCount,
        const float *pfVolumes);

    HRESULT (STDMETHODCALLTYPE *GetAllVolumes)(
        IMFAudioStreamVolume *This,
        UINT32 dwCount,
        float *pfVolumes);

    END_INTERFACE
} IMFAudioStreamVolumeVtbl;

interface IMFAudioStreamVolume {
    CONST_VTBL IMFAudioStreamVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFAudioStreamVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFAudioStreamVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFAudioStreamVolume_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAudioStreamVolume methods ***/
#define IMFAudioStreamVolume_GetChannelCount(This,pdwCount) (This)->lpVtbl->GetChannelCount(This,pdwCount)
#define IMFAudioStreamVolume_SetChannelVolume(This,dwIndex,fLevel) (This)->lpVtbl->SetChannelVolume(This,dwIndex,fLevel)
#define IMFAudioStreamVolume_GetChannelVolume(This,dwIndex,pfLevel) (This)->lpVtbl->GetChannelVolume(This,dwIndex,pfLevel)
#define IMFAudioStreamVolume_SetAllVolumes(This,dwCount,pfVolumes) (This)->lpVtbl->SetAllVolumes(This,dwCount,pfVolumes)
#define IMFAudioStreamVolume_GetAllVolumes(This,dwCount,pfVolumes) (This)->lpVtbl->GetAllVolumes(This,dwCount,pfVolumes)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFAudioStreamVolume_QueryInterface(IMFAudioStreamVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFAudioStreamVolume_AddRef(IMFAudioStreamVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFAudioStreamVolume_Release(IMFAudioStreamVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAudioStreamVolume methods ***/
static inline HRESULT IMFAudioStreamVolume_GetChannelCount(IMFAudioStreamVolume* This,UINT32 *pdwCount) {
    return This->lpVtbl->GetChannelCount(This,pdwCount);
}
static inline HRESULT IMFAudioStreamVolume_SetChannelVolume(IMFAudioStreamVolume* This,UINT32 dwIndex,const float fLevel) {
    return This->lpVtbl->SetChannelVolume(This,dwIndex,fLevel);
}
static inline HRESULT IMFAudioStreamVolume_GetChannelVolume(IMFAudioStreamVolume* This,UINT32 dwIndex,float *pfLevel) {
    return This->lpVtbl->GetChannelVolume(This,dwIndex,pfLevel);
}
static inline HRESULT IMFAudioStreamVolume_SetAllVolumes(IMFAudioStreamVolume* This,UINT32 dwCount,const float *pfVolumes) {
    return This->lpVtbl->SetAllVolumes(This,dwCount,pfVolumes);
}
static inline HRESULT IMFAudioStreamVolume_GetAllVolumes(IMFAudioStreamVolume* This,UINT32 dwCount,float *pfVolumes) {
    return This->lpVtbl->GetAllVolumes(This,dwCount,pfVolumes);
}
#endif
#endif

#endif


#endif  /* __IMFAudioStreamVolume_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaSink interface
 */
#ifndef __IMFMediaSink_INTERFACE_DEFINED__
#define __IMFMediaSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaSink, 0x6ef2a660, 0x47c0, 0x4666, 0xb1,0x3d, 0xcb,0xb7,0x17,0xf2,0xfa,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6ef2a660-47c0-4666-b13d-cbb717f2fa2c")
IMFMediaSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCharacteristics(
        DWORD *pdwCharacteristics) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStreamSink(
        DWORD dwStreamSinkIdentifier,
        IMFMediaType *pMediaType,
        IMFStreamSink **ppStreamSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStreamSink(
        DWORD dwStreamSinkIdentifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamSinkCount(
        DWORD *pcStreamSinkCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamSinkByIndex(
        DWORD dwIndex,
        IMFStreamSink **ppStreamSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamSinkById(
        DWORD dwStreamSinkIdentifier,
        IMFStreamSink **ppStreamSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPresentationClock(
        IMFPresentationClock *pPresentationClock) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPresentationClock(
        IMFPresentationClock **ppPresentationClock) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaSink, 0x6ef2a660, 0x47c0, 0x4666, 0xb1,0x3d, 0xcb,0xb7,0x17,0xf2,0xfa,0x2c)
#endif
#else
typedef struct IMFMediaSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaSink *This);

    /*** IMFMediaSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        IMFMediaSink *This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *AddStreamSink)(
        IMFMediaSink *This,
        DWORD dwStreamSinkIdentifier,
        IMFMediaType *pMediaType,
        IMFStreamSink **ppStreamSink);

    HRESULT (STDMETHODCALLTYPE *RemoveStreamSink)(
        IMFMediaSink *This,
        DWORD dwStreamSinkIdentifier);

    HRESULT (STDMETHODCALLTYPE *GetStreamSinkCount)(
        IMFMediaSink *This,
        DWORD *pcStreamSinkCount);

    HRESULT (STDMETHODCALLTYPE *GetStreamSinkByIndex)(
        IMFMediaSink *This,
        DWORD dwIndex,
        IMFStreamSink **ppStreamSink);

    HRESULT (STDMETHODCALLTYPE *GetStreamSinkById)(
        IMFMediaSink *This,
        DWORD dwStreamSinkIdentifier,
        IMFStreamSink **ppStreamSink);

    HRESULT (STDMETHODCALLTYPE *SetPresentationClock)(
        IMFMediaSink *This,
        IMFPresentationClock *pPresentationClock);

    HRESULT (STDMETHODCALLTYPE *GetPresentationClock)(
        IMFMediaSink *This,
        IMFPresentationClock **ppPresentationClock);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaSink *This);

    END_INTERFACE
} IMFMediaSinkVtbl;

interface IMFMediaSink {
    CONST_VTBL IMFMediaSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaSink methods ***/
#define IMFMediaSink_GetCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pdwCharacteristics)
#define IMFMediaSink_AddStreamSink(This,dwStreamSinkIdentifier,pMediaType,ppStreamSink) (This)->lpVtbl->AddStreamSink(This,dwStreamSinkIdentifier,pMediaType,ppStreamSink)
#define IMFMediaSink_RemoveStreamSink(This,dwStreamSinkIdentifier) (This)->lpVtbl->RemoveStreamSink(This,dwStreamSinkIdentifier)
#define IMFMediaSink_GetStreamSinkCount(This,pcStreamSinkCount) (This)->lpVtbl->GetStreamSinkCount(This,pcStreamSinkCount)
#define IMFMediaSink_GetStreamSinkByIndex(This,dwIndex,ppStreamSink) (This)->lpVtbl->GetStreamSinkByIndex(This,dwIndex,ppStreamSink)
#define IMFMediaSink_GetStreamSinkById(This,dwStreamSinkIdentifier,ppStreamSink) (This)->lpVtbl->GetStreamSinkById(This,dwStreamSinkIdentifier,ppStreamSink)
#define IMFMediaSink_SetPresentationClock(This,pPresentationClock) (This)->lpVtbl->SetPresentationClock(This,pPresentationClock)
#define IMFMediaSink_GetPresentationClock(This,ppPresentationClock) (This)->lpVtbl->GetPresentationClock(This,ppPresentationClock)
#define IMFMediaSink_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaSink_QueryInterface(IMFMediaSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaSink_AddRef(IMFMediaSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaSink_Release(IMFMediaSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaSink methods ***/
static inline HRESULT IMFMediaSink_GetCharacteristics(IMFMediaSink* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT IMFMediaSink_AddStreamSink(IMFMediaSink* This,DWORD dwStreamSinkIdentifier,IMFMediaType *pMediaType,IMFStreamSink **ppStreamSink) {
    return This->lpVtbl->AddStreamSink(This,dwStreamSinkIdentifier,pMediaType,ppStreamSink);
}
static inline HRESULT IMFMediaSink_RemoveStreamSink(IMFMediaSink* This,DWORD dwStreamSinkIdentifier) {
    return This->lpVtbl->RemoveStreamSink(This,dwStreamSinkIdentifier);
}
static inline HRESULT IMFMediaSink_GetStreamSinkCount(IMFMediaSink* This,DWORD *pcStreamSinkCount) {
    return This->lpVtbl->GetStreamSinkCount(This,pcStreamSinkCount);
}
static inline HRESULT IMFMediaSink_GetStreamSinkByIndex(IMFMediaSink* This,DWORD dwIndex,IMFStreamSink **ppStreamSink) {
    return This->lpVtbl->GetStreamSinkByIndex(This,dwIndex,ppStreamSink);
}
static inline HRESULT IMFMediaSink_GetStreamSinkById(IMFMediaSink* This,DWORD dwStreamSinkIdentifier,IMFStreamSink **ppStreamSink) {
    return This->lpVtbl->GetStreamSinkById(This,dwStreamSinkIdentifier,ppStreamSink);
}
static inline HRESULT IMFMediaSink_SetPresentationClock(IMFMediaSink* This,IMFPresentationClock *pPresentationClock) {
    return This->lpVtbl->SetPresentationClock(This,pPresentationClock);
}
static inline HRESULT IMFMediaSink_GetPresentationClock(IMFMediaSink* This,IMFPresentationClock **ppPresentationClock) {
    return This->lpVtbl->GetPresentationClock(This,ppPresentationClock);
}
static inline HRESULT IMFMediaSink_Shutdown(IMFMediaSink* This) {
    return This->lpVtbl->Shutdown(This);
}
#endif
#endif

#endif


#endif  /* __IMFMediaSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFFinalizableMediaSink interface
 */
#ifndef __IMFFinalizableMediaSink_INTERFACE_DEFINED__
#define __IMFFinalizableMediaSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFFinalizableMediaSink, 0xeaecb74a, 0x9a50, 0x42ce, 0x95,0x41, 0x6a,0x7f,0x57,0xaa,0x4a,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eaecb74a-9a50-42ce-9541-6a7f57aa4ad7")
IMFFinalizableMediaSink : public IMFMediaSink
{
    virtual HRESULT STDMETHODCALLTYPE BeginFinalize(
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndFinalize(
        IMFAsyncResult *pResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFFinalizableMediaSink, 0xeaecb74a, 0x9a50, 0x42ce, 0x95,0x41, 0x6a,0x7f,0x57,0xaa,0x4a,0xd7)
#endif
#else
typedef struct IMFFinalizableMediaSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFFinalizableMediaSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFFinalizableMediaSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFFinalizableMediaSink *This);

    /*** IMFMediaSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        IMFFinalizableMediaSink *This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *AddStreamSink)(
        IMFFinalizableMediaSink *This,
        DWORD dwStreamSinkIdentifier,
        IMFMediaType *pMediaType,
        IMFStreamSink **ppStreamSink);

    HRESULT (STDMETHODCALLTYPE *RemoveStreamSink)(
        IMFFinalizableMediaSink *This,
        DWORD dwStreamSinkIdentifier);

    HRESULT (STDMETHODCALLTYPE *GetStreamSinkCount)(
        IMFFinalizableMediaSink *This,
        DWORD *pcStreamSinkCount);

    HRESULT (STDMETHODCALLTYPE *GetStreamSinkByIndex)(
        IMFFinalizableMediaSink *This,
        DWORD dwIndex,
        IMFStreamSink **ppStreamSink);

    HRESULT (STDMETHODCALLTYPE *GetStreamSinkById)(
        IMFFinalizableMediaSink *This,
        DWORD dwStreamSinkIdentifier,
        IMFStreamSink **ppStreamSink);

    HRESULT (STDMETHODCALLTYPE *SetPresentationClock)(
        IMFFinalizableMediaSink *This,
        IMFPresentationClock *pPresentationClock);

    HRESULT (STDMETHODCALLTYPE *GetPresentationClock)(
        IMFFinalizableMediaSink *This,
        IMFPresentationClock **ppPresentationClock);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFFinalizableMediaSink *This);

    /*** IMFFinalizableMediaSink methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginFinalize)(
        IMFFinalizableMediaSink *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndFinalize)(
        IMFFinalizableMediaSink *This,
        IMFAsyncResult *pResult);

    END_INTERFACE
} IMFFinalizableMediaSinkVtbl;

interface IMFFinalizableMediaSink {
    CONST_VTBL IMFFinalizableMediaSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFFinalizableMediaSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFFinalizableMediaSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFFinalizableMediaSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaSink methods ***/
#define IMFFinalizableMediaSink_GetCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pdwCharacteristics)
#define IMFFinalizableMediaSink_AddStreamSink(This,dwStreamSinkIdentifier,pMediaType,ppStreamSink) (This)->lpVtbl->AddStreamSink(This,dwStreamSinkIdentifier,pMediaType,ppStreamSink)
#define IMFFinalizableMediaSink_RemoveStreamSink(This,dwStreamSinkIdentifier) (This)->lpVtbl->RemoveStreamSink(This,dwStreamSinkIdentifier)
#define IMFFinalizableMediaSink_GetStreamSinkCount(This,pcStreamSinkCount) (This)->lpVtbl->GetStreamSinkCount(This,pcStreamSinkCount)
#define IMFFinalizableMediaSink_GetStreamSinkByIndex(This,dwIndex,ppStreamSink) (This)->lpVtbl->GetStreamSinkByIndex(This,dwIndex,ppStreamSink)
#define IMFFinalizableMediaSink_GetStreamSinkById(This,dwStreamSinkIdentifier,ppStreamSink) (This)->lpVtbl->GetStreamSinkById(This,dwStreamSinkIdentifier,ppStreamSink)
#define IMFFinalizableMediaSink_SetPresentationClock(This,pPresentationClock) (This)->lpVtbl->SetPresentationClock(This,pPresentationClock)
#define IMFFinalizableMediaSink_GetPresentationClock(This,ppPresentationClock) (This)->lpVtbl->GetPresentationClock(This,ppPresentationClock)
#define IMFFinalizableMediaSink_Shutdown(This) (This)->lpVtbl->Shutdown(This)
/*** IMFFinalizableMediaSink methods ***/
#define IMFFinalizableMediaSink_BeginFinalize(This,pCallback,punkState) (This)->lpVtbl->BeginFinalize(This,pCallback,punkState)
#define IMFFinalizableMediaSink_EndFinalize(This,pResult) (This)->lpVtbl->EndFinalize(This,pResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFFinalizableMediaSink_QueryInterface(IMFFinalizableMediaSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFFinalizableMediaSink_AddRef(IMFFinalizableMediaSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFFinalizableMediaSink_Release(IMFFinalizableMediaSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaSink methods ***/
static inline HRESULT IMFFinalizableMediaSink_GetCharacteristics(IMFFinalizableMediaSink* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT IMFFinalizableMediaSink_AddStreamSink(IMFFinalizableMediaSink* This,DWORD dwStreamSinkIdentifier,IMFMediaType *pMediaType,IMFStreamSink **ppStreamSink) {
    return This->lpVtbl->AddStreamSink(This,dwStreamSinkIdentifier,pMediaType,ppStreamSink);
}
static inline HRESULT IMFFinalizableMediaSink_RemoveStreamSink(IMFFinalizableMediaSink* This,DWORD dwStreamSinkIdentifier) {
    return This->lpVtbl->RemoveStreamSink(This,dwStreamSinkIdentifier);
}
static inline HRESULT IMFFinalizableMediaSink_GetStreamSinkCount(IMFFinalizableMediaSink* This,DWORD *pcStreamSinkCount) {
    return This->lpVtbl->GetStreamSinkCount(This,pcStreamSinkCount);
}
static inline HRESULT IMFFinalizableMediaSink_GetStreamSinkByIndex(IMFFinalizableMediaSink* This,DWORD dwIndex,IMFStreamSink **ppStreamSink) {
    return This->lpVtbl->GetStreamSinkByIndex(This,dwIndex,ppStreamSink);
}
static inline HRESULT IMFFinalizableMediaSink_GetStreamSinkById(IMFFinalizableMediaSink* This,DWORD dwStreamSinkIdentifier,IMFStreamSink **ppStreamSink) {
    return This->lpVtbl->GetStreamSinkById(This,dwStreamSinkIdentifier,ppStreamSink);
}
static inline HRESULT IMFFinalizableMediaSink_SetPresentationClock(IMFFinalizableMediaSink* This,IMFPresentationClock *pPresentationClock) {
    return This->lpVtbl->SetPresentationClock(This,pPresentationClock);
}
static inline HRESULT IMFFinalizableMediaSink_GetPresentationClock(IMFFinalizableMediaSink* This,IMFPresentationClock **ppPresentationClock) {
    return This->lpVtbl->GetPresentationClock(This,ppPresentationClock);
}
static inline HRESULT IMFFinalizableMediaSink_Shutdown(IMFFinalizableMediaSink* This) {
    return This->lpVtbl->Shutdown(This);
}
/*** IMFFinalizableMediaSink methods ***/
static inline HRESULT IMFFinalizableMediaSink_BeginFinalize(IMFFinalizableMediaSink* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginFinalize(This,pCallback,punkState);
}
static inline HRESULT IMFFinalizableMediaSink_EndFinalize(IMFFinalizableMediaSink* This,IMFAsyncResult *pResult) {
    return This->lpVtbl->EndFinalize(This,pResult);
}
#endif
#endif

#endif


#endif  /* __IMFFinalizableMediaSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaSinkPreroll interface
 */
#ifndef __IMFMediaSinkPreroll_INTERFACE_DEFINED__
#define __IMFMediaSinkPreroll_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaSinkPreroll, 0x5dfd4b2a, 0x7674, 0x4110, 0xa4,0xe6, 0x8a,0x68,0xfd,0x5f,0x36,0x88);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5dfd4b2a-7674-4110-a4e6-8a68fd5f3688")
IMFMediaSinkPreroll : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NotifyPreroll(
        MFTIME hnsUpcomingStartTime) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaSinkPreroll, 0x5dfd4b2a, 0x7674, 0x4110, 0xa4,0xe6, 0x8a,0x68,0xfd,0x5f,0x36,0x88)
#endif
#else
typedef struct IMFMediaSinkPrerollVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaSinkPreroll *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaSinkPreroll *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaSinkPreroll *This);

    /*** IMFMediaSinkPreroll methods ***/
    HRESULT (STDMETHODCALLTYPE *NotifyPreroll)(
        IMFMediaSinkPreroll *This,
        MFTIME hnsUpcomingStartTime);

    END_INTERFACE
} IMFMediaSinkPrerollVtbl;

interface IMFMediaSinkPreroll {
    CONST_VTBL IMFMediaSinkPrerollVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaSinkPreroll_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaSinkPreroll_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaSinkPreroll_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaSinkPreroll methods ***/
#define IMFMediaSinkPreroll_NotifyPreroll(This,hnsUpcomingStartTime) (This)->lpVtbl->NotifyPreroll(This,hnsUpcomingStartTime)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaSinkPreroll_QueryInterface(IMFMediaSinkPreroll* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaSinkPreroll_AddRef(IMFMediaSinkPreroll* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaSinkPreroll_Release(IMFMediaSinkPreroll* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaSinkPreroll methods ***/
static inline HRESULT IMFMediaSinkPreroll_NotifyPreroll(IMFMediaSinkPreroll* This,MFTIME hnsUpcomingStartTime) {
    return This->lpVtbl->NotifyPreroll(This,hnsUpcomingStartTime);
}
#endif
#endif

#endif


#endif  /* __IMFMediaSinkPreroll_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaStream interface
 */
#ifndef __IMFMediaStream_INTERFACE_DEFINED__
#define __IMFMediaStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaStream, 0xd182108f, 0x4ec6, 0x443f, 0xaa,0x42, 0xa7,0x11,0x06,0xec,0x82,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d182108f-4ec6-443f-aa42-a71106ec825f")
IMFMediaStream : public IMFMediaEventGenerator
{
    virtual HRESULT STDMETHODCALLTYPE GetMediaSource(
        IMFMediaSource **ppMediaSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamDescriptor(
        IMFStreamDescriptor **ppStreamDescriptor) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestSample(
        IUnknown *pToken) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaStream, 0xd182108f, 0x4ec6, 0x443f, 0xaa,0x42, 0xa7,0x11,0x06,0xec,0x82,0x5f)
#endif
#else
typedef struct IMFMediaStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaStream *This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaStream *This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaStream *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaStream *This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaStream *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    /*** IMFMediaStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMediaSource)(
        IMFMediaStream *This,
        IMFMediaSource **ppMediaSource);

    HRESULT (STDMETHODCALLTYPE *GetStreamDescriptor)(
        IMFMediaStream *This,
        IMFStreamDescriptor **ppStreamDescriptor);

    HRESULT (STDMETHODCALLTYPE *RequestSample)(
        IMFMediaStream *This,
        IUnknown *pToken);

    END_INTERFACE
} IMFMediaStreamVtbl;

interface IMFMediaStream {
    CONST_VTBL IMFMediaStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaStream_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFMediaStream_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaStream_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaStream_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaStream_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
/*** IMFMediaStream methods ***/
#define IMFMediaStream_GetMediaSource(This,ppMediaSource) (This)->lpVtbl->GetMediaSource(This,ppMediaSource)
#define IMFMediaStream_GetStreamDescriptor(This,ppStreamDescriptor) (This)->lpVtbl->GetStreamDescriptor(This,ppStreamDescriptor)
#define IMFMediaStream_RequestSample(This,pToken) (This)->lpVtbl->RequestSample(This,pToken)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaStream_QueryInterface(IMFMediaStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaStream_AddRef(IMFMediaStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaStream_Release(IMFMediaStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static inline HRESULT IMFMediaStream_GetEvent(IMFMediaStream* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static inline HRESULT IMFMediaStream_BeginGetEvent(IMFMediaStream* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static inline HRESULT IMFMediaStream_EndGetEvent(IMFMediaStream* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static inline HRESULT IMFMediaStream_QueueEvent(IMFMediaStream* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
/*** IMFMediaStream methods ***/
static inline HRESULT IMFMediaStream_GetMediaSource(IMFMediaStream* This,IMFMediaSource **ppMediaSource) {
    return This->lpVtbl->GetMediaSource(This,ppMediaSource);
}
static inline HRESULT IMFMediaStream_GetStreamDescriptor(IMFMediaStream* This,IMFStreamDescriptor **ppStreamDescriptor) {
    return This->lpVtbl->GetStreamDescriptor(This,ppStreamDescriptor);
}
static inline HRESULT IMFMediaStream_RequestSample(IMFMediaStream* This,IUnknown *pToken) {
    return This->lpVtbl->RequestSample(This,pToken);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFMediaStream_RemoteRequestSample_Proxy(
    IMFMediaStream* This);
void __RPC_STUB IMFMediaStream_RemoteRequestSample_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFMediaStream_RequestSample_Proxy(
    IMFMediaStream* This,
    IUnknown *pToken);
HRESULT __RPC_STUB IMFMediaStream_RequestSample_Stub(
    IMFMediaStream* This);

#endif  /* __IMFMediaStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMetadata interface
 */
#ifndef __IMFMetadata_INTERFACE_DEFINED__
#define __IMFMetadata_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMetadata, 0xf88cfb8c, 0xef16, 0x4991, 0xb4,0x50, 0xcb,0x8c,0x69,0xe5,0x17,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f88cfb8c-ef16-4991-b450-cb8c69e51704")
IMFMetadata : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetLanguage(
        LPCWSTR pwszRFC1766) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguage(
        LPWSTR *ppwszRFC1766) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllLanguages(
        PROPVARIANT *ppvLanguages) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProperty(
        LPCWSTR pwszName,
        const PROPVARIANT *ppvValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        LPCWSTR pwszName,
        PROPVARIANT *ppvValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteProperty(
        LPCWSTR pwszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllPropertyNames(
        PROPVARIANT *ppvNames) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMetadata, 0xf88cfb8c, 0xef16, 0x4991, 0xb4,0x50, 0xcb,0x8c,0x69,0xe5,0x17,0x04)
#endif
#else
typedef struct IMFMetadataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMetadata *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMetadata *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMetadata *This);

    /*** IMFMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IMFMetadata *This,
        LPCWSTR pwszRFC1766);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IMFMetadata *This,
        LPWSTR *ppwszRFC1766);

    HRESULT (STDMETHODCALLTYPE *GetAllLanguages)(
        IMFMetadata *This,
        PROPVARIANT *ppvLanguages);

    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IMFMetadata *This,
        LPCWSTR pwszName,
        const PROPVARIANT *ppvValue);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IMFMetadata *This,
        LPCWSTR pwszName,
        PROPVARIANT *ppvValue);

    HRESULT (STDMETHODCALLTYPE *DeleteProperty)(
        IMFMetadata *This,
        LPCWSTR pwszName);

    HRESULT (STDMETHODCALLTYPE *GetAllPropertyNames)(
        IMFMetadata *This,
        PROPVARIANT *ppvNames);

    END_INTERFACE
} IMFMetadataVtbl;

interface IMFMetadata {
    CONST_VTBL IMFMetadataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMetadata_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMetadata_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMetadata_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMetadata methods ***/
#define IMFMetadata_SetLanguage(This,pwszRFC1766) (This)->lpVtbl->SetLanguage(This,pwszRFC1766)
#define IMFMetadata_GetLanguage(This,ppwszRFC1766) (This)->lpVtbl->GetLanguage(This,ppwszRFC1766)
#define IMFMetadata_GetAllLanguages(This,ppvLanguages) (This)->lpVtbl->GetAllLanguages(This,ppvLanguages)
#define IMFMetadata_SetProperty(This,pwszName,ppvValue) (This)->lpVtbl->SetProperty(This,pwszName,ppvValue)
#define IMFMetadata_GetProperty(This,pwszName,ppvValue) (This)->lpVtbl->GetProperty(This,pwszName,ppvValue)
#define IMFMetadata_DeleteProperty(This,pwszName) (This)->lpVtbl->DeleteProperty(This,pwszName)
#define IMFMetadata_GetAllPropertyNames(This,ppvNames) (This)->lpVtbl->GetAllPropertyNames(This,ppvNames)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMetadata_QueryInterface(IMFMetadata* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMetadata_AddRef(IMFMetadata* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMetadata_Release(IMFMetadata* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMetadata methods ***/
static inline HRESULT IMFMetadata_SetLanguage(IMFMetadata* This,LPCWSTR pwszRFC1766) {
    return This->lpVtbl->SetLanguage(This,pwszRFC1766);
}
static inline HRESULT IMFMetadata_GetLanguage(IMFMetadata* This,LPWSTR *ppwszRFC1766) {
    return This->lpVtbl->GetLanguage(This,ppwszRFC1766);
}
static inline HRESULT IMFMetadata_GetAllLanguages(IMFMetadata* This,PROPVARIANT *ppvLanguages) {
    return This->lpVtbl->GetAllLanguages(This,ppvLanguages);
}
static inline HRESULT IMFMetadata_SetProperty(IMFMetadata* This,LPCWSTR pwszName,const PROPVARIANT *ppvValue) {
    return This->lpVtbl->SetProperty(This,pwszName,ppvValue);
}
static inline HRESULT IMFMetadata_GetProperty(IMFMetadata* This,LPCWSTR pwszName,PROPVARIANT *ppvValue) {
    return This->lpVtbl->GetProperty(This,pwszName,ppvValue);
}
static inline HRESULT IMFMetadata_DeleteProperty(IMFMetadata* This,LPCWSTR pwszName) {
    return This->lpVtbl->DeleteProperty(This,pwszName);
}
static inline HRESULT IMFMetadata_GetAllPropertyNames(IMFMetadata* This,PROPVARIANT *ppvNames) {
    return This->lpVtbl->GetAllPropertyNames(This,ppvNames);
}
#endif
#endif

#endif


#endif  /* __IMFMetadata_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMetadataProvider interface
 */
#ifndef __IMFMetadataProvider_INTERFACE_DEFINED__
#define __IMFMetadataProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMetadataProvider, 0x56181d2d, 0xe221, 0x4adb, 0xb1,0xc8, 0x3c,0xee,0x6a,0x53,0xf7,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56181d2d-e221-4adb-b1c8-3cee6a53f76f")
IMFMetadataProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMFMetadata(
        IMFPresentationDescriptor *pPresentationDescriptor,
        DWORD dwStreamIdentifier,
        DWORD dwFlags,
        IMFMetadata **ppMFMetadata) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMetadataProvider, 0x56181d2d, 0xe221, 0x4adb, 0xb1,0xc8, 0x3c,0xee,0x6a,0x53,0xf7,0x6f)
#endif
#else
typedef struct IMFMetadataProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMetadataProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMetadataProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMetadataProvider *This);

    /*** IMFMetadataProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMFMetadata)(
        IMFMetadataProvider *This,
        IMFPresentationDescriptor *pPresentationDescriptor,
        DWORD dwStreamIdentifier,
        DWORD dwFlags,
        IMFMetadata **ppMFMetadata);

    END_INTERFACE
} IMFMetadataProviderVtbl;

interface IMFMetadataProvider {
    CONST_VTBL IMFMetadataProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMetadataProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMetadataProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMetadataProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMetadataProvider methods ***/
#define IMFMetadataProvider_GetMFMetadata(This,pPresentationDescriptor,dwStreamIdentifier,dwFlags,ppMFMetadata) (This)->lpVtbl->GetMFMetadata(This,pPresentationDescriptor,dwStreamIdentifier,dwFlags,ppMFMetadata)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMetadataProvider_QueryInterface(IMFMetadataProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMetadataProvider_AddRef(IMFMetadataProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMetadataProvider_Release(IMFMetadataProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMetadataProvider methods ***/
static inline HRESULT IMFMetadataProvider_GetMFMetadata(IMFMetadataProvider* This,IMFPresentationDescriptor *pPresentationDescriptor,DWORD dwStreamIdentifier,DWORD dwFlags,IMFMetadata **ppMFMetadata) {
    return This->lpVtbl->GetMFMetadata(This,pPresentationDescriptor,dwStreamIdentifier,dwFlags,ppMFMetadata);
}
#endif
#endif

#endif


#endif  /* __IMFMetadataProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFPresentationTimeSource interface
 */
#ifndef __IMFPresentationTimeSource_INTERFACE_DEFINED__
#define __IMFPresentationTimeSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPresentationTimeSource, 0x7ff12cce, 0xf76f, 0x41c2, 0x86,0x3b, 0x16,0x66,0xc8,0xe5,0xe1,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7ff12cce-f76f-41c2-863b-1666c8e5e139")
IMFPresentationTimeSource : public IMFClock
{
    virtual HRESULT STDMETHODCALLTYPE GetUnderlyingClock(
        IMFClock **ppClock) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPresentationTimeSource, 0x7ff12cce, 0xf76f, 0x41c2, 0x86,0x3b, 0x16,0x66,0xc8,0xe5,0xe1,0x39)
#endif
#else
typedef struct IMFPresentationTimeSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPresentationTimeSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPresentationTimeSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPresentationTimeSource *This);

    /*** IMFClock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClockCharacteristics)(
        IMFPresentationTimeSource *This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *GetCorrelatedTime)(
        IMFPresentationTimeSource *This,
        DWORD dwReserved,
        LONGLONG *pllClockTime,
        MFTIME *phnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *GetContinuityKey)(
        IMFPresentationTimeSource *This,
        DWORD *pdwContinuityKey);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IMFPresentationTimeSource *This,
        DWORD dwReserved,
        MFCLOCK_STATE *peClockState);

    HRESULT (STDMETHODCALLTYPE *GetProperties)(
        IMFPresentationTimeSource *This,
        MFCLOCK_PROPERTIES *pClockProperties);

    /*** IMFPresentationTimeSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUnderlyingClock)(
        IMFPresentationTimeSource *This,
        IMFClock **ppClock);

    END_INTERFACE
} IMFPresentationTimeSourceVtbl;

interface IMFPresentationTimeSource {
    CONST_VTBL IMFPresentationTimeSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPresentationTimeSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPresentationTimeSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPresentationTimeSource_Release(This) (This)->lpVtbl->Release(This)
/*** IMFClock methods ***/
#define IMFPresentationTimeSource_GetClockCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics)
#define IMFPresentationTimeSource_GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime) (This)->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime)
#define IMFPresentationTimeSource_GetContinuityKey(This,pdwContinuityKey) (This)->lpVtbl->GetContinuityKey(This,pdwContinuityKey)
#define IMFPresentationTimeSource_GetState(This,dwReserved,peClockState) (This)->lpVtbl->GetState(This,dwReserved,peClockState)
#define IMFPresentationTimeSource_GetProperties(This,pClockProperties) (This)->lpVtbl->GetProperties(This,pClockProperties)
/*** IMFPresentationTimeSource methods ***/
#define IMFPresentationTimeSource_GetUnderlyingClock(This,ppClock) (This)->lpVtbl->GetUnderlyingClock(This,ppClock)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPresentationTimeSource_QueryInterface(IMFPresentationTimeSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPresentationTimeSource_AddRef(IMFPresentationTimeSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPresentationTimeSource_Release(IMFPresentationTimeSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFClock methods ***/
static inline HRESULT IMFPresentationTimeSource_GetClockCharacteristics(IMFPresentationTimeSource* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT IMFPresentationTimeSource_GetCorrelatedTime(IMFPresentationTimeSource* This,DWORD dwReserved,LONGLONG *pllClockTime,MFTIME *phnsSystemTime) {
    return This->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime);
}
static inline HRESULT IMFPresentationTimeSource_GetContinuityKey(IMFPresentationTimeSource* This,DWORD *pdwContinuityKey) {
    return This->lpVtbl->GetContinuityKey(This,pdwContinuityKey);
}
static inline HRESULT IMFPresentationTimeSource_GetState(IMFPresentationTimeSource* This,DWORD dwReserved,MFCLOCK_STATE *peClockState) {
    return This->lpVtbl->GetState(This,dwReserved,peClockState);
}
static inline HRESULT IMFPresentationTimeSource_GetProperties(IMFPresentationTimeSource* This,MFCLOCK_PROPERTIES *pClockProperties) {
    return This->lpVtbl->GetProperties(This,pClockProperties);
}
/*** IMFPresentationTimeSource methods ***/
static inline HRESULT IMFPresentationTimeSource_GetUnderlyingClock(IMFPresentationTimeSource* This,IMFClock **ppClock) {
    return This->lpVtbl->GetUnderlyingClock(This,ppClock);
}
#endif
#endif

#endif


#endif  /* __IMFPresentationTimeSource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFPresentationClock interface
 */
#ifndef __IMFPresentationClock_INTERFACE_DEFINED__
#define __IMFPresentationClock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPresentationClock, 0x868ce85c, 0x8ea9, 0x4f55, 0xab,0x82, 0xb0,0x09,0xa9,0x10,0xa8,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("868ce85c-8ea9-4f55-ab82-b009a910a805")
IMFPresentationClock : public IMFClock
{
    virtual HRESULT STDMETHODCALLTYPE SetTimeSource(
        IMFPresentationTimeSource *pTimeSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimeSource(
        IMFPresentationTimeSource **ppTimeSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTime(
        MFTIME *phnsClockTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddClockStateSink(
        IMFClockStateSink *pStateSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveClockStateSink(
        IMFClockStateSink *pStateSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        LONGLONG llClockStartOffset) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPresentationClock, 0x868ce85c, 0x8ea9, 0x4f55, 0xab,0x82, 0xb0,0x09,0xa9,0x10,0xa8,0x05)
#endif
#else
typedef struct IMFPresentationClockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPresentationClock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPresentationClock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPresentationClock *This);

    /*** IMFClock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClockCharacteristics)(
        IMFPresentationClock *This,
        DWORD *pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *GetCorrelatedTime)(
        IMFPresentationClock *This,
        DWORD dwReserved,
        LONGLONG *pllClockTime,
        MFTIME *phnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *GetContinuityKey)(
        IMFPresentationClock *This,
        DWORD *pdwContinuityKey);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IMFPresentationClock *This,
        DWORD dwReserved,
        MFCLOCK_STATE *peClockState);

    HRESULT (STDMETHODCALLTYPE *GetProperties)(
        IMFPresentationClock *This,
        MFCLOCK_PROPERTIES *pClockProperties);

    /*** IMFPresentationClock methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTimeSource)(
        IMFPresentationClock *This,
        IMFPresentationTimeSource *pTimeSource);

    HRESULT (STDMETHODCALLTYPE *GetTimeSource)(
        IMFPresentationClock *This,
        IMFPresentationTimeSource **ppTimeSource);

    HRESULT (STDMETHODCALLTYPE *GetTime)(
        IMFPresentationClock *This,
        MFTIME *phnsClockTime);

    HRESULT (STDMETHODCALLTYPE *AddClockStateSink)(
        IMFPresentationClock *This,
        IMFClockStateSink *pStateSink);

    HRESULT (STDMETHODCALLTYPE *RemoveClockStateSink)(
        IMFPresentationClock *This,
        IMFClockStateSink *pStateSink);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IMFPresentationClock *This,
        LONGLONG llClockStartOffset);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMFPresentationClock *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFPresentationClock *This);

    END_INTERFACE
} IMFPresentationClockVtbl;

interface IMFPresentationClock {
    CONST_VTBL IMFPresentationClockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPresentationClock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPresentationClock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPresentationClock_Release(This) (This)->lpVtbl->Release(This)
/*** IMFClock methods ***/
#define IMFPresentationClock_GetClockCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics)
#define IMFPresentationClock_GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime) (This)->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime)
#define IMFPresentationClock_GetContinuityKey(This,pdwContinuityKey) (This)->lpVtbl->GetContinuityKey(This,pdwContinuityKey)
#define IMFPresentationClock_GetState(This,dwReserved,peClockState) (This)->lpVtbl->GetState(This,dwReserved,peClockState)
#define IMFPresentationClock_GetProperties(This,pClockProperties) (This)->lpVtbl->GetProperties(This,pClockProperties)
/*** IMFPresentationClock methods ***/
#define IMFPresentationClock_SetTimeSource(This,pTimeSource) (This)->lpVtbl->SetTimeSource(This,pTimeSource)
#define IMFPresentationClock_GetTimeSource(This,ppTimeSource) (This)->lpVtbl->GetTimeSource(This,ppTimeSource)
#define IMFPresentationClock_GetTime(This,phnsClockTime) (This)->lpVtbl->GetTime(This,phnsClockTime)
#define IMFPresentationClock_AddClockStateSink(This,pStateSink) (This)->lpVtbl->AddClockStateSink(This,pStateSink)
#define IMFPresentationClock_RemoveClockStateSink(This,pStateSink) (This)->lpVtbl->RemoveClockStateSink(This,pStateSink)
#define IMFPresentationClock_Start(This,llClockStartOffset) (This)->lpVtbl->Start(This,llClockStartOffset)
#define IMFPresentationClock_Stop(This) (This)->lpVtbl->Stop(This)
#define IMFPresentationClock_Pause(This) (This)->lpVtbl->Pause(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPresentationClock_QueryInterface(IMFPresentationClock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPresentationClock_AddRef(IMFPresentationClock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPresentationClock_Release(IMFPresentationClock* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFClock methods ***/
static inline HRESULT IMFPresentationClock_GetClockCharacteristics(IMFPresentationClock* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetClockCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT IMFPresentationClock_GetCorrelatedTime(IMFPresentationClock* This,DWORD dwReserved,LONGLONG *pllClockTime,MFTIME *phnsSystemTime) {
    return This->lpVtbl->GetCorrelatedTime(This,dwReserved,pllClockTime,phnsSystemTime);
}
static inline HRESULT IMFPresentationClock_GetContinuityKey(IMFPresentationClock* This,DWORD *pdwContinuityKey) {
    return This->lpVtbl->GetContinuityKey(This,pdwContinuityKey);
}
static inline HRESULT IMFPresentationClock_GetState(IMFPresentationClock* This,DWORD dwReserved,MFCLOCK_STATE *peClockState) {
    return This->lpVtbl->GetState(This,dwReserved,peClockState);
}
static inline HRESULT IMFPresentationClock_GetProperties(IMFPresentationClock* This,MFCLOCK_PROPERTIES *pClockProperties) {
    return This->lpVtbl->GetProperties(This,pClockProperties);
}
/*** IMFPresentationClock methods ***/
static inline HRESULT IMFPresentationClock_SetTimeSource(IMFPresentationClock* This,IMFPresentationTimeSource *pTimeSource) {
    return This->lpVtbl->SetTimeSource(This,pTimeSource);
}
static inline HRESULT IMFPresentationClock_GetTimeSource(IMFPresentationClock* This,IMFPresentationTimeSource **ppTimeSource) {
    return This->lpVtbl->GetTimeSource(This,ppTimeSource);
}
static inline HRESULT IMFPresentationClock_GetTime(IMFPresentationClock* This,MFTIME *phnsClockTime) {
    return This->lpVtbl->GetTime(This,phnsClockTime);
}
static inline HRESULT IMFPresentationClock_AddClockStateSink(IMFPresentationClock* This,IMFClockStateSink *pStateSink) {
    return This->lpVtbl->AddClockStateSink(This,pStateSink);
}
static inline HRESULT IMFPresentationClock_RemoveClockStateSink(IMFPresentationClock* This,IMFClockStateSink *pStateSink) {
    return This->lpVtbl->RemoveClockStateSink(This,pStateSink);
}
static inline HRESULT IMFPresentationClock_Start(IMFPresentationClock* This,LONGLONG llClockStartOffset) {
    return This->lpVtbl->Start(This,llClockStartOffset);
}
static inline HRESULT IMFPresentationClock_Stop(IMFPresentationClock* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IMFPresentationClock_Pause(IMFPresentationClock* This) {
    return This->lpVtbl->Pause(This);
}
#endif
#endif

#endif


#endif  /* __IMFPresentationClock_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFRateControl interface
 */
#ifndef __IMFRateControl_INTERFACE_DEFINED__
#define __IMFRateControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFRateControl, 0x88ddcd21, 0x03c3, 0x4275, 0x91,0xed, 0x55,0xee,0x39,0x29,0x32,0x8f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("88ddcd21-03c3-4275-91ed-55ee3929328f")
IMFRateControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetRate(
        WINBOOL fThin,
        float flRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRate(
        WINBOOL *pfThin,
        float *pflRate) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFRateControl, 0x88ddcd21, 0x03c3, 0x4275, 0x91,0xed, 0x55,0xee,0x39,0x29,0x32,0x8f)
#endif
#else
typedef struct IMFRateControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFRateControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFRateControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFRateControl *This);

    /*** IMFRateControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRate)(
        IMFRateControl *This,
        WINBOOL fThin,
        float flRate);

    HRESULT (STDMETHODCALLTYPE *GetRate)(
        IMFRateControl *This,
        WINBOOL *pfThin,
        float *pflRate);

    END_INTERFACE
} IMFRateControlVtbl;

interface IMFRateControl {
    CONST_VTBL IMFRateControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFRateControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFRateControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFRateControl_Release(This) (This)->lpVtbl->Release(This)
/*** IMFRateControl methods ***/
#define IMFRateControl_SetRate(This,fThin,flRate) (This)->lpVtbl->SetRate(This,fThin,flRate)
#define IMFRateControl_GetRate(This,pfThin,pflRate) (This)->lpVtbl->GetRate(This,pfThin,pflRate)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFRateControl_QueryInterface(IMFRateControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFRateControl_AddRef(IMFRateControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFRateControl_Release(IMFRateControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFRateControl methods ***/
static inline HRESULT IMFRateControl_SetRate(IMFRateControl* This,WINBOOL fThin,float flRate) {
    return This->lpVtbl->SetRate(This,fThin,flRate);
}
static inline HRESULT IMFRateControl_GetRate(IMFRateControl* This,WINBOOL *pfThin,float *pflRate) {
    return This->lpVtbl->GetRate(This,pfThin,pflRate);
}
#endif
#endif

#endif


#endif  /* __IMFRateControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFRateSupport interface
 */
#ifndef __IMFRateSupport_INTERFACE_DEFINED__
#define __IMFRateSupport_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFRateSupport, 0x0a9ccdbc, 0xd797, 0x4563, 0x96,0x67, 0x94,0xec,0x5d,0x79,0x29,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0a9ccdbc-d797-4563-9667-94ec5d79292d")
IMFRateSupport : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSlowestRate(
        MFRATE_DIRECTION eDirection,
        WINBOOL fThin,
        float *pflRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFastestRate(
        MFRATE_DIRECTION eDirection,
        WINBOOL fThin,
        float *pflRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRateSupported(
        WINBOOL fThin,
        float flRate,
        float *pflNearestSupportedRate) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFRateSupport, 0x0a9ccdbc, 0xd797, 0x4563, 0x96,0x67, 0x94,0xec,0x5d,0x79,0x29,0x2d)
#endif
#else
typedef struct IMFRateSupportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFRateSupport *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFRateSupport *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFRateSupport *This);

    /*** IMFRateSupport methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSlowestRate)(
        IMFRateSupport *This,
        MFRATE_DIRECTION eDirection,
        WINBOOL fThin,
        float *pflRate);

    HRESULT (STDMETHODCALLTYPE *GetFastestRate)(
        IMFRateSupport *This,
        MFRATE_DIRECTION eDirection,
        WINBOOL fThin,
        float *pflRate);

    HRESULT (STDMETHODCALLTYPE *IsRateSupported)(
        IMFRateSupport *This,
        WINBOOL fThin,
        float flRate,
        float *pflNearestSupportedRate);

    END_INTERFACE
} IMFRateSupportVtbl;

interface IMFRateSupport {
    CONST_VTBL IMFRateSupportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFRateSupport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFRateSupport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFRateSupport_Release(This) (This)->lpVtbl->Release(This)
/*** IMFRateSupport methods ***/
#define IMFRateSupport_GetSlowestRate(This,eDirection,fThin,pflRate) (This)->lpVtbl->GetSlowestRate(This,eDirection,fThin,pflRate)
#define IMFRateSupport_GetFastestRate(This,eDirection,fThin,pflRate) (This)->lpVtbl->GetFastestRate(This,eDirection,fThin,pflRate)
#define IMFRateSupport_IsRateSupported(This,fThin,flRate,pflNearestSupportedRate) (This)->lpVtbl->IsRateSupported(This,fThin,flRate,pflNearestSupportedRate)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFRateSupport_QueryInterface(IMFRateSupport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFRateSupport_AddRef(IMFRateSupport* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFRateSupport_Release(IMFRateSupport* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFRateSupport methods ***/
static inline HRESULT IMFRateSupport_GetSlowestRate(IMFRateSupport* This,MFRATE_DIRECTION eDirection,WINBOOL fThin,float *pflRate) {
    return This->lpVtbl->GetSlowestRate(This,eDirection,fThin,pflRate);
}
static inline HRESULT IMFRateSupport_GetFastestRate(IMFRateSupport* This,MFRATE_DIRECTION eDirection,WINBOOL fThin,float *pflRate) {
    return This->lpVtbl->GetFastestRate(This,eDirection,fThin,pflRate);
}
static inline HRESULT IMFRateSupport_IsRateSupported(IMFRateSupport* This,WINBOOL fThin,float flRate,float *pflNearestSupportedRate) {
    return This->lpVtbl->IsRateSupported(This,fThin,flRate,pflNearestSupportedRate);
}
#endif
#endif

#endif


#endif  /* __IMFRateSupport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSampleGrabberSinkCallback interface
 */
#ifndef __IMFSampleGrabberSinkCallback_INTERFACE_DEFINED__
#define __IMFSampleGrabberSinkCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSampleGrabberSinkCallback, 0x8c7b80bf, 0xee42, 0x4b59, 0xb1,0xdf, 0x55,0x66,0x8e,0x1b,0xdc,0xa8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8c7b80bf-ee42-4b59-b1df-55668e1bdca8")
IMFSampleGrabberSinkCallback : public IMFClockStateSink
{
    virtual HRESULT STDMETHODCALLTYPE OnSetPresentationClock(
        IMFPresentationClock *pPresentationClock) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnProcessSample(
        REFGUID guidMajorMediaType,
        DWORD dwSampleFlags,
        LONGLONG llSampleTime,
        LONGLONG llSampleDuration,
        const BYTE *pSampleBuffer,
        DWORD dwSampleSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnShutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSampleGrabberSinkCallback, 0x8c7b80bf, 0xee42, 0x4b59, 0xb1,0xdf, 0x55,0x66,0x8e,0x1b,0xdc,0xa8)
#endif
#else
typedef struct IMFSampleGrabberSinkCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSampleGrabberSinkCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSampleGrabberSinkCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSampleGrabberSinkCallback *This);

    /*** IMFClockStateSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnClockStart)(
        IMFSampleGrabberSinkCallback *This,
        MFTIME hnsSystemTime,
        LONGLONG llClockStartOffset);

    HRESULT (STDMETHODCALLTYPE *OnClockStop)(
        IMFSampleGrabberSinkCallback *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockPause)(
        IMFSampleGrabberSinkCallback *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockRestart)(
        IMFSampleGrabberSinkCallback *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockSetRate)(
        IMFSampleGrabberSinkCallback *This,
        MFTIME hnsSystemTime,
        float flRate);

    /*** IMFSampleGrabberSinkCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSetPresentationClock)(
        IMFSampleGrabberSinkCallback *This,
        IMFPresentationClock *pPresentationClock);

    HRESULT (STDMETHODCALLTYPE *OnProcessSample)(
        IMFSampleGrabberSinkCallback *This,
        REFGUID guidMajorMediaType,
        DWORD dwSampleFlags,
        LONGLONG llSampleTime,
        LONGLONG llSampleDuration,
        const BYTE *pSampleBuffer,
        DWORD dwSampleSize);

    HRESULT (STDMETHODCALLTYPE *OnShutdown)(
        IMFSampleGrabberSinkCallback *This);

    END_INTERFACE
} IMFSampleGrabberSinkCallbackVtbl;

interface IMFSampleGrabberSinkCallback {
    CONST_VTBL IMFSampleGrabberSinkCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSampleGrabberSinkCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSampleGrabberSinkCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSampleGrabberSinkCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFClockStateSink methods ***/
#define IMFSampleGrabberSinkCallback_OnClockStart(This,hnsSystemTime,llClockStartOffset) (This)->lpVtbl->OnClockStart(This,hnsSystemTime,llClockStartOffset)
#define IMFSampleGrabberSinkCallback_OnClockStop(This,hnsSystemTime) (This)->lpVtbl->OnClockStop(This,hnsSystemTime)
#define IMFSampleGrabberSinkCallback_OnClockPause(This,hnsSystemTime) (This)->lpVtbl->OnClockPause(This,hnsSystemTime)
#define IMFSampleGrabberSinkCallback_OnClockRestart(This,hnsSystemTime) (This)->lpVtbl->OnClockRestart(This,hnsSystemTime)
#define IMFSampleGrabberSinkCallback_OnClockSetRate(This,hnsSystemTime,flRate) (This)->lpVtbl->OnClockSetRate(This,hnsSystemTime,flRate)
/*** IMFSampleGrabberSinkCallback methods ***/
#define IMFSampleGrabberSinkCallback_OnSetPresentationClock(This,pPresentationClock) (This)->lpVtbl->OnSetPresentationClock(This,pPresentationClock)
#define IMFSampleGrabberSinkCallback_OnProcessSample(This,guidMajorMediaType,dwSampleFlags,llSampleTime,llSampleDuration,pSampleBuffer,dwSampleSize) (This)->lpVtbl->OnProcessSample(This,guidMajorMediaType,dwSampleFlags,llSampleTime,llSampleDuration,pSampleBuffer,dwSampleSize)
#define IMFSampleGrabberSinkCallback_OnShutdown(This) (This)->lpVtbl->OnShutdown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSampleGrabberSinkCallback_QueryInterface(IMFSampleGrabberSinkCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSampleGrabberSinkCallback_AddRef(IMFSampleGrabberSinkCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSampleGrabberSinkCallback_Release(IMFSampleGrabberSinkCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFClockStateSink methods ***/
static inline HRESULT IMFSampleGrabberSinkCallback_OnClockStart(IMFSampleGrabberSinkCallback* This,MFTIME hnsSystemTime,LONGLONG llClockStartOffset) {
    return This->lpVtbl->OnClockStart(This,hnsSystemTime,llClockStartOffset);
}
static inline HRESULT IMFSampleGrabberSinkCallback_OnClockStop(IMFSampleGrabberSinkCallback* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockStop(This,hnsSystemTime);
}
static inline HRESULT IMFSampleGrabberSinkCallback_OnClockPause(IMFSampleGrabberSinkCallback* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockPause(This,hnsSystemTime);
}
static inline HRESULT IMFSampleGrabberSinkCallback_OnClockRestart(IMFSampleGrabberSinkCallback* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockRestart(This,hnsSystemTime);
}
static inline HRESULT IMFSampleGrabberSinkCallback_OnClockSetRate(IMFSampleGrabberSinkCallback* This,MFTIME hnsSystemTime,float flRate) {
    return This->lpVtbl->OnClockSetRate(This,hnsSystemTime,flRate);
}
/*** IMFSampleGrabberSinkCallback methods ***/
static inline HRESULT IMFSampleGrabberSinkCallback_OnSetPresentationClock(IMFSampleGrabberSinkCallback* This,IMFPresentationClock *pPresentationClock) {
    return This->lpVtbl->OnSetPresentationClock(This,pPresentationClock);
}
static inline HRESULT IMFSampleGrabberSinkCallback_OnProcessSample(IMFSampleGrabberSinkCallback* This,REFGUID guidMajorMediaType,DWORD dwSampleFlags,LONGLONG llSampleTime,LONGLONG llSampleDuration,const BYTE *pSampleBuffer,DWORD dwSampleSize) {
    return This->lpVtbl->OnProcessSample(This,guidMajorMediaType,dwSampleFlags,llSampleTime,llSampleDuration,pSampleBuffer,dwSampleSize);
}
static inline HRESULT IMFSampleGrabberSinkCallback_OnShutdown(IMFSampleGrabberSinkCallback* This) {
    return This->lpVtbl->OnShutdown(This);
}
#endif
#endif

#endif


#endif  /* __IMFSampleGrabberSinkCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFShutdown interface
 */
#ifndef __IMFShutdown_INTERFACE_DEFINED__
#define __IMFShutdown_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFShutdown, 0x97ec2ea4, 0x0e42, 0x4937, 0x97,0xac, 0x9d,0x6d,0x32,0x88,0x24,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("97ec2ea4-0e42-4937-97ac-9d6d328824e1")
IMFShutdown : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetShutdownStatus(
        MFSHUTDOWN_STATUS *pStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFShutdown, 0x97ec2ea4, 0x0e42, 0x4937, 0x97,0xac, 0x9d,0x6d,0x32,0x88,0x24,0xe1)
#endif
#else
typedef struct IMFShutdownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFShutdown *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFShutdown *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFShutdown *This);

    /*** IMFShutdown methods ***/
    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFShutdown *This);

    HRESULT (STDMETHODCALLTYPE *GetShutdownStatus)(
        IMFShutdown *This,
        MFSHUTDOWN_STATUS *pStatus);

    END_INTERFACE
} IMFShutdownVtbl;

interface IMFShutdown {
    CONST_VTBL IMFShutdownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFShutdown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFShutdown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFShutdown_Release(This) (This)->lpVtbl->Release(This)
/*** IMFShutdown methods ***/
#define IMFShutdown_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#define IMFShutdown_GetShutdownStatus(This,pStatus) (This)->lpVtbl->GetShutdownStatus(This,pStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFShutdown_QueryInterface(IMFShutdown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFShutdown_AddRef(IMFShutdown* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFShutdown_Release(IMFShutdown* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFShutdown methods ***/
static inline HRESULT IMFShutdown_Shutdown(IMFShutdown* This) {
    return This->lpVtbl->Shutdown(This);
}
static inline HRESULT IMFShutdown_GetShutdownStatus(IMFShutdown* This,MFSHUTDOWN_STATUS *pStatus) {
    return This->lpVtbl->GetShutdownStatus(This,pStatus);
}
#endif
#endif

#endif


#endif  /* __IMFShutdown_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSimpleAudioVolume interface
 */
#ifndef __IMFSimpleAudioVolume_INTERFACE_DEFINED__
#define __IMFSimpleAudioVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSimpleAudioVolume, 0x089edf13, 0xcf71, 0x4338, 0x8d,0x13, 0x9e,0x56,0x9d,0xbd,0xc3,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("089edf13-cf71-4338-8d13-9e569dbdc319")
IMFSimpleAudioVolume : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetMasterVolume(
        float fLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMasterVolume(
        float *pfLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMute(
        const WINBOOL bMute) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMute(
        WINBOOL *pbMute) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSimpleAudioVolume, 0x089edf13, 0xcf71, 0x4338, 0x8d,0x13, 0x9e,0x56,0x9d,0xbd,0xc3,0x19)
#endif
#else
typedef struct IMFSimpleAudioVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSimpleAudioVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSimpleAudioVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSimpleAudioVolume *This);

    /*** IMFSimpleAudioVolume methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMasterVolume)(
        IMFSimpleAudioVolume *This,
        float fLevel);

    HRESULT (STDMETHODCALLTYPE *GetMasterVolume)(
        IMFSimpleAudioVolume *This,
        float *pfLevel);

    HRESULT (STDMETHODCALLTYPE *SetMute)(
        IMFSimpleAudioVolume *This,
        const WINBOOL bMute);

    HRESULT (STDMETHODCALLTYPE *GetMute)(
        IMFSimpleAudioVolume *This,
        WINBOOL *pbMute);

    END_INTERFACE
} IMFSimpleAudioVolumeVtbl;

interface IMFSimpleAudioVolume {
    CONST_VTBL IMFSimpleAudioVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSimpleAudioVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSimpleAudioVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSimpleAudioVolume_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSimpleAudioVolume methods ***/
#define IMFSimpleAudioVolume_SetMasterVolume(This,fLevel) (This)->lpVtbl->SetMasterVolume(This,fLevel)
#define IMFSimpleAudioVolume_GetMasterVolume(This,pfLevel) (This)->lpVtbl->GetMasterVolume(This,pfLevel)
#define IMFSimpleAudioVolume_SetMute(This,bMute) (This)->lpVtbl->SetMute(This,bMute)
#define IMFSimpleAudioVolume_GetMute(This,pbMute) (This)->lpVtbl->GetMute(This,pbMute)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSimpleAudioVolume_QueryInterface(IMFSimpleAudioVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSimpleAudioVolume_AddRef(IMFSimpleAudioVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSimpleAudioVolume_Release(IMFSimpleAudioVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSimpleAudioVolume methods ***/
static inline HRESULT IMFSimpleAudioVolume_SetMasterVolume(IMFSimpleAudioVolume* This,float fLevel) {
    return This->lpVtbl->SetMasterVolume(This,fLevel);
}
static inline HRESULT IMFSimpleAudioVolume_GetMasterVolume(IMFSimpleAudioVolume* This,float *pfLevel) {
    return This->lpVtbl->GetMasterVolume(This,pfLevel);
}
static inline HRESULT IMFSimpleAudioVolume_SetMute(IMFSimpleAudioVolume* This,const WINBOOL bMute) {
    return This->lpVtbl->SetMute(This,bMute);
}
static inline HRESULT IMFSimpleAudioVolume_GetMute(IMFSimpleAudioVolume* This,WINBOOL *pbMute) {
    return This->lpVtbl->GetMute(This,pbMute);
}
#endif
#endif

#endif


#endif  /* __IMFSimpleAudioVolume_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSourceResolver interface
 */
#ifndef __IMFSourceResolver_INTERFACE_DEFINED__
#define __IMFSourceResolver_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSourceResolver, 0xfbe5a32d, 0xa497, 0x4b61, 0xbb,0x85, 0x97,0xb1,0xa8,0x48,0xa6,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fbe5a32d-a497-4b61-bb85-97b1a848a6e3")
IMFSourceResolver : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateObjectFromURL(
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateObjectFromByteStream(
        IMFByteStream *pByteStream,
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginCreateObjectFromURL(
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        IUnknown **ppIUnknownCancelCookie,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndCreateObjectFromURL(
        IMFAsyncResult *pResult,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginCreateObjectFromByteStream(
        IMFByteStream *pByteStream,
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        IUnknown **ppIUnknownCancelCookie,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndCreateObjectFromByteStream(
        IMFAsyncResult *pResult,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelObjectCreation(
        IUnknown *pIUnknownCancelCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSourceResolver, 0xfbe5a32d, 0xa497, 0x4b61, 0xbb,0x85, 0x97,0xb1,0xa8,0x48,0xa6,0xe3)
#endif
#else
typedef struct IMFSourceResolverVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSourceResolver *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSourceResolver *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSourceResolver *This);

    /*** IMFSourceResolver methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateObjectFromURL)(
        IMFSourceResolver *This,
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject);

    HRESULT (STDMETHODCALLTYPE *CreateObjectFromByteStream)(
        IMFSourceResolver *This,
        IMFByteStream *pByteStream,
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject);

    HRESULT (STDMETHODCALLTYPE *BeginCreateObjectFromURL)(
        IMFSourceResolver *This,
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        IUnknown **ppIUnknownCancelCookie,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndCreateObjectFromURL)(
        IMFSourceResolver *This,
        IMFAsyncResult *pResult,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject);

    HRESULT (STDMETHODCALLTYPE *BeginCreateObjectFromByteStream)(
        IMFSourceResolver *This,
        IMFByteStream *pByteStream,
        LPCWSTR pwszURL,
        DWORD dwFlags,
        IPropertyStore *pProps,
        IUnknown **ppIUnknownCancelCookie,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndCreateObjectFromByteStream)(
        IMFSourceResolver *This,
        IMFAsyncResult *pResult,
        MF_OBJECT_TYPE *pObjectType,
        IUnknown **ppObject);

    HRESULT (STDMETHODCALLTYPE *CancelObjectCreation)(
        IMFSourceResolver *This,
        IUnknown *pIUnknownCancelCookie);

    END_INTERFACE
} IMFSourceResolverVtbl;

interface IMFSourceResolver {
    CONST_VTBL IMFSourceResolverVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSourceResolver_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSourceResolver_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSourceResolver_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSourceResolver methods ***/
#define IMFSourceResolver_CreateObjectFromURL(This,pwszURL,dwFlags,pProps,pObjectType,ppObject) (This)->lpVtbl->CreateObjectFromURL(This,pwszURL,dwFlags,pProps,pObjectType,ppObject)
#define IMFSourceResolver_CreateObjectFromByteStream(This,pByteStream,pwszURL,dwFlags,pProps,pObjectType,ppObject) (This)->lpVtbl->CreateObjectFromByteStream(This,pByteStream,pwszURL,dwFlags,pProps,pObjectType,ppObject)
#define IMFSourceResolver_BeginCreateObjectFromURL(This,pwszURL,dwFlags,pProps,ppIUnknownCancelCookie,pCallback,punkState) (This)->lpVtbl->BeginCreateObjectFromURL(This,pwszURL,dwFlags,pProps,ppIUnknownCancelCookie,pCallback,punkState)
#define IMFSourceResolver_EndCreateObjectFromURL(This,pResult,pObjectType,ppObject) (This)->lpVtbl->EndCreateObjectFromURL(This,pResult,pObjectType,ppObject)
#define IMFSourceResolver_BeginCreateObjectFromByteStream(This,pByteStream,pwszURL,dwFlags,pProps,ppIUnknownCancelCookie,pCallback,punkState) (This)->lpVtbl->BeginCreateObjectFromByteStream(This,pByteStream,pwszURL,dwFlags,pProps,ppIUnknownCancelCookie,pCallback,punkState)
#define IMFSourceResolver_EndCreateObjectFromByteStream(This,pResult,pObjectType,ppObject) (This)->lpVtbl->EndCreateObjectFromByteStream(This,pResult,pObjectType,ppObject)
#define IMFSourceResolver_CancelObjectCreation(This,pIUnknownCancelCookie) (This)->lpVtbl->CancelObjectCreation(This,pIUnknownCancelCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSourceResolver_QueryInterface(IMFSourceResolver* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSourceResolver_AddRef(IMFSourceResolver* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSourceResolver_Release(IMFSourceResolver* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSourceResolver methods ***/
static inline HRESULT IMFSourceResolver_CreateObjectFromURL(IMFSourceResolver* This,LPCWSTR pwszURL,DWORD dwFlags,IPropertyStore *pProps,MF_OBJECT_TYPE *pObjectType,IUnknown **ppObject) {
    return This->lpVtbl->CreateObjectFromURL(This,pwszURL,dwFlags,pProps,pObjectType,ppObject);
}
static inline HRESULT IMFSourceResolver_CreateObjectFromByteStream(IMFSourceResolver* This,IMFByteStream *pByteStream,LPCWSTR pwszURL,DWORD dwFlags,IPropertyStore *pProps,MF_OBJECT_TYPE *pObjectType,IUnknown **ppObject) {
    return This->lpVtbl->CreateObjectFromByteStream(This,pByteStream,pwszURL,dwFlags,pProps,pObjectType,ppObject);
}
static inline HRESULT IMFSourceResolver_BeginCreateObjectFromURL(IMFSourceResolver* This,LPCWSTR pwszURL,DWORD dwFlags,IPropertyStore *pProps,IUnknown **ppIUnknownCancelCookie,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginCreateObjectFromURL(This,pwszURL,dwFlags,pProps,ppIUnknownCancelCookie,pCallback,punkState);
}
static inline HRESULT IMFSourceResolver_EndCreateObjectFromURL(IMFSourceResolver* This,IMFAsyncResult *pResult,MF_OBJECT_TYPE *pObjectType,IUnknown **ppObject) {
    return This->lpVtbl->EndCreateObjectFromURL(This,pResult,pObjectType,ppObject);
}
static inline HRESULT IMFSourceResolver_BeginCreateObjectFromByteStream(IMFSourceResolver* This,IMFByteStream *pByteStream,LPCWSTR pwszURL,DWORD dwFlags,IPropertyStore *pProps,IUnknown **ppIUnknownCancelCookie,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginCreateObjectFromByteStream(This,pByteStream,pwszURL,dwFlags,pProps,ppIUnknownCancelCookie,pCallback,punkState);
}
static inline HRESULT IMFSourceResolver_EndCreateObjectFromByteStream(IMFSourceResolver* This,IMFAsyncResult *pResult,MF_OBJECT_TYPE *pObjectType,IUnknown **ppObject) {
    return This->lpVtbl->EndCreateObjectFromByteStream(This,pResult,pObjectType,ppObject);
}
static inline HRESULT IMFSourceResolver_CancelObjectCreation(IMFSourceResolver* This,IUnknown *pIUnknownCancelCookie) {
    return This->lpVtbl->CancelObjectCreation(This,pIUnknownCancelCookie);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFSourceResolver_RemoteBeginCreateObjectFromURL_Proxy(
    IMFSourceResolver* This,
    LPCWSTR pwszURL,
    DWORD dwFlags,
    IPropertyStore *pProps,
    IMFRemoteAsyncCallback *pCallback);
void __RPC_STUB IMFSourceResolver_RemoteBeginCreateObjectFromURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFSourceResolver_RemoteEndCreateObjectFromURL_Proxy(
    IMFSourceResolver* This,
    IUnknown *pResult,
    MF_OBJECT_TYPE *pObjectType,
    IUnknown **ppObject);
void __RPC_STUB IMFSourceResolver_RemoteEndCreateObjectFromURL_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFSourceResolver_RemoteBeginCreateObjectFromByteStream_Proxy(
    IMFSourceResolver* This,
    IMFByteStream *pByteStream,
    LPCWSTR pwszURL,
    DWORD dwFlags,
    IPropertyStore *pProps,
    IMFRemoteAsyncCallback *pCallback);
void __RPC_STUB IMFSourceResolver_RemoteBeginCreateObjectFromByteStream_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFSourceResolver_RemoteEndCreateObjectFromByteStream_Proxy(
    IMFSourceResolver* This,
    IUnknown *pResult,
    MF_OBJECT_TYPE *pObjectType,
    IUnknown **ppObject);
void __RPC_STUB IMFSourceResolver_RemoteEndCreateObjectFromByteStream_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFSourceResolver_BeginCreateObjectFromURL_Proxy(
    IMFSourceResolver* This,
    LPCWSTR pwszURL,
    DWORD dwFlags,
    IPropertyStore *pProps,
    IUnknown **ppIUnknownCancelCookie,
    IMFAsyncCallback *pCallback,
    IUnknown *punkState);
HRESULT __RPC_STUB IMFSourceResolver_BeginCreateObjectFromURL_Stub(
    IMFSourceResolver* This,
    LPCWSTR pwszURL,
    DWORD dwFlags,
    IPropertyStore *pProps,
    IMFRemoteAsyncCallback *pCallback);
HRESULT CALLBACK IMFSourceResolver_EndCreateObjectFromURL_Proxy(
    IMFSourceResolver* This,
    IMFAsyncResult *pResult,
    MF_OBJECT_TYPE *pObjectType,
    IUnknown **ppObject);
HRESULT __RPC_STUB IMFSourceResolver_EndCreateObjectFromURL_Stub(
    IMFSourceResolver* This,
    IUnknown *pResult,
    MF_OBJECT_TYPE *pObjectType,
    IUnknown **ppObject);
HRESULT CALLBACK IMFSourceResolver_BeginCreateObjectFromByteStream_Proxy(
    IMFSourceResolver* This,
    IMFByteStream *pByteStream,
    LPCWSTR pwszURL,
    DWORD dwFlags,
    IPropertyStore *pProps,
    IUnknown **ppIUnknownCancelCookie,
    IMFAsyncCallback *pCallback,
    IUnknown *punkState);
HRESULT __RPC_STUB IMFSourceResolver_BeginCreateObjectFromByteStream_Stub(
    IMFSourceResolver* This,
    IMFByteStream *pByteStream,
    LPCWSTR pwszURL,
    DWORD dwFlags,
    IPropertyStore *pProps,
    IMFRemoteAsyncCallback *pCallback);
HRESULT CALLBACK IMFSourceResolver_EndCreateObjectFromByteStream_Proxy(
    IMFSourceResolver* This,
    IMFAsyncResult *pResult,
    MF_OBJECT_TYPE *pObjectType,
    IUnknown **ppObject);
HRESULT __RPC_STUB IMFSourceResolver_EndCreateObjectFromByteStream_Stub(
    IMFSourceResolver* This,
    IUnknown *pResult,
    MF_OBJECT_TYPE *pObjectType,
    IUnknown **ppObject);

#endif  /* __IMFSourceResolver_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFStreamSink interface
 */
#ifndef __IMFStreamSink_INTERFACE_DEFINED__
#define __IMFStreamSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFStreamSink, 0x0a97b3cf, 0x8e7c, 0x4a3d, 0x8f,0x8c, 0x0c,0x84,0x3d,0xc2,0x47,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0a97b3cf-8e7c-4a3d-8f8c-0c843dc247fb")
IMFStreamSink : public IMFMediaEventGenerator
{
    virtual HRESULT STDMETHODCALLTYPE GetMediaSink(
        IMFMediaSink **ppMediaSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIdentifier(
        DWORD *pdwIdentifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTypeHandler(
        IMFMediaTypeHandler **ppHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessSample(
        IMFSample *pSample) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlaceMarker(
        MFSTREAMSINK_MARKER_TYPE eMarkerType,
        const PROPVARIANT *pvarMarkerValue,
        const PROPVARIANT *pvarContextValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFStreamSink, 0x0a97b3cf, 0x8e7c, 0x4a3d, 0x8f,0x8c, 0x0c,0x84,0x3d,0xc2,0x47,0xfb)
#endif
#else
typedef struct IMFStreamSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFStreamSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFStreamSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFStreamSink *This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFStreamSink *This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFStreamSink *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFStreamSink *This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFStreamSink *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    /*** IMFStreamSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMediaSink)(
        IMFStreamSink *This,
        IMFMediaSink **ppMediaSink);

    HRESULT (STDMETHODCALLTYPE *GetIdentifier)(
        IMFStreamSink *This,
        DWORD *pdwIdentifier);

    HRESULT (STDMETHODCALLTYPE *GetMediaTypeHandler)(
        IMFStreamSink *This,
        IMFMediaTypeHandler **ppHandler);

    HRESULT (STDMETHODCALLTYPE *ProcessSample)(
        IMFStreamSink *This,
        IMFSample *pSample);

    HRESULT (STDMETHODCALLTYPE *PlaceMarker)(
        IMFStreamSink *This,
        MFSTREAMSINK_MARKER_TYPE eMarkerType,
        const PROPVARIANT *pvarMarkerValue,
        const PROPVARIANT *pvarContextValue);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IMFStreamSink *This);

    END_INTERFACE
} IMFStreamSinkVtbl;

interface IMFStreamSink {
    CONST_VTBL IMFStreamSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFStreamSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFStreamSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFStreamSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFStreamSink_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFStreamSink_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFStreamSink_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFStreamSink_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
/*** IMFStreamSink methods ***/
#define IMFStreamSink_GetMediaSink(This,ppMediaSink) (This)->lpVtbl->GetMediaSink(This,ppMediaSink)
#define IMFStreamSink_GetIdentifier(This,pdwIdentifier) (This)->lpVtbl->GetIdentifier(This,pdwIdentifier)
#define IMFStreamSink_GetMediaTypeHandler(This,ppHandler) (This)->lpVtbl->GetMediaTypeHandler(This,ppHandler)
#define IMFStreamSink_ProcessSample(This,pSample) (This)->lpVtbl->ProcessSample(This,pSample)
#define IMFStreamSink_PlaceMarker(This,eMarkerType,pvarMarkerValue,pvarContextValue) (This)->lpVtbl->PlaceMarker(This,eMarkerType,pvarMarkerValue,pvarContextValue)
#define IMFStreamSink_Flush(This) (This)->lpVtbl->Flush(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFStreamSink_QueryInterface(IMFStreamSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFStreamSink_AddRef(IMFStreamSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFStreamSink_Release(IMFStreamSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static inline HRESULT IMFStreamSink_GetEvent(IMFStreamSink* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static inline HRESULT IMFStreamSink_BeginGetEvent(IMFStreamSink* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static inline HRESULT IMFStreamSink_EndGetEvent(IMFStreamSink* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static inline HRESULT IMFStreamSink_QueueEvent(IMFStreamSink* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
/*** IMFStreamSink methods ***/
static inline HRESULT IMFStreamSink_GetMediaSink(IMFStreamSink* This,IMFMediaSink **ppMediaSink) {
    return This->lpVtbl->GetMediaSink(This,ppMediaSink);
}
static inline HRESULT IMFStreamSink_GetIdentifier(IMFStreamSink* This,DWORD *pdwIdentifier) {
    return This->lpVtbl->GetIdentifier(This,pdwIdentifier);
}
static inline HRESULT IMFStreamSink_GetMediaTypeHandler(IMFStreamSink* This,IMFMediaTypeHandler **ppHandler) {
    return This->lpVtbl->GetMediaTypeHandler(This,ppHandler);
}
static inline HRESULT IMFStreamSink_ProcessSample(IMFStreamSink* This,IMFSample *pSample) {
    return This->lpVtbl->ProcessSample(This,pSample);
}
static inline HRESULT IMFStreamSink_PlaceMarker(IMFStreamSink* This,MFSTREAMSINK_MARKER_TYPE eMarkerType,const PROPVARIANT *pvarMarkerValue,const PROPVARIANT *pvarContextValue) {
    return This->lpVtbl->PlaceMarker(This,eMarkerType,pvarMarkerValue,pvarContextValue);
}
static inline HRESULT IMFStreamSink_Flush(IMFStreamSink* This) {
    return This->lpVtbl->Flush(This);
}
#endif
#endif

#endif


#endif  /* __IMFStreamSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFTimer interface
 */
#ifndef __IMFTimer_INTERFACE_DEFINED__
#define __IMFTimer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTimer, 0xe56e4cbd, 0x8f70, 0x49d8, 0xa0,0xf8, 0xed,0xb3,0xd6,0xab,0x9b,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e56e4cbd-8f70-49d8-a0f8-edb3d6ab9bf2")
IMFTimer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetTimer(
        DWORD dwFlags,
        LONGLONG llClockTime,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState,
        IUnknown **ppunkKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelTimer(
        IUnknown *punkKey) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTimer, 0xe56e4cbd, 0x8f70, 0x49d8, 0xa0,0xf8, 0xed,0xb3,0xd6,0xab,0x9b,0xf2)
#endif
#else
typedef struct IMFTimerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTimer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTimer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTimer *This);

    /*** IMFTimer methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTimer)(
        IMFTimer *This,
        DWORD dwFlags,
        LONGLONG llClockTime,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState,
        IUnknown **ppunkKey);

    HRESULT (STDMETHODCALLTYPE *CancelTimer)(
        IMFTimer *This,
        IUnknown *punkKey);

    END_INTERFACE
} IMFTimerVtbl;

interface IMFTimer {
    CONST_VTBL IMFTimerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTimer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTimer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTimer_Release(This) (This)->lpVtbl->Release(This)
/*** IMFTimer methods ***/
#define IMFTimer_SetTimer(This,dwFlags,llClockTime,pCallback,punkState,ppunkKey) (This)->lpVtbl->SetTimer(This,dwFlags,llClockTime,pCallback,punkState,ppunkKey)
#define IMFTimer_CancelTimer(This,punkKey) (This)->lpVtbl->CancelTimer(This,punkKey)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFTimer_QueryInterface(IMFTimer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFTimer_AddRef(IMFTimer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFTimer_Release(IMFTimer* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFTimer methods ***/
static inline HRESULT IMFTimer_SetTimer(IMFTimer* This,DWORD dwFlags,LONGLONG llClockTime,IMFAsyncCallback *pCallback,IUnknown *punkState,IUnknown **ppunkKey) {
    return This->lpVtbl->SetTimer(This,dwFlags,llClockTime,pCallback,punkState,ppunkKey);
}
static inline HRESULT IMFTimer_CancelTimer(IMFTimer* This,IUnknown *punkKey) {
    return This->lpVtbl->CancelTimer(This,punkKey);
}
#endif
#endif

#endif


#endif  /* __IMFTimer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFTopoLoader interface
 */
#ifndef __IMFTopoLoader_INTERFACE_DEFINED__
#define __IMFTopoLoader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTopoLoader, 0xde9a6157, 0xf660, 0x4643, 0xb5,0x6a, 0xdf,0x9f,0x79,0x98,0xc7,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("de9a6157-f660-4643-b56a-df9f7998c7cd")
IMFTopoLoader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Load(
        IMFTopology *pInputTopo,
        IMFTopology **ppOutputTopo,
        IMFTopology *pCurrentTopo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTopoLoader, 0xde9a6157, 0xf660, 0x4643, 0xb5,0x6a, 0xdf,0x9f,0x79,0x98,0xc7,0xcd)
#endif
#else
typedef struct IMFTopoLoaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTopoLoader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTopoLoader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTopoLoader *This);

    /*** IMFTopoLoader methods ***/
    HRESULT (STDMETHODCALLTYPE *Load)(
        IMFTopoLoader *This,
        IMFTopology *pInputTopo,
        IMFTopology **ppOutputTopo,
        IMFTopology *pCurrentTopo);

    END_INTERFACE
} IMFTopoLoaderVtbl;

interface IMFTopoLoader {
    CONST_VTBL IMFTopoLoaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTopoLoader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTopoLoader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTopoLoader_Release(This) (This)->lpVtbl->Release(This)
/*** IMFTopoLoader methods ***/
#define IMFTopoLoader_Load(This,pInputTopo,ppOutputTopo,pCurrentTopo) (This)->lpVtbl->Load(This,pInputTopo,ppOutputTopo,pCurrentTopo)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFTopoLoader_QueryInterface(IMFTopoLoader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFTopoLoader_AddRef(IMFTopoLoader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFTopoLoader_Release(IMFTopoLoader* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFTopoLoader methods ***/
static inline HRESULT IMFTopoLoader_Load(IMFTopoLoader* This,IMFTopology *pInputTopo,IMFTopology **ppOutputTopo,IMFTopology *pCurrentTopo) {
    return This->lpVtbl->Load(This,pInputTopo,ppOutputTopo,pCurrentTopo);
}
#endif
#endif

#endif


#endif  /* __IMFTopoLoader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFVideoSampleAllocator interface
 */
#ifndef __IMFVideoSampleAllocator_INTERFACE_DEFINED__
#define __IMFVideoSampleAllocator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoSampleAllocator, 0x86cbc910, 0xe533, 0x4751, 0x8e,0x3b, 0xf1,0x9b,0x5b,0x80,0x6a,0x03);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("86cbc910-e533-4751-8e3b-f19b5b806a03")
IMFVideoSampleAllocator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetDirectXManager(
        IUnknown *pManager) = 0;

    virtual HRESULT STDMETHODCALLTYPE UninitializeSampleAllocator(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeSampleAllocator(
        DWORD cRequestedFrames,
        IMFMediaType *pMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE AllocateSample(
        IMFSample **ppSample) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoSampleAllocator, 0x86cbc910, 0xe533, 0x4751, 0x8e,0x3b, 0xf1,0x9b,0x5b,0x80,0x6a,0x03)
#endif
#else
typedef struct IMFVideoSampleAllocatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoSampleAllocator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoSampleAllocator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoSampleAllocator *This);

    /*** IMFVideoSampleAllocator methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDirectXManager)(
        IMFVideoSampleAllocator *This,
        IUnknown *pManager);

    HRESULT (STDMETHODCALLTYPE *UninitializeSampleAllocator)(
        IMFVideoSampleAllocator *This);

    HRESULT (STDMETHODCALLTYPE *InitializeSampleAllocator)(
        IMFVideoSampleAllocator *This,
        DWORD cRequestedFrames,
        IMFMediaType *pMediaType);

    HRESULT (STDMETHODCALLTYPE *AllocateSample)(
        IMFVideoSampleAllocator *This,
        IMFSample **ppSample);

    END_INTERFACE
} IMFVideoSampleAllocatorVtbl;

interface IMFVideoSampleAllocator {
    CONST_VTBL IMFVideoSampleAllocatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoSampleAllocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoSampleAllocator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoSampleAllocator_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoSampleAllocator methods ***/
#define IMFVideoSampleAllocator_SetDirectXManager(This,pManager) (This)->lpVtbl->SetDirectXManager(This,pManager)
#define IMFVideoSampleAllocator_UninitializeSampleAllocator(This) (This)->lpVtbl->UninitializeSampleAllocator(This)
#define IMFVideoSampleAllocator_InitializeSampleAllocator(This,cRequestedFrames,pMediaType) (This)->lpVtbl->InitializeSampleAllocator(This,cRequestedFrames,pMediaType)
#define IMFVideoSampleAllocator_AllocateSample(This,ppSample) (This)->lpVtbl->AllocateSample(This,ppSample)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoSampleAllocator_QueryInterface(IMFVideoSampleAllocator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoSampleAllocator_AddRef(IMFVideoSampleAllocator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoSampleAllocator_Release(IMFVideoSampleAllocator* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoSampleAllocator methods ***/
static inline HRESULT IMFVideoSampleAllocator_SetDirectXManager(IMFVideoSampleAllocator* This,IUnknown *pManager) {
    return This->lpVtbl->SetDirectXManager(This,pManager);
}
static inline HRESULT IMFVideoSampleAllocator_UninitializeSampleAllocator(IMFVideoSampleAllocator* This) {
    return This->lpVtbl->UninitializeSampleAllocator(This);
}
static inline HRESULT IMFVideoSampleAllocator_InitializeSampleAllocator(IMFVideoSampleAllocator* This,DWORD cRequestedFrames,IMFMediaType *pMediaType) {
    return This->lpVtbl->InitializeSampleAllocator(This,cRequestedFrames,pMediaType);
}
static inline HRESULT IMFVideoSampleAllocator_AllocateSample(IMFVideoSampleAllocator* This,IMFSample **ppSample) {
    return This->lpVtbl->AllocateSample(This,ppSample);
}
#endif
#endif

#endif


#endif  /* __IMFVideoSampleAllocator_INTERFACE_DEFINED__ */

#if WINVER >= _WIN32_WINNT_WIN7
/*****************************************************************************
 * IMFVideoSampleAllocatorNotify interface
 */
#ifndef __IMFVideoSampleAllocatorNotify_INTERFACE_DEFINED__
#define __IMFVideoSampleAllocatorNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoSampleAllocatorNotify, 0xa792cdbe, 0xc374, 0x4e89, 0x83,0x35, 0x27,0x8e,0x7b,0x99,0x56,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a792cdbe-c374-4e89-8335-278e7b9956a4")
IMFVideoSampleAllocatorNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NotifyRelease(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoSampleAllocatorNotify, 0xa792cdbe, 0xc374, 0x4e89, 0x83,0x35, 0x27,0x8e,0x7b,0x99,0x56,0xa4)
#endif
#else
typedef struct IMFVideoSampleAllocatorNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoSampleAllocatorNotify *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoSampleAllocatorNotify *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoSampleAllocatorNotify *This);

    /*** IMFVideoSampleAllocatorNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *NotifyRelease)(
        IMFVideoSampleAllocatorNotify *This);

    END_INTERFACE
} IMFVideoSampleAllocatorNotifyVtbl;

interface IMFVideoSampleAllocatorNotify {
    CONST_VTBL IMFVideoSampleAllocatorNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoSampleAllocatorNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoSampleAllocatorNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoSampleAllocatorNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoSampleAllocatorNotify methods ***/
#define IMFVideoSampleAllocatorNotify_NotifyRelease(This) (This)->lpVtbl->NotifyRelease(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoSampleAllocatorNotify_QueryInterface(IMFVideoSampleAllocatorNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoSampleAllocatorNotify_AddRef(IMFVideoSampleAllocatorNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoSampleAllocatorNotify_Release(IMFVideoSampleAllocatorNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoSampleAllocatorNotify methods ***/
static inline HRESULT IMFVideoSampleAllocatorNotify_NotifyRelease(IMFVideoSampleAllocatorNotify* This) {
    return This->lpVtbl->NotifyRelease(This);
}
#endif
#endif

#endif


#endif  /* __IMFVideoSampleAllocatorNotify_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFVideoSampleAllocatorNotifyEx interface
 */
#ifndef __IMFVideoSampleAllocatorNotifyEx_INTERFACE_DEFINED__
#define __IMFVideoSampleAllocatorNotifyEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoSampleAllocatorNotifyEx, 0x3978aa1a, 0x6d5b, 0x4b7f, 0xa3,0x40, 0x90,0x89,0x91,0x89,0xae,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3978aa1a-6d5b-4b7f-a340-90899189ae34")
IMFVideoSampleAllocatorNotifyEx : public IMFVideoSampleAllocatorNotify
{
    virtual HRESULT STDMETHODCALLTYPE NotifyPrune(
        IMFSample *ppSample) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoSampleAllocatorNotifyEx, 0x3978aa1a, 0x6d5b, 0x4b7f, 0xa3,0x40, 0x90,0x89,0x91,0x89,0xae,0x34)
#endif
#else
typedef struct IMFVideoSampleAllocatorNotifyExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoSampleAllocatorNotifyEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoSampleAllocatorNotifyEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoSampleAllocatorNotifyEx *This);

    /*** IMFVideoSampleAllocatorNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *NotifyRelease)(
        IMFVideoSampleAllocatorNotifyEx *This);

    /*** IMFVideoSampleAllocatorNotifyEx methods ***/
    HRESULT (STDMETHODCALLTYPE *NotifyPrune)(
        IMFVideoSampleAllocatorNotifyEx *This,
        IMFSample *ppSample);

    END_INTERFACE
} IMFVideoSampleAllocatorNotifyExVtbl;

interface IMFVideoSampleAllocatorNotifyEx {
    CONST_VTBL IMFVideoSampleAllocatorNotifyExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoSampleAllocatorNotifyEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoSampleAllocatorNotifyEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoSampleAllocatorNotifyEx_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoSampleAllocatorNotify methods ***/
#define IMFVideoSampleAllocatorNotifyEx_NotifyRelease(This) (This)->lpVtbl->NotifyRelease(This)
/*** IMFVideoSampleAllocatorNotifyEx methods ***/
#define IMFVideoSampleAllocatorNotifyEx_NotifyPrune(This,ppSample) (This)->lpVtbl->NotifyPrune(This,ppSample)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoSampleAllocatorNotifyEx_QueryInterface(IMFVideoSampleAllocatorNotifyEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoSampleAllocatorNotifyEx_AddRef(IMFVideoSampleAllocatorNotifyEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoSampleAllocatorNotifyEx_Release(IMFVideoSampleAllocatorNotifyEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoSampleAllocatorNotify methods ***/
static inline HRESULT IMFVideoSampleAllocatorNotifyEx_NotifyRelease(IMFVideoSampleAllocatorNotifyEx* This) {
    return This->lpVtbl->NotifyRelease(This);
}
/*** IMFVideoSampleAllocatorNotifyEx methods ***/
static inline HRESULT IMFVideoSampleAllocatorNotifyEx_NotifyPrune(IMFVideoSampleAllocatorNotifyEx* This,IMFSample *ppSample) {
    return This->lpVtbl->NotifyPrune(This,ppSample);
}
#endif
#endif

#endif


#endif  /* __IMFVideoSampleAllocatorNotifyEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFVideoSampleAllocatorCallback interface
 */
#ifndef __IMFVideoSampleAllocatorCallback_INTERFACE_DEFINED__
#define __IMFVideoSampleAllocatorCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoSampleAllocatorCallback, 0x992388b4, 0x3372, 0x4f67, 0x8b,0x6f, 0xc8,0x4c,0x07,0x1f,0x47,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("992388b4-3372-4f67-8b6f-c84c071f4751")
IMFVideoSampleAllocatorCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetCallback(
        IMFVideoSampleAllocatorNotify *pNotify) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFreeSampleCount(
        LONG *plSamples) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoSampleAllocatorCallback, 0x992388b4, 0x3372, 0x4f67, 0x8b,0x6f, 0xc8,0x4c,0x07,0x1f,0x47,0x51)
#endif
#else
typedef struct IMFVideoSampleAllocatorCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoSampleAllocatorCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoSampleAllocatorCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoSampleAllocatorCallback *This);

    /*** IMFVideoSampleAllocatorCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *SetCallback)(
        IMFVideoSampleAllocatorCallback *This,
        IMFVideoSampleAllocatorNotify *pNotify);

    HRESULT (STDMETHODCALLTYPE *GetFreeSampleCount)(
        IMFVideoSampleAllocatorCallback *This,
        LONG *plSamples);

    END_INTERFACE
} IMFVideoSampleAllocatorCallbackVtbl;

interface IMFVideoSampleAllocatorCallback {
    CONST_VTBL IMFVideoSampleAllocatorCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoSampleAllocatorCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoSampleAllocatorCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoSampleAllocatorCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoSampleAllocatorCallback methods ***/
#define IMFVideoSampleAllocatorCallback_SetCallback(This,pNotify) (This)->lpVtbl->SetCallback(This,pNotify)
#define IMFVideoSampleAllocatorCallback_GetFreeSampleCount(This,plSamples) (This)->lpVtbl->GetFreeSampleCount(This,plSamples)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoSampleAllocatorCallback_QueryInterface(IMFVideoSampleAllocatorCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoSampleAllocatorCallback_AddRef(IMFVideoSampleAllocatorCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoSampleAllocatorCallback_Release(IMFVideoSampleAllocatorCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoSampleAllocatorCallback methods ***/
static inline HRESULT IMFVideoSampleAllocatorCallback_SetCallback(IMFVideoSampleAllocatorCallback* This,IMFVideoSampleAllocatorNotify *pNotify) {
    return This->lpVtbl->SetCallback(This,pNotify);
}
static inline HRESULT IMFVideoSampleAllocatorCallback_GetFreeSampleCount(IMFVideoSampleAllocatorCallback* This,LONG *plSamples) {
    return This->lpVtbl->GetFreeSampleCount(This,plSamples);
}
#endif
#endif

#endif


#endif  /* __IMFVideoSampleAllocatorCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFVideoSampleAllocatorEx interface
 */
#ifndef __IMFVideoSampleAllocatorEx_INTERFACE_DEFINED__
#define __IMFVideoSampleAllocatorEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoSampleAllocatorEx, 0x545b3a48, 0x3283, 0x4f62, 0x86,0x6f, 0xa6,0x2d,0x8f,0x59,0x8f,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("545b3a48-3283-4f62-866f-a62d8f598f9f")
IMFVideoSampleAllocatorEx : public IMFVideoSampleAllocator
{
    virtual HRESULT STDMETHODCALLTYPE InitializeSampleAllocatorEx(
        DWORD cInitialSamples,
        DWORD cMaximumSamples,
        IMFAttributes *pAttributes,
        IMFMediaType *pMediaType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoSampleAllocatorEx, 0x545b3a48, 0x3283, 0x4f62, 0x86,0x6f, 0xa6,0x2d,0x8f,0x59,0x8f,0x9f)
#endif
#else
typedef struct IMFVideoSampleAllocatorExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoSampleAllocatorEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoSampleAllocatorEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoSampleAllocatorEx *This);

    /*** IMFVideoSampleAllocator methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDirectXManager)(
        IMFVideoSampleAllocatorEx *This,
        IUnknown *pManager);

    HRESULT (STDMETHODCALLTYPE *UninitializeSampleAllocator)(
        IMFVideoSampleAllocatorEx *This);

    HRESULT (STDMETHODCALLTYPE *InitializeSampleAllocator)(
        IMFVideoSampleAllocatorEx *This,
        DWORD cRequestedFrames,
        IMFMediaType *pMediaType);

    HRESULT (STDMETHODCALLTYPE *AllocateSample)(
        IMFVideoSampleAllocatorEx *This,
        IMFSample **ppSample);

    /*** IMFVideoSampleAllocatorEx methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializeSampleAllocatorEx)(
        IMFVideoSampleAllocatorEx *This,
        DWORD cInitialSamples,
        DWORD cMaximumSamples,
        IMFAttributes *pAttributes,
        IMFMediaType *pMediaType);

    END_INTERFACE
} IMFVideoSampleAllocatorExVtbl;

interface IMFVideoSampleAllocatorEx {
    CONST_VTBL IMFVideoSampleAllocatorExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoSampleAllocatorEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoSampleAllocatorEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoSampleAllocatorEx_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoSampleAllocator methods ***/
#define IMFVideoSampleAllocatorEx_SetDirectXManager(This,pManager) (This)->lpVtbl->SetDirectXManager(This,pManager)
#define IMFVideoSampleAllocatorEx_UninitializeSampleAllocator(This) (This)->lpVtbl->UninitializeSampleAllocator(This)
#define IMFVideoSampleAllocatorEx_InitializeSampleAllocator(This,cRequestedFrames,pMediaType) (This)->lpVtbl->InitializeSampleAllocator(This,cRequestedFrames,pMediaType)
#define IMFVideoSampleAllocatorEx_AllocateSample(This,ppSample) (This)->lpVtbl->AllocateSample(This,ppSample)
/*** IMFVideoSampleAllocatorEx methods ***/
#define IMFVideoSampleAllocatorEx_InitializeSampleAllocatorEx(This,cInitialSamples,cMaximumSamples,pAttributes,pMediaType) (This)->lpVtbl->InitializeSampleAllocatorEx(This,cInitialSamples,cMaximumSamples,pAttributes,pMediaType)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoSampleAllocatorEx_QueryInterface(IMFVideoSampleAllocatorEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoSampleAllocatorEx_AddRef(IMFVideoSampleAllocatorEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoSampleAllocatorEx_Release(IMFVideoSampleAllocatorEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoSampleAllocator methods ***/
static inline HRESULT IMFVideoSampleAllocatorEx_SetDirectXManager(IMFVideoSampleAllocatorEx* This,IUnknown *pManager) {
    return This->lpVtbl->SetDirectXManager(This,pManager);
}
static inline HRESULT IMFVideoSampleAllocatorEx_UninitializeSampleAllocator(IMFVideoSampleAllocatorEx* This) {
    return This->lpVtbl->UninitializeSampleAllocator(This);
}
static inline HRESULT IMFVideoSampleAllocatorEx_InitializeSampleAllocator(IMFVideoSampleAllocatorEx* This,DWORD cRequestedFrames,IMFMediaType *pMediaType) {
    return This->lpVtbl->InitializeSampleAllocator(This,cRequestedFrames,pMediaType);
}
static inline HRESULT IMFVideoSampleAllocatorEx_AllocateSample(IMFVideoSampleAllocatorEx* This,IMFSample **ppSample) {
    return This->lpVtbl->AllocateSample(This,ppSample);
}
/*** IMFVideoSampleAllocatorEx methods ***/
static inline HRESULT IMFVideoSampleAllocatorEx_InitializeSampleAllocatorEx(IMFVideoSampleAllocatorEx* This,DWORD cInitialSamples,DWORD cMaximumSamples,IMFAttributes *pAttributes,IMFMediaType *pMediaType) {
    return This->lpVtbl->InitializeSampleAllocatorEx(This,cInitialSamples,cMaximumSamples,pAttributes,pMediaType);
}
#endif
#endif

#endif


#endif  /* __IMFVideoSampleAllocatorEx_INTERFACE_DEFINED__ */

#endif /* WINVER >= _WIN32_WINNT_WIN7 */
#if WINVER >= _WIN32_WINNT_WIN8
typedef enum _MF_VIDEO_PROCESSOR_ROTATION {
    ROTATION_NONE = 0,
    ROTATION_NORMAL = 1
} MF_VIDEO_PROCESSOR_ROTATION;
typedef enum _MF_VIDEO_PROCESSOR_MIRROR {
    MIRROR_NONE = 0,
    MIRROR_HORIZONTAL = 1,
    MIRROR_VERTICAL = 2
} MF_VIDEO_PROCESSOR_MIRROR;
/*****************************************************************************
 * IMFVideoProcessorControl interface
 */
#ifndef __IMFVideoProcessorControl_INTERFACE_DEFINED__
#define __IMFVideoProcessorControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoProcessorControl, 0xa3f675d5, 0x6119, 0x4f7f, 0xa1,0x00, 0x1d,0x8b,0x28,0x0f,0x0e,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a3f675d5-6119-4f7f-a100-1d8b280f0efb")
IMFVideoProcessorControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetBorderColor(
        MFARGB *pBorderColor) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSourceRectangle(
        RECT *pSrcRect) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDestinationRectangle(
        RECT *pDstRect) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMirror(
        MF_VIDEO_PROCESSOR_MIRROR eMirror) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRotation(
        MF_VIDEO_PROCESSOR_ROTATION eRotation) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConstrictionSize(
        SIZE *pConstrictionSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoProcessorControl, 0xa3f675d5, 0x6119, 0x4f7f, 0xa1,0x00, 0x1d,0x8b,0x28,0x0f,0x0e,0xfb)
#endif
#else
typedef struct IMFVideoProcessorControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoProcessorControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoProcessorControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoProcessorControl *This);

    /*** IMFVideoProcessorControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SetBorderColor)(
        IMFVideoProcessorControl *This,
        MFARGB *pBorderColor);

    HRESULT (STDMETHODCALLTYPE *SetSourceRectangle)(
        IMFVideoProcessorControl *This,
        RECT *pSrcRect);

    HRESULT (STDMETHODCALLTYPE *SetDestinationRectangle)(
        IMFVideoProcessorControl *This,
        RECT *pDstRect);

    HRESULT (STDMETHODCALLTYPE *SetMirror)(
        IMFVideoProcessorControl *This,
        MF_VIDEO_PROCESSOR_MIRROR eMirror);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IMFVideoProcessorControl *This,
        MF_VIDEO_PROCESSOR_ROTATION eRotation);

    HRESULT (STDMETHODCALLTYPE *SetConstrictionSize)(
        IMFVideoProcessorControl *This,
        SIZE *pConstrictionSize);

    END_INTERFACE
} IMFVideoProcessorControlVtbl;

interface IMFVideoProcessorControl {
    CONST_VTBL IMFVideoProcessorControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoProcessorControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoProcessorControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoProcessorControl_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoProcessorControl methods ***/
#define IMFVideoProcessorControl_SetBorderColor(This,pBorderColor) (This)->lpVtbl->SetBorderColor(This,pBorderColor)
#define IMFVideoProcessorControl_SetSourceRectangle(This,pSrcRect) (This)->lpVtbl->SetSourceRectangle(This,pSrcRect)
#define IMFVideoProcessorControl_SetDestinationRectangle(This,pDstRect) (This)->lpVtbl->SetDestinationRectangle(This,pDstRect)
#define IMFVideoProcessorControl_SetMirror(This,eMirror) (This)->lpVtbl->SetMirror(This,eMirror)
#define IMFVideoProcessorControl_SetRotation(This,eRotation) (This)->lpVtbl->SetRotation(This,eRotation)
#define IMFVideoProcessorControl_SetConstrictionSize(This,pConstrictionSize) (This)->lpVtbl->SetConstrictionSize(This,pConstrictionSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoProcessorControl_QueryInterface(IMFVideoProcessorControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoProcessorControl_AddRef(IMFVideoProcessorControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoProcessorControl_Release(IMFVideoProcessorControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoProcessorControl methods ***/
static inline HRESULT IMFVideoProcessorControl_SetBorderColor(IMFVideoProcessorControl* This,MFARGB *pBorderColor) {
    return This->lpVtbl->SetBorderColor(This,pBorderColor);
}
static inline HRESULT IMFVideoProcessorControl_SetSourceRectangle(IMFVideoProcessorControl* This,RECT *pSrcRect) {
    return This->lpVtbl->SetSourceRectangle(This,pSrcRect);
}
static inline HRESULT IMFVideoProcessorControl_SetDestinationRectangle(IMFVideoProcessorControl* This,RECT *pDstRect) {
    return This->lpVtbl->SetDestinationRectangle(This,pDstRect);
}
static inline HRESULT IMFVideoProcessorControl_SetMirror(IMFVideoProcessorControl* This,MF_VIDEO_PROCESSOR_MIRROR eMirror) {
    return This->lpVtbl->SetMirror(This,eMirror);
}
static inline HRESULT IMFVideoProcessorControl_SetRotation(IMFVideoProcessorControl* This,MF_VIDEO_PROCESSOR_ROTATION eRotation) {
    return This->lpVtbl->SetRotation(This,eRotation);
}
static inline HRESULT IMFVideoProcessorControl_SetConstrictionSize(IMFVideoProcessorControl* This,SIZE *pConstrictionSize) {
    return This->lpVtbl->SetConstrictionSize(This,pConstrictionSize);
}
#endif
#endif

#endif


#endif  /* __IMFVideoProcessorControl_INTERFACE_DEFINED__ */

#if WINVER >= _WIN32_WINNT_WINBLUE
/*****************************************************************************
 * IMFVideoProcessorControl2 interface
 */
#ifndef __IMFVideoProcessorControl2_INTERFACE_DEFINED__
#define __IMFVideoProcessorControl2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoProcessorControl2, 0xbde633d3, 0xe1dc, 0x4a7f, 0xa6,0x93, 0xbb,0xae,0x39,0x9c,0x4a,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bde633d3-e1dc-4a7f-a693-bbae399c4a20")
IMFVideoProcessorControl2 : public IMFVideoProcessorControl
{
    virtual HRESULT STDMETHODCALLTYPE SetRotationOverride(
        UINT uiRotation) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableHardwareEffects(
        WINBOOL fEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedHardwareEffects(
        UINT *puiSupport) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoProcessorControl2, 0xbde633d3, 0xe1dc, 0x4a7f, 0xa6,0x93, 0xbb,0xae,0x39,0x9c,0x4a,0x20)
#endif
#else
typedef struct IMFVideoProcessorControl2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoProcessorControl2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoProcessorControl2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoProcessorControl2 *This);

    /*** IMFVideoProcessorControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SetBorderColor)(
        IMFVideoProcessorControl2 *This,
        MFARGB *pBorderColor);

    HRESULT (STDMETHODCALLTYPE *SetSourceRectangle)(
        IMFVideoProcessorControl2 *This,
        RECT *pSrcRect);

    HRESULT (STDMETHODCALLTYPE *SetDestinationRectangle)(
        IMFVideoProcessorControl2 *This,
        RECT *pDstRect);

    HRESULT (STDMETHODCALLTYPE *SetMirror)(
        IMFVideoProcessorControl2 *This,
        MF_VIDEO_PROCESSOR_MIRROR eMirror);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IMFVideoProcessorControl2 *This,
        MF_VIDEO_PROCESSOR_ROTATION eRotation);

    HRESULT (STDMETHODCALLTYPE *SetConstrictionSize)(
        IMFVideoProcessorControl2 *This,
        SIZE *pConstrictionSize);

    /*** IMFVideoProcessorControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRotationOverride)(
        IMFVideoProcessorControl2 *This,
        UINT uiRotation);

    HRESULT (STDMETHODCALLTYPE *EnableHardwareEffects)(
        IMFVideoProcessorControl2 *This,
        WINBOOL fEnabled);

    HRESULT (STDMETHODCALLTYPE *GetSupportedHardwareEffects)(
        IMFVideoProcessorControl2 *This,
        UINT *puiSupport);

    END_INTERFACE
} IMFVideoProcessorControl2Vtbl;

interface IMFVideoProcessorControl2 {
    CONST_VTBL IMFVideoProcessorControl2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoProcessorControl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoProcessorControl2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoProcessorControl2_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoProcessorControl methods ***/
#define IMFVideoProcessorControl2_SetBorderColor(This,pBorderColor) (This)->lpVtbl->SetBorderColor(This,pBorderColor)
#define IMFVideoProcessorControl2_SetSourceRectangle(This,pSrcRect) (This)->lpVtbl->SetSourceRectangle(This,pSrcRect)
#define IMFVideoProcessorControl2_SetDestinationRectangle(This,pDstRect) (This)->lpVtbl->SetDestinationRectangle(This,pDstRect)
#define IMFVideoProcessorControl2_SetMirror(This,eMirror) (This)->lpVtbl->SetMirror(This,eMirror)
#define IMFVideoProcessorControl2_SetRotation(This,eRotation) (This)->lpVtbl->SetRotation(This,eRotation)
#define IMFVideoProcessorControl2_SetConstrictionSize(This,pConstrictionSize) (This)->lpVtbl->SetConstrictionSize(This,pConstrictionSize)
/*** IMFVideoProcessorControl2 methods ***/
#define IMFVideoProcessorControl2_SetRotationOverride(This,uiRotation) (This)->lpVtbl->SetRotationOverride(This,uiRotation)
#define IMFVideoProcessorControl2_EnableHardwareEffects(This,fEnabled) (This)->lpVtbl->EnableHardwareEffects(This,fEnabled)
#define IMFVideoProcessorControl2_GetSupportedHardwareEffects(This,puiSupport) (This)->lpVtbl->GetSupportedHardwareEffects(This,puiSupport)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoProcessorControl2_QueryInterface(IMFVideoProcessorControl2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoProcessorControl2_AddRef(IMFVideoProcessorControl2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoProcessorControl2_Release(IMFVideoProcessorControl2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoProcessorControl methods ***/
static inline HRESULT IMFVideoProcessorControl2_SetBorderColor(IMFVideoProcessorControl2* This,MFARGB *pBorderColor) {
    return This->lpVtbl->SetBorderColor(This,pBorderColor);
}
static inline HRESULT IMFVideoProcessorControl2_SetSourceRectangle(IMFVideoProcessorControl2* This,RECT *pSrcRect) {
    return This->lpVtbl->SetSourceRectangle(This,pSrcRect);
}
static inline HRESULT IMFVideoProcessorControl2_SetDestinationRectangle(IMFVideoProcessorControl2* This,RECT *pDstRect) {
    return This->lpVtbl->SetDestinationRectangle(This,pDstRect);
}
static inline HRESULT IMFVideoProcessorControl2_SetMirror(IMFVideoProcessorControl2* This,MF_VIDEO_PROCESSOR_MIRROR eMirror) {
    return This->lpVtbl->SetMirror(This,eMirror);
}
static inline HRESULT IMFVideoProcessorControl2_SetRotation(IMFVideoProcessorControl2* This,MF_VIDEO_PROCESSOR_ROTATION eRotation) {
    return This->lpVtbl->SetRotation(This,eRotation);
}
static inline HRESULT IMFVideoProcessorControl2_SetConstrictionSize(IMFVideoProcessorControl2* This,SIZE *pConstrictionSize) {
    return This->lpVtbl->SetConstrictionSize(This,pConstrictionSize);
}
/*** IMFVideoProcessorControl2 methods ***/
static inline HRESULT IMFVideoProcessorControl2_SetRotationOverride(IMFVideoProcessorControl2* This,UINT uiRotation) {
    return This->lpVtbl->SetRotationOverride(This,uiRotation);
}
static inline HRESULT IMFVideoProcessorControl2_EnableHardwareEffects(IMFVideoProcessorControl2* This,WINBOOL fEnabled) {
    return This->lpVtbl->EnableHardwareEffects(This,fEnabled);
}
static inline HRESULT IMFVideoProcessorControl2_GetSupportedHardwareEffects(IMFVideoProcessorControl2* This,UINT *puiSupport) {
    return This->lpVtbl->GetSupportedHardwareEffects(This,puiSupport);
}
#endif
#endif

#endif


#endif  /* __IMFVideoProcessorControl2_INTERFACE_DEFINED__ */

#if WINVER >= _WIN32_WINNT_WIN10
typedef enum _MFVideoSphericalFormat {
    MFVideoSphericalFormat_Unsupported = 0,
    MFVideoSphericalFormat_Equirectangular = 1,
    MFVideoSphericalFormat_CubeMap = 2,
    MFVideoSphericalFormat_3DMesh = 3
} MFVideoSphericalFormat;
#endif /* WINVER >= _WIN32_WINNT_WIN10 */
#if NTDDI_VERSION >= NTDDI_WIN10_RS3
EXTERN_GUID(MF_XVP_SAMPLE_LOCK_TIMEOUT, 0xaa4ddb29, 0x5134, 0x4363, 0xac, 0x72, 0x83, 0xec, 0x4b, 0xc1, 0x4, 0x26);
typedef enum MFVideoSphericalProjectionMode {
    MFVideoSphericalProjectionMode_Spherical = 0,
    MFVideoSphericalProjectionMode_Flat = 1
} MFVideoSphericalProjectionMode;
/*****************************************************************************
 * IMFVideoProcessorControl3 interface
 */
#ifndef __IMFVideoProcessorControl3_INTERFACE_DEFINED__
#define __IMFVideoProcessorControl3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoProcessorControl3, 0x2424b3f2, 0xeb23, 0x40f1, 0x91,0xaa, 0x74,0xbd,0xde,0xea,0x08,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2424b3f2-eb23-40f1-91aa-74bddeea0883")
IMFVideoProcessorControl3 : public IMFVideoProcessorControl2
{
    virtual HRESULT STDMETHODCALLTYPE GetNaturalOutputType(
        IMFMediaType **ppType) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableSphericalVideoProcessing(
        WINBOOL fEnable,
        MFVideoSphericalFormat eFormat,
        MFVideoSphericalProjectionMode eProjectionMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSphericalVideoProperties(
        float X,
        float Y,
        float Z,
        float W,
        float fieldOfView) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputDevice(
        IUnknown *pOutputDevice) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoProcessorControl3, 0x2424b3f2, 0xeb23, 0x40f1, 0x91,0xaa, 0x74,0xbd,0xde,0xea,0x08,0x83)
#endif
#else
typedef struct IMFVideoProcessorControl3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoProcessorControl3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoProcessorControl3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoProcessorControl3 *This);

    /*** IMFVideoProcessorControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SetBorderColor)(
        IMFVideoProcessorControl3 *This,
        MFARGB *pBorderColor);

    HRESULT (STDMETHODCALLTYPE *SetSourceRectangle)(
        IMFVideoProcessorControl3 *This,
        RECT *pSrcRect);

    HRESULT (STDMETHODCALLTYPE *SetDestinationRectangle)(
        IMFVideoProcessorControl3 *This,
        RECT *pDstRect);

    HRESULT (STDMETHODCALLTYPE *SetMirror)(
        IMFVideoProcessorControl3 *This,
        MF_VIDEO_PROCESSOR_MIRROR eMirror);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IMFVideoProcessorControl3 *This,
        MF_VIDEO_PROCESSOR_ROTATION eRotation);

    HRESULT (STDMETHODCALLTYPE *SetConstrictionSize)(
        IMFVideoProcessorControl3 *This,
        SIZE *pConstrictionSize);

    /*** IMFVideoProcessorControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRotationOverride)(
        IMFVideoProcessorControl3 *This,
        UINT uiRotation);

    HRESULT (STDMETHODCALLTYPE *EnableHardwareEffects)(
        IMFVideoProcessorControl3 *This,
        WINBOOL fEnabled);

    HRESULT (STDMETHODCALLTYPE *GetSupportedHardwareEffects)(
        IMFVideoProcessorControl3 *This,
        UINT *puiSupport);

    /*** IMFVideoProcessorControl3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNaturalOutputType)(
        IMFVideoProcessorControl3 *This,
        IMFMediaType **ppType);

    HRESULT (STDMETHODCALLTYPE *EnableSphericalVideoProcessing)(
        IMFVideoProcessorControl3 *This,
        WINBOOL fEnable,
        MFVideoSphericalFormat eFormat,
        MFVideoSphericalProjectionMode eProjectionMode);

    HRESULT (STDMETHODCALLTYPE *SetSphericalVideoProperties)(
        IMFVideoProcessorControl3 *This,
        float X,
        float Y,
        float Z,
        float W,
        float fieldOfView);

    HRESULT (STDMETHODCALLTYPE *SetOutputDevice)(
        IMFVideoProcessorControl3 *This,
        IUnknown *pOutputDevice);

    END_INTERFACE
} IMFVideoProcessorControl3Vtbl;

interface IMFVideoProcessorControl3 {
    CONST_VTBL IMFVideoProcessorControl3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoProcessorControl3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoProcessorControl3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoProcessorControl3_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoProcessorControl methods ***/
#define IMFVideoProcessorControl3_SetBorderColor(This,pBorderColor) (This)->lpVtbl->SetBorderColor(This,pBorderColor)
#define IMFVideoProcessorControl3_SetSourceRectangle(This,pSrcRect) (This)->lpVtbl->SetSourceRectangle(This,pSrcRect)
#define IMFVideoProcessorControl3_SetDestinationRectangle(This,pDstRect) (This)->lpVtbl->SetDestinationRectangle(This,pDstRect)
#define IMFVideoProcessorControl3_SetMirror(This,eMirror) (This)->lpVtbl->SetMirror(This,eMirror)
#define IMFVideoProcessorControl3_SetRotation(This,eRotation) (This)->lpVtbl->SetRotation(This,eRotation)
#define IMFVideoProcessorControl3_SetConstrictionSize(This,pConstrictionSize) (This)->lpVtbl->SetConstrictionSize(This,pConstrictionSize)
/*** IMFVideoProcessorControl2 methods ***/
#define IMFVideoProcessorControl3_SetRotationOverride(This,uiRotation) (This)->lpVtbl->SetRotationOverride(This,uiRotation)
#define IMFVideoProcessorControl3_EnableHardwareEffects(This,fEnabled) (This)->lpVtbl->EnableHardwareEffects(This,fEnabled)
#define IMFVideoProcessorControl3_GetSupportedHardwareEffects(This,puiSupport) (This)->lpVtbl->GetSupportedHardwareEffects(This,puiSupport)
/*** IMFVideoProcessorControl3 methods ***/
#define IMFVideoProcessorControl3_GetNaturalOutputType(This,ppType) (This)->lpVtbl->GetNaturalOutputType(This,ppType)
#define IMFVideoProcessorControl3_EnableSphericalVideoProcessing(This,fEnable,eFormat,eProjectionMode) (This)->lpVtbl->EnableSphericalVideoProcessing(This,fEnable,eFormat,eProjectionMode)
#define IMFVideoProcessorControl3_SetSphericalVideoProperties(This,X,Y,Z,W,fieldOfView) (This)->lpVtbl->SetSphericalVideoProperties(This,X,Y,Z,W,fieldOfView)
#define IMFVideoProcessorControl3_SetOutputDevice(This,pOutputDevice) (This)->lpVtbl->SetOutputDevice(This,pOutputDevice)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoProcessorControl3_QueryInterface(IMFVideoProcessorControl3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoProcessorControl3_AddRef(IMFVideoProcessorControl3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoProcessorControl3_Release(IMFVideoProcessorControl3* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoProcessorControl methods ***/
static inline HRESULT IMFVideoProcessorControl3_SetBorderColor(IMFVideoProcessorControl3* This,MFARGB *pBorderColor) {
    return This->lpVtbl->SetBorderColor(This,pBorderColor);
}
static inline HRESULT IMFVideoProcessorControl3_SetSourceRectangle(IMFVideoProcessorControl3* This,RECT *pSrcRect) {
    return This->lpVtbl->SetSourceRectangle(This,pSrcRect);
}
static inline HRESULT IMFVideoProcessorControl3_SetDestinationRectangle(IMFVideoProcessorControl3* This,RECT *pDstRect) {
    return This->lpVtbl->SetDestinationRectangle(This,pDstRect);
}
static inline HRESULT IMFVideoProcessorControl3_SetMirror(IMFVideoProcessorControl3* This,MF_VIDEO_PROCESSOR_MIRROR eMirror) {
    return This->lpVtbl->SetMirror(This,eMirror);
}
static inline HRESULT IMFVideoProcessorControl3_SetRotation(IMFVideoProcessorControl3* This,MF_VIDEO_PROCESSOR_ROTATION eRotation) {
    return This->lpVtbl->SetRotation(This,eRotation);
}
static inline HRESULT IMFVideoProcessorControl3_SetConstrictionSize(IMFVideoProcessorControl3* This,SIZE *pConstrictionSize) {
    return This->lpVtbl->SetConstrictionSize(This,pConstrictionSize);
}
/*** IMFVideoProcessorControl2 methods ***/
static inline HRESULT IMFVideoProcessorControl3_SetRotationOverride(IMFVideoProcessorControl3* This,UINT uiRotation) {
    return This->lpVtbl->SetRotationOverride(This,uiRotation);
}
static inline HRESULT IMFVideoProcessorControl3_EnableHardwareEffects(IMFVideoProcessorControl3* This,WINBOOL fEnabled) {
    return This->lpVtbl->EnableHardwareEffects(This,fEnabled);
}
static inline HRESULT IMFVideoProcessorControl3_GetSupportedHardwareEffects(IMFVideoProcessorControl3* This,UINT *puiSupport) {
    return This->lpVtbl->GetSupportedHardwareEffects(This,puiSupport);
}
/*** IMFVideoProcessorControl3 methods ***/
static inline HRESULT IMFVideoProcessorControl3_GetNaturalOutputType(IMFVideoProcessorControl3* This,IMFMediaType **ppType) {
    return This->lpVtbl->GetNaturalOutputType(This,ppType);
}
static inline HRESULT IMFVideoProcessorControl3_EnableSphericalVideoProcessing(IMFVideoProcessorControl3* This,WINBOOL fEnable,MFVideoSphericalFormat eFormat,MFVideoSphericalProjectionMode eProjectionMode) {
    return This->lpVtbl->EnableSphericalVideoProcessing(This,fEnable,eFormat,eProjectionMode);
}
static inline HRESULT IMFVideoProcessorControl3_SetSphericalVideoProperties(IMFVideoProcessorControl3* This,float X,float Y,float Z,float W,float fieldOfView) {
    return This->lpVtbl->SetSphericalVideoProperties(This,X,Y,Z,W,fieldOfView);
}
static inline HRESULT IMFVideoProcessorControl3_SetOutputDevice(IMFVideoProcessorControl3* This,IUnknown *pOutputDevice) {
    return This->lpVtbl->SetOutputDevice(This,pOutputDevice);
}
#endif
#endif

#endif


#endif  /* __IMFVideoProcessorControl3_INTERFACE_DEFINED__ */

#endif /* NTDDI_VERSION >= NTDDI_WIN10_RS3 */
#endif /* WINVER >= _WIN32_WINNT_WINBLUE */
#if NTDDI_VERSION >= NTDDI_WIN10_VB
/*****************************************************************************
 * IMFVideoRendererEffectControl interface
 */
#ifndef __IMFVideoRendererEffectControl_INTERFACE_DEFINED__
#define __IMFVideoRendererEffectControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoRendererEffectControl, 0x604d33d7, 0xcf23, 0x41d5, 0x82,0x24, 0x5b,0xbb,0xb1,0xa8,0x74,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("604d33d7-cf23-41d5-8224-5bbbb1a87475")
IMFVideoRendererEffectControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnAppServiceConnectionEstablished(
        IUnknown *pAppServiceConnection) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoRendererEffectControl, 0x604d33d7, 0xcf23, 0x41d5, 0x82,0x24, 0x5b,0xbb,0xb1,0xa8,0x74,0x75)
#endif
#else
typedef struct IMFVideoRendererEffectControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoRendererEffectControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoRendererEffectControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoRendererEffectControl *This);

    /*** IMFVideoRendererEffectControl methods ***/
    HRESULT (STDMETHODCALLTYPE *OnAppServiceConnectionEstablished)(
        IMFVideoRendererEffectControl *This,
        IUnknown *pAppServiceConnection);

    END_INTERFACE
} IMFVideoRendererEffectControlVtbl;

interface IMFVideoRendererEffectControl {
    CONST_VTBL IMFVideoRendererEffectControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoRendererEffectControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoRendererEffectControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoRendererEffectControl_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoRendererEffectControl methods ***/
#define IMFVideoRendererEffectControl_OnAppServiceConnectionEstablished(This,pAppServiceConnection) (This)->lpVtbl->OnAppServiceConnectionEstablished(This,pAppServiceConnection)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoRendererEffectControl_QueryInterface(IMFVideoRendererEffectControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoRendererEffectControl_AddRef(IMFVideoRendererEffectControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoRendererEffectControl_Release(IMFVideoRendererEffectControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoRendererEffectControl methods ***/
static inline HRESULT IMFVideoRendererEffectControl_OnAppServiceConnectionEstablished(IMFVideoRendererEffectControl* This,IUnknown *pAppServiceConnection) {
    return This->lpVtbl->OnAppServiceConnectionEstablished(This,pAppServiceConnection);
}
#endif
#endif

#endif


#endif  /* __IMFVideoRendererEffectControl_INTERFACE_DEFINED__ */

#endif /* WINVER >= NTDDI_WIN10_VB */
#endif /* WINVER >= _WIN32_WINNT_WIN8 */
HRESULT WINAPI MFCreate3GPMediaSink(IMFByteStream *pIByteStream,IMFMediaType *pVideoMediaType,IMFMediaType *pAudioMediaType,IMFMediaSink **ppIMediaSink);
HRESULT WINAPI MFCreateAggregateSource(IMFCollection *pSourceCollection,IMFMediaSource **ppAggSource);

HRESULT WINAPI MFCreateAlignedMemoryBuffer(DWORD cbMaxLength,DWORD fAlignmentFlags,IMFMediaBuffer **ppBuffer);
HRESULT WINAPI MFCreateASFContentInfo(IMFASFContentInfo **ppIContentInfo);
HRESULT WINAPI MFCreateASFIndexer(IMFASFIndexer **ppIIndexer);
HRESULT WINAPI MFCreateASFIndexerByteStream(IMFByteStream *pIContentByteStream,QWORD cbIndexStartOffset,IMFByteStream **pIIndexByteStream);
HRESULT WINAPI MFCreateASFMediaSink(IMFByteStream *pIByteStream,IMFMediaSink **ppIMediaSink);
HRESULT WINAPI MFCreateASFMediaSinkActivate(LPCWSTR pwszFileName,IMFASFContentInfo *pContentInfo,IMFActivate **ppIActivate);
HRESULT WINAPI MFCreateASFMultiplexer(IMFASFMultiplexer **ppIMultiplexer);
HRESULT WINAPI MFCreateASFProfile(IMFASFProfile **ppIProfile);
HRESULT WINAPI MFCreateASFProfileFromPresentationDescriptor(IMFPresentationDescriptor *pIPD,IMFASFProfile **ppIProfile);
HRESULT WINAPI MFCreateASFSplitter(IMFASFSplitter **ppISplitter);
HRESULT WINAPI MFCreateAudioRenderer(IMFAttributes *pAudioAttributes, IMFMediaSink **ppSink);
HRESULT WINAPI MFCreateAudioRendererActivate(IMFActivate **ppActivate);
HRESULT WINAPI MFCreateDeviceSource(IMFAttributes *pAttributes,IMFMediaSource **ppSource);
HRESULT WINAPI MFCreateMediaSession(IMFAttributes *pConfiguration,IMFMediaSession **ppMS);
HRESULT WINAPI MFCreateMP3MediaSink(IMFByteStream *pTargetByteStream,IMFMediaSink **ppMediaSink);
HRESULT WINAPI MFCreateMPEG4MediaSink(IMFByteStream *pIByteStream,IMFMediaType *pVideoMediaType,IMFMediaType *pAudioMediaType,IMFMediaSink **ppIMediaSink);
HRESULT WINAPI MFCreateNetSchemePlugin(REFIID riid,LPVOID *ppvHandler);
HRESULT WINAPI MFCreatePMPServer(DWORD dwCreationFlags,IMFPMPServer **ppPMPServer);
HRESULT WINAPI MFCreatePMPMediaSession(DWORD dwCreationFlags,IMFAttributes *pConfiguration,IMFMediaSession **ppMediaSession,IMFActivate **ppEnablerActivate);
HRESULT WINAPI MFCreatePathFromURL(LPCWSTR pwszFileURL,LPWSTR *ppwszFilePath);
HRESULT WINAPI MFCreatePresentationClock(IMFPresentationClock **ppPresentationClock);
HRESULT WINAPI MFCreatePresentationDescriptor(DWORD cStreamDescriptors,IMFStreamDescriptor **apStreamDescriptors,IMFPresentationDescriptor **ppPresentationDescriptor);
HRESULT WINAPI MFCreatePresentationDescriptorFromASFProfile(IMFASFProfile *pIProfile,IMFPresentationDescriptor **ppIPD);
HRESULT WINAPI MFCreateProxyLocator(LPCWSTR pszProtocol,IPropertyStore *pProxyConfig,IMFNetProxyLocator **ppProxyLocator);
HRESULT WINAPI MFCreateRemoteDesktopPlugin(IMFRemoteDesktopPlugin **ppPlugin);
HRESULT WINAPI MFCreateSample(IMFSample **ppIMFSample);
HRESULT WINAPI MFCreateSampleCopierMFT(IMFTransform **ppCopierMFT);
HRESULT WINAPI MFCreateSampleGrabberSinkActivate(IMFMediaType *pIMFMediaType,IMFSampleGrabberSinkCallback *pIMFSampleGrabberSinkCallback,IMFActivate **ppIActivate);
HRESULT WINAPI MFCreateSequencerSource(IUnknown *pReserved,IMFSequencerSource **ppSequencerSource);
HRESULT WINAPI MFCreateSimpleTypeHandler(IMFMediaTypeHandler **ppHandler);
HRESULT WINAPI MFCreateStandardQualityManager(IMFQualityManager **ppQualityManager);
HRESULT WINAPI MFCreateStreamDescriptor(DWORD dwStreamIdentifier,DWORD cMediaTypes,IMFMediaType **apMediaTypes,IMFStreamDescriptor **ppDescriptor);
HRESULT WINAPI MFCreateSourceResolver(IMFSourceResolver **ppISourceResolver);
HRESULT WINAPI MFCreateSystemTimeSource(IMFPresentationTimeSource **ppSystemTimeSource);
HRESULT WINAPI MFCreateTopoLoader(IMFTopoLoader **ppObj);
HRESULT WINAPI MFCreateTopology(IMFTopology **ppTopo);
HRESULT WINAPI MFCreateTopologyNode(MF_TOPOLOGY_TYPE NodeType,IMFTopologyNode **ppNode);
HRESULT WINAPI MFCreateVideoRenderer(REFIID riidRenderer,void **ppVideoRenderer);
HRESULT WINAPI MFCreateVideoRendererActivate(HWND hwndVideo,IMFActivate **ppActivate);
HRESULT WINAPI MFGetService(IUnknown *punkObject,REFGUID guidService,REFIID riid,LPVOID *ppvObject);
HRESULT WINAPI MFGetSupportedMimeTypes(PROPVARIANT *pPropVarMimeTypeArray);
HRESULT WINAPI MFGetSupportedSchemes(PROPVARIANT *pPropVarSchemeArray);
MFTIME WINAPI MFGetSystemTime(void);
HRESULT WINAPI MFShutdownObject(IUnknown *pUnk);
HRESULT WINAPI CreateNamedPropertyStore(INamedPropertyStore **ppStore);
HRESULT WINAPI CreatePropertyStore(IPropertyStore **ppStore);
#if (WINVER >= 0x0601)

HRESULT WINAPI MFCreateTranscodeProfile(IMFTranscodeProfile **ppTranscodeProfile);
HRESULT WINAPI MFCreateTranscodeSinkActivate(IMFActivate **ppActivate);
HRESULT WINAPI MFCreateTranscodeTopology(IMFMediaSource *pSrc,LPCWSTR pwszOutputFilePath,IMFTranscodeProfile *pProfile,IMFTopology **ppTranscodeTopo);
HRESULT WINAPI MFEnumDeviceSources(IMFAttributes *pAttributes,IMFActivate ***pppSourceActivate,UINT32 *pcSourceActivate);
HRESULT WINAPI MFGetTopoNodeCurrentType(IMFTopologyNode *pNode,DWORD dwStreamIndex,WINBOOL fOutput,IMFMediaType **ppType);
HRESULT WINAPI MFTranscodeGetAudioOutputAvailableTypes(REFGUID guidSubType,DWORD dwMFTFlags,IMFAttributes *pCodecConfig,IMFCollection **ppAvailableTypes);
#endif
EXTERN_GUID(MFNETSOURCE_STATISTICS, 0x3cb1f274, 0x0505, 0x4c5d, 0xae, 0x71, 0x0a, 0x55, 0x63, 0x44, 0xef, 0xa1);
EXTERN_GUID(MFNETSOURCE_STATISTICS_SERVICE, 0x3cb1f275, 0x0505, 0x4c5d, 0xae, 0x71, 0x0a, 0x55, 0x63, 0x44, 0xef, 0xa1);
EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_MIXER_CLSID, 0xba491360, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );
EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_MIXER_ACTIVATE, 0xba491361, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );
EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_MIXER_FLAGS, 0xba491362, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );
EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_PRESENTER_CLSID, 0xba491364, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );
EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_PRESENTER_ACTIVATE, 0xba491365, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );
EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_PRESENTER_FLAGS, 0xba491366, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );
EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_SESSION_ID, 0xede4b5e3, 0xf805, 0x4d6c, 0x99, 0xb3, 0xdb, 0x01, 0xbf, 0x95, 0xdf, 0xab);
EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_ENDPOINT_ID, 0xb10aaec3, 0xef71, 0x4cc3, 0xb8, 0x73, 0x5, 0xa9, 0xa0, 0x8b, 0x9f, 0x8e);
EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_ENDPOINT_ROLE, 0x6ba644ff, 0x27c5, 0x4d02, 0x98, 0x87, 0xc2, 0x86, 0x19, 0xfd, 0xb9, 0x1b);
EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_STREAM_CATEGORY, 0xa9770471, 0x92ec, 0x4df4, 0x94, 0xfe, 0x81, 0xc3, 0x6f, 0xc, 0x3a, 0x7a);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_FRIENDLY_NAME, 0x60d0e559,0x52f8,0x4fa2,0xbb,0xce,0xac,0xdb,0x34,0xa8,0xec,0x1);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_MEDIA_TYPE, 0x56a819ca,0xc78,0x4de4,0xa0,0xa7,0x3d,0xda,0xba,0xf,0x24,0xd4);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_GUID, 0x14dd9a1c, 0x7cff, 0x41be, 0xb1, 0xb9, 0xba, 0x1a, 0xc6, 0xec, 0xb5, 0x71);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_ENDPOINT_ID, 0x30da9258,0xfeb9,0x47a7,0xa4,0x53,0x76,0x3a,0x7a,0x8e,0x1c,0x5f);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_ROLE, 0xbc9d118e,0x8c67,0x4a18,0x85,0xd4,0x12,0xd3,0x0,0x40,0x5,0x52);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_CATEGORY, 0x77f0ae69,0xc3bd,0x4509,0x94,0x1d,0x46,0x7e,0x4d,0x24,0x89,0x9e);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_HW_SOURCE, 0xde7046ba,0x54d6,0x4487,0xa2,0xa4,0xec,0x7c,0xd,0x1b,0xd1,0x63);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_MAX_BUFFERS, 0x7dd9b730,0x4f2d,0x41d5,0x8f,0x95,0xc,0xc9,0xa9,0x12,0xba,0x26);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_SYMBOLIC_LINK, 0x58f0aad8,0x22bf,0x4f8a,0xbb,0x3d,0xd2,0xc4,0x97,0x8c,0x6e,0x2f);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE, 0xc60ac5fe,0x252a,0x478f,0xa0,0xef,0xbc,0x8f,0xa5,0xf7,0xca,0xd3);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_GUID, 0x8ac3587a,0x4ae7,0x42d8,0x99,0xe0,0x0a,0x60,0x13,0xee,0xf9,0x0f);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_SYMBOLIC_LINK, 0x98d24b5e, 0x5930, 0x4614, 0xb5, 0xa1, 0xf6, 0x0, 0xf9, 0x35, 0x5a, 0x78);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_PROVIDER_DEVICE_ID, 0x36689d42, 0xa06c, 0x40ae, 0x84, 0xcf, 0xf5, 0xa0, 0x34, 0x6, 0x7c, 0xc4);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_XADDRESS, 0xbca0be52, 0xc327, 0x44c7, 0x9b, 0x7d, 0x7f, 0xa8, 0xd9, 0xb5, 0xbc, 0xda);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_STREAM_URL, 0x9d7b40d2, 0x3617, 0x4043, 0x93, 0xe3, 0x8d, 0x6d, 0xa9, 0xbb, 0x34, 0x92);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_USERNAME,0x5d01add, 0x949f, 0x46eb, 0xbc, 0x8e, 0x8b, 0xd, 0x2b, 0x32, 0xd7, 0x9d);
EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_PASSWORD, 0xa0fd7e16, 0x42d9, 0x49df, 0x84, 0xc0, 0xe8, 0x2c, 0x5e, 0xab, 0x88, 0x74);
EXTERN_GUID(MF_METADATA_PROVIDER_SERVICE, 0xdb214084, 0x58a4, 0x4d2e, 0xb8, 0x4f, 0x6f, 0x75, 0x5b, 0x2f, 0x7a, 0xd);
EXTERN_GUID(MF_PROPERTY_HANDLER_SERVICE, 0xa3face02, 0x32b8, 0x41dd, 0x90, 0xe7, 0x5f, 0xef, 0x7c, 0x89, 0x91, 0xb5);
EXTERN_GUID(MF_RATE_CONTROL_SERVICE, 0x866fa297, 0xb802, 0x4bf8, 0x9d, 0xc9, 0x5e, 0x3b, 0x6a, 0x9f, 0x53, 0xc9);
EXTERN_GUID(MF_SAMPLEGRABBERSINK_IGNORE_CLOCK, 0x0efda2c0, 0x2b69, 0x4e2e, 0xab, 0x8d, 0x46, 0xdc, 0xbf, 0xf7, 0xd2, 0x5d);
EXTERN_GUID(MF_SD_LANGUAGE, 0xaf2180, 0xbdc2, 0x423c, 0xab, 0xca, 0xf5, 0x3, 0x59, 0x3b, 0xc1, 0x21);
EXTERN_GUID(MF_SD_PROTECTED, 0xaf2181, 0xbdc2, 0x423c, 0xab, 0xca, 0xf5, 0x3, 0x59, 0x3b, 0xc1, 0x21);
EXTERN_GUID(MF_SD_STREAM_NAME, 0x4f1b099d, 0xd314, 0x41e5, 0xa7, 0x81, 0x7f, 0xef, 0xaa, 0x4c, 0x50, 0x1f);
EXTERN_GUID(MF_SD_MUTUALLY_EXCLUSIVE, 0x23ef79c, 0x388d, 0x487f, 0xac, 0x17, 0x69, 0x6c, 0xd6, 0xe3, 0xc6, 0xf5);
EXTERN_GUID(MF_TOPONODE_DRAIN, 0x494bbce9, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_D3DAWARE, 0x494bbced, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPOLOGY_RESOLUTION_STATUS, 0x494bbcde, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_ERRORCODE, 0x494bbcee, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_CONNECT_METHOD, 0x494bbcf1, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_LOCKED, 0x494bbcf7, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_WORKQUEUE_ID, 0x494bbcf8, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_WORKQUEUE_MMCSS_CLASS, 0x494bbcf9, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_DECRYPTOR, 0x494bbcfa, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_DISCARDABLE, 0x494bbcfb, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_ERROR_MAJORTYPE, 0x494bbcfd, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_ERROR_SUBTYPE, 0x494bbcfe, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_WORKQUEUE_MMCSS_TASKID, 0x494bbcff, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_WORKQUEUE_MMCSS_PRIORITY, 0x5001f840, 0x2816, 0x48f4, 0x93, 0x64, 0xad, 0x1e, 0xf6, 0x61, 0xa1, 0x23);
EXTERN_GUID(MF_TOPONODE_WORKQUEUE_ITEM_PRIORITY, 0xa1ff99be, 0x5e97, 0x4a53, 0xb4, 0x94, 0x56, 0x8c, 0x64, 0x2c, 0x0f, 0xf3);
EXTERN_GUID(MF_TOPONODE_MARKIN_HERE, 0x494bbd00, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_MARKOUT_HERE, 0x494bbd01, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_DECODER, 0x494bbd02, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);
EXTERN_GUID(MF_TOPONODE_MEDIASTART, 0x835c58ea, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);
EXTERN_GUID(MF_TOPONODE_MEDIASTOP, 0x835c58eb, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);
EXTERN_GUID(MF_TOPONODE_SOURCE, 0x835c58ec, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);
EXTERN_GUID(MF_TOPONODE_PRESENTATION_DESCRIPTOR, 0x835c58ed, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);
EXTERN_GUID(MF_TOPONODE_STREAM_DESCRIPTOR, 0x835c58ee, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);
EXTERN_GUID(MF_TOPONODE_SEQUENCE_ELEMENTID, 0x835c58ef, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);
EXTERN_GUID(MF_TOPONODE_TRANSFORM_OBJECTID, 0x88dcc0c9, 0x293e, 0x4e8b, 0x9a, 0xeb, 0xa, 0xd6, 0x4c, 0xc0, 0x16, 0xb0);
EXTERN_GUID(MF_TOPONODE_STREAMID, 0x14932f9b, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);
EXTERN_GUID(MF_TOPONODE_NOSHUTDOWN_ON_REMOVE, 0x14932f9c, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);
EXTERN_GUID(MF_TOPONODE_RATELESS, 0x14932f9d, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);
EXTERN_GUID(MF_TOPONODE_DISABLE_PREROLL, 0x14932f9e, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);
EXTERN_GUID(MF_TOPONODE_PRIMARYOUTPUT, 0x6304ef99, 0x16b2, 0x4ebe, 0x9d, 0x67, 0xe4, 0xc5, 0x39, 0xb3, 0xa2, 0x59);
EXTERN_GUID(MF_TRANSCODE_SKIP_METADATA_TRANSFER, 0x4e4469ef, 0xb571, 0x4959, 0x8f, 0x83, 0x3d, 0xcf, 0xba, 0x33, 0xa3, 0x93);
EXTERN_GUID(MF_TRANSCODE_TOPOLOGYMODE, 0x3e3df610, 0x394a, 0x40b2, 0x9d, 0xea, 0x3b, 0xab, 0x65, 0xb, 0xeb, 0xf2);
EXTERN_GUID(MF_TRANSCODE_ADJUST_PROFILE, 0x9c37c21b, 0x60f, 0x487c, 0xa6, 0x90, 0x80, 0xd7, 0xf5, 0xd, 0x1c, 0x72);
EXTERN_GUID(MF_TRANSCODE_ENCODINGPROFILE, 0x6947787c, 0xf508, 0x4ea9, 0xb1, 0xe9, 0xa1, 0xfe, 0x3a, 0x49, 0xfb, 0xc9);
EXTERN_GUID(MF_TRANSCODE_QUALITYVSSPEED, 0x98332df8, 0x03cd, 0x476b, 0x89, 0xfa, 0x3f, 0x9e, 0x44, 0x2d, 0xec, 0x9f);
EXTERN_GUID(MF_TRANSCODE_DONOT_INSERT_ENCODER, 0xf45aa7ce, 0xab24, 0x4012, 0xa1, 0x1b, 0xdc, 0x82, 0x20, 0x20, 0x14, 0x10);
EXTERN_GUID(MR_AUDIO_POLICY_SERVICE, 0x911fd737, 0x6775, 0x4ab0, 0xa6, 0x14, 0x29, 0x78, 0x62, 0xfd, 0xac, 0x88);
EXTERN_GUID(MR_CAPTURE_POLICY_VOLUME_SERVICE, 0x24030acd, 0x107a, 0x4265, 0x97, 0x5c, 0x41, 0x4e, 0x33, 0xe6, 0x5f, 0x2a);
EXTERN_GUID(MR_POLICY_VOLUME_SERVICE, 0x1abaa2ac, 0x9d3b, 0x47c6, 0xab, 0x48, 0xc5, 0x95, 0x6, 0xde, 0x78, 0x4d);
EXTERN_GUID(MR_STREAM_VOLUME_SERVICE, 0xf8b5fa2f, 0x32ef, 0x46f5, 0xb1, 0x72, 0x13, 0x21, 0x21, 0x2f, 0xb2, 0xc4);
EXTERN_GUID(CLSID_VideoProcessorMFT, 0x88753b26, 0x5b24, 0x49bd, 0xb2, 0xe7, 0xc, 0x44, 0x5c, 0x78, 0xc9, 0x82);
EXTERN_GUID(CLSID_FrameServerNetworkCameraSource, 0x7a213aa7, 0x866f, 0x414a, 0x8c, 0x1a, 0x27, 0x5c, 0x72, 0x83, 0xa3, 0x95);
EXTERN_GUID(CLSID_MSH264DecoderMFT, 0x62ce7e72, 0x4c71, 0x4d20, 0xb1, 0x5d, 0x45, 0x28, 0x31, 0xa8, 0x7d, 0x9d);
EXTERN_GUID(CLSID_MSH264EncoderMFT, 0x6ca50344, 0x051a, 0x4ded, 0x97, 0x79, 0xa4, 0x33, 0x05, 0x16, 0x5e, 0x35);
EXTERN_GUID(CLSID_MSDDPlusDecMFT, 0x177c0afe, 0x900b, 0x48d4, 0x9e, 0x4c, 0x57, 0xad, 0xd2, 0x50, 0xb3, 0xd4);
EXTERN_GUID(CLSID_MP3DecMediaObject, 0xbbeea841, 0x0a63, 0x4f52, 0xa7, 0xab, 0xa9, 0xb3, 0xa8, 0x4e, 0xd3, 0x8a);
EXTERN_GUID(CLSID_MSAACDecMFT, 0x32d186a7, 0x218f, 0x4c75, 0x88, 0x76, 0xdd, 0x77, 0x27, 0x3a, 0x89, 0x99);
EXTERN_GUID(CLSID_MSH265DecoderMFT, 0x420a51a3, 0xd605, 0x430c, 0xb4, 0xfc, 0x45, 0x27, 0x4f, 0xa6, 0xc5, 0x62);
EXTERN_GUID(CLSID_WMVDecoderMFT, 0x82d353df, 0x90bd, 0x4382, 0x8b, 0xc2, 0x3f, 0x61, 0x92, 0xb7, 0x6e, 0x34);
EXTERN_GUID(CLSID_WMADecMediaObject, 0x2eeb4adf, 0x4578, 0x4d10, 0xbc, 0xa7, 0xbb, 0x95, 0x5f, 0x56, 0x32, 0x0a);
EXTERN_GUID(CLSID_MSMPEGAudDecMFT, 0x70707b39, 0xb2ca, 0x4015, 0xab, 0xea, 0xf8, 0x44, 0x7d, 0x22, 0xd8, 0x8b);
EXTERN_GUID(CLSID_MSMPEGDecoderMFT, 0x2d709e52, 0x123f, 0x49b5, 0x9c, 0xbc, 0x9a, 0xf5, 0xcd, 0xe2, 0x8f, 0xb9);
EXTERN_GUID(CLSID_AudioResamplerMediaObject, 0xf447b69e, 0x1884, 0x4a7e, 0x80, 0x55, 0x34, 0x6f, 0x74, 0xd6, 0xed, 0xb3);
EXTERN_GUID(CLSID_MSVPxDecoder, 0xe3aaf548, 0xc9a4, 0x4c6e, 0x23, 0x4d, 0x5a, 0xda, 0x37, 0x4b, 0x00, 0x00);
EXTERN_GUID(CLSID_MSOpusDecoder, 0x63e17c10, 0x2d43, 0x4c42, 0x8f, 0xe3, 0x8d, 0x8b, 0x63, 0xe4, 0x6a, 0x6a);
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mfidl_h__ */
