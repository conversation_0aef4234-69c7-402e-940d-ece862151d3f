/*** Autogenerated by WIDL 10.12 from include/windows.management.deployment.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_management_deployment_h__
#define __windows_management_deployment_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult ABI::Windows::Management::Deployment::IDeploymentResult
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IDeploymentResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager ABI::Windows::Management::Deployment::IPackageManager
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IPackageManager;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 ABI::Windows::Management::Deployment::IPackageManager2
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IPackageManager2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation ABI::Windows::Management::Deployment::IPackageUserInformation
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IPackageUserInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentResult_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                class DeploymentResult;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CDeploymentResult __x_ABI_CWindows_CManagement_CDeployment_CDeploymentResult;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentResult_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CPackageManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CPackageManager_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                class PackageManager;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CPackageManager __x_ABI_CWindows_CManagement_CDeployment_CPackageManager;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CManagement_CDeployment_CPackageManager_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CPackageUserInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CPackageUserInformation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                class PackageUserInformation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CPackageUserInformation __x_ABI_CWindows_CManagement_CDeployment_CPackageUserInformation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CManagement_CDeployment_CPackageUserInformation_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CFoundation__CUri_FWD_DEFINED__
#define ____FIIterable_1_Windows__CFoundation__CUri_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CFoundation__CUri __FIIterable_1_Windows__CFoundation__CUri;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CFoundation__CUri ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Uri* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CFoundation__CUri_FWD_DEFINED__
#define ____FIIterator_1_Windows__CFoundation__CUri_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CFoundation__CUri __FIIterator_1_Windows__CFoundation__CUri;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CFoundation__CUri ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Uri* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CApplicationModel__CPackage __FIIterable_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CApplicationModel__CPackage __FIIterator_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
#define ____FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
#define ____FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Management::Deployment::PackageUserInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
#define ____FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
typedef interface __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress;
#ifdef __cplusplus
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
#define ____FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
typedef interface __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress;
#ifdef __cplusplus
#define __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress ABI::Windows::Foundation::IAsyncOperationProgressHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
#define ____FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
typedef interface __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress;
#ifdef __cplusplus
#define __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress ABI::Windows::Foundation::IAsyncOperationWithProgressCompletedHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <windows.foundation.h>
#include <windows.applicationmodel.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions_ENUM_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                enum DeploymentOptions {
                    DeploymentOptions_None = 0x0,
                    DeploymentOptions_ForceApplicationShutdown = 0x1,
                    DeploymentOptions_DevelopmentMode = 0x2,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    DeploymentOptions_InstallAllResources = 0x20,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    DeploymentOptions_ForceTargetApplicationShutdown = 0x40,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
                    DeploymentOptions_RequiredContentGroupOnly = 0x100,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
                    DeploymentOptions_ForceUpdateFromAnyVersion = 0x40000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
                    DeploymentOptions_RetainFilesOnFailure = 0x200000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
                    DeploymentOptions_StageInPlace = 0x400000
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions {
    DeploymentOptions_None = 0x0,
    DeploymentOptions_ForceApplicationShutdown = 0x1,
    DeploymentOptions_DevelopmentMode = 0x2,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    DeploymentOptions_InstallAllResources = 0x20,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    DeploymentOptions_ForceTargetApplicationShutdown = 0x40,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
    DeploymentOptions_RequiredContentGroupOnly = 0x100,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
    DeploymentOptions_ForceUpdateFromAnyVersion = 0x40000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
    DeploymentOptions_RetainFilesOnFailure = 0x200000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
    DeploymentOptions_StageInPlace = 0x400000
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
};
#ifdef WIDL_using_Windows_Management_Deployment
#define DeploymentOptions __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif /* ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState_ENUM_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                enum DeploymentProgressState {
                    DeploymentProgressState_Queued = 0,
                    DeploymentProgressState_Processing = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState {
    DeploymentProgressState_Queued = 0,
    DeploymentProgressState_Processing = 1
};
#ifdef WIDL_using_Windows_Management_Deployment
#define DeploymentProgressState __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif /* ____x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState_ENUM_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                enum PackageInstallState {
                    PackageInstallState_NotInstalled = 0,
                    PackageInstallState_Staged = 1,
                    PackageInstallState_Installed = 2,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
                    PackageInstallState_Paused = 6
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState {
    PackageInstallState_NotInstalled = 0,
    PackageInstallState_Staged = 1,
    PackageInstallState_Installed = 2,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
    PackageInstallState_Paused = 6
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
};
#ifdef WIDL_using_Windows_Management_Deployment
#define PackageInstallState __x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif /* ____x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState __x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CPackageState_ENUM_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CPackageState_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                enum PackageState {
                    PackageState_Normal = 0,
                    PackageState_LicenseInvalid = 1,
                    PackageState_Modified = 2,
                    PackageState_Tampered = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CManagement_CDeployment_CPackageState {
    PackageState_Normal = 0,
    PackageState_LicenseInvalid = 1,
    PackageState_Modified = 2,
    PackageState_Tampered = 3
};
#ifdef WIDL_using_Windows_Management_Deployment
#define PackageState __x_ABI_CWindows_CManagement_CDeployment_CPackageState
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif /* ____x_ABI_CWindows_CManagement_CDeployment_CPackageState_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CManagement_CDeployment_CPackageState __x_ABI_CWindows_CManagement_CDeployment_CPackageState;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CPackageTypes_ENUM_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CPackageTypes_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                enum PackageTypes {
                    PackageTypes_None = 0x0,
                    PackageTypes_Main = 0x1,
                    PackageTypes_Framework = 0x2,
                    PackageTypes_Resource = 0x4,
                    PackageTypes_Bundle = 0x8,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    PackageTypes_Xap = 0x10,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    PackageTypes_Optional = 0x20,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
                    PackageTypes_All = 0xffffffff
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes {
    PackageTypes_None = 0x0,
    PackageTypes_Main = 0x1,
    PackageTypes_Framework = 0x2,
    PackageTypes_Resource = 0x4,
    PackageTypes_Bundle = 0x8,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    PackageTypes_Xap = 0x10,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    PackageTypes_Optional = 0x20,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
    PackageTypes_All = 0xffffffff
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
};
#ifdef WIDL_using_Windows_Management_Deployment
#define PackageTypes __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif /* ____x_ABI_CWindows_CManagement_CDeployment_CPackageTypes_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions_ENUM_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                enum RemovalOptions {
                    RemovalOptions_None = 0x0,
                    RemovalOptions_PreserveApplicationData = 0x1000,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
                    RemovalOptions_PreserveRoamableApplicationData = 0x80,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
                    RemovalOptions_RemoveForAllUsers = 0x80000
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions {
    RemovalOptions_None = 0x0,
    RemovalOptions_PreserveApplicationData = 0x1000,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
    RemovalOptions_PreserveRoamableApplicationData = 0x80,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
    RemovalOptions_RemoveForAllUsers = 0x80000
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */
};
#ifdef WIDL_using_Windows_Management_Deployment
#define RemovalOptions __x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif /* ____x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions __x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgress __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgress;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                typedef struct DeploymentProgress DeploymentProgress;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult ABI::Windows::Management::Deployment::IDeploymentResult
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IDeploymentResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager ABI::Windows::Management::Deployment::IPackageManager
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IPackageManager;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 ABI::Windows::Management::Deployment::IPackageManager2
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IPackageManager2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation ABI::Windows::Management::Deployment::IPackageUserInformation
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                interface IPackageUserInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CFoundation__CUri_FWD_DEFINED__
#define ____FIIterable_1_Windows__CFoundation__CUri_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CFoundation__CUri __FIIterable_1_Windows__CFoundation__CUri;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CFoundation__CUri ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Uri* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CFoundation__CUri_FWD_DEFINED__
#define ____FIIterator_1_Windows__CFoundation__CUri_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CFoundation__CUri __FIIterator_1_Windows__CFoundation__CUri;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CFoundation__CUri ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Uri* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CApplicationModel__CPackage __FIIterable_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CApplicationModel__CPackage __FIIterator_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
#define ____FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
#define ____FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Management::Deployment::PackageUserInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
#define ____FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_FWD_DEFINED__
typedef interface __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress;
#ifdef __cplusplus
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                struct DeploymentProgress {
                    ABI::Windows::Management::Deployment::DeploymentProgressState state;
                    UINT32 percentage;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgress {
    __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgressState state;
    UINT32 percentage;
};
#ifdef WIDL_using_Windows_Management_Deployment
#define DeploymentProgress __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgress
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IDeploymentResult interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult, 0x2563b9ae, 0xb77d, 0x4c1f, 0x8a,0x7b, 0x20,0xe6,0xad,0x51,0x5e,0xf3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                MIDL_INTERFACE("2563b9ae-b77d-4c1f-8a7b-20e6ad515ef3")
                IDeploymentResult : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ErrorText(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ActivityId(
                        GUID *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ExtendedErrorCode(
                        HRESULT *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult, 0x2563b9ae, 0xb77d, 0x4c1f, 0x8a,0x7b, 0x20,0xe6,0xad,0x51,0x5e,0xf3)
#endif
#else
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This,
        TrustLevel *trustLevel);

    /*** IDeploymentResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ErrorText)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_ActivityId)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *get_ExtendedErrorCode)(
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult *This,
        HRESULT *value);

    END_INTERFACE
} __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResultVtbl;

interface __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult {
    CONST_VTBL __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeploymentResult methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ErrorText(This,value) (This)->lpVtbl->get_ErrorText(This,value)
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ActivityId(This,value) (This)->lpVtbl->get_ActivityId(This,value)
#define __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ExtendedErrorCode(This,value) (This)->lpVtbl->get_ExtendedErrorCode(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_QueryInterface(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_AddRef(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_Release(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetIids(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetRuntimeClassName(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetTrustLevel(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeploymentResult methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ErrorText(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This,HSTRING *value) {
    return This->lpVtbl->get_ErrorText(This,value);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ActivityId(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This,GUID *value) {
    return This->lpVtbl->get_ActivityId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ExtendedErrorCode(__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult* This,HRESULT *value) {
    return This->lpVtbl->get_ExtendedErrorCode(This,value);
}
#endif
#ifdef WIDL_using_Windows_Management_Deployment
#define IID_IDeploymentResult IID___x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult
#define IDeploymentResultVtbl __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResultVtbl
#define IDeploymentResult __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult
#define IDeploymentResult_QueryInterface __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_QueryInterface
#define IDeploymentResult_AddRef __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_AddRef
#define IDeploymentResult_Release __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_Release
#define IDeploymentResult_GetIids __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetIids
#define IDeploymentResult_GetRuntimeClassName __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetRuntimeClassName
#define IDeploymentResult_GetTrustLevel __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_GetTrustLevel
#define IDeploymentResult_get_ErrorText __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ErrorText
#define IDeploymentResult_get_ActivityId __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ActivityId
#define IDeploymentResult_get_ExtendedErrorCode __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_get_ExtendedErrorCode
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackageManager interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CManagement_CDeployment_CIPackageManager, 0x9a7d4b65, 0x5e8f, 0x4fc7, 0xa2,0xe5, 0x7f,0x69,0x25,0xcb,0x8b,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                MIDL_INTERFACE("9a7d4b65-5e8f-4fc7-a2e5-7f6925cb8b53")
                IPackageManager : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE AddPackageAsync(
                        ABI::Windows::Foundation::IUriRuntimeClass *uri,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Uri* > *dependencies,
                        ABI::Windows::Management::Deployment::DeploymentOptions options,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE UpdatePackageAsync(
                        ABI::Windows::Foundation::IUriRuntimeClass *uri,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Uri* > *dependencies,
                        ABI::Windows::Management::Deployment::DeploymentOptions options,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RemovePackageAsync(
                        HSTRING name,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StagePackageAsync(
                        ABI::Windows::Foundation::IUriRuntimeClass *uri,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Uri* > *dependencies,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RegisterPackageAsync(
                        ABI::Windows::Foundation::IUriRuntimeClass *uri,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Uri* > *dependencies,
                        ABI::Windows::Management::Deployment::DeploymentOptions options,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackages(
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByUserSecurityId(
                        HSTRING sid,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByNamePublisher(
                        HSTRING name,
                        HSTRING publisher,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByUserSecurityIdNamePublisher(
                        HSTRING sid,
                        HSTRING name,
                        HSTRING publisher,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindUsers(
                        HSTRING name,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* > **users) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SetPackageState(
                        HSTRING name,
                        ABI::Windows::Management::Deployment::PackageState state) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackageByPackageFullName(
                        HSTRING name,
                        ABI::Windows::ApplicationModel::IPackage **package) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CleanupPackageForUserAsync(
                        HSTRING name,
                        HSTRING sid,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByPackageFamilyName(
                        HSTRING family_name,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByUserSecurityIdPackageFamilyName(
                        HSTRING sid,
                        HSTRING family_name,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackageByUserSecurityIdPackageFullName(
                        HSTRING sid,
                        HSTRING name,
                        ABI::Windows::ApplicationModel::IPackage **package) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager, 0x9a7d4b65, 0x5e8f, 0x4fc7, 0xa2,0xe5, 0x7f,0x69,0x25,0xcb,0x8b,0x53)
#endif
#else
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CIPackageManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        TrustLevel *trustLevel);

    /*** IPackageManager methods ***/
    HRESULT (STDMETHODCALLTYPE *AddPackageAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __FIIterable_1_Windows__CFoundation__CUri *dependencies,
        __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *UpdatePackageAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __FIIterable_1_Windows__CFoundation__CUri *dependencies,
        __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *RemovePackageAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING name,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *StagePackageAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __FIIterable_1_Windows__CFoundation__CUri *dependencies,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *RegisterPackageAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __FIIterable_1_Windows__CFoundation__CUri *dependencies,
        __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *FindPackages)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByUserSecurityId)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING sid,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByNamePublisher)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING name,
        HSTRING publisher,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByUserSecurityIdNamePublisher)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING sid,
        HSTRING name,
        HSTRING publisher,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindUsers)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING name,
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation **users);

    HRESULT (STDMETHODCALLTYPE *SetPackageState)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING name,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageState state);

    HRESULT (STDMETHODCALLTYPE *FindPackageByPackageFullName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING name,
        __x_ABI_CWindows_CApplicationModel_CIPackage **package);

    HRESULT (STDMETHODCALLTYPE *CleanupPackageForUserAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING name,
        HSTRING sid,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByPackageFamilyName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING family_name,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByUserSecurityIdPackageFamilyName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING sid,
        HSTRING family_name,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackageByUserSecurityIdPackageFullName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager *This,
        HSTRING sid,
        HSTRING name,
        __x_ABI_CWindows_CApplicationModel_CIPackage **package);

    END_INTERFACE
} __x_ABI_CWindows_CManagement_CDeployment_CIPackageManagerVtbl;

interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager {
    CONST_VTBL __x_ABI_CWindows_CManagement_CDeployment_CIPackageManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackageManager methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_AddPackageAsync(This,uri,dependencies,options,operation) (This)->lpVtbl->AddPackageAsync(This,uri,dependencies,options,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_UpdatePackageAsync(This,uri,dependencies,options,operation) (This)->lpVtbl->UpdatePackageAsync(This,uri,dependencies,options,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_RemovePackageAsync(This,name,operation) (This)->lpVtbl->RemovePackageAsync(This,name,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_StagePackageAsync(This,uri,dependencies,operation) (This)->lpVtbl->StagePackageAsync(This,uri,dependencies,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_RegisterPackageAsync(This,uri,dependencies,options,operation) (This)->lpVtbl->RegisterPackageAsync(This,uri,dependencies,options,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackages(This,packages) (This)->lpVtbl->FindPackages(This,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityId(This,sid,packages) (This)->lpVtbl->FindPackagesByUserSecurityId(This,sid,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByNamePublisher(This,name,publisher,packages) (This)->lpVtbl->FindPackagesByNamePublisher(This,name,publisher,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityIdNamePublisher(This,sid,name,publisher,packages) (This)->lpVtbl->FindPackagesByUserSecurityIdNamePublisher(This,sid,name,publisher,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindUsers(This,name,users) (This)->lpVtbl->FindUsers(This,name,users)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_SetPackageState(This,name,state) (This)->lpVtbl->SetPackageState(This,name,state)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackageByPackageFullName(This,name,package) (This)->lpVtbl->FindPackageByPackageFullName(This,name,package)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_CleanupPackageForUserAsync(This,name,sid,operation) (This)->lpVtbl->CleanupPackageForUserAsync(This,name,sid,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByPackageFamilyName(This,family_name,packages) (This)->lpVtbl->FindPackagesByPackageFamilyName(This,family_name,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityIdPackageFamilyName(This,sid,family_name,packages) (This)->lpVtbl->FindPackagesByUserSecurityIdPackageFamilyName(This,sid,family_name,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackageByUserSecurityIdPackageFullName(This,sid,name,package) (This)->lpVtbl->FindPackageByUserSecurityIdPackageFullName(This,sid,name,package)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_QueryInterface(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_AddRef(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_Release(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetIids(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetRuntimeClassName(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetTrustLevel(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackageManager methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_AddPackageAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__FIIterable_1_Windows__CFoundation__CUri *dependencies,__x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->AddPackageAsync(This,uri,dependencies,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_UpdatePackageAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__FIIterable_1_Windows__CFoundation__CUri *dependencies,__x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->UpdatePackageAsync(This,uri,dependencies,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_RemovePackageAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING name,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->RemovePackageAsync(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_StagePackageAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__FIIterable_1_Windows__CFoundation__CUri *dependencies,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->StagePackageAsync(This,uri,dependencies,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_RegisterPackageAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__FIIterable_1_Windows__CFoundation__CUri *dependencies,__x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->RegisterPackageAsync(This,uri,dependencies,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackages(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackages(This,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityId(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING sid,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByUserSecurityId(This,sid,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByNamePublisher(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING name,HSTRING publisher,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByNamePublisher(This,name,publisher,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityIdNamePublisher(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING sid,HSTRING name,HSTRING publisher,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByUserSecurityIdNamePublisher(This,sid,name,publisher,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindUsers(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING name,__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation **users) {
    return This->lpVtbl->FindUsers(This,name,users);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_SetPackageState(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING name,__x_ABI_CWindows_CManagement_CDeployment_CPackageState state) {
    return This->lpVtbl->SetPackageState(This,name,state);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackageByPackageFullName(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING name,__x_ABI_CWindows_CApplicationModel_CIPackage **package) {
    return This->lpVtbl->FindPackageByPackageFullName(This,name,package);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_CleanupPackageForUserAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING name,HSTRING sid,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->CleanupPackageForUserAsync(This,name,sid,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByPackageFamilyName(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING family_name,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByPackageFamilyName(This,family_name,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityIdPackageFamilyName(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING sid,HSTRING family_name,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByUserSecurityIdPackageFamilyName(This,sid,family_name,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackageByUserSecurityIdPackageFullName(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager* This,HSTRING sid,HSTRING name,__x_ABI_CWindows_CApplicationModel_CIPackage **package) {
    return This->lpVtbl->FindPackageByUserSecurityIdPackageFullName(This,sid,name,package);
}
#endif
#ifdef WIDL_using_Windows_Management_Deployment
#define IID_IPackageManager IID___x_ABI_CWindows_CManagement_CDeployment_CIPackageManager
#define IPackageManagerVtbl __x_ABI_CWindows_CManagement_CDeployment_CIPackageManagerVtbl
#define IPackageManager __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager
#define IPackageManager_QueryInterface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_QueryInterface
#define IPackageManager_AddRef __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_AddRef
#define IPackageManager_Release __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_Release
#define IPackageManager_GetIids __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetIids
#define IPackageManager_GetRuntimeClassName __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetRuntimeClassName
#define IPackageManager_GetTrustLevel __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_GetTrustLevel
#define IPackageManager_AddPackageAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_AddPackageAsync
#define IPackageManager_UpdatePackageAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_UpdatePackageAsync
#define IPackageManager_RemovePackageAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_RemovePackageAsync
#define IPackageManager_StagePackageAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_StagePackageAsync
#define IPackageManager_RegisterPackageAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_RegisterPackageAsync
#define IPackageManager_FindPackages __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackages
#define IPackageManager_FindPackagesByUserSecurityId __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityId
#define IPackageManager_FindPackagesByNamePublisher __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByNamePublisher
#define IPackageManager_FindPackagesByUserSecurityIdNamePublisher __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityIdNamePublisher
#define IPackageManager_FindUsers __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindUsers
#define IPackageManager_SetPackageState __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_SetPackageState
#define IPackageManager_FindPackageByPackageFullName __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackageByPackageFullName
#define IPackageManager_CleanupPackageForUserAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_CleanupPackageForUserAsync
#define IPackageManager_FindPackagesByPackageFamilyName __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByPackageFamilyName
#define IPackageManager_FindPackagesByUserSecurityIdPackageFamilyName __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackagesByUserSecurityIdPackageFamilyName
#define IPackageManager_FindPackageByUserSecurityIdPackageFullName __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_FindPackageByUserSecurityIdPackageFullName
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackageManager2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2, 0xf7aad08d, 0x0840, 0x46f2, 0xb5,0xd8, 0xca,0xd4,0x76,0x93,0xa0,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                MIDL_INTERFACE("f7aad08d-0840-46f2-b5d8-cad47693a095")
                IPackageManager2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE RemovePackageWithOptionsAsync(
                        HSTRING name,
                        ABI::Windows::Management::Deployment::RemovalOptions options,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StagePackageWithOptionsAsync(
                        ABI::Windows::Foundation::IUriRuntimeClass *uri,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Uri* > *dependencies,
                        ABI::Windows::Management::Deployment::DeploymentOptions options,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RegisterPackageByFullNameAsync(
                        HSTRING name,
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *dependencies,
                        ABI::Windows::Management::Deployment::DeploymentOptions options,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesWithPackageTypes(
                        ABI::Windows::Management::Deployment::PackageTypes types,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByUserSecurityIdWithPackageTypes(
                        HSTRING sid,
                        ABI::Windows::Management::Deployment::PackageTypes types,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByNamePublisherWithPackageTypes(
                        HSTRING name,
                        HSTRING publisher,
                        ABI::Windows::Management::Deployment::PackageTypes types,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByUserSecurityIdNamePublisherWithPackageTypes(
                        HSTRING sid,
                        HSTRING name,
                        HSTRING publisher,
                        ABI::Windows::Management::Deployment::PackageTypes types,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByPackageFamilyNameWithPackageTypes(
                        HSTRING family_name,
                        ABI::Windows::Management::Deployment::PackageTypes types,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes(
                        HSTRING sid,
                        HSTRING family_name,
                        ABI::Windows::Management::Deployment::PackageTypes types,
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* > **packages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StageUserDataAsync(
                        HSTRING name,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2, 0xf7aad08d, 0x0840, 0x46f2, 0xb5,0xd8, 0xca,0xd4,0x76,0x93,0xa0,0x95)
#endif
#else
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        TrustLevel *trustLevel);

    /*** IPackageManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *RemovePackageWithOptionsAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING name,
        __x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions options,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *StagePackageWithOptionsAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __FIIterable_1_Windows__CFoundation__CUri *dependencies,
        __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *RegisterPackageByFullNameAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING name,
        __FIIterable_1_HSTRING *dependencies,
        __x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    HRESULT (STDMETHODCALLTYPE *FindPackagesWithPackageTypes)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByUserSecurityIdWithPackageTypes)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING sid,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByNamePublisherWithPackageTypes)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING name,
        HSTRING publisher,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByUserSecurityIdNamePublisherWithPackageTypes)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING sid,
        HSTRING name,
        HSTRING publisher,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByPackageFamilyNameWithPackageTypes)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING family_name,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING sid,
        HSTRING family_name,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,
        __FIIterable_1_Windows__CApplicationModel__CPackage **packages);

    HRESULT (STDMETHODCALLTYPE *StageUserDataAsync)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 *This,
        HSTRING name,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation);

    END_INTERFACE
} __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2Vtbl;

interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2 {
    CONST_VTBL __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackageManager2 methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_RemovePackageWithOptionsAsync(This,name,options,operation) (This)->lpVtbl->RemovePackageWithOptionsAsync(This,name,options,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_StagePackageWithOptionsAsync(This,uri,dependencies,options,operation) (This)->lpVtbl->StagePackageWithOptionsAsync(This,uri,dependencies,options,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_RegisterPackageByFullNameAsync(This,name,dependencies,options,operation) (This)->lpVtbl->RegisterPackageByFullNameAsync(This,name,dependencies,options,operation)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesWithPackageTypes(This,types,packages) (This)->lpVtbl->FindPackagesWithPackageTypes(This,types,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdWithPackageTypes(This,sid,types,packages) (This)->lpVtbl->FindPackagesByUserSecurityIdWithPackageTypes(This,sid,types,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByNamePublisherWithPackageTypes(This,name,publisher,types,packages) (This)->lpVtbl->FindPackagesByNamePublisherWithPackageTypes(This,name,publisher,types,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdNamePublisherWithPackageTypes(This,sid,name,publisher,types,packages) (This)->lpVtbl->FindPackagesByUserSecurityIdNamePublisherWithPackageTypes(This,sid,name,publisher,types,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByPackageFamilyNameWithPackageTypes(This,family_name,types,packages) (This)->lpVtbl->FindPackagesByPackageFamilyNameWithPackageTypes(This,family_name,types,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes(This,sid,family_name,types,packages) (This)->lpVtbl->FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes(This,sid,family_name,types,packages)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_StageUserDataAsync(This,name,operation) (This)->lpVtbl->StageUserDataAsync(This,name,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_QueryInterface(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_AddRef(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_Release(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetIids(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetRuntimeClassName(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetTrustLevel(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackageManager2 methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_RemovePackageWithOptionsAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING name,__x_ABI_CWindows_CManagement_CDeployment_CRemovalOptions options,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->RemovePackageWithOptionsAsync(This,name,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_StagePackageWithOptionsAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__FIIterable_1_Windows__CFoundation__CUri *dependencies,__x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->StagePackageWithOptionsAsync(This,uri,dependencies,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_RegisterPackageByFullNameAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING name,__FIIterable_1_HSTRING *dependencies,__x_ABI_CWindows_CManagement_CDeployment_CDeploymentOptions options,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->RegisterPackageByFullNameAsync(This,name,dependencies,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesWithPackageTypes(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,__x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesWithPackageTypes(This,types,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdWithPackageTypes(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING sid,__x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByUserSecurityIdWithPackageTypes(This,sid,types,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByNamePublisherWithPackageTypes(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING name,HSTRING publisher,__x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByNamePublisherWithPackageTypes(This,name,publisher,types,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdNamePublisherWithPackageTypes(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING sid,HSTRING name,HSTRING publisher,__x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByUserSecurityIdNamePublisherWithPackageTypes(This,sid,name,publisher,types,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByPackageFamilyNameWithPackageTypes(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING family_name,__x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByPackageFamilyNameWithPackageTypes(This,family_name,types,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING sid,HSTRING family_name,__x_ABI_CWindows_CManagement_CDeployment_CPackageTypes types,__FIIterable_1_Windows__CApplicationModel__CPackage **packages) {
    return This->lpVtbl->FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes(This,sid,family_name,types,packages);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_StageUserDataAsync(__x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2* This,HSTRING name,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **operation) {
    return This->lpVtbl->StageUserDataAsync(This,name,operation);
}
#endif
#ifdef WIDL_using_Windows_Management_Deployment
#define IID_IPackageManager2 IID___x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2
#define IPackageManager2Vtbl __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2Vtbl
#define IPackageManager2 __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2
#define IPackageManager2_QueryInterface __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_QueryInterface
#define IPackageManager2_AddRef __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_AddRef
#define IPackageManager2_Release __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_Release
#define IPackageManager2_GetIids __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetIids
#define IPackageManager2_GetRuntimeClassName __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetRuntimeClassName
#define IPackageManager2_GetTrustLevel __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_GetTrustLevel
#define IPackageManager2_RemovePackageWithOptionsAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_RemovePackageWithOptionsAsync
#define IPackageManager2_StagePackageWithOptionsAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_StagePackageWithOptionsAsync
#define IPackageManager2_RegisterPackageByFullNameAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_RegisterPackageByFullNameAsync
#define IPackageManager2_FindPackagesWithPackageTypes __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesWithPackageTypes
#define IPackageManager2_FindPackagesByUserSecurityIdWithPackageTypes __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdWithPackageTypes
#define IPackageManager2_FindPackagesByNamePublisherWithPackageTypes __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByNamePublisherWithPackageTypes
#define IPackageManager2_FindPackagesByUserSecurityIdNamePublisherWithPackageTypes __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdNamePublisherWithPackageTypes
#define IPackageManager2_FindPackagesByPackageFamilyNameWithPackageTypes __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByPackageFamilyNameWithPackageTypes
#define IPackageManager2_FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes
#define IPackageManager2_StageUserDataAsync __x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_StageUserDataAsync
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CManagement_CDeployment_CIPackageManager2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackageUserInformation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation, 0xf6383423, 0xfa09, 0x4cbc, 0x90,0x55, 0x15,0xca,0x27,0x5e,0x2e,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Management {
            namespace Deployment {
                MIDL_INTERFACE("f6383423-fa09-4cbc-9055-15ca275e2e7e")
                IPackageUserInformation : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_UserSecurityId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_InstallState(
                        ABI::Windows::Management::Deployment::PackageInstallState *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation, 0xf6383423, 0xfa09, 0x4cbc, 0x90,0x55, 0x15,0xca,0x27,0x5e,0x2e,0x7e)
#endif
#else
typedef struct __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This,
        TrustLevel *trustLevel);

    /*** IPackageUserInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UserSecurityId)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_InstallState)(
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation *This,
        __x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState *value);

    END_INTERFACE
} __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformationVtbl;

interface __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation {
    CONST_VTBL __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackageUserInformation methods ***/
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_get_UserSecurityId(This,value) (This)->lpVtbl->get_UserSecurityId(This,value)
#define __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_get_InstallState(This,value) (This)->lpVtbl->get_InstallState(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_QueryInterface(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_AddRef(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_Release(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetIids(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetRuntimeClassName(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetTrustLevel(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackageUserInformation methods ***/
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_get_UserSecurityId(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This,HSTRING *value) {
    return This->lpVtbl->get_UserSecurityId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_get_InstallState(__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation* This,__x_ABI_CWindows_CManagement_CDeployment_CPackageInstallState *value) {
    return This->lpVtbl->get_InstallState(This,value);
}
#endif
#ifdef WIDL_using_Windows_Management_Deployment
#define IID_IPackageUserInformation IID___x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation
#define IPackageUserInformationVtbl __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformationVtbl
#define IPackageUserInformation __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation
#define IPackageUserInformation_QueryInterface __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_QueryInterface
#define IPackageUserInformation_AddRef __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_AddRef
#define IPackageUserInformation_Release __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_Release
#define IPackageUserInformation_GetIids __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetIids
#define IPackageUserInformation_GetRuntimeClassName __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetRuntimeClassName
#define IPackageUserInformation_GetTrustLevel __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_GetTrustLevel
#define IPackageUserInformation_get_UserSecurityId __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_get_UserSecurityId
#define IPackageUserInformation_get_InstallState __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_get_InstallState
#endif /* WIDL_using_Windows_Management_Deployment */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Management.Deployment.DeploymentResult
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Management_Deployment_DeploymentResult_DEFINED
#define RUNTIMECLASS_Windows_Management_Deployment_DeploymentResult_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Management_Deployment_DeploymentResult[] = {'W','i','n','d','o','w','s','.','M','a','n','a','g','e','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','R','e','s','u','l','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Management_Deployment_DeploymentResult[] = L"Windows.Management.Deployment.DeploymentResult";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Management_Deployment_DeploymentResult[] = {'W','i','n','d','o','w','s','.','M','a','n','a','g','e','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','R','e','s','u','l','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Management_Deployment_DeploymentResult_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Management.Deployment.PackageManager
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Management_Deployment_PackageManager_DEFINED
#define RUNTIMECLASS_Windows_Management_Deployment_PackageManager_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Management_Deployment_PackageManager[] = {'W','i','n','d','o','w','s','.','M','a','n','a','g','e','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','.','P','a','c','k','a','g','e','M','a','n','a','g','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Management_Deployment_PackageManager[] = L"Windows.Management.Deployment.PackageManager";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Management_Deployment_PackageManager[] = {'W','i','n','d','o','w','s','.','M','a','n','a','g','e','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','.','P','a','c','k','a','g','e','M','a','n','a','g','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Management_Deployment_PackageManager_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Management.Deployment.PackageUserInformation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Management_Deployment_PackageUserInformation_DEFINED
#define RUNTIMECLASS_Windows_Management_Deployment_PackageUserInformation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Management_Deployment_PackageUserInformation[] = {'W','i','n','d','o','w','s','.','M','a','n','a','g','e','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','.','P','a','c','k','a','g','e','U','s','e','r','I','n','f','o','r','m','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Management_Deployment_PackageUserInformation[] = L"Windows.Management.Deployment.PackageUserInformation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Management_Deployment_PackageUserInformation[] = {'W','i','n','d','o','w','s','.','M','a','n','a','g','e','m','e','n','t','.','D','e','p','l','o','y','m','e','n','t','.','P','a','c','k','a','g','e','U','s','e','r','I','n','f','o','r','m','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Management_Deployment_PackageUserInformation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Foundation::Uri* > interface
 */
#ifndef ____FIIterable_1_Windows__CFoundation__CUri_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CFoundation__CUri_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CFoundation__CUri, 0xb0d63b78, 0x78ad, 0x5e31, 0xb6,0xd8, 0xe3,0x2a,0x0e,0x16,0xc4,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("b0d63b78-78ad-5e31-b6d8-e32a0e16c447")
                IIterable<ABI::Windows::Foundation::Uri* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Foundation::Uri*, ABI::Windows::Foundation::IUriRuntimeClass* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CFoundation__CUri, 0xb0d63b78, 0x78ad, 0x5e31, 0xb6,0xd8, 0xe3,0x2a,0x0e,0x16,0xc4,0x47)
#endif
#else
typedef struct __FIIterable_1_Windows__CFoundation__CUriVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CFoundation__CUri *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CFoundation__CUri *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CFoundation__CUri *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CFoundation__CUri *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CFoundation__CUri *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CFoundation__CUri *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Foundation::Uri* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CFoundation__CUri *This,
        __FIIterator_1_Windows__CFoundation__CUri **value);

    END_INTERFACE
} __FIIterable_1_Windows__CFoundation__CUriVtbl;

interface __FIIterable_1_Windows__CFoundation__CUri {
    CONST_VTBL __FIIterable_1_Windows__CFoundation__CUriVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CFoundation__CUri_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CFoundation__CUri_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CFoundation__CUri_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CFoundation__CUri_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CFoundation__CUri_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CFoundation__CUri_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Foundation::Uri* > methods ***/
#define __FIIterable_1_Windows__CFoundation__CUri_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CFoundation__CUri_QueryInterface(__FIIterable_1_Windows__CFoundation__CUri* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CFoundation__CUri_AddRef(__FIIterable_1_Windows__CFoundation__CUri* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CFoundation__CUri_Release(__FIIterable_1_Windows__CFoundation__CUri* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CFoundation__CUri_GetIids(__FIIterable_1_Windows__CFoundation__CUri* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CFoundation__CUri_GetRuntimeClassName(__FIIterable_1_Windows__CFoundation__CUri* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CFoundation__CUri_GetTrustLevel(__FIIterable_1_Windows__CFoundation__CUri* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Foundation::Uri* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CFoundation__CUri_First(__FIIterable_1_Windows__CFoundation__CUri* This,__FIIterator_1_Windows__CFoundation__CUri **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_Uri IID___FIIterable_1_Windows__CFoundation__CUri
#define IIterable_UriVtbl __FIIterable_1_Windows__CFoundation__CUriVtbl
#define IIterable_Uri __FIIterable_1_Windows__CFoundation__CUri
#define IIterable_Uri_QueryInterface __FIIterable_1_Windows__CFoundation__CUri_QueryInterface
#define IIterable_Uri_AddRef __FIIterable_1_Windows__CFoundation__CUri_AddRef
#define IIterable_Uri_Release __FIIterable_1_Windows__CFoundation__CUri_Release
#define IIterable_Uri_GetIids __FIIterable_1_Windows__CFoundation__CUri_GetIids
#define IIterable_Uri_GetRuntimeClassName __FIIterable_1_Windows__CFoundation__CUri_GetRuntimeClassName
#define IIterable_Uri_GetTrustLevel __FIIterable_1_Windows__CFoundation__CUri_GetTrustLevel
#define IIterable_Uri_First __FIIterable_1_Windows__CFoundation__CUri_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CFoundation__CUri_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Foundation::Uri* > interface
 */
#ifndef ____FIIterator_1_Windows__CFoundation__CUri_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CFoundation__CUri_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CFoundation__CUri, 0x1c157d0f, 0x5efe, 0x5cec, 0xbb,0xd6, 0x0c,0x6c,0xe9,0xaf,0x07,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("1c157d0f-5efe-5cec-bbd6-0c6ce9af07a5")
                IIterator<ABI::Windows::Foundation::Uri* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Foundation::Uri*, ABI::Windows::Foundation::IUriRuntimeClass* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CFoundation__CUri, 0x1c157d0f, 0x5efe, 0x5cec, 0xbb,0xd6, 0x0c,0x6c,0xe9,0xaf,0x07,0xa5)
#endif
#else
typedef struct __FIIterator_1_Windows__CFoundation__CUriVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CFoundation__CUri *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CFoundation__CUri *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Foundation::Uri* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CFoundation__CUri *This,
        UINT32 items_size,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CFoundation__CUriVtbl;

interface __FIIterator_1_Windows__CFoundation__CUri {
    CONST_VTBL __FIIterator_1_Windows__CFoundation__CUriVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CFoundation__CUri_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CFoundation__CUri_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CFoundation__CUri_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CFoundation__CUri_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CFoundation__CUri_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CFoundation__CUri_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Foundation::Uri* > methods ***/
#define __FIIterator_1_Windows__CFoundation__CUri_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CFoundation__CUri_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CFoundation__CUri_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CFoundation__CUri_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_QueryInterface(__FIIterator_1_Windows__CFoundation__CUri* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CFoundation__CUri_AddRef(__FIIterator_1_Windows__CFoundation__CUri* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CFoundation__CUri_Release(__FIIterator_1_Windows__CFoundation__CUri* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_GetIids(__FIIterator_1_Windows__CFoundation__CUri* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_GetRuntimeClassName(__FIIterator_1_Windows__CFoundation__CUri* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_GetTrustLevel(__FIIterator_1_Windows__CFoundation__CUri* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Foundation::Uri* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_get_Current(__FIIterator_1_Windows__CFoundation__CUri* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_get_HasCurrent(__FIIterator_1_Windows__CFoundation__CUri* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_MoveNext(__FIIterator_1_Windows__CFoundation__CUri* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CFoundation__CUri_GetMany(__FIIterator_1_Windows__CFoundation__CUri* This,UINT32 items_size,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_Uri IID___FIIterator_1_Windows__CFoundation__CUri
#define IIterator_UriVtbl __FIIterator_1_Windows__CFoundation__CUriVtbl
#define IIterator_Uri __FIIterator_1_Windows__CFoundation__CUri
#define IIterator_Uri_QueryInterface __FIIterator_1_Windows__CFoundation__CUri_QueryInterface
#define IIterator_Uri_AddRef __FIIterator_1_Windows__CFoundation__CUri_AddRef
#define IIterator_Uri_Release __FIIterator_1_Windows__CFoundation__CUri_Release
#define IIterator_Uri_GetIids __FIIterator_1_Windows__CFoundation__CUri_GetIids
#define IIterator_Uri_GetRuntimeClassName __FIIterator_1_Windows__CFoundation__CUri_GetRuntimeClassName
#define IIterator_Uri_GetTrustLevel __FIIterator_1_Windows__CFoundation__CUri_GetTrustLevel
#define IIterator_Uri_get_Current __FIIterator_1_Windows__CFoundation__CUri_get_Current
#define IIterator_Uri_get_HasCurrent __FIIterator_1_Windows__CFoundation__CUri_get_HasCurrent
#define IIterator_Uri_MoveNext __FIIterator_1_Windows__CFoundation__CUri_MoveNext
#define IIterator_Uri_GetMany __FIIterator_1_Windows__CFoundation__CUri_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CFoundation__CUri_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::ApplicationModel::Package* > interface
 */
#ifndef ____FIIterable_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CApplicationModel__CPackage, 0x69ad6aa7, 0x0c49, 0x5f27, 0xa5,0xeb, 0xef,0x4d,0x59,0x46,0x7b,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("69ad6aa7-0c49-5f27-a5eb-ef4d59467b6d")
                IIterable<ABI::Windows::ApplicationModel::Package* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Package*, ABI::Windows::ApplicationModel::IPackage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CApplicationModel__CPackage, 0x69ad6aa7, 0x0c49, 0x5f27, 0xa5,0xeb, 0xef,0x4d,0x59,0x46,0x7b,0x6d)
#endif
#else
typedef struct __FIIterable_1_Windows__CApplicationModel__CPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::ApplicationModel::Package* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        __FIIterator_1_Windows__CApplicationModel__CPackage **value);

    END_INTERFACE
} __FIIterable_1_Windows__CApplicationModel__CPackageVtbl;

interface __FIIterable_1_Windows__CApplicationModel__CPackage {
    CONST_VTBL __FIIterable_1_Windows__CApplicationModel__CPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CPackage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::ApplicationModel::Package* > methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CPackage_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_QueryInterface(__FIIterable_1_Windows__CApplicationModel__CPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CApplicationModel__CPackage_AddRef(__FIIterable_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CApplicationModel__CPackage_Release(__FIIterable_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_GetIids(__FIIterable_1_Windows__CApplicationModel__CPackage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(__FIIterable_1_Windows__CApplicationModel__CPackage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_GetTrustLevel(__FIIterable_1_Windows__CApplicationModel__CPackage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::ApplicationModel::Package* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_First(__FIIterable_1_Windows__CApplicationModel__CPackage* This,__FIIterator_1_Windows__CApplicationModel__CPackage **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_Package IID___FIIterable_1_Windows__CApplicationModel__CPackage
#define IIterable_PackageVtbl __FIIterable_1_Windows__CApplicationModel__CPackageVtbl
#define IIterable_Package __FIIterable_1_Windows__CApplicationModel__CPackage
#define IIterable_Package_QueryInterface __FIIterable_1_Windows__CApplicationModel__CPackage_QueryInterface
#define IIterable_Package_AddRef __FIIterable_1_Windows__CApplicationModel__CPackage_AddRef
#define IIterable_Package_Release __FIIterable_1_Windows__CApplicationModel__CPackage_Release
#define IIterable_Package_GetIids __FIIterable_1_Windows__CApplicationModel__CPackage_GetIids
#define IIterable_Package_GetRuntimeClassName __FIIterable_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName
#define IIterable_Package_GetTrustLevel __FIIterable_1_Windows__CApplicationModel__CPackage_GetTrustLevel
#define IIterable_Package_First __FIIterable_1_Windows__CApplicationModel__CPackage_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::ApplicationModel::Package* > interface
 */
#ifndef ____FIIterator_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CApplicationModel__CPackage, 0x0217f069, 0x025c, 0x5ee6, 0xa8,0x7f, 0xe7,0x82,0xe3,0xb6,0x23,0xae);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0217f069-025c-5ee6-a87f-e782e3b623ae")
                IIterator<ABI::Windows::ApplicationModel::Package* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Package*, ABI::Windows::ApplicationModel::IPackage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CApplicationModel__CPackage, 0x0217f069, 0x025c, 0x5ee6, 0xa8,0x7f, 0xe7,0x82,0xe3,0xb6,0x23,0xae)
#endif
#else
typedef struct __FIIterator_1_Windows__CApplicationModel__CPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::ApplicationModel::Package* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        __x_ABI_CWindows_CApplicationModel_CIPackage **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        UINT32 items_size,
        __x_ABI_CWindows_CApplicationModel_CIPackage **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CApplicationModel__CPackageVtbl;

interface __FIIterator_1_Windows__CApplicationModel__CPackage {
    CONST_VTBL __FIIterator_1_Windows__CApplicationModel__CPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::ApplicationModel::Package* > methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CPackage_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_QueryInterface(__FIIterator_1_Windows__CApplicationModel__CPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CApplicationModel__CPackage_AddRef(__FIIterator_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CApplicationModel__CPackage_Release(__FIIterator_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetIids(__FIIterator_1_Windows__CApplicationModel__CPackage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(__FIIterator_1_Windows__CApplicationModel__CPackage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetTrustLevel(__FIIterator_1_Windows__CApplicationModel__CPackage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::ApplicationModel::Package* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_get_Current(__FIIterator_1_Windows__CApplicationModel__CPackage* This,__x_ABI_CWindows_CApplicationModel_CIPackage **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_get_HasCurrent(__FIIterator_1_Windows__CApplicationModel__CPackage* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_MoveNext(__FIIterator_1_Windows__CApplicationModel__CPackage* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetMany(__FIIterator_1_Windows__CApplicationModel__CPackage* This,UINT32 items_size,__x_ABI_CWindows_CApplicationModel_CIPackage **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_Package IID___FIIterator_1_Windows__CApplicationModel__CPackage
#define IIterator_PackageVtbl __FIIterator_1_Windows__CApplicationModel__CPackageVtbl
#define IIterator_Package __FIIterator_1_Windows__CApplicationModel__CPackage
#define IIterator_Package_QueryInterface __FIIterator_1_Windows__CApplicationModel__CPackage_QueryInterface
#define IIterator_Package_AddRef __FIIterator_1_Windows__CApplicationModel__CPackage_AddRef
#define IIterator_Package_Release __FIIterator_1_Windows__CApplicationModel__CPackage_Release
#define IIterator_Package_GetIids __FIIterator_1_Windows__CApplicationModel__CPackage_GetIids
#define IIterator_Package_GetRuntimeClassName __FIIterator_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName
#define IIterator_Package_GetTrustLevel __FIIterator_1_Windows__CApplicationModel__CPackage_GetTrustLevel
#define IIterator_Package_get_Current __FIIterator_1_Windows__CApplicationModel__CPackage_get_Current
#define IIterator_Package_get_HasCurrent __FIIterator_1_Windows__CApplicationModel__CPackage_get_HasCurrent
#define IIterator_Package_MoveNext __FIIterator_1_Windows__CApplicationModel__CPackage_MoveNext
#define IIterator_Package_GetMany __FIIterator_1_Windows__CApplicationModel__CPackage_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* > interface
 */
#ifndef ____FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation, 0x341348b9, 0x52c8, 0x5b57, 0x9e,0x91, 0xf1,0x9f,0x2a,0x05,0xb1,0x88);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("341348b9-52c8-5b57-9e91-f19f2a05b188")
                IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Management::Deployment::PackageUserInformation*, ABI::Windows::Management::Deployment::IPackageUserInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation, 0x341348b9, 0x52c8, 0x5b57, 0x9e,0x91, 0xf1,0x9f,0x2a,0x05,0xb1,0x88)
#endif
#else
typedef struct __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation **value);

    END_INTERFACE
} __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl;

interface __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation {
    CONST_VTBL __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* > methods ***/
#define __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_QueryInterface(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_AddRef(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_Release(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetIids(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetRuntimeClassName(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetTrustLevel(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Management::Deployment::PackageUserInformation* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_First(__FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_PackageUserInformation IID___FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation
#define IIterable_PackageUserInformationVtbl __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl
#define IIterable_PackageUserInformation __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation
#define IIterable_PackageUserInformation_QueryInterface __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_QueryInterface
#define IIterable_PackageUserInformation_AddRef __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_AddRef
#define IIterable_PackageUserInformation_Release __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_Release
#define IIterable_PackageUserInformation_GetIids __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetIids
#define IIterable_PackageUserInformation_GetRuntimeClassName __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetRuntimeClassName
#define IIterable_PackageUserInformation_GetTrustLevel __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetTrustLevel
#define IIterable_PackageUserInformation_First __FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CManagement__CDeployment__CPackageUserInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Management::Deployment::PackageUserInformation* > interface
 */
#ifndef ____FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation, 0x75660566, 0xae43, 0x5858, 0xad,0xa6, 0xd5,0x7d,0xda,0xe9,0x02,0x77);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("75660566-ae43-5858-ada6-d57ddae90277")
                IIterator<ABI::Windows::Management::Deployment::PackageUserInformation* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Management::Deployment::PackageUserInformation*, ABI::Windows::Management::Deployment::IPackageUserInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation, 0x75660566, 0xae43, 0x5858, 0xad,0xa6, 0xd5,0x7d,0xda,0xe9,0x02,0x77)
#endif
#else
typedef struct __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Management::Deployment::PackageUserInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation *This,
        UINT32 items_size,
        __x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl;

interface __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation {
    CONST_VTBL __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Management::Deployment::PackageUserInformation* > methods ***/
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_QueryInterface(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_AddRef(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_Release(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetIids(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetRuntimeClassName(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetTrustLevel(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Management::Deployment::PackageUserInformation* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_get_Current(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_get_HasCurrent(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_MoveNext(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetMany(__FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation* This,UINT32 items_size,__x_ABI_CWindows_CManagement_CDeployment_CIPackageUserInformation **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_PackageUserInformation IID___FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation
#define IIterator_PackageUserInformationVtbl __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformationVtbl
#define IIterator_PackageUserInformation __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation
#define IIterator_PackageUserInformation_QueryInterface __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_QueryInterface
#define IIterator_PackageUserInformation_AddRef __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_AddRef
#define IIterator_PackageUserInformation_Release __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_Release
#define IIterator_PackageUserInformation_GetIids __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetIids
#define IIterator_PackageUserInformation_GetRuntimeClassName __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetRuntimeClassName
#define IIterator_PackageUserInformation_GetTrustLevel __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetTrustLevel
#define IIterator_PackageUserInformation_get_Current __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_get_Current
#define IIterator_PackageUserInformation_get_HasCurrent __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_get_HasCurrent
#define IIterator_PackageUserInformation_MoveNext __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_MoveNext
#define IIterator_PackageUserInformation_GetMany __FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CManagement__CDeployment__CPackageUserInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > interface
 */
#ifndef ____FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__
#define ____FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress, 0x5a97aab7, 0xb6ea, 0x55ac, 0xa5,0xdc, 0xd5,0xb1,0x64,0xd9,0x4e,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5a97aab7-b6ea-55ac-a5dc-d5b164d94e94")
            IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > : IAsyncOperationWithProgress_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Management::Deployment::DeploymentResult*, ABI::Windows::Management::Deployment::IDeploymentResult* >, ABI::Windows::Management::Deployment::DeploymentProgress >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress, 0x5a97aab7, 0xb6ea, 0x55ac, 0xa5,0xdc, 0xd5,0xb1,0x64,0xd9,0x4e,0x94)
#endif
#else
typedef struct __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Progress)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *handler);

    HRESULT (STDMETHODCALLTYPE *get_Progress)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **handler);

    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        __x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult **results);

    END_INTERFACE
} __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl;

interface __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress {
    CONST_VTBL __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_put_Progress(This,handler) (This)->lpVtbl->put_Progress(This,handler)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_get_Progress(This,handler) (This)->lpVtbl->get_Progress(This,handler)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetIids(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetRuntimeClassName(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetTrustLevel(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperationWithProgress<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_put_Progress(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,__FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *handler) {
    return This->lpVtbl->put_Progress(This,handler);
}
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_get_Progress(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,__FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **handler) {
    return This->lpVtbl->get_Progress(This,handler);
}
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_put_Completed(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,__FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_get_Completed(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,__FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetResults(__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,__x_ABI_CWindows_CManagement_CDeployment_CIDeploymentResult **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress IID___FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgressVtbl __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_QueryInterface __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_AddRef __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_Release __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_GetIids __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetIids
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_GetRuntimeClassName __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetRuntimeClassName
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_GetTrustLevel __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetTrustLevel
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_put_Progress __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_put_Progress
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_get_Progress __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_get_Progress
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_put_Completed __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_put_Completed
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_get_Completed __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_get_Completed
#define IAsyncOperationWithProgress_DeploymentResult_DeploymentProgress_GetResults __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationProgressHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > interface
 */
#ifndef ____FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__
#define ____FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress, 0xf1b926d1, 0x1796, 0x597a, 0x9b,0xea, 0x6c,0x64,0x49,0xd0,0x3e,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("f1b926d1-1796-597a-9bea-6c6449d03eef")
            IAsyncOperationProgressHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > : IAsyncOperationProgressHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Management::Deployment::DeploymentResult*, ABI::Windows::Management::Deployment::IDeploymentResult* >, ABI::Windows::Management::Deployment::DeploymentProgress >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress, 0xf1b926d1, 0x1796, 0x597a, 0x9b,0xea, 0x6c,0x64,0x49,0xd0,0x3e,0xef)
#endif
#else
typedef struct __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This);

    /*** IAsyncOperationProgressHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *info,
        __x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgress progress);

    END_INTERFACE
} __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl;

interface __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress {
    CONST_VTBL __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationProgressHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
#define __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Invoke(This,info,progress) (This)->lpVtbl->Invoke(This,info,progress)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface(__FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef(__FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release(__FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationProgressHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
static inline HRESULT __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Invoke(__FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *info,__x_ABI_CWindows_CManagement_CDeployment_CDeploymentProgress progress) {
    return This->lpVtbl->Invoke(This,info,progress);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationProgressHandler_DeploymentResult_DeploymentProgress IID___FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress
#define IAsyncOperationProgressHandler_DeploymentResult_DeploymentProgressVtbl __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl
#define IAsyncOperationProgressHandler_DeploymentResult_DeploymentProgress __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress
#define IAsyncOperationProgressHandler_DeploymentResult_DeploymentProgress_QueryInterface __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface
#define IAsyncOperationProgressHandler_DeploymentResult_DeploymentProgress_AddRef __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef
#define IAsyncOperationProgressHandler_DeploymentResult_DeploymentProgress_Release __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release
#define IAsyncOperationProgressHandler_DeploymentResult_DeploymentProgress_Invoke __FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationProgressHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationWithProgressCompletedHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > interface
 */
#ifndef ____FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__
#define ____FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress, 0x6e1c7129, 0x61e0, 0x5d88, 0x9f,0xd4, 0xf3,0xce,0x65,0xa0,0x57,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("6e1c7129-61e0-5d88-9fd4-f3ce65a05719")
            IAsyncOperationWithProgressCompletedHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > : IAsyncOperationWithProgressCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Management::Deployment::DeploymentResult*, ABI::Windows::Management::Deployment::IDeploymentResult* >, ABI::Windows::Management::Deployment::DeploymentProgress >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress, 0x6e1c7129, 0x61e0, 0x5d88, 0x9f,0xd4, 0xf3,0xce,0x65,0xa0,0x57,0x19)
#endif
#else
typedef struct __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This);

    /*** IAsyncOperationWithProgressCompletedHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *This,
        __FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl;

interface __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress {
    CONST_VTBL __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationWithProgressCompletedHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
#define __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface(__FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef(__FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release(__FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationWithProgressCompletedHandler<ABI::Windows::Management::Deployment::DeploymentResult*,ABI::Windows::Management::Deployment::DeploymentProgress > methods ***/
static inline HRESULT __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Invoke(__FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress* This,__FIAsyncOperationWithProgress_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationWithProgressCompletedHandler_DeploymentResult_DeploymentProgress IID___FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress
#define IAsyncOperationWithProgressCompletedHandler_DeploymentResult_DeploymentProgressVtbl __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgressVtbl
#define IAsyncOperationWithProgressCompletedHandler_DeploymentResult_DeploymentProgress __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress
#define IAsyncOperationWithProgressCompletedHandler_DeploymentResult_DeploymentProgress_QueryInterface __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_QueryInterface
#define IAsyncOperationWithProgressCompletedHandler_DeploymentResult_DeploymentProgress_AddRef __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_AddRef
#define IAsyncOperationWithProgressCompletedHandler_DeploymentResult_DeploymentProgress_Release __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Release
#define IAsyncOperationWithProgressCompletedHandler_DeploymentResult_DeploymentProgress_Invoke __FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationWithProgressCompletedHandler_2_Windows__CManagement__CDeployment__CDeploymentResult_DeploymentProgress_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_management_deployment_h__ */
