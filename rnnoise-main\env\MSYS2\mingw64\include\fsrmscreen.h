/*** Autogenerated by WIDL 10.12 from include/fsrmscreen.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __fsrmscreen_h__
#define __fsrmscreen_h__

/* Forward declarations */

#ifndef __IFsrmFileGroupManager_FWD_DEFINED__
#define __IFsrmFileGroupManager_FWD_DEFINED__
typedef interface IFsrmFileGroupManager IFsrmFileGroupManager;
#ifdef __cplusplus
interface IFsrmFileGroupManager;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenManager_FWD_DEFINED__
#define __IFsrmFileScreenManager_FWD_DEFINED__
typedef interface IFsrmFileScreenManager IFsrmFileScreenManager;
#ifdef __cplusplus
interface IFsrmFileScreenManager;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenTemplateManager_FWD_DEFINED__
#define __IFsrmFileScreenTemplateManager_FWD_DEFINED__
typedef interface IFsrmFileScreenTemplateManager IFsrmFileScreenTemplateManager;
#ifdef __cplusplus
interface IFsrmFileScreenTemplateManager;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileGroup_FWD_DEFINED__
#define __IFsrmFileGroup_FWD_DEFINED__
typedef interface IFsrmFileGroup IFsrmFileGroup;
#ifdef __cplusplus
interface IFsrmFileGroup;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenBase_FWD_DEFINED__
#define __IFsrmFileScreenBase_FWD_DEFINED__
typedef interface IFsrmFileScreenBase IFsrmFileScreenBase;
#ifdef __cplusplus
interface IFsrmFileScreenBase;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenException_FWD_DEFINED__
#define __IFsrmFileScreenException_FWD_DEFINED__
typedef interface IFsrmFileScreenException IFsrmFileScreenException;
#ifdef __cplusplus
interface IFsrmFileScreenException;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreen_FWD_DEFINED__
#define __IFsrmFileScreen_FWD_DEFINED__
typedef interface IFsrmFileScreen IFsrmFileScreen;
#ifdef __cplusplus
interface IFsrmFileScreen;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileGroupImported_FWD_DEFINED__
#define __IFsrmFileGroupImported_FWD_DEFINED__
typedef interface IFsrmFileGroupImported IFsrmFileGroupImported;
#ifdef __cplusplus
interface IFsrmFileGroupImported;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenTemplate_FWD_DEFINED__
#define __IFsrmFileScreenTemplate_FWD_DEFINED__
typedef interface IFsrmFileScreenTemplate IFsrmFileScreenTemplate;
#ifdef __cplusplus
interface IFsrmFileScreenTemplate;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenTemplateImported_FWD_DEFINED__
#define __IFsrmFileScreenTemplateImported_FWD_DEFINED__
typedef interface IFsrmFileScreenTemplateImported IFsrmFileScreenTemplateImported;
#ifdef __cplusplus
interface IFsrmFileScreenTemplateImported;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <fsrmenums.h>
#include <fsrm.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IFsrmFileGroup_FWD_DEFINED__
#define __IFsrmFileGroup_FWD_DEFINED__
typedef interface IFsrmFileGroup IFsrmFileGroup;
#ifdef __cplusplus
interface IFsrmFileGroup;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileGroupImported_FWD_DEFINED__
#define __IFsrmFileGroupImported_FWD_DEFINED__
typedef interface IFsrmFileGroupImported IFsrmFileGroupImported;
#ifdef __cplusplus
interface IFsrmFileGroupImported;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileGroupManager_FWD_DEFINED__
#define __IFsrmFileGroupManager_FWD_DEFINED__
typedef interface IFsrmFileGroupManager IFsrmFileGroupManager;
#ifdef __cplusplus
interface IFsrmFileGroupManager;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreen_FWD_DEFINED__
#define __IFsrmFileScreen_FWD_DEFINED__
typedef interface IFsrmFileScreen IFsrmFileScreen;
#ifdef __cplusplus
interface IFsrmFileScreen;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenBase_FWD_DEFINED__
#define __IFsrmFileScreenBase_FWD_DEFINED__
typedef interface IFsrmFileScreenBase IFsrmFileScreenBase;
#ifdef __cplusplus
interface IFsrmFileScreenBase;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenException_FWD_DEFINED__
#define __IFsrmFileScreenException_FWD_DEFINED__
typedef interface IFsrmFileScreenException IFsrmFileScreenException;
#ifdef __cplusplus
interface IFsrmFileScreenException;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenManager_FWD_DEFINED__
#define __IFsrmFileScreenManager_FWD_DEFINED__
typedef interface IFsrmFileScreenManager IFsrmFileScreenManager;
#ifdef __cplusplus
interface IFsrmFileScreenManager;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenTemplate_FWD_DEFINED__
#define __IFsrmFileScreenTemplate_FWD_DEFINED__
typedef interface IFsrmFileScreenTemplate IFsrmFileScreenTemplate;
#ifdef __cplusplus
interface IFsrmFileScreenTemplate;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenTemplateImported_FWD_DEFINED__
#define __IFsrmFileScreenTemplateImported_FWD_DEFINED__
typedef interface IFsrmFileScreenTemplateImported IFsrmFileScreenTemplateImported;
#ifdef __cplusplus
interface IFsrmFileScreenTemplateImported;
#endif /* __cplusplus */
#endif

#ifndef __IFsrmFileScreenTemplateManager_FWD_DEFINED__
#define __IFsrmFileScreenTemplateManager_FWD_DEFINED__
typedef interface IFsrmFileScreenTemplateManager IFsrmFileScreenTemplateManager;
#ifdef __cplusplus
interface IFsrmFileScreenTemplateManager;
#endif /* __cplusplus */
#endif

#define FSRM_DISPID_FILEGROUP (FSRM_DISPID_FEATURE_FILESCREEN | 0x100000)

#define FSRM_DISPID_FILEGROUP_IMPORTED (FSRM_DISPID_FILEGROUP | 0x10000)

#define FSRM_DISPID_FILEGROUP_MANAGER (FSRM_DISPID_FEATURE_FILESCREEN | 0x200000)

#define FSRM_DISPID_FILESCREEN_BASE (FSRM_DISPID_FEATURE_FILESCREEN | 0x300000)

#define FSRM_DISPID_FILESCREEN (FSRM_DISPID_FILESCREEN_BASE | 0x10000)

#define FSRM_DISPID_FILESCREEN_TEMPLATE (FSRM_DISPID_FILESCREEN_BASE | 0x20000)

#define FSRM_DISPID_FILESCREEN_TEMPLATE_IMPORTED (FSRM_DISPID_FILESCREEN_TEMPLATE | 0x1000)

#define FSRM_DISPID_FILESCREEN_EXCEPTION (FSRM_DISPID_FEATURE_FILESCREEN | 0x400000)

#define FSRM_DISPID_FILESCREEN_MANAGER (FSRM_DISPID_FEATURE_FILESCREEN | 0x500000)

#define FSRM_DISPID_FILESCREEN_TEMPLATE_MANAGER (FSRM_DISPID_FEATURE_FILESCREEN | 0x600000)

/*****************************************************************************
 * IFsrmFileGroupManager interface
 */
#ifndef __IFsrmFileGroupManager_INTERFACE_DEFINED__
#define __IFsrmFileGroupManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileGroupManager, 0x426677d5, 0x018c, 0x485c, 0x8a,0x51, 0x20,0xb8,0x6d,0x00,0xbd,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("426677d5-018c-485c-8a51-20b86d00bdc4")
IFsrmFileGroupManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE CreateFileGroup(
        IFsrmFileGroup **fileGroup) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileGroup(
        BSTR name,
        IFsrmFileGroup **fileGroup) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumFileGroups(
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileGroups) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExportFileGroups(
        VARIANT *fileGroupNamesArray,
        BSTR *serializedFileGroups) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportFileGroups(
        BSTR serializedFileGroups,
        VARIANT *fileGroupNamesArray,
        IFsrmCommittableCollection **fileGroups) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileGroupManager, 0x426677d5, 0x018c, 0x485c, 0x8a,0x51, 0x20,0xb8,0x6d,0x00,0xbd,0xc4)
#endif
#else
typedef struct IFsrmFileGroupManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileGroupManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileGroupManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileGroupManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileGroupManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileGroupManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileGroupManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileGroupManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmFileGroupManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFileGroup)(
        IFsrmFileGroupManager *This,
        IFsrmFileGroup **fileGroup);

    HRESULT (STDMETHODCALLTYPE *GetFileGroup)(
        IFsrmFileGroupManager *This,
        BSTR name,
        IFsrmFileGroup **fileGroup);

    HRESULT (STDMETHODCALLTYPE *EnumFileGroups)(
        IFsrmFileGroupManager *This,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileGroups);

    HRESULT (STDMETHODCALLTYPE *ExportFileGroups)(
        IFsrmFileGroupManager *This,
        VARIANT *fileGroupNamesArray,
        BSTR *serializedFileGroups);

    HRESULT (STDMETHODCALLTYPE *ImportFileGroups)(
        IFsrmFileGroupManager *This,
        BSTR serializedFileGroups,
        VARIANT *fileGroupNamesArray,
        IFsrmCommittableCollection **fileGroups);

    END_INTERFACE
} IFsrmFileGroupManagerVtbl;

interface IFsrmFileGroupManager {
    CONST_VTBL IFsrmFileGroupManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileGroupManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileGroupManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileGroupManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileGroupManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileGroupManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileGroupManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileGroupManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmFileGroupManager methods ***/
#define IFsrmFileGroupManager_CreateFileGroup(This,fileGroup) (This)->lpVtbl->CreateFileGroup(This,fileGroup)
#define IFsrmFileGroupManager_GetFileGroup(This,name,fileGroup) (This)->lpVtbl->GetFileGroup(This,name,fileGroup)
#define IFsrmFileGroupManager_EnumFileGroups(This,options,fileGroups) (This)->lpVtbl->EnumFileGroups(This,options,fileGroups)
#define IFsrmFileGroupManager_ExportFileGroups(This,fileGroupNamesArray,serializedFileGroups) (This)->lpVtbl->ExportFileGroups(This,fileGroupNamesArray,serializedFileGroups)
#define IFsrmFileGroupManager_ImportFileGroups(This,serializedFileGroups,fileGroupNamesArray,fileGroups) (This)->lpVtbl->ImportFileGroups(This,serializedFileGroups,fileGroupNamesArray,fileGroups)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileGroupManager_QueryInterface(IFsrmFileGroupManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileGroupManager_AddRef(IFsrmFileGroupManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileGroupManager_Release(IFsrmFileGroupManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileGroupManager_GetTypeInfoCount(IFsrmFileGroupManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileGroupManager_GetTypeInfo(IFsrmFileGroupManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileGroupManager_GetIDsOfNames(IFsrmFileGroupManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileGroupManager_Invoke(IFsrmFileGroupManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmFileGroupManager methods ***/
static inline HRESULT IFsrmFileGroupManager_CreateFileGroup(IFsrmFileGroupManager* This,IFsrmFileGroup **fileGroup) {
    return This->lpVtbl->CreateFileGroup(This,fileGroup);
}
static inline HRESULT IFsrmFileGroupManager_GetFileGroup(IFsrmFileGroupManager* This,BSTR name,IFsrmFileGroup **fileGroup) {
    return This->lpVtbl->GetFileGroup(This,name,fileGroup);
}
static inline HRESULT IFsrmFileGroupManager_EnumFileGroups(IFsrmFileGroupManager* This,FsrmEnumOptions options,IFsrmCommittableCollection **fileGroups) {
    return This->lpVtbl->EnumFileGroups(This,options,fileGroups);
}
static inline HRESULT IFsrmFileGroupManager_ExportFileGroups(IFsrmFileGroupManager* This,VARIANT *fileGroupNamesArray,BSTR *serializedFileGroups) {
    return This->lpVtbl->ExportFileGroups(This,fileGroupNamesArray,serializedFileGroups);
}
static inline HRESULT IFsrmFileGroupManager_ImportFileGroups(IFsrmFileGroupManager* This,BSTR serializedFileGroups,VARIANT *fileGroupNamesArray,IFsrmCommittableCollection **fileGroups) {
    return This->lpVtbl->ImportFileGroups(This,serializedFileGroups,fileGroupNamesArray,fileGroups);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileGroupManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileScreenManager interface
 */
#ifndef __IFsrmFileScreenManager_INTERFACE_DEFINED__
#define __IFsrmFileScreenManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileScreenManager, 0xff4fa04e, 0x5a94, 0x4bda, 0xa3,0xa0, 0xd5,0xb4,0xd3,0xc5,0x2e,0xba);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ff4fa04e-5a94-4bda-a3a0-d5b4d3c52eba")
IFsrmFileScreenManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ActionVariables(
        SAFEARRAY **variables) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ActionVariableDescriptions(
        SAFEARRAY **descriptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFileScreen(
        BSTR path,
        IFsrmFileScreen **fileScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileScreen(
        BSTR path,
        IFsrmFileScreen **fileScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumFileScreens(
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileScreens) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFileScreenException(
        BSTR path,
        IFsrmFileScreenException **fileScreenException) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileScreenException(
        BSTR path,
        IFsrmFileScreenException **fileScreenException) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumFileScreenExceptions(
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileScreenExceptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFileScreenCollection(
        IFsrmCommittableCollection **collection) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileScreenManager, 0xff4fa04e, 0x5a94, 0x4bda, 0xa3,0xa0, 0xd5,0xb4,0xd3,0xc5,0x2e,0xba)
#endif
#else
typedef struct IFsrmFileScreenManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileScreenManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileScreenManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileScreenManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileScreenManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileScreenManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileScreenManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileScreenManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmFileScreenManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ActionVariables)(
        IFsrmFileScreenManager *This,
        SAFEARRAY **variables);

    HRESULT (STDMETHODCALLTYPE *get_ActionVariableDescriptions)(
        IFsrmFileScreenManager *This,
        SAFEARRAY **descriptions);

    HRESULT (STDMETHODCALLTYPE *CreateFileScreen)(
        IFsrmFileScreenManager *This,
        BSTR path,
        IFsrmFileScreen **fileScreen);

    HRESULT (STDMETHODCALLTYPE *GetFileScreen)(
        IFsrmFileScreenManager *This,
        BSTR path,
        IFsrmFileScreen **fileScreen);

    HRESULT (STDMETHODCALLTYPE *EnumFileScreens)(
        IFsrmFileScreenManager *This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileScreens);

    HRESULT (STDMETHODCALLTYPE *CreateFileScreenException)(
        IFsrmFileScreenManager *This,
        BSTR path,
        IFsrmFileScreenException **fileScreenException);

    HRESULT (STDMETHODCALLTYPE *GetFileScreenException)(
        IFsrmFileScreenManager *This,
        BSTR path,
        IFsrmFileScreenException **fileScreenException);

    HRESULT (STDMETHODCALLTYPE *EnumFileScreenExceptions)(
        IFsrmFileScreenManager *This,
        BSTR path,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileScreenExceptions);

    HRESULT (STDMETHODCALLTYPE *CreateFileScreenCollection)(
        IFsrmFileScreenManager *This,
        IFsrmCommittableCollection **collection);

    END_INTERFACE
} IFsrmFileScreenManagerVtbl;

interface IFsrmFileScreenManager {
    CONST_VTBL IFsrmFileScreenManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileScreenManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileScreenManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileScreenManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileScreenManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileScreenManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileScreenManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileScreenManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmFileScreenManager methods ***/
#define IFsrmFileScreenManager_get_ActionVariables(This,variables) (This)->lpVtbl->get_ActionVariables(This,variables)
#define IFsrmFileScreenManager_get_ActionVariableDescriptions(This,descriptions) (This)->lpVtbl->get_ActionVariableDescriptions(This,descriptions)
#define IFsrmFileScreenManager_CreateFileScreen(This,path,fileScreen) (This)->lpVtbl->CreateFileScreen(This,path,fileScreen)
#define IFsrmFileScreenManager_GetFileScreen(This,path,fileScreen) (This)->lpVtbl->GetFileScreen(This,path,fileScreen)
#define IFsrmFileScreenManager_EnumFileScreens(This,path,options,fileScreens) (This)->lpVtbl->EnumFileScreens(This,path,options,fileScreens)
#define IFsrmFileScreenManager_CreateFileScreenException(This,path,fileScreenException) (This)->lpVtbl->CreateFileScreenException(This,path,fileScreenException)
#define IFsrmFileScreenManager_GetFileScreenException(This,path,fileScreenException) (This)->lpVtbl->GetFileScreenException(This,path,fileScreenException)
#define IFsrmFileScreenManager_EnumFileScreenExceptions(This,path,options,fileScreenExceptions) (This)->lpVtbl->EnumFileScreenExceptions(This,path,options,fileScreenExceptions)
#define IFsrmFileScreenManager_CreateFileScreenCollection(This,collection) (This)->lpVtbl->CreateFileScreenCollection(This,collection)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileScreenManager_QueryInterface(IFsrmFileScreenManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileScreenManager_AddRef(IFsrmFileScreenManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileScreenManager_Release(IFsrmFileScreenManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileScreenManager_GetTypeInfoCount(IFsrmFileScreenManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileScreenManager_GetTypeInfo(IFsrmFileScreenManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileScreenManager_GetIDsOfNames(IFsrmFileScreenManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileScreenManager_Invoke(IFsrmFileScreenManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmFileScreenManager methods ***/
static inline HRESULT IFsrmFileScreenManager_get_ActionVariables(IFsrmFileScreenManager* This,SAFEARRAY **variables) {
    return This->lpVtbl->get_ActionVariables(This,variables);
}
static inline HRESULT IFsrmFileScreenManager_get_ActionVariableDescriptions(IFsrmFileScreenManager* This,SAFEARRAY **descriptions) {
    return This->lpVtbl->get_ActionVariableDescriptions(This,descriptions);
}
static inline HRESULT IFsrmFileScreenManager_CreateFileScreen(IFsrmFileScreenManager* This,BSTR path,IFsrmFileScreen **fileScreen) {
    return This->lpVtbl->CreateFileScreen(This,path,fileScreen);
}
static inline HRESULT IFsrmFileScreenManager_GetFileScreen(IFsrmFileScreenManager* This,BSTR path,IFsrmFileScreen **fileScreen) {
    return This->lpVtbl->GetFileScreen(This,path,fileScreen);
}
static inline HRESULT IFsrmFileScreenManager_EnumFileScreens(IFsrmFileScreenManager* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **fileScreens) {
    return This->lpVtbl->EnumFileScreens(This,path,options,fileScreens);
}
static inline HRESULT IFsrmFileScreenManager_CreateFileScreenException(IFsrmFileScreenManager* This,BSTR path,IFsrmFileScreenException **fileScreenException) {
    return This->lpVtbl->CreateFileScreenException(This,path,fileScreenException);
}
static inline HRESULT IFsrmFileScreenManager_GetFileScreenException(IFsrmFileScreenManager* This,BSTR path,IFsrmFileScreenException **fileScreenException) {
    return This->lpVtbl->GetFileScreenException(This,path,fileScreenException);
}
static inline HRESULT IFsrmFileScreenManager_EnumFileScreenExceptions(IFsrmFileScreenManager* This,BSTR path,FsrmEnumOptions options,IFsrmCommittableCollection **fileScreenExceptions) {
    return This->lpVtbl->EnumFileScreenExceptions(This,path,options,fileScreenExceptions);
}
static inline HRESULT IFsrmFileScreenManager_CreateFileScreenCollection(IFsrmFileScreenManager* This,IFsrmCommittableCollection **collection) {
    return This->lpVtbl->CreateFileScreenCollection(This,collection);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileScreenManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileScreenTemplateManager interface
 */
#ifndef __IFsrmFileScreenTemplateManager_INTERFACE_DEFINED__
#define __IFsrmFileScreenTemplateManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileScreenTemplateManager, 0xcfe36cba, 0x1949, 0x4e74, 0xa1,0x4f, 0xf1,0xd5,0x80,0xce,0xaf,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cfe36cba-1949-4e74-a14f-f1d580ceaf13")
IFsrmFileScreenTemplateManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE CreateTemplate(
        IFsrmFileScreenTemplate **fileScreenTemplate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTemplate(
        BSTR name,
        IFsrmFileScreenTemplate **fileScreenTemplate) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumTemplates(
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileScreenTemplates) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExportTemplates(
        VARIANT *fileScreenTemplateNamesArray,
        BSTR *serializedFileScreenTemplates) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportTemplates(
        BSTR serializedFileScreenTemplates,
        VARIANT *fileScreenTemplateNamesArray,
        IFsrmCommittableCollection **fileScreenTemplates) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileScreenTemplateManager, 0xcfe36cba, 0x1949, 0x4e74, 0xa1,0x4f, 0xf1,0xd5,0x80,0xce,0xaf,0x13)
#endif
#else
typedef struct IFsrmFileScreenTemplateManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileScreenTemplateManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileScreenTemplateManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileScreenTemplateManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileScreenTemplateManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileScreenTemplateManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileScreenTemplateManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileScreenTemplateManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmFileScreenTemplateManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTemplate)(
        IFsrmFileScreenTemplateManager *This,
        IFsrmFileScreenTemplate **fileScreenTemplate);

    HRESULT (STDMETHODCALLTYPE *GetTemplate)(
        IFsrmFileScreenTemplateManager *This,
        BSTR name,
        IFsrmFileScreenTemplate **fileScreenTemplate);

    HRESULT (STDMETHODCALLTYPE *EnumTemplates)(
        IFsrmFileScreenTemplateManager *This,
        FsrmEnumOptions options,
        IFsrmCommittableCollection **fileScreenTemplates);

    HRESULT (STDMETHODCALLTYPE *ExportTemplates)(
        IFsrmFileScreenTemplateManager *This,
        VARIANT *fileScreenTemplateNamesArray,
        BSTR *serializedFileScreenTemplates);

    HRESULT (STDMETHODCALLTYPE *ImportTemplates)(
        IFsrmFileScreenTemplateManager *This,
        BSTR serializedFileScreenTemplates,
        VARIANT *fileScreenTemplateNamesArray,
        IFsrmCommittableCollection **fileScreenTemplates);

    END_INTERFACE
} IFsrmFileScreenTemplateManagerVtbl;

interface IFsrmFileScreenTemplateManager {
    CONST_VTBL IFsrmFileScreenTemplateManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileScreenTemplateManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileScreenTemplateManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileScreenTemplateManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileScreenTemplateManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileScreenTemplateManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileScreenTemplateManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileScreenTemplateManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmFileScreenTemplateManager methods ***/
#define IFsrmFileScreenTemplateManager_CreateTemplate(This,fileScreenTemplate) (This)->lpVtbl->CreateTemplate(This,fileScreenTemplate)
#define IFsrmFileScreenTemplateManager_GetTemplate(This,name,fileScreenTemplate) (This)->lpVtbl->GetTemplate(This,name,fileScreenTemplate)
#define IFsrmFileScreenTemplateManager_EnumTemplates(This,options,fileScreenTemplates) (This)->lpVtbl->EnumTemplates(This,options,fileScreenTemplates)
#define IFsrmFileScreenTemplateManager_ExportTemplates(This,fileScreenTemplateNamesArray,serializedFileScreenTemplates) (This)->lpVtbl->ExportTemplates(This,fileScreenTemplateNamesArray,serializedFileScreenTemplates)
#define IFsrmFileScreenTemplateManager_ImportTemplates(This,serializedFileScreenTemplates,fileScreenTemplateNamesArray,fileScreenTemplates) (This)->lpVtbl->ImportTemplates(This,serializedFileScreenTemplates,fileScreenTemplateNamesArray,fileScreenTemplates)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileScreenTemplateManager_QueryInterface(IFsrmFileScreenTemplateManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileScreenTemplateManager_AddRef(IFsrmFileScreenTemplateManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileScreenTemplateManager_Release(IFsrmFileScreenTemplateManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileScreenTemplateManager_GetTypeInfoCount(IFsrmFileScreenTemplateManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileScreenTemplateManager_GetTypeInfo(IFsrmFileScreenTemplateManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileScreenTemplateManager_GetIDsOfNames(IFsrmFileScreenTemplateManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileScreenTemplateManager_Invoke(IFsrmFileScreenTemplateManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmFileScreenTemplateManager methods ***/
static inline HRESULT IFsrmFileScreenTemplateManager_CreateTemplate(IFsrmFileScreenTemplateManager* This,IFsrmFileScreenTemplate **fileScreenTemplate) {
    return This->lpVtbl->CreateTemplate(This,fileScreenTemplate);
}
static inline HRESULT IFsrmFileScreenTemplateManager_GetTemplate(IFsrmFileScreenTemplateManager* This,BSTR name,IFsrmFileScreenTemplate **fileScreenTemplate) {
    return This->lpVtbl->GetTemplate(This,name,fileScreenTemplate);
}
static inline HRESULT IFsrmFileScreenTemplateManager_EnumTemplates(IFsrmFileScreenTemplateManager* This,FsrmEnumOptions options,IFsrmCommittableCollection **fileScreenTemplates) {
    return This->lpVtbl->EnumTemplates(This,options,fileScreenTemplates);
}
static inline HRESULT IFsrmFileScreenTemplateManager_ExportTemplates(IFsrmFileScreenTemplateManager* This,VARIANT *fileScreenTemplateNamesArray,BSTR *serializedFileScreenTemplates) {
    return This->lpVtbl->ExportTemplates(This,fileScreenTemplateNamesArray,serializedFileScreenTemplates);
}
static inline HRESULT IFsrmFileScreenTemplateManager_ImportTemplates(IFsrmFileScreenTemplateManager* This,BSTR serializedFileScreenTemplates,VARIANT *fileScreenTemplateNamesArray,IFsrmCommittableCollection **fileScreenTemplates) {
    return This->lpVtbl->ImportTemplates(This,serializedFileScreenTemplates,fileScreenTemplateNamesArray,fileScreenTemplates);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileScreenTemplateManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileGroup interface
 */
#ifndef __IFsrmFileGroup_INTERFACE_DEFINED__
#define __IFsrmFileGroup_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileGroup, 0x8dd04909, 0x0e34, 0x4d55, 0xaf,0xaa, 0x89,0xe1,0xf1,0xa1,0xbb,0xb9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8dd04909-0e34-4d55-afaa-89e1f1a1bbb9")
IFsrmFileGroup : public IFsrmObject
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Members(
        IFsrmMutableCollection **members) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Members(
        IFsrmMutableCollection *members) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NonMembers(
        IFsrmMutableCollection **nonMembers) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NonMembers(
        IFsrmMutableCollection *nonMembers) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileGroup, 0x8dd04909, 0x0e34, 0x4d55, 0xaf,0xaa, 0x89,0xe1,0xf1,0xa1,0xbb,0xb9)
#endif
#else
typedef struct IFsrmFileGroupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileGroup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileGroup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileGroup *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileGroup *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileGroup *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileGroup *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileGroup *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileGroup *This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileGroup *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileGroup *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileGroup *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileGroup *This);

    /*** IFsrmFileGroup methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmFileGroup *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmFileGroup *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Members)(
        IFsrmFileGroup *This,
        IFsrmMutableCollection **members);

    HRESULT (STDMETHODCALLTYPE *put_Members)(
        IFsrmFileGroup *This,
        IFsrmMutableCollection *members);

    HRESULT (STDMETHODCALLTYPE *get_NonMembers)(
        IFsrmFileGroup *This,
        IFsrmMutableCollection **nonMembers);

    HRESULT (STDMETHODCALLTYPE *put_NonMembers)(
        IFsrmFileGroup *This,
        IFsrmMutableCollection *nonMembers);

    END_INTERFACE
} IFsrmFileGroupVtbl;

interface IFsrmFileGroup {
    CONST_VTBL IFsrmFileGroupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileGroup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileGroup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileGroup_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileGroup_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileGroup_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileGroup_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileGroup_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileGroup_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileGroup_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileGroup_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileGroup_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileGroup_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileGroup methods ***/
#define IFsrmFileGroup_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmFileGroup_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmFileGroup_get_Members(This,members) (This)->lpVtbl->get_Members(This,members)
#define IFsrmFileGroup_put_Members(This,members) (This)->lpVtbl->put_Members(This,members)
#define IFsrmFileGroup_get_NonMembers(This,nonMembers) (This)->lpVtbl->get_NonMembers(This,nonMembers)
#define IFsrmFileGroup_put_NonMembers(This,nonMembers) (This)->lpVtbl->put_NonMembers(This,nonMembers)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileGroup_QueryInterface(IFsrmFileGroup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileGroup_AddRef(IFsrmFileGroup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileGroup_Release(IFsrmFileGroup* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileGroup_GetTypeInfoCount(IFsrmFileGroup* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileGroup_GetTypeInfo(IFsrmFileGroup* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileGroup_GetIDsOfNames(IFsrmFileGroup* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileGroup_Invoke(IFsrmFileGroup* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static inline HRESULT IFsrmFileGroup_get_Id(IFsrmFileGroup* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IFsrmFileGroup_get_Description(IFsrmFileGroup* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IFsrmFileGroup_put_Description(IFsrmFileGroup* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IFsrmFileGroup_Delete(IFsrmFileGroup* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IFsrmFileGroup_Commit(IFsrmFileGroup* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileGroup methods ***/
static inline HRESULT IFsrmFileGroup_get_Name(IFsrmFileGroup* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT IFsrmFileGroup_put_Name(IFsrmFileGroup* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT IFsrmFileGroup_get_Members(IFsrmFileGroup* This,IFsrmMutableCollection **members) {
    return This->lpVtbl->get_Members(This,members);
}
static inline HRESULT IFsrmFileGroup_put_Members(IFsrmFileGroup* This,IFsrmMutableCollection *members) {
    return This->lpVtbl->put_Members(This,members);
}
static inline HRESULT IFsrmFileGroup_get_NonMembers(IFsrmFileGroup* This,IFsrmMutableCollection **nonMembers) {
    return This->lpVtbl->get_NonMembers(This,nonMembers);
}
static inline HRESULT IFsrmFileGroup_put_NonMembers(IFsrmFileGroup* This,IFsrmMutableCollection *nonMembers) {
    return This->lpVtbl->put_NonMembers(This,nonMembers);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileGroup_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileScreenBase interface
 */
#ifndef __IFsrmFileScreenBase_INTERFACE_DEFINED__
#define __IFsrmFileScreenBase_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileScreenBase, 0xf3637e80, 0x5b22, 0x4a2b, 0xa6,0x37, 0xbb,0xb6,0x42,0xb4,0x1c,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f3637e80-5b22-4a2b-a637-bbb642b41cfc")
IFsrmFileScreenBase : public IFsrmObject
{
    virtual HRESULT STDMETHODCALLTYPE get_BlockedFileGroups(
        IFsrmMutableCollection **blockList) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_BlockedFileGroups(
        IFsrmMutableCollection *blockList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FileScreenFlags(
        LONG *fileScreenFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FileScreenFlags(
        LONG fileScreenFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAction(
        FsrmActionType actionType,
        IFsrmAction **action) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumActions(
        IFsrmCollection **actions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileScreenBase, 0xf3637e80, 0x5b22, 0x4a2b, 0xa6,0x37, 0xbb,0xb6,0x42,0xb4,0x1c,0xfc)
#endif
#else
typedef struct IFsrmFileScreenBaseVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileScreenBase *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileScreenBase *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileScreenBase *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileScreenBase *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileScreenBase *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileScreenBase *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileScreenBase *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileScreenBase *This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileScreenBase *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileScreenBase *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileScreenBase *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileScreenBase *This);

    /*** IFsrmFileScreenBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BlockedFileGroups)(
        IFsrmFileScreenBase *This,
        IFsrmMutableCollection **blockList);

    HRESULT (STDMETHODCALLTYPE *put_BlockedFileGroups)(
        IFsrmFileScreenBase *This,
        IFsrmMutableCollection *blockList);

    HRESULT (STDMETHODCALLTYPE *get_FileScreenFlags)(
        IFsrmFileScreenBase *This,
        LONG *fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *put_FileScreenFlags)(
        IFsrmFileScreenBase *This,
        LONG fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *CreateAction)(
        IFsrmFileScreenBase *This,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumActions)(
        IFsrmFileScreenBase *This,
        IFsrmCollection **actions);

    END_INTERFACE
} IFsrmFileScreenBaseVtbl;

interface IFsrmFileScreenBase {
    CONST_VTBL IFsrmFileScreenBaseVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileScreenBase_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileScreenBase_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileScreenBase_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileScreenBase_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileScreenBase_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileScreenBase_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileScreenBase_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileScreenBase_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileScreenBase_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileScreenBase_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileScreenBase_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileScreenBase_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileScreenBase methods ***/
#define IFsrmFileScreenBase_get_BlockedFileGroups(This,blockList) (This)->lpVtbl->get_BlockedFileGroups(This,blockList)
#define IFsrmFileScreenBase_put_BlockedFileGroups(This,blockList) (This)->lpVtbl->put_BlockedFileGroups(This,blockList)
#define IFsrmFileScreenBase_get_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->get_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreenBase_put_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->put_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreenBase_CreateAction(This,actionType,action) (This)->lpVtbl->CreateAction(This,actionType,action)
#define IFsrmFileScreenBase_EnumActions(This,actions) (This)->lpVtbl->EnumActions(This,actions)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileScreenBase_QueryInterface(IFsrmFileScreenBase* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileScreenBase_AddRef(IFsrmFileScreenBase* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileScreenBase_Release(IFsrmFileScreenBase* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileScreenBase_GetTypeInfoCount(IFsrmFileScreenBase* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileScreenBase_GetTypeInfo(IFsrmFileScreenBase* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileScreenBase_GetIDsOfNames(IFsrmFileScreenBase* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileScreenBase_Invoke(IFsrmFileScreenBase* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static inline HRESULT IFsrmFileScreenBase_get_Id(IFsrmFileScreenBase* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IFsrmFileScreenBase_get_Description(IFsrmFileScreenBase* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IFsrmFileScreenBase_put_Description(IFsrmFileScreenBase* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IFsrmFileScreenBase_Delete(IFsrmFileScreenBase* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IFsrmFileScreenBase_Commit(IFsrmFileScreenBase* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileScreenBase methods ***/
static inline HRESULT IFsrmFileScreenBase_get_BlockedFileGroups(IFsrmFileScreenBase* This,IFsrmMutableCollection **blockList) {
    return This->lpVtbl->get_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreenBase_put_BlockedFileGroups(IFsrmFileScreenBase* This,IFsrmMutableCollection *blockList) {
    return This->lpVtbl->put_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreenBase_get_FileScreenFlags(IFsrmFileScreenBase* This,LONG *fileScreenFlags) {
    return This->lpVtbl->get_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreenBase_put_FileScreenFlags(IFsrmFileScreenBase* This,LONG fileScreenFlags) {
    return This->lpVtbl->put_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreenBase_CreateAction(IFsrmFileScreenBase* This,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateAction(This,actionType,action);
}
static inline HRESULT IFsrmFileScreenBase_EnumActions(IFsrmFileScreenBase* This,IFsrmCollection **actions) {
    return This->lpVtbl->EnumActions(This,actions);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileScreenBase_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileScreenException interface
 */
#ifndef __IFsrmFileScreenException_INTERFACE_DEFINED__
#define __IFsrmFileScreenException_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileScreenException, 0xbee7ce02, 0xdf77, 0x4515, 0x93,0x89, 0x78,0xf0,0x1c,0x5a,0xfc,0x1a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bee7ce02-df77-4515-9389-78f01c5afc1a")
IFsrmFileScreenException : public IFsrmObject
{
    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowedFileGroups(
        IFsrmMutableCollection **allowList) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowedFileGroups(
        IFsrmMutableCollection *allowList) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileScreenException, 0xbee7ce02, 0xdf77, 0x4515, 0x93,0x89, 0x78,0xf0,0x1c,0x5a,0xfc,0x1a)
#endif
#else
typedef struct IFsrmFileScreenExceptionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileScreenException *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileScreenException *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileScreenException *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileScreenException *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileScreenException *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileScreenException *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileScreenException *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileScreenException *This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileScreenException *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileScreenException *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileScreenException *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileScreenException *This);

    /*** IFsrmFileScreenException methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IFsrmFileScreenException *This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_AllowedFileGroups)(
        IFsrmFileScreenException *This,
        IFsrmMutableCollection **allowList);

    HRESULT (STDMETHODCALLTYPE *put_AllowedFileGroups)(
        IFsrmFileScreenException *This,
        IFsrmMutableCollection *allowList);

    END_INTERFACE
} IFsrmFileScreenExceptionVtbl;

interface IFsrmFileScreenException {
    CONST_VTBL IFsrmFileScreenExceptionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileScreenException_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileScreenException_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileScreenException_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileScreenException_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileScreenException_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileScreenException_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileScreenException_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileScreenException_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileScreenException_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileScreenException_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileScreenException_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileScreenException_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileScreenException methods ***/
#define IFsrmFileScreenException_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IFsrmFileScreenException_get_AllowedFileGroups(This,allowList) (This)->lpVtbl->get_AllowedFileGroups(This,allowList)
#define IFsrmFileScreenException_put_AllowedFileGroups(This,allowList) (This)->lpVtbl->put_AllowedFileGroups(This,allowList)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileScreenException_QueryInterface(IFsrmFileScreenException* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileScreenException_AddRef(IFsrmFileScreenException* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileScreenException_Release(IFsrmFileScreenException* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileScreenException_GetTypeInfoCount(IFsrmFileScreenException* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileScreenException_GetTypeInfo(IFsrmFileScreenException* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileScreenException_GetIDsOfNames(IFsrmFileScreenException* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileScreenException_Invoke(IFsrmFileScreenException* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static inline HRESULT IFsrmFileScreenException_get_Id(IFsrmFileScreenException* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IFsrmFileScreenException_get_Description(IFsrmFileScreenException* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IFsrmFileScreenException_put_Description(IFsrmFileScreenException* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IFsrmFileScreenException_Delete(IFsrmFileScreenException* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IFsrmFileScreenException_Commit(IFsrmFileScreenException* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileScreenException methods ***/
static inline HRESULT IFsrmFileScreenException_get_Path(IFsrmFileScreenException* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static inline HRESULT IFsrmFileScreenException_get_AllowedFileGroups(IFsrmFileScreenException* This,IFsrmMutableCollection **allowList) {
    return This->lpVtbl->get_AllowedFileGroups(This,allowList);
}
static inline HRESULT IFsrmFileScreenException_put_AllowedFileGroups(IFsrmFileScreenException* This,IFsrmMutableCollection *allowList) {
    return This->lpVtbl->put_AllowedFileGroups(This,allowList);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileScreenException_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileScreen interface
 */
#ifndef __IFsrmFileScreen_INTERFACE_DEFINED__
#define __IFsrmFileScreen_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileScreen, 0x5f6325d3, 0xce88, 0x4733, 0x84,0xc1, 0x2d,0x6a,0xef,0xc5,0xea,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5f6325d3-ce88-4733-84c1-2d6aefc5ea07")
IFsrmFileScreen : public IFsrmFileScreenBase
{
    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SourceTemplateName(
        BSTR *fileScreenTemplateName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MatchesSourceTemplate(
        VARIANT_BOOL *matches) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserSid(
        BSTR *userSid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserAccount(
        BSTR *userAccount) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplyTemplate(
        BSTR fileScreenTemplateName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileScreen, 0x5f6325d3, 0xce88, 0x4733, 0x84,0xc1, 0x2d,0x6a,0xef,0xc5,0xea,0x07)
#endif
#else
typedef struct IFsrmFileScreenVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileScreen *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileScreen *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileScreen *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileScreen *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileScreen *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileScreen *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileScreen *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileScreen *This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileScreen *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileScreen *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileScreen *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileScreen *This);

    /*** IFsrmFileScreenBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BlockedFileGroups)(
        IFsrmFileScreen *This,
        IFsrmMutableCollection **blockList);

    HRESULT (STDMETHODCALLTYPE *put_BlockedFileGroups)(
        IFsrmFileScreen *This,
        IFsrmMutableCollection *blockList);

    HRESULT (STDMETHODCALLTYPE *get_FileScreenFlags)(
        IFsrmFileScreen *This,
        LONG *fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *put_FileScreenFlags)(
        IFsrmFileScreen *This,
        LONG fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *CreateAction)(
        IFsrmFileScreen *This,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumActions)(
        IFsrmFileScreen *This,
        IFsrmCollection **actions);

    /*** IFsrmFileScreen methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IFsrmFileScreen *This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_SourceTemplateName)(
        IFsrmFileScreen *This,
        BSTR *fileScreenTemplateName);

    HRESULT (STDMETHODCALLTYPE *get_MatchesSourceTemplate)(
        IFsrmFileScreen *This,
        VARIANT_BOOL *matches);

    HRESULT (STDMETHODCALLTYPE *get_UserSid)(
        IFsrmFileScreen *This,
        BSTR *userSid);

    HRESULT (STDMETHODCALLTYPE *get_UserAccount)(
        IFsrmFileScreen *This,
        BSTR *userAccount);

    HRESULT (STDMETHODCALLTYPE *ApplyTemplate)(
        IFsrmFileScreen *This,
        BSTR fileScreenTemplateName);

    END_INTERFACE
} IFsrmFileScreenVtbl;

interface IFsrmFileScreen {
    CONST_VTBL IFsrmFileScreenVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileScreen_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileScreen_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileScreen_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileScreen_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileScreen_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileScreen_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileScreen_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileScreen_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileScreen_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileScreen_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileScreen_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileScreen_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileScreenBase methods ***/
#define IFsrmFileScreen_get_BlockedFileGroups(This,blockList) (This)->lpVtbl->get_BlockedFileGroups(This,blockList)
#define IFsrmFileScreen_put_BlockedFileGroups(This,blockList) (This)->lpVtbl->put_BlockedFileGroups(This,blockList)
#define IFsrmFileScreen_get_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->get_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreen_put_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->put_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreen_CreateAction(This,actionType,action) (This)->lpVtbl->CreateAction(This,actionType,action)
#define IFsrmFileScreen_EnumActions(This,actions) (This)->lpVtbl->EnumActions(This,actions)
/*** IFsrmFileScreen methods ***/
#define IFsrmFileScreen_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IFsrmFileScreen_get_SourceTemplateName(This,fileScreenTemplateName) (This)->lpVtbl->get_SourceTemplateName(This,fileScreenTemplateName)
#define IFsrmFileScreen_get_MatchesSourceTemplate(This,matches) (This)->lpVtbl->get_MatchesSourceTemplate(This,matches)
#define IFsrmFileScreen_get_UserSid(This,userSid) (This)->lpVtbl->get_UserSid(This,userSid)
#define IFsrmFileScreen_get_UserAccount(This,userAccount) (This)->lpVtbl->get_UserAccount(This,userAccount)
#define IFsrmFileScreen_ApplyTemplate(This,fileScreenTemplateName) (This)->lpVtbl->ApplyTemplate(This,fileScreenTemplateName)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileScreen_QueryInterface(IFsrmFileScreen* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileScreen_AddRef(IFsrmFileScreen* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileScreen_Release(IFsrmFileScreen* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileScreen_GetTypeInfoCount(IFsrmFileScreen* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileScreen_GetTypeInfo(IFsrmFileScreen* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileScreen_GetIDsOfNames(IFsrmFileScreen* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileScreen_Invoke(IFsrmFileScreen* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static inline HRESULT IFsrmFileScreen_get_Id(IFsrmFileScreen* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IFsrmFileScreen_get_Description(IFsrmFileScreen* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IFsrmFileScreen_put_Description(IFsrmFileScreen* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IFsrmFileScreen_Delete(IFsrmFileScreen* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IFsrmFileScreen_Commit(IFsrmFileScreen* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileScreenBase methods ***/
static inline HRESULT IFsrmFileScreen_get_BlockedFileGroups(IFsrmFileScreen* This,IFsrmMutableCollection **blockList) {
    return This->lpVtbl->get_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreen_put_BlockedFileGroups(IFsrmFileScreen* This,IFsrmMutableCollection *blockList) {
    return This->lpVtbl->put_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreen_get_FileScreenFlags(IFsrmFileScreen* This,LONG *fileScreenFlags) {
    return This->lpVtbl->get_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreen_put_FileScreenFlags(IFsrmFileScreen* This,LONG fileScreenFlags) {
    return This->lpVtbl->put_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreen_CreateAction(IFsrmFileScreen* This,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateAction(This,actionType,action);
}
static inline HRESULT IFsrmFileScreen_EnumActions(IFsrmFileScreen* This,IFsrmCollection **actions) {
    return This->lpVtbl->EnumActions(This,actions);
}
/*** IFsrmFileScreen methods ***/
static inline HRESULT IFsrmFileScreen_get_Path(IFsrmFileScreen* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static inline HRESULT IFsrmFileScreen_get_SourceTemplateName(IFsrmFileScreen* This,BSTR *fileScreenTemplateName) {
    return This->lpVtbl->get_SourceTemplateName(This,fileScreenTemplateName);
}
static inline HRESULT IFsrmFileScreen_get_MatchesSourceTemplate(IFsrmFileScreen* This,VARIANT_BOOL *matches) {
    return This->lpVtbl->get_MatchesSourceTemplate(This,matches);
}
static inline HRESULT IFsrmFileScreen_get_UserSid(IFsrmFileScreen* This,BSTR *userSid) {
    return This->lpVtbl->get_UserSid(This,userSid);
}
static inline HRESULT IFsrmFileScreen_get_UserAccount(IFsrmFileScreen* This,BSTR *userAccount) {
    return This->lpVtbl->get_UserAccount(This,userAccount);
}
static inline HRESULT IFsrmFileScreen_ApplyTemplate(IFsrmFileScreen* This,BSTR fileScreenTemplateName) {
    return This->lpVtbl->ApplyTemplate(This,fileScreenTemplateName);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileScreen_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileGroupImported interface
 */
#ifndef __IFsrmFileGroupImported_INTERFACE_DEFINED__
#define __IFsrmFileGroupImported_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileGroupImported, 0xad55f10b, 0x5f11, 0x4be7, 0x94,0xef, 0xd9,0xee,0x2e,0x47,0x0d,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ad55f10b-5f11-4be7-94ef-d9ee2e470ded")
IFsrmFileGroupImported : public IFsrmFileGroup
{
    virtual HRESULT STDMETHODCALLTYPE get_OverwriteOnCommit(
        VARIANT_BOOL *overwrite) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_OverwriteOnCommit(
        VARIANT_BOOL overwrite) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileGroupImported, 0xad55f10b, 0x5f11, 0x4be7, 0x94,0xef, 0xd9,0xee,0x2e,0x47,0x0d,0xed)
#endif
#else
typedef struct IFsrmFileGroupImportedVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileGroupImported *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileGroupImported *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileGroupImported *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileGroupImported *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileGroupImported *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileGroupImported *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileGroupImported *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileGroupImported *This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileGroupImported *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileGroupImported *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileGroupImported *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileGroupImported *This);

    /*** IFsrmFileGroup methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmFileGroupImported *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmFileGroupImported *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Members)(
        IFsrmFileGroupImported *This,
        IFsrmMutableCollection **members);

    HRESULT (STDMETHODCALLTYPE *put_Members)(
        IFsrmFileGroupImported *This,
        IFsrmMutableCollection *members);

    HRESULT (STDMETHODCALLTYPE *get_NonMembers)(
        IFsrmFileGroupImported *This,
        IFsrmMutableCollection **nonMembers);

    HRESULT (STDMETHODCALLTYPE *put_NonMembers)(
        IFsrmFileGroupImported *This,
        IFsrmMutableCollection *nonMembers);

    /*** IFsrmFileGroupImported methods ***/
    HRESULT (STDMETHODCALLTYPE *get_OverwriteOnCommit)(
        IFsrmFileGroupImported *This,
        VARIANT_BOOL *overwrite);

    HRESULT (STDMETHODCALLTYPE *put_OverwriteOnCommit)(
        IFsrmFileGroupImported *This,
        VARIANT_BOOL overwrite);

    END_INTERFACE
} IFsrmFileGroupImportedVtbl;

interface IFsrmFileGroupImported {
    CONST_VTBL IFsrmFileGroupImportedVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileGroupImported_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileGroupImported_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileGroupImported_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileGroupImported_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileGroupImported_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileGroupImported_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileGroupImported_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileGroupImported_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileGroupImported_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileGroupImported_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileGroupImported_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileGroupImported_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileGroup methods ***/
#define IFsrmFileGroupImported_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmFileGroupImported_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmFileGroupImported_get_Members(This,members) (This)->lpVtbl->get_Members(This,members)
#define IFsrmFileGroupImported_put_Members(This,members) (This)->lpVtbl->put_Members(This,members)
#define IFsrmFileGroupImported_get_NonMembers(This,nonMembers) (This)->lpVtbl->get_NonMembers(This,nonMembers)
#define IFsrmFileGroupImported_put_NonMembers(This,nonMembers) (This)->lpVtbl->put_NonMembers(This,nonMembers)
/*** IFsrmFileGroupImported methods ***/
#define IFsrmFileGroupImported_get_OverwriteOnCommit(This,overwrite) (This)->lpVtbl->get_OverwriteOnCommit(This,overwrite)
#define IFsrmFileGroupImported_put_OverwriteOnCommit(This,overwrite) (This)->lpVtbl->put_OverwriteOnCommit(This,overwrite)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileGroupImported_QueryInterface(IFsrmFileGroupImported* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileGroupImported_AddRef(IFsrmFileGroupImported* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileGroupImported_Release(IFsrmFileGroupImported* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileGroupImported_GetTypeInfoCount(IFsrmFileGroupImported* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileGroupImported_GetTypeInfo(IFsrmFileGroupImported* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileGroupImported_GetIDsOfNames(IFsrmFileGroupImported* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileGroupImported_Invoke(IFsrmFileGroupImported* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static inline HRESULT IFsrmFileGroupImported_get_Id(IFsrmFileGroupImported* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IFsrmFileGroupImported_get_Description(IFsrmFileGroupImported* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IFsrmFileGroupImported_put_Description(IFsrmFileGroupImported* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IFsrmFileGroupImported_Delete(IFsrmFileGroupImported* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IFsrmFileGroupImported_Commit(IFsrmFileGroupImported* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileGroup methods ***/
static inline HRESULT IFsrmFileGroupImported_get_Name(IFsrmFileGroupImported* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT IFsrmFileGroupImported_put_Name(IFsrmFileGroupImported* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT IFsrmFileGroupImported_get_Members(IFsrmFileGroupImported* This,IFsrmMutableCollection **members) {
    return This->lpVtbl->get_Members(This,members);
}
static inline HRESULT IFsrmFileGroupImported_put_Members(IFsrmFileGroupImported* This,IFsrmMutableCollection *members) {
    return This->lpVtbl->put_Members(This,members);
}
static inline HRESULT IFsrmFileGroupImported_get_NonMembers(IFsrmFileGroupImported* This,IFsrmMutableCollection **nonMembers) {
    return This->lpVtbl->get_NonMembers(This,nonMembers);
}
static inline HRESULT IFsrmFileGroupImported_put_NonMembers(IFsrmFileGroupImported* This,IFsrmMutableCollection *nonMembers) {
    return This->lpVtbl->put_NonMembers(This,nonMembers);
}
/*** IFsrmFileGroupImported methods ***/
static inline HRESULT IFsrmFileGroupImported_get_OverwriteOnCommit(IFsrmFileGroupImported* This,VARIANT_BOOL *overwrite) {
    return This->lpVtbl->get_OverwriteOnCommit(This,overwrite);
}
static inline HRESULT IFsrmFileGroupImported_put_OverwriteOnCommit(IFsrmFileGroupImported* This,VARIANT_BOOL overwrite) {
    return This->lpVtbl->put_OverwriteOnCommit(This,overwrite);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileGroupImported_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileScreenTemplate interface
 */
#ifndef __IFsrmFileScreenTemplate_INTERFACE_DEFINED__
#define __IFsrmFileScreenTemplate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileScreenTemplate, 0x205bebf8, 0xdd93, 0x452a, 0x95,0xa6, 0x32,0xb5,0x66,0xb3,0x58,0x28);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("205bebf8-dd93-452a-95a6-32b566b35828")
IFsrmFileScreenTemplate : public IFsrmFileScreenBase
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyTemplate(
        BSTR fileScreenTemplateName) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitAndUpdateDerived(
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileScreenTemplate, 0x205bebf8, 0xdd93, 0x452a, 0x95,0xa6, 0x32,0xb5,0x66,0xb3,0x58,0x28)
#endif
#else
typedef struct IFsrmFileScreenTemplateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileScreenTemplate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileScreenTemplate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileScreenTemplate *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileScreenTemplate *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileScreenTemplate *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileScreenTemplate *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileScreenTemplate *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileScreenTemplate *This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileScreenTemplate *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileScreenTemplate *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileScreenTemplate *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileScreenTemplate *This);

    /*** IFsrmFileScreenBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BlockedFileGroups)(
        IFsrmFileScreenTemplate *This,
        IFsrmMutableCollection **blockList);

    HRESULT (STDMETHODCALLTYPE *put_BlockedFileGroups)(
        IFsrmFileScreenTemplate *This,
        IFsrmMutableCollection *blockList);

    HRESULT (STDMETHODCALLTYPE *get_FileScreenFlags)(
        IFsrmFileScreenTemplate *This,
        LONG *fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *put_FileScreenFlags)(
        IFsrmFileScreenTemplate *This,
        LONG fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *CreateAction)(
        IFsrmFileScreenTemplate *This,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumActions)(
        IFsrmFileScreenTemplate *This,
        IFsrmCollection **actions);

    /*** IFsrmFileScreenTemplate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmFileScreenTemplate *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmFileScreenTemplate *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *CopyTemplate)(
        IFsrmFileScreenTemplate *This,
        BSTR fileScreenTemplateName);

    HRESULT (STDMETHODCALLTYPE *CommitAndUpdateDerived)(
        IFsrmFileScreenTemplate *This,
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult);

    END_INTERFACE
} IFsrmFileScreenTemplateVtbl;

interface IFsrmFileScreenTemplate {
    CONST_VTBL IFsrmFileScreenTemplateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileScreenTemplate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileScreenTemplate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileScreenTemplate_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileScreenTemplate_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileScreenTemplate_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileScreenTemplate_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileScreenTemplate_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileScreenTemplate_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileScreenTemplate_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileScreenTemplate_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileScreenTemplate_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileScreenTemplate_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileScreenBase methods ***/
#define IFsrmFileScreenTemplate_get_BlockedFileGroups(This,blockList) (This)->lpVtbl->get_BlockedFileGroups(This,blockList)
#define IFsrmFileScreenTemplate_put_BlockedFileGroups(This,blockList) (This)->lpVtbl->put_BlockedFileGroups(This,blockList)
#define IFsrmFileScreenTemplate_get_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->get_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreenTemplate_put_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->put_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreenTemplate_CreateAction(This,actionType,action) (This)->lpVtbl->CreateAction(This,actionType,action)
#define IFsrmFileScreenTemplate_EnumActions(This,actions) (This)->lpVtbl->EnumActions(This,actions)
/*** IFsrmFileScreenTemplate methods ***/
#define IFsrmFileScreenTemplate_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmFileScreenTemplate_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmFileScreenTemplate_CopyTemplate(This,fileScreenTemplateName) (This)->lpVtbl->CopyTemplate(This,fileScreenTemplateName)
#define IFsrmFileScreenTemplate_CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult) (This)->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileScreenTemplate_QueryInterface(IFsrmFileScreenTemplate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileScreenTemplate_AddRef(IFsrmFileScreenTemplate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileScreenTemplate_Release(IFsrmFileScreenTemplate* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileScreenTemplate_GetTypeInfoCount(IFsrmFileScreenTemplate* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileScreenTemplate_GetTypeInfo(IFsrmFileScreenTemplate* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileScreenTemplate_GetIDsOfNames(IFsrmFileScreenTemplate* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileScreenTemplate_Invoke(IFsrmFileScreenTemplate* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static inline HRESULT IFsrmFileScreenTemplate_get_Id(IFsrmFileScreenTemplate* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IFsrmFileScreenTemplate_get_Description(IFsrmFileScreenTemplate* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IFsrmFileScreenTemplate_put_Description(IFsrmFileScreenTemplate* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IFsrmFileScreenTemplate_Delete(IFsrmFileScreenTemplate* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IFsrmFileScreenTemplate_Commit(IFsrmFileScreenTemplate* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileScreenBase methods ***/
static inline HRESULT IFsrmFileScreenTemplate_get_BlockedFileGroups(IFsrmFileScreenTemplate* This,IFsrmMutableCollection **blockList) {
    return This->lpVtbl->get_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreenTemplate_put_BlockedFileGroups(IFsrmFileScreenTemplate* This,IFsrmMutableCollection *blockList) {
    return This->lpVtbl->put_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreenTemplate_get_FileScreenFlags(IFsrmFileScreenTemplate* This,LONG *fileScreenFlags) {
    return This->lpVtbl->get_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreenTemplate_put_FileScreenFlags(IFsrmFileScreenTemplate* This,LONG fileScreenFlags) {
    return This->lpVtbl->put_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreenTemplate_CreateAction(IFsrmFileScreenTemplate* This,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateAction(This,actionType,action);
}
static inline HRESULT IFsrmFileScreenTemplate_EnumActions(IFsrmFileScreenTemplate* This,IFsrmCollection **actions) {
    return This->lpVtbl->EnumActions(This,actions);
}
/*** IFsrmFileScreenTemplate methods ***/
static inline HRESULT IFsrmFileScreenTemplate_get_Name(IFsrmFileScreenTemplate* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT IFsrmFileScreenTemplate_put_Name(IFsrmFileScreenTemplate* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT IFsrmFileScreenTemplate_CopyTemplate(IFsrmFileScreenTemplate* This,BSTR fileScreenTemplateName) {
    return This->lpVtbl->CopyTemplate(This,fileScreenTemplateName);
}
static inline HRESULT IFsrmFileScreenTemplate_CommitAndUpdateDerived(IFsrmFileScreenTemplate* This,FsrmCommitOptions commitOptions,FsrmTemplateApplyOptions applyOptions,IFsrmDerivedObjectsResult **derivedObjectsResult) {
    return This->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileScreenTemplate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFsrmFileScreenTemplateImported interface
 */
#ifndef __IFsrmFileScreenTemplateImported_INTERFACE_DEFINED__
#define __IFsrmFileScreenTemplateImported_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFsrmFileScreenTemplateImported, 0xe1010359, 0x3e5d, 0x4ecd, 0x9f,0xe4, 0xef,0x48,0x62,0x2f,0xdf,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e1010359-3e5d-4ecd-9fe4-ef48622fdf30")
IFsrmFileScreenTemplateImported : public IFsrmFileScreenTemplate
{
    virtual HRESULT STDMETHODCALLTYPE get_OverwriteOnCommit(
        VARIANT_BOOL *overwrite) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_OverwriteOnCommit(
        VARIANT_BOOL overwrite) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFsrmFileScreenTemplateImported, 0xe1010359, 0x3e5d, 0x4ecd, 0x9f,0xe4, 0xef,0x48,0x62,0x2f,0xdf,0x30)
#endif
#else
typedef struct IFsrmFileScreenTemplateImportedVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFsrmFileScreenTemplateImported *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFsrmFileScreenTemplateImported *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFsrmFileScreenTemplateImported *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFsrmFileScreenTemplateImported *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFsrmFileScreenTemplateImported *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFsrmFileScreenTemplateImported *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFsrmFileScreenTemplateImported *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IFsrmObject methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IFsrmFileScreenTemplateImported *This,
        FSRM_OBJECT_ID *id);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IFsrmFileScreenTemplateImported *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IFsrmFileScreenTemplateImported *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IFsrmFileScreenTemplateImported *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IFsrmFileScreenTemplateImported *This);

    /*** IFsrmFileScreenBase methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BlockedFileGroups)(
        IFsrmFileScreenTemplateImported *This,
        IFsrmMutableCollection **blockList);

    HRESULT (STDMETHODCALLTYPE *put_BlockedFileGroups)(
        IFsrmFileScreenTemplateImported *This,
        IFsrmMutableCollection *blockList);

    HRESULT (STDMETHODCALLTYPE *get_FileScreenFlags)(
        IFsrmFileScreenTemplateImported *This,
        LONG *fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *put_FileScreenFlags)(
        IFsrmFileScreenTemplateImported *This,
        LONG fileScreenFlags);

    HRESULT (STDMETHODCALLTYPE *CreateAction)(
        IFsrmFileScreenTemplateImported *This,
        FsrmActionType actionType,
        IFsrmAction **action);

    HRESULT (STDMETHODCALLTYPE *EnumActions)(
        IFsrmFileScreenTemplateImported *This,
        IFsrmCollection **actions);

    /*** IFsrmFileScreenTemplate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFsrmFileScreenTemplateImported *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFsrmFileScreenTemplateImported *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *CopyTemplate)(
        IFsrmFileScreenTemplateImported *This,
        BSTR fileScreenTemplateName);

    HRESULT (STDMETHODCALLTYPE *CommitAndUpdateDerived)(
        IFsrmFileScreenTemplateImported *This,
        FsrmCommitOptions commitOptions,
        FsrmTemplateApplyOptions applyOptions,
        IFsrmDerivedObjectsResult **derivedObjectsResult);

    /*** IFsrmFileScreenTemplateImported methods ***/
    HRESULT (STDMETHODCALLTYPE *get_OverwriteOnCommit)(
        IFsrmFileScreenTemplateImported *This,
        VARIANT_BOOL *overwrite);

    HRESULT (STDMETHODCALLTYPE *put_OverwriteOnCommit)(
        IFsrmFileScreenTemplateImported *This,
        VARIANT_BOOL overwrite);

    END_INTERFACE
} IFsrmFileScreenTemplateImportedVtbl;

interface IFsrmFileScreenTemplateImported {
    CONST_VTBL IFsrmFileScreenTemplateImportedVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFsrmFileScreenTemplateImported_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFsrmFileScreenTemplateImported_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFsrmFileScreenTemplateImported_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFsrmFileScreenTemplateImported_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFsrmFileScreenTemplateImported_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFsrmFileScreenTemplateImported_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFsrmFileScreenTemplateImported_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IFsrmObject methods ***/
#define IFsrmFileScreenTemplateImported_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IFsrmFileScreenTemplateImported_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IFsrmFileScreenTemplateImported_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IFsrmFileScreenTemplateImported_Delete(This) (This)->lpVtbl->Delete(This)
#define IFsrmFileScreenTemplateImported_Commit(This) (This)->lpVtbl->Commit(This)
/*** IFsrmFileScreenBase methods ***/
#define IFsrmFileScreenTemplateImported_get_BlockedFileGroups(This,blockList) (This)->lpVtbl->get_BlockedFileGroups(This,blockList)
#define IFsrmFileScreenTemplateImported_put_BlockedFileGroups(This,blockList) (This)->lpVtbl->put_BlockedFileGroups(This,blockList)
#define IFsrmFileScreenTemplateImported_get_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->get_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreenTemplateImported_put_FileScreenFlags(This,fileScreenFlags) (This)->lpVtbl->put_FileScreenFlags(This,fileScreenFlags)
#define IFsrmFileScreenTemplateImported_CreateAction(This,actionType,action) (This)->lpVtbl->CreateAction(This,actionType,action)
#define IFsrmFileScreenTemplateImported_EnumActions(This,actions) (This)->lpVtbl->EnumActions(This,actions)
/*** IFsrmFileScreenTemplate methods ***/
#define IFsrmFileScreenTemplateImported_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IFsrmFileScreenTemplateImported_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFsrmFileScreenTemplateImported_CopyTemplate(This,fileScreenTemplateName) (This)->lpVtbl->CopyTemplate(This,fileScreenTemplateName)
#define IFsrmFileScreenTemplateImported_CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult) (This)->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult)
/*** IFsrmFileScreenTemplateImported methods ***/
#define IFsrmFileScreenTemplateImported_get_OverwriteOnCommit(This,overwrite) (This)->lpVtbl->get_OverwriteOnCommit(This,overwrite)
#define IFsrmFileScreenTemplateImported_put_OverwriteOnCommit(This,overwrite) (This)->lpVtbl->put_OverwriteOnCommit(This,overwrite)
#else
/*** IUnknown methods ***/
static inline HRESULT IFsrmFileScreenTemplateImported_QueryInterface(IFsrmFileScreenTemplateImported* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFsrmFileScreenTemplateImported_AddRef(IFsrmFileScreenTemplateImported* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFsrmFileScreenTemplateImported_Release(IFsrmFileScreenTemplateImported* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFsrmFileScreenTemplateImported_GetTypeInfoCount(IFsrmFileScreenTemplateImported* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFsrmFileScreenTemplateImported_GetTypeInfo(IFsrmFileScreenTemplateImported* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFsrmFileScreenTemplateImported_GetIDsOfNames(IFsrmFileScreenTemplateImported* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFsrmFileScreenTemplateImported_Invoke(IFsrmFileScreenTemplateImported* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IFsrmObject methods ***/
static inline HRESULT IFsrmFileScreenTemplateImported_get_Id(IFsrmFileScreenTemplateImported* This,FSRM_OBJECT_ID *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IFsrmFileScreenTemplateImported_get_Description(IFsrmFileScreenTemplateImported* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IFsrmFileScreenTemplateImported_put_Description(IFsrmFileScreenTemplateImported* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IFsrmFileScreenTemplateImported_Delete(IFsrmFileScreenTemplateImported* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IFsrmFileScreenTemplateImported_Commit(IFsrmFileScreenTemplateImported* This) {
    return This->lpVtbl->Commit(This);
}
/*** IFsrmFileScreenBase methods ***/
static inline HRESULT IFsrmFileScreenTemplateImported_get_BlockedFileGroups(IFsrmFileScreenTemplateImported* This,IFsrmMutableCollection **blockList) {
    return This->lpVtbl->get_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreenTemplateImported_put_BlockedFileGroups(IFsrmFileScreenTemplateImported* This,IFsrmMutableCollection *blockList) {
    return This->lpVtbl->put_BlockedFileGroups(This,blockList);
}
static inline HRESULT IFsrmFileScreenTemplateImported_get_FileScreenFlags(IFsrmFileScreenTemplateImported* This,LONG *fileScreenFlags) {
    return This->lpVtbl->get_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreenTemplateImported_put_FileScreenFlags(IFsrmFileScreenTemplateImported* This,LONG fileScreenFlags) {
    return This->lpVtbl->put_FileScreenFlags(This,fileScreenFlags);
}
static inline HRESULT IFsrmFileScreenTemplateImported_CreateAction(IFsrmFileScreenTemplateImported* This,FsrmActionType actionType,IFsrmAction **action) {
    return This->lpVtbl->CreateAction(This,actionType,action);
}
static inline HRESULT IFsrmFileScreenTemplateImported_EnumActions(IFsrmFileScreenTemplateImported* This,IFsrmCollection **actions) {
    return This->lpVtbl->EnumActions(This,actions);
}
/*** IFsrmFileScreenTemplate methods ***/
static inline HRESULT IFsrmFileScreenTemplateImported_get_Name(IFsrmFileScreenTemplateImported* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT IFsrmFileScreenTemplateImported_put_Name(IFsrmFileScreenTemplateImported* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT IFsrmFileScreenTemplateImported_CopyTemplate(IFsrmFileScreenTemplateImported* This,BSTR fileScreenTemplateName) {
    return This->lpVtbl->CopyTemplate(This,fileScreenTemplateName);
}
static inline HRESULT IFsrmFileScreenTemplateImported_CommitAndUpdateDerived(IFsrmFileScreenTemplateImported* This,FsrmCommitOptions commitOptions,FsrmTemplateApplyOptions applyOptions,IFsrmDerivedObjectsResult **derivedObjectsResult) {
    return This->lpVtbl->CommitAndUpdateDerived(This,commitOptions,applyOptions,derivedObjectsResult);
}
/*** IFsrmFileScreenTemplateImported methods ***/
static inline HRESULT IFsrmFileScreenTemplateImported_get_OverwriteOnCommit(IFsrmFileScreenTemplateImported* This,VARIANT_BOOL *overwrite) {
    return This->lpVtbl->get_OverwriteOnCommit(This,overwrite);
}
static inline HRESULT IFsrmFileScreenTemplateImported_put_OverwriteOnCommit(IFsrmFileScreenTemplateImported* This,VARIANT_BOOL overwrite) {
    return This->lpVtbl->put_OverwriteOnCommit(This,overwrite);
}
#endif
#endif

#endif


#endif  /* __IFsrmFileScreenTemplateImported_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __fsrmscreen_h__ */
