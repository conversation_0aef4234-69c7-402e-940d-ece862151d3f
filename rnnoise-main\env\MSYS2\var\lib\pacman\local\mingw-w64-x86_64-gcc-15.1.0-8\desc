%NAME%
mingw-w64-x86_64-gcc

%VERSION%
15.1.0-8

%BASE%
mingw-w64-gcc

%DESC%
GNU Compiler Collection (C,C++,OpenMP) for MinGW-w64

%URL%
https://gcc.gnu.org

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/ab3c2437/16504241312)

%SIZE%
256635252

%GROUPS%
mingw-w64-x86_64-toolchain

%LICENSE%
spdx:GPL-3.0-or-later

%VALIDATION%
sha256
pgp

%DEPENDS%
mingw-w64-x86_64-binutils
mingw-w64-x86_64-crt
mingw-w64-x86_64-headers
mingw-w64-x86_64-isl
mingw-w64-x86_64-gmp
mingw-w64-x86_64-mpfr
mingw-w64-x86_64-mpc
mingw-w64-x86_64-gcc-libs=15.1.0-8
mingw-w64-x86_64-windows-default-manifest
mingw-w64-x86_64-winpthreads
mingw-w64-x86_64-zlib
mingw-w64-x86_64-zstd

%CONFLICTS%
mingw-w64-x86_64-gcc-base
mingw-w64-x86_64-gcc-rust

%PROVIDES%
mingw-w64-x86_64-gcc-base
mingw-w64-x86_64-cc

%XDATA%
pkgtype=split

