/*** Autogenerated by WIDL 10.12 from include/dvdif.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dvdif_h__
#define __dvdif_h__

/* Forward declarations */

#ifndef __IDvdControl_FWD_DEFINED__
#define __IDvdControl_FWD_DEFINED__
typedef interface IDvdControl IDvdControl;
#ifdef __cplusplus
interface IDvdControl;
#endif /* __cplusplus */
#endif

#ifndef __IDvdInfo_FWD_DEFINED__
#define __IDvdInfo_FWD_DEFINED__
typedef interface IDvdInfo IDvdInfo;
#ifdef __cplusplus
interface IDvdInfo;
#endif /* __cplusplus */
#endif

#ifndef __IDvdCmd_FWD_DEFINED__
#define __IDvdCmd_FWD_DEFINED__
typedef interface IDvdCmd IDvdCmd;
#ifdef __cplusplus
interface IDvdCmd;
#endif /* __cplusplus */
#endif

#ifndef __IDvdState_FWD_DEFINED__
#define __IDvdState_FWD_DEFINED__
typedef interface IDvdState IDvdState;
#ifdef __cplusplus
interface IDvdState;
#endif /* __cplusplus */
#endif

#ifndef __IDvdControl2_FWD_DEFINED__
#define __IDvdControl2_FWD_DEFINED__
typedef interface IDvdControl2 IDvdControl2;
#ifdef __cplusplus
interface IDvdControl2;
#endif /* __cplusplus */
#endif

#ifndef __IDvdInfo2_FWD_DEFINED__
#define __IDvdInfo2_FWD_DEFINED__
typedef interface IDvdInfo2 IDvdInfo2;
#ifdef __cplusplus
interface IDvdInfo2;
#endif /* __cplusplus */
#endif

#ifndef __IDvdGraphBuilder_FWD_DEFINED__
#define __IDvdGraphBuilder_FWD_DEFINED__
typedef interface IDvdGraphBuilder IDvdGraphBuilder;
#ifdef __cplusplus
interface IDvdGraphBuilder;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <objidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <ddraw.h>
#if 0
#ifndef __IGraphBuilder_FWD_DEFINED__
#define __IGraphBuilder_FWD_DEFINED__
typedef interface IGraphBuilder IGraphBuilder;
#ifdef __cplusplus
interface IGraphBuilder;
#endif /* __cplusplus */
#endif

#endif
typedef enum tagDVD_DOMAIN {
    DVD_DOMAIN_FirstPlay = 1,
    DVD_DOMAIN_VideoManagerMenu = 2,
    DVD_DOMAIN_VideoTitleSetMenu = 3,
    DVD_DOMAIN_Title = 4,
    DVD_DOMAIN_Stop = 5
} DVD_DOMAIN;
typedef enum tagDVD_MENU_ID {
    DVD_MENU_Title = 2,
    DVD_MENU_Root = 3,
    DVD_MENU_Subpicture = 4,
    DVD_MENU_Audio = 5,
    DVD_MENU_Angle = 6,
    DVD_MENU_Chapter = 7
} DVD_MENU_ID;
typedef enum tagDVD_DISC_SIDE {
    DVD_SIDE_A = 1,
    DVD_SIDE_B = 2
} DVD_DISC_SIDE;
typedef enum tagDVD_PREFERRED_DISPLAY_MODE {
    DISPLAY_CONTENT_DEFAULT = 0,
    DISPLAY_16x9 = 1,
    DISPLAY_4x3_PANSCAN_PREFERRED = 2,
    DISPLAY_4x3_LETTERBOX_PREFERRED = 3
} DVD_PREFERRED_DISPLAY_MODE;
typedef WORD DVD_REGISTER;
typedef DVD_REGISTER GPRMARRAY[16];
typedef DVD_REGISTER SPRMARRAY[24];
typedef struct tagDVD_ATR {
    ULONG ulCAT;
    BYTE pbATRI[768];
} DVD_ATR;
typedef BYTE DVD_VideoATR[2];
typedef BYTE DVD_AudioATR[8];
typedef BYTE DVD_SubpictureATR[6];
typedef enum tagDVD_FRAMERATE {
    DVD_FPS_25 = 1,
    DVD_FPS_30NonDrop = 3
} DVD_FRAMERATE;
typedef struct tagDVD_TIMECODE
{
   ULONG Hours1 : 4;
   ULONG Hours10 : 4;
   ULONG Minutes1 : 4;
   ULONG Minutes10 : 4;
   ULONG Seconds1 : 4;
   ULONG Seconds10 : 4;
   ULONG Frames1 : 4;
   ULONG Frames10 : 2;
   ULONG FrameRateCode : 2;
} DVD_TIMECODE;
typedef enum tagDVD_NavCmdType {
    DVD_NavCmdType_Pre = 1,
    DVD_NavCmdType_Post = 2,
    DVD_NavCmdType_Cell = 3,
    DVD_NavCmdType_Button = 4
} DVD_NavCmdType;
typedef enum tagDVD_TIMECODE_FLAGS {
    DVD_TC_FLAG_25fps = 0x1,
    DVD_TC_FLAG_30fps = 0x2,
    DVD_TC_FLAG_DropFrame = 0x4,
    DVD_TC_FLAG_Interpolated = 0x8
} DVD_TIMECODE_FLAGS;
typedef struct tagDVD_HMSF_TIMECODE {
    BYTE bHours;
    BYTE bMinutes;
    BYTE bSeconds;
    BYTE bFrames;
} DVD_HMSF_TIMECODE;
typedef struct tagDVD_PLAYBACK_LOCATION2 {
    ULONG TitleNum;
    ULONG ChapterNum;
    DVD_HMSF_TIMECODE TimeCode;
    ULONG TimeCodeFlags;
} DVD_PLAYBACK_LOCATION2;
typedef struct tagDVD_PLAYBACK_LOCATION {
    ULONG TitleNum;
    ULONG ChapterNum;
    ULONG TimeCode;
} DVD_PLAYBACK_LOCATION;
typedef DWORD VALID_UOP_SOMTHING_OR_OTHER;
typedef enum __WIDL_dvdif_generated_name_0000000E {
    UOP_FLAG_Play_Title_Or_AtTime = 0x1,
    UOP_FLAG_Play_Chapter = 0x2,
    UOP_FLAG_Play_Title = 0x4,
    UOP_FLAG_Stop = 0x8,
    UOP_FLAG_ReturnFromSubMenu = 0x10,
    UOP_FLAG_Play_Chapter_Or_AtTime = 0x20,
    UOP_FLAG_PlayPrev_Or_Replay_Chapter = 0x40,
    UOP_FLAG_PlayNext_Chapter = 0x80,
    UOP_FLAG_Play_Forwards = 0x100,
    UOP_FLAG_Play_Backwards = 0x200,
    UOP_FLAG_ShowMenu_Title = 0x400,
    UOP_FLAG_ShowMenu_Root = 0x800,
    UOP_FLAG_ShowMenu_SubPic = 0x1000,
    UOP_FLAG_ShowMenu_Audio = 0x2000,
    UOP_FLAG_ShowMenu_Angle = 0x4000,
    UOP_FLAG_ShowMenu_Chapter = 0x8000,
    UOP_FLAG_Resume = 0x10000,
    UOP_FLAG_Select_Or_Activate_Button = 0x20000,
    UOP_FLAG_Still_Off = 0x40000,
    UOP_FLAG_Pause_On = 0x80000,
    UOP_FLAG_Select_Audio_Stream = 0x100000,
    UOP_FLAG_Select_SubPic_Stream = 0x200000,
    UOP_FLAG_Select_Angle = 0x400000,
    UOP_FLAG_Select_Karaoke_Audio_Presentation_Mode = 0x800000,
    UOP_FLAG_Select_Video_Mode_Preference = 0x1000000
} VALID_UOP_FLAG;
typedef enum __WIDL_dvdif_generated_name_0000000F {
    DVD_CMD_FLAG_None = 0x0,
    DVD_CMD_FLAG_Flush = 0x1,
    DVD_CMD_FLAG_SendEvents = 0x2,
    DVD_CMD_FLAG_Block = 0x4,
    DVD_CMD_FLAG_StartWhenRendered = 0x8,
    DVD_CMD_FLAG_EndAfterRendered = 0x10
} DVD_CMD_FLAGS;
typedef enum __WIDL_dvdif_generated_name_00000010 {
    DVD_ResetOnStop = 1,
    DVD_NotifyParentalLevelChange = 2,
    DVD_HMSF_TimeCodeEvents = 3,
    DVD_AudioDuringFFwdRew = 4,
    DVD_EnableNonblockingAPIs = 5,
    DVD_CacheSizeInMB = 6,
    DVD_EnablePortableBookmarks = 7,
    DVD_EnableExtendedCopyProtectErrors = 8,
    DVD_NotifyPositionChange = 9,
    DVD_IncreaseOutputControl = 10,
    DVD_EnableStreaming = 11,
    DVD_EnableESOutput = 12,
    DVD_EnableTitleLength = 13,
    DVD_DisableStillThrottle = 14,
    DVD_EnableLoggingEvents = 15,
    DVD_MaxReadBurstInKB = 16,
    DVD_ReadBurstPeriodInMS = 17,
    DVD_RestartDisc = 18,
    DVD_EnableCC = 19
} DVD_OPTION_FLAG;
typedef enum __WIDL_dvdif_generated_name_00000011 {
    DVD_Relative_Upper = 1,
    DVD_Relative_Lower = 2,
    DVD_Relative_Left = 3,
    DVD_Relative_Right = 4
} DVD_RELATIVE_BUTTON;
typedef enum tagDVD_PARENTAL_LEVEL {
    DVD_PARENTAL_LEVEL_8 = 0x8000,
    DVD_PARENTAL_LEVEL_7 = 0x4000,
    DVD_PARENTAL_LEVEL_6 = 0x2000,
    DVD_PARENTAL_LEVEL_5 = 0x1000,
    DVD_PARENTAL_LEVEL_4 = 0x800,
    DVD_PARENTAL_LEVEL_3 = 0x400,
    DVD_PARENTAL_LEVEL_2 = 0x200,
    DVD_PARENTAL_LEVEL_1 = 0x100
} DVD_PARENTAL_LEVEL;
typedef enum tagDVD_AUDIO_LANG_EXT {
    DVD_AUD_EXT_NotSpecified = 0,
    DVD_AUD_EXT_Captions = 1,
    DVD_AUD_EXT_VisuallyImpaired = 2,
    DVD_AUD_EXT_DirectorComments1 = 3,
    DVD_AUD_EXT_DirectorComments2 = 4
} DVD_AUDIO_LANG_EXT;
typedef enum tagDVD_SUBPICTURE_LANG_EXT {
    DVD_SP_EXT_NotSpecified = 0,
    DVD_SP_EXT_Caption_Normal = 1,
    DVD_SP_EXT_Caption_Big = 2,
    DVD_SP_EXT_Caption_Children = 3,
    DVD_SP_EXT_CC_Normal = 5,
    DVD_SP_EXT_CC_Big = 6,
    DVD_SP_EXT_CC_Children = 7,
    DVD_SP_EXT_Forced = 9,
    DVD_SP_EXT_DirectorComments_Normal = 13,
    DVD_SP_EXT_DirectorComments_Big = 14,
    DVD_SP_EXT_DirectorComments_Children = 15
} DVD_SUBPICTURE_LANG_EXT;
typedef enum tagDVD_AUDIO_APPMODE {
    DVD_AudioMode_None = 0,
    DVD_AudioMode_Karaoke = 1,
    DVD_AudioMode_Surround = 2,
    DVD_AudioMode_Other = 3
} DVD_AUDIO_APPMODE;
typedef enum tagDVD_AUDIO_FORMAT {
    DVD_AudioFormat_AC3 = 0,
    DVD_AudioFormat_MPEG1 = 1,
    DVD_AudioFormat_MPEG1_DRC = 2,
    DVD_AudioFormat_MPEG2 = 3,
    DVD_AudioFormat_MPEG2_DRC = 4,
    DVD_AudioFormat_LPCM = 5,
    DVD_AudioFormat_DTS = 6,
    DVD_AudioFormat_SDDS = 7,
    DVD_AudioFormat_Other = 8
} DVD_AUDIO_FORMAT;
typedef enum tagDVD_KARAOKE_DOWNMIX {
    DVD_Mix_0to0 = 0x1,
    DVD_Mix_1to0 = 0x2,
    DVD_Mix_2to0 = 0x4,
    DVD_Mix_3to0 = 0x8,
    DVD_Mix_4to0 = 0x10,
    DVD_Mix_Lto0 = 0x20,
    DVD_Mix_Rto0 = 0x40,
    DVD_Mix_0to1 = 0x100,
    DVD_Mix_1to1 = 0x200,
    DVD_Mix_2to1 = 0x400,
    DVD_Mix_3to1 = 0x800,
    DVD_Mix_4to1 = 0x1000,
    DVD_Mix_Lto1 = 0x2000,
    DVD_Mix_Rto1 = 0x4000
} DVD_KARAOKE_DOWNMIX;
typedef enum tagDVD_KARAOKE_CONTENTS {
    DVD_Karaoke_GuideVocal1 = 0x1,
    DVD_Karaoke_GuideVocal2 = 0x2,
    DVD_Karaoke_GuideMelody1 = 0x4,
    DVD_Karaoke_GuideMelody2 = 0x8,
    DVD_Karaoke_GuideMelodyA = 0x10,
    DVD_Karaoke_GuideMelodyB = 0x20,
    DVD_Karaoke_SoundEffectA = 0x40,
    DVD_Karaoke_SoundEffectB = 0x80
} DVD_KARAOKE_CONTENTS;
typedef enum tagDVD_KARAOKE_ASSIGNMENT {
    DVD_Assignment_reserved0 = 0,
    DVD_Assignment_reserved1 = 1,
    DVD_Assignment_LR = 2,
    DVD_Assignment_LRM = 3,
    DVD_Assignment_LR1 = 4,
    DVD_Assignment_LRM1 = 5,
    DVD_Assignment_LR12 = 6,
    DVD_Assignment_LRM12 = 7
} DVD_KARAOKE_ASSIGNMENT;
typedef struct tagDVD_MUA_MixingInfo {
    WINBOOL fMixTo0;
    WINBOOL fMixTo1;
    WINBOOL fMix0InPhase;
    WINBOOL fMix1InPhase;
    DWORD dwSpeakerPosition;
} DVD_MUA_MixingInfo;
typedef struct tagDVD_MUA_Coeff {
    double log2_alpha;
    double log2_beta;
} DVD_MUA_Coeff;
typedef enum tagDVD_VIDEO_COMPRESSION {
    DVD_VideoCompression_Other = 0,
    DVD_VideoCompression_MPEG1 = 1,
    DVD_VideoCompression_MPEG2 = 2
} DVD_VIDEO_COMPRESSION;
typedef enum tagDVD_SUBPICTURE_TYPE {
    DVD_SPType_NotSpecified = 0,
    DVD_SPType_Language = 1,
    DVD_SPType_Other = 2
} DVD_SUBPICTURE_TYPE;
typedef enum tagDVD_SUBPICTURE_CODING {
    DVD_SPCoding_RunLength = 0,
    DVD_SPCoding_Extended = 1,
    DVD_SPCoding_Other = 2
} DVD_SUBPICTURE_CODING;
typedef enum tagDVD_TITLE_APPMODE {
    DVD_AppMode_Not_Specified = 0,
    DVD_AppMode_Karaoke = 1,
    DVD_AppMode_Other = 3
} DVD_TITLE_APPMODE;
enum DVD_TextStringType {
    DVD_Struct_Volume = 0x1,
    DVD_Struct_Title = 0x2,
    DVD_Struct_ParentalID = 0x3,
    DVD_Struct_PartOfTitle = 0x4,
    DVD_Struct_Cell = 0x5,
    DVD_Stream_Audio = 0x10,
    DVD_Stream_Subpicture = 0x11,
    DVD_Stream_Angle = 0x12,
    DVD_Channel_Audio = 0x20,
    DVD_General_Name = 0x30,
    DVD_General_Comments = 0x31,
    DVD_Title_Series = 0x38,
    DVD_Title_Movie = 0x39,
    DVD_Title_Video = 0x3a,
    DVD_Title_Album = 0x3b,
    DVD_Title_Song = 0x3c,
    DVD_Title_Other = 0x3f,
    DVD_Title_Sub_Series = 0x40,
    DVD_Title_Sub_Movie = 0x41,
    DVD_Title_Sub_Video = 0x42,
    DVD_Title_Sub_Album = 0x43,
    DVD_Title_Sub_Song = 0x44,
    DVD_Title_Sub_Other = 0x47,
    DVD_Title_Orig_Series = 0x48,
    DVD_Title_Orig_Movie = 0x49,
    DVD_Title_Orig_Video = 0x4a,
    DVD_Title_Orig_Album = 0x4b,
    DVD_Title_Orig_Song = 0x4c,
    DVD_Title_Orig_Other = 0x4f,
    DVD_Other_Scene = 0x50,
    DVD_Other_Cut = 0x51,
    DVD_Other_Take = 0x52
};
enum DVD_TextCharSet {
    DVD_CharSet_Unicode = 0,
    DVD_CharSet_ISO646 = 1,
    DVD_CharSet_JIS_Roman_Kanji = 2,
    DVD_CharSet_ISO8859_1 = 3,
    DVD_CharSet_ShiftJIS_Kanji_Roman_Katakana = 4
};
typedef struct tagDVD_AudioAttributes {
    DVD_AUDIO_APPMODE AppMode;
    BYTE AppModeData;
    DVD_AUDIO_FORMAT AudioFormat;
    LCID Language;
    DVD_AUDIO_LANG_EXT LanguageExtension;
    WINBOOL fHasMultichannelInfo;
    DWORD dwFrequency;
    BYTE bQuantization;
    BYTE bNumberOfChannels;
    DWORD dwReserved[2];
} DVD_AudioAttributes;
typedef struct tagDVD_MultichannelAudioAttributes {
    DVD_MUA_MixingInfo Info[8];
    DVD_MUA_Coeff Coeff[8];
} DVD_MultichannelAudioAttributes;
typedef struct tagDVD_VideoAttributes {
    WINBOOL fPanscanPermitted;
    WINBOOL fLetterboxPermitted;
    ULONG ulAspectX;
    ULONG ulAspectY;
    ULONG ulFrameRate;
    ULONG ulFrameHeight;
    DVD_VIDEO_COMPRESSION Compression;
    WINBOOL fLine21Field1InGOP;
    WINBOOL fLine21Field2InGOP;
    ULONG ulSourceResolutionX;
    ULONG ulSourceResolutionY;
    WINBOOL fIsSourceLetterboxed;
    WINBOOL fIsFilmMode;
} DVD_VideoAttributes;
typedef struct tagDVD_SubpictureAttributes {
    DVD_SUBPICTURE_TYPE Type;
    DVD_SUBPICTURE_CODING CodingMode;
    LCID Language;
    DVD_SUBPICTURE_LANG_EXT LanguageExtension;
} DVD_SubpictureAttributes;
typedef struct tagDVD_KaraokeAttributes {
    BYTE bVersion;
    WINBOOL fMasterOfCeremoniesInGuideVocal1;
    WINBOOL fDuet;
    DVD_KARAOKE_ASSIGNMENT ChannelAssignment;
    WORD wChannelContents[8];
} DVD_KaraokeAttributes;
typedef struct tagDVD_TitleMainAttributes {
    union {
        DVD_TITLE_APPMODE AppMode;
        DVD_HMSF_TIMECODE TitleLength;
    } DUMMYUNIONNAME;
    DVD_VideoAttributes VideoAttributes;
    ULONG ulNumberOfAudioStreams;
    DVD_AudioAttributes AudioAttributes[8];
    DVD_MultichannelAudioAttributes MultichannelAudioAttributes[8];
    ULONG ulNumberOfSubpictureStreams;
    DVD_SubpictureAttributes SubpictureAttributes[32];
} DVD_TitleAttributes;
typedef struct tagDVD_MenuAttributes {
    WINBOOL fCompatibleRegion[8];
    DVD_VideoAttributes VideoAttributes;
    WINBOOL fAudioPresent;
    DVD_AudioAttributes AudioAttributes;
    WINBOOL fSubpicturePresent;
    DVD_SubpictureAttributes SubpictureAttributes;
} DVD_MenuAttributes;
#define DVD_TITLE_MENU           0x000
#define DVD_STREAM_DATA_VMGM     0x400
#define DVD_STREAM_DATA_VTSM     0x401
#define DVD_STREAM_DATA_CURRENT  0x800
#define DVD_DEFAULT_AUDIO_STREAM 0x00f
#define DVD_AUDIO_CAPS_AC3   0x00000001
#define DVD_AUDIO_CAPS_MPEG2 0x00000002
#define DVD_AUDIO_CAPS_LPCM  0x00000004
#define DVD_AUDIO_CAPS_DTS   0x00000008
#define DVD_AUDIO_CAPS_SDDS  0x00000010
typedef struct tagDVD_DECODER_CAPS {
    DWORD dwSize;
    DWORD dwAudioCaps;
    double dFwdMaxRateVideo;
    double dFwdMaxRateAudio;
    double dFwdMaxRateSP;
    double dBwdMaxRateVideo;
    double dBwdMaxRateAudio;
    double dBwdMaxRateSP;
    DWORD dwRes1;
    DWORD dwRes2;
    DWORD dwRes3;
    DWORD dwRes4;
} DVD_DECODER_CAPS;
typedef enum _AM_DVD_GRAPH_FLAGS {
    AM_DVD_HWDEC_PREFER = 0x1,
    AM_DVD_HWDEC_ONLY = 0x2,
    AM_DVD_SWDEC_PREFER = 0x4,
    AM_DVD_SWDEC_ONLY = 0x8,
    AM_DVD_NOVPE = 0x100,
    AM_DVD_DO_NOT_CLEAR = 0x200,
    AM_DVD_VMR9_ONLY = 0x800,
    AM_DVD_EVR_ONLY = 0x1000,
    AM_DVD_EVR_QOS = 0x2000,
    AM_DVD_ADAPT_GRAPH = 0x4000,
    AM_DVD_MASK = 0xffff
} AM_DVD_GRAPH_FLAGS;
typedef enum _AM_DVD_STREAM_FLAGS {
    AM_DVD_STREAM_VIDEO = 0x1,
    AM_DVD_STREAM_AUDIO = 0x2,
    AM_DVD_STREAM_SUBPIC = 0x4
} AM_DVD_STREAM_FLAGS;
typedef struct tagAM_DVD_RENDERSTATUS {
    HRESULT hrVPEStatus;
    WINBOOL bDvdVolInvalid;
    WINBOOL bDvdVolUnknown;
    WINBOOL bNoLine21In;
    WINBOOL bNoLine21Out;
    int iNumStreams;
    int iNumStreamsFailed;
    DWORD dwFailedStreamsFlag;
} AM_DVD_RENDERSTATUS;
/*****************************************************************************
 * IDvdControl interface
 */
#ifndef __IDvdControl_INTERFACE_DEFINED__
#define __IDvdControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDvdControl, 0xa70efe61, 0xe2a3, 0x11d0, 0xa9,0xbe, 0x00,0xaa,0x00,0x61,0xbe,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a70efe61-e2a3-11d0-a9be-00aa0061be93")
IDvdControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE TitlePlay(
        ULONG title) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChapterPlay(
        ULONG title,
        ULONG chapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE TimePlay(
        ULONG title,
        ULONG time) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopForResume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GoUp(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE TimeSearch(
        ULONG time) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChapterSearch(
        ULONG chapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE PrevPGSearch(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE TopPGSearch(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE NextPGSearch(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ForwardScan(
        double speed) = 0;

    virtual HRESULT STDMETHODCALLTYPE BackwardScan(
        double speed) = 0;

    virtual HRESULT STDMETHODCALLTYPE MenuCall(
        DVD_MENU_ID id) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpperButtonSelect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE LowerButtonSelect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE LeftButtonSelect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RightButtonSelect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ButtonActivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ButtonSelectActivate(
        ULONG button) = 0;

    virtual HRESULT STDMETHODCALLTYPE StillOff(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PauseOn(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PauseOff(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE MenuLanguageSelect(
        LCID language) = 0;

    virtual HRESULT STDMETHODCALLTYPE AudioStreamChange(
        ULONG audio) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubpictureStreamChange(
        ULONG subpicture,
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE AngleChange(
        ULONG angle) = 0;

    virtual HRESULT STDMETHODCALLTYPE ParentalLevelSelect(
        ULONG level) = 0;

    virtual HRESULT STDMETHODCALLTYPE ParentalCountrySelect(
        WORD country) = 0;

    virtual HRESULT STDMETHODCALLTYPE KaraokeAudioPresentationModeChange(
        ULONG mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE VideoModePreference(
        ULONG mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRoot(
        const WCHAR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE MouseActivate(
        POINT point) = 0;

    virtual HRESULT STDMETHODCALLTYPE MouseSelect(
        POINT point) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChapterPlayAutoStop(
        ULONG title,
        ULONG chapter,
        ULONG count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDvdControl, 0xa70efe61, 0xe2a3, 0x11d0, 0xa9,0xbe, 0x00,0xaa,0x00,0x61,0xbe,0x93)
#endif
#else
typedef struct IDvdControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDvdControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDvdControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDvdControl *This);

    /*** IDvdControl methods ***/
    HRESULT (STDMETHODCALLTYPE *TitlePlay)(
        IDvdControl *This,
        ULONG title);

    HRESULT (STDMETHODCALLTYPE *ChapterPlay)(
        IDvdControl *This,
        ULONG title,
        ULONG chapter);

    HRESULT (STDMETHODCALLTYPE *TimePlay)(
        IDvdControl *This,
        ULONG title,
        ULONG time);

    HRESULT (STDMETHODCALLTYPE *StopForResume)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *GoUp)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *TimeSearch)(
        IDvdControl *This,
        ULONG time);

    HRESULT (STDMETHODCALLTYPE *ChapterSearch)(
        IDvdControl *This,
        ULONG chapter);

    HRESULT (STDMETHODCALLTYPE *PrevPGSearch)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *TopPGSearch)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *NextPGSearch)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *ForwardScan)(
        IDvdControl *This,
        double speed);

    HRESULT (STDMETHODCALLTYPE *BackwardScan)(
        IDvdControl *This,
        double speed);

    HRESULT (STDMETHODCALLTYPE *MenuCall)(
        IDvdControl *This,
        DVD_MENU_ID id);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *UpperButtonSelect)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *LowerButtonSelect)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *LeftButtonSelect)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *RightButtonSelect)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *ButtonActivate)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *ButtonSelectActivate)(
        IDvdControl *This,
        ULONG button);

    HRESULT (STDMETHODCALLTYPE *StillOff)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *PauseOn)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *PauseOff)(
        IDvdControl *This);

    HRESULT (STDMETHODCALLTYPE *MenuLanguageSelect)(
        IDvdControl *This,
        LCID language);

    HRESULT (STDMETHODCALLTYPE *AudioStreamChange)(
        IDvdControl *This,
        ULONG audio);

    HRESULT (STDMETHODCALLTYPE *SubpictureStreamChange)(
        IDvdControl *This,
        ULONG subpicture,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *AngleChange)(
        IDvdControl *This,
        ULONG angle);

    HRESULT (STDMETHODCALLTYPE *ParentalLevelSelect)(
        IDvdControl *This,
        ULONG level);

    HRESULT (STDMETHODCALLTYPE *ParentalCountrySelect)(
        IDvdControl *This,
        WORD country);

    HRESULT (STDMETHODCALLTYPE *KaraokeAudioPresentationModeChange)(
        IDvdControl *This,
        ULONG mode);

    HRESULT (STDMETHODCALLTYPE *VideoModePreference)(
        IDvdControl *This,
        ULONG mode);

    HRESULT (STDMETHODCALLTYPE *SetRoot)(
        IDvdControl *This,
        const WCHAR *path);

    HRESULT (STDMETHODCALLTYPE *MouseActivate)(
        IDvdControl *This,
        POINT point);

    HRESULT (STDMETHODCALLTYPE *MouseSelect)(
        IDvdControl *This,
        POINT point);

    HRESULT (STDMETHODCALLTYPE *ChapterPlayAutoStop)(
        IDvdControl *This,
        ULONG title,
        ULONG chapter,
        ULONG count);

    END_INTERFACE
} IDvdControlVtbl;

interface IDvdControl {
    CONST_VTBL IDvdControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDvdControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDvdControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDvdControl_Release(This) (This)->lpVtbl->Release(This)
/*** IDvdControl methods ***/
#define IDvdControl_TitlePlay(This,title) (This)->lpVtbl->TitlePlay(This,title)
#define IDvdControl_ChapterPlay(This,title,chapter) (This)->lpVtbl->ChapterPlay(This,title,chapter)
#define IDvdControl_TimePlay(This,title,time) (This)->lpVtbl->TimePlay(This,title,time)
#define IDvdControl_StopForResume(This) (This)->lpVtbl->StopForResume(This)
#define IDvdControl_GoUp(This) (This)->lpVtbl->GoUp(This)
#define IDvdControl_TimeSearch(This,time) (This)->lpVtbl->TimeSearch(This,time)
#define IDvdControl_ChapterSearch(This,chapter) (This)->lpVtbl->ChapterSearch(This,chapter)
#define IDvdControl_PrevPGSearch(This) (This)->lpVtbl->PrevPGSearch(This)
#define IDvdControl_TopPGSearch(This) (This)->lpVtbl->TopPGSearch(This)
#define IDvdControl_NextPGSearch(This) (This)->lpVtbl->NextPGSearch(This)
#define IDvdControl_ForwardScan(This,speed) (This)->lpVtbl->ForwardScan(This,speed)
#define IDvdControl_BackwardScan(This,speed) (This)->lpVtbl->BackwardScan(This,speed)
#define IDvdControl_MenuCall(This,id) (This)->lpVtbl->MenuCall(This,id)
#define IDvdControl_Resume(This) (This)->lpVtbl->Resume(This)
#define IDvdControl_UpperButtonSelect(This) (This)->lpVtbl->UpperButtonSelect(This)
#define IDvdControl_LowerButtonSelect(This) (This)->lpVtbl->LowerButtonSelect(This)
#define IDvdControl_LeftButtonSelect(This) (This)->lpVtbl->LeftButtonSelect(This)
#define IDvdControl_RightButtonSelect(This) (This)->lpVtbl->RightButtonSelect(This)
#define IDvdControl_ButtonActivate(This) (This)->lpVtbl->ButtonActivate(This)
#define IDvdControl_ButtonSelectActivate(This,button) (This)->lpVtbl->ButtonSelectActivate(This,button)
#define IDvdControl_StillOff(This) (This)->lpVtbl->StillOff(This)
#define IDvdControl_PauseOn(This) (This)->lpVtbl->PauseOn(This)
#define IDvdControl_PauseOff(This) (This)->lpVtbl->PauseOff(This)
#define IDvdControl_MenuLanguageSelect(This,language) (This)->lpVtbl->MenuLanguageSelect(This,language)
#define IDvdControl_AudioStreamChange(This,audio) (This)->lpVtbl->AudioStreamChange(This,audio)
#define IDvdControl_SubpictureStreamChange(This,subpicture,enable) (This)->lpVtbl->SubpictureStreamChange(This,subpicture,enable)
#define IDvdControl_AngleChange(This,angle) (This)->lpVtbl->AngleChange(This,angle)
#define IDvdControl_ParentalLevelSelect(This,level) (This)->lpVtbl->ParentalLevelSelect(This,level)
#define IDvdControl_ParentalCountrySelect(This,country) (This)->lpVtbl->ParentalCountrySelect(This,country)
#define IDvdControl_KaraokeAudioPresentationModeChange(This,mode) (This)->lpVtbl->KaraokeAudioPresentationModeChange(This,mode)
#define IDvdControl_VideoModePreference(This,mode) (This)->lpVtbl->VideoModePreference(This,mode)
#define IDvdControl_SetRoot(This,path) (This)->lpVtbl->SetRoot(This,path)
#define IDvdControl_MouseActivate(This,point) (This)->lpVtbl->MouseActivate(This,point)
#define IDvdControl_MouseSelect(This,point) (This)->lpVtbl->MouseSelect(This,point)
#define IDvdControl_ChapterPlayAutoStop(This,title,chapter,count) (This)->lpVtbl->ChapterPlayAutoStop(This,title,chapter,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IDvdControl_QueryInterface(IDvdControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDvdControl_AddRef(IDvdControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDvdControl_Release(IDvdControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IDvdControl methods ***/
static inline HRESULT IDvdControl_TitlePlay(IDvdControl* This,ULONG title) {
    return This->lpVtbl->TitlePlay(This,title);
}
static inline HRESULT IDvdControl_ChapterPlay(IDvdControl* This,ULONG title,ULONG chapter) {
    return This->lpVtbl->ChapterPlay(This,title,chapter);
}
static inline HRESULT IDvdControl_TimePlay(IDvdControl* This,ULONG title,ULONG time) {
    return This->lpVtbl->TimePlay(This,title,time);
}
static inline HRESULT IDvdControl_StopForResume(IDvdControl* This) {
    return This->lpVtbl->StopForResume(This);
}
static inline HRESULT IDvdControl_GoUp(IDvdControl* This) {
    return This->lpVtbl->GoUp(This);
}
static inline HRESULT IDvdControl_TimeSearch(IDvdControl* This,ULONG time) {
    return This->lpVtbl->TimeSearch(This,time);
}
static inline HRESULT IDvdControl_ChapterSearch(IDvdControl* This,ULONG chapter) {
    return This->lpVtbl->ChapterSearch(This,chapter);
}
static inline HRESULT IDvdControl_PrevPGSearch(IDvdControl* This) {
    return This->lpVtbl->PrevPGSearch(This);
}
static inline HRESULT IDvdControl_TopPGSearch(IDvdControl* This) {
    return This->lpVtbl->TopPGSearch(This);
}
static inline HRESULT IDvdControl_NextPGSearch(IDvdControl* This) {
    return This->lpVtbl->NextPGSearch(This);
}
static inline HRESULT IDvdControl_ForwardScan(IDvdControl* This,double speed) {
    return This->lpVtbl->ForwardScan(This,speed);
}
static inline HRESULT IDvdControl_BackwardScan(IDvdControl* This,double speed) {
    return This->lpVtbl->BackwardScan(This,speed);
}
static inline HRESULT IDvdControl_MenuCall(IDvdControl* This,DVD_MENU_ID id) {
    return This->lpVtbl->MenuCall(This,id);
}
static inline HRESULT IDvdControl_Resume(IDvdControl* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IDvdControl_UpperButtonSelect(IDvdControl* This) {
    return This->lpVtbl->UpperButtonSelect(This);
}
static inline HRESULT IDvdControl_LowerButtonSelect(IDvdControl* This) {
    return This->lpVtbl->LowerButtonSelect(This);
}
static inline HRESULT IDvdControl_LeftButtonSelect(IDvdControl* This) {
    return This->lpVtbl->LeftButtonSelect(This);
}
static inline HRESULT IDvdControl_RightButtonSelect(IDvdControl* This) {
    return This->lpVtbl->RightButtonSelect(This);
}
static inline HRESULT IDvdControl_ButtonActivate(IDvdControl* This) {
    return This->lpVtbl->ButtonActivate(This);
}
static inline HRESULT IDvdControl_ButtonSelectActivate(IDvdControl* This,ULONG button) {
    return This->lpVtbl->ButtonSelectActivate(This,button);
}
static inline HRESULT IDvdControl_StillOff(IDvdControl* This) {
    return This->lpVtbl->StillOff(This);
}
static inline HRESULT IDvdControl_PauseOn(IDvdControl* This) {
    return This->lpVtbl->PauseOn(This);
}
static inline HRESULT IDvdControl_PauseOff(IDvdControl* This) {
    return This->lpVtbl->PauseOff(This);
}
static inline HRESULT IDvdControl_MenuLanguageSelect(IDvdControl* This,LCID language) {
    return This->lpVtbl->MenuLanguageSelect(This,language);
}
static inline HRESULT IDvdControl_AudioStreamChange(IDvdControl* This,ULONG audio) {
    return This->lpVtbl->AudioStreamChange(This,audio);
}
static inline HRESULT IDvdControl_SubpictureStreamChange(IDvdControl* This,ULONG subpicture,WINBOOL enable) {
    return This->lpVtbl->SubpictureStreamChange(This,subpicture,enable);
}
static inline HRESULT IDvdControl_AngleChange(IDvdControl* This,ULONG angle) {
    return This->lpVtbl->AngleChange(This,angle);
}
static inline HRESULT IDvdControl_ParentalLevelSelect(IDvdControl* This,ULONG level) {
    return This->lpVtbl->ParentalLevelSelect(This,level);
}
static inline HRESULT IDvdControl_ParentalCountrySelect(IDvdControl* This,WORD country) {
    return This->lpVtbl->ParentalCountrySelect(This,country);
}
static inline HRESULT IDvdControl_KaraokeAudioPresentationModeChange(IDvdControl* This,ULONG mode) {
    return This->lpVtbl->KaraokeAudioPresentationModeChange(This,mode);
}
static inline HRESULT IDvdControl_VideoModePreference(IDvdControl* This,ULONG mode) {
    return This->lpVtbl->VideoModePreference(This,mode);
}
static inline HRESULT IDvdControl_SetRoot(IDvdControl* This,const WCHAR *path) {
    return This->lpVtbl->SetRoot(This,path);
}
static inline HRESULT IDvdControl_MouseActivate(IDvdControl* This,POINT point) {
    return This->lpVtbl->MouseActivate(This,point);
}
static inline HRESULT IDvdControl_MouseSelect(IDvdControl* This,POINT point) {
    return This->lpVtbl->MouseSelect(This,point);
}
static inline HRESULT IDvdControl_ChapterPlayAutoStop(IDvdControl* This,ULONG title,ULONG chapter,ULONG count) {
    return This->lpVtbl->ChapterPlayAutoStop(This,title,chapter,count);
}
#endif
#endif

#endif


#endif  /* __IDvdControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDvdInfo interface
 */
#ifndef __IDvdInfo_INTERFACE_DEFINED__
#define __IDvdInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDvdInfo, 0xa70efe60, 0xe2a3, 0x11d0, 0xa9,0xbe, 0x00,0xaa,0x00,0x61,0xbe,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a70efe60-e2a3-11d0-a9be-00aa0061be93")
IDvdInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCurrentDomain(
        DVD_DOMAIN *domain) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentLocation(
        DVD_PLAYBACK_LOCATION *location) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTotalTitleTime(
        ULONG *time) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentButton(
        ULONG *count,
        ULONG *current) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentAngle(
        ULONG *count,
        ULONG *current) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentAudio(
        ULONG *count,
        ULONG *current) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentSubpicture(
        ULONG *count,
        ULONG *current,
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentUOPS(
        VALID_UOP_SOMTHING_OR_OTHER *uops) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllSPRMs(
        SPRMARRAY *regs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllGPRMs(
        GPRMARRAY *regs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAudioLanguage(
        ULONG stream,
        LCID *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubpictureLanguage(
        ULONG stream,
        LCID *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTitleAttributes(
        ULONG title,
        DVD_ATR *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVMGAttributes(
        DVD_ATR *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentVideoAttributes(
        DVD_VideoATR *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentAudioAttributes(
        DVD_AudioATR *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentSubpictureAttributes(
        DVD_SubpictureATR *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentVolumeInfo(
        ULONG *volume_count,
        ULONG *current,
        DVD_DISC_SIDE *side,
        ULONG *title_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDVDTextInfo(
        BYTE *text_manager,
        ULONG size,
        ULONG *ret_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPlayerParentalLevel(
        ULONG *level,
        ULONG *country_code) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberOfChapters(
        ULONG title,
        ULONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTitleParentalLevels(
        ULONG title,
        ULONG *levels) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRoot(
        char *path,
        ULONG size,
        ULONG *ret_size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDvdInfo, 0xa70efe60, 0xe2a3, 0x11d0, 0xa9,0xbe, 0x00,0xaa,0x00,0x61,0xbe,0x93)
#endif
#else
typedef struct IDvdInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDvdInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDvdInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDvdInfo *This);

    /*** IDvdInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCurrentDomain)(
        IDvdInfo *This,
        DVD_DOMAIN *domain);

    HRESULT (STDMETHODCALLTYPE *GetCurrentLocation)(
        IDvdInfo *This,
        DVD_PLAYBACK_LOCATION *location);

    HRESULT (STDMETHODCALLTYPE *GetTotalTitleTime)(
        IDvdInfo *This,
        ULONG *time);

    HRESULT (STDMETHODCALLTYPE *GetCurrentButton)(
        IDvdInfo *This,
        ULONG *count,
        ULONG *current);

    HRESULT (STDMETHODCALLTYPE *GetCurrentAngle)(
        IDvdInfo *This,
        ULONG *count,
        ULONG *current);

    HRESULT (STDMETHODCALLTYPE *GetCurrentAudio)(
        IDvdInfo *This,
        ULONG *count,
        ULONG *current);

    HRESULT (STDMETHODCALLTYPE *GetCurrentSubpicture)(
        IDvdInfo *This,
        ULONG *count,
        ULONG *current,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *GetCurrentUOPS)(
        IDvdInfo *This,
        VALID_UOP_SOMTHING_OR_OTHER *uops);

    HRESULT (STDMETHODCALLTYPE *GetAllSPRMs)(
        IDvdInfo *This,
        SPRMARRAY *regs);

    HRESULT (STDMETHODCALLTYPE *GetAllGPRMs)(
        IDvdInfo *This,
        GPRMARRAY *regs);

    HRESULT (STDMETHODCALLTYPE *GetAudioLanguage)(
        IDvdInfo *This,
        ULONG stream,
        LCID *language);

    HRESULT (STDMETHODCALLTYPE *GetSubpictureLanguage)(
        IDvdInfo *This,
        ULONG stream,
        LCID *language);

    HRESULT (STDMETHODCALLTYPE *GetTitleAttributes)(
        IDvdInfo *This,
        ULONG title,
        DVD_ATR *attr);

    HRESULT (STDMETHODCALLTYPE *GetVMGAttributes)(
        IDvdInfo *This,
        DVD_ATR *attr);

    HRESULT (STDMETHODCALLTYPE *GetCurrentVideoAttributes)(
        IDvdInfo *This,
        DVD_VideoATR *attr);

    HRESULT (STDMETHODCALLTYPE *GetCurrentAudioAttributes)(
        IDvdInfo *This,
        DVD_AudioATR *attr);

    HRESULT (STDMETHODCALLTYPE *GetCurrentSubpictureAttributes)(
        IDvdInfo *This,
        DVD_SubpictureATR *attr);

    HRESULT (STDMETHODCALLTYPE *GetCurrentVolumeInfo)(
        IDvdInfo *This,
        ULONG *volume_count,
        ULONG *current,
        DVD_DISC_SIDE *side,
        ULONG *title_count);

    HRESULT (STDMETHODCALLTYPE *GetDVDTextInfo)(
        IDvdInfo *This,
        BYTE *text_manager,
        ULONG size,
        ULONG *ret_size);

    HRESULT (STDMETHODCALLTYPE *GetPlayerParentalLevel)(
        IDvdInfo *This,
        ULONG *level,
        ULONG *country_code);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfChapters)(
        IDvdInfo *This,
        ULONG title,
        ULONG *count);

    HRESULT (STDMETHODCALLTYPE *GetTitleParentalLevels)(
        IDvdInfo *This,
        ULONG title,
        ULONG *levels);

    HRESULT (STDMETHODCALLTYPE *GetRoot)(
        IDvdInfo *This,
        char *path,
        ULONG size,
        ULONG *ret_size);

    END_INTERFACE
} IDvdInfoVtbl;

interface IDvdInfo {
    CONST_VTBL IDvdInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDvdInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDvdInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDvdInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IDvdInfo methods ***/
#define IDvdInfo_GetCurrentDomain(This,domain) (This)->lpVtbl->GetCurrentDomain(This,domain)
#define IDvdInfo_GetCurrentLocation(This,location) (This)->lpVtbl->GetCurrentLocation(This,location)
#define IDvdInfo_GetTotalTitleTime(This,time) (This)->lpVtbl->GetTotalTitleTime(This,time)
#define IDvdInfo_GetCurrentButton(This,count,current) (This)->lpVtbl->GetCurrentButton(This,count,current)
#define IDvdInfo_GetCurrentAngle(This,count,current) (This)->lpVtbl->GetCurrentAngle(This,count,current)
#define IDvdInfo_GetCurrentAudio(This,count,current) (This)->lpVtbl->GetCurrentAudio(This,count,current)
#define IDvdInfo_GetCurrentSubpicture(This,count,current,enable) (This)->lpVtbl->GetCurrentSubpicture(This,count,current,enable)
#define IDvdInfo_GetCurrentUOPS(This,uops) (This)->lpVtbl->GetCurrentUOPS(This,uops)
#define IDvdInfo_GetAllSPRMs(This,regs) (This)->lpVtbl->GetAllSPRMs(This,regs)
#define IDvdInfo_GetAllGPRMs(This,regs) (This)->lpVtbl->GetAllGPRMs(This,regs)
#define IDvdInfo_GetAudioLanguage(This,stream,language) (This)->lpVtbl->GetAudioLanguage(This,stream,language)
#define IDvdInfo_GetSubpictureLanguage(This,stream,language) (This)->lpVtbl->GetSubpictureLanguage(This,stream,language)
#define IDvdInfo_GetTitleAttributes(This,title,attr) (This)->lpVtbl->GetTitleAttributes(This,title,attr)
#define IDvdInfo_GetVMGAttributes(This,attr) (This)->lpVtbl->GetVMGAttributes(This,attr)
#define IDvdInfo_GetCurrentVideoAttributes(This,attr) (This)->lpVtbl->GetCurrentVideoAttributes(This,attr)
#define IDvdInfo_GetCurrentAudioAttributes(This,attr) (This)->lpVtbl->GetCurrentAudioAttributes(This,attr)
#define IDvdInfo_GetCurrentSubpictureAttributes(This,attr) (This)->lpVtbl->GetCurrentSubpictureAttributes(This,attr)
#define IDvdInfo_GetCurrentVolumeInfo(This,volume_count,current,side,title_count) (This)->lpVtbl->GetCurrentVolumeInfo(This,volume_count,current,side,title_count)
#define IDvdInfo_GetDVDTextInfo(This,text_manager,size,ret_size) (This)->lpVtbl->GetDVDTextInfo(This,text_manager,size,ret_size)
#define IDvdInfo_GetPlayerParentalLevel(This,level,country_code) (This)->lpVtbl->GetPlayerParentalLevel(This,level,country_code)
#define IDvdInfo_GetNumberOfChapters(This,title,count) (This)->lpVtbl->GetNumberOfChapters(This,title,count)
#define IDvdInfo_GetTitleParentalLevels(This,title,levels) (This)->lpVtbl->GetTitleParentalLevels(This,title,levels)
#define IDvdInfo_GetRoot(This,path,size,ret_size) (This)->lpVtbl->GetRoot(This,path,size,ret_size)
#else
/*** IUnknown methods ***/
static inline HRESULT IDvdInfo_QueryInterface(IDvdInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDvdInfo_AddRef(IDvdInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDvdInfo_Release(IDvdInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IDvdInfo methods ***/
static inline HRESULT IDvdInfo_GetCurrentDomain(IDvdInfo* This,DVD_DOMAIN *domain) {
    return This->lpVtbl->GetCurrentDomain(This,domain);
}
static inline HRESULT IDvdInfo_GetCurrentLocation(IDvdInfo* This,DVD_PLAYBACK_LOCATION *location) {
    return This->lpVtbl->GetCurrentLocation(This,location);
}
static inline HRESULT IDvdInfo_GetTotalTitleTime(IDvdInfo* This,ULONG *time) {
    return This->lpVtbl->GetTotalTitleTime(This,time);
}
static inline HRESULT IDvdInfo_GetCurrentButton(IDvdInfo* This,ULONG *count,ULONG *current) {
    return This->lpVtbl->GetCurrentButton(This,count,current);
}
static inline HRESULT IDvdInfo_GetCurrentAngle(IDvdInfo* This,ULONG *count,ULONG *current) {
    return This->lpVtbl->GetCurrentAngle(This,count,current);
}
static inline HRESULT IDvdInfo_GetCurrentAudio(IDvdInfo* This,ULONG *count,ULONG *current) {
    return This->lpVtbl->GetCurrentAudio(This,count,current);
}
static inline HRESULT IDvdInfo_GetCurrentSubpicture(IDvdInfo* This,ULONG *count,ULONG *current,WINBOOL *enable) {
    return This->lpVtbl->GetCurrentSubpicture(This,count,current,enable);
}
static inline HRESULT IDvdInfo_GetCurrentUOPS(IDvdInfo* This,VALID_UOP_SOMTHING_OR_OTHER *uops) {
    return This->lpVtbl->GetCurrentUOPS(This,uops);
}
static inline HRESULT IDvdInfo_GetAllSPRMs(IDvdInfo* This,SPRMARRAY *regs) {
    return This->lpVtbl->GetAllSPRMs(This,regs);
}
static inline HRESULT IDvdInfo_GetAllGPRMs(IDvdInfo* This,GPRMARRAY *regs) {
    return This->lpVtbl->GetAllGPRMs(This,regs);
}
static inline HRESULT IDvdInfo_GetAudioLanguage(IDvdInfo* This,ULONG stream,LCID *language) {
    return This->lpVtbl->GetAudioLanguage(This,stream,language);
}
static inline HRESULT IDvdInfo_GetSubpictureLanguage(IDvdInfo* This,ULONG stream,LCID *language) {
    return This->lpVtbl->GetSubpictureLanguage(This,stream,language);
}
static inline HRESULT IDvdInfo_GetTitleAttributes(IDvdInfo* This,ULONG title,DVD_ATR *attr) {
    return This->lpVtbl->GetTitleAttributes(This,title,attr);
}
static inline HRESULT IDvdInfo_GetVMGAttributes(IDvdInfo* This,DVD_ATR *attr) {
    return This->lpVtbl->GetVMGAttributes(This,attr);
}
static inline HRESULT IDvdInfo_GetCurrentVideoAttributes(IDvdInfo* This,DVD_VideoATR *attr) {
    return This->lpVtbl->GetCurrentVideoAttributes(This,attr);
}
static inline HRESULT IDvdInfo_GetCurrentAudioAttributes(IDvdInfo* This,DVD_AudioATR *attr) {
    return This->lpVtbl->GetCurrentAudioAttributes(This,attr);
}
static inline HRESULT IDvdInfo_GetCurrentSubpictureAttributes(IDvdInfo* This,DVD_SubpictureATR *attr) {
    return This->lpVtbl->GetCurrentSubpictureAttributes(This,attr);
}
static inline HRESULT IDvdInfo_GetCurrentVolumeInfo(IDvdInfo* This,ULONG *volume_count,ULONG *current,DVD_DISC_SIDE *side,ULONG *title_count) {
    return This->lpVtbl->GetCurrentVolumeInfo(This,volume_count,current,side,title_count);
}
static inline HRESULT IDvdInfo_GetDVDTextInfo(IDvdInfo* This,BYTE *text_manager,ULONG size,ULONG *ret_size) {
    return This->lpVtbl->GetDVDTextInfo(This,text_manager,size,ret_size);
}
static inline HRESULT IDvdInfo_GetPlayerParentalLevel(IDvdInfo* This,ULONG *level,ULONG *country_code) {
    return This->lpVtbl->GetPlayerParentalLevel(This,level,country_code);
}
static inline HRESULT IDvdInfo_GetNumberOfChapters(IDvdInfo* This,ULONG title,ULONG *count) {
    return This->lpVtbl->GetNumberOfChapters(This,title,count);
}
static inline HRESULT IDvdInfo_GetTitleParentalLevels(IDvdInfo* This,ULONG title,ULONG *levels) {
    return This->lpVtbl->GetTitleParentalLevels(This,title,levels);
}
static inline HRESULT IDvdInfo_GetRoot(IDvdInfo* This,char *path,ULONG size,ULONG *ret_size) {
    return This->lpVtbl->GetRoot(This,path,size,ret_size);
}
#endif
#endif

#endif


#endif  /* __IDvdInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDvdCmd interface
 */
#ifndef __IDvdCmd_INTERFACE_DEFINED__
#define __IDvdCmd_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDvdCmd, 0x5a4a97e4, 0x94ee, 0x4a55, 0x97,0x51, 0x74,0xb5,0x64,0x3a,0xa2,0x7d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5a4a97e4-94ee-4a55-9751-74b5643aa27d")
IDvdCmd : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE WaitForStart(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitForEnd(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDvdCmd, 0x5a4a97e4, 0x94ee, 0x4a55, 0x97,0x51, 0x74,0xb5,0x64,0x3a,0xa2,0x7d)
#endif
#else
typedef struct IDvdCmdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDvdCmd *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDvdCmd *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDvdCmd *This);

    /*** IDvdCmd methods ***/
    HRESULT (STDMETHODCALLTYPE *WaitForStart)(
        IDvdCmd *This);

    HRESULT (STDMETHODCALLTYPE *WaitForEnd)(
        IDvdCmd *This);

    END_INTERFACE
} IDvdCmdVtbl;

interface IDvdCmd {
    CONST_VTBL IDvdCmdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDvdCmd_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDvdCmd_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDvdCmd_Release(This) (This)->lpVtbl->Release(This)
/*** IDvdCmd methods ***/
#define IDvdCmd_WaitForStart(This) (This)->lpVtbl->WaitForStart(This)
#define IDvdCmd_WaitForEnd(This) (This)->lpVtbl->WaitForEnd(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDvdCmd_QueryInterface(IDvdCmd* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDvdCmd_AddRef(IDvdCmd* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDvdCmd_Release(IDvdCmd* This) {
    return This->lpVtbl->Release(This);
}
/*** IDvdCmd methods ***/
static inline HRESULT IDvdCmd_WaitForStart(IDvdCmd* This) {
    return This->lpVtbl->WaitForStart(This);
}
static inline HRESULT IDvdCmd_WaitForEnd(IDvdCmd* This) {
    return This->lpVtbl->WaitForEnd(This);
}
#endif
#endif

#endif


#endif  /* __IDvdCmd_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDvdState interface
 */
#ifndef __IDvdState_INTERFACE_DEFINED__
#define __IDvdState_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDvdState, 0x86303d6d, 0x1c4a, 0x4087, 0xab,0x42, 0xf7,0x11,0x16,0x70,0x48,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("86303d6d-1c4a-4087-ab42-f711167048ef")
IDvdState : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDiscID(
        ULONGLONG *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParentalLevel(
        ULONG *level) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDvdState, 0x86303d6d, 0x1c4a, 0x4087, 0xab,0x42, 0xf7,0x11,0x16,0x70,0x48,0xef)
#endif
#else
typedef struct IDvdStateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDvdState *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDvdState *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDvdState *This);

    /*** IDvdState methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDiscID)(
        IDvdState *This,
        ULONGLONG *id);

    HRESULT (STDMETHODCALLTYPE *GetParentalLevel)(
        IDvdState *This,
        ULONG *level);

    END_INTERFACE
} IDvdStateVtbl;

interface IDvdState {
    CONST_VTBL IDvdStateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDvdState_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDvdState_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDvdState_Release(This) (This)->lpVtbl->Release(This)
/*** IDvdState methods ***/
#define IDvdState_GetDiscID(This,id) (This)->lpVtbl->GetDiscID(This,id)
#define IDvdState_GetParentalLevel(This,level) (This)->lpVtbl->GetParentalLevel(This,level)
#else
/*** IUnknown methods ***/
static inline HRESULT IDvdState_QueryInterface(IDvdState* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDvdState_AddRef(IDvdState* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDvdState_Release(IDvdState* This) {
    return This->lpVtbl->Release(This);
}
/*** IDvdState methods ***/
static inline HRESULT IDvdState_GetDiscID(IDvdState* This,ULONGLONG *id) {
    return This->lpVtbl->GetDiscID(This,id);
}
static inline HRESULT IDvdState_GetParentalLevel(IDvdState* This,ULONG *level) {
    return This->lpVtbl->GetParentalLevel(This,level);
}
#endif
#endif

#endif


#endif  /* __IDvdState_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDvdControl2 interface
 */
#ifndef __IDvdControl2_INTERFACE_DEFINED__
#define __IDvdControl2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDvdControl2, 0x33bc7430, 0xeec0, 0x11d2, 0x82,0x01, 0x00,0xa0,0xc9,0xd7,0x48,0x42);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("33bc7430-eec0-11d2-8201-00a0c9d74842")
IDvdControl2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PlayTitle(
        ULONG title,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayChapterInTitle(
        ULONG title,
        ULONG chapter,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayTimeInTitle(
        ULONG title,
        DVD_HMSF_TIMECODE *time,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReturnFromSubmenu(
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayAtTime(
        DVD_HMSF_TIMECODE *time,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayChapter(
        ULONG chapter,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayPrevChapter(
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReplayChapter(
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayNextChapter(
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayForwards(
        double speed,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayBackwards(
        double speed,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowMenu(
        DVD_MENU_ID id,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectRelativeButton(
        DVD_RELATIVE_BUTTON button) = 0;

    virtual HRESULT STDMETHODCALLTYPE ActivateButton(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectButton(
        ULONG button) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectAndActivateButton(
        ULONG button) = 0;

    virtual HRESULT STDMETHODCALLTYPE StillOff(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectAudioStream(
        ULONG stream,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectSubpictureStream(
        ULONG stream,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSubpictureState(
        WINBOOL enable,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectAngle(
        ULONG angle,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectParentalLevel(
        ULONG level) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectParentalCountry(
        BYTE country[2]) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectKaraokeAudioPresentationMode(
        ULONG mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectVideoModePreference(
        ULONG mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDVDDirectory(
        const WCHAR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE ActivateAtPosition(
        POINT point) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectAtPosition(
        POINT point) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayChaptersAutoStop(
        ULONG title,
        ULONG chapter,
        ULONG count,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE AcceptParentalLevelChange(
        WINBOOL accept) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOption(
        DVD_OPTION_FLAG flag,
        WINBOOL option) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetState(
        IDvdState *state,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlayPeriodInTitleAutoStop(
        ULONG title,
        DVD_HMSF_TIMECODE *start_time,
        DVD_HMSF_TIMECODE *end_time,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGRPM(
        ULONG index,
        WORD value,
        DWORD flags,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDefaultMenuLanguage(
        LCID language) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDefaultAudioLanguage(
        LCID language,
        DVD_AUDIO_LANG_EXT extension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDefaultSubpictureLanguage(
        LCID language,
        DVD_SUBPICTURE_LANG_EXT extension) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDvdControl2, 0x33bc7430, 0xeec0, 0x11d2, 0x82,0x01, 0x00,0xa0,0xc9,0xd7,0x48,0x42)
#endif
#else
typedef struct IDvdControl2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDvdControl2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDvdControl2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDvdControl2 *This);

    /*** IDvdControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *PlayTitle)(
        IDvdControl2 *This,
        ULONG title,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayChapterInTitle)(
        IDvdControl2 *This,
        ULONG title,
        ULONG chapter,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayTimeInTitle)(
        IDvdControl2 *This,
        ULONG title,
        DVD_HMSF_TIMECODE *time,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IDvdControl2 *This);

    HRESULT (STDMETHODCALLTYPE *ReturnFromSubmenu)(
        IDvdControl2 *This,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayAtTime)(
        IDvdControl2 *This,
        DVD_HMSF_TIMECODE *time,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayChapter)(
        IDvdControl2 *This,
        ULONG chapter,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayPrevChapter)(
        IDvdControl2 *This,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *ReplayChapter)(
        IDvdControl2 *This,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayNextChapter)(
        IDvdControl2 *This,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayForwards)(
        IDvdControl2 *This,
        double speed,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayBackwards)(
        IDvdControl2 *This,
        double speed,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *ShowMenu)(
        IDvdControl2 *This,
        DVD_MENU_ID id,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IDvdControl2 *This,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *SelectRelativeButton)(
        IDvdControl2 *This,
        DVD_RELATIVE_BUTTON button);

    HRESULT (STDMETHODCALLTYPE *ActivateButton)(
        IDvdControl2 *This);

    HRESULT (STDMETHODCALLTYPE *SelectButton)(
        IDvdControl2 *This,
        ULONG button);

    HRESULT (STDMETHODCALLTYPE *SelectAndActivateButton)(
        IDvdControl2 *This,
        ULONG button);

    HRESULT (STDMETHODCALLTYPE *StillOff)(
        IDvdControl2 *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IDvdControl2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *SelectAudioStream)(
        IDvdControl2 *This,
        ULONG stream,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *SelectSubpictureStream)(
        IDvdControl2 *This,
        ULONG stream,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *SetSubpictureState)(
        IDvdControl2 *This,
        WINBOOL enable,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *SelectAngle)(
        IDvdControl2 *This,
        ULONG angle,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *SelectParentalLevel)(
        IDvdControl2 *This,
        ULONG level);

    HRESULT (STDMETHODCALLTYPE *SelectParentalCountry)(
        IDvdControl2 *This,
        BYTE country[2]);

    HRESULT (STDMETHODCALLTYPE *SelectKaraokeAudioPresentationMode)(
        IDvdControl2 *This,
        ULONG mode);

    HRESULT (STDMETHODCALLTYPE *SelectVideoModePreference)(
        IDvdControl2 *This,
        ULONG mode);

    HRESULT (STDMETHODCALLTYPE *SetDVDDirectory)(
        IDvdControl2 *This,
        const WCHAR *path);

    HRESULT (STDMETHODCALLTYPE *ActivateAtPosition)(
        IDvdControl2 *This,
        POINT point);

    HRESULT (STDMETHODCALLTYPE *SelectAtPosition)(
        IDvdControl2 *This,
        POINT point);

    HRESULT (STDMETHODCALLTYPE *PlayChaptersAutoStop)(
        IDvdControl2 *This,
        ULONG title,
        ULONG chapter,
        ULONG count,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *AcceptParentalLevelChange)(
        IDvdControl2 *This,
        WINBOOL accept);

    HRESULT (STDMETHODCALLTYPE *SetOption)(
        IDvdControl2 *This,
        DVD_OPTION_FLAG flag,
        WINBOOL option);

    HRESULT (STDMETHODCALLTYPE *SetState)(
        IDvdControl2 *This,
        IDvdState *state,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *PlayPeriodInTitleAutoStop)(
        IDvdControl2 *This,
        ULONG title,
        DVD_HMSF_TIMECODE *start_time,
        DVD_HMSF_TIMECODE *end_time,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *SetGRPM)(
        IDvdControl2 *This,
        ULONG index,
        WORD value,
        DWORD flags,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *SelectDefaultMenuLanguage)(
        IDvdControl2 *This,
        LCID language);

    HRESULT (STDMETHODCALLTYPE *SelectDefaultAudioLanguage)(
        IDvdControl2 *This,
        LCID language,
        DVD_AUDIO_LANG_EXT extension);

    HRESULT (STDMETHODCALLTYPE *SelectDefaultSubpictureLanguage)(
        IDvdControl2 *This,
        LCID language,
        DVD_SUBPICTURE_LANG_EXT extension);

    END_INTERFACE
} IDvdControl2Vtbl;

interface IDvdControl2 {
    CONST_VTBL IDvdControl2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDvdControl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDvdControl2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDvdControl2_Release(This) (This)->lpVtbl->Release(This)
/*** IDvdControl2 methods ***/
#define IDvdControl2_PlayTitle(This,title,flags,cmd) (This)->lpVtbl->PlayTitle(This,title,flags,cmd)
#define IDvdControl2_PlayChapterInTitle(This,title,chapter,flags,cmd) (This)->lpVtbl->PlayChapterInTitle(This,title,chapter,flags,cmd)
#define IDvdControl2_PlayTimeInTitle(This,title,time,flags,cmd) (This)->lpVtbl->PlayTimeInTitle(This,title,time,flags,cmd)
#define IDvdControl2_Stop(This) (This)->lpVtbl->Stop(This)
#define IDvdControl2_ReturnFromSubmenu(This,flags,cmd) (This)->lpVtbl->ReturnFromSubmenu(This,flags,cmd)
#define IDvdControl2_PlayAtTime(This,time,flags,cmd) (This)->lpVtbl->PlayAtTime(This,time,flags,cmd)
#define IDvdControl2_PlayChapter(This,chapter,flags,cmd) (This)->lpVtbl->PlayChapter(This,chapter,flags,cmd)
#define IDvdControl2_PlayPrevChapter(This,flags,cmd) (This)->lpVtbl->PlayPrevChapter(This,flags,cmd)
#define IDvdControl2_ReplayChapter(This,flags,cmd) (This)->lpVtbl->ReplayChapter(This,flags,cmd)
#define IDvdControl2_PlayNextChapter(This,flags,cmd) (This)->lpVtbl->PlayNextChapter(This,flags,cmd)
#define IDvdControl2_PlayForwards(This,speed,flags,cmd) (This)->lpVtbl->PlayForwards(This,speed,flags,cmd)
#define IDvdControl2_PlayBackwards(This,speed,flags,cmd) (This)->lpVtbl->PlayBackwards(This,speed,flags,cmd)
#define IDvdControl2_ShowMenu(This,id,flags,cmd) (This)->lpVtbl->ShowMenu(This,id,flags,cmd)
#define IDvdControl2_Resume(This,flags,cmd) (This)->lpVtbl->Resume(This,flags,cmd)
#define IDvdControl2_SelectRelativeButton(This,button) (This)->lpVtbl->SelectRelativeButton(This,button)
#define IDvdControl2_ActivateButton(This) (This)->lpVtbl->ActivateButton(This)
#define IDvdControl2_SelectButton(This,button) (This)->lpVtbl->SelectButton(This,button)
#define IDvdControl2_SelectAndActivateButton(This,button) (This)->lpVtbl->SelectAndActivateButton(This,button)
#define IDvdControl2_StillOff(This) (This)->lpVtbl->StillOff(This)
#define IDvdControl2_Pause(This,enable) (This)->lpVtbl->Pause(This,enable)
#define IDvdControl2_SelectAudioStream(This,stream,flags,cmd) (This)->lpVtbl->SelectAudioStream(This,stream,flags,cmd)
#define IDvdControl2_SelectSubpictureStream(This,stream,flags,cmd) (This)->lpVtbl->SelectSubpictureStream(This,stream,flags,cmd)
#define IDvdControl2_SetSubpictureState(This,enable,flags,cmd) (This)->lpVtbl->SetSubpictureState(This,enable,flags,cmd)
#define IDvdControl2_SelectAngle(This,angle,flags,cmd) (This)->lpVtbl->SelectAngle(This,angle,flags,cmd)
#define IDvdControl2_SelectParentalLevel(This,level) (This)->lpVtbl->SelectParentalLevel(This,level)
#define IDvdControl2_SelectParentalCountry(This,country) (This)->lpVtbl->SelectParentalCountry(This,country)
#define IDvdControl2_SelectKaraokeAudioPresentationMode(This,mode) (This)->lpVtbl->SelectKaraokeAudioPresentationMode(This,mode)
#define IDvdControl2_SelectVideoModePreference(This,mode) (This)->lpVtbl->SelectVideoModePreference(This,mode)
#define IDvdControl2_SetDVDDirectory(This,path) (This)->lpVtbl->SetDVDDirectory(This,path)
#define IDvdControl2_ActivateAtPosition(This,point) (This)->lpVtbl->ActivateAtPosition(This,point)
#define IDvdControl2_SelectAtPosition(This,point) (This)->lpVtbl->SelectAtPosition(This,point)
#define IDvdControl2_PlayChaptersAutoStop(This,title,chapter,count,flags,cmd) (This)->lpVtbl->PlayChaptersAutoStop(This,title,chapter,count,flags,cmd)
#define IDvdControl2_AcceptParentalLevelChange(This,accept) (This)->lpVtbl->AcceptParentalLevelChange(This,accept)
#define IDvdControl2_SetOption(This,flag,option) (This)->lpVtbl->SetOption(This,flag,option)
#define IDvdControl2_SetState(This,state,flags,cmd) (This)->lpVtbl->SetState(This,state,flags,cmd)
#define IDvdControl2_PlayPeriodInTitleAutoStop(This,title,start_time,end_time,flags,cmd) (This)->lpVtbl->PlayPeriodInTitleAutoStop(This,title,start_time,end_time,flags,cmd)
#define IDvdControl2_SetGRPM(This,index,value,flags,cmd) (This)->lpVtbl->SetGRPM(This,index,value,flags,cmd)
#define IDvdControl2_SelectDefaultMenuLanguage(This,language) (This)->lpVtbl->SelectDefaultMenuLanguage(This,language)
#define IDvdControl2_SelectDefaultAudioLanguage(This,language,extension) (This)->lpVtbl->SelectDefaultAudioLanguage(This,language,extension)
#define IDvdControl2_SelectDefaultSubpictureLanguage(This,language,extension) (This)->lpVtbl->SelectDefaultSubpictureLanguage(This,language,extension)
#else
/*** IUnknown methods ***/
static inline HRESULT IDvdControl2_QueryInterface(IDvdControl2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDvdControl2_AddRef(IDvdControl2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDvdControl2_Release(IDvdControl2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDvdControl2 methods ***/
static inline HRESULT IDvdControl2_PlayTitle(IDvdControl2* This,ULONG title,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayTitle(This,title,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayChapterInTitle(IDvdControl2* This,ULONG title,ULONG chapter,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayChapterInTitle(This,title,chapter,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayTimeInTitle(IDvdControl2* This,ULONG title,DVD_HMSF_TIMECODE *time,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayTimeInTitle(This,title,time,flags,cmd);
}
static inline HRESULT IDvdControl2_Stop(IDvdControl2* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IDvdControl2_ReturnFromSubmenu(IDvdControl2* This,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->ReturnFromSubmenu(This,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayAtTime(IDvdControl2* This,DVD_HMSF_TIMECODE *time,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayAtTime(This,time,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayChapter(IDvdControl2* This,ULONG chapter,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayChapter(This,chapter,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayPrevChapter(IDvdControl2* This,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayPrevChapter(This,flags,cmd);
}
static inline HRESULT IDvdControl2_ReplayChapter(IDvdControl2* This,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->ReplayChapter(This,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayNextChapter(IDvdControl2* This,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayNextChapter(This,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayForwards(IDvdControl2* This,double speed,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayForwards(This,speed,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayBackwards(IDvdControl2* This,double speed,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayBackwards(This,speed,flags,cmd);
}
static inline HRESULT IDvdControl2_ShowMenu(IDvdControl2* This,DVD_MENU_ID id,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->ShowMenu(This,id,flags,cmd);
}
static inline HRESULT IDvdControl2_Resume(IDvdControl2* This,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->Resume(This,flags,cmd);
}
static inline HRESULT IDvdControl2_SelectRelativeButton(IDvdControl2* This,DVD_RELATIVE_BUTTON button) {
    return This->lpVtbl->SelectRelativeButton(This,button);
}
static inline HRESULT IDvdControl2_ActivateButton(IDvdControl2* This) {
    return This->lpVtbl->ActivateButton(This);
}
static inline HRESULT IDvdControl2_SelectButton(IDvdControl2* This,ULONG button) {
    return This->lpVtbl->SelectButton(This,button);
}
static inline HRESULT IDvdControl2_SelectAndActivateButton(IDvdControl2* This,ULONG button) {
    return This->lpVtbl->SelectAndActivateButton(This,button);
}
static inline HRESULT IDvdControl2_StillOff(IDvdControl2* This) {
    return This->lpVtbl->StillOff(This);
}
static inline HRESULT IDvdControl2_Pause(IDvdControl2* This,WINBOOL enable) {
    return This->lpVtbl->Pause(This,enable);
}
static inline HRESULT IDvdControl2_SelectAudioStream(IDvdControl2* This,ULONG stream,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->SelectAudioStream(This,stream,flags,cmd);
}
static inline HRESULT IDvdControl2_SelectSubpictureStream(IDvdControl2* This,ULONG stream,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->SelectSubpictureStream(This,stream,flags,cmd);
}
static inline HRESULT IDvdControl2_SetSubpictureState(IDvdControl2* This,WINBOOL enable,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->SetSubpictureState(This,enable,flags,cmd);
}
static inline HRESULT IDvdControl2_SelectAngle(IDvdControl2* This,ULONG angle,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->SelectAngle(This,angle,flags,cmd);
}
static inline HRESULT IDvdControl2_SelectParentalLevel(IDvdControl2* This,ULONG level) {
    return This->lpVtbl->SelectParentalLevel(This,level);
}
static inline HRESULT IDvdControl2_SelectParentalCountry(IDvdControl2* This,BYTE country[2]) {
    return This->lpVtbl->SelectParentalCountry(This,country);
}
static inline HRESULT IDvdControl2_SelectKaraokeAudioPresentationMode(IDvdControl2* This,ULONG mode) {
    return This->lpVtbl->SelectKaraokeAudioPresentationMode(This,mode);
}
static inline HRESULT IDvdControl2_SelectVideoModePreference(IDvdControl2* This,ULONG mode) {
    return This->lpVtbl->SelectVideoModePreference(This,mode);
}
static inline HRESULT IDvdControl2_SetDVDDirectory(IDvdControl2* This,const WCHAR *path) {
    return This->lpVtbl->SetDVDDirectory(This,path);
}
static inline HRESULT IDvdControl2_ActivateAtPosition(IDvdControl2* This,POINT point) {
    return This->lpVtbl->ActivateAtPosition(This,point);
}
static inline HRESULT IDvdControl2_SelectAtPosition(IDvdControl2* This,POINT point) {
    return This->lpVtbl->SelectAtPosition(This,point);
}
static inline HRESULT IDvdControl2_PlayChaptersAutoStop(IDvdControl2* This,ULONG title,ULONG chapter,ULONG count,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayChaptersAutoStop(This,title,chapter,count,flags,cmd);
}
static inline HRESULT IDvdControl2_AcceptParentalLevelChange(IDvdControl2* This,WINBOOL accept) {
    return This->lpVtbl->AcceptParentalLevelChange(This,accept);
}
static inline HRESULT IDvdControl2_SetOption(IDvdControl2* This,DVD_OPTION_FLAG flag,WINBOOL option) {
    return This->lpVtbl->SetOption(This,flag,option);
}
static inline HRESULT IDvdControl2_SetState(IDvdControl2* This,IDvdState *state,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->SetState(This,state,flags,cmd);
}
static inline HRESULT IDvdControl2_PlayPeriodInTitleAutoStop(IDvdControl2* This,ULONG title,DVD_HMSF_TIMECODE *start_time,DVD_HMSF_TIMECODE *end_time,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->PlayPeriodInTitleAutoStop(This,title,start_time,end_time,flags,cmd);
}
static inline HRESULT IDvdControl2_SetGRPM(IDvdControl2* This,ULONG index,WORD value,DWORD flags,IDvdCmd **cmd) {
    return This->lpVtbl->SetGRPM(This,index,value,flags,cmd);
}
static inline HRESULT IDvdControl2_SelectDefaultMenuLanguage(IDvdControl2* This,LCID language) {
    return This->lpVtbl->SelectDefaultMenuLanguage(This,language);
}
static inline HRESULT IDvdControl2_SelectDefaultAudioLanguage(IDvdControl2* This,LCID language,DVD_AUDIO_LANG_EXT extension) {
    return This->lpVtbl->SelectDefaultAudioLanguage(This,language,extension);
}
static inline HRESULT IDvdControl2_SelectDefaultSubpictureLanguage(IDvdControl2* This,LCID language,DVD_SUBPICTURE_LANG_EXT extension) {
    return This->lpVtbl->SelectDefaultSubpictureLanguage(This,language,extension);
}
#endif
#endif

#endif


#endif  /* __IDvdControl2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDvdInfo2 interface
 */
#ifndef __IDvdInfo2_INTERFACE_DEFINED__
#define __IDvdInfo2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDvdInfo2, 0x34151510, 0xeec0, 0x11d2, 0x82,0x01, 0x00,0xa0,0xc9,0xd7,0x48,0x42);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("34151510-eec0-11d2-8201-00a0c9d74842")
IDvdInfo2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCurrentDomain(
        DVD_DOMAIN *domain) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentLocation(
        DVD_PLAYBACK_LOCATION2 *location) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTotalTitleTime(
        DVD_HMSF_TIMECODE *time,
        ULONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentButton(
        ULONG *count,
        ULONG *current) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentAngle(
        ULONG *count,
        ULONG *current) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentAudio(
        ULONG *count,
        ULONG *current) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentSubpicture(
        ULONG *count,
        ULONG *current,
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentUOPS(
        ULONG *uops) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllSPRMs(
        SPRMARRAY *regs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllGPRMs(
        GPRMARRAY *regs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAudioLanguage(
        ULONG stream,
        LCID *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubpictureLanguage(
        ULONG stream,
        LCID *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTitleAttributes(
        ULONG index,
        DVD_MenuAttributes *menu,
        DVD_TitleAttributes *title) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVMGAttributes(
        DVD_MenuAttributes *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoAttributes(
        DVD_VideoAttributes *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAudioAttributes(
        ULONG stream,
        DVD_AudioAttributes *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetKaraokeAttributes(
        ULONG stream,
        DVD_KaraokeAttributes *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubpictureAttributes(
        ULONG stream,
        DVD_SubpictureAttributes *attr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentVolumeInfo(
        ULONG *volume_count,
        ULONG *current,
        DVD_DISC_SIDE *side,
        ULONG *title_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDVDTextNumberOfLanguages(
        ULONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDVDTextLanguageInfo(
        ULONG index,
        ULONG *string_count,
        LCID *language,
        enum DVD_TextCharSet *character_set) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDVDTextStringAsNative(
        ULONG lang_index,
        ULONG string_index,
        BYTE *string,
        ULONG size,
        ULONG *ret_size,
        enum DVD_TextStringType *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDVDTextStringAsUnicode(
        ULONG lang_index,
        ULONG string_index,
        WCHAR *string,
        ULONG size,
        ULONG *ret_size,
        enum DVD_TextStringType *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPlayerParentalLevel(
        ULONG *level,
        BYTE country_code[2]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberOfChapters(
        ULONG title,
        ULONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTitleParentalLevels(
        ULONG title,
        ULONG *levels) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDVDDirectory(
        WCHAR *path,
        ULONG size,
        ULONG *ret_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsAudioStreamEnabled(
        ULONG stream,
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDiscID(
        const WCHAR *path,
        ULONGLONG *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        IDvdState **state) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMenuLanguages(
        LCID *languages,
        ULONG count,
        ULONG *ret_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetButtonAtPosition(
        POINT point,
        ULONG *button) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCmdFromEvent(
        LONG_PTR param,
        IDvdCmd **cmd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultMenuLanguage(
        LCID *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultAudioLanguage(
        LCID *language,
        DVD_AUDIO_LANG_EXT *extension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDefaultSubpictureLanguage(
        LCID *language,
        DVD_SUBPICTURE_LANG_EXT *extension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDecoderCaps(
        DVD_DECODER_CAPS *caps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetButtonRect(
        ULONG button,
        RECT *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSubpictureStreamEnabled(
        ULONG stream,
        WINBOOL *enable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDvdInfo2, 0x34151510, 0xeec0, 0x11d2, 0x82,0x01, 0x00,0xa0,0xc9,0xd7,0x48,0x42)
#endif
#else
typedef struct IDvdInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDvdInfo2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDvdInfo2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDvdInfo2 *This);

    /*** IDvdInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCurrentDomain)(
        IDvdInfo2 *This,
        DVD_DOMAIN *domain);

    HRESULT (STDMETHODCALLTYPE *GetCurrentLocation)(
        IDvdInfo2 *This,
        DVD_PLAYBACK_LOCATION2 *location);

    HRESULT (STDMETHODCALLTYPE *GetTotalTitleTime)(
        IDvdInfo2 *This,
        DVD_HMSF_TIMECODE *time,
        ULONG *flags);

    HRESULT (STDMETHODCALLTYPE *GetCurrentButton)(
        IDvdInfo2 *This,
        ULONG *count,
        ULONG *current);

    HRESULT (STDMETHODCALLTYPE *GetCurrentAngle)(
        IDvdInfo2 *This,
        ULONG *count,
        ULONG *current);

    HRESULT (STDMETHODCALLTYPE *GetCurrentAudio)(
        IDvdInfo2 *This,
        ULONG *count,
        ULONG *current);

    HRESULT (STDMETHODCALLTYPE *GetCurrentSubpicture)(
        IDvdInfo2 *This,
        ULONG *count,
        ULONG *current,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *GetCurrentUOPS)(
        IDvdInfo2 *This,
        ULONG *uops);

    HRESULT (STDMETHODCALLTYPE *GetAllSPRMs)(
        IDvdInfo2 *This,
        SPRMARRAY *regs);

    HRESULT (STDMETHODCALLTYPE *GetAllGPRMs)(
        IDvdInfo2 *This,
        GPRMARRAY *regs);

    HRESULT (STDMETHODCALLTYPE *GetAudioLanguage)(
        IDvdInfo2 *This,
        ULONG stream,
        LCID *language);

    HRESULT (STDMETHODCALLTYPE *GetSubpictureLanguage)(
        IDvdInfo2 *This,
        ULONG stream,
        LCID *language);

    HRESULT (STDMETHODCALLTYPE *GetTitleAttributes)(
        IDvdInfo2 *This,
        ULONG index,
        DVD_MenuAttributes *menu,
        DVD_TitleAttributes *title);

    HRESULT (STDMETHODCALLTYPE *GetVMGAttributes)(
        IDvdInfo2 *This,
        DVD_MenuAttributes *attr);

    HRESULT (STDMETHODCALLTYPE *GetVideoAttributes)(
        IDvdInfo2 *This,
        DVD_VideoAttributes *attr);

    HRESULT (STDMETHODCALLTYPE *GetAudioAttributes)(
        IDvdInfo2 *This,
        ULONG stream,
        DVD_AudioAttributes *attr);

    HRESULT (STDMETHODCALLTYPE *GetKaraokeAttributes)(
        IDvdInfo2 *This,
        ULONG stream,
        DVD_KaraokeAttributes *attr);

    HRESULT (STDMETHODCALLTYPE *GetSubpictureAttributes)(
        IDvdInfo2 *This,
        ULONG stream,
        DVD_SubpictureAttributes *attr);

    HRESULT (STDMETHODCALLTYPE *GetCurrentVolumeInfo)(
        IDvdInfo2 *This,
        ULONG *volume_count,
        ULONG *current,
        DVD_DISC_SIDE *side,
        ULONG *title_count);

    HRESULT (STDMETHODCALLTYPE *GetDVDTextNumberOfLanguages)(
        IDvdInfo2 *This,
        ULONG *count);

    HRESULT (STDMETHODCALLTYPE *GetDVDTextLanguageInfo)(
        IDvdInfo2 *This,
        ULONG index,
        ULONG *string_count,
        LCID *language,
        enum DVD_TextCharSet *character_set);

    HRESULT (STDMETHODCALLTYPE *GetDVDTextStringAsNative)(
        IDvdInfo2 *This,
        ULONG lang_index,
        ULONG string_index,
        BYTE *string,
        ULONG size,
        ULONG *ret_size,
        enum DVD_TextStringType *type);

    HRESULT (STDMETHODCALLTYPE *GetDVDTextStringAsUnicode)(
        IDvdInfo2 *This,
        ULONG lang_index,
        ULONG string_index,
        WCHAR *string,
        ULONG size,
        ULONG *ret_size,
        enum DVD_TextStringType *type);

    HRESULT (STDMETHODCALLTYPE *GetPlayerParentalLevel)(
        IDvdInfo2 *This,
        ULONG *level,
        BYTE country_code[2]);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfChapters)(
        IDvdInfo2 *This,
        ULONG title,
        ULONG *count);

    HRESULT (STDMETHODCALLTYPE *GetTitleParentalLevels)(
        IDvdInfo2 *This,
        ULONG title,
        ULONG *levels);

    HRESULT (STDMETHODCALLTYPE *GetDVDDirectory)(
        IDvdInfo2 *This,
        WCHAR *path,
        ULONG size,
        ULONG *ret_size);

    HRESULT (STDMETHODCALLTYPE *IsAudioStreamEnabled)(
        IDvdInfo2 *This,
        ULONG stream,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *GetDiscID)(
        IDvdInfo2 *This,
        const WCHAR *path,
        ULONGLONG *id);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IDvdInfo2 *This,
        IDvdState **state);

    HRESULT (STDMETHODCALLTYPE *GetMenuLanguages)(
        IDvdInfo2 *This,
        LCID *languages,
        ULONG count,
        ULONG *ret_count);

    HRESULT (STDMETHODCALLTYPE *GetButtonAtPosition)(
        IDvdInfo2 *This,
        POINT point,
        ULONG *button);

    HRESULT (STDMETHODCALLTYPE *GetCmdFromEvent)(
        IDvdInfo2 *This,
        LONG_PTR param,
        IDvdCmd **cmd);

    HRESULT (STDMETHODCALLTYPE *GetDefaultMenuLanguage)(
        IDvdInfo2 *This,
        LCID *language);

    HRESULT (STDMETHODCALLTYPE *GetDefaultAudioLanguage)(
        IDvdInfo2 *This,
        LCID *language,
        DVD_AUDIO_LANG_EXT *extension);

    HRESULT (STDMETHODCALLTYPE *SelectDefaultSubpictureLanguage)(
        IDvdInfo2 *This,
        LCID *language,
        DVD_SUBPICTURE_LANG_EXT *extension);

    HRESULT (STDMETHODCALLTYPE *GetDecoderCaps)(
        IDvdInfo2 *This,
        DVD_DECODER_CAPS *caps);

    HRESULT (STDMETHODCALLTYPE *GetButtonRect)(
        IDvdInfo2 *This,
        ULONG button,
        RECT *rect);

    HRESULT (STDMETHODCALLTYPE *IsSubpictureStreamEnabled)(
        IDvdInfo2 *This,
        ULONG stream,
        WINBOOL *enable);

    END_INTERFACE
} IDvdInfo2Vtbl;

interface IDvdInfo2 {
    CONST_VTBL IDvdInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDvdInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDvdInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDvdInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** IDvdInfo2 methods ***/
#define IDvdInfo2_GetCurrentDomain(This,domain) (This)->lpVtbl->GetCurrentDomain(This,domain)
#define IDvdInfo2_GetCurrentLocation(This,location) (This)->lpVtbl->GetCurrentLocation(This,location)
#define IDvdInfo2_GetTotalTitleTime(This,time,flags) (This)->lpVtbl->GetTotalTitleTime(This,time,flags)
#define IDvdInfo2_GetCurrentButton(This,count,current) (This)->lpVtbl->GetCurrentButton(This,count,current)
#define IDvdInfo2_GetCurrentAngle(This,count,current) (This)->lpVtbl->GetCurrentAngle(This,count,current)
#define IDvdInfo2_GetCurrentAudio(This,count,current) (This)->lpVtbl->GetCurrentAudio(This,count,current)
#define IDvdInfo2_GetCurrentSubpicture(This,count,current,enable) (This)->lpVtbl->GetCurrentSubpicture(This,count,current,enable)
#define IDvdInfo2_GetCurrentUOPS(This,uops) (This)->lpVtbl->GetCurrentUOPS(This,uops)
#define IDvdInfo2_GetAllSPRMs(This,regs) (This)->lpVtbl->GetAllSPRMs(This,regs)
#define IDvdInfo2_GetAllGPRMs(This,regs) (This)->lpVtbl->GetAllGPRMs(This,regs)
#define IDvdInfo2_GetAudioLanguage(This,stream,language) (This)->lpVtbl->GetAudioLanguage(This,stream,language)
#define IDvdInfo2_GetSubpictureLanguage(This,stream,language) (This)->lpVtbl->GetSubpictureLanguage(This,stream,language)
#define IDvdInfo2_GetTitleAttributes(This,index,menu,title) (This)->lpVtbl->GetTitleAttributes(This,index,menu,title)
#define IDvdInfo2_GetVMGAttributes(This,attr) (This)->lpVtbl->GetVMGAttributes(This,attr)
#define IDvdInfo2_GetVideoAttributes(This,attr) (This)->lpVtbl->GetVideoAttributes(This,attr)
#define IDvdInfo2_GetAudioAttributes(This,stream,attr) (This)->lpVtbl->GetAudioAttributes(This,stream,attr)
#define IDvdInfo2_GetKaraokeAttributes(This,stream,attr) (This)->lpVtbl->GetKaraokeAttributes(This,stream,attr)
#define IDvdInfo2_GetSubpictureAttributes(This,stream,attr) (This)->lpVtbl->GetSubpictureAttributes(This,stream,attr)
#define IDvdInfo2_GetCurrentVolumeInfo(This,volume_count,current,side,title_count) (This)->lpVtbl->GetCurrentVolumeInfo(This,volume_count,current,side,title_count)
#define IDvdInfo2_GetDVDTextNumberOfLanguages(This,count) (This)->lpVtbl->GetDVDTextNumberOfLanguages(This,count)
#define IDvdInfo2_GetDVDTextLanguageInfo(This,index,string_count,language,character_set) (This)->lpVtbl->GetDVDTextLanguageInfo(This,index,string_count,language,character_set)
#define IDvdInfo2_GetDVDTextStringAsNative(This,lang_index,string_index,string,size,ret_size,type) (This)->lpVtbl->GetDVDTextStringAsNative(This,lang_index,string_index,string,size,ret_size,type)
#define IDvdInfo2_GetDVDTextStringAsUnicode(This,lang_index,string_index,string,size,ret_size,type) (This)->lpVtbl->GetDVDTextStringAsUnicode(This,lang_index,string_index,string,size,ret_size,type)
#define IDvdInfo2_GetPlayerParentalLevel(This,level,country_code) (This)->lpVtbl->GetPlayerParentalLevel(This,level,country_code)
#define IDvdInfo2_GetNumberOfChapters(This,title,count) (This)->lpVtbl->GetNumberOfChapters(This,title,count)
#define IDvdInfo2_GetTitleParentalLevels(This,title,levels) (This)->lpVtbl->GetTitleParentalLevels(This,title,levels)
#define IDvdInfo2_GetDVDDirectory(This,path,size,ret_size) (This)->lpVtbl->GetDVDDirectory(This,path,size,ret_size)
#define IDvdInfo2_IsAudioStreamEnabled(This,stream,enable) (This)->lpVtbl->IsAudioStreamEnabled(This,stream,enable)
#define IDvdInfo2_GetDiscID(This,path,id) (This)->lpVtbl->GetDiscID(This,path,id)
#define IDvdInfo2_GetState(This,state) (This)->lpVtbl->GetState(This,state)
#define IDvdInfo2_GetMenuLanguages(This,languages,count,ret_count) (This)->lpVtbl->GetMenuLanguages(This,languages,count,ret_count)
#define IDvdInfo2_GetButtonAtPosition(This,point,button) (This)->lpVtbl->GetButtonAtPosition(This,point,button)
#define IDvdInfo2_GetCmdFromEvent(This,param,cmd) (This)->lpVtbl->GetCmdFromEvent(This,param,cmd)
#define IDvdInfo2_GetDefaultMenuLanguage(This,language) (This)->lpVtbl->GetDefaultMenuLanguage(This,language)
#define IDvdInfo2_GetDefaultAudioLanguage(This,language,extension) (This)->lpVtbl->GetDefaultAudioLanguage(This,language,extension)
#define IDvdInfo2_SelectDefaultSubpictureLanguage(This,language,extension) (This)->lpVtbl->SelectDefaultSubpictureLanguage(This,language,extension)
#define IDvdInfo2_GetDecoderCaps(This,caps) (This)->lpVtbl->GetDecoderCaps(This,caps)
#define IDvdInfo2_GetButtonRect(This,button,rect) (This)->lpVtbl->GetButtonRect(This,button,rect)
#define IDvdInfo2_IsSubpictureStreamEnabled(This,stream,enable) (This)->lpVtbl->IsSubpictureStreamEnabled(This,stream,enable)
#else
/*** IUnknown methods ***/
static inline HRESULT IDvdInfo2_QueryInterface(IDvdInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDvdInfo2_AddRef(IDvdInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDvdInfo2_Release(IDvdInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDvdInfo2 methods ***/
static inline HRESULT IDvdInfo2_GetCurrentDomain(IDvdInfo2* This,DVD_DOMAIN *domain) {
    return This->lpVtbl->GetCurrentDomain(This,domain);
}
static inline HRESULT IDvdInfo2_GetCurrentLocation(IDvdInfo2* This,DVD_PLAYBACK_LOCATION2 *location) {
    return This->lpVtbl->GetCurrentLocation(This,location);
}
static inline HRESULT IDvdInfo2_GetTotalTitleTime(IDvdInfo2* This,DVD_HMSF_TIMECODE *time,ULONG *flags) {
    return This->lpVtbl->GetTotalTitleTime(This,time,flags);
}
static inline HRESULT IDvdInfo2_GetCurrentButton(IDvdInfo2* This,ULONG *count,ULONG *current) {
    return This->lpVtbl->GetCurrentButton(This,count,current);
}
static inline HRESULT IDvdInfo2_GetCurrentAngle(IDvdInfo2* This,ULONG *count,ULONG *current) {
    return This->lpVtbl->GetCurrentAngle(This,count,current);
}
static inline HRESULT IDvdInfo2_GetCurrentAudio(IDvdInfo2* This,ULONG *count,ULONG *current) {
    return This->lpVtbl->GetCurrentAudio(This,count,current);
}
static inline HRESULT IDvdInfo2_GetCurrentSubpicture(IDvdInfo2* This,ULONG *count,ULONG *current,WINBOOL *enable) {
    return This->lpVtbl->GetCurrentSubpicture(This,count,current,enable);
}
static inline HRESULT IDvdInfo2_GetCurrentUOPS(IDvdInfo2* This,ULONG *uops) {
    return This->lpVtbl->GetCurrentUOPS(This,uops);
}
static inline HRESULT IDvdInfo2_GetAllSPRMs(IDvdInfo2* This,SPRMARRAY *regs) {
    return This->lpVtbl->GetAllSPRMs(This,regs);
}
static inline HRESULT IDvdInfo2_GetAllGPRMs(IDvdInfo2* This,GPRMARRAY *regs) {
    return This->lpVtbl->GetAllGPRMs(This,regs);
}
static inline HRESULT IDvdInfo2_GetAudioLanguage(IDvdInfo2* This,ULONG stream,LCID *language) {
    return This->lpVtbl->GetAudioLanguage(This,stream,language);
}
static inline HRESULT IDvdInfo2_GetSubpictureLanguage(IDvdInfo2* This,ULONG stream,LCID *language) {
    return This->lpVtbl->GetSubpictureLanguage(This,stream,language);
}
static inline HRESULT IDvdInfo2_GetTitleAttributes(IDvdInfo2* This,ULONG index,DVD_MenuAttributes *menu,DVD_TitleAttributes *title) {
    return This->lpVtbl->GetTitleAttributes(This,index,menu,title);
}
static inline HRESULT IDvdInfo2_GetVMGAttributes(IDvdInfo2* This,DVD_MenuAttributes *attr) {
    return This->lpVtbl->GetVMGAttributes(This,attr);
}
static inline HRESULT IDvdInfo2_GetVideoAttributes(IDvdInfo2* This,DVD_VideoAttributes *attr) {
    return This->lpVtbl->GetVideoAttributes(This,attr);
}
static inline HRESULT IDvdInfo2_GetAudioAttributes(IDvdInfo2* This,ULONG stream,DVD_AudioAttributes *attr) {
    return This->lpVtbl->GetAudioAttributes(This,stream,attr);
}
static inline HRESULT IDvdInfo2_GetKaraokeAttributes(IDvdInfo2* This,ULONG stream,DVD_KaraokeAttributes *attr) {
    return This->lpVtbl->GetKaraokeAttributes(This,stream,attr);
}
static inline HRESULT IDvdInfo2_GetSubpictureAttributes(IDvdInfo2* This,ULONG stream,DVD_SubpictureAttributes *attr) {
    return This->lpVtbl->GetSubpictureAttributes(This,stream,attr);
}
static inline HRESULT IDvdInfo2_GetCurrentVolumeInfo(IDvdInfo2* This,ULONG *volume_count,ULONG *current,DVD_DISC_SIDE *side,ULONG *title_count) {
    return This->lpVtbl->GetCurrentVolumeInfo(This,volume_count,current,side,title_count);
}
static inline HRESULT IDvdInfo2_GetDVDTextNumberOfLanguages(IDvdInfo2* This,ULONG *count) {
    return This->lpVtbl->GetDVDTextNumberOfLanguages(This,count);
}
static inline HRESULT IDvdInfo2_GetDVDTextLanguageInfo(IDvdInfo2* This,ULONG index,ULONG *string_count,LCID *language,enum DVD_TextCharSet *character_set) {
    return This->lpVtbl->GetDVDTextLanguageInfo(This,index,string_count,language,character_set);
}
static inline HRESULT IDvdInfo2_GetDVDTextStringAsNative(IDvdInfo2* This,ULONG lang_index,ULONG string_index,BYTE *string,ULONG size,ULONG *ret_size,enum DVD_TextStringType *type) {
    return This->lpVtbl->GetDVDTextStringAsNative(This,lang_index,string_index,string,size,ret_size,type);
}
static inline HRESULT IDvdInfo2_GetDVDTextStringAsUnicode(IDvdInfo2* This,ULONG lang_index,ULONG string_index,WCHAR *string,ULONG size,ULONG *ret_size,enum DVD_TextStringType *type) {
    return This->lpVtbl->GetDVDTextStringAsUnicode(This,lang_index,string_index,string,size,ret_size,type);
}
static inline HRESULT IDvdInfo2_GetPlayerParentalLevel(IDvdInfo2* This,ULONG *level,BYTE country_code[2]) {
    return This->lpVtbl->GetPlayerParentalLevel(This,level,country_code);
}
static inline HRESULT IDvdInfo2_GetNumberOfChapters(IDvdInfo2* This,ULONG title,ULONG *count) {
    return This->lpVtbl->GetNumberOfChapters(This,title,count);
}
static inline HRESULT IDvdInfo2_GetTitleParentalLevels(IDvdInfo2* This,ULONG title,ULONG *levels) {
    return This->lpVtbl->GetTitleParentalLevels(This,title,levels);
}
static inline HRESULT IDvdInfo2_GetDVDDirectory(IDvdInfo2* This,WCHAR *path,ULONG size,ULONG *ret_size) {
    return This->lpVtbl->GetDVDDirectory(This,path,size,ret_size);
}
static inline HRESULT IDvdInfo2_IsAudioStreamEnabled(IDvdInfo2* This,ULONG stream,WINBOOL *enable) {
    return This->lpVtbl->IsAudioStreamEnabled(This,stream,enable);
}
static inline HRESULT IDvdInfo2_GetDiscID(IDvdInfo2* This,const WCHAR *path,ULONGLONG *id) {
    return This->lpVtbl->GetDiscID(This,path,id);
}
static inline HRESULT IDvdInfo2_GetState(IDvdInfo2* This,IDvdState **state) {
    return This->lpVtbl->GetState(This,state);
}
static inline HRESULT IDvdInfo2_GetMenuLanguages(IDvdInfo2* This,LCID *languages,ULONG count,ULONG *ret_count) {
    return This->lpVtbl->GetMenuLanguages(This,languages,count,ret_count);
}
static inline HRESULT IDvdInfo2_GetButtonAtPosition(IDvdInfo2* This,POINT point,ULONG *button) {
    return This->lpVtbl->GetButtonAtPosition(This,point,button);
}
static inline HRESULT IDvdInfo2_GetCmdFromEvent(IDvdInfo2* This,LONG_PTR param,IDvdCmd **cmd) {
    return This->lpVtbl->GetCmdFromEvent(This,param,cmd);
}
static inline HRESULT IDvdInfo2_GetDefaultMenuLanguage(IDvdInfo2* This,LCID *language) {
    return This->lpVtbl->GetDefaultMenuLanguage(This,language);
}
static inline HRESULT IDvdInfo2_GetDefaultAudioLanguage(IDvdInfo2* This,LCID *language,DVD_AUDIO_LANG_EXT *extension) {
    return This->lpVtbl->GetDefaultAudioLanguage(This,language,extension);
}
static inline HRESULT IDvdInfo2_SelectDefaultSubpictureLanguage(IDvdInfo2* This,LCID *language,DVD_SUBPICTURE_LANG_EXT *extension) {
    return This->lpVtbl->SelectDefaultSubpictureLanguage(This,language,extension);
}
static inline HRESULT IDvdInfo2_GetDecoderCaps(IDvdInfo2* This,DVD_DECODER_CAPS *caps) {
    return This->lpVtbl->GetDecoderCaps(This,caps);
}
static inline HRESULT IDvdInfo2_GetButtonRect(IDvdInfo2* This,ULONG button,RECT *rect) {
    return This->lpVtbl->GetButtonRect(This,button,rect);
}
static inline HRESULT IDvdInfo2_IsSubpictureStreamEnabled(IDvdInfo2* This,ULONG stream,WINBOOL *enable) {
    return This->lpVtbl->IsSubpictureStreamEnabled(This,stream,enable);
}
#endif
#endif

#endif


#endif  /* __IDvdInfo2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDvdGraphBuilder interface
 */
#ifndef __IDvdGraphBuilder_INTERFACE_DEFINED__
#define __IDvdGraphBuilder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDvdGraphBuilder, 0xfcc152b6, 0xf372, 0x11d0, 0x8e,0x00, 0x00,0xc0,0x4f,0xd7,0xc0,0x8b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fcc152b6-f372-11d0-8e00-00c04fd7c08b")
IDvdGraphBuilder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetFiltergraph(
        IGraphBuilder **graph) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDvdInterface(
        REFIID iid,
        void **out) = 0;

    virtual HRESULT STDMETHODCALLTYPE RenderDvdVideoVolume(
        const WCHAR *path,
        DWORD flags,
        AM_DVD_RENDERSTATUS *status) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDvdGraphBuilder, 0xfcc152b6, 0xf372, 0x11d0, 0x8e,0x00, 0x00,0xc0,0x4f,0xd7,0xc0,0x8b)
#endif
#else
typedef struct IDvdGraphBuilderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDvdGraphBuilder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDvdGraphBuilder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDvdGraphBuilder *This);

    /*** IDvdGraphBuilder methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFiltergraph)(
        IDvdGraphBuilder *This,
        IGraphBuilder **graph);

    HRESULT (STDMETHODCALLTYPE *GetDvdInterface)(
        IDvdGraphBuilder *This,
        REFIID iid,
        void **out);

    HRESULT (STDMETHODCALLTYPE *RenderDvdVideoVolume)(
        IDvdGraphBuilder *This,
        const WCHAR *path,
        DWORD flags,
        AM_DVD_RENDERSTATUS *status);

    END_INTERFACE
} IDvdGraphBuilderVtbl;

interface IDvdGraphBuilder {
    CONST_VTBL IDvdGraphBuilderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDvdGraphBuilder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDvdGraphBuilder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDvdGraphBuilder_Release(This) (This)->lpVtbl->Release(This)
/*** IDvdGraphBuilder methods ***/
#define IDvdGraphBuilder_GetFiltergraph(This,graph) (This)->lpVtbl->GetFiltergraph(This,graph)
#define IDvdGraphBuilder_GetDvdInterface(This,iid,out) (This)->lpVtbl->GetDvdInterface(This,iid,out)
#define IDvdGraphBuilder_RenderDvdVideoVolume(This,path,flags,status) (This)->lpVtbl->RenderDvdVideoVolume(This,path,flags,status)
#else
/*** IUnknown methods ***/
static inline HRESULT IDvdGraphBuilder_QueryInterface(IDvdGraphBuilder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDvdGraphBuilder_AddRef(IDvdGraphBuilder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDvdGraphBuilder_Release(IDvdGraphBuilder* This) {
    return This->lpVtbl->Release(This);
}
/*** IDvdGraphBuilder methods ***/
static inline HRESULT IDvdGraphBuilder_GetFiltergraph(IDvdGraphBuilder* This,IGraphBuilder **graph) {
    return This->lpVtbl->GetFiltergraph(This,graph);
}
static inline HRESULT IDvdGraphBuilder_GetDvdInterface(IDvdGraphBuilder* This,REFIID iid,void **out) {
    return This->lpVtbl->GetDvdInterface(This,iid,out);
}
static inline HRESULT IDvdGraphBuilder_RenderDvdVideoVolume(IDvdGraphBuilder* This,const WCHAR *path,DWORD flags,AM_DVD_RENDERSTATUS *status) {
    return This->lpVtbl->RenderDvdVideoVolume(This,path,flags,status);
}
#endif
#endif

#endif


#endif  /* __IDvdGraphBuilder_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dvdif_h__ */
