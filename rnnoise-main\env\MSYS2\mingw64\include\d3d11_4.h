/*** Autogenerated by WIDL 10.12 from include/d3d11_4.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d11_4_h__
#define __d3d11_4_h__

/* Forward declarations */

#ifndef __ID3D11Device4_FWD_DEFINED__
#define __ID3D11Device4_FWD_DEFINED__
typedef interface ID3D11Device4 ID3D11Device4;
#ifdef __cplusplus
interface ID3D11Device4;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Device5_FWD_DEFINED__
#define __ID3D11Device5_FWD_DEFINED__
typedef interface ID3D11Device5 ID3D11Device5;
#ifdef __cplusplus
interface ID3D11Device5;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Multithread_FWD_DEFINED__
#define __ID3D11Multithread_FWD_DEFINED__
typedef interface ID3D11Multithread ID3D11Multithread;
#ifdef __cplusplus
interface ID3D11Multithread;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11VideoContext2_FWD_DEFINED__
#define __ID3D11VideoContext2_FWD_DEFINED__
typedef interface ID3D11VideoContext2 ID3D11VideoContext2;
#ifdef __cplusplus
interface ID3D11VideoContext2;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <dxgi1_5.h>
#include <d3dcommon.h>
#include <d3d11_3.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct D3D11_FEATURE_DATA_D3D11_OPTIONS4 {
    WINBOOL ExtendedNV12SharedTextureSupported;
} D3D11_FEATURE_DATA_D3D11_OPTIONS4;
/*****************************************************************************
 * ID3D11Device4 interface
 */
#ifndef __ID3D11Device4_INTERFACE_DEFINED__
#define __ID3D11Device4_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Device4, 0x8992ab71, 0x02e6, 0x4b8d, 0xba,0x48, 0xb0,0x56,0xdc,0xda,0x42,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8992ab71-02e6-4b8d-ba48-b056dcda42c4")
ID3D11Device4 : public ID3D11Device3
{
    virtual HRESULT STDMETHODCALLTYPE RegisterDeviceRemovedEvent(
        HANDLE event,
        DWORD *cookie) = 0;

    virtual void STDMETHODCALLTYPE UnregisterDeviceRemoved(
        DWORD cookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Device4, 0x8992ab71, 0x02e6, 0x4b8d, 0xba,0x48, 0xb0,0x56,0xdc,0xda,0x42,0xc4)
#endif
#else
typedef struct ID3D11Device4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Device4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Device4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Device4 *This);

    /*** ID3D11Device methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBuffer)(
        ID3D11Device4 *This,
        const D3D11_BUFFER_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Buffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *CreateTexture1D)(
        ID3D11Device4 *This,
        const D3D11_TEXTURE1D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture1D **ppTexture1D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture2D)(
        ID3D11Device4 *This,
        const D3D11_TEXTURE2D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture2D **ppTexture2D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D)(
        ID3D11Device4 *This,
        const D3D11_TEXTURE3D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture3D **ppTexture3D);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView)(
        ID3D11Device4 *This,
        ID3D11Resource *pResource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D11ShaderResourceView **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView)(
        ID3D11Device4 *This,
        ID3D11Resource *pResource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        ID3D11UnorderedAccessView **ppUAView);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView)(
        ID3D11Device4 *This,
        ID3D11Resource *pResource,
        const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D11RenderTargetView **ppRTView);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilView)(
        ID3D11Device4 *This,
        ID3D11Resource *pResource,
        const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D11DepthStencilView **ppDepthStencilView);

    HRESULT (STDMETHODCALLTYPE *CreateInputLayout)(
        ID3D11Device4 *This,
        const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D11InputLayout **ppInputLayout);

    HRESULT (STDMETHODCALLTYPE *CreateVertexShader)(
        ID3D11Device4 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11VertexShader **ppVertexShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShader)(
        ID3D11Device4 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShaderWithStreamOutput)(
        ID3D11Device4 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        const UINT *pBufferStrides,
        UINT NumStrides,
        UINT RasterizedStream,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreatePixelShader)(
        ID3D11Device4 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11PixelShader **ppPixelShader);

    HRESULT (STDMETHODCALLTYPE *CreateHullShader)(
        ID3D11Device4 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11HullShader **ppHullShader);

    HRESULT (STDMETHODCALLTYPE *CreateDomainShader)(
        ID3D11Device4 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11DomainShader **ppDomainShader);

    HRESULT (STDMETHODCALLTYPE *CreateComputeShader)(
        ID3D11Device4 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11ComputeShader **ppComputeShader);

    HRESULT (STDMETHODCALLTYPE *CreateClassLinkage)(
        ID3D11Device4 *This,
        ID3D11ClassLinkage **ppLinkage);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState)(
        ID3D11Device4 *This,
        const D3D11_BLEND_DESC *pBlendStateDesc,
        ID3D11BlendState **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilState)(
        ID3D11Device4 *This,
        const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D11DepthStencilState **ppDepthStencilState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState)(
        ID3D11Device4 *This,
        const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        ID3D11RasterizerState **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateSamplerState)(
        ID3D11Device4 *This,
        const D3D11_SAMPLER_DESC *pSamplerDesc,
        ID3D11SamplerState **ppSamplerState);

    HRESULT (STDMETHODCALLTYPE *CreateQuery)(
        ID3D11Device4 *This,
        const D3D11_QUERY_DESC *pQueryDesc,
        ID3D11Query **ppQuery);

    HRESULT (STDMETHODCALLTYPE *CreatePredicate)(
        ID3D11Device4 *This,
        const D3D11_QUERY_DESC *pPredicateDesc,
        ID3D11Predicate **ppPredicate);

    HRESULT (STDMETHODCALLTYPE *CreateCounter)(
        ID3D11Device4 *This,
        const D3D11_COUNTER_DESC *pCounterDesc,
        ID3D11Counter **ppCounter);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext)(
        ID3D11Device4 *This,
        UINT ContextFlags,
        ID3D11DeviceContext **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource)(
        ID3D11Device4 *This,
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *CheckFormatSupport)(
        ID3D11Device4 *This,
        DXGI_FORMAT Format,
        UINT *pFormatSupport);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels)(
        ID3D11Device4 *This,
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels);

    void (STDMETHODCALLTYPE *CheckCounterInfo)(
        ID3D11Device4 *This,
        D3D11_COUNTER_INFO *pCounterInfo);

    HRESULT (STDMETHODCALLTYPE *CheckCounter)(
        ID3D11Device4 *This,
        const D3D11_COUNTER_DESC *pDesc,
        D3D11_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        LPSTR szName,
        UINT *pNameLength,
        LPSTR szUnits,
        UINT *pUnitsLength,
        LPSTR szDescription,
        UINT *pDescriptionLength);

    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D11Device4 *This,
        D3D11_FEATURE Feature,
        void *pFeatureSupportData,
        UINT FeatureSupportDataSize);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Device4 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Device4 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Device4 *This,
        REFGUID guid,
        const IUnknown *pData);

    D3D_FEATURE_LEVEL (STDMETHODCALLTYPE *GetFeatureLevel)(
        ID3D11Device4 *This);

    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        ID3D11Device4 *This);

    HRESULT (STDMETHODCALLTYPE *GetDeviceRemovedReason)(
        ID3D11Device4 *This);

    void (STDMETHODCALLTYPE *GetImmediateContext)(
        ID3D11Device4 *This,
        ID3D11DeviceContext **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *SetExceptionMode)(
        ID3D11Device4 *This,
        UINT RaiseFlags);

    UINT (STDMETHODCALLTYPE *GetExceptionMode)(
        ID3D11Device4 *This);

    /*** ID3D11Device1 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext1)(
        ID3D11Device4 *This,
        ID3D11DeviceContext1 **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext1)(
        ID3D11Device4 *This,
        UINT ContextFlags,
        ID3D11DeviceContext1 **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState1)(
        ID3D11Device4 *This,
        const D3D11_BLEND_DESC1 *pBlendStateDesc,
        ID3D11BlendState1 **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState1)(
        ID3D11Device4 *This,
        const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,
        ID3D11RasterizerState1 **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateDeviceContextState)(
        ID3D11Device4 *This,
        UINT Flags,
        const D3D_FEATURE_LEVEL *pFeatureLevels,
        UINT FeatureLevels,
        UINT SDKVersion,
        REFIID EmulatedInterface,
        D3D_FEATURE_LEVEL *pChosenFeatureLevel,
        ID3DDeviceContextState **ppContextState);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource1)(
        ID3D11Device4 *This,
        HANDLE hResource,
        REFIID returnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResourceByName)(
        ID3D11Device4 *This,
        LPCWSTR lpName,
        DWORD dwDesiredAccess,
        REFIID returnedInterface,
        void **ppResource);

    /*** ID3D11Device2 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext2)(
        ID3D11Device4 *This,
        ID3D11DeviceContext2 **context);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext2)(
        ID3D11Device4 *This,
        UINT flags,
        ID3D11DeviceContext2 **context);

    void (STDMETHODCALLTYPE *GetResourceTiling)(
        ID3D11Device4 *This,
        ID3D11Resource *resource,
        UINT *tile_count,
        D3D11_PACKED_MIP_DESC *mip_desc,
        D3D11_TILE_SHAPE *tile_shape,
        UINT *subresource_tiling_count,
        UINT first_subresource_tiling,
        D3D11_SUBRESOURCE_TILING *subresource_tiling);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels1)(
        ID3D11Device4 *This,
        DXGI_FORMAT format,
        UINT sample_count,
        UINT flags,
        UINT *quality_level_count);

    /*** ID3D11Device3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTexture2D1)(
        ID3D11Device4 *This,
        const D3D11_TEXTURE2D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture2D1 **texture);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D1)(
        ID3D11Device4 *This,
        const D3D11_TEXTURE3D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture3D1 **texture);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState2)(
        ID3D11Device4 *This,
        const D3D11_RASTERIZER_DESC2 *desc,
        ID3D11RasterizerState2 **state);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView1)(
        ID3D11Device4 *This,
        ID3D11Resource *resource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,
        ID3D11ShaderResourceView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView1)(
        ID3D11Device4 *This,
        ID3D11Resource *resource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,
        ID3D11UnorderedAccessView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView1)(
        ID3D11Device4 *This,
        ID3D11Resource *resource,
        const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,
        ID3D11RenderTargetView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateQuery1)(
        ID3D11Device4 *This,
        const D3D11_QUERY_DESC1 *desc,
        ID3D11Query1 **query);

    void (STDMETHODCALLTYPE *GetImmediateContext3)(
        ID3D11Device4 *This,
        ID3D11DeviceContext3 **context);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext3)(
        ID3D11Device4 *This,
        UINT flags,
        ID3D11DeviceContext3 **context);

    void (STDMETHODCALLTYPE *WriteToSubresource)(
        ID3D11Device4 *This,
        ID3D11Resource *dst_resource,
        UINT dst_subresource,
        const D3D11_BOX *dst_box,
        const void *src_data,
        UINT src_row_pitch,
        UINT src_depth_pitch);

    void (STDMETHODCALLTYPE *ReadFromSubresource)(
        ID3D11Device4 *This,
        void *dst_data,
        UINT dst_row_pitch,
        UINT dst_depth_pitch,
        ID3D11Resource *src_resource,
        UINT src_subresource,
        const D3D11_BOX *src_box);

    /*** ID3D11Device4 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterDeviceRemovedEvent)(
        ID3D11Device4 *This,
        HANDLE event,
        DWORD *cookie);

    void (STDMETHODCALLTYPE *UnregisterDeviceRemoved)(
        ID3D11Device4 *This,
        DWORD cookie);

    END_INTERFACE
} ID3D11Device4Vtbl;

interface ID3D11Device4 {
    CONST_VTBL ID3D11Device4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Device4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Device4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Device4_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Device methods ***/
#define ID3D11Device4_CreateBuffer(This,pDesc,pInitialData,ppBuffer) (This)->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer)
#define ID3D11Device4_CreateTexture1D(This,pDesc,pInitialData,ppTexture1D) (This)->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D)
#define ID3D11Device4_CreateTexture2D(This,pDesc,pInitialData,ppTexture2D) (This)->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D)
#define ID3D11Device4_CreateTexture3D(This,pDesc,pInitialData,ppTexture3D) (This)->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D)
#define ID3D11Device4_CreateShaderResourceView(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView)
#define ID3D11Device4_CreateUnorderedAccessView(This,pResource,pDesc,ppUAView) (This)->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView)
#define ID3D11Device4_CreateRenderTargetView(This,pResource,pDesc,ppRTView) (This)->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView)
#define ID3D11Device4_CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView) (This)->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView)
#define ID3D11Device4_CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout) (This)->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout)
#define ID3D11Device4_CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader) (This)->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader)
#define ID3D11Device4_CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader)
#define ID3D11Device4_CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader)
#define ID3D11Device4_CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader) (This)->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader)
#define ID3D11Device4_CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader) (This)->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader)
#define ID3D11Device4_CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader) (This)->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader)
#define ID3D11Device4_CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader) (This)->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader)
#define ID3D11Device4_CreateClassLinkage(This,ppLinkage) (This)->lpVtbl->CreateClassLinkage(This,ppLinkage)
#define ID3D11Device4_CreateBlendState(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device4_CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState) (This)->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState)
#define ID3D11Device4_CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device4_CreateSamplerState(This,pSamplerDesc,ppSamplerState) (This)->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState)
#define ID3D11Device4_CreateQuery(This,pQueryDesc,ppQuery) (This)->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery)
#define ID3D11Device4_CreatePredicate(This,pPredicateDesc,ppPredicate) (This)->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate)
#define ID3D11Device4_CreateCounter(This,pCounterDesc,ppCounter) (This)->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter)
#define ID3D11Device4_CreateDeferredContext(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext)
#define ID3D11Device4_OpenSharedResource(This,hResource,ReturnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource)
#define ID3D11Device4_CheckFormatSupport(This,Format,pFormatSupport) (This)->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport)
#define ID3D11Device4_CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels) (This)->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels)
#define ID3D11Device4_CheckCounterInfo(This,pCounterInfo) (This)->lpVtbl->CheckCounterInfo(This,pCounterInfo)
#define ID3D11Device4_CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength) (This)->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength)
#define ID3D11Device4_CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize) (This)->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize)
#define ID3D11Device4_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Device4_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Device4_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#define ID3D11Device4_GetFeatureLevel(This) (This)->lpVtbl->GetFeatureLevel(This)
#define ID3D11Device4_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#define ID3D11Device4_GetDeviceRemovedReason(This) (This)->lpVtbl->GetDeviceRemovedReason(This)
#define ID3D11Device4_GetImmediateContext(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext(This,ppImmediateContext)
#define ID3D11Device4_SetExceptionMode(This,RaiseFlags) (This)->lpVtbl->SetExceptionMode(This,RaiseFlags)
#define ID3D11Device4_GetExceptionMode(This) (This)->lpVtbl->GetExceptionMode(This)
/*** ID3D11Device1 methods ***/
#define ID3D11Device4_GetImmediateContext1(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext1(This,ppImmediateContext)
#define ID3D11Device4_CreateDeferredContext1(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext)
#define ID3D11Device4_CreateBlendState1(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device4_CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device4_CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState) (This)->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState)
#define ID3D11Device4_OpenSharedResource1(This,hResource,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource)
#define ID3D11Device4_OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource)
/*** ID3D11Device2 methods ***/
#define ID3D11Device4_GetImmediateContext2(This,context) (This)->lpVtbl->GetImmediateContext2(This,context)
#define ID3D11Device4_CreateDeferredContext2(This,flags,context) (This)->lpVtbl->CreateDeferredContext2(This,flags,context)
#define ID3D11Device4_GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling) (This)->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling)
#define ID3D11Device4_CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count) (This)->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count)
/*** ID3D11Device3 methods ***/
#define ID3D11Device4_CreateTexture2D1(This,desc,initial_data,texture) (This)->lpVtbl->CreateTexture2D1(This,desc,initial_data,texture)
#define ID3D11Device4_CreateTexture3D1(This,desc,initial_data,texture) (This)->lpVtbl->CreateTexture3D1(This,desc,initial_data,texture)
#define ID3D11Device4_CreateRasterizerState2(This,desc,state) (This)->lpVtbl->CreateRasterizerState2(This,desc,state)
#define ID3D11Device4_CreateShaderResourceView1(This,resource,desc,view) (This)->lpVtbl->CreateShaderResourceView1(This,resource,desc,view)
#define ID3D11Device4_CreateUnorderedAccessView1(This,resource,desc,view) (This)->lpVtbl->CreateUnorderedAccessView1(This,resource,desc,view)
#define ID3D11Device4_CreateRenderTargetView1(This,resource,desc,view) (This)->lpVtbl->CreateRenderTargetView1(This,resource,desc,view)
#define ID3D11Device4_CreateQuery1(This,desc,query) (This)->lpVtbl->CreateQuery1(This,desc,query)
#define ID3D11Device4_GetImmediateContext3(This,context) (This)->lpVtbl->GetImmediateContext3(This,context)
#define ID3D11Device4_CreateDeferredContext3(This,flags,context) (This)->lpVtbl->CreateDeferredContext3(This,flags,context)
#define ID3D11Device4_WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch) (This)->lpVtbl->WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch)
#define ID3D11Device4_ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box) (This)->lpVtbl->ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box)
/*** ID3D11Device4 methods ***/
#define ID3D11Device4_RegisterDeviceRemovedEvent(This,event,cookie) (This)->lpVtbl->RegisterDeviceRemovedEvent(This,event,cookie)
#define ID3D11Device4_UnregisterDeviceRemoved(This,cookie) (This)->lpVtbl->UnregisterDeviceRemoved(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Device4_QueryInterface(ID3D11Device4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Device4_AddRef(ID3D11Device4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Device4_Release(ID3D11Device4* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Device methods ***/
static inline HRESULT ID3D11Device4_CreateBuffer(ID3D11Device4* This,const D3D11_BUFFER_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Buffer **ppBuffer) {
    return This->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer);
}
static inline HRESULT ID3D11Device4_CreateTexture1D(ID3D11Device4* This,const D3D11_TEXTURE1D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture1D **ppTexture1D) {
    return This->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D);
}
static inline HRESULT ID3D11Device4_CreateTexture2D(ID3D11Device4* This,const D3D11_TEXTURE2D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture2D **ppTexture2D) {
    return This->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D);
}
static inline HRESULT ID3D11Device4_CreateTexture3D(ID3D11Device4* This,const D3D11_TEXTURE3D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture3D **ppTexture3D) {
    return This->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D);
}
static inline HRESULT ID3D11Device4_CreateShaderResourceView(ID3D11Device4* This,ID3D11Resource *pResource,const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,ID3D11ShaderResourceView **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView);
}
static inline HRESULT ID3D11Device4_CreateUnorderedAccessView(ID3D11Device4* This,ID3D11Resource *pResource,const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,ID3D11UnorderedAccessView **ppUAView) {
    return This->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView);
}
static inline HRESULT ID3D11Device4_CreateRenderTargetView(ID3D11Device4* This,ID3D11Resource *pResource,const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,ID3D11RenderTargetView **ppRTView) {
    return This->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView);
}
static inline HRESULT ID3D11Device4_CreateDepthStencilView(ID3D11Device4* This,ID3D11Resource *pResource,const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,ID3D11DepthStencilView **ppDepthStencilView) {
    return This->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView);
}
static inline HRESULT ID3D11Device4_CreateInputLayout(ID3D11Device4* This,const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,UINT NumElements,const void *pShaderBytecodeWithInputSignature,SIZE_T BytecodeLength,ID3D11InputLayout **ppInputLayout) {
    return This->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout);
}
static inline HRESULT ID3D11Device4_CreateVertexShader(ID3D11Device4* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11VertexShader **ppVertexShader) {
    return This->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader);
}
static inline HRESULT ID3D11Device4_CreateGeometryShader(ID3D11Device4* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device4_CreateGeometryShaderWithStreamOutput(ID3D11Device4* This,const void *pShaderBytecode,SIZE_T BytecodeLength,const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,UINT NumEntries,const UINT *pBufferStrides,UINT NumStrides,UINT RasterizedStream,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device4_CreatePixelShader(ID3D11Device4* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11PixelShader **ppPixelShader) {
    return This->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader);
}
static inline HRESULT ID3D11Device4_CreateHullShader(ID3D11Device4* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11HullShader **ppHullShader) {
    return This->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader);
}
static inline HRESULT ID3D11Device4_CreateDomainShader(ID3D11Device4* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11DomainShader **ppDomainShader) {
    return This->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader);
}
static inline HRESULT ID3D11Device4_CreateComputeShader(ID3D11Device4* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11ComputeShader **ppComputeShader) {
    return This->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader);
}
static inline HRESULT ID3D11Device4_CreateClassLinkage(ID3D11Device4* This,ID3D11ClassLinkage **ppLinkage) {
    return This->lpVtbl->CreateClassLinkage(This,ppLinkage);
}
static inline HRESULT ID3D11Device4_CreateBlendState(ID3D11Device4* This,const D3D11_BLEND_DESC *pBlendStateDesc,ID3D11BlendState **ppBlendState) {
    return This->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device4_CreateDepthStencilState(ID3D11Device4* This,const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,ID3D11DepthStencilState **ppDepthStencilState) {
    return This->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState);
}
static inline HRESULT ID3D11Device4_CreateRasterizerState(ID3D11Device4* This,const D3D11_RASTERIZER_DESC *pRasterizerDesc,ID3D11RasterizerState **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device4_CreateSamplerState(ID3D11Device4* This,const D3D11_SAMPLER_DESC *pSamplerDesc,ID3D11SamplerState **ppSamplerState) {
    return This->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState);
}
static inline HRESULT ID3D11Device4_CreateQuery(ID3D11Device4* This,const D3D11_QUERY_DESC *pQueryDesc,ID3D11Query **ppQuery) {
    return This->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery);
}
static inline HRESULT ID3D11Device4_CreatePredicate(ID3D11Device4* This,const D3D11_QUERY_DESC *pPredicateDesc,ID3D11Predicate **ppPredicate) {
    return This->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate);
}
static inline HRESULT ID3D11Device4_CreateCounter(ID3D11Device4* This,const D3D11_COUNTER_DESC *pCounterDesc,ID3D11Counter **ppCounter) {
    return This->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter);
}
static inline HRESULT ID3D11Device4_CreateDeferredContext(ID3D11Device4* This,UINT ContextFlags,ID3D11DeviceContext **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device4_OpenSharedResource(ID3D11Device4* This,HANDLE hResource,REFIID ReturnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource);
}
static inline HRESULT ID3D11Device4_CheckFormatSupport(ID3D11Device4* This,DXGI_FORMAT Format,UINT *pFormatSupport) {
    return This->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport);
}
static inline HRESULT ID3D11Device4_CheckMultisampleQualityLevels(ID3D11Device4* This,DXGI_FORMAT Format,UINT SampleCount,UINT *pNumQualityLevels) {
    return This->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels);
}
static inline void ID3D11Device4_CheckCounterInfo(ID3D11Device4* This,D3D11_COUNTER_INFO *pCounterInfo) {
    This->lpVtbl->CheckCounterInfo(This,pCounterInfo);
}
static inline HRESULT ID3D11Device4_CheckCounter(ID3D11Device4* This,const D3D11_COUNTER_DESC *pDesc,D3D11_COUNTER_TYPE *pType,UINT *pActiveCounters,LPSTR szName,UINT *pNameLength,LPSTR szUnits,UINT *pUnitsLength,LPSTR szDescription,UINT *pDescriptionLength) {
    return This->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength);
}
static inline HRESULT ID3D11Device4_CheckFeatureSupport(ID3D11Device4* This,D3D11_FEATURE Feature,void *pFeatureSupportData,UINT FeatureSupportDataSize) {
    return This->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize);
}
static inline HRESULT ID3D11Device4_GetPrivateData(ID3D11Device4* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Device4_SetPrivateData(ID3D11Device4* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Device4_SetPrivateDataInterface(ID3D11Device4* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
static inline D3D_FEATURE_LEVEL ID3D11Device4_GetFeatureLevel(ID3D11Device4* This) {
    return This->lpVtbl->GetFeatureLevel(This);
}
static inline UINT ID3D11Device4_GetCreationFlags(ID3D11Device4* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
static inline HRESULT ID3D11Device4_GetDeviceRemovedReason(ID3D11Device4* This) {
    return This->lpVtbl->GetDeviceRemovedReason(This);
}
static inline void ID3D11Device4_GetImmediateContext(ID3D11Device4* This,ID3D11DeviceContext **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device4_SetExceptionMode(ID3D11Device4* This,UINT RaiseFlags) {
    return This->lpVtbl->SetExceptionMode(This,RaiseFlags);
}
static inline UINT ID3D11Device4_GetExceptionMode(ID3D11Device4* This) {
    return This->lpVtbl->GetExceptionMode(This);
}
/*** ID3D11Device1 methods ***/
static inline void ID3D11Device4_GetImmediateContext1(ID3D11Device4* This,ID3D11DeviceContext1 **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext1(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device4_CreateDeferredContext1(ID3D11Device4* This,UINT ContextFlags,ID3D11DeviceContext1 **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device4_CreateBlendState1(ID3D11Device4* This,const D3D11_BLEND_DESC1 *pBlendStateDesc,ID3D11BlendState1 **ppBlendState) {
    return This->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device4_CreateRasterizerState1(ID3D11Device4* This,const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,ID3D11RasterizerState1 **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device4_CreateDeviceContextState(ID3D11Device4* This,UINT Flags,const D3D_FEATURE_LEVEL *pFeatureLevels,UINT FeatureLevels,UINT SDKVersion,REFIID EmulatedInterface,D3D_FEATURE_LEVEL *pChosenFeatureLevel,ID3DDeviceContextState **ppContextState) {
    return This->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState);
}
static inline HRESULT ID3D11Device4_OpenSharedResource1(ID3D11Device4* This,HANDLE hResource,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource);
}
static inline HRESULT ID3D11Device4_OpenSharedResourceByName(ID3D11Device4* This,LPCWSTR lpName,DWORD dwDesiredAccess,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource);
}
/*** ID3D11Device2 methods ***/
static inline void ID3D11Device4_GetImmediateContext2(ID3D11Device4* This,ID3D11DeviceContext2 **context) {
    This->lpVtbl->GetImmediateContext2(This,context);
}
static inline HRESULT ID3D11Device4_CreateDeferredContext2(ID3D11Device4* This,UINT flags,ID3D11DeviceContext2 **context) {
    return This->lpVtbl->CreateDeferredContext2(This,flags,context);
}
static inline void ID3D11Device4_GetResourceTiling(ID3D11Device4* This,ID3D11Resource *resource,UINT *tile_count,D3D11_PACKED_MIP_DESC *mip_desc,D3D11_TILE_SHAPE *tile_shape,UINT *subresource_tiling_count,UINT first_subresource_tiling,D3D11_SUBRESOURCE_TILING *subresource_tiling) {
    This->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling);
}
static inline HRESULT ID3D11Device4_CheckMultisampleQualityLevels1(ID3D11Device4* This,DXGI_FORMAT format,UINT sample_count,UINT flags,UINT *quality_level_count) {
    return This->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count);
}
/*** ID3D11Device3 methods ***/
static inline HRESULT ID3D11Device4_CreateTexture2D1(ID3D11Device4* This,const D3D11_TEXTURE2D_DESC1 *desc,const D3D11_SUBRESOURCE_DATA *initial_data,ID3D11Texture2D1 **texture) {
    return This->lpVtbl->CreateTexture2D1(This,desc,initial_data,texture);
}
static inline HRESULT ID3D11Device4_CreateTexture3D1(ID3D11Device4* This,const D3D11_TEXTURE3D_DESC1 *desc,const D3D11_SUBRESOURCE_DATA *initial_data,ID3D11Texture3D1 **texture) {
    return This->lpVtbl->CreateTexture3D1(This,desc,initial_data,texture);
}
static inline HRESULT ID3D11Device4_CreateRasterizerState2(ID3D11Device4* This,const D3D11_RASTERIZER_DESC2 *desc,ID3D11RasterizerState2 **state) {
    return This->lpVtbl->CreateRasterizerState2(This,desc,state);
}
static inline HRESULT ID3D11Device4_CreateShaderResourceView1(ID3D11Device4* This,ID3D11Resource *resource,const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,ID3D11ShaderResourceView1 **view) {
    return This->lpVtbl->CreateShaderResourceView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device4_CreateUnorderedAccessView1(ID3D11Device4* This,ID3D11Resource *resource,const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,ID3D11UnorderedAccessView1 **view) {
    return This->lpVtbl->CreateUnorderedAccessView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device4_CreateRenderTargetView1(ID3D11Device4* This,ID3D11Resource *resource,const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,ID3D11RenderTargetView1 **view) {
    return This->lpVtbl->CreateRenderTargetView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device4_CreateQuery1(ID3D11Device4* This,const D3D11_QUERY_DESC1 *desc,ID3D11Query1 **query) {
    return This->lpVtbl->CreateQuery1(This,desc,query);
}
static inline void ID3D11Device4_GetImmediateContext3(ID3D11Device4* This,ID3D11DeviceContext3 **context) {
    This->lpVtbl->GetImmediateContext3(This,context);
}
static inline HRESULT ID3D11Device4_CreateDeferredContext3(ID3D11Device4* This,UINT flags,ID3D11DeviceContext3 **context) {
    return This->lpVtbl->CreateDeferredContext3(This,flags,context);
}
static inline void ID3D11Device4_WriteToSubresource(ID3D11Device4* This,ID3D11Resource *dst_resource,UINT dst_subresource,const D3D11_BOX *dst_box,const void *src_data,UINT src_row_pitch,UINT src_depth_pitch) {
    This->lpVtbl->WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch);
}
static inline void ID3D11Device4_ReadFromSubresource(ID3D11Device4* This,void *dst_data,UINT dst_row_pitch,UINT dst_depth_pitch,ID3D11Resource *src_resource,UINT src_subresource,const D3D11_BOX *src_box) {
    This->lpVtbl->ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box);
}
/*** ID3D11Device4 methods ***/
static inline HRESULT ID3D11Device4_RegisterDeviceRemovedEvent(ID3D11Device4* This,HANDLE event,DWORD *cookie) {
    return This->lpVtbl->RegisterDeviceRemovedEvent(This,event,cookie);
}
static inline void ID3D11Device4_UnregisterDeviceRemoved(ID3D11Device4* This,DWORD cookie) {
    This->lpVtbl->UnregisterDeviceRemoved(This,cookie);
}
#endif
#endif

#endif


#endif  /* __ID3D11Device4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Device5 interface
 */
#ifndef __ID3D11Device5_INTERFACE_DEFINED__
#define __ID3D11Device5_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Device5, 0x8ffde202, 0xa0e7, 0x45df, 0x9e,0x01, 0xe8,0x37,0x80,0x1b,0x5e,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8ffde202-a0e7-45df-9e01-e837801b5ea0")
ID3D11Device5 : public ID3D11Device4
{
    virtual HRESULT STDMETHODCALLTYPE OpenSharedFence(
        HANDLE handle,
        REFIID iid,
        void **fence) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFence(
        UINT64 initial_value,
        D3D11_FENCE_FLAG flags,
        REFIID iid,
        void **fence) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Device5, 0x8ffde202, 0xa0e7, 0x45df, 0x9e,0x01, 0xe8,0x37,0x80,0x1b,0x5e,0xa0)
#endif
#else
typedef struct ID3D11Device5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Device5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Device5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Device5 *This);

    /*** ID3D11Device methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBuffer)(
        ID3D11Device5 *This,
        const D3D11_BUFFER_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Buffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *CreateTexture1D)(
        ID3D11Device5 *This,
        const D3D11_TEXTURE1D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture1D **ppTexture1D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture2D)(
        ID3D11Device5 *This,
        const D3D11_TEXTURE2D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture2D **ppTexture2D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D)(
        ID3D11Device5 *This,
        const D3D11_TEXTURE3D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture3D **ppTexture3D);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView)(
        ID3D11Device5 *This,
        ID3D11Resource *pResource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D11ShaderResourceView **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView)(
        ID3D11Device5 *This,
        ID3D11Resource *pResource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        ID3D11UnorderedAccessView **ppUAView);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView)(
        ID3D11Device5 *This,
        ID3D11Resource *pResource,
        const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D11RenderTargetView **ppRTView);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilView)(
        ID3D11Device5 *This,
        ID3D11Resource *pResource,
        const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D11DepthStencilView **ppDepthStencilView);

    HRESULT (STDMETHODCALLTYPE *CreateInputLayout)(
        ID3D11Device5 *This,
        const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D11InputLayout **ppInputLayout);

    HRESULT (STDMETHODCALLTYPE *CreateVertexShader)(
        ID3D11Device5 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11VertexShader **ppVertexShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShader)(
        ID3D11Device5 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShaderWithStreamOutput)(
        ID3D11Device5 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        const UINT *pBufferStrides,
        UINT NumStrides,
        UINT RasterizedStream,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreatePixelShader)(
        ID3D11Device5 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11PixelShader **ppPixelShader);

    HRESULT (STDMETHODCALLTYPE *CreateHullShader)(
        ID3D11Device5 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11HullShader **ppHullShader);

    HRESULT (STDMETHODCALLTYPE *CreateDomainShader)(
        ID3D11Device5 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11DomainShader **ppDomainShader);

    HRESULT (STDMETHODCALLTYPE *CreateComputeShader)(
        ID3D11Device5 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11ComputeShader **ppComputeShader);

    HRESULT (STDMETHODCALLTYPE *CreateClassLinkage)(
        ID3D11Device5 *This,
        ID3D11ClassLinkage **ppLinkage);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState)(
        ID3D11Device5 *This,
        const D3D11_BLEND_DESC *pBlendStateDesc,
        ID3D11BlendState **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilState)(
        ID3D11Device5 *This,
        const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D11DepthStencilState **ppDepthStencilState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState)(
        ID3D11Device5 *This,
        const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        ID3D11RasterizerState **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateSamplerState)(
        ID3D11Device5 *This,
        const D3D11_SAMPLER_DESC *pSamplerDesc,
        ID3D11SamplerState **ppSamplerState);

    HRESULT (STDMETHODCALLTYPE *CreateQuery)(
        ID3D11Device5 *This,
        const D3D11_QUERY_DESC *pQueryDesc,
        ID3D11Query **ppQuery);

    HRESULT (STDMETHODCALLTYPE *CreatePredicate)(
        ID3D11Device5 *This,
        const D3D11_QUERY_DESC *pPredicateDesc,
        ID3D11Predicate **ppPredicate);

    HRESULT (STDMETHODCALLTYPE *CreateCounter)(
        ID3D11Device5 *This,
        const D3D11_COUNTER_DESC *pCounterDesc,
        ID3D11Counter **ppCounter);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext)(
        ID3D11Device5 *This,
        UINT ContextFlags,
        ID3D11DeviceContext **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource)(
        ID3D11Device5 *This,
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *CheckFormatSupport)(
        ID3D11Device5 *This,
        DXGI_FORMAT Format,
        UINT *pFormatSupport);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels)(
        ID3D11Device5 *This,
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels);

    void (STDMETHODCALLTYPE *CheckCounterInfo)(
        ID3D11Device5 *This,
        D3D11_COUNTER_INFO *pCounterInfo);

    HRESULT (STDMETHODCALLTYPE *CheckCounter)(
        ID3D11Device5 *This,
        const D3D11_COUNTER_DESC *pDesc,
        D3D11_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        LPSTR szName,
        UINT *pNameLength,
        LPSTR szUnits,
        UINT *pUnitsLength,
        LPSTR szDescription,
        UINT *pDescriptionLength);

    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D11Device5 *This,
        D3D11_FEATURE Feature,
        void *pFeatureSupportData,
        UINT FeatureSupportDataSize);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Device5 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Device5 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Device5 *This,
        REFGUID guid,
        const IUnknown *pData);

    D3D_FEATURE_LEVEL (STDMETHODCALLTYPE *GetFeatureLevel)(
        ID3D11Device5 *This);

    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        ID3D11Device5 *This);

    HRESULT (STDMETHODCALLTYPE *GetDeviceRemovedReason)(
        ID3D11Device5 *This);

    void (STDMETHODCALLTYPE *GetImmediateContext)(
        ID3D11Device5 *This,
        ID3D11DeviceContext **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *SetExceptionMode)(
        ID3D11Device5 *This,
        UINT RaiseFlags);

    UINT (STDMETHODCALLTYPE *GetExceptionMode)(
        ID3D11Device5 *This);

    /*** ID3D11Device1 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext1)(
        ID3D11Device5 *This,
        ID3D11DeviceContext1 **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext1)(
        ID3D11Device5 *This,
        UINT ContextFlags,
        ID3D11DeviceContext1 **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState1)(
        ID3D11Device5 *This,
        const D3D11_BLEND_DESC1 *pBlendStateDesc,
        ID3D11BlendState1 **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState1)(
        ID3D11Device5 *This,
        const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,
        ID3D11RasterizerState1 **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateDeviceContextState)(
        ID3D11Device5 *This,
        UINT Flags,
        const D3D_FEATURE_LEVEL *pFeatureLevels,
        UINT FeatureLevels,
        UINT SDKVersion,
        REFIID EmulatedInterface,
        D3D_FEATURE_LEVEL *pChosenFeatureLevel,
        ID3DDeviceContextState **ppContextState);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource1)(
        ID3D11Device5 *This,
        HANDLE hResource,
        REFIID returnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResourceByName)(
        ID3D11Device5 *This,
        LPCWSTR lpName,
        DWORD dwDesiredAccess,
        REFIID returnedInterface,
        void **ppResource);

    /*** ID3D11Device2 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext2)(
        ID3D11Device5 *This,
        ID3D11DeviceContext2 **context);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext2)(
        ID3D11Device5 *This,
        UINT flags,
        ID3D11DeviceContext2 **context);

    void (STDMETHODCALLTYPE *GetResourceTiling)(
        ID3D11Device5 *This,
        ID3D11Resource *resource,
        UINT *tile_count,
        D3D11_PACKED_MIP_DESC *mip_desc,
        D3D11_TILE_SHAPE *tile_shape,
        UINT *subresource_tiling_count,
        UINT first_subresource_tiling,
        D3D11_SUBRESOURCE_TILING *subresource_tiling);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels1)(
        ID3D11Device5 *This,
        DXGI_FORMAT format,
        UINT sample_count,
        UINT flags,
        UINT *quality_level_count);

    /*** ID3D11Device3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTexture2D1)(
        ID3D11Device5 *This,
        const D3D11_TEXTURE2D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture2D1 **texture);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D1)(
        ID3D11Device5 *This,
        const D3D11_TEXTURE3D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture3D1 **texture);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState2)(
        ID3D11Device5 *This,
        const D3D11_RASTERIZER_DESC2 *desc,
        ID3D11RasterizerState2 **state);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView1)(
        ID3D11Device5 *This,
        ID3D11Resource *resource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,
        ID3D11ShaderResourceView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView1)(
        ID3D11Device5 *This,
        ID3D11Resource *resource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,
        ID3D11UnorderedAccessView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView1)(
        ID3D11Device5 *This,
        ID3D11Resource *resource,
        const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,
        ID3D11RenderTargetView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateQuery1)(
        ID3D11Device5 *This,
        const D3D11_QUERY_DESC1 *desc,
        ID3D11Query1 **query);

    void (STDMETHODCALLTYPE *GetImmediateContext3)(
        ID3D11Device5 *This,
        ID3D11DeviceContext3 **context);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext3)(
        ID3D11Device5 *This,
        UINT flags,
        ID3D11DeviceContext3 **context);

    void (STDMETHODCALLTYPE *WriteToSubresource)(
        ID3D11Device5 *This,
        ID3D11Resource *dst_resource,
        UINT dst_subresource,
        const D3D11_BOX *dst_box,
        const void *src_data,
        UINT src_row_pitch,
        UINT src_depth_pitch);

    void (STDMETHODCALLTYPE *ReadFromSubresource)(
        ID3D11Device5 *This,
        void *dst_data,
        UINT dst_row_pitch,
        UINT dst_depth_pitch,
        ID3D11Resource *src_resource,
        UINT src_subresource,
        const D3D11_BOX *src_box);

    /*** ID3D11Device4 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterDeviceRemovedEvent)(
        ID3D11Device5 *This,
        HANDLE event,
        DWORD *cookie);

    void (STDMETHODCALLTYPE *UnregisterDeviceRemoved)(
        ID3D11Device5 *This,
        DWORD cookie);

    /*** ID3D11Device5 methods ***/
    HRESULT (STDMETHODCALLTYPE *OpenSharedFence)(
        ID3D11Device5 *This,
        HANDLE handle,
        REFIID iid,
        void **fence);

    HRESULT (STDMETHODCALLTYPE *CreateFence)(
        ID3D11Device5 *This,
        UINT64 initial_value,
        D3D11_FENCE_FLAG flags,
        REFIID iid,
        void **fence);

    END_INTERFACE
} ID3D11Device5Vtbl;

interface ID3D11Device5 {
    CONST_VTBL ID3D11Device5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Device5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Device5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Device5_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Device methods ***/
#define ID3D11Device5_CreateBuffer(This,pDesc,pInitialData,ppBuffer) (This)->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer)
#define ID3D11Device5_CreateTexture1D(This,pDesc,pInitialData,ppTexture1D) (This)->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D)
#define ID3D11Device5_CreateTexture2D(This,pDesc,pInitialData,ppTexture2D) (This)->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D)
#define ID3D11Device5_CreateTexture3D(This,pDesc,pInitialData,ppTexture3D) (This)->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D)
#define ID3D11Device5_CreateShaderResourceView(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView)
#define ID3D11Device5_CreateUnorderedAccessView(This,pResource,pDesc,ppUAView) (This)->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView)
#define ID3D11Device5_CreateRenderTargetView(This,pResource,pDesc,ppRTView) (This)->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView)
#define ID3D11Device5_CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView) (This)->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView)
#define ID3D11Device5_CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout) (This)->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout)
#define ID3D11Device5_CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader) (This)->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader)
#define ID3D11Device5_CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader)
#define ID3D11Device5_CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader)
#define ID3D11Device5_CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader) (This)->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader)
#define ID3D11Device5_CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader) (This)->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader)
#define ID3D11Device5_CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader) (This)->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader)
#define ID3D11Device5_CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader) (This)->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader)
#define ID3D11Device5_CreateClassLinkage(This,ppLinkage) (This)->lpVtbl->CreateClassLinkage(This,ppLinkage)
#define ID3D11Device5_CreateBlendState(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device5_CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState) (This)->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState)
#define ID3D11Device5_CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device5_CreateSamplerState(This,pSamplerDesc,ppSamplerState) (This)->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState)
#define ID3D11Device5_CreateQuery(This,pQueryDesc,ppQuery) (This)->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery)
#define ID3D11Device5_CreatePredicate(This,pPredicateDesc,ppPredicate) (This)->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate)
#define ID3D11Device5_CreateCounter(This,pCounterDesc,ppCounter) (This)->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter)
#define ID3D11Device5_CreateDeferredContext(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext)
#define ID3D11Device5_OpenSharedResource(This,hResource,ReturnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource)
#define ID3D11Device5_CheckFormatSupport(This,Format,pFormatSupport) (This)->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport)
#define ID3D11Device5_CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels) (This)->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels)
#define ID3D11Device5_CheckCounterInfo(This,pCounterInfo) (This)->lpVtbl->CheckCounterInfo(This,pCounterInfo)
#define ID3D11Device5_CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength) (This)->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength)
#define ID3D11Device5_CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize) (This)->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize)
#define ID3D11Device5_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Device5_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Device5_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#define ID3D11Device5_GetFeatureLevel(This) (This)->lpVtbl->GetFeatureLevel(This)
#define ID3D11Device5_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#define ID3D11Device5_GetDeviceRemovedReason(This) (This)->lpVtbl->GetDeviceRemovedReason(This)
#define ID3D11Device5_GetImmediateContext(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext(This,ppImmediateContext)
#define ID3D11Device5_SetExceptionMode(This,RaiseFlags) (This)->lpVtbl->SetExceptionMode(This,RaiseFlags)
#define ID3D11Device5_GetExceptionMode(This) (This)->lpVtbl->GetExceptionMode(This)
/*** ID3D11Device1 methods ***/
#define ID3D11Device5_GetImmediateContext1(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext1(This,ppImmediateContext)
#define ID3D11Device5_CreateDeferredContext1(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext)
#define ID3D11Device5_CreateBlendState1(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device5_CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device5_CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState) (This)->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState)
#define ID3D11Device5_OpenSharedResource1(This,hResource,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource)
#define ID3D11Device5_OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource)
/*** ID3D11Device2 methods ***/
#define ID3D11Device5_GetImmediateContext2(This,context) (This)->lpVtbl->GetImmediateContext2(This,context)
#define ID3D11Device5_CreateDeferredContext2(This,flags,context) (This)->lpVtbl->CreateDeferredContext2(This,flags,context)
#define ID3D11Device5_GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling) (This)->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling)
#define ID3D11Device5_CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count) (This)->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count)
/*** ID3D11Device3 methods ***/
#define ID3D11Device5_CreateTexture2D1(This,desc,initial_data,texture) (This)->lpVtbl->CreateTexture2D1(This,desc,initial_data,texture)
#define ID3D11Device5_CreateTexture3D1(This,desc,initial_data,texture) (This)->lpVtbl->CreateTexture3D1(This,desc,initial_data,texture)
#define ID3D11Device5_CreateRasterizerState2(This,desc,state) (This)->lpVtbl->CreateRasterizerState2(This,desc,state)
#define ID3D11Device5_CreateShaderResourceView1(This,resource,desc,view) (This)->lpVtbl->CreateShaderResourceView1(This,resource,desc,view)
#define ID3D11Device5_CreateUnorderedAccessView1(This,resource,desc,view) (This)->lpVtbl->CreateUnorderedAccessView1(This,resource,desc,view)
#define ID3D11Device5_CreateRenderTargetView1(This,resource,desc,view) (This)->lpVtbl->CreateRenderTargetView1(This,resource,desc,view)
#define ID3D11Device5_CreateQuery1(This,desc,query) (This)->lpVtbl->CreateQuery1(This,desc,query)
#define ID3D11Device5_GetImmediateContext3(This,context) (This)->lpVtbl->GetImmediateContext3(This,context)
#define ID3D11Device5_CreateDeferredContext3(This,flags,context) (This)->lpVtbl->CreateDeferredContext3(This,flags,context)
#define ID3D11Device5_WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch) (This)->lpVtbl->WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch)
#define ID3D11Device5_ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box) (This)->lpVtbl->ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box)
/*** ID3D11Device4 methods ***/
#define ID3D11Device5_RegisterDeviceRemovedEvent(This,event,cookie) (This)->lpVtbl->RegisterDeviceRemovedEvent(This,event,cookie)
#define ID3D11Device5_UnregisterDeviceRemoved(This,cookie) (This)->lpVtbl->UnregisterDeviceRemoved(This,cookie)
/*** ID3D11Device5 methods ***/
#define ID3D11Device5_OpenSharedFence(This,handle,iid,fence) (This)->lpVtbl->OpenSharedFence(This,handle,iid,fence)
#define ID3D11Device5_CreateFence(This,initial_value,flags,iid,fence) (This)->lpVtbl->CreateFence(This,initial_value,flags,iid,fence)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Device5_QueryInterface(ID3D11Device5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Device5_AddRef(ID3D11Device5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Device5_Release(ID3D11Device5* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Device methods ***/
static inline HRESULT ID3D11Device5_CreateBuffer(ID3D11Device5* This,const D3D11_BUFFER_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Buffer **ppBuffer) {
    return This->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer);
}
static inline HRESULT ID3D11Device5_CreateTexture1D(ID3D11Device5* This,const D3D11_TEXTURE1D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture1D **ppTexture1D) {
    return This->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D);
}
static inline HRESULT ID3D11Device5_CreateTexture2D(ID3D11Device5* This,const D3D11_TEXTURE2D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture2D **ppTexture2D) {
    return This->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D);
}
static inline HRESULT ID3D11Device5_CreateTexture3D(ID3D11Device5* This,const D3D11_TEXTURE3D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture3D **ppTexture3D) {
    return This->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D);
}
static inline HRESULT ID3D11Device5_CreateShaderResourceView(ID3D11Device5* This,ID3D11Resource *pResource,const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,ID3D11ShaderResourceView **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView);
}
static inline HRESULT ID3D11Device5_CreateUnorderedAccessView(ID3D11Device5* This,ID3D11Resource *pResource,const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,ID3D11UnorderedAccessView **ppUAView) {
    return This->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView);
}
static inline HRESULT ID3D11Device5_CreateRenderTargetView(ID3D11Device5* This,ID3D11Resource *pResource,const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,ID3D11RenderTargetView **ppRTView) {
    return This->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView);
}
static inline HRESULT ID3D11Device5_CreateDepthStencilView(ID3D11Device5* This,ID3D11Resource *pResource,const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,ID3D11DepthStencilView **ppDepthStencilView) {
    return This->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView);
}
static inline HRESULT ID3D11Device5_CreateInputLayout(ID3D11Device5* This,const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,UINT NumElements,const void *pShaderBytecodeWithInputSignature,SIZE_T BytecodeLength,ID3D11InputLayout **ppInputLayout) {
    return This->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout);
}
static inline HRESULT ID3D11Device5_CreateVertexShader(ID3D11Device5* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11VertexShader **ppVertexShader) {
    return This->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader);
}
static inline HRESULT ID3D11Device5_CreateGeometryShader(ID3D11Device5* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device5_CreateGeometryShaderWithStreamOutput(ID3D11Device5* This,const void *pShaderBytecode,SIZE_T BytecodeLength,const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,UINT NumEntries,const UINT *pBufferStrides,UINT NumStrides,UINT RasterizedStream,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device5_CreatePixelShader(ID3D11Device5* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11PixelShader **ppPixelShader) {
    return This->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader);
}
static inline HRESULT ID3D11Device5_CreateHullShader(ID3D11Device5* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11HullShader **ppHullShader) {
    return This->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader);
}
static inline HRESULT ID3D11Device5_CreateDomainShader(ID3D11Device5* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11DomainShader **ppDomainShader) {
    return This->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader);
}
static inline HRESULT ID3D11Device5_CreateComputeShader(ID3D11Device5* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11ComputeShader **ppComputeShader) {
    return This->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader);
}
static inline HRESULT ID3D11Device5_CreateClassLinkage(ID3D11Device5* This,ID3D11ClassLinkage **ppLinkage) {
    return This->lpVtbl->CreateClassLinkage(This,ppLinkage);
}
static inline HRESULT ID3D11Device5_CreateBlendState(ID3D11Device5* This,const D3D11_BLEND_DESC *pBlendStateDesc,ID3D11BlendState **ppBlendState) {
    return This->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device5_CreateDepthStencilState(ID3D11Device5* This,const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,ID3D11DepthStencilState **ppDepthStencilState) {
    return This->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState);
}
static inline HRESULT ID3D11Device5_CreateRasterizerState(ID3D11Device5* This,const D3D11_RASTERIZER_DESC *pRasterizerDesc,ID3D11RasterizerState **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device5_CreateSamplerState(ID3D11Device5* This,const D3D11_SAMPLER_DESC *pSamplerDesc,ID3D11SamplerState **ppSamplerState) {
    return This->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState);
}
static inline HRESULT ID3D11Device5_CreateQuery(ID3D11Device5* This,const D3D11_QUERY_DESC *pQueryDesc,ID3D11Query **ppQuery) {
    return This->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery);
}
static inline HRESULT ID3D11Device5_CreatePredicate(ID3D11Device5* This,const D3D11_QUERY_DESC *pPredicateDesc,ID3D11Predicate **ppPredicate) {
    return This->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate);
}
static inline HRESULT ID3D11Device5_CreateCounter(ID3D11Device5* This,const D3D11_COUNTER_DESC *pCounterDesc,ID3D11Counter **ppCounter) {
    return This->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter);
}
static inline HRESULT ID3D11Device5_CreateDeferredContext(ID3D11Device5* This,UINT ContextFlags,ID3D11DeviceContext **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device5_OpenSharedResource(ID3D11Device5* This,HANDLE hResource,REFIID ReturnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource);
}
static inline HRESULT ID3D11Device5_CheckFormatSupport(ID3D11Device5* This,DXGI_FORMAT Format,UINT *pFormatSupport) {
    return This->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport);
}
static inline HRESULT ID3D11Device5_CheckMultisampleQualityLevels(ID3D11Device5* This,DXGI_FORMAT Format,UINT SampleCount,UINT *pNumQualityLevels) {
    return This->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels);
}
static inline void ID3D11Device5_CheckCounterInfo(ID3D11Device5* This,D3D11_COUNTER_INFO *pCounterInfo) {
    This->lpVtbl->CheckCounterInfo(This,pCounterInfo);
}
static inline HRESULT ID3D11Device5_CheckCounter(ID3D11Device5* This,const D3D11_COUNTER_DESC *pDesc,D3D11_COUNTER_TYPE *pType,UINT *pActiveCounters,LPSTR szName,UINT *pNameLength,LPSTR szUnits,UINT *pUnitsLength,LPSTR szDescription,UINT *pDescriptionLength) {
    return This->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength);
}
static inline HRESULT ID3D11Device5_CheckFeatureSupport(ID3D11Device5* This,D3D11_FEATURE Feature,void *pFeatureSupportData,UINT FeatureSupportDataSize) {
    return This->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize);
}
static inline HRESULT ID3D11Device5_GetPrivateData(ID3D11Device5* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Device5_SetPrivateData(ID3D11Device5* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Device5_SetPrivateDataInterface(ID3D11Device5* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
static inline D3D_FEATURE_LEVEL ID3D11Device5_GetFeatureLevel(ID3D11Device5* This) {
    return This->lpVtbl->GetFeatureLevel(This);
}
static inline UINT ID3D11Device5_GetCreationFlags(ID3D11Device5* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
static inline HRESULT ID3D11Device5_GetDeviceRemovedReason(ID3D11Device5* This) {
    return This->lpVtbl->GetDeviceRemovedReason(This);
}
static inline void ID3D11Device5_GetImmediateContext(ID3D11Device5* This,ID3D11DeviceContext **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device5_SetExceptionMode(ID3D11Device5* This,UINT RaiseFlags) {
    return This->lpVtbl->SetExceptionMode(This,RaiseFlags);
}
static inline UINT ID3D11Device5_GetExceptionMode(ID3D11Device5* This) {
    return This->lpVtbl->GetExceptionMode(This);
}
/*** ID3D11Device1 methods ***/
static inline void ID3D11Device5_GetImmediateContext1(ID3D11Device5* This,ID3D11DeviceContext1 **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext1(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device5_CreateDeferredContext1(ID3D11Device5* This,UINT ContextFlags,ID3D11DeviceContext1 **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device5_CreateBlendState1(ID3D11Device5* This,const D3D11_BLEND_DESC1 *pBlendStateDesc,ID3D11BlendState1 **ppBlendState) {
    return This->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device5_CreateRasterizerState1(ID3D11Device5* This,const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,ID3D11RasterizerState1 **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device5_CreateDeviceContextState(ID3D11Device5* This,UINT Flags,const D3D_FEATURE_LEVEL *pFeatureLevels,UINT FeatureLevels,UINT SDKVersion,REFIID EmulatedInterface,D3D_FEATURE_LEVEL *pChosenFeatureLevel,ID3DDeviceContextState **ppContextState) {
    return This->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState);
}
static inline HRESULT ID3D11Device5_OpenSharedResource1(ID3D11Device5* This,HANDLE hResource,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource);
}
static inline HRESULT ID3D11Device5_OpenSharedResourceByName(ID3D11Device5* This,LPCWSTR lpName,DWORD dwDesiredAccess,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource);
}
/*** ID3D11Device2 methods ***/
static inline void ID3D11Device5_GetImmediateContext2(ID3D11Device5* This,ID3D11DeviceContext2 **context) {
    This->lpVtbl->GetImmediateContext2(This,context);
}
static inline HRESULT ID3D11Device5_CreateDeferredContext2(ID3D11Device5* This,UINT flags,ID3D11DeviceContext2 **context) {
    return This->lpVtbl->CreateDeferredContext2(This,flags,context);
}
static inline void ID3D11Device5_GetResourceTiling(ID3D11Device5* This,ID3D11Resource *resource,UINT *tile_count,D3D11_PACKED_MIP_DESC *mip_desc,D3D11_TILE_SHAPE *tile_shape,UINT *subresource_tiling_count,UINT first_subresource_tiling,D3D11_SUBRESOURCE_TILING *subresource_tiling) {
    This->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling);
}
static inline HRESULT ID3D11Device5_CheckMultisampleQualityLevels1(ID3D11Device5* This,DXGI_FORMAT format,UINT sample_count,UINT flags,UINT *quality_level_count) {
    return This->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count);
}
/*** ID3D11Device3 methods ***/
static inline HRESULT ID3D11Device5_CreateTexture2D1(ID3D11Device5* This,const D3D11_TEXTURE2D_DESC1 *desc,const D3D11_SUBRESOURCE_DATA *initial_data,ID3D11Texture2D1 **texture) {
    return This->lpVtbl->CreateTexture2D1(This,desc,initial_data,texture);
}
static inline HRESULT ID3D11Device5_CreateTexture3D1(ID3D11Device5* This,const D3D11_TEXTURE3D_DESC1 *desc,const D3D11_SUBRESOURCE_DATA *initial_data,ID3D11Texture3D1 **texture) {
    return This->lpVtbl->CreateTexture3D1(This,desc,initial_data,texture);
}
static inline HRESULT ID3D11Device5_CreateRasterizerState2(ID3D11Device5* This,const D3D11_RASTERIZER_DESC2 *desc,ID3D11RasterizerState2 **state) {
    return This->lpVtbl->CreateRasterizerState2(This,desc,state);
}
static inline HRESULT ID3D11Device5_CreateShaderResourceView1(ID3D11Device5* This,ID3D11Resource *resource,const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,ID3D11ShaderResourceView1 **view) {
    return This->lpVtbl->CreateShaderResourceView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device5_CreateUnorderedAccessView1(ID3D11Device5* This,ID3D11Resource *resource,const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,ID3D11UnorderedAccessView1 **view) {
    return This->lpVtbl->CreateUnorderedAccessView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device5_CreateRenderTargetView1(ID3D11Device5* This,ID3D11Resource *resource,const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,ID3D11RenderTargetView1 **view) {
    return This->lpVtbl->CreateRenderTargetView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device5_CreateQuery1(ID3D11Device5* This,const D3D11_QUERY_DESC1 *desc,ID3D11Query1 **query) {
    return This->lpVtbl->CreateQuery1(This,desc,query);
}
static inline void ID3D11Device5_GetImmediateContext3(ID3D11Device5* This,ID3D11DeviceContext3 **context) {
    This->lpVtbl->GetImmediateContext3(This,context);
}
static inline HRESULT ID3D11Device5_CreateDeferredContext3(ID3D11Device5* This,UINT flags,ID3D11DeviceContext3 **context) {
    return This->lpVtbl->CreateDeferredContext3(This,flags,context);
}
static inline void ID3D11Device5_WriteToSubresource(ID3D11Device5* This,ID3D11Resource *dst_resource,UINT dst_subresource,const D3D11_BOX *dst_box,const void *src_data,UINT src_row_pitch,UINT src_depth_pitch) {
    This->lpVtbl->WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch);
}
static inline void ID3D11Device5_ReadFromSubresource(ID3D11Device5* This,void *dst_data,UINT dst_row_pitch,UINT dst_depth_pitch,ID3D11Resource *src_resource,UINT src_subresource,const D3D11_BOX *src_box) {
    This->lpVtbl->ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box);
}
/*** ID3D11Device4 methods ***/
static inline HRESULT ID3D11Device5_RegisterDeviceRemovedEvent(ID3D11Device5* This,HANDLE event,DWORD *cookie) {
    return This->lpVtbl->RegisterDeviceRemovedEvent(This,event,cookie);
}
static inline void ID3D11Device5_UnregisterDeviceRemoved(ID3D11Device5* This,DWORD cookie) {
    This->lpVtbl->UnregisterDeviceRemoved(This,cookie);
}
/*** ID3D11Device5 methods ***/
static inline HRESULT ID3D11Device5_OpenSharedFence(ID3D11Device5* This,HANDLE handle,REFIID iid,void **fence) {
    return This->lpVtbl->OpenSharedFence(This,handle,iid,fence);
}
static inline HRESULT ID3D11Device5_CreateFence(ID3D11Device5* This,UINT64 initial_value,D3D11_FENCE_FLAG flags,REFIID iid,void **fence) {
    return This->lpVtbl->CreateFence(This,initial_value,flags,iid,fence);
}
#endif
#endif

#endif


#endif  /* __ID3D11Device5_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Multithread interface
 */
#ifndef __ID3D11Multithread_INTERFACE_DEFINED__
#define __ID3D11Multithread_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Multithread, 0x9b7e4e00, 0x342c, 0x4106, 0xa1,0x9f, 0x4f,0x27,0x04,0xf6,0x89,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9b7e4e00-342c-4106-a19f-4f2704f689f0")
ID3D11Multithread : public IUnknown
{
    virtual void STDMETHODCALLTYPE Enter(
        ) = 0;

    virtual void STDMETHODCALLTYPE Leave(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE SetMultithreadProtected(
        WINBOOL enable) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetMultithreadProtected(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Multithread, 0x9b7e4e00, 0x342c, 0x4106, 0xa1,0x9f, 0x4f,0x27,0x04,0xf6,0x89,0xf0)
#endif
#else
typedef struct ID3D11MultithreadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Multithread *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Multithread *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Multithread *This);

    /*** ID3D11Multithread methods ***/
    void (STDMETHODCALLTYPE *Enter)(
        ID3D11Multithread *This);

    void (STDMETHODCALLTYPE *Leave)(
        ID3D11Multithread *This);

    WINBOOL (STDMETHODCALLTYPE *SetMultithreadProtected)(
        ID3D11Multithread *This,
        WINBOOL enable);

    WINBOOL (STDMETHODCALLTYPE *GetMultithreadProtected)(
        ID3D11Multithread *This);

    END_INTERFACE
} ID3D11MultithreadVtbl;

interface ID3D11Multithread {
    CONST_VTBL ID3D11MultithreadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Multithread_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Multithread_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Multithread_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Multithread methods ***/
#define ID3D11Multithread_Enter(This) (This)->lpVtbl->Enter(This)
#define ID3D11Multithread_Leave(This) (This)->lpVtbl->Leave(This)
#define ID3D11Multithread_SetMultithreadProtected(This,enable) (This)->lpVtbl->SetMultithreadProtected(This,enable)
#define ID3D11Multithread_GetMultithreadProtected(This) (This)->lpVtbl->GetMultithreadProtected(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Multithread_QueryInterface(ID3D11Multithread* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Multithread_AddRef(ID3D11Multithread* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Multithread_Release(ID3D11Multithread* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Multithread methods ***/
static inline void ID3D11Multithread_Enter(ID3D11Multithread* This) {
    This->lpVtbl->Enter(This);
}
static inline void ID3D11Multithread_Leave(ID3D11Multithread* This) {
    This->lpVtbl->Leave(This);
}
static inline WINBOOL ID3D11Multithread_SetMultithreadProtected(ID3D11Multithread* This,WINBOOL enable) {
    return This->lpVtbl->SetMultithreadProtected(This,enable);
}
static inline WINBOOL ID3D11Multithread_GetMultithreadProtected(ID3D11Multithread* This) {
    return This->lpVtbl->GetMultithreadProtected(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11Multithread_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11VideoContext2 interface
 */
#ifndef __ID3D11VideoContext2_INTERFACE_DEFINED__
#define __ID3D11VideoContext2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11VideoContext2, 0xc4e7374c, 0x6243, 0x4d1b, 0xae,0x87, 0x52,0xb4,0xf7,0x40,0xe2,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c4e7374c-6243-4d1b-ae87-52b4f740e261")
ID3D11VideoContext2 : public ID3D11VideoContext1
{
    virtual void STDMETHODCALLTYPE VideoProcessorSetOutputHDRMetaData(
        ID3D11VideoProcessor *processor,
        DXGI_HDR_METADATA_TYPE type,
        UINT size,
        const void *meta_data) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetOutputHDRMetaData(
        ID3D11VideoProcessor *processor,
        DXGI_HDR_METADATA_TYPE *type,
        UINT size,
        void *meta_data) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorSetStreamHDRMetaData(
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_HDR_METADATA_TYPE type,
        UINT size,
        const void *meta_data) = 0;

    virtual void STDMETHODCALLTYPE VideoProcessorGetStreamHDRMetaData(
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_HDR_METADATA_TYPE *type,
        UINT size,
        void *meta_data) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11VideoContext2, 0xc4e7374c, 0x6243, 0x4d1b, 0xae,0x87, 0x52,0xb4,0xf7,0x40,0xe2,0x61)
#endif
#else
typedef struct ID3D11VideoContext2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11VideoContext2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11VideoContext2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11VideoContext2 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11VideoContext2 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11VideoContext2 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11VideoContext2 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11VideoContext2 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11VideoContext methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDecoderBuffer)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type,
        UINT *buffer_size,
        void **buffer);

    HRESULT (STDMETHODCALLTYPE *ReleaseDecoderBuffer)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        D3D11_VIDEO_DECODER_BUFFER_TYPE type);

    HRESULT (STDMETHODCALLTYPE *DecoderBeginFrame)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        ID3D11VideoDecoderOutputView *view,
        UINT key_size,
        const void *key);

    HRESULT (STDMETHODCALLTYPE *DecoderEndFrame)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder);

    HRESULT (STDMETHODCALLTYPE *SubmitDecoderBuffers)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        UINT buffers_count,
        const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc);

    HRESULT (STDMETHODCALLTYPE *DecoderExtension)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        const D3D11_VIDEO_DECODER_EXTENSION *extension);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputTargetRect)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputBackgroundColor)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL y_cb_cr,
        const D3D11_VIDEO_COLOR *color);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputColorSpace)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputAlphaFillMode)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,
        UINT stream_idx);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputConstriction)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable,
        SIZE size);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputStereoMode)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorSetOutputExtension)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputTargetRect)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputBackgroundColor)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *y_cb_cr,
        D3D11_VIDEO_COLOR *color);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputColorSpace)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputAlphaFillMode)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,
        UINT *stream_idx);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputConstriction)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled,
        SIZE *size);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputStereoMode)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetOutputExtension)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamFrameFormat)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT format);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamColorSpace)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamOutputRate)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,
        WINBOOL repeat,
        const DXGI_RATIONAL *custom_rate);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamSourceRect)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamDestRect)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamAlpha)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float alpha);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamPalette)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        const UINT *entries);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamPixelAspectRatio)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        const DXGI_RATIONAL *src_aspect_ratio,
        const DXGI_RATIONAL *dst_aspect_ratio);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamLumaKey)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        float lower,
        float upper);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamStereoFormat)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,
        WINBOOL left_view_frame0,
        WINBOOL base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,
        int mono_offset);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamAutoProcessingMode)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamFilter)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL enable,
        int level);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorSetStreamExtension)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamFrameFormat)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_FRAME_FORMAT *format);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamColorSpace)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamOutputRate)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,
        WINBOOL *repeat,
        DXGI_RATIONAL *custom_rate);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamSourceRect)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamDestRect)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        RECT *rect);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamAlpha)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *alpha);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamPalette)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        UINT entry_count,
        UINT *entries);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamPixelAspectRatio)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        DXGI_RATIONAL *src_aspect_ratio,
        DXGI_RATIONAL *dst_aspect_ratio);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamLumaKey)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        float *lower,
        float *upper);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamStereoFormat)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled,
        D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,
        WINBOOL *left_view_frame0,
        WINBOOL *base_view_frame0,
        D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,
        int *mono_offset);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamAutoProcessingMode)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enabled);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamFilter)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        D3D11_VIDEO_PROCESSOR_FILTER filter,
        WINBOOL *enabled,
        int *level);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetStreamExtension)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        const GUID *guid,
        UINT data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorBlt)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        ID3D11VideoProcessorOutputView *view,
        UINT frame_idx,
        UINT stream_count,
        const D3D11_VIDEO_PROCESSOR_STREAM *streams);

    HRESULT (STDMETHODCALLTYPE *NegotiateCryptoSessionKeyExchange)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session,
        UINT data_size,
        void *data);

    void (STDMETHODCALLTYPE *EncryptionBlt)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        UINT iv_size,
        void *iv);

    void (STDMETHODCALLTYPE *DecryptionBlt)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session,
        ID3D11Texture2D *src_surface,
        ID3D11Texture2D *dst_surface,
        D3D11_ENCRYPTED_BLOCK_INFO *block_info,
        UINT key_size,
        const void *key,
        UINT iv_size,
        void *iv);

    void (STDMETHODCALLTYPE *StartSessionKeyRefresh)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session,
        UINT random_number_size,
        void *random_number);

    void (STDMETHODCALLTYPE *FinishSessionKeyRefresh)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session);

    HRESULT (STDMETHODCALLTYPE *GetEncryptionBltKey)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session,
        UINT key_size,
        void *key);

    HRESULT (STDMETHODCALLTYPE *NegotiateAuthenticatedChannelKeyExchange)(
        ID3D11VideoContext2 *This,
        ID3D11AuthenticatedChannel *channel,
        UINT data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *QueryAuthenticatedChannel)(
        ID3D11VideoContext2 *This,
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        UINT output_size,
        void *output);

    HRESULT (STDMETHODCALLTYPE *ConfigureAuthenticatedChannel)(
        ID3D11VideoContext2 *This,
        ID3D11AuthenticatedChannel *channel,
        UINT input_size,
        const void *input,
        D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamRotation)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL enable,
        D3D11_VIDEO_PROCESSOR_ROTATION rotation);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamRotation)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_idx,
        WINBOOL *enable,
        D3D11_VIDEO_PROCESSOR_ROTATION *rotation);

    /*** ID3D11VideoContext1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SubmitDecoderBuffers1)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        UINT buffer_count,
        const D3D11_VIDEO_DECODER_BUFFER_DESC1 *buffer_desc);

    HRESULT (STDMETHODCALLTYPE *GetDataForNewHardwareKey)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session,
        UINT input_size,
        const void *input_data,
        UINT64 *output_data);

    HRESULT (STDMETHODCALLTYPE *CheckCryptoSessionStatus)(
        ID3D11VideoContext2 *This,
        ID3D11CryptoSession *session,
        D3D11_CRYPTO_SESSION_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *DecoderEnableDownsampling)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        DXGI_COLOR_SPACE_TYPE colour_space,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc,
        UINT reference_frame_count);

    HRESULT (STDMETHODCALLTYPE *DecoderUpdateDownsampling)(
        ID3D11VideoContext2 *This,
        ID3D11VideoDecoder *decoder,
        const D3D11_VIDEO_SAMPLE_DESC *output_desc);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputColorSpace1)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        DXGI_COLOR_SPACE_TYPE colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetOutputShaderUsage)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL shader_usage);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputColorSpace1)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        DXGI_COLOR_SPACE_TYPE *colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputShaderUsage)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        WINBOOL *shader_usage);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamColorSpace1)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_COLOR_SPACE_TYPE colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamMirror)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        WINBOOL enable,
        WINBOOL flip_horizontal,
        WINBOOL flip_vertical);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamColorSpace1)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_COLOR_SPACE_TYPE *colour_space);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamMirror)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        WINBOOL *enable,
        WINBOOL *flip_horizontal,
        WINBOOL *flip_vertical);

    HRESULT (STDMETHODCALLTYPE *VideoProcessorGetBehaviorHints)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT output_width,
        UINT output_height,
        DXGI_FORMAT output_format,
        UINT stream_count,
        const D3D11_VIDEO_PROCESSOR_STREAM_BEHAVIOR_HINT *streams,
        UINT *behaviour_hints);

    /*** ID3D11VideoContext2 methods ***/
    void (STDMETHODCALLTYPE *VideoProcessorSetOutputHDRMetaData)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        DXGI_HDR_METADATA_TYPE type,
        UINT size,
        const void *meta_data);

    void (STDMETHODCALLTYPE *VideoProcessorGetOutputHDRMetaData)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        DXGI_HDR_METADATA_TYPE *type,
        UINT size,
        void *meta_data);

    void (STDMETHODCALLTYPE *VideoProcessorSetStreamHDRMetaData)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_HDR_METADATA_TYPE type,
        UINT size,
        const void *meta_data);

    void (STDMETHODCALLTYPE *VideoProcessorGetStreamHDRMetaData)(
        ID3D11VideoContext2 *This,
        ID3D11VideoProcessor *processor,
        UINT stream_index,
        DXGI_HDR_METADATA_TYPE *type,
        UINT size,
        void *meta_data);

    END_INTERFACE
} ID3D11VideoContext2Vtbl;

interface ID3D11VideoContext2 {
    CONST_VTBL ID3D11VideoContext2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11VideoContext2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11VideoContext2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11VideoContext2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11VideoContext2_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11VideoContext2_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11VideoContext2_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11VideoContext2_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11VideoContext methods ***/
#define ID3D11VideoContext2_GetDecoderBuffer(This,decoder,type,buffer_size,buffer) (This)->lpVtbl->GetDecoderBuffer(This,decoder,type,buffer_size,buffer)
#define ID3D11VideoContext2_ReleaseDecoderBuffer(This,decoder,type) (This)->lpVtbl->ReleaseDecoderBuffer(This,decoder,type)
#define ID3D11VideoContext2_DecoderBeginFrame(This,decoder,view,key_size,key) (This)->lpVtbl->DecoderBeginFrame(This,decoder,view,key_size,key)
#define ID3D11VideoContext2_DecoderEndFrame(This,decoder) (This)->lpVtbl->DecoderEndFrame(This,decoder)
#define ID3D11VideoContext2_SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc) (This)->lpVtbl->SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc)
#define ID3D11VideoContext2_DecoderExtension(This,decoder,extension) (This)->lpVtbl->DecoderExtension(This,decoder,extension)
#define ID3D11VideoContext2_VideoProcessorSetOutputTargetRect(This,processor,enable,rect) (This)->lpVtbl->VideoProcessorSetOutputTargetRect(This,processor,enable,rect)
#define ID3D11VideoContext2_VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color) (This)->lpVtbl->VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color)
#define ID3D11VideoContext2_VideoProcessorSetOutputColorSpace(This,processor,color_space) (This)->lpVtbl->VideoProcessorSetOutputColorSpace(This,processor,color_space)
#define ID3D11VideoContext2_VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx) (This)->lpVtbl->VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx)
#define ID3D11VideoContext2_VideoProcessorSetOutputConstriction(This,processor,enable,size) (This)->lpVtbl->VideoProcessorSetOutputConstriction(This,processor,enable,size)
#define ID3D11VideoContext2_VideoProcessorSetOutputStereoMode(This,processor,enable) (This)->lpVtbl->VideoProcessorSetOutputStereoMode(This,processor,enable)
#define ID3D11VideoContext2_VideoProcessorSetOutputExtension(This,processor,guid,data_size,data) (This)->lpVtbl->VideoProcessorSetOutputExtension(This,processor,guid,data_size,data)
#define ID3D11VideoContext2_VideoProcessorGetOutputTargetRect(This,processor,enabled,rect) (This)->lpVtbl->VideoProcessorGetOutputTargetRect(This,processor,enabled,rect)
#define ID3D11VideoContext2_VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color) (This)->lpVtbl->VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color)
#define ID3D11VideoContext2_VideoProcessorGetOutputColorSpace(This,processor,color_space) (This)->lpVtbl->VideoProcessorGetOutputColorSpace(This,processor,color_space)
#define ID3D11VideoContext2_VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx) (This)->lpVtbl->VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx)
#define ID3D11VideoContext2_VideoProcessorGetOutputConstriction(This,processor,enabled,size) (This)->lpVtbl->VideoProcessorGetOutputConstriction(This,processor,enabled,size)
#define ID3D11VideoContext2_VideoProcessorGetOutputStereoMode(This,processor,enabled) (This)->lpVtbl->VideoProcessorGetOutputStereoMode(This,processor,enabled)
#define ID3D11VideoContext2_VideoProcessorGetOutputExtension(This,processor,guid,data_size,data) (This)->lpVtbl->VideoProcessorGetOutputExtension(This,processor,guid,data_size,data)
#define ID3D11VideoContext2_VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format) (This)->lpVtbl->VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format)
#define ID3D11VideoContext2_VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space) (This)->lpVtbl->VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space)
#define ID3D11VideoContext2_VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate) (This)->lpVtbl->VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate)
#define ID3D11VideoContext2_VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect) (This)->lpVtbl->VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect)
#define ID3D11VideoContext2_VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect) (This)->lpVtbl->VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect)
#define ID3D11VideoContext2_VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha) (This)->lpVtbl->VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha)
#define ID3D11VideoContext2_VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries) (This)->lpVtbl->VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries)
#define ID3D11VideoContext2_VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio) (This)->lpVtbl->VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio)
#define ID3D11VideoContext2_VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper) (This)->lpVtbl->VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper)
#define ID3D11VideoContext2_VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset) (This)->lpVtbl->VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset)
#define ID3D11VideoContext2_VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable) (This)->lpVtbl->VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable)
#define ID3D11VideoContext2_VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level) (This)->lpVtbl->VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level)
#define ID3D11VideoContext2_VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data) (This)->lpVtbl->VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data)
#define ID3D11VideoContext2_VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format) (This)->lpVtbl->VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format)
#define ID3D11VideoContext2_VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space) (This)->lpVtbl->VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space)
#define ID3D11VideoContext2_VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate) (This)->lpVtbl->VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate)
#define ID3D11VideoContext2_VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect) (This)->lpVtbl->VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect)
#define ID3D11VideoContext2_VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect) (This)->lpVtbl->VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect)
#define ID3D11VideoContext2_VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha) (This)->lpVtbl->VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha)
#define ID3D11VideoContext2_VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries) (This)->lpVtbl->VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries)
#define ID3D11VideoContext2_VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio) (This)->lpVtbl->VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio)
#define ID3D11VideoContext2_VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper) (This)->lpVtbl->VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper)
#define ID3D11VideoContext2_VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset) (This)->lpVtbl->VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset)
#define ID3D11VideoContext2_VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled) (This)->lpVtbl->VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled)
#define ID3D11VideoContext2_VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level) (This)->lpVtbl->VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level)
#define ID3D11VideoContext2_VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data) (This)->lpVtbl->VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data)
#define ID3D11VideoContext2_VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams) (This)->lpVtbl->VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams)
#define ID3D11VideoContext2_NegotiateCryptoSessionKeyExchange(This,session,data_size,data) (This)->lpVtbl->NegotiateCryptoSessionKeyExchange(This,session,data_size,data)
#define ID3D11VideoContext2_EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv) (This)->lpVtbl->EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv)
#define ID3D11VideoContext2_DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv) (This)->lpVtbl->DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv)
#define ID3D11VideoContext2_StartSessionKeyRefresh(This,session,random_number_size,random_number) (This)->lpVtbl->StartSessionKeyRefresh(This,session,random_number_size,random_number)
#define ID3D11VideoContext2_FinishSessionKeyRefresh(This,session) (This)->lpVtbl->FinishSessionKeyRefresh(This,session)
#define ID3D11VideoContext2_GetEncryptionBltKey(This,session,key_size,key) (This)->lpVtbl->GetEncryptionBltKey(This,session,key_size,key)
#define ID3D11VideoContext2_NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data) (This)->lpVtbl->NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data)
#define ID3D11VideoContext2_QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output) (This)->lpVtbl->QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output)
#define ID3D11VideoContext2_ConfigureAuthenticatedChannel(This,channel,input_size,input,output) (This)->lpVtbl->ConfigureAuthenticatedChannel(This,channel,input_size,input,output)
#define ID3D11VideoContext2_VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation) (This)->lpVtbl->VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation)
#define ID3D11VideoContext2_VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation) (This)->lpVtbl->VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation)
/*** ID3D11VideoContext1 methods ***/
#define ID3D11VideoContext2_SubmitDecoderBuffers1(This,decoder,buffer_count,buffer_desc) (This)->lpVtbl->SubmitDecoderBuffers1(This,decoder,buffer_count,buffer_desc)
#define ID3D11VideoContext2_GetDataForNewHardwareKey(This,session,input_size,input_data,output_data) (This)->lpVtbl->GetDataForNewHardwareKey(This,session,input_size,input_data,output_data)
#define ID3D11VideoContext2_CheckCryptoSessionStatus(This,session,status) (This)->lpVtbl->CheckCryptoSessionStatus(This,session,status)
#define ID3D11VideoContext2_DecoderEnableDownsampling(This,decoder,colour_space,output_desc,reference_frame_count) (This)->lpVtbl->DecoderEnableDownsampling(This,decoder,colour_space,output_desc,reference_frame_count)
#define ID3D11VideoContext2_DecoderUpdateDownsampling(This,decoder,output_desc) (This)->lpVtbl->DecoderUpdateDownsampling(This,decoder,output_desc)
#define ID3D11VideoContext2_VideoProcessorSetOutputColorSpace1(This,processor,colour_space) (This)->lpVtbl->VideoProcessorSetOutputColorSpace1(This,processor,colour_space)
#define ID3D11VideoContext2_VideoProcessorSetOutputShaderUsage(This,processor,shader_usage) (This)->lpVtbl->VideoProcessorSetOutputShaderUsage(This,processor,shader_usage)
#define ID3D11VideoContext2_VideoProcessorGetOutputColorSpace1(This,processor,colour_space) (This)->lpVtbl->VideoProcessorGetOutputColorSpace1(This,processor,colour_space)
#define ID3D11VideoContext2_VideoProcessorGetOutputShaderUsage(This,processor,shader_usage) (This)->lpVtbl->VideoProcessorGetOutputShaderUsage(This,processor,shader_usage)
#define ID3D11VideoContext2_VideoProcessorSetStreamColorSpace1(This,processor,stream_index,colour_space) (This)->lpVtbl->VideoProcessorSetStreamColorSpace1(This,processor,stream_index,colour_space)
#define ID3D11VideoContext2_VideoProcessorSetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical) (This)->lpVtbl->VideoProcessorSetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical)
#define ID3D11VideoContext2_VideoProcessorGetStreamColorSpace1(This,processor,stream_index,colour_space) (This)->lpVtbl->VideoProcessorGetStreamColorSpace1(This,processor,stream_index,colour_space)
#define ID3D11VideoContext2_VideoProcessorGetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical) (This)->lpVtbl->VideoProcessorGetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical)
#define ID3D11VideoContext2_VideoProcessorGetBehaviorHints(This,processor,output_width,output_height,output_format,stream_count,streams,behaviour_hints) (This)->lpVtbl->VideoProcessorGetBehaviorHints(This,processor,output_width,output_height,output_format,stream_count,streams,behaviour_hints)
/*** ID3D11VideoContext2 methods ***/
#define ID3D11VideoContext2_VideoProcessorSetOutputHDRMetaData(This,processor,type,size,meta_data) (This)->lpVtbl->VideoProcessorSetOutputHDRMetaData(This,processor,type,size,meta_data)
#define ID3D11VideoContext2_VideoProcessorGetOutputHDRMetaData(This,processor,type,size,meta_data) (This)->lpVtbl->VideoProcessorGetOutputHDRMetaData(This,processor,type,size,meta_data)
#define ID3D11VideoContext2_VideoProcessorSetStreamHDRMetaData(This,processor,stream_index,type,size,meta_data) (This)->lpVtbl->VideoProcessorSetStreamHDRMetaData(This,processor,stream_index,type,size,meta_data)
#define ID3D11VideoContext2_VideoProcessorGetStreamHDRMetaData(This,processor,stream_index,type,size,meta_data) (This)->lpVtbl->VideoProcessorGetStreamHDRMetaData(This,processor,stream_index,type,size,meta_data)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11VideoContext2_QueryInterface(ID3D11VideoContext2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11VideoContext2_AddRef(ID3D11VideoContext2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11VideoContext2_Release(ID3D11VideoContext2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11VideoContext2_GetDevice(ID3D11VideoContext2* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11VideoContext2_GetPrivateData(ID3D11VideoContext2* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11VideoContext2_SetPrivateData(ID3D11VideoContext2* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11VideoContext2_SetPrivateDataInterface(ID3D11VideoContext2* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11VideoContext methods ***/
static inline HRESULT ID3D11VideoContext2_GetDecoderBuffer(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,D3D11_VIDEO_DECODER_BUFFER_TYPE type,UINT *buffer_size,void **buffer) {
    return This->lpVtbl->GetDecoderBuffer(This,decoder,type,buffer_size,buffer);
}
static inline HRESULT ID3D11VideoContext2_ReleaseDecoderBuffer(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,D3D11_VIDEO_DECODER_BUFFER_TYPE type) {
    return This->lpVtbl->ReleaseDecoderBuffer(This,decoder,type);
}
static inline HRESULT ID3D11VideoContext2_DecoderBeginFrame(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,ID3D11VideoDecoderOutputView *view,UINT key_size,const void *key) {
    return This->lpVtbl->DecoderBeginFrame(This,decoder,view,key_size,key);
}
static inline HRESULT ID3D11VideoContext2_DecoderEndFrame(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder) {
    return This->lpVtbl->DecoderEndFrame(This,decoder);
}
static inline HRESULT ID3D11VideoContext2_SubmitDecoderBuffers(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,UINT buffers_count,const D3D11_VIDEO_DECODER_BUFFER_DESC *buffer_desc) {
    return This->lpVtbl->SubmitDecoderBuffers(This,decoder,buffers_count,buffer_desc);
}
static inline HRESULT ID3D11VideoContext2_DecoderExtension(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,const D3D11_VIDEO_DECODER_EXTENSION *extension) {
    return This->lpVtbl->DecoderExtension(This,decoder,extension);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputTargetRect(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetOutputTargetRect(This,processor,enable,rect);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputBackgroundColor(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL y_cb_cr,const D3D11_VIDEO_COLOR *color) {
    This->lpVtbl->VideoProcessorSetOutputBackgroundColor(This,processor,y_cb_cr,color);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputColorSpace(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorSetOutputColorSpace(This,processor,color_space);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputAlphaFillMode(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE alpha_fill_mode,UINT stream_idx) {
    This->lpVtbl->VideoProcessorSetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputConstriction(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL enable,SIZE size) {
    This->lpVtbl->VideoProcessorSetOutputConstriction(This,processor,enable,size);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputStereoMode(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL enable) {
    This->lpVtbl->VideoProcessorSetOutputStereoMode(This,processor,enable);
}
static inline HRESULT ID3D11VideoContext2_VideoProcessorSetOutputExtension(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorSetOutputExtension(This,processor,guid,data_size,data);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputTargetRect(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetOutputTargetRect(This,processor,enabled,rect);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputBackgroundColor(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL *y_cb_cr,D3D11_VIDEO_COLOR *color) {
    This->lpVtbl->VideoProcessorGetOutputBackgroundColor(This,processor,y_cb_cr,color);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputColorSpace(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorGetOutputColorSpace(This,processor,color_space);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputAlphaFillMode(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,D3D11_VIDEO_PROCESSOR_ALPHA_FILL_MODE *alpha_fill_mode,UINT *stream_idx) {
    This->lpVtbl->VideoProcessorGetOutputAlphaFillMode(This,processor,alpha_fill_mode,stream_idx);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputConstriction(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL *enabled,SIZE *size) {
    This->lpVtbl->VideoProcessorGetOutputConstriction(This,processor,enabled,size);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputStereoMode(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL *enabled) {
    This->lpVtbl->VideoProcessorGetOutputStereoMode(This,processor,enabled);
}
static inline HRESULT ID3D11VideoContext2_VideoProcessorGetOutputExtension(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorGetOutputExtension(This,processor,guid,data_size,data);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamFrameFormat(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_FRAME_FORMAT format) {
    This->lpVtbl->VideoProcessorSetStreamFrameFormat(This,processor,stream_idx,format);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamColorSpace(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,const D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorSetStreamColorSpace(This,processor,stream_idx,color_space);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamOutputRate(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_OUTPUT_RATE rate,WINBOOL repeat,const DXGI_RATIONAL *custom_rate) {
    This->lpVtbl->VideoProcessorSetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamSourceRect(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetStreamSourceRect(This,processor,stream_idx,enable,rect);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamDestRect(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const RECT *rect) {
    This->lpVtbl->VideoProcessorSetStreamDestRect(This,processor,stream_idx,enable,rect);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamAlpha(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,float alpha) {
    This->lpVtbl->VideoProcessorSetStreamAlpha(This,processor,stream_idx,enable,alpha);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamPalette(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,UINT entry_count,const UINT *entries) {
    This->lpVtbl->VideoProcessorSetStreamPalette(This,processor,stream_idx,entry_count,entries);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamPixelAspectRatio(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,const DXGI_RATIONAL *src_aspect_ratio,const DXGI_RATIONAL *dst_aspect_ratio) {
    This->lpVtbl->VideoProcessorSetStreamPixelAspectRatio(This,processor,stream_idx,enable,src_aspect_ratio,dst_aspect_ratio);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamLumaKey(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,float lower,float upper) {
    This->lpVtbl->VideoProcessorSetStreamLumaKey(This,processor,stream_idx,enable,lower,upper);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamStereoFormat(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,D3D11_VIDEO_PROCESSOR_STEREO_FORMAT format,WINBOOL left_view_frame0,WINBOOL base_view_frame0,D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE flip_mode,int mono_offset) {
    This->lpVtbl->VideoProcessorSetStreamStereoFormat(This,processor,stream_idx,enable,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamAutoProcessingMode(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable) {
    This->lpVtbl->VideoProcessorSetStreamAutoProcessingMode(This,processor,stream_idx,enable);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamFilter(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_FILTER filter,WINBOOL enable,int level) {
    This->lpVtbl->VideoProcessorSetStreamFilter(This,processor,stream_idx,filter,enable,level);
}
static inline HRESULT ID3D11VideoContext2_VideoProcessorSetStreamExtension(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorSetStreamExtension(This,processor,stream_idx,guid,data_size,data);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamFrameFormat(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_FRAME_FORMAT *format) {
    This->lpVtbl->VideoProcessorGetStreamFrameFormat(This,processor,stream_idx,format);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamColorSpace(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_COLOR_SPACE *color_space) {
    This->lpVtbl->VideoProcessorGetStreamColorSpace(This,processor,stream_idx,color_space);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamOutputRate(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_OUTPUT_RATE *rate,WINBOOL *repeat,DXGI_RATIONAL *custom_rate) {
    This->lpVtbl->VideoProcessorGetStreamOutputRate(This,processor,stream_idx,rate,repeat,custom_rate);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamSourceRect(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetStreamSourceRect(This,processor,stream_idx,enabled,rect);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamDestRect(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,RECT *rect) {
    This->lpVtbl->VideoProcessorGetStreamDestRect(This,processor,stream_idx,enabled,rect);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamAlpha(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,float *alpha) {
    This->lpVtbl->VideoProcessorGetStreamAlpha(This,processor,stream_idx,enabled,alpha);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamPalette(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,UINT entry_count,UINT *entries) {
    This->lpVtbl->VideoProcessorGetStreamPalette(This,processor,stream_idx,entry_count,entries);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamPixelAspectRatio(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,DXGI_RATIONAL *src_aspect_ratio,DXGI_RATIONAL *dst_aspect_ratio) {
    This->lpVtbl->VideoProcessorGetStreamPixelAspectRatio(This,processor,stream_idx,enabled,src_aspect_ratio,dst_aspect_ratio);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamLumaKey(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,float *lower,float *upper) {
    This->lpVtbl->VideoProcessorGetStreamLumaKey(This,processor,stream_idx,enabled,lower,upper);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamStereoFormat(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled,D3D11_VIDEO_PROCESSOR_STEREO_FORMAT *format,WINBOOL *left_view_frame0,WINBOOL *base_view_frame0,D3D11_VIDEO_PROCESSOR_STEREO_FLIP_MODE *flip_mode,int *mono_offset) {
    This->lpVtbl->VideoProcessorGetStreamStereoFormat(This,processor,stream_idx,enabled,format,left_view_frame0,base_view_frame0,flip_mode,mono_offset);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamAutoProcessingMode(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enabled) {
    This->lpVtbl->VideoProcessorGetStreamAutoProcessingMode(This,processor,stream_idx,enabled);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamFilter(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,D3D11_VIDEO_PROCESSOR_FILTER filter,WINBOOL *enabled,int *level) {
    This->lpVtbl->VideoProcessorGetStreamFilter(This,processor,stream_idx,filter,enabled,level);
}
static inline HRESULT ID3D11VideoContext2_VideoProcessorGetStreamExtension(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,const GUID *guid,UINT data_size,void *data) {
    return This->lpVtbl->VideoProcessorGetStreamExtension(This,processor,stream_idx,guid,data_size,data);
}
static inline HRESULT ID3D11VideoContext2_VideoProcessorBlt(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,ID3D11VideoProcessorOutputView *view,UINT frame_idx,UINT stream_count,const D3D11_VIDEO_PROCESSOR_STREAM *streams) {
    return This->lpVtbl->VideoProcessorBlt(This,processor,view,frame_idx,stream_count,streams);
}
static inline HRESULT ID3D11VideoContext2_NegotiateCryptoSessionKeyExchange(ID3D11VideoContext2* This,ID3D11CryptoSession *session,UINT data_size,void *data) {
    return This->lpVtbl->NegotiateCryptoSessionKeyExchange(This,session,data_size,data);
}
static inline void ID3D11VideoContext2_EncryptionBlt(ID3D11VideoContext2* This,ID3D11CryptoSession *session,ID3D11Texture2D *src_surface,ID3D11Texture2D *dst_surface,UINT iv_size,void *iv) {
    This->lpVtbl->EncryptionBlt(This,session,src_surface,dst_surface,iv_size,iv);
}
static inline void ID3D11VideoContext2_DecryptionBlt(ID3D11VideoContext2* This,ID3D11CryptoSession *session,ID3D11Texture2D *src_surface,ID3D11Texture2D *dst_surface,D3D11_ENCRYPTED_BLOCK_INFO *block_info,UINT key_size,const void *key,UINT iv_size,void *iv) {
    This->lpVtbl->DecryptionBlt(This,session,src_surface,dst_surface,block_info,key_size,key,iv_size,iv);
}
static inline void ID3D11VideoContext2_StartSessionKeyRefresh(ID3D11VideoContext2* This,ID3D11CryptoSession *session,UINT random_number_size,void *random_number) {
    This->lpVtbl->StartSessionKeyRefresh(This,session,random_number_size,random_number);
}
static inline void ID3D11VideoContext2_FinishSessionKeyRefresh(ID3D11VideoContext2* This,ID3D11CryptoSession *session) {
    This->lpVtbl->FinishSessionKeyRefresh(This,session);
}
static inline HRESULT ID3D11VideoContext2_GetEncryptionBltKey(ID3D11VideoContext2* This,ID3D11CryptoSession *session,UINT key_size,void *key) {
    return This->lpVtbl->GetEncryptionBltKey(This,session,key_size,key);
}
static inline HRESULT ID3D11VideoContext2_NegotiateAuthenticatedChannelKeyExchange(ID3D11VideoContext2* This,ID3D11AuthenticatedChannel *channel,UINT data_size,void *data) {
    return This->lpVtbl->NegotiateAuthenticatedChannelKeyExchange(This,channel,data_size,data);
}
static inline HRESULT ID3D11VideoContext2_QueryAuthenticatedChannel(ID3D11VideoContext2* This,ID3D11AuthenticatedChannel *channel,UINT input_size,const void *input,UINT output_size,void *output) {
    return This->lpVtbl->QueryAuthenticatedChannel(This,channel,input_size,input,output_size,output);
}
static inline HRESULT ID3D11VideoContext2_ConfigureAuthenticatedChannel(ID3D11VideoContext2* This,ID3D11AuthenticatedChannel *channel,UINT input_size,const void *input,D3D11_AUTHENTICATED_CONFIGURE_OUTPUT *output) {
    return This->lpVtbl->ConfigureAuthenticatedChannel(This,channel,input_size,input,output);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamRotation(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL enable,D3D11_VIDEO_PROCESSOR_ROTATION rotation) {
    This->lpVtbl->VideoProcessorSetStreamRotation(This,processor,stream_idx,enable,rotation);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamRotation(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_idx,WINBOOL *enable,D3D11_VIDEO_PROCESSOR_ROTATION *rotation) {
    This->lpVtbl->VideoProcessorGetStreamRotation(This,processor,stream_idx,enable,rotation);
}
/*** ID3D11VideoContext1 methods ***/
static inline HRESULT ID3D11VideoContext2_SubmitDecoderBuffers1(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,UINT buffer_count,const D3D11_VIDEO_DECODER_BUFFER_DESC1 *buffer_desc) {
    return This->lpVtbl->SubmitDecoderBuffers1(This,decoder,buffer_count,buffer_desc);
}
static inline HRESULT ID3D11VideoContext2_GetDataForNewHardwareKey(ID3D11VideoContext2* This,ID3D11CryptoSession *session,UINT input_size,const void *input_data,UINT64 *output_data) {
    return This->lpVtbl->GetDataForNewHardwareKey(This,session,input_size,input_data,output_data);
}
static inline HRESULT ID3D11VideoContext2_CheckCryptoSessionStatus(ID3D11VideoContext2* This,ID3D11CryptoSession *session,D3D11_CRYPTO_SESSION_STATUS *status) {
    return This->lpVtbl->CheckCryptoSessionStatus(This,session,status);
}
static inline HRESULT ID3D11VideoContext2_DecoderEnableDownsampling(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,DXGI_COLOR_SPACE_TYPE colour_space,const D3D11_VIDEO_SAMPLE_DESC *output_desc,UINT reference_frame_count) {
    return This->lpVtbl->DecoderEnableDownsampling(This,decoder,colour_space,output_desc,reference_frame_count);
}
static inline HRESULT ID3D11VideoContext2_DecoderUpdateDownsampling(ID3D11VideoContext2* This,ID3D11VideoDecoder *decoder,const D3D11_VIDEO_SAMPLE_DESC *output_desc) {
    return This->lpVtbl->DecoderUpdateDownsampling(This,decoder,output_desc);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputColorSpace1(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,DXGI_COLOR_SPACE_TYPE colour_space) {
    This->lpVtbl->VideoProcessorSetOutputColorSpace1(This,processor,colour_space);
}
static inline void ID3D11VideoContext2_VideoProcessorSetOutputShaderUsage(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL shader_usage) {
    This->lpVtbl->VideoProcessorSetOutputShaderUsage(This,processor,shader_usage);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputColorSpace1(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,DXGI_COLOR_SPACE_TYPE *colour_space) {
    This->lpVtbl->VideoProcessorGetOutputColorSpace1(This,processor,colour_space);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputShaderUsage(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,WINBOOL *shader_usage) {
    This->lpVtbl->VideoProcessorGetOutputShaderUsage(This,processor,shader_usage);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamColorSpace1(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_index,DXGI_COLOR_SPACE_TYPE colour_space) {
    This->lpVtbl->VideoProcessorSetStreamColorSpace1(This,processor,stream_index,colour_space);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamMirror(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_index,WINBOOL enable,WINBOOL flip_horizontal,WINBOOL flip_vertical) {
    This->lpVtbl->VideoProcessorSetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamColorSpace1(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_index,DXGI_COLOR_SPACE_TYPE *colour_space) {
    This->lpVtbl->VideoProcessorGetStreamColorSpace1(This,processor,stream_index,colour_space);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamMirror(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_index,WINBOOL *enable,WINBOOL *flip_horizontal,WINBOOL *flip_vertical) {
    This->lpVtbl->VideoProcessorGetStreamMirror(This,processor,stream_index,enable,flip_horizontal,flip_vertical);
}
static inline HRESULT ID3D11VideoContext2_VideoProcessorGetBehaviorHints(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT output_width,UINT output_height,DXGI_FORMAT output_format,UINT stream_count,const D3D11_VIDEO_PROCESSOR_STREAM_BEHAVIOR_HINT *streams,UINT *behaviour_hints) {
    return This->lpVtbl->VideoProcessorGetBehaviorHints(This,processor,output_width,output_height,output_format,stream_count,streams,behaviour_hints);
}
/*** ID3D11VideoContext2 methods ***/
static inline void ID3D11VideoContext2_VideoProcessorSetOutputHDRMetaData(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,DXGI_HDR_METADATA_TYPE type,UINT size,const void *meta_data) {
    This->lpVtbl->VideoProcessorSetOutputHDRMetaData(This,processor,type,size,meta_data);
}
static inline void ID3D11VideoContext2_VideoProcessorGetOutputHDRMetaData(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,DXGI_HDR_METADATA_TYPE *type,UINT size,void *meta_data) {
    This->lpVtbl->VideoProcessorGetOutputHDRMetaData(This,processor,type,size,meta_data);
}
static inline void ID3D11VideoContext2_VideoProcessorSetStreamHDRMetaData(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_index,DXGI_HDR_METADATA_TYPE type,UINT size,const void *meta_data) {
    This->lpVtbl->VideoProcessorSetStreamHDRMetaData(This,processor,stream_index,type,size,meta_data);
}
static inline void ID3D11VideoContext2_VideoProcessorGetStreamHDRMetaData(ID3D11VideoContext2* This,ID3D11VideoProcessor *processor,UINT stream_index,DXGI_HDR_METADATA_TYPE *type,UINT size,void *meta_data) {
    This->lpVtbl->VideoProcessorGetStreamHDRMetaData(This,processor,stream_index,type,size,meta_data);
}
#endif
#endif

#endif


#endif  /* __ID3D11VideoContext2_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d11_4_h__ */
