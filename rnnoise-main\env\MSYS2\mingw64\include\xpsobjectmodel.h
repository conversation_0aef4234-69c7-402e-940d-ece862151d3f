/*** Autogenerated by WIDL 10.12 from include/xpsobjectmodel.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __xpsobjectmodel_h__
#define __xpsobjectmodel_h__

/* Forward declarations */

#ifndef __IXpsOMShareable_FWD_DEFINED__
#define __IXpsOMShareable_FWD_DEFINED__
typedef interface IXpsOMShareable IXpsOMShareable;
#ifdef __cplusplus
interface IXpsOMShareable;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPart_FWD_DEFINED__
#define __IXpsOMPart_FWD_DEFINED__
typedef interface IXpsOMPart IXpsOMPart;
#ifdef __cplusplus
interface IXpsOMPart;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGlyphsEditor_FWD_DEFINED__
#define __IXpsOMGlyphsEditor_FWD_DEFINED__
typedef interface IXpsOMGlyphsEditor IXpsOMGlyphsEditor;
#ifdef __cplusplus
interface IXpsOMGlyphsEditor;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDashCollection_FWD_DEFINED__
#define __IXpsOMDashCollection_FWD_DEFINED__
typedef interface IXpsOMDashCollection IXpsOMDashCollection;
#ifdef __cplusplus
interface IXpsOMDashCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGeometryFigure_FWD_DEFINED__
#define __IXpsOMGeometryFigure_FWD_DEFINED__
typedef interface IXpsOMGeometryFigure IXpsOMGeometryFigure;
#ifdef __cplusplus
interface IXpsOMGeometryFigure;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGeometryFigureCollection_FWD_DEFINED__
#define __IXpsOMGeometryFigureCollection_FWD_DEFINED__
typedef interface IXpsOMGeometryFigureCollection IXpsOMGeometryFigureCollection;
#ifdef __cplusplus
interface IXpsOMGeometryFigureCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGradientStopCollection_FWD_DEFINED__
#define __IXpsOMGradientStopCollection_FWD_DEFINED__
typedef interface IXpsOMGradientStopCollection IXpsOMGradientStopCollection;
#ifdef __cplusplus
interface IXpsOMGradientStopCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGradientStop_FWD_DEFINED__
#define __IXpsOMGradientStop_FWD_DEFINED__
typedef interface IXpsOMGradientStop IXpsOMGradientStop;
#ifdef __cplusplus
interface IXpsOMGradientStop;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPartResources_FWD_DEFINED__
#define __IXpsOMPartResources_FWD_DEFINED__
typedef interface IXpsOMPartResources IXpsOMPartResources;
#ifdef __cplusplus
interface IXpsOMPartResources;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMFontResourceCollection_FWD_DEFINED__
#define __IXpsOMFontResourceCollection_FWD_DEFINED__
typedef interface IXpsOMFontResourceCollection IXpsOMFontResourceCollection;
#ifdef __cplusplus
interface IXpsOMFontResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMImageResourceCollection_FWD_DEFINED__
#define __IXpsOMImageResourceCollection_FWD_DEFINED__
typedef interface IXpsOMImageResourceCollection IXpsOMImageResourceCollection;
#ifdef __cplusplus
interface IXpsOMImageResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMColorProfileResourceCollection_FWD_DEFINED__
#define __IXpsOMColorProfileResourceCollection_FWD_DEFINED__
typedef interface IXpsOMColorProfileResourceCollection IXpsOMColorProfileResourceCollection;
#ifdef __cplusplus
interface IXpsOMColorProfileResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMRemoteDictionaryResourceCollection_FWD_DEFINED__
#define __IXpsOMRemoteDictionaryResourceCollection_FWD_DEFINED__
typedef interface IXpsOMRemoteDictionaryResourceCollection IXpsOMRemoteDictionaryResourceCollection;
#ifdef __cplusplus
interface IXpsOMRemoteDictionaryResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMSignatureBlockResourceCollection_FWD_DEFINED__
#define __IXpsOMSignatureBlockResourceCollection_FWD_DEFINED__
typedef interface IXpsOMSignatureBlockResourceCollection IXpsOMSignatureBlockResourceCollection;
#ifdef __cplusplus
interface IXpsOMSignatureBlockResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMVisualCollection_FWD_DEFINED__
#define __IXpsOMVisualCollection_FWD_DEFINED__
typedef interface IXpsOMVisualCollection IXpsOMVisualCollection;
#ifdef __cplusplus
interface IXpsOMVisualCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDictionary_FWD_DEFINED__
#define __IXpsOMDictionary_FWD_DEFINED__
typedef interface IXpsOMDictionary IXpsOMDictionary;
#ifdef __cplusplus
interface IXpsOMDictionary;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPageReference_FWD_DEFINED__
#define __IXpsOMPageReference_FWD_DEFINED__
typedef interface IXpsOMPageReference IXpsOMPageReference;
#ifdef __cplusplus
interface IXpsOMPageReference;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPageReferenceCollection_FWD_DEFINED__
#define __IXpsOMPageReferenceCollection_FWD_DEFINED__
typedef interface IXpsOMPageReferenceCollection IXpsOMPageReferenceCollection;
#ifdef __cplusplus
interface IXpsOMPageReferenceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDocumentCollection_FWD_DEFINED__
#define __IXpsOMDocumentCollection_FWD_DEFINED__
typedef interface IXpsOMDocumentCollection IXpsOMDocumentCollection;
#ifdef __cplusplus
interface IXpsOMDocumentCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackage_FWD_DEFINED__
#define __IXpsOMPackage_FWD_DEFINED__
typedef interface IXpsOMPackage IXpsOMPackage;
#ifdef __cplusplus
interface IXpsOMPackage;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMObjectFactory_FWD_DEFINED__
#define __IXpsOMObjectFactory_FWD_DEFINED__
typedef interface IXpsOMObjectFactory IXpsOMObjectFactory;
#ifdef __cplusplus
interface IXpsOMObjectFactory;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMNameCollection_FWD_DEFINED__
#define __IXpsOMNameCollection_FWD_DEFINED__
typedef interface IXpsOMNameCollection IXpsOMNameCollection;
#ifdef __cplusplus
interface IXpsOMNameCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPartUriCollection_FWD_DEFINED__
#define __IXpsOMPartUriCollection_FWD_DEFINED__
typedef interface IXpsOMPartUriCollection IXpsOMPartUriCollection;
#ifdef __cplusplus
interface IXpsOMPartUriCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackageWriter_FWD_DEFINED__
#define __IXpsOMPackageWriter_FWD_DEFINED__
typedef interface IXpsOMPackageWriter IXpsOMPackageWriter;
#ifdef __cplusplus
interface IXpsOMPackageWriter;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackageTarget_FWD_DEFINED__
#define __IXpsOMPackageTarget_FWD_DEFINED__
typedef interface IXpsOMPackageTarget IXpsOMPackageTarget;
#ifdef __cplusplus
interface IXpsOMPackageTarget;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMVisual_FWD_DEFINED__
#define __IXpsOMVisual_FWD_DEFINED__
typedef interface IXpsOMVisual IXpsOMVisual;
#ifdef __cplusplus
interface IXpsOMVisual;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMBrush_FWD_DEFINED__
#define __IXpsOMBrush_FWD_DEFINED__
typedef interface IXpsOMBrush IXpsOMBrush;
#ifdef __cplusplus
interface IXpsOMBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMMatrixTransform_FWD_DEFINED__
#define __IXpsOMMatrixTransform_FWD_DEFINED__
typedef interface IXpsOMMatrixTransform IXpsOMMatrixTransform;
#ifdef __cplusplus
interface IXpsOMMatrixTransform;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGeometry_FWD_DEFINED__
#define __IXpsOMGeometry_FWD_DEFINED__
typedef interface IXpsOMGeometry IXpsOMGeometry;
#ifdef __cplusplus
interface IXpsOMGeometry;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGlyphs_FWD_DEFINED__
#define __IXpsOMGlyphs_FWD_DEFINED__
typedef interface IXpsOMGlyphs IXpsOMGlyphs;
#ifdef __cplusplus
interface IXpsOMGlyphs;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPath_FWD_DEFINED__
#define __IXpsOMPath_FWD_DEFINED__
typedef interface IXpsOMPath IXpsOMPath;
#ifdef __cplusplus
interface IXpsOMPath;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMSolidColorBrush_FWD_DEFINED__
#define __IXpsOMSolidColorBrush_FWD_DEFINED__
typedef interface IXpsOMSolidColorBrush IXpsOMSolidColorBrush;
#ifdef __cplusplus
interface IXpsOMSolidColorBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMTileBrush_FWD_DEFINED__
#define __IXpsOMTileBrush_FWD_DEFINED__
typedef interface IXpsOMTileBrush IXpsOMTileBrush;
#ifdef __cplusplus
interface IXpsOMTileBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGradientBrush_FWD_DEFINED__
#define __IXpsOMGradientBrush_FWD_DEFINED__
typedef interface IXpsOMGradientBrush IXpsOMGradientBrush;
#ifdef __cplusplus
interface IXpsOMGradientBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMVisualBrush_FWD_DEFINED__
#define __IXpsOMVisualBrush_FWD_DEFINED__
typedef interface IXpsOMVisualBrush IXpsOMVisualBrush;
#ifdef __cplusplus
interface IXpsOMVisualBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMImageBrush_FWD_DEFINED__
#define __IXpsOMImageBrush_FWD_DEFINED__
typedef interface IXpsOMImageBrush IXpsOMImageBrush;
#ifdef __cplusplus
interface IXpsOMImageBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMLinearGradientBrush_FWD_DEFINED__
#define __IXpsOMLinearGradientBrush_FWD_DEFINED__
typedef interface IXpsOMLinearGradientBrush IXpsOMLinearGradientBrush;
#ifdef __cplusplus
interface IXpsOMLinearGradientBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMRadialGradientBrush_FWD_DEFINED__
#define __IXpsOMRadialGradientBrush_FWD_DEFINED__
typedef interface IXpsOMRadialGradientBrush IXpsOMRadialGradientBrush;
#ifdef __cplusplus
interface IXpsOMRadialGradientBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMResource_FWD_DEFINED__
#define __IXpsOMResource_FWD_DEFINED__
typedef interface IXpsOMResource IXpsOMResource;
#ifdef __cplusplus
interface IXpsOMResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMFontResource_FWD_DEFINED__
#define __IXpsOMFontResource_FWD_DEFINED__
typedef interface IXpsOMFontResource IXpsOMFontResource;
#ifdef __cplusplus
interface IXpsOMFontResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMImageResource_FWD_DEFINED__
#define __IXpsOMImageResource_FWD_DEFINED__
typedef interface IXpsOMImageResource IXpsOMImageResource;
#ifdef __cplusplus
interface IXpsOMImageResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMColorProfileResource_FWD_DEFINED__
#define __IXpsOMColorProfileResource_FWD_DEFINED__
typedef interface IXpsOMColorProfileResource IXpsOMColorProfileResource;
#ifdef __cplusplus
interface IXpsOMColorProfileResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPrintTicketResource_FWD_DEFINED__
#define __IXpsOMPrintTicketResource_FWD_DEFINED__
typedef interface IXpsOMPrintTicketResource IXpsOMPrintTicketResource;
#ifdef __cplusplus
interface IXpsOMPrintTicketResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMRemoteDictionaryResource_FWD_DEFINED__
#define __IXpsOMRemoteDictionaryResource_FWD_DEFINED__
typedef interface IXpsOMRemoteDictionaryResource IXpsOMRemoteDictionaryResource;
#ifdef __cplusplus
interface IXpsOMRemoteDictionaryResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDocumentStructureResource_FWD_DEFINED__
#define __IXpsOMDocumentStructureResource_FWD_DEFINED__
typedef interface IXpsOMDocumentStructureResource IXpsOMDocumentStructureResource;
#ifdef __cplusplus
interface IXpsOMDocumentStructureResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMStoryFragmentsResource_FWD_DEFINED__
#define __IXpsOMStoryFragmentsResource_FWD_DEFINED__
typedef interface IXpsOMStoryFragmentsResource IXpsOMStoryFragmentsResource;
#ifdef __cplusplus
interface IXpsOMStoryFragmentsResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMSignatureBlockResource_FWD_DEFINED__
#define __IXpsOMSignatureBlockResource_FWD_DEFINED__
typedef interface IXpsOMSignatureBlockResource IXpsOMSignatureBlockResource;
#ifdef __cplusplus
interface IXpsOMSignatureBlockResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMCanvas_FWD_DEFINED__
#define __IXpsOMCanvas_FWD_DEFINED__
typedef interface IXpsOMCanvas IXpsOMCanvas;
#ifdef __cplusplus
interface IXpsOMCanvas;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPage_FWD_DEFINED__
#define __IXpsOMPage_FWD_DEFINED__
typedef interface IXpsOMPage IXpsOMPage;
#ifdef __cplusplus
interface IXpsOMPage;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDocument_FWD_DEFINED__
#define __IXpsOMDocument_FWD_DEFINED__
typedef interface IXpsOMDocument IXpsOMDocument;
#ifdef __cplusplus
interface IXpsOMDocument;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDocumentSequence_FWD_DEFINED__
#define __IXpsOMDocumentSequence_FWD_DEFINED__
typedef interface IXpsOMDocumentSequence IXpsOMDocumentSequence;
#ifdef __cplusplus
interface IXpsOMDocumentSequence;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMCoreProperties_FWD_DEFINED__
#define __IXpsOMCoreProperties_FWD_DEFINED__
typedef interface IXpsOMCoreProperties IXpsOMCoreProperties;
#ifdef __cplusplus
interface IXpsOMCoreProperties;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMThumbnailGenerator_FWD_DEFINED__
#define __IXpsOMThumbnailGenerator_FWD_DEFINED__
typedef interface IXpsOMThumbnailGenerator IXpsOMThumbnailGenerator;
#ifdef __cplusplus
interface IXpsOMThumbnailGenerator;
#endif /* __cplusplus */
#endif

#ifndef __XpsOMObjectFactory_FWD_DEFINED__
#define __XpsOMObjectFactory_FWD_DEFINED__
#ifdef __cplusplus
typedef class XpsOMObjectFactory XpsOMObjectFactory;
#else
typedef struct XpsOMObjectFactory XpsOMObjectFactory;
#endif /* defined __cplusplus */
#endif /* defined __XpsOMObjectFactory_FWD_DEFINED__ */

#ifndef __XpsOMThumbnailGenerator_FWD_DEFINED__
#define __XpsOMThumbnailGenerator_FWD_DEFINED__
#ifdef __cplusplus
typedef class XpsOMThumbnailGenerator XpsOMThumbnailGenerator;
#else
typedef struct XpsOMThumbnailGenerator XpsOMThumbnailGenerator;
#endif /* defined __cplusplus */
#endif /* defined __XpsOMThumbnailGenerator_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <msopc.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if NTDDI_VERSION >= 0x06010000
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#define XPS_E_INVALID_LANGUAGE MAKE_HRESULT(1, FACILITY_XPS, 0x0)
#define XPS_E_INVALID_NAME MAKE_HRESULT(1, FACILITY_XPS, 0x1)
#define XPS_E_INVALID_RESOURCE_KEY MAKE_HRESULT(1, FACILITY_XPS, 0x2)
#define XPS_E_INVALID_PAGE_SIZE MAKE_HRESULT(1, FACILITY_XPS, 0x3)
#define XPS_E_INVALID_BLEED_BOX MAKE_HRESULT(1, FACILITY_XPS, 0x4)
#define XPS_E_INVALID_THUMBNAIL_IMAGE_TYPE MAKE_HRESULT(1, FACILITY_XPS, 0x5)
#define XPS_E_INVALID_LOOKUP_TYPE MAKE_HRESULT(1, FACILITY_XPS, 0x6)
#define XPS_E_INVALID_FLOAT MAKE_HRESULT(1, FACILITY_XPS, 0x7)
#define XPS_E_UNEXPECTED_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_XPS, 0x8)
#define XPS_E_INVALID_FONT_URI MAKE_HRESULT(1, FACILITY_XPS, 0xa)
#define XPS_E_INVALID_CONTENT_BOX MAKE_HRESULT(1, FACILITY_XPS, 0xb)
#define XPS_E_INVALID_MARKUP MAKE_HRESULT(1, FACILITY_XPS, 0xc)
#define XPS_E_INVALID_XML_ENCODING MAKE_HRESULT(1, FACILITY_XPS, 0xd)
#define XPS_E_INVALID_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_XPS, 0xe)
#define XPS_E_INVALID_OBFUSCATED_FONT_URI MAKE_HRESULT(1, FACILITY_XPS, 0xf)
#define XPS_E_UNEXPECTED_RELATIONSHIP_TYPE MAKE_HRESULT(1, FACILITY_XPS, 0x10)
#define XPS_E_UNEXPECTED_RESTRICTED_FONT_RELATIONSHIP MAKE_HRESULT(1, FACILITY_XPS, 0x11)
#define XPS_E_MISSING_NAME MAKE_HRESULT(1, FACILITY_XPS, 0x100)
#define XPS_E_MISSING_LOOKUP MAKE_HRESULT(1, FACILITY_XPS, 0x101)
#define XPS_E_MISSING_GLYPHS MAKE_HRESULT(1, FACILITY_XPS, 0x102)
#define XPS_E_MISSING_SEGMENT_DATA MAKE_HRESULT(1, FACILITY_XPS, 0x103)
#define XPS_E_MISSING_COLORPROFILE MAKE_HRESULT(1, FACILITY_XPS, 0x104)
#define XPS_E_MISSING_RELATIONSHIP_TARGET MAKE_HRESULT(1, FACILITY_XPS, 0x105)
#define XPS_E_MISSING_RESOURCE_RELATIONSHIP MAKE_HRESULT(1, FACILITY_XPS, 0x106)
#define XPS_E_MISSING_FONTURI MAKE_HRESULT(1, FACILITY_XPS, 0x107)
#define XPS_E_MISSING_DOCUMENTSEQUENCE_RELATIONSHIP MAKE_HRESULT(1, FACILITY_XPS, 0x108)
#define XPS_E_MISSING_DOCUMENT MAKE_HRESULT(1, FACILITY_XPS, 0x109)
#define XPS_E_MISSING_REFERRED_DOCUMENT MAKE_HRESULT(1, FACILITY_XPS, 0x10a)
#define XPS_E_MISSING_REFERRED_PAGE MAKE_HRESULT(1, FACILITY_XPS, 0x10b)
#define XPS_E_MISSING_PAGE_IN_DOCUMENT MAKE_HRESULT(1, FACILITY_XPS, 0x10c)
#define XPS_E_MISSING_PAGE_IN_PAGEREFERENCE MAKE_HRESULT(1, FACILITY_XPS, 0x10d)
#define XPS_E_MISSING_IMAGE_IN_IMAGEBRUSH MAKE_HRESULT(1, FACILITY_XPS, 0x10e)
#define XPS_E_MISSING_RESOURCE_KEY MAKE_HRESULT(1, FACILITY_XPS, 0x10f)
#define XPS_E_MISSING_PART_REFERENCE MAKE_HRESULT(1, FACILITY_XPS, 0x110)
#define XPS_E_MISSING_RESTRICTED_FONT_RELATIONSHIP MAKE_HRESULT(1, FACILITY_XPS, 0x111)
#define XPS_E_MISSING_DISCARDCONTROL MAKE_HRESULT(1, FACILITY_XPS, 0x112)
#define XPS_E_MISSING_PART_STREAM MAKE_HRESULT(1, FACILITY_XPS, 0x113)
#define XPS_E_UNAVAILABLE_PACKAGE MAKE_HRESULT(1, FACILITY_XPS, 0x114)
#define XPS_E_DUPLICATE_RESOURCE_KEYS MAKE_HRESULT(1, FACILITY_XPS, 0x200)
#define XPS_E_MULTIPLE_RESOURCES MAKE_HRESULT(1, FACILITY_XPS, 0x201)
#define XPS_E_MULTIPLE_DOCUMENTSEQUENCE_RELATIONSHIPS MAKE_HRESULT(1, FACILITY_XPS, 0x202)
#define XPS_E_MULTIPLE_THUMBNAILS_ON_PAGE MAKE_HRESULT(1, FACILITY_XPS, 0x203)
#define XPS_E_MULTIPLE_THUMBNAILS_ON_PACKAGE MAKE_HRESULT(1, FACILITY_XPS, 0x204)
#define XPS_E_MULTIPLE_PRINTTICKETS_ON_PAGE MAKE_HRESULT(1, FACILITY_XPS, 0x205)
#define XPS_E_MULTIPLE_PRINTTICKETS_ON_DOCUMENT MAKE_HRESULT(1, FACILITY_XPS, 0x206)
#define XPS_E_MULTIPLE_PRINTTICKETS_ON_DOCUMENTSEQUENCE MAKE_HRESULT(1, FACILITY_XPS, 0x207)
#define XPS_E_MULTIPLE_REFERENCES_TO_PART MAKE_HRESULT(1, FACILITY_XPS, 0x208)
#define XPS_E_DUPLICATE_NAMES MAKE_HRESULT(1, FACILITY_XPS, 0x209)
#define XPS_E_STRING_TOO_LONG MAKE_HRESULT(1, FACILITY_XPS, 0x300)
#define XPS_E_TOO_MANY_INDICES MAKE_HRESULT(1, FACILITY_XPS, 0x301)
#define XPS_E_MAPPING_OUT_OF_ORDER MAKE_HRESULT(1, FACILITY_XPS, 0x302)
#define XPS_E_MAPPING_OUTSIDE_STRING MAKE_HRESULT(1, FACILITY_XPS, 0x303)
#define XPS_E_MAPPING_OUTSIDE_INDICES MAKE_HRESULT(1, FACILITY_XPS, 0x304)
#define XPS_E_CARET_OUTSIDE_STRING MAKE_HRESULT(1, FACILITY_XPS, 0x305)
#define XPS_E_CARET_OUT_OF_ORDER MAKE_HRESULT(1, FACILITY_XPS, 0x306)
#define XPS_E_ODD_BIDILEVEL MAKE_HRESULT(1, FACILITY_XPS, 0x307)
#define XPS_E_ONE_TO_ONE_MAPPING_EXPECTED MAKE_HRESULT(1, FACILITY_XPS, 0x308)
#define XPS_E_RESTRICTED_FONT_NOT_OBFUSCATED MAKE_HRESULT(1, FACILITY_XPS, 0x309)
#define XPS_E_NEGATIVE_FLOAT MAKE_HRESULT(1, FACILITY_XPS, 0x30a)
#define XPS_E_XKEY_ATTR_PRESENT_OUTSIDE_RES_DICT MAKE_HRESULT(1, FACILITY_XPS, 0x400)
#define XPS_E_DICTIONARY_ITEM_NAMED MAKE_HRESULT(1, FACILITY_XPS, 0x401)
#define XPS_E_NESTED_REMOTE_DICTIONARY MAKE_HRESULT(1, FACILITY_XPS, 0x402)
#define XPS_E_INDEX_OUT_OF_RANGE MAKE_HRESULT(1, FACILITY_XPS, 0x500)
#define XPS_E_VISUAL_CIRCULAR_REF MAKE_HRESULT(1, FACILITY_XPS, 0x501)
#define XPS_E_NO_CUSTOM_OBJECTS MAKE_HRESULT(1, FACILITY_XPS, 0x502)
#define XPS_E_ALREADY_OWNED MAKE_HRESULT(1, FACILITY_XPS, 0x503)
#define XPS_E_RESOURCE_NOT_OWNED MAKE_HRESULT(1, FACILITY_XPS, 0x504)
#define XPS_E_UNEXPECTED_COLORPROFILE MAKE_HRESULT(1, FACILITY_XPS, 0x505)
#define XPS_E_COLOR_COMPONENT_OUT_OF_RANGE MAKE_HRESULT(1, FACILITY_XPS, 0x506)
#define XPS_E_BOTH_PATHFIGURE_AND_ABBR_SYNTAX_PRESENT MAKE_HRESULT(1, FACILITY_XPS, 0x507)
#define XPS_E_BOTH_RESOURCE_AND_SOURCEATTR_PRESENT MAKE_HRESULT(1, FACILITY_XPS, 0x508)
#define XPS_E_BLEED_BOX_PAGE_DIMENSIONS_NOT_IN_SYNC MAKE_HRESULT(1, FACILITY_XPS, 0x509)
#define XPS_E_RELATIONSHIP_EXTERNAL MAKE_HRESULT(1, FACILITY_XPS, 0x50a)
#define XPS_E_NOT_ENOUGH_GRADIENT_STOPS MAKE_HRESULT(1, FACILITY_XPS, 0x50b)
#define XPS_E_PACKAGE_WRITER_NOT_CLOSED MAKE_HRESULT(1, FACILITY_XPS, 0x50c)
#ifndef __IXpsOMDocumentStructureResource_FWD_DEFINED__
#define __IXpsOMDocumentStructureResource_FWD_DEFINED__
typedef interface IXpsOMDocumentStructureResource IXpsOMDocumentStructureResource;
#ifdef __cplusplus
interface IXpsOMDocumentStructureResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMCoreProperties_FWD_DEFINED__
#define __IXpsOMCoreProperties_FWD_DEFINED__
typedef interface IXpsOMCoreProperties IXpsOMCoreProperties;
#ifdef __cplusplus
interface IXpsOMCoreProperties;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPrintTicketResource_FWD_DEFINED__
#define __IXpsOMPrintTicketResource_FWD_DEFINED__
typedef interface IXpsOMPrintTicketResource IXpsOMPrintTicketResource;
#ifdef __cplusplus
interface IXpsOMPrintTicketResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMStoryFragmentsResource_FWD_DEFINED__
#define __IXpsOMStoryFragmentsResource_FWD_DEFINED__
typedef interface IXpsOMStoryFragmentsResource IXpsOMStoryFragmentsResource;
#ifdef __cplusplus
interface IXpsOMStoryFragmentsResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackage_FWD_DEFINED__
#define __IXpsOMPackage_FWD_DEFINED__
typedef interface IXpsOMPackage IXpsOMPackage;
#ifdef __cplusplus
interface IXpsOMPackage;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPart_FWD_DEFINED__
#define __IXpsOMPart_FWD_DEFINED__
typedef interface IXpsOMPart IXpsOMPart;
#ifdef __cplusplus
interface IXpsOMPart;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMShareable_FWD_DEFINED__
#define __IXpsOMShareable_FWD_DEFINED__
typedef interface IXpsOMShareable IXpsOMShareable;
#ifdef __cplusplus
interface IXpsOMShareable;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMVisual_FWD_DEFINED__
#define __IXpsOMVisual_FWD_DEFINED__
typedef interface IXpsOMVisual IXpsOMVisual;
#ifdef __cplusplus
interface IXpsOMVisual;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMBrush_FWD_DEFINED__
#define __IXpsOMBrush_FWD_DEFINED__
typedef interface IXpsOMBrush IXpsOMBrush;
#ifdef __cplusplus
interface IXpsOMBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMTileBrush_FWD_DEFINED__
#define __IXpsOMTileBrush_FWD_DEFINED__
typedef interface IXpsOMTileBrush IXpsOMTileBrush;
#ifdef __cplusplus
interface IXpsOMTileBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMResource_FWD_DEFINED__
#define __IXpsOMResource_FWD_DEFINED__
typedef interface IXpsOMResource IXpsOMResource;
#ifdef __cplusplus
interface IXpsOMResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMCanvas_FWD_DEFINED__
#define __IXpsOMCanvas_FWD_DEFINED__
typedef interface IXpsOMCanvas IXpsOMCanvas;
#ifdef __cplusplus
interface IXpsOMCanvas;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMColorProfileResource_FWD_DEFINED__
#define __IXpsOMColorProfileResource_FWD_DEFINED__
typedef interface IXpsOMColorProfileResource IXpsOMColorProfileResource;
#ifdef __cplusplus
interface IXpsOMColorProfileResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMColorProfileResourceCollection_FWD_DEFINED__
#define __IXpsOMColorProfileResourceCollection_FWD_DEFINED__
typedef interface IXpsOMColorProfileResourceCollection IXpsOMColorProfileResourceCollection;
#ifdef __cplusplus
interface IXpsOMColorProfileResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDashCollection_FWD_DEFINED__
#define __IXpsOMDashCollection_FWD_DEFINED__
typedef interface IXpsOMDashCollection IXpsOMDashCollection;
#ifdef __cplusplus
interface IXpsOMDashCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMFontResource_FWD_DEFINED__
#define __IXpsOMFontResource_FWD_DEFINED__
typedef interface IXpsOMFontResource IXpsOMFontResource;
#ifdef __cplusplus
interface IXpsOMFontResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMFontResourceCollection_FWD_DEFINED__
#define __IXpsOMFontResourceCollection_FWD_DEFINED__
typedef interface IXpsOMFontResourceCollection IXpsOMFontResourceCollection;
#ifdef __cplusplus
interface IXpsOMFontResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGeometry_FWD_DEFINED__
#define __IXpsOMGeometry_FWD_DEFINED__
typedef interface IXpsOMGeometry IXpsOMGeometry;
#ifdef __cplusplus
interface IXpsOMGeometry;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGeometryFigure_FWD_DEFINED__
#define __IXpsOMGeometryFigure_FWD_DEFINED__
typedef interface IXpsOMGeometryFigure IXpsOMGeometryFigure;
#ifdef __cplusplus
interface IXpsOMGeometryFigure;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGeometryFigureCollection_FWD_DEFINED__
#define __IXpsOMGeometryFigureCollection_FWD_DEFINED__
typedef interface IXpsOMGeometryFigureCollection IXpsOMGeometryFigureCollection;
#ifdef __cplusplus
interface IXpsOMGeometryFigureCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGlyphs_FWD_DEFINED__
#define __IXpsOMGlyphs_FWD_DEFINED__
typedef interface IXpsOMGlyphs IXpsOMGlyphs;
#ifdef __cplusplus
interface IXpsOMGlyphs;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGradientBrush_FWD_DEFINED__
#define __IXpsOMGradientBrush_FWD_DEFINED__
typedef interface IXpsOMGradientBrush IXpsOMGradientBrush;
#ifdef __cplusplus
interface IXpsOMGradientBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGradientStop_FWD_DEFINED__
#define __IXpsOMGradientStop_FWD_DEFINED__
typedef interface IXpsOMGradientStop IXpsOMGradientStop;
#ifdef __cplusplus
interface IXpsOMGradientStop;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMGradientStopCollection_FWD_DEFINED__
#define __IXpsOMGradientStopCollection_FWD_DEFINED__
typedef interface IXpsOMGradientStopCollection IXpsOMGradientStopCollection;
#ifdef __cplusplus
interface IXpsOMGradientStopCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMImageBrush_FWD_DEFINED__
#define __IXpsOMImageBrush_FWD_DEFINED__
typedef interface IXpsOMImageBrush IXpsOMImageBrush;
#ifdef __cplusplus
interface IXpsOMImageBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMImageResource_FWD_DEFINED__
#define __IXpsOMImageResource_FWD_DEFINED__
typedef interface IXpsOMImageResource IXpsOMImageResource;
#ifdef __cplusplus
interface IXpsOMImageResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMImageResourceCollection_FWD_DEFINED__
#define __IXpsOMImageResourceCollection_FWD_DEFINED__
typedef interface IXpsOMImageResourceCollection IXpsOMImageResourceCollection;
#ifdef __cplusplus
interface IXpsOMImageResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMLinearGradientBrush_FWD_DEFINED__
#define __IXpsOMLinearGradientBrush_FWD_DEFINED__
typedef interface IXpsOMLinearGradientBrush IXpsOMLinearGradientBrush;
#ifdef __cplusplus
interface IXpsOMLinearGradientBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMMatrixTransform_FWD_DEFINED__
#define __IXpsOMMatrixTransform_FWD_DEFINED__
typedef interface IXpsOMMatrixTransform IXpsOMMatrixTransform;
#ifdef __cplusplus
interface IXpsOMMatrixTransform;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPartResources_FWD_DEFINED__
#define __IXpsOMPartResources_FWD_DEFINED__
typedef interface IXpsOMPartResources IXpsOMPartResources;
#ifdef __cplusplus
interface IXpsOMPartResources;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPath_FWD_DEFINED__
#define __IXpsOMPath_FWD_DEFINED__
typedef interface IXpsOMPath IXpsOMPath;
#ifdef __cplusplus
interface IXpsOMPath;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPartUriCollection_FWD_DEFINED__
#define __IXpsOMPartUriCollection_FWD_DEFINED__
typedef interface IXpsOMPartUriCollection IXpsOMPartUriCollection;
#ifdef __cplusplus
interface IXpsOMPartUriCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMRadialGradientBrush_FWD_DEFINED__
#define __IXpsOMRadialGradientBrush_FWD_DEFINED__
typedef interface IXpsOMRadialGradientBrush IXpsOMRadialGradientBrush;
#ifdef __cplusplus
interface IXpsOMRadialGradientBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMRemoteDictionaryResource_FWD_DEFINED__
#define __IXpsOMRemoteDictionaryResource_FWD_DEFINED__
typedef interface IXpsOMRemoteDictionaryResource IXpsOMRemoteDictionaryResource;
#ifdef __cplusplus
interface IXpsOMRemoteDictionaryResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMRemoteDictionaryResourceCollection_FWD_DEFINED__
#define __IXpsOMRemoteDictionaryResourceCollection_FWD_DEFINED__
typedef interface IXpsOMRemoteDictionaryResourceCollection IXpsOMRemoteDictionaryResourceCollection;
#ifdef __cplusplus
interface IXpsOMRemoteDictionaryResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDictionary_FWD_DEFINED__
#define __IXpsOMDictionary_FWD_DEFINED__
typedef interface IXpsOMDictionary IXpsOMDictionary;
#ifdef __cplusplus
interface IXpsOMDictionary;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMShareable_FWD_DEFINED__
#define __IXpsOMShareable_FWD_DEFINED__
typedef interface IXpsOMShareable IXpsOMShareable;
#ifdef __cplusplus
interface IXpsOMShareable;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMSolidColorBrush_FWD_DEFINED__
#define __IXpsOMSolidColorBrush_FWD_DEFINED__
typedef interface IXpsOMSolidColorBrush IXpsOMSolidColorBrush;
#ifdef __cplusplus
interface IXpsOMSolidColorBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMTileBrush_FWD_DEFINED__
#define __IXpsOMTileBrush_FWD_DEFINED__
typedef interface IXpsOMTileBrush IXpsOMTileBrush;
#ifdef __cplusplus
interface IXpsOMTileBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMVisualBrush_FWD_DEFINED__
#define __IXpsOMVisualBrush_FWD_DEFINED__
typedef interface IXpsOMVisualBrush IXpsOMVisualBrush;
#ifdef __cplusplus
interface IXpsOMVisualBrush;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMVisualCollection_FWD_DEFINED__
#define __IXpsOMVisualCollection_FWD_DEFINED__
typedef interface IXpsOMVisualCollection IXpsOMVisualCollection;
#ifdef __cplusplus
interface IXpsOMVisualCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPageReference_FWD_DEFINED__
#define __IXpsOMPageReference_FWD_DEFINED__
typedef interface IXpsOMPageReference IXpsOMPageReference;
#ifdef __cplusplus
interface IXpsOMPageReference;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDocumentSequence_FWD_DEFINED__
#define __IXpsOMDocumentSequence_FWD_DEFINED__
typedef interface IXpsOMDocumentSequence IXpsOMDocumentSequence;
#ifdef __cplusplus
interface IXpsOMDocumentSequence;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMSignatureBlockResource_FWD_DEFINED__
#define __IXpsOMSignatureBlockResource_FWD_DEFINED__
typedef interface IXpsOMSignatureBlockResource IXpsOMSignatureBlockResource;
#ifdef __cplusplus
interface IXpsOMSignatureBlockResource;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMSignatureBlockResourceCollection_FWD_DEFINED__
#define __IXpsOMSignatureBlockResourceCollection_FWD_DEFINED__
typedef interface IXpsOMSignatureBlockResourceCollection IXpsOMSignatureBlockResourceCollection;
#ifdef __cplusplus
interface IXpsOMSignatureBlockResourceCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMNameCollection_FWD_DEFINED__
#define __IXpsOMNameCollection_FWD_DEFINED__
typedef interface IXpsOMNameCollection IXpsOMNameCollection;
#ifdef __cplusplus
interface IXpsOMNameCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMDocument_FWD_DEFINED__
#define __IXpsOMDocument_FWD_DEFINED__
typedef interface IXpsOMDocument IXpsOMDocument;
#ifdef __cplusplus
interface IXpsOMDocument;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPage_FWD_DEFINED__
#define __IXpsOMPage_FWD_DEFINED__
typedef interface IXpsOMPage IXpsOMPage;
#ifdef __cplusplus
interface IXpsOMPage;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackageWriter_FWD_DEFINED__
#define __IXpsOMPackageWriter_FWD_DEFINED__
typedef interface IXpsOMPackageWriter IXpsOMPackageWriter;
#ifdef __cplusplus
interface IXpsOMPackageWriter;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackageTarget_FWD_DEFINED__
#define __IXpsOMPackageTarget_FWD_DEFINED__
typedef interface IXpsOMPackageTarget IXpsOMPackageTarget;
#ifdef __cplusplus
interface IXpsOMPackageTarget;
#endif /* __cplusplus */
#endif

typedef enum __WIDL_xpsobjectmodel_generated_name_00000031 {
    XPS_COLOR_INTERPOLATION_SCRGBLINEAR = 1,
    XPS_COLOR_INTERPOLATION_SRGBLINEAR = 2
} XPS_COLOR_INTERPOLATION;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000032 {
    XPS_COLOR_TYPE_SRGB = 1,
    XPS_COLOR_TYPE_SCRGB = 2,
    XPS_COLOR_TYPE_CONTEXT = 3
} XPS_COLOR_TYPE;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000033 {
    XPS_DASH_CAP_FLAT = 1,
    XPS_DASH_CAP_ROUND = 2,
    XPS_DASH_CAP_SQUARE = 3,
    XPS_DASH_CAP_TRIANGLE = 4
} XPS_DASH_CAP;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000034 {
    XPS_FILL_RULE_EVENODD = 1,
    XPS_FILL_RULE_NONZERO = 2
} XPS_FILL_RULE;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000035 {
    XPS_FONT_EMBEDDING_NORMAL = 1,
    XPS_FONT_EMBEDDING_OBFUSCATED = 2,
    XPS_FONT_EMBEDDING_RESTRICTED = 3,
    XPS_FONT_EMBEDDING_RESTRICTED_UNOBFUSCATED = 4
} XPS_FONT_EMBEDDING;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000036 {
    XPS_IMAGE_TYPE_JPEG = 1,
    XPS_IMAGE_TYPE_PNG = 2,
    XPS_IMAGE_TYPE_TIFF = 3,
    XPS_IMAGE_TYPE_WDP = 4,
    XPS_IMAGE_TYPE_JXR = 5
} XPS_IMAGE_TYPE;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000037 {
    XPS_INTERLEAVING_OFF = 1,
    XPS_INTERLEAVING_ON = 2
} XPS_INTERLEAVING;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000038 {
    XPS_LINE_CAP_FLAT = 1,
    XPS_LINE_CAP_ROUND = 2,
    XPS_LINE_CAP_SQUARE = 3,
    XPS_LINE_CAP_TRIANGLE = 4
} XPS_LINE_CAP;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000039 {
    XPS_LINE_JOIN_MITER = 1,
    XPS_LINE_JOIN_BEVEL = 2,
    XPS_LINE_JOIN_ROUND = 3
} XPS_LINE_JOIN;
typedef enum __WIDL_xpsobjectmodel_generated_name_0000003A {
    XPS_OBJECT_TYPE_CANVAS = 1,
    XPS_OBJECT_TYPE_GLYPHS = 2,
    XPS_OBJECT_TYPE_PATH = 3,
    XPS_OBJECT_TYPE_MATRIX_TRANSFORM = 4,
    XPS_OBJECT_TYPE_GEOMETRY = 5,
    XPS_OBJECT_TYPE_SOLID_COLOR_BRUSH = 6,
    XPS_OBJECT_TYPE_IMAGE_BRUSH = 7,
    XPS_OBJECT_TYPE_LINEAR_GRADIENT_BRUSH = 8,
    XPS_OBJECT_TYPE_RADIAL_GRADIENT_BRUSH = 9,
    XPS_OBJECT_TYPE_VISUAL_BRUSH = 10
} XPS_OBJECT_TYPE;
typedef enum __WIDL_xpsobjectmodel_generated_name_0000003B {
    XPS_SEGMENT_STROKE_PATTERN_ALL = 1,
    XPS_SEGMENT_STROKE_PATTERN_NONE = 2,
    XPS_SEGMENT_STROKE_PATTERN_MIXED = 3
} XPS_SEGMENT_STROKE_PATTERN;
typedef enum __WIDL_xpsobjectmodel_generated_name_0000003C {
    XPS_SEGMENT_TYPE_ARC_LARGE_CLOCKWISE = 1,
    XPS_SEGMENT_TYPE_ARC_LARGE_COUNTERCLOCKWISE = 2,
    XPS_SEGMENT_TYPE_ARC_SMALL_CLOCKWISE = 3,
    XPS_SEGMENT_TYPE_ARC_SMALL_COUNTERCLOCKWISE = 4,
    XPS_SEGMENT_TYPE_BEZIER = 5,
    XPS_SEGMENT_TYPE_LINE = 6,
    XPS_SEGMENT_TYPE_QUADRATIC_BEZIER = 7
} XPS_SEGMENT_TYPE;
typedef enum __WIDL_xpsobjectmodel_generated_name_0000003D {
    XPS_SPREAD_METHOD_PAD = 1,
    XPS_SPREAD_METHOD_REFLECT = 2,
    XPS_SPREAD_METHOD_REPEAT = 3
} XPS_SPREAD_METHOD;
typedef enum __WIDL_xpsobjectmodel_generated_name_0000003E {
    XPS_STYLE_SIMULATION_NONE = 1,
    XPS_STYLE_SIMULATION_ITALIC = 2,
    XPS_STYLE_SIMULATION_BOLD = 3,
    XPS_STYLE_SIMULATION_BOLDITALIC = 4
} XPS_STYLE_SIMULATION;
typedef enum __WIDL_xpsobjectmodel_generated_name_0000003F {
    XPS_THUMBNAIL_SIZE_VERYSMALL = 1,
    XPS_THUMBNAIL_SIZE_SMALL = 2,
    XPS_THUMBNAIL_SIZE_MEDIUM = 3,
    XPS_THUMBNAIL_SIZE_LARGE = 4
} XPS_THUMBNAIL_SIZE;
typedef enum __WIDL_xpsobjectmodel_generated_name_00000040 {
    XPS_TILE_MODE_NONE = 1,
    XPS_TILE_MODE_TILE = 2,
    XPS_TILE_MODE_FLIPX = 3,
    XPS_TILE_MODE_FLIPY = 4,
    XPS_TILE_MODE_FLIPXY = 5
} XPS_TILE_MODE;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000042 {
    XPS_COLOR_TYPE colorType;
    union __WIDL_xpsobjectmodel_generated_name_00000041 {
        struct {
            UINT8 alpha;
            UINT8 red;
            UINT8 green;
            UINT8 blue;
        } sRGB;
        struct {
            FLOAT alpha;
            FLOAT red;
            FLOAT green;
            FLOAT blue;
        } scRGB;
        struct {
            UINT8 channelCount;
            FLOAT channels[9];
        } context;
    } value;
} XPS_COLOR;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000043 {
    FLOAT length;
    FLOAT gap;
} XPS_DASH;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000044 {
    LONG index;
    FLOAT advanceWidth;
    FLOAT horizontalOffset;
    FLOAT verticalOffset;
} XPS_GLYPH_INDEX;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000045 {
    UINT32 unicodeStringStart;
    UINT16 unicodeStringLength;
    UINT32 glyphIndicesStart;
    UINT16 glyphIndicesLength;
} XPS_GLYPH_MAPPING;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000046 {
    FLOAT m11;
    FLOAT m12;
    FLOAT m21;
    FLOAT m22;
    FLOAT m31;
    FLOAT m32;
} XPS_MATRIX;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000047 {
    FLOAT x;
    FLOAT y;
} XPS_POINT;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000048 {
    FLOAT x;
    FLOAT y;
    FLOAT width;
    FLOAT height;
} XPS_RECT;
typedef struct __WIDL_xpsobjectmodel_generated_name_00000049 {
    FLOAT width;
    FLOAT height;
} XPS_SIZE;
/*****************************************************************************
 * IXpsOMShareable interface
 */
#ifndef __IXpsOMShareable_INTERFACE_DEFINED__
#define __IXpsOMShareable_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMShareable, 0x7137398f, 0x2fc1, 0x454d, 0x8c,0x6a, 0x2c,0x31,0x15,0xa1,0x6e,0xce);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7137398f-2fc1-454d-8c6a-2c3115a16ece")
IXpsOMShareable : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IUnknown **owner) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        XPS_OBJECT_TYPE *type) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMShareable, 0x7137398f, 0x2fc1, 0x454d, 0x8c,0x6a, 0x2c,0x31,0x15,0xa1,0x6e,0xce)
#endif
#else
typedef struct IXpsOMShareableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMShareable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMShareable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMShareable *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMShareable *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMShareable *This,
        XPS_OBJECT_TYPE *type);

    END_INTERFACE
} IXpsOMShareableVtbl;

interface IXpsOMShareable {
    CONST_VTBL IXpsOMShareableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMShareable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMShareable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMShareable_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMShareable_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMShareable_GetType(This,type) (This)->lpVtbl->GetType(This,type)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMShareable_QueryInterface(IXpsOMShareable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMShareable_AddRef(IXpsOMShareable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMShareable_Release(IXpsOMShareable* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMShareable_GetOwner(IXpsOMShareable* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMShareable_GetType(IXpsOMShareable* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
#endif
#endif

#endif


#endif  /* __IXpsOMShareable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPart interface
 */
#ifndef __IXpsOMPart_INTERFACE_DEFINED__
#define __IXpsOMPart_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPart, 0x74eb2f0b, 0xa91e, 0x4486, 0xaf,0xac, 0x0f,0xab,0xec,0xa3,0xdf,0xc6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("74eb2f0b-a91e-4486-afac-0fabeca3dfc6")
IXpsOMPart : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPartName(
        IOpcPartUri **partUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPartName(
        IOpcPartUri *partUri) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPart, 0x74eb2f0b, 0xa91e, 0x4486, 0xaf,0xac, 0x0f,0xab,0xec,0xa3,0xdf,0xc6)
#endif
#else
typedef struct IXpsOMPartVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPart *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPart *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPart *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMPart *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMPart *This,
        IOpcPartUri *partUri);

    END_INTERFACE
} IXpsOMPartVtbl;

interface IXpsOMPart {
    CONST_VTBL IXpsOMPartVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPart_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPart_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPart_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMPart_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMPart_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPart_QueryInterface(IXpsOMPart* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPart_AddRef(IXpsOMPart* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPart_Release(IXpsOMPart* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMPart_GetPartName(IXpsOMPart* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMPart_SetPartName(IXpsOMPart* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPart_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGlyphsEditor interface
 */
#ifndef __IXpsOMGlyphsEditor_INTERFACE_DEFINED__
#define __IXpsOMGlyphsEditor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGlyphsEditor, 0xa5ab8616, 0x5b16, 0x4b9f, 0x96,0x29, 0x89,0xb3,0x23,0xed,0x79,0x09);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a5ab8616-5b16-4b9f-9629-89b323ed7909")
IXpsOMGlyphsEditor : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ApplyEdits(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnicodeString(
        LPWSTR *unicodeString) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnicodeString(
        LPCWSTR unicodeString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphIndexCount(
        UINT32 *indexCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphIndices(
        UINT32 *indexCount,
        XPS_GLYPH_INDEX *glyphIndices) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGlyphIndices(
        UINT32 indexCount,
        const XPS_GLYPH_INDEX *glyphIndices) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphMappingCount(
        UINT32 *glyphMappingCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphMappings(
        UINT32 *glyphMappingCount,
        XPS_GLYPH_MAPPING *glyphMappings) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGlyphMappings(
        UINT32 glyphMappingCount,
        const XPS_GLYPH_MAPPING *glyphMappings) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProhibitedCaretStopCount(
        UINT32 *prohibitedCaretStopCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProhibitedCaretStops(
        UINT32 *count,
        UINT32 *prohibitedCaretStops) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProhibitedCaretStops(
        UINT32 count,
        const UINT32 *prohibitedCaretStops) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBidiLevel(
        UINT32 *bidiLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBidiLevel(
        UINT32 bidiLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsSideways(
        WINBOOL *isSideways) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIsSideways(
        WINBOOL isSideways) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceFontName(
        LPWSTR *deviceFontName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDeviceFontName(
        LPCWSTR deviceFontName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGlyphsEditor, 0xa5ab8616, 0x5b16, 0x4b9f, 0x96,0x29, 0x89,0xb3,0x23,0xed,0x79,0x09)
#endif
#else
typedef struct IXpsOMGlyphsEditorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGlyphsEditor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGlyphsEditor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGlyphsEditor *This);

    /*** IXpsOMGlyphsEditor methods ***/
    HRESULT (STDMETHODCALLTYPE *ApplyEdits)(
        IXpsOMGlyphsEditor *This);

    HRESULT (STDMETHODCALLTYPE *GetUnicodeString)(
        IXpsOMGlyphsEditor *This,
        LPWSTR *unicodeString);

    HRESULT (STDMETHODCALLTYPE *SetUnicodeString)(
        IXpsOMGlyphsEditor *This,
        LPCWSTR unicodeString);

    HRESULT (STDMETHODCALLTYPE *GetGlyphIndexCount)(
        IXpsOMGlyphsEditor *This,
        UINT32 *indexCount);

    HRESULT (STDMETHODCALLTYPE *GetGlyphIndices)(
        IXpsOMGlyphsEditor *This,
        UINT32 *indexCount,
        XPS_GLYPH_INDEX *glyphIndices);

    HRESULT (STDMETHODCALLTYPE *SetGlyphIndices)(
        IXpsOMGlyphsEditor *This,
        UINT32 indexCount,
        const XPS_GLYPH_INDEX *glyphIndices);

    HRESULT (STDMETHODCALLTYPE *GetGlyphMappingCount)(
        IXpsOMGlyphsEditor *This,
        UINT32 *glyphMappingCount);

    HRESULT (STDMETHODCALLTYPE *GetGlyphMappings)(
        IXpsOMGlyphsEditor *This,
        UINT32 *glyphMappingCount,
        XPS_GLYPH_MAPPING *glyphMappings);

    HRESULT (STDMETHODCALLTYPE *SetGlyphMappings)(
        IXpsOMGlyphsEditor *This,
        UINT32 glyphMappingCount,
        const XPS_GLYPH_MAPPING *glyphMappings);

    HRESULT (STDMETHODCALLTYPE *GetProhibitedCaretStopCount)(
        IXpsOMGlyphsEditor *This,
        UINT32 *prohibitedCaretStopCount);

    HRESULT (STDMETHODCALLTYPE *GetProhibitedCaretStops)(
        IXpsOMGlyphsEditor *This,
        UINT32 *count,
        UINT32 *prohibitedCaretStops);

    HRESULT (STDMETHODCALLTYPE *SetProhibitedCaretStops)(
        IXpsOMGlyphsEditor *This,
        UINT32 count,
        const UINT32 *prohibitedCaretStops);

    HRESULT (STDMETHODCALLTYPE *GetBidiLevel)(
        IXpsOMGlyphsEditor *This,
        UINT32 *bidiLevel);

    HRESULT (STDMETHODCALLTYPE *SetBidiLevel)(
        IXpsOMGlyphsEditor *This,
        UINT32 bidiLevel);

    HRESULT (STDMETHODCALLTYPE *GetIsSideways)(
        IXpsOMGlyphsEditor *This,
        WINBOOL *isSideways);

    HRESULT (STDMETHODCALLTYPE *SetIsSideways)(
        IXpsOMGlyphsEditor *This,
        WINBOOL isSideways);

    HRESULT (STDMETHODCALLTYPE *GetDeviceFontName)(
        IXpsOMGlyphsEditor *This,
        LPWSTR *deviceFontName);

    HRESULT (STDMETHODCALLTYPE *SetDeviceFontName)(
        IXpsOMGlyphsEditor *This,
        LPCWSTR deviceFontName);

    END_INTERFACE
} IXpsOMGlyphsEditorVtbl;

interface IXpsOMGlyphsEditor {
    CONST_VTBL IXpsOMGlyphsEditorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGlyphsEditor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGlyphsEditor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGlyphsEditor_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMGlyphsEditor methods ***/
#define IXpsOMGlyphsEditor_ApplyEdits(This) (This)->lpVtbl->ApplyEdits(This)
#define IXpsOMGlyphsEditor_GetUnicodeString(This,unicodeString) (This)->lpVtbl->GetUnicodeString(This,unicodeString)
#define IXpsOMGlyphsEditor_SetUnicodeString(This,unicodeString) (This)->lpVtbl->SetUnicodeString(This,unicodeString)
#define IXpsOMGlyphsEditor_GetGlyphIndexCount(This,indexCount) (This)->lpVtbl->GetGlyphIndexCount(This,indexCount)
#define IXpsOMGlyphsEditor_GetGlyphIndices(This,indexCount,glyphIndices) (This)->lpVtbl->GetGlyphIndices(This,indexCount,glyphIndices)
#define IXpsOMGlyphsEditor_SetGlyphIndices(This,indexCount,glyphIndices) (This)->lpVtbl->SetGlyphIndices(This,indexCount,glyphIndices)
#define IXpsOMGlyphsEditor_GetGlyphMappingCount(This,glyphMappingCount) (This)->lpVtbl->GetGlyphMappingCount(This,glyphMappingCount)
#define IXpsOMGlyphsEditor_GetGlyphMappings(This,glyphMappingCount,glyphMappings) (This)->lpVtbl->GetGlyphMappings(This,glyphMappingCount,glyphMappings)
#define IXpsOMGlyphsEditor_SetGlyphMappings(This,glyphMappingCount,glyphMappings) (This)->lpVtbl->SetGlyphMappings(This,glyphMappingCount,glyphMappings)
#define IXpsOMGlyphsEditor_GetProhibitedCaretStopCount(This,prohibitedCaretStopCount) (This)->lpVtbl->GetProhibitedCaretStopCount(This,prohibitedCaretStopCount)
#define IXpsOMGlyphsEditor_GetProhibitedCaretStops(This,count,prohibitedCaretStops) (This)->lpVtbl->GetProhibitedCaretStops(This,count,prohibitedCaretStops)
#define IXpsOMGlyphsEditor_SetProhibitedCaretStops(This,count,prohibitedCaretStops) (This)->lpVtbl->SetProhibitedCaretStops(This,count,prohibitedCaretStops)
#define IXpsOMGlyphsEditor_GetBidiLevel(This,bidiLevel) (This)->lpVtbl->GetBidiLevel(This,bidiLevel)
#define IXpsOMGlyphsEditor_SetBidiLevel(This,bidiLevel) (This)->lpVtbl->SetBidiLevel(This,bidiLevel)
#define IXpsOMGlyphsEditor_GetIsSideways(This,isSideways) (This)->lpVtbl->GetIsSideways(This,isSideways)
#define IXpsOMGlyphsEditor_SetIsSideways(This,isSideways) (This)->lpVtbl->SetIsSideways(This,isSideways)
#define IXpsOMGlyphsEditor_GetDeviceFontName(This,deviceFontName) (This)->lpVtbl->GetDeviceFontName(This,deviceFontName)
#define IXpsOMGlyphsEditor_SetDeviceFontName(This,deviceFontName) (This)->lpVtbl->SetDeviceFontName(This,deviceFontName)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGlyphsEditor_QueryInterface(IXpsOMGlyphsEditor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGlyphsEditor_AddRef(IXpsOMGlyphsEditor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGlyphsEditor_Release(IXpsOMGlyphsEditor* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMGlyphsEditor methods ***/
static inline HRESULT IXpsOMGlyphsEditor_ApplyEdits(IXpsOMGlyphsEditor* This) {
    return This->lpVtbl->ApplyEdits(This);
}
static inline HRESULT IXpsOMGlyphsEditor_GetUnicodeString(IXpsOMGlyphsEditor* This,LPWSTR *unicodeString) {
    return This->lpVtbl->GetUnicodeString(This,unicodeString);
}
static inline HRESULT IXpsOMGlyphsEditor_SetUnicodeString(IXpsOMGlyphsEditor* This,LPCWSTR unicodeString) {
    return This->lpVtbl->SetUnicodeString(This,unicodeString);
}
static inline HRESULT IXpsOMGlyphsEditor_GetGlyphIndexCount(IXpsOMGlyphsEditor* This,UINT32 *indexCount) {
    return This->lpVtbl->GetGlyphIndexCount(This,indexCount);
}
static inline HRESULT IXpsOMGlyphsEditor_GetGlyphIndices(IXpsOMGlyphsEditor* This,UINT32 *indexCount,XPS_GLYPH_INDEX *glyphIndices) {
    return This->lpVtbl->GetGlyphIndices(This,indexCount,glyphIndices);
}
static inline HRESULT IXpsOMGlyphsEditor_SetGlyphIndices(IXpsOMGlyphsEditor* This,UINT32 indexCount,const XPS_GLYPH_INDEX *glyphIndices) {
    return This->lpVtbl->SetGlyphIndices(This,indexCount,glyphIndices);
}
static inline HRESULT IXpsOMGlyphsEditor_GetGlyphMappingCount(IXpsOMGlyphsEditor* This,UINT32 *glyphMappingCount) {
    return This->lpVtbl->GetGlyphMappingCount(This,glyphMappingCount);
}
static inline HRESULT IXpsOMGlyphsEditor_GetGlyphMappings(IXpsOMGlyphsEditor* This,UINT32 *glyphMappingCount,XPS_GLYPH_MAPPING *glyphMappings) {
    return This->lpVtbl->GetGlyphMappings(This,glyphMappingCount,glyphMappings);
}
static inline HRESULT IXpsOMGlyphsEditor_SetGlyphMappings(IXpsOMGlyphsEditor* This,UINT32 glyphMappingCount,const XPS_GLYPH_MAPPING *glyphMappings) {
    return This->lpVtbl->SetGlyphMappings(This,glyphMappingCount,glyphMappings);
}
static inline HRESULT IXpsOMGlyphsEditor_GetProhibitedCaretStopCount(IXpsOMGlyphsEditor* This,UINT32 *prohibitedCaretStopCount) {
    return This->lpVtbl->GetProhibitedCaretStopCount(This,prohibitedCaretStopCount);
}
static inline HRESULT IXpsOMGlyphsEditor_GetProhibitedCaretStops(IXpsOMGlyphsEditor* This,UINT32 *count,UINT32 *prohibitedCaretStops) {
    return This->lpVtbl->GetProhibitedCaretStops(This,count,prohibitedCaretStops);
}
static inline HRESULT IXpsOMGlyphsEditor_SetProhibitedCaretStops(IXpsOMGlyphsEditor* This,UINT32 count,const UINT32 *prohibitedCaretStops) {
    return This->lpVtbl->SetProhibitedCaretStops(This,count,prohibitedCaretStops);
}
static inline HRESULT IXpsOMGlyphsEditor_GetBidiLevel(IXpsOMGlyphsEditor* This,UINT32 *bidiLevel) {
    return This->lpVtbl->GetBidiLevel(This,bidiLevel);
}
static inline HRESULT IXpsOMGlyphsEditor_SetBidiLevel(IXpsOMGlyphsEditor* This,UINT32 bidiLevel) {
    return This->lpVtbl->SetBidiLevel(This,bidiLevel);
}
static inline HRESULT IXpsOMGlyphsEditor_GetIsSideways(IXpsOMGlyphsEditor* This,WINBOOL *isSideways) {
    return This->lpVtbl->GetIsSideways(This,isSideways);
}
static inline HRESULT IXpsOMGlyphsEditor_SetIsSideways(IXpsOMGlyphsEditor* This,WINBOOL isSideways) {
    return This->lpVtbl->SetIsSideways(This,isSideways);
}
static inline HRESULT IXpsOMGlyphsEditor_GetDeviceFontName(IXpsOMGlyphsEditor* This,LPWSTR *deviceFontName) {
    return This->lpVtbl->GetDeviceFontName(This,deviceFontName);
}
static inline HRESULT IXpsOMGlyphsEditor_SetDeviceFontName(IXpsOMGlyphsEditor* This,LPCWSTR deviceFontName) {
    return This->lpVtbl->SetDeviceFontName(This,deviceFontName);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGlyphsEditor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMDashCollection interface
 */
#ifndef __IXpsOMDashCollection_INTERFACE_DEFINED__
#define __IXpsOMDashCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMDashCollection, 0x081613f4, 0x74eb, 0x48f2, 0x83,0xb3, 0x37,0xa9,0xce,0x2d,0x7d,0xc6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("081613f4-74eb-48f2-83b3-37a9ce2d7dc6")
IXpsOMDashCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        XPS_DASH *dash) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        const XPS_DASH *dash) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        const XPS_DASH *dash) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        const XPS_DASH *dash) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMDashCollection, 0x081613f4, 0x74eb, 0x48f2, 0x83,0xb3, 0x37,0xa9,0xce,0x2d,0x7d,0xc6)
#endif
#else
typedef struct IXpsOMDashCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMDashCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMDashCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMDashCollection *This);

    /*** IXpsOMDashCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMDashCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMDashCollection *This,
        UINT32 index,
        XPS_DASH *dash);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMDashCollection *This,
        UINT32 index,
        const XPS_DASH *dash);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMDashCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMDashCollection *This,
        UINT32 index,
        const XPS_DASH *dash);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMDashCollection *This,
        const XPS_DASH *dash);

    END_INTERFACE
} IXpsOMDashCollectionVtbl;

interface IXpsOMDashCollection {
    CONST_VTBL IXpsOMDashCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMDashCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMDashCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMDashCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMDashCollection methods ***/
#define IXpsOMDashCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMDashCollection_GetAt(This,index,dash) (This)->lpVtbl->GetAt(This,index,dash)
#define IXpsOMDashCollection_InsertAt(This,index,dash) (This)->lpVtbl->InsertAt(This,index,dash)
#define IXpsOMDashCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMDashCollection_SetAt(This,index,dash) (This)->lpVtbl->SetAt(This,index,dash)
#define IXpsOMDashCollection_Append(This,dash) (This)->lpVtbl->Append(This,dash)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMDashCollection_QueryInterface(IXpsOMDashCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMDashCollection_AddRef(IXpsOMDashCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMDashCollection_Release(IXpsOMDashCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMDashCollection methods ***/
static inline HRESULT IXpsOMDashCollection_GetCount(IXpsOMDashCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMDashCollection_GetAt(IXpsOMDashCollection* This,UINT32 index,XPS_DASH *dash) {
    return This->lpVtbl->GetAt(This,index,dash);
}
static inline HRESULT IXpsOMDashCollection_InsertAt(IXpsOMDashCollection* This,UINT32 index,const XPS_DASH *dash) {
    return This->lpVtbl->InsertAt(This,index,dash);
}
static inline HRESULT IXpsOMDashCollection_RemoveAt(IXpsOMDashCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMDashCollection_SetAt(IXpsOMDashCollection* This,UINT32 index,const XPS_DASH *dash) {
    return This->lpVtbl->SetAt(This,index,dash);
}
static inline HRESULT IXpsOMDashCollection_Append(IXpsOMDashCollection* This,const XPS_DASH *dash) {
    return This->lpVtbl->Append(This,dash);
}
#endif
#endif

#endif


#endif  /* __IXpsOMDashCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGeometryFigure interface
 */
#ifndef __IXpsOMGeometryFigure_INTERFACE_DEFINED__
#define __IXpsOMGeometryFigure_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGeometryFigure, 0xd410dc83, 0x908c, 0x443e, 0x89,0x47, 0xb1,0x79,0x5d,0x3c,0x16,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d410dc83-908c-443e-8947-b1795d3c165a")
IXpsOMGeometryFigure : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMGeometry **owner) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSegmentData(
        UINT32 *dataCount,
        FLOAT *segmentData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSegmentTypes(
        UINT32 *segmentCount,
        XPS_SEGMENT_TYPE *segmentTypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSegmentStrokes(
        UINT32 *segmentCount,
        WINBOOL *segmentStrokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSegments(
        UINT32 segmentCount,
        UINT32 segmentDataCount,
        const XPS_SEGMENT_TYPE *segmentTypes,
        const FLOAT *segmentData,
        const WINBOOL *segmentStrokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStartPoint(
        XPS_POINT *startPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStartPoint(
        const XPS_POINT *startPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsClosed(
        WINBOOL *isClosed) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIsClosed(
        WINBOOL isClosed) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsFilled(
        WINBOOL *isFilled) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIsFilled(
        WINBOOL isFilled) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSegmentCount(
        UINT32 *segmentCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSegmentDataCount(
        UINT32 *segmentDataCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSegmentStrokePattern(
        XPS_SEGMENT_STROKE_PATTERN *segmentStrokePattern) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMGeometryFigure **geometryFigure) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGeometryFigure, 0xd410dc83, 0x908c, 0x443e, 0x89,0x47, 0xb1,0x79,0x5d,0x3c,0x16,0x5a)
#endif
#else
typedef struct IXpsOMGeometryFigureVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGeometryFigure *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGeometryFigure *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGeometryFigure *This);

    /*** IXpsOMGeometryFigure methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMGeometryFigure *This,
        IXpsOMGeometry **owner);

    HRESULT (STDMETHODCALLTYPE *GetSegmentData)(
        IXpsOMGeometryFigure *This,
        UINT32 *dataCount,
        FLOAT *segmentData);

    HRESULT (STDMETHODCALLTYPE *GetSegmentTypes)(
        IXpsOMGeometryFigure *This,
        UINT32 *segmentCount,
        XPS_SEGMENT_TYPE *segmentTypes);

    HRESULT (STDMETHODCALLTYPE *GetSegmentStrokes)(
        IXpsOMGeometryFigure *This,
        UINT32 *segmentCount,
        WINBOOL *segmentStrokes);

    HRESULT (STDMETHODCALLTYPE *SetSegments)(
        IXpsOMGeometryFigure *This,
        UINT32 segmentCount,
        UINT32 segmentDataCount,
        const XPS_SEGMENT_TYPE *segmentTypes,
        const FLOAT *segmentData,
        const WINBOOL *segmentStrokes);

    HRESULT (STDMETHODCALLTYPE *GetStartPoint)(
        IXpsOMGeometryFigure *This,
        XPS_POINT *startPoint);

    HRESULT (STDMETHODCALLTYPE *SetStartPoint)(
        IXpsOMGeometryFigure *This,
        const XPS_POINT *startPoint);

    HRESULT (STDMETHODCALLTYPE *GetIsClosed)(
        IXpsOMGeometryFigure *This,
        WINBOOL *isClosed);

    HRESULT (STDMETHODCALLTYPE *SetIsClosed)(
        IXpsOMGeometryFigure *This,
        WINBOOL isClosed);

    HRESULT (STDMETHODCALLTYPE *GetIsFilled)(
        IXpsOMGeometryFigure *This,
        WINBOOL *isFilled);

    HRESULT (STDMETHODCALLTYPE *SetIsFilled)(
        IXpsOMGeometryFigure *This,
        WINBOOL isFilled);

    HRESULT (STDMETHODCALLTYPE *GetSegmentCount)(
        IXpsOMGeometryFigure *This,
        UINT32 *segmentCount);

    HRESULT (STDMETHODCALLTYPE *GetSegmentDataCount)(
        IXpsOMGeometryFigure *This,
        UINT32 *segmentDataCount);

    HRESULT (STDMETHODCALLTYPE *GetSegmentStrokePattern)(
        IXpsOMGeometryFigure *This,
        XPS_SEGMENT_STROKE_PATTERN *segmentStrokePattern);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMGeometryFigure *This,
        IXpsOMGeometryFigure **geometryFigure);

    END_INTERFACE
} IXpsOMGeometryFigureVtbl;

interface IXpsOMGeometryFigure {
    CONST_VTBL IXpsOMGeometryFigureVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGeometryFigure_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGeometryFigure_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGeometryFigure_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMGeometryFigure methods ***/
#define IXpsOMGeometryFigure_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMGeometryFigure_GetSegmentData(This,dataCount,segmentData) (This)->lpVtbl->GetSegmentData(This,dataCount,segmentData)
#define IXpsOMGeometryFigure_GetSegmentTypes(This,segmentCount,segmentTypes) (This)->lpVtbl->GetSegmentTypes(This,segmentCount,segmentTypes)
#define IXpsOMGeometryFigure_GetSegmentStrokes(This,segmentCount,segmentStrokes) (This)->lpVtbl->GetSegmentStrokes(This,segmentCount,segmentStrokes)
#define IXpsOMGeometryFigure_SetSegments(This,segmentCount,segmentDataCount,segmentTypes,segmentData,segmentStrokes) (This)->lpVtbl->SetSegments(This,segmentCount,segmentDataCount,segmentTypes,segmentData,segmentStrokes)
#define IXpsOMGeometryFigure_GetStartPoint(This,startPoint) (This)->lpVtbl->GetStartPoint(This,startPoint)
#define IXpsOMGeometryFigure_SetStartPoint(This,startPoint) (This)->lpVtbl->SetStartPoint(This,startPoint)
#define IXpsOMGeometryFigure_GetIsClosed(This,isClosed) (This)->lpVtbl->GetIsClosed(This,isClosed)
#define IXpsOMGeometryFigure_SetIsClosed(This,isClosed) (This)->lpVtbl->SetIsClosed(This,isClosed)
#define IXpsOMGeometryFigure_GetIsFilled(This,isFilled) (This)->lpVtbl->GetIsFilled(This,isFilled)
#define IXpsOMGeometryFigure_SetIsFilled(This,isFilled) (This)->lpVtbl->SetIsFilled(This,isFilled)
#define IXpsOMGeometryFigure_GetSegmentCount(This,segmentCount) (This)->lpVtbl->GetSegmentCount(This,segmentCount)
#define IXpsOMGeometryFigure_GetSegmentDataCount(This,segmentDataCount) (This)->lpVtbl->GetSegmentDataCount(This,segmentDataCount)
#define IXpsOMGeometryFigure_GetSegmentStrokePattern(This,segmentStrokePattern) (This)->lpVtbl->GetSegmentStrokePattern(This,segmentStrokePattern)
#define IXpsOMGeometryFigure_Clone(This,geometryFigure) (This)->lpVtbl->Clone(This,geometryFigure)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGeometryFigure_QueryInterface(IXpsOMGeometryFigure* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGeometryFigure_AddRef(IXpsOMGeometryFigure* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGeometryFigure_Release(IXpsOMGeometryFigure* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMGeometryFigure methods ***/
static inline HRESULT IXpsOMGeometryFigure_GetOwner(IXpsOMGeometryFigure* This,IXpsOMGeometry **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMGeometryFigure_GetSegmentData(IXpsOMGeometryFigure* This,UINT32 *dataCount,FLOAT *segmentData) {
    return This->lpVtbl->GetSegmentData(This,dataCount,segmentData);
}
static inline HRESULT IXpsOMGeometryFigure_GetSegmentTypes(IXpsOMGeometryFigure* This,UINT32 *segmentCount,XPS_SEGMENT_TYPE *segmentTypes) {
    return This->lpVtbl->GetSegmentTypes(This,segmentCount,segmentTypes);
}
static inline HRESULT IXpsOMGeometryFigure_GetSegmentStrokes(IXpsOMGeometryFigure* This,UINT32 *segmentCount,WINBOOL *segmentStrokes) {
    return This->lpVtbl->GetSegmentStrokes(This,segmentCount,segmentStrokes);
}
static inline HRESULT IXpsOMGeometryFigure_SetSegments(IXpsOMGeometryFigure* This,UINT32 segmentCount,UINT32 segmentDataCount,const XPS_SEGMENT_TYPE *segmentTypes,const FLOAT *segmentData,const WINBOOL *segmentStrokes) {
    return This->lpVtbl->SetSegments(This,segmentCount,segmentDataCount,segmentTypes,segmentData,segmentStrokes);
}
static inline HRESULT IXpsOMGeometryFigure_GetStartPoint(IXpsOMGeometryFigure* This,XPS_POINT *startPoint) {
    return This->lpVtbl->GetStartPoint(This,startPoint);
}
static inline HRESULT IXpsOMGeometryFigure_SetStartPoint(IXpsOMGeometryFigure* This,const XPS_POINT *startPoint) {
    return This->lpVtbl->SetStartPoint(This,startPoint);
}
static inline HRESULT IXpsOMGeometryFigure_GetIsClosed(IXpsOMGeometryFigure* This,WINBOOL *isClosed) {
    return This->lpVtbl->GetIsClosed(This,isClosed);
}
static inline HRESULT IXpsOMGeometryFigure_SetIsClosed(IXpsOMGeometryFigure* This,WINBOOL isClosed) {
    return This->lpVtbl->SetIsClosed(This,isClosed);
}
static inline HRESULT IXpsOMGeometryFigure_GetIsFilled(IXpsOMGeometryFigure* This,WINBOOL *isFilled) {
    return This->lpVtbl->GetIsFilled(This,isFilled);
}
static inline HRESULT IXpsOMGeometryFigure_SetIsFilled(IXpsOMGeometryFigure* This,WINBOOL isFilled) {
    return This->lpVtbl->SetIsFilled(This,isFilled);
}
static inline HRESULT IXpsOMGeometryFigure_GetSegmentCount(IXpsOMGeometryFigure* This,UINT32 *segmentCount) {
    return This->lpVtbl->GetSegmentCount(This,segmentCount);
}
static inline HRESULT IXpsOMGeometryFigure_GetSegmentDataCount(IXpsOMGeometryFigure* This,UINT32 *segmentDataCount) {
    return This->lpVtbl->GetSegmentDataCount(This,segmentDataCount);
}
static inline HRESULT IXpsOMGeometryFigure_GetSegmentStrokePattern(IXpsOMGeometryFigure* This,XPS_SEGMENT_STROKE_PATTERN *segmentStrokePattern) {
    return This->lpVtbl->GetSegmentStrokePattern(This,segmentStrokePattern);
}
static inline HRESULT IXpsOMGeometryFigure_Clone(IXpsOMGeometryFigure* This,IXpsOMGeometryFigure **geometryFigure) {
    return This->lpVtbl->Clone(This,geometryFigure);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGeometryFigure_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGeometryFigureCollection interface
 */
#ifndef __IXpsOMGeometryFigureCollection_INTERFACE_DEFINED__
#define __IXpsOMGeometryFigureCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGeometryFigureCollection, 0xfd48c3f3, 0xa58e, 0x4b5a, 0x88,0x26, 0x1d,0xe5,0x4a,0xbe,0x72,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fd48c3f3-a58e-4b5a-8826-1de54abe72b2")
IXpsOMGeometryFigureCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMGeometryFigure **geometryFigure) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMGeometryFigure *geometryFigure) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMGeometryFigure *geometryFigure) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMGeometryFigure *geometryFigure) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGeometryFigureCollection, 0xfd48c3f3, 0xa58e, 0x4b5a, 0x88,0x26, 0x1d,0xe5,0x4a,0xbe,0x72,0xb2)
#endif
#else
typedef struct IXpsOMGeometryFigureCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGeometryFigureCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGeometryFigureCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGeometryFigureCollection *This);

    /*** IXpsOMGeometryFigureCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMGeometryFigureCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMGeometryFigureCollection *This,
        UINT32 index,
        IXpsOMGeometryFigure **geometryFigure);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMGeometryFigureCollection *This,
        UINT32 index,
        IXpsOMGeometryFigure *geometryFigure);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMGeometryFigureCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMGeometryFigureCollection *This,
        UINT32 index,
        IXpsOMGeometryFigure *geometryFigure);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMGeometryFigureCollection *This,
        IXpsOMGeometryFigure *geometryFigure);

    END_INTERFACE
} IXpsOMGeometryFigureCollectionVtbl;

interface IXpsOMGeometryFigureCollection {
    CONST_VTBL IXpsOMGeometryFigureCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGeometryFigureCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGeometryFigureCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGeometryFigureCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMGeometryFigureCollection methods ***/
#define IXpsOMGeometryFigureCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMGeometryFigureCollection_GetAt(This,index,geometryFigure) (This)->lpVtbl->GetAt(This,index,geometryFigure)
#define IXpsOMGeometryFigureCollection_InsertAt(This,index,geometryFigure) (This)->lpVtbl->InsertAt(This,index,geometryFigure)
#define IXpsOMGeometryFigureCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMGeometryFigureCollection_SetAt(This,index,geometryFigure) (This)->lpVtbl->SetAt(This,index,geometryFigure)
#define IXpsOMGeometryFigureCollection_Append(This,geometryFigure) (This)->lpVtbl->Append(This,geometryFigure)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGeometryFigureCollection_QueryInterface(IXpsOMGeometryFigureCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGeometryFigureCollection_AddRef(IXpsOMGeometryFigureCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGeometryFigureCollection_Release(IXpsOMGeometryFigureCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMGeometryFigureCollection methods ***/
static inline HRESULT IXpsOMGeometryFigureCollection_GetCount(IXpsOMGeometryFigureCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMGeometryFigureCollection_GetAt(IXpsOMGeometryFigureCollection* This,UINT32 index,IXpsOMGeometryFigure **geometryFigure) {
    return This->lpVtbl->GetAt(This,index,geometryFigure);
}
static inline HRESULT IXpsOMGeometryFigureCollection_InsertAt(IXpsOMGeometryFigureCollection* This,UINT32 index,IXpsOMGeometryFigure *geometryFigure) {
    return This->lpVtbl->InsertAt(This,index,geometryFigure);
}
static inline HRESULT IXpsOMGeometryFigureCollection_RemoveAt(IXpsOMGeometryFigureCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMGeometryFigureCollection_SetAt(IXpsOMGeometryFigureCollection* This,UINT32 index,IXpsOMGeometryFigure *geometryFigure) {
    return This->lpVtbl->SetAt(This,index,geometryFigure);
}
static inline HRESULT IXpsOMGeometryFigureCollection_Append(IXpsOMGeometryFigureCollection* This,IXpsOMGeometryFigure *geometryFigure) {
    return This->lpVtbl->Append(This,geometryFigure);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGeometryFigureCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGradientStopCollection interface
 */
#ifndef __IXpsOMGradientStopCollection_INTERFACE_DEFINED__
#define __IXpsOMGradientStopCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGradientStopCollection, 0xc9174c3a, 0x3cd3, 0x4319, 0xbd,0xa4, 0x11,0xa3,0x93,0x92,0xce,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c9174c3a-3cd3-4319-bda4-11a39392ceef")
IXpsOMGradientStopCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMGradientStop **stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMGradientStop *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMGradientStop *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMGradientStop *stop) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGradientStopCollection, 0xc9174c3a, 0x3cd3, 0x4319, 0xbd,0xa4, 0x11,0xa3,0x93,0x92,0xce,0xef)
#endif
#else
typedef struct IXpsOMGradientStopCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGradientStopCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGradientStopCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGradientStopCollection *This);

    /*** IXpsOMGradientStopCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMGradientStopCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMGradientStopCollection *This,
        UINT32 index,
        IXpsOMGradientStop **stop);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMGradientStopCollection *This,
        UINT32 index,
        IXpsOMGradientStop *stop);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMGradientStopCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMGradientStopCollection *This,
        UINT32 index,
        IXpsOMGradientStop *stop);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMGradientStopCollection *This,
        IXpsOMGradientStop *stop);

    END_INTERFACE
} IXpsOMGradientStopCollectionVtbl;

interface IXpsOMGradientStopCollection {
    CONST_VTBL IXpsOMGradientStopCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGradientStopCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGradientStopCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGradientStopCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMGradientStopCollection methods ***/
#define IXpsOMGradientStopCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMGradientStopCollection_GetAt(This,index,stop) (This)->lpVtbl->GetAt(This,index,stop)
#define IXpsOMGradientStopCollection_InsertAt(This,index,stop) (This)->lpVtbl->InsertAt(This,index,stop)
#define IXpsOMGradientStopCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMGradientStopCollection_SetAt(This,index,stop) (This)->lpVtbl->SetAt(This,index,stop)
#define IXpsOMGradientStopCollection_Append(This,stop) (This)->lpVtbl->Append(This,stop)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGradientStopCollection_QueryInterface(IXpsOMGradientStopCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGradientStopCollection_AddRef(IXpsOMGradientStopCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGradientStopCollection_Release(IXpsOMGradientStopCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMGradientStopCollection methods ***/
static inline HRESULT IXpsOMGradientStopCollection_GetCount(IXpsOMGradientStopCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMGradientStopCollection_GetAt(IXpsOMGradientStopCollection* This,UINT32 index,IXpsOMGradientStop **stop) {
    return This->lpVtbl->GetAt(This,index,stop);
}
static inline HRESULT IXpsOMGradientStopCollection_InsertAt(IXpsOMGradientStopCollection* This,UINT32 index,IXpsOMGradientStop *stop) {
    return This->lpVtbl->InsertAt(This,index,stop);
}
static inline HRESULT IXpsOMGradientStopCollection_RemoveAt(IXpsOMGradientStopCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMGradientStopCollection_SetAt(IXpsOMGradientStopCollection* This,UINT32 index,IXpsOMGradientStop *stop) {
    return This->lpVtbl->SetAt(This,index,stop);
}
static inline HRESULT IXpsOMGradientStopCollection_Append(IXpsOMGradientStopCollection* This,IXpsOMGradientStop *stop) {
    return This->lpVtbl->Append(This,stop);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGradientStopCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGradientStop interface
 */
#ifndef __IXpsOMGradientStop_INTERFACE_DEFINED__
#define __IXpsOMGradientStop_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGradientStop, 0x5cf4f5cc, 0x3969, 0x49b5, 0xa7,0x0a, 0x55,0x50,0xb6,0x18,0xfe,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5cf4f5cc-3969-49b5-a70a-5550b618fe49")
IXpsOMGradientStop : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMGradientBrush **owner) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOffset(
        FLOAT *offset) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOffset(
        FLOAT offset) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColor(
        XPS_COLOR *color,
        IXpsOMColorProfileResource **colorProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColor(
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMGradientStop **gradientStop) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGradientStop, 0x5cf4f5cc, 0x3969, 0x49b5, 0xa7,0x0a, 0x55,0x50,0xb6,0x18,0xfe,0x49)
#endif
#else
typedef struct IXpsOMGradientStopVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGradientStop *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGradientStop *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGradientStop *This);

    /*** IXpsOMGradientStop methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMGradientStop *This,
        IXpsOMGradientBrush **owner);

    HRESULT (STDMETHODCALLTYPE *GetOffset)(
        IXpsOMGradientStop *This,
        FLOAT *offset);

    HRESULT (STDMETHODCALLTYPE *SetOffset)(
        IXpsOMGradientStop *This,
        FLOAT offset);

    HRESULT (STDMETHODCALLTYPE *GetColor)(
        IXpsOMGradientStop *This,
        XPS_COLOR *color,
        IXpsOMColorProfileResource **colorProfile);

    HRESULT (STDMETHODCALLTYPE *SetColor)(
        IXpsOMGradientStop *This,
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMGradientStop *This,
        IXpsOMGradientStop **gradientStop);

    END_INTERFACE
} IXpsOMGradientStopVtbl;

interface IXpsOMGradientStop {
    CONST_VTBL IXpsOMGradientStopVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGradientStop_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGradientStop_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGradientStop_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMGradientStop methods ***/
#define IXpsOMGradientStop_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMGradientStop_GetOffset(This,offset) (This)->lpVtbl->GetOffset(This,offset)
#define IXpsOMGradientStop_SetOffset(This,offset) (This)->lpVtbl->SetOffset(This,offset)
#define IXpsOMGradientStop_GetColor(This,color,colorProfile) (This)->lpVtbl->GetColor(This,color,colorProfile)
#define IXpsOMGradientStop_SetColor(This,color,colorProfile) (This)->lpVtbl->SetColor(This,color,colorProfile)
#define IXpsOMGradientStop_Clone(This,gradientStop) (This)->lpVtbl->Clone(This,gradientStop)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGradientStop_QueryInterface(IXpsOMGradientStop* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGradientStop_AddRef(IXpsOMGradientStop* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGradientStop_Release(IXpsOMGradientStop* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMGradientStop methods ***/
static inline HRESULT IXpsOMGradientStop_GetOwner(IXpsOMGradientStop* This,IXpsOMGradientBrush **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMGradientStop_GetOffset(IXpsOMGradientStop* This,FLOAT *offset) {
    return This->lpVtbl->GetOffset(This,offset);
}
static inline HRESULT IXpsOMGradientStop_SetOffset(IXpsOMGradientStop* This,FLOAT offset) {
    return This->lpVtbl->SetOffset(This,offset);
}
static inline HRESULT IXpsOMGradientStop_GetColor(IXpsOMGradientStop* This,XPS_COLOR *color,IXpsOMColorProfileResource **colorProfile) {
    return This->lpVtbl->GetColor(This,color,colorProfile);
}
static inline HRESULT IXpsOMGradientStop_SetColor(IXpsOMGradientStop* This,const XPS_COLOR *color,IXpsOMColorProfileResource *colorProfile) {
    return This->lpVtbl->SetColor(This,color,colorProfile);
}
static inline HRESULT IXpsOMGradientStop_Clone(IXpsOMGradientStop* This,IXpsOMGradientStop **gradientStop) {
    return This->lpVtbl->Clone(This,gradientStop);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGradientStop_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPartResources interface
 */
#ifndef __IXpsOMPartResources_INTERFACE_DEFINED__
#define __IXpsOMPartResources_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPartResources, 0xf4cf7729, 0x4864, 0x4275, 0x99,0xb3, 0xa8,0x71,0x71,0x63,0xec,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f4cf7729-4864-4275-99b3-a8717163ecaf")
IXpsOMPartResources : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetFontResources(
        IXpsOMFontResourceCollection **fontResources) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImageResources(
        IXpsOMImageResourceCollection **imageResources) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorProfileResources(
        IXpsOMColorProfileResourceCollection **colorProfileResources) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRemoteDictionaryResources(
        IXpsOMRemoteDictionaryResourceCollection **dictionaryResources) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPartResources, 0xf4cf7729, 0x4864, 0x4275, 0x99,0xb3, 0xa8,0x71,0x71,0x63,0xec,0xaf)
#endif
#else
typedef struct IXpsOMPartResourcesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPartResources *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPartResources *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPartResources *This);

    /*** IXpsOMPartResources methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFontResources)(
        IXpsOMPartResources *This,
        IXpsOMFontResourceCollection **fontResources);

    HRESULT (STDMETHODCALLTYPE *GetImageResources)(
        IXpsOMPartResources *This,
        IXpsOMImageResourceCollection **imageResources);

    HRESULT (STDMETHODCALLTYPE *GetColorProfileResources)(
        IXpsOMPartResources *This,
        IXpsOMColorProfileResourceCollection **colorProfileResources);

    HRESULT (STDMETHODCALLTYPE *GetRemoteDictionaryResources)(
        IXpsOMPartResources *This,
        IXpsOMRemoteDictionaryResourceCollection **dictionaryResources);

    END_INTERFACE
} IXpsOMPartResourcesVtbl;

interface IXpsOMPartResources {
    CONST_VTBL IXpsOMPartResourcesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPartResources_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPartResources_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPartResources_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPartResources methods ***/
#define IXpsOMPartResources_GetFontResources(This,fontResources) (This)->lpVtbl->GetFontResources(This,fontResources)
#define IXpsOMPartResources_GetImageResources(This,imageResources) (This)->lpVtbl->GetImageResources(This,imageResources)
#define IXpsOMPartResources_GetColorProfileResources(This,colorProfileResources) (This)->lpVtbl->GetColorProfileResources(This,colorProfileResources)
#define IXpsOMPartResources_GetRemoteDictionaryResources(This,dictionaryResources) (This)->lpVtbl->GetRemoteDictionaryResources(This,dictionaryResources)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPartResources_QueryInterface(IXpsOMPartResources* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPartResources_AddRef(IXpsOMPartResources* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPartResources_Release(IXpsOMPartResources* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPartResources methods ***/
static inline HRESULT IXpsOMPartResources_GetFontResources(IXpsOMPartResources* This,IXpsOMFontResourceCollection **fontResources) {
    return This->lpVtbl->GetFontResources(This,fontResources);
}
static inline HRESULT IXpsOMPartResources_GetImageResources(IXpsOMPartResources* This,IXpsOMImageResourceCollection **imageResources) {
    return This->lpVtbl->GetImageResources(This,imageResources);
}
static inline HRESULT IXpsOMPartResources_GetColorProfileResources(IXpsOMPartResources* This,IXpsOMColorProfileResourceCollection **colorProfileResources) {
    return This->lpVtbl->GetColorProfileResources(This,colorProfileResources);
}
static inline HRESULT IXpsOMPartResources_GetRemoteDictionaryResources(IXpsOMPartResources* This,IXpsOMRemoteDictionaryResourceCollection **dictionaryResources) {
    return This->lpVtbl->GetRemoteDictionaryResources(This,dictionaryResources);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPartResources_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMFontResourceCollection interface
 */
#ifndef __IXpsOMFontResourceCollection_INTERFACE_DEFINED__
#define __IXpsOMFontResourceCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMFontResourceCollection, 0x70b4a6bb, 0x88d4, 0x4fa8, 0xaa,0xf9, 0x6d,0x9c,0x59,0x6f,0xdb,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("70b4a6bb-88d4-4fa8-aaf9-6d9c596fdbad")
IXpsOMFontResourceCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMFontResource **value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMFontResource *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMFontResource *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMFontResource *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetByPartName(
        IOpcPartUri *partName,
        IXpsOMFontResource **part) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMFontResourceCollection, 0x70b4a6bb, 0x88d4, 0x4fa8, 0xaa,0xf9, 0x6d,0x9c,0x59,0x6f,0xdb,0xad)
#endif
#else
typedef struct IXpsOMFontResourceCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMFontResourceCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMFontResourceCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMFontResourceCollection *This);

    /*** IXpsOMFontResourceCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMFontResourceCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMFontResourceCollection *This,
        UINT32 index,
        IXpsOMFontResource **value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMFontResourceCollection *This,
        UINT32 index,
        IXpsOMFontResource *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMFontResourceCollection *This,
        UINT32 index,
        IXpsOMFontResource *value);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMFontResourceCollection *This,
        IXpsOMFontResource *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMFontResourceCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *GetByPartName)(
        IXpsOMFontResourceCollection *This,
        IOpcPartUri *partName,
        IXpsOMFontResource **part);

    END_INTERFACE
} IXpsOMFontResourceCollectionVtbl;

interface IXpsOMFontResourceCollection {
    CONST_VTBL IXpsOMFontResourceCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMFontResourceCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMFontResourceCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMFontResourceCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMFontResourceCollection methods ***/
#define IXpsOMFontResourceCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMFontResourceCollection_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define IXpsOMFontResourceCollection_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define IXpsOMFontResourceCollection_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define IXpsOMFontResourceCollection_Append(This,value) (This)->lpVtbl->Append(This,value)
#define IXpsOMFontResourceCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMFontResourceCollection_GetByPartName(This,partName,part) (This)->lpVtbl->GetByPartName(This,partName,part)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMFontResourceCollection_QueryInterface(IXpsOMFontResourceCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMFontResourceCollection_AddRef(IXpsOMFontResourceCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMFontResourceCollection_Release(IXpsOMFontResourceCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMFontResourceCollection methods ***/
static inline HRESULT IXpsOMFontResourceCollection_GetCount(IXpsOMFontResourceCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMFontResourceCollection_GetAt(IXpsOMFontResourceCollection* This,UINT32 index,IXpsOMFontResource **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT IXpsOMFontResourceCollection_SetAt(IXpsOMFontResourceCollection* This,UINT32 index,IXpsOMFontResource *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT IXpsOMFontResourceCollection_InsertAt(IXpsOMFontResourceCollection* This,UINT32 index,IXpsOMFontResource *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT IXpsOMFontResourceCollection_Append(IXpsOMFontResourceCollection* This,IXpsOMFontResource *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT IXpsOMFontResourceCollection_RemoveAt(IXpsOMFontResourceCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMFontResourceCollection_GetByPartName(IXpsOMFontResourceCollection* This,IOpcPartUri *partName,IXpsOMFontResource **part) {
    return This->lpVtbl->GetByPartName(This,partName,part);
}
#endif
#endif

#endif


#endif  /* __IXpsOMFontResourceCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMImageResourceCollection interface
 */
#ifndef __IXpsOMImageResourceCollection_INTERFACE_DEFINED__
#define __IXpsOMImageResourceCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMImageResourceCollection, 0x7a4a1a71, 0x9cde, 0x4b71, 0xb3,0x3f, 0x62,0xde,0x84,0x3e,0xab,0xfe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7a4a1a71-9cde-4b71-b33f-62de843eabfe")
IXpsOMImageResourceCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMImageResource **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMImageResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMImageResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMImageResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetByPartName(
        IOpcPartUri *partName,
        IXpsOMImageResource **part) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMImageResourceCollection, 0x7a4a1a71, 0x9cde, 0x4b71, 0xb3,0x3f, 0x62,0xde,0x84,0x3e,0xab,0xfe)
#endif
#else
typedef struct IXpsOMImageResourceCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMImageResourceCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMImageResourceCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMImageResourceCollection *This);

    /*** IXpsOMImageResourceCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMImageResourceCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMImageResourceCollection *This,
        UINT32 index,
        IXpsOMImageResource **object);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMImageResourceCollection *This,
        UINT32 index,
        IXpsOMImageResource *object);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMImageResourceCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMImageResourceCollection *This,
        UINT32 index,
        IXpsOMImageResource *object);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMImageResourceCollection *This,
        IXpsOMImageResource *object);

    HRESULT (STDMETHODCALLTYPE *GetByPartName)(
        IXpsOMImageResourceCollection *This,
        IOpcPartUri *partName,
        IXpsOMImageResource **part);

    END_INTERFACE
} IXpsOMImageResourceCollectionVtbl;

interface IXpsOMImageResourceCollection {
    CONST_VTBL IXpsOMImageResourceCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMImageResourceCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMImageResourceCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMImageResourceCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMImageResourceCollection methods ***/
#define IXpsOMImageResourceCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMImageResourceCollection_GetAt(This,index,object) (This)->lpVtbl->GetAt(This,index,object)
#define IXpsOMImageResourceCollection_InsertAt(This,index,object) (This)->lpVtbl->InsertAt(This,index,object)
#define IXpsOMImageResourceCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMImageResourceCollection_SetAt(This,index,object) (This)->lpVtbl->SetAt(This,index,object)
#define IXpsOMImageResourceCollection_Append(This,object) (This)->lpVtbl->Append(This,object)
#define IXpsOMImageResourceCollection_GetByPartName(This,partName,part) (This)->lpVtbl->GetByPartName(This,partName,part)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMImageResourceCollection_QueryInterface(IXpsOMImageResourceCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMImageResourceCollection_AddRef(IXpsOMImageResourceCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMImageResourceCollection_Release(IXpsOMImageResourceCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMImageResourceCollection methods ***/
static inline HRESULT IXpsOMImageResourceCollection_GetCount(IXpsOMImageResourceCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMImageResourceCollection_GetAt(IXpsOMImageResourceCollection* This,UINT32 index,IXpsOMImageResource **object) {
    return This->lpVtbl->GetAt(This,index,object);
}
static inline HRESULT IXpsOMImageResourceCollection_InsertAt(IXpsOMImageResourceCollection* This,UINT32 index,IXpsOMImageResource *object) {
    return This->lpVtbl->InsertAt(This,index,object);
}
static inline HRESULT IXpsOMImageResourceCollection_RemoveAt(IXpsOMImageResourceCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMImageResourceCollection_SetAt(IXpsOMImageResourceCollection* This,UINT32 index,IXpsOMImageResource *object) {
    return This->lpVtbl->SetAt(This,index,object);
}
static inline HRESULT IXpsOMImageResourceCollection_Append(IXpsOMImageResourceCollection* This,IXpsOMImageResource *object) {
    return This->lpVtbl->Append(This,object);
}
static inline HRESULT IXpsOMImageResourceCollection_GetByPartName(IXpsOMImageResourceCollection* This,IOpcPartUri *partName,IXpsOMImageResource **part) {
    return This->lpVtbl->GetByPartName(This,partName,part);
}
#endif
#endif

#endif


#endif  /* __IXpsOMImageResourceCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMColorProfileResourceCollection interface
 */
#ifndef __IXpsOMColorProfileResourceCollection_INTERFACE_DEFINED__
#define __IXpsOMColorProfileResourceCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMColorProfileResourceCollection, 0x12759630, 0x5fba, 0x4283, 0x8f,0x7d, 0xcc,0xa8,0x49,0x80,0x9e,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("12759630-5fba-4283-8f7d-cca849809edb")
IXpsOMColorProfileResourceCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMColorProfileResource **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMColorProfileResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMColorProfileResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMColorProfileResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetByPartName(
        IOpcPartUri *partName,
        IXpsOMColorProfileResource **part) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMColorProfileResourceCollection, 0x12759630, 0x5fba, 0x4283, 0x8f,0x7d, 0xcc,0xa8,0x49,0x80,0x9e,0xdb)
#endif
#else
typedef struct IXpsOMColorProfileResourceCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMColorProfileResourceCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMColorProfileResourceCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMColorProfileResourceCollection *This);

    /*** IXpsOMColorProfileResourceCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMColorProfileResourceCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMColorProfileResourceCollection *This,
        UINT32 index,
        IXpsOMColorProfileResource **object);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMColorProfileResourceCollection *This,
        UINT32 index,
        IXpsOMColorProfileResource *object);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMColorProfileResourceCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMColorProfileResourceCollection *This,
        UINT32 index,
        IXpsOMColorProfileResource *object);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMColorProfileResourceCollection *This,
        IXpsOMColorProfileResource *object);

    HRESULT (STDMETHODCALLTYPE *GetByPartName)(
        IXpsOMColorProfileResourceCollection *This,
        IOpcPartUri *partName,
        IXpsOMColorProfileResource **part);

    END_INTERFACE
} IXpsOMColorProfileResourceCollectionVtbl;

interface IXpsOMColorProfileResourceCollection {
    CONST_VTBL IXpsOMColorProfileResourceCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMColorProfileResourceCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMColorProfileResourceCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMColorProfileResourceCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMColorProfileResourceCollection methods ***/
#define IXpsOMColorProfileResourceCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMColorProfileResourceCollection_GetAt(This,index,object) (This)->lpVtbl->GetAt(This,index,object)
#define IXpsOMColorProfileResourceCollection_InsertAt(This,index,object) (This)->lpVtbl->InsertAt(This,index,object)
#define IXpsOMColorProfileResourceCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMColorProfileResourceCollection_SetAt(This,index,object) (This)->lpVtbl->SetAt(This,index,object)
#define IXpsOMColorProfileResourceCollection_Append(This,object) (This)->lpVtbl->Append(This,object)
#define IXpsOMColorProfileResourceCollection_GetByPartName(This,partName,part) (This)->lpVtbl->GetByPartName(This,partName,part)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMColorProfileResourceCollection_QueryInterface(IXpsOMColorProfileResourceCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMColorProfileResourceCollection_AddRef(IXpsOMColorProfileResourceCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMColorProfileResourceCollection_Release(IXpsOMColorProfileResourceCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMColorProfileResourceCollection methods ***/
static inline HRESULT IXpsOMColorProfileResourceCollection_GetCount(IXpsOMColorProfileResourceCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMColorProfileResourceCollection_GetAt(IXpsOMColorProfileResourceCollection* This,UINT32 index,IXpsOMColorProfileResource **object) {
    return This->lpVtbl->GetAt(This,index,object);
}
static inline HRESULT IXpsOMColorProfileResourceCollection_InsertAt(IXpsOMColorProfileResourceCollection* This,UINT32 index,IXpsOMColorProfileResource *object) {
    return This->lpVtbl->InsertAt(This,index,object);
}
static inline HRESULT IXpsOMColorProfileResourceCollection_RemoveAt(IXpsOMColorProfileResourceCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMColorProfileResourceCollection_SetAt(IXpsOMColorProfileResourceCollection* This,UINT32 index,IXpsOMColorProfileResource *object) {
    return This->lpVtbl->SetAt(This,index,object);
}
static inline HRESULT IXpsOMColorProfileResourceCollection_Append(IXpsOMColorProfileResourceCollection* This,IXpsOMColorProfileResource *object) {
    return This->lpVtbl->Append(This,object);
}
static inline HRESULT IXpsOMColorProfileResourceCollection_GetByPartName(IXpsOMColorProfileResourceCollection* This,IOpcPartUri *partName,IXpsOMColorProfileResource **part) {
    return This->lpVtbl->GetByPartName(This,partName,part);
}
#endif
#endif

#endif


#endif  /* __IXpsOMColorProfileResourceCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMRemoteDictionaryResourceCollection interface
 */
#ifndef __IXpsOMRemoteDictionaryResourceCollection_INTERFACE_DEFINED__
#define __IXpsOMRemoteDictionaryResourceCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMRemoteDictionaryResourceCollection, 0x5c38db61, 0x7fec, 0x464a, 0x87,0xbd, 0x41,0xe3,0xbe,0xf0,0x18,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5c38db61-7fec-464a-87bd-41e3bef018be")
IXpsOMRemoteDictionaryResourceCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMRemoteDictionaryResource **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMRemoteDictionaryResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMRemoteDictionaryResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMRemoteDictionaryResource *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetByPartName(
        IOpcPartUri *partName,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMRemoteDictionaryResourceCollection, 0x5c38db61, 0x7fec, 0x464a, 0x87,0xbd, 0x41,0xe3,0xbe,0xf0,0x18,0xbe)
#endif
#else
typedef struct IXpsOMRemoteDictionaryResourceCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMRemoteDictionaryResourceCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMRemoteDictionaryResourceCollection *This);

    /*** IXpsOMRemoteDictionaryResourceCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        UINT32 index,
        IXpsOMRemoteDictionaryResource **object);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        UINT32 index,
        IXpsOMRemoteDictionaryResource *object);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        UINT32 index,
        IXpsOMRemoteDictionaryResource *object);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        IXpsOMRemoteDictionaryResource *object);

    HRESULT (STDMETHODCALLTYPE *GetByPartName)(
        IXpsOMRemoteDictionaryResourceCollection *This,
        IOpcPartUri *partName,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource);

    END_INTERFACE
} IXpsOMRemoteDictionaryResourceCollectionVtbl;

interface IXpsOMRemoteDictionaryResourceCollection {
    CONST_VTBL IXpsOMRemoteDictionaryResourceCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMRemoteDictionaryResourceCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMRemoteDictionaryResourceCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMRemoteDictionaryResourceCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMRemoteDictionaryResourceCollection methods ***/
#define IXpsOMRemoteDictionaryResourceCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMRemoteDictionaryResourceCollection_GetAt(This,index,object) (This)->lpVtbl->GetAt(This,index,object)
#define IXpsOMRemoteDictionaryResourceCollection_InsertAt(This,index,object) (This)->lpVtbl->InsertAt(This,index,object)
#define IXpsOMRemoteDictionaryResourceCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMRemoteDictionaryResourceCollection_SetAt(This,index,object) (This)->lpVtbl->SetAt(This,index,object)
#define IXpsOMRemoteDictionaryResourceCollection_Append(This,object) (This)->lpVtbl->Append(This,object)
#define IXpsOMRemoteDictionaryResourceCollection_GetByPartName(This,partName,remoteDictionaryResource) (This)->lpVtbl->GetByPartName(This,partName,remoteDictionaryResource)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_QueryInterface(IXpsOMRemoteDictionaryResourceCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMRemoteDictionaryResourceCollection_AddRef(IXpsOMRemoteDictionaryResourceCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMRemoteDictionaryResourceCollection_Release(IXpsOMRemoteDictionaryResourceCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMRemoteDictionaryResourceCollection methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_GetCount(IXpsOMRemoteDictionaryResourceCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_GetAt(IXpsOMRemoteDictionaryResourceCollection* This,UINT32 index,IXpsOMRemoteDictionaryResource **object) {
    return This->lpVtbl->GetAt(This,index,object);
}
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_InsertAt(IXpsOMRemoteDictionaryResourceCollection* This,UINT32 index,IXpsOMRemoteDictionaryResource *object) {
    return This->lpVtbl->InsertAt(This,index,object);
}
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_RemoveAt(IXpsOMRemoteDictionaryResourceCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_SetAt(IXpsOMRemoteDictionaryResourceCollection* This,UINT32 index,IXpsOMRemoteDictionaryResource *object) {
    return This->lpVtbl->SetAt(This,index,object);
}
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_Append(IXpsOMRemoteDictionaryResourceCollection* This,IXpsOMRemoteDictionaryResource *object) {
    return This->lpVtbl->Append(This,object);
}
static inline HRESULT IXpsOMRemoteDictionaryResourceCollection_GetByPartName(IXpsOMRemoteDictionaryResourceCollection* This,IOpcPartUri *partName,IXpsOMRemoteDictionaryResource **remoteDictionaryResource) {
    return This->lpVtbl->GetByPartName(This,partName,remoteDictionaryResource);
}
#endif
#endif

#endif


#endif  /* __IXpsOMRemoteDictionaryResourceCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMSignatureBlockResourceCollection interface
 */
#ifndef __IXpsOMSignatureBlockResourceCollection_INTERFACE_DEFINED__
#define __IXpsOMSignatureBlockResourceCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMSignatureBlockResourceCollection, 0xab8f5d8e, 0x351b, 0x4d33, 0xaa,0xed, 0xfa,0x56,0xf0,0x02,0x29,0x31);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ab8f5d8e-351b-4d33-aaed-fa56f0022931")
IXpsOMSignatureBlockResourceCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMSignatureBlockResource **signatureBlockResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMSignatureBlockResource *signatureBlockResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMSignatureBlockResource *signatureBlockResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMSignatureBlockResource *signatureBlockResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetByPartName(
        IOpcPartUri *partName,
        IXpsOMSignatureBlockResource **signatureBlockResource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMSignatureBlockResourceCollection, 0xab8f5d8e, 0x351b, 0x4d33, 0xaa,0xed, 0xfa,0x56,0xf0,0x02,0x29,0x31)
#endif
#else
typedef struct IXpsOMSignatureBlockResourceCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMSignatureBlockResourceCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMSignatureBlockResourceCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMSignatureBlockResourceCollection *This);

    /*** IXpsOMSignatureBlockResourceCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMSignatureBlockResourceCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMSignatureBlockResourceCollection *This,
        UINT32 index,
        IXpsOMSignatureBlockResource **signatureBlockResource);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMSignatureBlockResourceCollection *This,
        UINT32 index,
        IXpsOMSignatureBlockResource *signatureBlockResource);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMSignatureBlockResourceCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMSignatureBlockResourceCollection *This,
        UINT32 index,
        IXpsOMSignatureBlockResource *signatureBlockResource);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMSignatureBlockResourceCollection *This,
        IXpsOMSignatureBlockResource *signatureBlockResource);

    HRESULT (STDMETHODCALLTYPE *GetByPartName)(
        IXpsOMSignatureBlockResourceCollection *This,
        IOpcPartUri *partName,
        IXpsOMSignatureBlockResource **signatureBlockResource);

    END_INTERFACE
} IXpsOMSignatureBlockResourceCollectionVtbl;

interface IXpsOMSignatureBlockResourceCollection {
    CONST_VTBL IXpsOMSignatureBlockResourceCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMSignatureBlockResourceCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMSignatureBlockResourceCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMSignatureBlockResourceCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMSignatureBlockResourceCollection methods ***/
#define IXpsOMSignatureBlockResourceCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMSignatureBlockResourceCollection_GetAt(This,index,signatureBlockResource) (This)->lpVtbl->GetAt(This,index,signatureBlockResource)
#define IXpsOMSignatureBlockResourceCollection_InsertAt(This,index,signatureBlockResource) (This)->lpVtbl->InsertAt(This,index,signatureBlockResource)
#define IXpsOMSignatureBlockResourceCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMSignatureBlockResourceCollection_SetAt(This,index,signatureBlockResource) (This)->lpVtbl->SetAt(This,index,signatureBlockResource)
#define IXpsOMSignatureBlockResourceCollection_Append(This,signatureBlockResource) (This)->lpVtbl->Append(This,signatureBlockResource)
#define IXpsOMSignatureBlockResourceCollection_GetByPartName(This,partName,signatureBlockResource) (This)->lpVtbl->GetByPartName(This,partName,signatureBlockResource)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMSignatureBlockResourceCollection_QueryInterface(IXpsOMSignatureBlockResourceCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMSignatureBlockResourceCollection_AddRef(IXpsOMSignatureBlockResourceCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMSignatureBlockResourceCollection_Release(IXpsOMSignatureBlockResourceCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMSignatureBlockResourceCollection methods ***/
static inline HRESULT IXpsOMSignatureBlockResourceCollection_GetCount(IXpsOMSignatureBlockResourceCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMSignatureBlockResourceCollection_GetAt(IXpsOMSignatureBlockResourceCollection* This,UINT32 index,IXpsOMSignatureBlockResource **signatureBlockResource) {
    return This->lpVtbl->GetAt(This,index,signatureBlockResource);
}
static inline HRESULT IXpsOMSignatureBlockResourceCollection_InsertAt(IXpsOMSignatureBlockResourceCollection* This,UINT32 index,IXpsOMSignatureBlockResource *signatureBlockResource) {
    return This->lpVtbl->InsertAt(This,index,signatureBlockResource);
}
static inline HRESULT IXpsOMSignatureBlockResourceCollection_RemoveAt(IXpsOMSignatureBlockResourceCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMSignatureBlockResourceCollection_SetAt(IXpsOMSignatureBlockResourceCollection* This,UINT32 index,IXpsOMSignatureBlockResource *signatureBlockResource) {
    return This->lpVtbl->SetAt(This,index,signatureBlockResource);
}
static inline HRESULT IXpsOMSignatureBlockResourceCollection_Append(IXpsOMSignatureBlockResourceCollection* This,IXpsOMSignatureBlockResource *signatureBlockResource) {
    return This->lpVtbl->Append(This,signatureBlockResource);
}
static inline HRESULT IXpsOMSignatureBlockResourceCollection_GetByPartName(IXpsOMSignatureBlockResourceCollection* This,IOpcPartUri *partName,IXpsOMSignatureBlockResource **signatureBlockResource) {
    return This->lpVtbl->GetByPartName(This,partName,signatureBlockResource);
}
#endif
#endif

#endif


#endif  /* __IXpsOMSignatureBlockResourceCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMVisualCollection interface
 */
#ifndef __IXpsOMVisualCollection_INTERFACE_DEFINED__
#define __IXpsOMVisualCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMVisualCollection, 0x94d8abde, 0xab91, 0x46a8, 0x82,0xb7, 0xf5,0xb0,0x5e,0xf0,0x1a,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94d8abde-ab91-46a8-82b7-f5b05ef01a96")
IXpsOMVisualCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMVisual **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMVisual *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMVisual *object) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMVisual *object) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMVisualCollection, 0x94d8abde, 0xab91, 0x46a8, 0x82,0xb7, 0xf5,0xb0,0x5e,0xf0,0x1a,0x96)
#endif
#else
typedef struct IXpsOMVisualCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMVisualCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMVisualCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMVisualCollection *This);

    /*** IXpsOMVisualCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMVisualCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMVisualCollection *This,
        UINT32 index,
        IXpsOMVisual **object);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMVisualCollection *This,
        UINT32 index,
        IXpsOMVisual *object);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMVisualCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMVisualCollection *This,
        UINT32 index,
        IXpsOMVisual *object);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMVisualCollection *This,
        IXpsOMVisual *object);

    END_INTERFACE
} IXpsOMVisualCollectionVtbl;

interface IXpsOMVisualCollection {
    CONST_VTBL IXpsOMVisualCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMVisualCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMVisualCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMVisualCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMVisualCollection methods ***/
#define IXpsOMVisualCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMVisualCollection_GetAt(This,index,object) (This)->lpVtbl->GetAt(This,index,object)
#define IXpsOMVisualCollection_InsertAt(This,index,object) (This)->lpVtbl->InsertAt(This,index,object)
#define IXpsOMVisualCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMVisualCollection_SetAt(This,index,object) (This)->lpVtbl->SetAt(This,index,object)
#define IXpsOMVisualCollection_Append(This,object) (This)->lpVtbl->Append(This,object)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMVisualCollection_QueryInterface(IXpsOMVisualCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMVisualCollection_AddRef(IXpsOMVisualCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMVisualCollection_Release(IXpsOMVisualCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMVisualCollection methods ***/
static inline HRESULT IXpsOMVisualCollection_GetCount(IXpsOMVisualCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMVisualCollection_GetAt(IXpsOMVisualCollection* This,UINT32 index,IXpsOMVisual **object) {
    return This->lpVtbl->GetAt(This,index,object);
}
static inline HRESULT IXpsOMVisualCollection_InsertAt(IXpsOMVisualCollection* This,UINT32 index,IXpsOMVisual *object) {
    return This->lpVtbl->InsertAt(This,index,object);
}
static inline HRESULT IXpsOMVisualCollection_RemoveAt(IXpsOMVisualCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMVisualCollection_SetAt(IXpsOMVisualCollection* This,UINT32 index,IXpsOMVisual *object) {
    return This->lpVtbl->SetAt(This,index,object);
}
static inline HRESULT IXpsOMVisualCollection_Append(IXpsOMVisualCollection* This,IXpsOMVisual *object) {
    return This->lpVtbl->Append(This,object);
}
#endif
#endif

#endif


#endif  /* __IXpsOMVisualCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMDictionary interface
 */
#ifndef __IXpsOMDictionary_INTERFACE_DEFINED__
#define __IXpsOMDictionary_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMDictionary, 0x897c86b8, 0x8eaf, 0x4ae3, 0xbd,0xde, 0x56,0x41,0x9f,0xcf,0x42,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("897c86b8-8eaf-4ae3-bdde-56419fcf4236")
IXpsOMDictionary : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IUnknown **owner) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        LPWSTR *key,
        IXpsOMShareable **entry) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetByKey(
        LPCWSTR key,
        IXpsOMShareable *beforeEntry,
        IXpsOMShareable **entry) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIndex(
        IXpsOMShareable *entry,
        UINT32 *index) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        LPCWSTR key,
        IXpsOMShareable *entry) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        LPCWSTR key,
        IXpsOMShareable *entry) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        LPCWSTR key,
        IXpsOMShareable *entry) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMDictionary **dictionary) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMDictionary, 0x897c86b8, 0x8eaf, 0x4ae3, 0xbd,0xde, 0x56,0x41,0x9f,0xcf,0x42,0x36)
#endif
#else
typedef struct IXpsOMDictionaryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMDictionary *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMDictionary *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMDictionary *This);

    /*** IXpsOMDictionary methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMDictionary *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMDictionary *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMDictionary *This,
        UINT32 index,
        LPWSTR *key,
        IXpsOMShareable **entry);

    HRESULT (STDMETHODCALLTYPE *GetByKey)(
        IXpsOMDictionary *This,
        LPCWSTR key,
        IXpsOMShareable *beforeEntry,
        IXpsOMShareable **entry);

    HRESULT (STDMETHODCALLTYPE *GetIndex)(
        IXpsOMDictionary *This,
        IXpsOMShareable *entry,
        UINT32 *index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMDictionary *This,
        LPCWSTR key,
        IXpsOMShareable *entry);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMDictionary *This,
        UINT32 index,
        LPCWSTR key,
        IXpsOMShareable *entry);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMDictionary *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMDictionary *This,
        UINT32 index,
        LPCWSTR key,
        IXpsOMShareable *entry);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMDictionary *This,
        IXpsOMDictionary **dictionary);

    END_INTERFACE
} IXpsOMDictionaryVtbl;

interface IXpsOMDictionary {
    CONST_VTBL IXpsOMDictionaryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMDictionary_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMDictionary_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMDictionary_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMDictionary methods ***/
#define IXpsOMDictionary_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMDictionary_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMDictionary_GetAt(This,index,key,entry) (This)->lpVtbl->GetAt(This,index,key,entry)
#define IXpsOMDictionary_GetByKey(This,key,beforeEntry,entry) (This)->lpVtbl->GetByKey(This,key,beforeEntry,entry)
#define IXpsOMDictionary_GetIndex(This,entry,index) (This)->lpVtbl->GetIndex(This,entry,index)
#define IXpsOMDictionary_Append(This,key,entry) (This)->lpVtbl->Append(This,key,entry)
#define IXpsOMDictionary_InsertAt(This,index,key,entry) (This)->lpVtbl->InsertAt(This,index,key,entry)
#define IXpsOMDictionary_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMDictionary_SetAt(This,index,key,entry) (This)->lpVtbl->SetAt(This,index,key,entry)
#define IXpsOMDictionary_Clone(This,dictionary) (This)->lpVtbl->Clone(This,dictionary)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMDictionary_QueryInterface(IXpsOMDictionary* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMDictionary_AddRef(IXpsOMDictionary* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMDictionary_Release(IXpsOMDictionary* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMDictionary methods ***/
static inline HRESULT IXpsOMDictionary_GetOwner(IXpsOMDictionary* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMDictionary_GetCount(IXpsOMDictionary* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMDictionary_GetAt(IXpsOMDictionary* This,UINT32 index,LPWSTR *key,IXpsOMShareable **entry) {
    return This->lpVtbl->GetAt(This,index,key,entry);
}
static inline HRESULT IXpsOMDictionary_GetByKey(IXpsOMDictionary* This,LPCWSTR key,IXpsOMShareable *beforeEntry,IXpsOMShareable **entry) {
    return This->lpVtbl->GetByKey(This,key,beforeEntry,entry);
}
static inline HRESULT IXpsOMDictionary_GetIndex(IXpsOMDictionary* This,IXpsOMShareable *entry,UINT32 *index) {
    return This->lpVtbl->GetIndex(This,entry,index);
}
static inline HRESULT IXpsOMDictionary_Append(IXpsOMDictionary* This,LPCWSTR key,IXpsOMShareable *entry) {
    return This->lpVtbl->Append(This,key,entry);
}
static inline HRESULT IXpsOMDictionary_InsertAt(IXpsOMDictionary* This,UINT32 index,LPCWSTR key,IXpsOMShareable *entry) {
    return This->lpVtbl->InsertAt(This,index,key,entry);
}
static inline HRESULT IXpsOMDictionary_RemoveAt(IXpsOMDictionary* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMDictionary_SetAt(IXpsOMDictionary* This,UINT32 index,LPCWSTR key,IXpsOMShareable *entry) {
    return This->lpVtbl->SetAt(This,index,key,entry);
}
static inline HRESULT IXpsOMDictionary_Clone(IXpsOMDictionary* This,IXpsOMDictionary **dictionary) {
    return This->lpVtbl->Clone(This,dictionary);
}
#endif
#endif

#endif


#endif  /* __IXpsOMDictionary_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPageReference interface
 */
#ifndef __IXpsOMPageReference_INTERFACE_DEFINED__
#define __IXpsOMPageReference_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPageReference, 0xed360180, 0x6f92, 0x4998, 0x89,0x0d, 0x2f,0x20,0x85,0x31,0xa0,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ed360180-6f92-4998-890d-2f208531a0a0")
IXpsOMPageReference : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMDocument **document) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPage(
        IXpsOMPage **page) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPage(
        IXpsOMPage *page) = 0;

    virtual HRESULT STDMETHODCALLTYPE DiscardPage(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPageLoaded(
        WINBOOL *isPageLoaded) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdvisoryPageDimensions(
        XPS_SIZE *pageDimensions) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAdvisoryPageDimensions(
        const XPS_SIZE *pageDimensions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStoryFragmentsResource(
        IXpsOMStoryFragmentsResource **storyFragmentsResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStoryFragmentsResource(
        IXpsOMStoryFragmentsResource *storyFragmentsResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrintTicketResource(
        IXpsOMPrintTicketResource **printTicketResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrintTicketResource(
        IXpsOMPrintTicketResource *printTicketResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetThumbnailResource(
        IXpsOMImageResource **imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetThumbnailResource(
        IXpsOMImageResource *imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CollectLinkTargets(
        IXpsOMNameCollection **linkTargets) = 0;

    virtual HRESULT STDMETHODCALLTYPE CollectPartResources(
        IXpsOMPartResources **partResources) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasRestrictedFonts(
        WINBOOL *restrictedFonts) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMPageReference **pageReference) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPageReference, 0xed360180, 0x6f92, 0x4998, 0x89,0x0d, 0x2f,0x20,0x85,0x31,0xa0,0xa0)
#endif
#else
typedef struct IXpsOMPageReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPageReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPageReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPageReference *This);

    /*** IXpsOMPageReference methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMPageReference *This,
        IXpsOMDocument **document);

    HRESULT (STDMETHODCALLTYPE *GetPage)(
        IXpsOMPageReference *This,
        IXpsOMPage **page);

    HRESULT (STDMETHODCALLTYPE *SetPage)(
        IXpsOMPageReference *This,
        IXpsOMPage *page);

    HRESULT (STDMETHODCALLTYPE *DiscardPage)(
        IXpsOMPageReference *This);

    HRESULT (STDMETHODCALLTYPE *IsPageLoaded)(
        IXpsOMPageReference *This,
        WINBOOL *isPageLoaded);

    HRESULT (STDMETHODCALLTYPE *GetAdvisoryPageDimensions)(
        IXpsOMPageReference *This,
        XPS_SIZE *pageDimensions);

    HRESULT (STDMETHODCALLTYPE *SetAdvisoryPageDimensions)(
        IXpsOMPageReference *This,
        const XPS_SIZE *pageDimensions);

    HRESULT (STDMETHODCALLTYPE *GetStoryFragmentsResource)(
        IXpsOMPageReference *This,
        IXpsOMStoryFragmentsResource **storyFragmentsResource);

    HRESULT (STDMETHODCALLTYPE *SetStoryFragmentsResource)(
        IXpsOMPageReference *This,
        IXpsOMStoryFragmentsResource *storyFragmentsResource);

    HRESULT (STDMETHODCALLTYPE *GetPrintTicketResource)(
        IXpsOMPageReference *This,
        IXpsOMPrintTicketResource **printTicketResource);

    HRESULT (STDMETHODCALLTYPE *SetPrintTicketResource)(
        IXpsOMPageReference *This,
        IXpsOMPrintTicketResource *printTicketResource);

    HRESULT (STDMETHODCALLTYPE *GetThumbnailResource)(
        IXpsOMPageReference *This,
        IXpsOMImageResource **imageResource);

    HRESULT (STDMETHODCALLTYPE *SetThumbnailResource)(
        IXpsOMPageReference *This,
        IXpsOMImageResource *imageResource);

    HRESULT (STDMETHODCALLTYPE *CollectLinkTargets)(
        IXpsOMPageReference *This,
        IXpsOMNameCollection **linkTargets);

    HRESULT (STDMETHODCALLTYPE *CollectPartResources)(
        IXpsOMPageReference *This,
        IXpsOMPartResources **partResources);

    HRESULT (STDMETHODCALLTYPE *HasRestrictedFonts)(
        IXpsOMPageReference *This,
        WINBOOL *restrictedFonts);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMPageReference *This,
        IXpsOMPageReference **pageReference);

    END_INTERFACE
} IXpsOMPageReferenceVtbl;

interface IXpsOMPageReference {
    CONST_VTBL IXpsOMPageReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPageReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPageReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPageReference_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPageReference methods ***/
#define IXpsOMPageReference_GetOwner(This,document) (This)->lpVtbl->GetOwner(This,document)
#define IXpsOMPageReference_GetPage(This,page) (This)->lpVtbl->GetPage(This,page)
#define IXpsOMPageReference_SetPage(This,page) (This)->lpVtbl->SetPage(This,page)
#define IXpsOMPageReference_DiscardPage(This) (This)->lpVtbl->DiscardPage(This)
#define IXpsOMPageReference_IsPageLoaded(This,isPageLoaded) (This)->lpVtbl->IsPageLoaded(This,isPageLoaded)
#define IXpsOMPageReference_GetAdvisoryPageDimensions(This,pageDimensions) (This)->lpVtbl->GetAdvisoryPageDimensions(This,pageDimensions)
#define IXpsOMPageReference_SetAdvisoryPageDimensions(This,pageDimensions) (This)->lpVtbl->SetAdvisoryPageDimensions(This,pageDimensions)
#define IXpsOMPageReference_GetStoryFragmentsResource(This,storyFragmentsResource) (This)->lpVtbl->GetStoryFragmentsResource(This,storyFragmentsResource)
#define IXpsOMPageReference_SetStoryFragmentsResource(This,storyFragmentsResource) (This)->lpVtbl->SetStoryFragmentsResource(This,storyFragmentsResource)
#define IXpsOMPageReference_GetPrintTicketResource(This,printTicketResource) (This)->lpVtbl->GetPrintTicketResource(This,printTicketResource)
#define IXpsOMPageReference_SetPrintTicketResource(This,printTicketResource) (This)->lpVtbl->SetPrintTicketResource(This,printTicketResource)
#define IXpsOMPageReference_GetThumbnailResource(This,imageResource) (This)->lpVtbl->GetThumbnailResource(This,imageResource)
#define IXpsOMPageReference_SetThumbnailResource(This,imageResource) (This)->lpVtbl->SetThumbnailResource(This,imageResource)
#define IXpsOMPageReference_CollectLinkTargets(This,linkTargets) (This)->lpVtbl->CollectLinkTargets(This,linkTargets)
#define IXpsOMPageReference_CollectPartResources(This,partResources) (This)->lpVtbl->CollectPartResources(This,partResources)
#define IXpsOMPageReference_HasRestrictedFonts(This,restrictedFonts) (This)->lpVtbl->HasRestrictedFonts(This,restrictedFonts)
#define IXpsOMPageReference_Clone(This,pageReference) (This)->lpVtbl->Clone(This,pageReference)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPageReference_QueryInterface(IXpsOMPageReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPageReference_AddRef(IXpsOMPageReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPageReference_Release(IXpsOMPageReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPageReference methods ***/
static inline HRESULT IXpsOMPageReference_GetOwner(IXpsOMPageReference* This,IXpsOMDocument **document) {
    return This->lpVtbl->GetOwner(This,document);
}
static inline HRESULT IXpsOMPageReference_GetPage(IXpsOMPageReference* This,IXpsOMPage **page) {
    return This->lpVtbl->GetPage(This,page);
}
static inline HRESULT IXpsOMPageReference_SetPage(IXpsOMPageReference* This,IXpsOMPage *page) {
    return This->lpVtbl->SetPage(This,page);
}
static inline HRESULT IXpsOMPageReference_DiscardPage(IXpsOMPageReference* This) {
    return This->lpVtbl->DiscardPage(This);
}
static inline HRESULT IXpsOMPageReference_IsPageLoaded(IXpsOMPageReference* This,WINBOOL *isPageLoaded) {
    return This->lpVtbl->IsPageLoaded(This,isPageLoaded);
}
static inline HRESULT IXpsOMPageReference_GetAdvisoryPageDimensions(IXpsOMPageReference* This,XPS_SIZE *pageDimensions) {
    return This->lpVtbl->GetAdvisoryPageDimensions(This,pageDimensions);
}
static inline HRESULT IXpsOMPageReference_SetAdvisoryPageDimensions(IXpsOMPageReference* This,const XPS_SIZE *pageDimensions) {
    return This->lpVtbl->SetAdvisoryPageDimensions(This,pageDimensions);
}
static inline HRESULT IXpsOMPageReference_GetStoryFragmentsResource(IXpsOMPageReference* This,IXpsOMStoryFragmentsResource **storyFragmentsResource) {
    return This->lpVtbl->GetStoryFragmentsResource(This,storyFragmentsResource);
}
static inline HRESULT IXpsOMPageReference_SetStoryFragmentsResource(IXpsOMPageReference* This,IXpsOMStoryFragmentsResource *storyFragmentsResource) {
    return This->lpVtbl->SetStoryFragmentsResource(This,storyFragmentsResource);
}
static inline HRESULT IXpsOMPageReference_GetPrintTicketResource(IXpsOMPageReference* This,IXpsOMPrintTicketResource **printTicketResource) {
    return This->lpVtbl->GetPrintTicketResource(This,printTicketResource);
}
static inline HRESULT IXpsOMPageReference_SetPrintTicketResource(IXpsOMPageReference* This,IXpsOMPrintTicketResource *printTicketResource) {
    return This->lpVtbl->SetPrintTicketResource(This,printTicketResource);
}
static inline HRESULT IXpsOMPageReference_GetThumbnailResource(IXpsOMPageReference* This,IXpsOMImageResource **imageResource) {
    return This->lpVtbl->GetThumbnailResource(This,imageResource);
}
static inline HRESULT IXpsOMPageReference_SetThumbnailResource(IXpsOMPageReference* This,IXpsOMImageResource *imageResource) {
    return This->lpVtbl->SetThumbnailResource(This,imageResource);
}
static inline HRESULT IXpsOMPageReference_CollectLinkTargets(IXpsOMPageReference* This,IXpsOMNameCollection **linkTargets) {
    return This->lpVtbl->CollectLinkTargets(This,linkTargets);
}
static inline HRESULT IXpsOMPageReference_CollectPartResources(IXpsOMPageReference* This,IXpsOMPartResources **partResources) {
    return This->lpVtbl->CollectPartResources(This,partResources);
}
static inline HRESULT IXpsOMPageReference_HasRestrictedFonts(IXpsOMPageReference* This,WINBOOL *restrictedFonts) {
    return This->lpVtbl->HasRestrictedFonts(This,restrictedFonts);
}
static inline HRESULT IXpsOMPageReference_Clone(IXpsOMPageReference* This,IXpsOMPageReference **pageReference) {
    return This->lpVtbl->Clone(This,pageReference);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPageReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPageReferenceCollection interface
 */
#ifndef __IXpsOMPageReferenceCollection_INTERFACE_DEFINED__
#define __IXpsOMPageReferenceCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPageReferenceCollection, 0xca16ba4d, 0xe7b9, 0x45c5, 0x95,0x8b, 0xf9,0x80,0x22,0x47,0x37,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ca16ba4d-e7b9-45c5-958b-f98022473745")
IXpsOMPageReferenceCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMPageReference **pageReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMPageReference *pageReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMPageReference *pageReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMPageReference *pageReference) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPageReferenceCollection, 0xca16ba4d, 0xe7b9, 0x45c5, 0x95,0x8b, 0xf9,0x80,0x22,0x47,0x37,0x45)
#endif
#else
typedef struct IXpsOMPageReferenceCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPageReferenceCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPageReferenceCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPageReferenceCollection *This);

    /*** IXpsOMPageReferenceCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMPageReferenceCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMPageReferenceCollection *This,
        UINT32 index,
        IXpsOMPageReference **pageReference);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMPageReferenceCollection *This,
        UINT32 index,
        IXpsOMPageReference *pageReference);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMPageReferenceCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMPageReferenceCollection *This,
        UINT32 index,
        IXpsOMPageReference *pageReference);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMPageReferenceCollection *This,
        IXpsOMPageReference *pageReference);

    END_INTERFACE
} IXpsOMPageReferenceCollectionVtbl;

interface IXpsOMPageReferenceCollection {
    CONST_VTBL IXpsOMPageReferenceCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPageReferenceCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPageReferenceCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPageReferenceCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPageReferenceCollection methods ***/
#define IXpsOMPageReferenceCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMPageReferenceCollection_GetAt(This,index,pageReference) (This)->lpVtbl->GetAt(This,index,pageReference)
#define IXpsOMPageReferenceCollection_InsertAt(This,index,pageReference) (This)->lpVtbl->InsertAt(This,index,pageReference)
#define IXpsOMPageReferenceCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMPageReferenceCollection_SetAt(This,index,pageReference) (This)->lpVtbl->SetAt(This,index,pageReference)
#define IXpsOMPageReferenceCollection_Append(This,pageReference) (This)->lpVtbl->Append(This,pageReference)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPageReferenceCollection_QueryInterface(IXpsOMPageReferenceCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPageReferenceCollection_AddRef(IXpsOMPageReferenceCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPageReferenceCollection_Release(IXpsOMPageReferenceCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPageReferenceCollection methods ***/
static inline HRESULT IXpsOMPageReferenceCollection_GetCount(IXpsOMPageReferenceCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMPageReferenceCollection_GetAt(IXpsOMPageReferenceCollection* This,UINT32 index,IXpsOMPageReference **pageReference) {
    return This->lpVtbl->GetAt(This,index,pageReference);
}
static inline HRESULT IXpsOMPageReferenceCollection_InsertAt(IXpsOMPageReferenceCollection* This,UINT32 index,IXpsOMPageReference *pageReference) {
    return This->lpVtbl->InsertAt(This,index,pageReference);
}
static inline HRESULT IXpsOMPageReferenceCollection_RemoveAt(IXpsOMPageReferenceCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMPageReferenceCollection_SetAt(IXpsOMPageReferenceCollection* This,UINT32 index,IXpsOMPageReference *pageReference) {
    return This->lpVtbl->SetAt(This,index,pageReference);
}
static inline HRESULT IXpsOMPageReferenceCollection_Append(IXpsOMPageReferenceCollection* This,IXpsOMPageReference *pageReference) {
    return This->lpVtbl->Append(This,pageReference);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPageReferenceCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMDocumentCollection interface
 */
#ifndef __IXpsOMDocumentCollection_INTERFACE_DEFINED__
#define __IXpsOMDocumentCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMDocumentCollection, 0xd1c87f0d, 0xe947, 0x4754, 0x8a,0x25, 0x97,0x14,0x78,0xf7,0xe8,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d1c87f0d-e947-4754-8a25-971478f7e83e")
IXpsOMDocumentCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsOMDocument **document) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IXpsOMDocument *document) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IXpsOMDocument *document) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IXpsOMDocument *document) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMDocumentCollection, 0xd1c87f0d, 0xe947, 0x4754, 0x8a,0x25, 0x97,0x14,0x78,0xf7,0xe8,0x3e)
#endif
#else
typedef struct IXpsOMDocumentCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMDocumentCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMDocumentCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMDocumentCollection *This);

    /*** IXpsOMDocumentCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMDocumentCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMDocumentCollection *This,
        UINT32 index,
        IXpsOMDocument **document);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMDocumentCollection *This,
        UINT32 index,
        IXpsOMDocument *document);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMDocumentCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMDocumentCollection *This,
        UINT32 index,
        IXpsOMDocument *document);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMDocumentCollection *This,
        IXpsOMDocument *document);

    END_INTERFACE
} IXpsOMDocumentCollectionVtbl;

interface IXpsOMDocumentCollection {
    CONST_VTBL IXpsOMDocumentCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMDocumentCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMDocumentCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMDocumentCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMDocumentCollection methods ***/
#define IXpsOMDocumentCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMDocumentCollection_GetAt(This,index,document) (This)->lpVtbl->GetAt(This,index,document)
#define IXpsOMDocumentCollection_InsertAt(This,index,document) (This)->lpVtbl->InsertAt(This,index,document)
#define IXpsOMDocumentCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMDocumentCollection_SetAt(This,index,document) (This)->lpVtbl->SetAt(This,index,document)
#define IXpsOMDocumentCollection_Append(This,document) (This)->lpVtbl->Append(This,document)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMDocumentCollection_QueryInterface(IXpsOMDocumentCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMDocumentCollection_AddRef(IXpsOMDocumentCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMDocumentCollection_Release(IXpsOMDocumentCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMDocumentCollection methods ***/
static inline HRESULT IXpsOMDocumentCollection_GetCount(IXpsOMDocumentCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMDocumentCollection_GetAt(IXpsOMDocumentCollection* This,UINT32 index,IXpsOMDocument **document) {
    return This->lpVtbl->GetAt(This,index,document);
}
static inline HRESULT IXpsOMDocumentCollection_InsertAt(IXpsOMDocumentCollection* This,UINT32 index,IXpsOMDocument *document) {
    return This->lpVtbl->InsertAt(This,index,document);
}
static inline HRESULT IXpsOMDocumentCollection_RemoveAt(IXpsOMDocumentCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMDocumentCollection_SetAt(IXpsOMDocumentCollection* This,UINT32 index,IXpsOMDocument *document) {
    return This->lpVtbl->SetAt(This,index,document);
}
static inline HRESULT IXpsOMDocumentCollection_Append(IXpsOMDocumentCollection* This,IXpsOMDocument *document) {
    return This->lpVtbl->Append(This,document);
}
#endif
#endif

#endif


#endif  /* __IXpsOMDocumentCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPackage interface
 */
#ifndef __IXpsOMPackage_INTERFACE_DEFINED__
#define __IXpsOMPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPackage, 0x18c3df65, 0x81e1, 0x4674, 0x91,0xdc, 0xfc,0x45,0x2f,0x5a,0x41,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("18c3df65-81e1-4674-91dc-fc452f5a416f")
IXpsOMPackage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentSequence(
        IXpsOMDocumentSequence **documentSequence) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDocumentSequence(
        IXpsOMDocumentSequence *documentSequence) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCoreProperties(
        IXpsOMCoreProperties **coreProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCoreProperties(
        IXpsOMCoreProperties *coreProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDiscardControlPartName(
        IOpcPartUri **discardControlPartUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDiscardControlPartName(
        IOpcPartUri *discardControlPartUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetThumbnailResource(
        IXpsOMImageResource **imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetThumbnailResource(
        IXpsOMImageResource *imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteToFile(
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteToStream(
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPackage, 0x18c3df65, 0x81e1, 0x4674, 0x91,0xdc, 0xfc,0x45,0x2f,0x5a,0x41,0x6f)
#endif
#else
typedef struct IXpsOMPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPackage *This);

    /*** IXpsOMPackage methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentSequence)(
        IXpsOMPackage *This,
        IXpsOMDocumentSequence **documentSequence);

    HRESULT (STDMETHODCALLTYPE *SetDocumentSequence)(
        IXpsOMPackage *This,
        IXpsOMDocumentSequence *documentSequence);

    HRESULT (STDMETHODCALLTYPE *GetCoreProperties)(
        IXpsOMPackage *This,
        IXpsOMCoreProperties **coreProperties);

    HRESULT (STDMETHODCALLTYPE *SetCoreProperties)(
        IXpsOMPackage *This,
        IXpsOMCoreProperties *coreProperties);

    HRESULT (STDMETHODCALLTYPE *GetDiscardControlPartName)(
        IXpsOMPackage *This,
        IOpcPartUri **discardControlPartUri);

    HRESULT (STDMETHODCALLTYPE *SetDiscardControlPartName)(
        IXpsOMPackage *This,
        IOpcPartUri *discardControlPartUri);

    HRESULT (STDMETHODCALLTYPE *GetThumbnailResource)(
        IXpsOMPackage *This,
        IXpsOMImageResource **imageResource);

    HRESULT (STDMETHODCALLTYPE *SetThumbnailResource)(
        IXpsOMPackage *This,
        IXpsOMImageResource *imageResource);

    HRESULT (STDMETHODCALLTYPE *WriteToFile)(
        IXpsOMPackage *This,
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize);

    HRESULT (STDMETHODCALLTYPE *WriteToStream)(
        IXpsOMPackage *This,
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize);

    END_INTERFACE
} IXpsOMPackageVtbl;

interface IXpsOMPackage {
    CONST_VTBL IXpsOMPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPackage methods ***/
#define IXpsOMPackage_GetDocumentSequence(This,documentSequence) (This)->lpVtbl->GetDocumentSequence(This,documentSequence)
#define IXpsOMPackage_SetDocumentSequence(This,documentSequence) (This)->lpVtbl->SetDocumentSequence(This,documentSequence)
#define IXpsOMPackage_GetCoreProperties(This,coreProperties) (This)->lpVtbl->GetCoreProperties(This,coreProperties)
#define IXpsOMPackage_SetCoreProperties(This,coreProperties) (This)->lpVtbl->SetCoreProperties(This,coreProperties)
#define IXpsOMPackage_GetDiscardControlPartName(This,discardControlPartUri) (This)->lpVtbl->GetDiscardControlPartName(This,discardControlPartUri)
#define IXpsOMPackage_SetDiscardControlPartName(This,discardControlPartUri) (This)->lpVtbl->SetDiscardControlPartName(This,discardControlPartUri)
#define IXpsOMPackage_GetThumbnailResource(This,imageResource) (This)->lpVtbl->GetThumbnailResource(This,imageResource)
#define IXpsOMPackage_SetThumbnailResource(This,imageResource) (This)->lpVtbl->SetThumbnailResource(This,imageResource)
#define IXpsOMPackage_WriteToFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize) (This)->lpVtbl->WriteToFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize)
#define IXpsOMPackage_WriteToStream(This,stream,optimizeMarkupSize) (This)->lpVtbl->WriteToStream(This,stream,optimizeMarkupSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPackage_QueryInterface(IXpsOMPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPackage_AddRef(IXpsOMPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPackage_Release(IXpsOMPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPackage methods ***/
static inline HRESULT IXpsOMPackage_GetDocumentSequence(IXpsOMPackage* This,IXpsOMDocumentSequence **documentSequence) {
    return This->lpVtbl->GetDocumentSequence(This,documentSequence);
}
static inline HRESULT IXpsOMPackage_SetDocumentSequence(IXpsOMPackage* This,IXpsOMDocumentSequence *documentSequence) {
    return This->lpVtbl->SetDocumentSequence(This,documentSequence);
}
static inline HRESULT IXpsOMPackage_GetCoreProperties(IXpsOMPackage* This,IXpsOMCoreProperties **coreProperties) {
    return This->lpVtbl->GetCoreProperties(This,coreProperties);
}
static inline HRESULT IXpsOMPackage_SetCoreProperties(IXpsOMPackage* This,IXpsOMCoreProperties *coreProperties) {
    return This->lpVtbl->SetCoreProperties(This,coreProperties);
}
static inline HRESULT IXpsOMPackage_GetDiscardControlPartName(IXpsOMPackage* This,IOpcPartUri **discardControlPartUri) {
    return This->lpVtbl->GetDiscardControlPartName(This,discardControlPartUri);
}
static inline HRESULT IXpsOMPackage_SetDiscardControlPartName(IXpsOMPackage* This,IOpcPartUri *discardControlPartUri) {
    return This->lpVtbl->SetDiscardControlPartName(This,discardControlPartUri);
}
static inline HRESULT IXpsOMPackage_GetThumbnailResource(IXpsOMPackage* This,IXpsOMImageResource **imageResource) {
    return This->lpVtbl->GetThumbnailResource(This,imageResource);
}
static inline HRESULT IXpsOMPackage_SetThumbnailResource(IXpsOMPackage* This,IXpsOMImageResource *imageResource) {
    return This->lpVtbl->SetThumbnailResource(This,imageResource);
}
static inline HRESULT IXpsOMPackage_WriteToFile(IXpsOMPackage* This,LPCWSTR fileName,LPSECURITY_ATTRIBUTES securityAttributes,DWORD flagsAndAttributes,WINBOOL optimizeMarkupSize) {
    return This->lpVtbl->WriteToFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize);
}
static inline HRESULT IXpsOMPackage_WriteToStream(IXpsOMPackage* This,ISequentialStream *stream,WINBOOL optimizeMarkupSize) {
    return This->lpVtbl->WriteToStream(This,stream,optimizeMarkupSize);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPackage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMObjectFactory interface
 */
#ifndef __IXpsOMObjectFactory_INTERFACE_DEFINED__
#define __IXpsOMObjectFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMObjectFactory, 0xf9b2a685, 0xa50d, 0x4fc2, 0xb7,0x64, 0xb5,0x6e,0x09,0x3e,0xa0,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f9b2a685-a50d-4fc2-b764-b56e093ea0ca")
IXpsOMObjectFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreatePackage(
        IXpsOMPackage **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageFromFile(
        LPCWSTR filename,
        WINBOOL reuseObjects,
        IXpsOMPackage **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageFromStream(
        IStream *stream,
        WINBOOL reuseObjects,
        IXpsOMPackage **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStoryFragmentsResource(
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMStoryFragmentsResource **storyFragmentsResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDocumentStructureResource(
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMDocumentStructureResource **documentStructureResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSignatureBlockResource(
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMSignatureBlockResource **signatureBlockResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRemoteDictionaryResource(
        IXpsOMDictionary *dictionary,
        IOpcPartUri *partUri,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRemoteDictionaryResourceFromStream(
        IStream *dictionaryMarkupStream,
        IOpcPartUri *dictionaryPartUri,
        IXpsOMPartResources *resources,
        IXpsOMRemoteDictionaryResource **dictionaryResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePartResources(
        IXpsOMPartResources **partResources) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDocumentSequence(
        IOpcPartUri *partUri,
        IXpsOMDocumentSequence **documentSequence) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDocument(
        IOpcPartUri *partUri,
        IXpsOMDocument **document) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePageReference(
        const XPS_SIZE *advisoryPageDimensions,
        IXpsOMPageReference **pageReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePage(
        const XPS_SIZE *pageDimensions,
        LPCWSTR language,
        IOpcPartUri *partUri,
        IXpsOMPage **page) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePageFromStream(
        IStream *pageMarkupStream,
        IOpcPartUri *partUri,
        IXpsOMPartResources *resources,
        WINBOOL reuseObjects,
        IXpsOMPage **page) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCanvas(
        IXpsOMCanvas **canvas) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGlyphs(
        IXpsOMFontResource *fontResource,
        IXpsOMGlyphs **glyphs) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePath(
        IXpsOMPath **path) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGeometry(
        IXpsOMGeometry **geometry) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGeometryFigure(
        const XPS_POINT *startPoint,
        IXpsOMGeometryFigure **figure) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMatrixTransform(
        const XPS_MATRIX *matrix,
        IXpsOMMatrixTransform **transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSolidColorBrush(
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile,
        IXpsOMSolidColorBrush **solidColorBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateColorProfileResource(
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMColorProfileResource **colorProfileResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateImageBrush(
        IXpsOMImageResource *image,
        const XPS_RECT *viewBox,
        const XPS_RECT *viewPort,
        IXpsOMImageBrush **imageBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVisualBrush(
        const XPS_RECT *viewBox,
        const XPS_RECT *viewPort,
        IXpsOMVisualBrush **visualBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateImageResource(
        IStream *acquiredStream,
        XPS_IMAGE_TYPE contentType,
        IOpcPartUri *partUri,
        IXpsOMImageResource **imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePrintTicketResource(
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMPrintTicketResource **printTicketResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFontResource(
        IStream *acquiredStream,
        XPS_FONT_EMBEDDING fontEmbedding,
        IOpcPartUri *partUri,
        WINBOOL isObfSourceStream,
        IXpsOMFontResource **fontResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGradientStop(
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile,
        FLOAT offset,
        IXpsOMGradientStop **gradientStop) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateLinearGradientBrush(
        IXpsOMGradientStop *gradStop1,
        IXpsOMGradientStop *gradStop2,
        const XPS_POINT *startPoint,
        const XPS_POINT *endPoint,
        IXpsOMLinearGradientBrush **linearGradientBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRadialGradientBrush(
        IXpsOMGradientStop *gradStop1,
        IXpsOMGradientStop *gradStop2,
        const XPS_POINT *centerPoint,
        const XPS_POINT *gradientOrigin,
        const XPS_SIZE *radiiSizes,
        IXpsOMRadialGradientBrush **radialGradientBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCoreProperties(
        IOpcPartUri *partUri,
        IXpsOMCoreProperties **coreProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDictionary(
        IXpsOMDictionary **dictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePartUriCollection(
        IXpsOMPartUriCollection **partUriCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageWriterOnFile(
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageWriterOnStream(
        ISequentialStream *outputStream,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePartUri(
        LPCWSTR uri,
        IOpcPartUri **partUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateReadOnlyStreamOnFile(
        LPCWSTR filename,
        IStream **stream) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMObjectFactory, 0xf9b2a685, 0xa50d, 0x4fc2, 0xb7,0x64, 0xb5,0x6e,0x09,0x3e,0xa0,0xca)
#endif
#else
typedef struct IXpsOMObjectFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMObjectFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMObjectFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMObjectFactory *This);

    /*** IXpsOMObjectFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreatePackage)(
        IXpsOMObjectFactory *This,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *CreatePackageFromFile)(
        IXpsOMObjectFactory *This,
        LPCWSTR filename,
        WINBOOL reuseObjects,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *CreatePackageFromStream)(
        IXpsOMObjectFactory *This,
        IStream *stream,
        WINBOOL reuseObjects,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *CreateStoryFragmentsResource)(
        IXpsOMObjectFactory *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMStoryFragmentsResource **storyFragmentsResource);

    HRESULT (STDMETHODCALLTYPE *CreateDocumentStructureResource)(
        IXpsOMObjectFactory *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMDocumentStructureResource **documentStructureResource);

    HRESULT (STDMETHODCALLTYPE *CreateSignatureBlockResource)(
        IXpsOMObjectFactory *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMSignatureBlockResource **signatureBlockResource);

    HRESULT (STDMETHODCALLTYPE *CreateRemoteDictionaryResource)(
        IXpsOMObjectFactory *This,
        IXpsOMDictionary *dictionary,
        IOpcPartUri *partUri,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *CreateRemoteDictionaryResourceFromStream)(
        IXpsOMObjectFactory *This,
        IStream *dictionaryMarkupStream,
        IOpcPartUri *dictionaryPartUri,
        IXpsOMPartResources *resources,
        IXpsOMRemoteDictionaryResource **dictionaryResource);

    HRESULT (STDMETHODCALLTYPE *CreatePartResources)(
        IXpsOMObjectFactory *This,
        IXpsOMPartResources **partResources);

    HRESULT (STDMETHODCALLTYPE *CreateDocumentSequence)(
        IXpsOMObjectFactory *This,
        IOpcPartUri *partUri,
        IXpsOMDocumentSequence **documentSequence);

    HRESULT (STDMETHODCALLTYPE *CreateDocument)(
        IXpsOMObjectFactory *This,
        IOpcPartUri *partUri,
        IXpsOMDocument **document);

    HRESULT (STDMETHODCALLTYPE *CreatePageReference)(
        IXpsOMObjectFactory *This,
        const XPS_SIZE *advisoryPageDimensions,
        IXpsOMPageReference **pageReference);

    HRESULT (STDMETHODCALLTYPE *CreatePage)(
        IXpsOMObjectFactory *This,
        const XPS_SIZE *pageDimensions,
        LPCWSTR language,
        IOpcPartUri *partUri,
        IXpsOMPage **page);

    HRESULT (STDMETHODCALLTYPE *CreatePageFromStream)(
        IXpsOMObjectFactory *This,
        IStream *pageMarkupStream,
        IOpcPartUri *partUri,
        IXpsOMPartResources *resources,
        WINBOOL reuseObjects,
        IXpsOMPage **page);

    HRESULT (STDMETHODCALLTYPE *CreateCanvas)(
        IXpsOMObjectFactory *This,
        IXpsOMCanvas **canvas);

    HRESULT (STDMETHODCALLTYPE *CreateGlyphs)(
        IXpsOMObjectFactory *This,
        IXpsOMFontResource *fontResource,
        IXpsOMGlyphs **glyphs);

    HRESULT (STDMETHODCALLTYPE *CreatePath)(
        IXpsOMObjectFactory *This,
        IXpsOMPath **path);

    HRESULT (STDMETHODCALLTYPE *CreateGeometry)(
        IXpsOMObjectFactory *This,
        IXpsOMGeometry **geometry);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryFigure)(
        IXpsOMObjectFactory *This,
        const XPS_POINT *startPoint,
        IXpsOMGeometryFigure **figure);

    HRESULT (STDMETHODCALLTYPE *CreateMatrixTransform)(
        IXpsOMObjectFactory *This,
        const XPS_MATRIX *matrix,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *CreateSolidColorBrush)(
        IXpsOMObjectFactory *This,
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile,
        IXpsOMSolidColorBrush **solidColorBrush);

    HRESULT (STDMETHODCALLTYPE *CreateColorProfileResource)(
        IXpsOMObjectFactory *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMColorProfileResource **colorProfileResource);

    HRESULT (STDMETHODCALLTYPE *CreateImageBrush)(
        IXpsOMObjectFactory *This,
        IXpsOMImageResource *image,
        const XPS_RECT *viewBox,
        const XPS_RECT *viewPort,
        IXpsOMImageBrush **imageBrush);

    HRESULT (STDMETHODCALLTYPE *CreateVisualBrush)(
        IXpsOMObjectFactory *This,
        const XPS_RECT *viewBox,
        const XPS_RECT *viewPort,
        IXpsOMVisualBrush **visualBrush);

    HRESULT (STDMETHODCALLTYPE *CreateImageResource)(
        IXpsOMObjectFactory *This,
        IStream *acquiredStream,
        XPS_IMAGE_TYPE contentType,
        IOpcPartUri *partUri,
        IXpsOMImageResource **imageResource);

    HRESULT (STDMETHODCALLTYPE *CreatePrintTicketResource)(
        IXpsOMObjectFactory *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMPrintTicketResource **printTicketResource);

    HRESULT (STDMETHODCALLTYPE *CreateFontResource)(
        IXpsOMObjectFactory *This,
        IStream *acquiredStream,
        XPS_FONT_EMBEDDING fontEmbedding,
        IOpcPartUri *partUri,
        WINBOOL isObfSourceStream,
        IXpsOMFontResource **fontResource);

    HRESULT (STDMETHODCALLTYPE *CreateGradientStop)(
        IXpsOMObjectFactory *This,
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile,
        FLOAT offset,
        IXpsOMGradientStop **gradientStop);

    HRESULT (STDMETHODCALLTYPE *CreateLinearGradientBrush)(
        IXpsOMObjectFactory *This,
        IXpsOMGradientStop *gradStop1,
        IXpsOMGradientStop *gradStop2,
        const XPS_POINT *startPoint,
        const XPS_POINT *endPoint,
        IXpsOMLinearGradientBrush **linearGradientBrush);

    HRESULT (STDMETHODCALLTYPE *CreateRadialGradientBrush)(
        IXpsOMObjectFactory *This,
        IXpsOMGradientStop *gradStop1,
        IXpsOMGradientStop *gradStop2,
        const XPS_POINT *centerPoint,
        const XPS_POINT *gradientOrigin,
        const XPS_SIZE *radiiSizes,
        IXpsOMRadialGradientBrush **radialGradientBrush);

    HRESULT (STDMETHODCALLTYPE *CreateCoreProperties)(
        IXpsOMObjectFactory *This,
        IOpcPartUri *partUri,
        IXpsOMCoreProperties **coreProperties);

    HRESULT (STDMETHODCALLTYPE *CreateDictionary)(
        IXpsOMObjectFactory *This,
        IXpsOMDictionary **dictionary);

    HRESULT (STDMETHODCALLTYPE *CreatePartUriCollection)(
        IXpsOMObjectFactory *This,
        IXpsOMPartUriCollection **partUriCollection);

    HRESULT (STDMETHODCALLTYPE *CreatePackageWriterOnFile)(
        IXpsOMObjectFactory *This,
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter);

    HRESULT (STDMETHODCALLTYPE *CreatePackageWriterOnStream)(
        IXpsOMObjectFactory *This,
        ISequentialStream *outputStream,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter);

    HRESULT (STDMETHODCALLTYPE *CreatePartUri)(
        IXpsOMObjectFactory *This,
        LPCWSTR uri,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *CreateReadOnlyStreamOnFile)(
        IXpsOMObjectFactory *This,
        LPCWSTR filename,
        IStream **stream);

    END_INTERFACE
} IXpsOMObjectFactoryVtbl;

interface IXpsOMObjectFactory {
    CONST_VTBL IXpsOMObjectFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMObjectFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMObjectFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMObjectFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMObjectFactory methods ***/
#define IXpsOMObjectFactory_CreatePackage(This,package) (This)->lpVtbl->CreatePackage(This,package)
#define IXpsOMObjectFactory_CreatePackageFromFile(This,filename,reuseObjects,package) (This)->lpVtbl->CreatePackageFromFile(This,filename,reuseObjects,package)
#define IXpsOMObjectFactory_CreatePackageFromStream(This,stream,reuseObjects,package) (This)->lpVtbl->CreatePackageFromStream(This,stream,reuseObjects,package)
#define IXpsOMObjectFactory_CreateStoryFragmentsResource(This,acquiredStream,partUri,storyFragmentsResource) (This)->lpVtbl->CreateStoryFragmentsResource(This,acquiredStream,partUri,storyFragmentsResource)
#define IXpsOMObjectFactory_CreateDocumentStructureResource(This,acquiredStream,partUri,documentStructureResource) (This)->lpVtbl->CreateDocumentStructureResource(This,acquiredStream,partUri,documentStructureResource)
#define IXpsOMObjectFactory_CreateSignatureBlockResource(This,acquiredStream,partUri,signatureBlockResource) (This)->lpVtbl->CreateSignatureBlockResource(This,acquiredStream,partUri,signatureBlockResource)
#define IXpsOMObjectFactory_CreateRemoteDictionaryResource(This,dictionary,partUri,remoteDictionaryResource) (This)->lpVtbl->CreateRemoteDictionaryResource(This,dictionary,partUri,remoteDictionaryResource)
#define IXpsOMObjectFactory_CreateRemoteDictionaryResourceFromStream(This,dictionaryMarkupStream,dictionaryPartUri,resources,dictionaryResource) (This)->lpVtbl->CreateRemoteDictionaryResourceFromStream(This,dictionaryMarkupStream,dictionaryPartUri,resources,dictionaryResource)
#define IXpsOMObjectFactory_CreatePartResources(This,partResources) (This)->lpVtbl->CreatePartResources(This,partResources)
#define IXpsOMObjectFactory_CreateDocumentSequence(This,partUri,documentSequence) (This)->lpVtbl->CreateDocumentSequence(This,partUri,documentSequence)
#define IXpsOMObjectFactory_CreateDocument(This,partUri,document) (This)->lpVtbl->CreateDocument(This,partUri,document)
#define IXpsOMObjectFactory_CreatePageReference(This,advisoryPageDimensions,pageReference) (This)->lpVtbl->CreatePageReference(This,advisoryPageDimensions,pageReference)
#define IXpsOMObjectFactory_CreatePage(This,pageDimensions,language,partUri,page) (This)->lpVtbl->CreatePage(This,pageDimensions,language,partUri,page)
#define IXpsOMObjectFactory_CreatePageFromStream(This,pageMarkupStream,partUri,resources,reuseObjects,page) (This)->lpVtbl->CreatePageFromStream(This,pageMarkupStream,partUri,resources,reuseObjects,page)
#define IXpsOMObjectFactory_CreateCanvas(This,canvas) (This)->lpVtbl->CreateCanvas(This,canvas)
#define IXpsOMObjectFactory_CreateGlyphs(This,fontResource,glyphs) (This)->lpVtbl->CreateGlyphs(This,fontResource,glyphs)
#define IXpsOMObjectFactory_CreatePath(This,path) (This)->lpVtbl->CreatePath(This,path)
#define IXpsOMObjectFactory_CreateGeometry(This,geometry) (This)->lpVtbl->CreateGeometry(This,geometry)
#define IXpsOMObjectFactory_CreateGeometryFigure(This,startPoint,figure) (This)->lpVtbl->CreateGeometryFigure(This,startPoint,figure)
#define IXpsOMObjectFactory_CreateMatrixTransform(This,matrix,transform) (This)->lpVtbl->CreateMatrixTransform(This,matrix,transform)
#define IXpsOMObjectFactory_CreateSolidColorBrush(This,color,colorProfile,solidColorBrush) (This)->lpVtbl->CreateSolidColorBrush(This,color,colorProfile,solidColorBrush)
#define IXpsOMObjectFactory_CreateColorProfileResource(This,acquiredStream,partUri,colorProfileResource) (This)->lpVtbl->CreateColorProfileResource(This,acquiredStream,partUri,colorProfileResource)
#define IXpsOMObjectFactory_CreateImageBrush(This,image,viewBox,viewPort,imageBrush) (This)->lpVtbl->CreateImageBrush(This,image,viewBox,viewPort,imageBrush)
#define IXpsOMObjectFactory_CreateVisualBrush(This,viewBox,viewPort,visualBrush) (This)->lpVtbl->CreateVisualBrush(This,viewBox,viewPort,visualBrush)
#define IXpsOMObjectFactory_CreateImageResource(This,acquiredStream,contentType,partUri,imageResource) (This)->lpVtbl->CreateImageResource(This,acquiredStream,contentType,partUri,imageResource)
#define IXpsOMObjectFactory_CreatePrintTicketResource(This,acquiredStream,partUri,printTicketResource) (This)->lpVtbl->CreatePrintTicketResource(This,acquiredStream,partUri,printTicketResource)
#define IXpsOMObjectFactory_CreateFontResource(This,acquiredStream,fontEmbedding,partUri,isObfSourceStream,fontResource) (This)->lpVtbl->CreateFontResource(This,acquiredStream,fontEmbedding,partUri,isObfSourceStream,fontResource)
#define IXpsOMObjectFactory_CreateGradientStop(This,color,colorProfile,offset,gradientStop) (This)->lpVtbl->CreateGradientStop(This,color,colorProfile,offset,gradientStop)
#define IXpsOMObjectFactory_CreateLinearGradientBrush(This,gradStop1,gradStop2,startPoint,endPoint,linearGradientBrush) (This)->lpVtbl->CreateLinearGradientBrush(This,gradStop1,gradStop2,startPoint,endPoint,linearGradientBrush)
#define IXpsOMObjectFactory_CreateRadialGradientBrush(This,gradStop1,gradStop2,centerPoint,gradientOrigin,radiiSizes,radialGradientBrush) (This)->lpVtbl->CreateRadialGradientBrush(This,gradStop1,gradStop2,centerPoint,gradientOrigin,radiiSizes,radialGradientBrush)
#define IXpsOMObjectFactory_CreateCoreProperties(This,partUri,coreProperties) (This)->lpVtbl->CreateCoreProperties(This,partUri,coreProperties)
#define IXpsOMObjectFactory_CreateDictionary(This,dictionary) (This)->lpVtbl->CreateDictionary(This,dictionary)
#define IXpsOMObjectFactory_CreatePartUriCollection(This,partUriCollection) (This)->lpVtbl->CreatePartUriCollection(This,partUriCollection)
#define IXpsOMObjectFactory_CreatePackageWriterOnFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter) (This)->lpVtbl->CreatePackageWriterOnFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter)
#define IXpsOMObjectFactory_CreatePackageWriterOnStream(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter) (This)->lpVtbl->CreatePackageWriterOnStream(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter)
#define IXpsOMObjectFactory_CreatePartUri(This,uri,partUri) (This)->lpVtbl->CreatePartUri(This,uri,partUri)
#define IXpsOMObjectFactory_CreateReadOnlyStreamOnFile(This,filename,stream) (This)->lpVtbl->CreateReadOnlyStreamOnFile(This,filename,stream)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMObjectFactory_QueryInterface(IXpsOMObjectFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMObjectFactory_AddRef(IXpsOMObjectFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMObjectFactory_Release(IXpsOMObjectFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMObjectFactory methods ***/
static inline HRESULT IXpsOMObjectFactory_CreatePackage(IXpsOMObjectFactory* This,IXpsOMPackage **package) {
    return This->lpVtbl->CreatePackage(This,package);
}
static inline HRESULT IXpsOMObjectFactory_CreatePackageFromFile(IXpsOMObjectFactory* This,LPCWSTR filename,WINBOOL reuseObjects,IXpsOMPackage **package) {
    return This->lpVtbl->CreatePackageFromFile(This,filename,reuseObjects,package);
}
static inline HRESULT IXpsOMObjectFactory_CreatePackageFromStream(IXpsOMObjectFactory* This,IStream *stream,WINBOOL reuseObjects,IXpsOMPackage **package) {
    return This->lpVtbl->CreatePackageFromStream(This,stream,reuseObjects,package);
}
static inline HRESULT IXpsOMObjectFactory_CreateStoryFragmentsResource(IXpsOMObjectFactory* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMStoryFragmentsResource **storyFragmentsResource) {
    return This->lpVtbl->CreateStoryFragmentsResource(This,acquiredStream,partUri,storyFragmentsResource);
}
static inline HRESULT IXpsOMObjectFactory_CreateDocumentStructureResource(IXpsOMObjectFactory* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMDocumentStructureResource **documentStructureResource) {
    return This->lpVtbl->CreateDocumentStructureResource(This,acquiredStream,partUri,documentStructureResource);
}
static inline HRESULT IXpsOMObjectFactory_CreateSignatureBlockResource(IXpsOMObjectFactory* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMSignatureBlockResource **signatureBlockResource) {
    return This->lpVtbl->CreateSignatureBlockResource(This,acquiredStream,partUri,signatureBlockResource);
}
static inline HRESULT IXpsOMObjectFactory_CreateRemoteDictionaryResource(IXpsOMObjectFactory* This,IXpsOMDictionary *dictionary,IOpcPartUri *partUri,IXpsOMRemoteDictionaryResource **remoteDictionaryResource) {
    return This->lpVtbl->CreateRemoteDictionaryResource(This,dictionary,partUri,remoteDictionaryResource);
}
static inline HRESULT IXpsOMObjectFactory_CreateRemoteDictionaryResourceFromStream(IXpsOMObjectFactory* This,IStream *dictionaryMarkupStream,IOpcPartUri *dictionaryPartUri,IXpsOMPartResources *resources,IXpsOMRemoteDictionaryResource **dictionaryResource) {
    return This->lpVtbl->CreateRemoteDictionaryResourceFromStream(This,dictionaryMarkupStream,dictionaryPartUri,resources,dictionaryResource);
}
static inline HRESULT IXpsOMObjectFactory_CreatePartResources(IXpsOMObjectFactory* This,IXpsOMPartResources **partResources) {
    return This->lpVtbl->CreatePartResources(This,partResources);
}
static inline HRESULT IXpsOMObjectFactory_CreateDocumentSequence(IXpsOMObjectFactory* This,IOpcPartUri *partUri,IXpsOMDocumentSequence **documentSequence) {
    return This->lpVtbl->CreateDocumentSequence(This,partUri,documentSequence);
}
static inline HRESULT IXpsOMObjectFactory_CreateDocument(IXpsOMObjectFactory* This,IOpcPartUri *partUri,IXpsOMDocument **document) {
    return This->lpVtbl->CreateDocument(This,partUri,document);
}
static inline HRESULT IXpsOMObjectFactory_CreatePageReference(IXpsOMObjectFactory* This,const XPS_SIZE *advisoryPageDimensions,IXpsOMPageReference **pageReference) {
    return This->lpVtbl->CreatePageReference(This,advisoryPageDimensions,pageReference);
}
static inline HRESULT IXpsOMObjectFactory_CreatePage(IXpsOMObjectFactory* This,const XPS_SIZE *pageDimensions,LPCWSTR language,IOpcPartUri *partUri,IXpsOMPage **page) {
    return This->lpVtbl->CreatePage(This,pageDimensions,language,partUri,page);
}
static inline HRESULT IXpsOMObjectFactory_CreatePageFromStream(IXpsOMObjectFactory* This,IStream *pageMarkupStream,IOpcPartUri *partUri,IXpsOMPartResources *resources,WINBOOL reuseObjects,IXpsOMPage **page) {
    return This->lpVtbl->CreatePageFromStream(This,pageMarkupStream,partUri,resources,reuseObjects,page);
}
static inline HRESULT IXpsOMObjectFactory_CreateCanvas(IXpsOMObjectFactory* This,IXpsOMCanvas **canvas) {
    return This->lpVtbl->CreateCanvas(This,canvas);
}
static inline HRESULT IXpsOMObjectFactory_CreateGlyphs(IXpsOMObjectFactory* This,IXpsOMFontResource *fontResource,IXpsOMGlyphs **glyphs) {
    return This->lpVtbl->CreateGlyphs(This,fontResource,glyphs);
}
static inline HRESULT IXpsOMObjectFactory_CreatePath(IXpsOMObjectFactory* This,IXpsOMPath **path) {
    return This->lpVtbl->CreatePath(This,path);
}
static inline HRESULT IXpsOMObjectFactory_CreateGeometry(IXpsOMObjectFactory* This,IXpsOMGeometry **geometry) {
    return This->lpVtbl->CreateGeometry(This,geometry);
}
static inline HRESULT IXpsOMObjectFactory_CreateGeometryFigure(IXpsOMObjectFactory* This,const XPS_POINT *startPoint,IXpsOMGeometryFigure **figure) {
    return This->lpVtbl->CreateGeometryFigure(This,startPoint,figure);
}
static inline HRESULT IXpsOMObjectFactory_CreateMatrixTransform(IXpsOMObjectFactory* This,const XPS_MATRIX *matrix,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->CreateMatrixTransform(This,matrix,transform);
}
static inline HRESULT IXpsOMObjectFactory_CreateSolidColorBrush(IXpsOMObjectFactory* This,const XPS_COLOR *color,IXpsOMColorProfileResource *colorProfile,IXpsOMSolidColorBrush **solidColorBrush) {
    return This->lpVtbl->CreateSolidColorBrush(This,color,colorProfile,solidColorBrush);
}
static inline HRESULT IXpsOMObjectFactory_CreateColorProfileResource(IXpsOMObjectFactory* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMColorProfileResource **colorProfileResource) {
    return This->lpVtbl->CreateColorProfileResource(This,acquiredStream,partUri,colorProfileResource);
}
static inline HRESULT IXpsOMObjectFactory_CreateImageBrush(IXpsOMObjectFactory* This,IXpsOMImageResource *image,const XPS_RECT *viewBox,const XPS_RECT *viewPort,IXpsOMImageBrush **imageBrush) {
    return This->lpVtbl->CreateImageBrush(This,image,viewBox,viewPort,imageBrush);
}
static inline HRESULT IXpsOMObjectFactory_CreateVisualBrush(IXpsOMObjectFactory* This,const XPS_RECT *viewBox,const XPS_RECT *viewPort,IXpsOMVisualBrush **visualBrush) {
    return This->lpVtbl->CreateVisualBrush(This,viewBox,viewPort,visualBrush);
}
static inline HRESULT IXpsOMObjectFactory_CreateImageResource(IXpsOMObjectFactory* This,IStream *acquiredStream,XPS_IMAGE_TYPE contentType,IOpcPartUri *partUri,IXpsOMImageResource **imageResource) {
    return This->lpVtbl->CreateImageResource(This,acquiredStream,contentType,partUri,imageResource);
}
static inline HRESULT IXpsOMObjectFactory_CreatePrintTicketResource(IXpsOMObjectFactory* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMPrintTicketResource **printTicketResource) {
    return This->lpVtbl->CreatePrintTicketResource(This,acquiredStream,partUri,printTicketResource);
}
static inline HRESULT IXpsOMObjectFactory_CreateFontResource(IXpsOMObjectFactory* This,IStream *acquiredStream,XPS_FONT_EMBEDDING fontEmbedding,IOpcPartUri *partUri,WINBOOL isObfSourceStream,IXpsOMFontResource **fontResource) {
    return This->lpVtbl->CreateFontResource(This,acquiredStream,fontEmbedding,partUri,isObfSourceStream,fontResource);
}
static inline HRESULT IXpsOMObjectFactory_CreateGradientStop(IXpsOMObjectFactory* This,const XPS_COLOR *color,IXpsOMColorProfileResource *colorProfile,FLOAT offset,IXpsOMGradientStop **gradientStop) {
    return This->lpVtbl->CreateGradientStop(This,color,colorProfile,offset,gradientStop);
}
static inline HRESULT IXpsOMObjectFactory_CreateLinearGradientBrush(IXpsOMObjectFactory* This,IXpsOMGradientStop *gradStop1,IXpsOMGradientStop *gradStop2,const XPS_POINT *startPoint,const XPS_POINT *endPoint,IXpsOMLinearGradientBrush **linearGradientBrush) {
    return This->lpVtbl->CreateLinearGradientBrush(This,gradStop1,gradStop2,startPoint,endPoint,linearGradientBrush);
}
static inline HRESULT IXpsOMObjectFactory_CreateRadialGradientBrush(IXpsOMObjectFactory* This,IXpsOMGradientStop *gradStop1,IXpsOMGradientStop *gradStop2,const XPS_POINT *centerPoint,const XPS_POINT *gradientOrigin,const XPS_SIZE *radiiSizes,IXpsOMRadialGradientBrush **radialGradientBrush) {
    return This->lpVtbl->CreateRadialGradientBrush(This,gradStop1,gradStop2,centerPoint,gradientOrigin,radiiSizes,radialGradientBrush);
}
static inline HRESULT IXpsOMObjectFactory_CreateCoreProperties(IXpsOMObjectFactory* This,IOpcPartUri *partUri,IXpsOMCoreProperties **coreProperties) {
    return This->lpVtbl->CreateCoreProperties(This,partUri,coreProperties);
}
static inline HRESULT IXpsOMObjectFactory_CreateDictionary(IXpsOMObjectFactory* This,IXpsOMDictionary **dictionary) {
    return This->lpVtbl->CreateDictionary(This,dictionary);
}
static inline HRESULT IXpsOMObjectFactory_CreatePartUriCollection(IXpsOMObjectFactory* This,IXpsOMPartUriCollection **partUriCollection) {
    return This->lpVtbl->CreatePartUriCollection(This,partUriCollection);
}
static inline HRESULT IXpsOMObjectFactory_CreatePackageWriterOnFile(IXpsOMObjectFactory* This,LPCWSTR fileName,LPSECURITY_ATTRIBUTES securityAttributes,DWORD flagsAndAttributes,WINBOOL optimizeMarkupSize,XPS_INTERLEAVING interleaving,IOpcPartUri *documentSequencePartName,IXpsOMCoreProperties *coreProperties,IXpsOMImageResource *packageThumbnail,IXpsOMPrintTicketResource *documentSequencePrintTicket,IOpcPartUri *discardControlPartName,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->CreatePackageWriterOnFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter);
}
static inline HRESULT IXpsOMObjectFactory_CreatePackageWriterOnStream(IXpsOMObjectFactory* This,ISequentialStream *outputStream,WINBOOL optimizeMarkupSize,XPS_INTERLEAVING interleaving,IOpcPartUri *documentSequencePartName,IXpsOMCoreProperties *coreProperties,IXpsOMImageResource *packageThumbnail,IXpsOMPrintTicketResource *documentSequencePrintTicket,IOpcPartUri *discardControlPartName,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->CreatePackageWriterOnStream(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter);
}
static inline HRESULT IXpsOMObjectFactory_CreatePartUri(IXpsOMObjectFactory* This,LPCWSTR uri,IOpcPartUri **partUri) {
    return This->lpVtbl->CreatePartUri(This,uri,partUri);
}
static inline HRESULT IXpsOMObjectFactory_CreateReadOnlyStreamOnFile(IXpsOMObjectFactory* This,LPCWSTR filename,IStream **stream) {
    return This->lpVtbl->CreateReadOnlyStreamOnFile(This,filename,stream);
}
#endif
#endif

#endif


#endif  /* __IXpsOMObjectFactory_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMNameCollection interface
 */
#ifndef __IXpsOMNameCollection_INTERFACE_DEFINED__
#define __IXpsOMNameCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMNameCollection, 0x4bddf8ec, 0xc915, 0x421b, 0xa1,0x66, 0xd1,0x73,0xd2,0x56,0x53,0xd2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4bddf8ec-c915-421b-a166-d173d25653d2")
IXpsOMNameCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        LPWSTR *name) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMNameCollection, 0x4bddf8ec, 0xc915, 0x421b, 0xa1,0x66, 0xd1,0x73,0xd2,0x56,0x53,0xd2)
#endif
#else
typedef struct IXpsOMNameCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMNameCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMNameCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMNameCollection *This);

    /*** IXpsOMNameCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMNameCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMNameCollection *This,
        UINT32 index,
        LPWSTR *name);

    END_INTERFACE
} IXpsOMNameCollectionVtbl;

interface IXpsOMNameCollection {
    CONST_VTBL IXpsOMNameCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMNameCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMNameCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMNameCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMNameCollection methods ***/
#define IXpsOMNameCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMNameCollection_GetAt(This,index,name) (This)->lpVtbl->GetAt(This,index,name)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMNameCollection_QueryInterface(IXpsOMNameCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMNameCollection_AddRef(IXpsOMNameCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMNameCollection_Release(IXpsOMNameCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMNameCollection methods ***/
static inline HRESULT IXpsOMNameCollection_GetCount(IXpsOMNameCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMNameCollection_GetAt(IXpsOMNameCollection* This,UINT32 index,LPWSTR *name) {
    return This->lpVtbl->GetAt(This,index,name);
}
#endif
#endif

#endif


#endif  /* __IXpsOMNameCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPartUriCollection interface
 */
#ifndef __IXpsOMPartUriCollection_INTERFACE_DEFINED__
#define __IXpsOMPartUriCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPartUriCollection, 0x57c650d4, 0x067c, 0x4893, 0x8c,0x33, 0xf6,0x2a,0x06,0x33,0x73,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("57c650d4-067c-4893-8c33-f62a0633730f")
IXpsOMPartUriCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IOpcPartUri **partUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAt(
        UINT32 index,
        IOpcPartUri *partUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAt(
        UINT32 index,
        IOpcPartUri *partUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE Append(
        IOpcPartUri *partUri) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPartUriCollection, 0x57c650d4, 0x067c, 0x4893, 0x8c,0x33, 0xf6,0x2a,0x06,0x33,0x73,0x0f)
#endif
#else
typedef struct IXpsOMPartUriCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPartUriCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPartUriCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPartUriCollection *This);

    /*** IXpsOMPartUriCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsOMPartUriCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsOMPartUriCollection *This,
        UINT32 index,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        IXpsOMPartUriCollection *This,
        UINT32 index,
        IOpcPartUri *partUri);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsOMPartUriCollection *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        IXpsOMPartUriCollection *This,
        UINT32 index,
        IOpcPartUri *partUri);

    HRESULT (STDMETHODCALLTYPE *Append)(
        IXpsOMPartUriCollection *This,
        IOpcPartUri *partUri);

    END_INTERFACE
} IXpsOMPartUriCollectionVtbl;

interface IXpsOMPartUriCollection {
    CONST_VTBL IXpsOMPartUriCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPartUriCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPartUriCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPartUriCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPartUriCollection methods ***/
#define IXpsOMPartUriCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsOMPartUriCollection_GetAt(This,index,partUri) (This)->lpVtbl->GetAt(This,index,partUri)
#define IXpsOMPartUriCollection_InsertAt(This,index,partUri) (This)->lpVtbl->InsertAt(This,index,partUri)
#define IXpsOMPartUriCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define IXpsOMPartUriCollection_SetAt(This,index,partUri) (This)->lpVtbl->SetAt(This,index,partUri)
#define IXpsOMPartUriCollection_Append(This,partUri) (This)->lpVtbl->Append(This,partUri)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPartUriCollection_QueryInterface(IXpsOMPartUriCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPartUriCollection_AddRef(IXpsOMPartUriCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPartUriCollection_Release(IXpsOMPartUriCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPartUriCollection methods ***/
static inline HRESULT IXpsOMPartUriCollection_GetCount(IXpsOMPartUriCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsOMPartUriCollection_GetAt(IXpsOMPartUriCollection* This,UINT32 index,IOpcPartUri **partUri) {
    return This->lpVtbl->GetAt(This,index,partUri);
}
static inline HRESULT IXpsOMPartUriCollection_InsertAt(IXpsOMPartUriCollection* This,UINT32 index,IOpcPartUri *partUri) {
    return This->lpVtbl->InsertAt(This,index,partUri);
}
static inline HRESULT IXpsOMPartUriCollection_RemoveAt(IXpsOMPartUriCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT IXpsOMPartUriCollection_SetAt(IXpsOMPartUriCollection* This,UINT32 index,IOpcPartUri *partUri) {
    return This->lpVtbl->SetAt(This,index,partUri);
}
static inline HRESULT IXpsOMPartUriCollection_Append(IXpsOMPartUriCollection* This,IOpcPartUri *partUri) {
    return This->lpVtbl->Append(This,partUri);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPartUriCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPackageWriter interface
 */
#ifndef __IXpsOMPackageWriter_INTERFACE_DEFINED__
#define __IXpsOMPackageWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPackageWriter, 0x4e2aa182, 0xa443, 0x42c6, 0xb4,0x1b, 0x4f,0x8e,0x9d,0xe7,0x3f,0xf9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4e2aa182-a443-42c6-b41b-4f8e9de73ff9")
IXpsOMPackageWriter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartNewDocument(
        IOpcPartUri *documentPartName,
        IXpsOMPrintTicketResource *documentPrintTicket,
        IXpsOMDocumentStructureResource *documentStructure,
        IXpsOMSignatureBlockResourceCollection *signatureBlockResources,
        IXpsOMPartUriCollection *restrictedFonts) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddPage(
        IXpsOMPage *page,
        const XPS_SIZE *advisoryPageDimensions,
        IXpsOMPartUriCollection *discardableResourceParts,
        IXpsOMStoryFragmentsResource *storyFragments,
        IXpsOMPrintTicketResource *pagePrintTicket,
        IXpsOMImageResource *pageThumbnail) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddResource(
        IXpsOMResource *resource) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsClosed(
        WINBOOL *isClosed) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPackageWriter, 0x4e2aa182, 0xa443, 0x42c6, 0xb4,0x1b, 0x4f,0x8e,0x9d,0xe7,0x3f,0xf9)
#endif
#else
typedef struct IXpsOMPackageWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPackageWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPackageWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPackageWriter *This);

    /*** IXpsOMPackageWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *StartNewDocument)(
        IXpsOMPackageWriter *This,
        IOpcPartUri *documentPartName,
        IXpsOMPrintTicketResource *documentPrintTicket,
        IXpsOMDocumentStructureResource *documentStructure,
        IXpsOMSignatureBlockResourceCollection *signatureBlockResources,
        IXpsOMPartUriCollection *restrictedFonts);

    HRESULT (STDMETHODCALLTYPE *AddPage)(
        IXpsOMPackageWriter *This,
        IXpsOMPage *page,
        const XPS_SIZE *advisoryPageDimensions,
        IXpsOMPartUriCollection *discardableResourceParts,
        IXpsOMStoryFragmentsResource *storyFragments,
        IXpsOMPrintTicketResource *pagePrintTicket,
        IXpsOMImageResource *pageThumbnail);

    HRESULT (STDMETHODCALLTYPE *AddResource)(
        IXpsOMPackageWriter *This,
        IXpsOMResource *resource);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IXpsOMPackageWriter *This);

    HRESULT (STDMETHODCALLTYPE *IsClosed)(
        IXpsOMPackageWriter *This,
        WINBOOL *isClosed);

    END_INTERFACE
} IXpsOMPackageWriterVtbl;

interface IXpsOMPackageWriter {
    CONST_VTBL IXpsOMPackageWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPackageWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPackageWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPackageWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPackageWriter methods ***/
#define IXpsOMPackageWriter_StartNewDocument(This,documentPartName,documentPrintTicket,documentStructure,signatureBlockResources,restrictedFonts) (This)->lpVtbl->StartNewDocument(This,documentPartName,documentPrintTicket,documentStructure,signatureBlockResources,restrictedFonts)
#define IXpsOMPackageWriter_AddPage(This,page,advisoryPageDimensions,discardableResourceParts,storyFragments,pagePrintTicket,pageThumbnail) (This)->lpVtbl->AddPage(This,page,advisoryPageDimensions,discardableResourceParts,storyFragments,pagePrintTicket,pageThumbnail)
#define IXpsOMPackageWriter_AddResource(This,resource) (This)->lpVtbl->AddResource(This,resource)
#define IXpsOMPackageWriter_Close(This) (This)->lpVtbl->Close(This)
#define IXpsOMPackageWriter_IsClosed(This,isClosed) (This)->lpVtbl->IsClosed(This,isClosed)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPackageWriter_QueryInterface(IXpsOMPackageWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPackageWriter_AddRef(IXpsOMPackageWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPackageWriter_Release(IXpsOMPackageWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPackageWriter methods ***/
static inline HRESULT IXpsOMPackageWriter_StartNewDocument(IXpsOMPackageWriter* This,IOpcPartUri *documentPartName,IXpsOMPrintTicketResource *documentPrintTicket,IXpsOMDocumentStructureResource *documentStructure,IXpsOMSignatureBlockResourceCollection *signatureBlockResources,IXpsOMPartUriCollection *restrictedFonts) {
    return This->lpVtbl->StartNewDocument(This,documentPartName,documentPrintTicket,documentStructure,signatureBlockResources,restrictedFonts);
}
static inline HRESULT IXpsOMPackageWriter_AddPage(IXpsOMPackageWriter* This,IXpsOMPage *page,const XPS_SIZE *advisoryPageDimensions,IXpsOMPartUriCollection *discardableResourceParts,IXpsOMStoryFragmentsResource *storyFragments,IXpsOMPrintTicketResource *pagePrintTicket,IXpsOMImageResource *pageThumbnail) {
    return This->lpVtbl->AddPage(This,page,advisoryPageDimensions,discardableResourceParts,storyFragments,pagePrintTicket,pageThumbnail);
}
static inline HRESULT IXpsOMPackageWriter_AddResource(IXpsOMPackageWriter* This,IXpsOMResource *resource) {
    return This->lpVtbl->AddResource(This,resource);
}
static inline HRESULT IXpsOMPackageWriter_Close(IXpsOMPackageWriter* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IXpsOMPackageWriter_IsClosed(IXpsOMPackageWriter* This,WINBOOL *isClosed) {
    return This->lpVtbl->IsClosed(This,isClosed);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPackageWriter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPackageTarget interface
 */
#ifndef __IXpsOMPackageTarget_INTERFACE_DEFINED__
#define __IXpsOMPackageTarget_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPackageTarget, 0x219a9db0, 0x4959, 0x47d0, 0x80,0x34, 0xb1,0xce,0x84,0xf4,0x1a,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("219a9db0-4959-47d0-8034-b1ce84f41a4d")
IXpsOMPackageTarget : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateXpsOMPackageWriter(
        IOpcPartUri *documentSequencePartName,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPackageTarget, 0x219a9db0, 0x4959, 0x47d0, 0x80,0x34, 0xb1,0xce,0x84,0xf4,0x1a,0x4d)
#endif
#else
typedef struct IXpsOMPackageTargetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPackageTarget *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPackageTarget *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPackageTarget *This);

    /*** IXpsOMPackageTarget methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateXpsOMPackageWriter)(
        IXpsOMPackageTarget *This,
        IOpcPartUri *documentSequencePartName,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter);

    END_INTERFACE
} IXpsOMPackageTargetVtbl;

interface IXpsOMPackageTarget {
    CONST_VTBL IXpsOMPackageTargetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPackageTarget_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPackageTarget_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPackageTarget_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPackageTarget methods ***/
#define IXpsOMPackageTarget_CreateXpsOMPackageWriter(This,documentSequencePartName,documentSequencePrintTicket,discardControlPartName,packageWriter) (This)->lpVtbl->CreateXpsOMPackageWriter(This,documentSequencePartName,documentSequencePrintTicket,discardControlPartName,packageWriter)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPackageTarget_QueryInterface(IXpsOMPackageTarget* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPackageTarget_AddRef(IXpsOMPackageTarget* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPackageTarget_Release(IXpsOMPackageTarget* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPackageTarget methods ***/
static inline HRESULT IXpsOMPackageTarget_CreateXpsOMPackageWriter(IXpsOMPackageTarget* This,IOpcPartUri *documentSequencePartName,IXpsOMPrintTicketResource *documentSequencePrintTicket,IOpcPartUri *discardControlPartName,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->CreateXpsOMPackageWriter(This,documentSequencePartName,documentSequencePrintTicket,discardControlPartName,packageWriter);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPackageTarget_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMVisual interface
 */
#ifndef __IXpsOMVisual_INTERFACE_DEFINED__
#define __IXpsOMVisual_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMVisual, 0xbc3e7333, 0xfb0b, 0x4af3, 0xa8,0x19, 0x0b,0x4e,0xaa,0xd0,0xd2,0xfd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bc3e7333-fb0b-4af3-a819-0b4eaad0d2fd")
IXpsOMVisual : public IXpsOMShareable
{
    virtual HRESULT STDMETHODCALLTYPE GetTransform(
        IXpsOMMatrixTransform **matrixTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLocal(
        IXpsOMMatrixTransform **matrixTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLocal(
        IXpsOMMatrixTransform *matrixTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLookup(
        LPWSTR *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLookup(
        LPCWSTR key) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClipGeometry(
        IXpsOMGeometry **clipGeometry) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClipGeometryLocal(
        IXpsOMGeometry **clipGeometry) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetClipGeometryLocal(
        IXpsOMGeometry *clipGeometry) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClipGeometryLookup(
        LPWSTR *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetClipGeometryLookup(
        LPCWSTR key) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOpacity(
        FLOAT *opacity) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpacity(
        FLOAT opacity) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOpacityMaskBrush(
        IXpsOMBrush **opacityMaskBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOpacityMaskBrushLocal(
        IXpsOMBrush **opacityMaskBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpacityMaskBrushLocal(
        IXpsOMBrush *opacityMaskBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOpacityMaskBrushLookup(
        LPWSTR *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpacityMaskBrushLookup(
        LPCWSTR key) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetName(
        LPWSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetName(
        LPCWSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsHyperlinkTarget(
        WINBOOL *isHyperlink) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIsHyperlinkTarget(
        WINBOOL isHyperlink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHyperlinkNavigateUri(
        IUri **hyperlinkUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHyperlinkNavigateUri(
        IUri *hyperlinkUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguage(
        LPWSTR *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLanguage(
        LPCWSTR language) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMVisual, 0xbc3e7333, 0xfb0b, 0x4af3, 0xa8,0x19, 0x0b,0x4e,0xaa,0xd0,0xd2,0xfd)
#endif
#else
typedef struct IXpsOMVisualVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMVisual *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMVisual *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMVisual *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMVisual *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMVisual *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMVisual methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMVisual *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMVisual *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMVisual *This,
        IXpsOMMatrixTransform *matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMVisual *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMVisual *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometry)(
        IXpsOMVisual *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLocal)(
        IXpsOMVisual *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLocal)(
        IXpsOMVisual *This,
        IXpsOMGeometry *clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLookup)(
        IXpsOMVisual *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLookup)(
        IXpsOMVisual *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMVisual *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMVisual *This,
        FLOAT opacity);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrush)(
        IXpsOMVisual *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLocal)(
        IXpsOMVisual *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLocal)(
        IXpsOMVisual *This,
        IXpsOMBrush *opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLookup)(
        IXpsOMVisual *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLookup)(
        IXpsOMVisual *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IXpsOMVisual *This,
        LPWSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IXpsOMVisual *This,
        LPCWSTR name);

    HRESULT (STDMETHODCALLTYPE *GetIsHyperlinkTarget)(
        IXpsOMVisual *This,
        WINBOOL *isHyperlink);

    HRESULT (STDMETHODCALLTYPE *SetIsHyperlinkTarget)(
        IXpsOMVisual *This,
        WINBOOL isHyperlink);

    HRESULT (STDMETHODCALLTYPE *GetHyperlinkNavigateUri)(
        IXpsOMVisual *This,
        IUri **hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *SetHyperlinkNavigateUri)(
        IXpsOMVisual *This,
        IUri *hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IXpsOMVisual *This,
        LPWSTR *language);

    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IXpsOMVisual *This,
        LPCWSTR language);

    END_INTERFACE
} IXpsOMVisualVtbl;

interface IXpsOMVisual {
    CONST_VTBL IXpsOMVisualVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMVisual_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMVisual_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMVisual_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMVisual_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMVisual_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMVisual methods ***/
#define IXpsOMVisual_GetTransform(This,matrixTransform) (This)->lpVtbl->GetTransform(This,matrixTransform)
#define IXpsOMVisual_GetTransformLocal(This,matrixTransform) (This)->lpVtbl->GetTransformLocal(This,matrixTransform)
#define IXpsOMVisual_SetTransformLocal(This,matrixTransform) (This)->lpVtbl->SetTransformLocal(This,matrixTransform)
#define IXpsOMVisual_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMVisual_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMVisual_GetClipGeometry(This,clipGeometry) (This)->lpVtbl->GetClipGeometry(This,clipGeometry)
#define IXpsOMVisual_GetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->GetClipGeometryLocal(This,clipGeometry)
#define IXpsOMVisual_SetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->SetClipGeometryLocal(This,clipGeometry)
#define IXpsOMVisual_GetClipGeometryLookup(This,key) (This)->lpVtbl->GetClipGeometryLookup(This,key)
#define IXpsOMVisual_SetClipGeometryLookup(This,key) (This)->lpVtbl->SetClipGeometryLookup(This,key)
#define IXpsOMVisual_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMVisual_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
#define IXpsOMVisual_GetOpacityMaskBrush(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush)
#define IXpsOMVisual_GetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMVisual_SetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMVisual_GetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->GetOpacityMaskBrushLookup(This,key)
#define IXpsOMVisual_SetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->SetOpacityMaskBrushLookup(This,key)
#define IXpsOMVisual_GetName(This,name) (This)->lpVtbl->GetName(This,name)
#define IXpsOMVisual_SetName(This,name) (This)->lpVtbl->SetName(This,name)
#define IXpsOMVisual_GetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMVisual_SetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMVisual_GetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMVisual_SetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMVisual_GetLanguage(This,language) (This)->lpVtbl->GetLanguage(This,language)
#define IXpsOMVisual_SetLanguage(This,language) (This)->lpVtbl->SetLanguage(This,language)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMVisual_QueryInterface(IXpsOMVisual* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMVisual_AddRef(IXpsOMVisual* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMVisual_Release(IXpsOMVisual* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMVisual_GetOwner(IXpsOMVisual* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMVisual_GetType(IXpsOMVisual* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMVisual methods ***/
static inline HRESULT IXpsOMVisual_GetTransform(IXpsOMVisual* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransform(This,matrixTransform);
}
static inline HRESULT IXpsOMVisual_GetTransformLocal(IXpsOMVisual* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMVisual_SetTransformLocal(IXpsOMVisual* This,IXpsOMMatrixTransform *matrixTransform) {
    return This->lpVtbl->SetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMVisual_GetTransformLookup(IXpsOMVisual* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMVisual_SetTransformLookup(IXpsOMVisual* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMVisual_GetClipGeometry(IXpsOMVisual* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometry(This,clipGeometry);
}
static inline HRESULT IXpsOMVisual_GetClipGeometryLocal(IXpsOMVisual* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMVisual_SetClipGeometryLocal(IXpsOMVisual* This,IXpsOMGeometry *clipGeometry) {
    return This->lpVtbl->SetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMVisual_GetClipGeometryLookup(IXpsOMVisual* This,LPWSTR *key) {
    return This->lpVtbl->GetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMVisual_SetClipGeometryLookup(IXpsOMVisual* This,LPCWSTR key) {
    return This->lpVtbl->SetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMVisual_GetOpacity(IXpsOMVisual* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMVisual_SetOpacity(IXpsOMVisual* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
static inline HRESULT IXpsOMVisual_GetOpacityMaskBrush(IXpsOMVisual* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMVisual_GetOpacityMaskBrushLocal(IXpsOMVisual* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMVisual_SetOpacityMaskBrushLocal(IXpsOMVisual* This,IXpsOMBrush *opacityMaskBrush) {
    return This->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMVisual_GetOpacityMaskBrushLookup(IXpsOMVisual* This,LPWSTR *key) {
    return This->lpVtbl->GetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMVisual_SetOpacityMaskBrushLookup(IXpsOMVisual* This,LPCWSTR key) {
    return This->lpVtbl->SetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMVisual_GetName(IXpsOMVisual* This,LPWSTR *name) {
    return This->lpVtbl->GetName(This,name);
}
static inline HRESULT IXpsOMVisual_SetName(IXpsOMVisual* This,LPCWSTR name) {
    return This->lpVtbl->SetName(This,name);
}
static inline HRESULT IXpsOMVisual_GetIsHyperlinkTarget(IXpsOMVisual* This,WINBOOL *isHyperlink) {
    return This->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMVisual_SetIsHyperlinkTarget(IXpsOMVisual* This,WINBOOL isHyperlink) {
    return This->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMVisual_GetHyperlinkNavigateUri(IXpsOMVisual* This,IUri **hyperlinkUri) {
    return This->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMVisual_SetHyperlinkNavigateUri(IXpsOMVisual* This,IUri *hyperlinkUri) {
    return This->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMVisual_GetLanguage(IXpsOMVisual* This,LPWSTR *language) {
    return This->lpVtbl->GetLanguage(This,language);
}
static inline HRESULT IXpsOMVisual_SetLanguage(IXpsOMVisual* This,LPCWSTR language) {
    return This->lpVtbl->SetLanguage(This,language);
}
#endif
#endif

#endif


#endif  /* __IXpsOMVisual_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMBrush interface
 */
#ifndef __IXpsOMBrush_INTERFACE_DEFINED__
#define __IXpsOMBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMBrush, 0x56a3f80c, 0xea4c, 0x4187, 0xa5,0x7b, 0xa2,0xa4,0x73,0xb2,0xb4,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56a3f80c-ea4c-4187-a57b-a2a473b2b42b")
IXpsOMBrush : public IXpsOMShareable
{
    virtual HRESULT STDMETHODCALLTYPE GetOpacity(
        FLOAT *opacity) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpacity(
        FLOAT opacity) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMBrush, 0x56a3f80c, 0xea4c, 0x4187, 0xa5,0x7b, 0xa2,0xa4,0x73,0xb2,0xb4,0x2b)
#endif
#else
typedef struct IXpsOMBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMBrush *This,
        FLOAT opacity);

    END_INTERFACE
} IXpsOMBrushVtbl;

interface IXpsOMBrush {
    CONST_VTBL IXpsOMBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMBrush_QueryInterface(IXpsOMBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMBrush_AddRef(IXpsOMBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMBrush_Release(IXpsOMBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMBrush_GetOwner(IXpsOMBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMBrush_GetType(IXpsOMBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMBrush_GetOpacity(IXpsOMBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMBrush_SetOpacity(IXpsOMBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
#endif
#endif

#endif


#endif  /* __IXpsOMBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMMatrixTransform interface
 */
#ifndef __IXpsOMMatrixTransform_INTERFACE_DEFINED__
#define __IXpsOMMatrixTransform_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMMatrixTransform, 0xb77330ff, 0xbb37, 0x4501, 0xa9,0x3e, 0xf1,0xb1,0xe5,0x0b,0xfc,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b77330ff-bb37-4501-a93e-f1b1e50bfc46")
IXpsOMMatrixTransform : public IXpsOMShareable
{
    virtual HRESULT STDMETHODCALLTYPE GetMatrix(
        XPS_MATRIX *matrix) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMatrix(
        const XPS_MATRIX *matrix) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMMatrixTransform **matrixTransform) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMMatrixTransform, 0xb77330ff, 0xbb37, 0x4501, 0xa9,0x3e, 0xf1,0xb1,0xe5,0x0b,0xfc,0x46)
#endif
#else
typedef struct IXpsOMMatrixTransformVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMMatrixTransform *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMMatrixTransform *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMMatrixTransform *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMMatrixTransform *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMMatrixTransform *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMMatrixTransform methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMatrix)(
        IXpsOMMatrixTransform *This,
        XPS_MATRIX *matrix);

    HRESULT (STDMETHODCALLTYPE *SetMatrix)(
        IXpsOMMatrixTransform *This,
        const XPS_MATRIX *matrix);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMMatrixTransform *This,
        IXpsOMMatrixTransform **matrixTransform);

    END_INTERFACE
} IXpsOMMatrixTransformVtbl;

interface IXpsOMMatrixTransform {
    CONST_VTBL IXpsOMMatrixTransformVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMMatrixTransform_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMMatrixTransform_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMMatrixTransform_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMMatrixTransform_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMMatrixTransform_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMMatrixTransform methods ***/
#define IXpsOMMatrixTransform_GetMatrix(This,matrix) (This)->lpVtbl->GetMatrix(This,matrix)
#define IXpsOMMatrixTransform_SetMatrix(This,matrix) (This)->lpVtbl->SetMatrix(This,matrix)
#define IXpsOMMatrixTransform_Clone(This,matrixTransform) (This)->lpVtbl->Clone(This,matrixTransform)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMMatrixTransform_QueryInterface(IXpsOMMatrixTransform* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMMatrixTransform_AddRef(IXpsOMMatrixTransform* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMMatrixTransform_Release(IXpsOMMatrixTransform* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMMatrixTransform_GetOwner(IXpsOMMatrixTransform* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMMatrixTransform_GetType(IXpsOMMatrixTransform* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMMatrixTransform methods ***/
static inline HRESULT IXpsOMMatrixTransform_GetMatrix(IXpsOMMatrixTransform* This,XPS_MATRIX *matrix) {
    return This->lpVtbl->GetMatrix(This,matrix);
}
static inline HRESULT IXpsOMMatrixTransform_SetMatrix(IXpsOMMatrixTransform* This,const XPS_MATRIX *matrix) {
    return This->lpVtbl->SetMatrix(This,matrix);
}
static inline HRESULT IXpsOMMatrixTransform_Clone(IXpsOMMatrixTransform* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->Clone(This,matrixTransform);
}
#endif
#endif

#endif


#endif  /* __IXpsOMMatrixTransform_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGeometry interface
 */
#ifndef __IXpsOMGeometry_INTERFACE_DEFINED__
#define __IXpsOMGeometry_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGeometry, 0x64fcf3d7, 0x4d58, 0x44ba, 0xad,0x73, 0xa1,0x3a,0xf6,0x49,0x20,0x72);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("64fcf3d7-4d58-44ba-ad73-a13af6492072")
IXpsOMGeometry : public IXpsOMShareable
{
    virtual HRESULT STDMETHODCALLTYPE GetFigures(
        IXpsOMGeometryFigureCollection **figures) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFillRule(
        XPS_FILL_RULE *fillRule) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFillRule(
        XPS_FILL_RULE fillRule) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransform(
        IXpsOMMatrixTransform **transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLocal(
        IXpsOMMatrixTransform **transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLocal(
        IXpsOMMatrixTransform *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLookup(
        LPWSTR *lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLookup(
        LPCWSTR lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMGeometry **geometry) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGeometry, 0x64fcf3d7, 0x4d58, 0x44ba, 0xad,0x73, 0xa1,0x3a,0xf6,0x49,0x20,0x72)
#endif
#else
typedef struct IXpsOMGeometryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGeometry *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGeometry *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGeometry *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMGeometry *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMGeometry *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMGeometry methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFigures)(
        IXpsOMGeometry *This,
        IXpsOMGeometryFigureCollection **figures);

    HRESULT (STDMETHODCALLTYPE *GetFillRule)(
        IXpsOMGeometry *This,
        XPS_FILL_RULE *fillRule);

    HRESULT (STDMETHODCALLTYPE *SetFillRule)(
        IXpsOMGeometry *This,
        XPS_FILL_RULE fillRule);

    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMGeometry *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMGeometry *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMGeometry *This,
        IXpsOMMatrixTransform *transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMGeometry *This,
        LPWSTR *lookup);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMGeometry *This,
        LPCWSTR lookup);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMGeometry *This,
        IXpsOMGeometry **geometry);

    END_INTERFACE
} IXpsOMGeometryVtbl;

interface IXpsOMGeometry {
    CONST_VTBL IXpsOMGeometryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGeometry_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGeometry_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGeometry_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMGeometry_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMGeometry_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMGeometry methods ***/
#define IXpsOMGeometry_GetFigures(This,figures) (This)->lpVtbl->GetFigures(This,figures)
#define IXpsOMGeometry_GetFillRule(This,fillRule) (This)->lpVtbl->GetFillRule(This,fillRule)
#define IXpsOMGeometry_SetFillRule(This,fillRule) (This)->lpVtbl->SetFillRule(This,fillRule)
#define IXpsOMGeometry_GetTransform(This,transform) (This)->lpVtbl->GetTransform(This,transform)
#define IXpsOMGeometry_GetTransformLocal(This,transform) (This)->lpVtbl->GetTransformLocal(This,transform)
#define IXpsOMGeometry_SetTransformLocal(This,transform) (This)->lpVtbl->SetTransformLocal(This,transform)
#define IXpsOMGeometry_GetTransformLookup(This,lookup) (This)->lpVtbl->GetTransformLookup(This,lookup)
#define IXpsOMGeometry_SetTransformLookup(This,lookup) (This)->lpVtbl->SetTransformLookup(This,lookup)
#define IXpsOMGeometry_Clone(This,geometry) (This)->lpVtbl->Clone(This,geometry)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGeometry_QueryInterface(IXpsOMGeometry* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGeometry_AddRef(IXpsOMGeometry* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGeometry_Release(IXpsOMGeometry* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMGeometry_GetOwner(IXpsOMGeometry* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMGeometry_GetType(IXpsOMGeometry* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMGeometry methods ***/
static inline HRESULT IXpsOMGeometry_GetFigures(IXpsOMGeometry* This,IXpsOMGeometryFigureCollection **figures) {
    return This->lpVtbl->GetFigures(This,figures);
}
static inline HRESULT IXpsOMGeometry_GetFillRule(IXpsOMGeometry* This,XPS_FILL_RULE *fillRule) {
    return This->lpVtbl->GetFillRule(This,fillRule);
}
static inline HRESULT IXpsOMGeometry_SetFillRule(IXpsOMGeometry* This,XPS_FILL_RULE fillRule) {
    return This->lpVtbl->SetFillRule(This,fillRule);
}
static inline HRESULT IXpsOMGeometry_GetTransform(IXpsOMGeometry* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransform(This,transform);
}
static inline HRESULT IXpsOMGeometry_GetTransformLocal(IXpsOMGeometry* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMGeometry_SetTransformLocal(IXpsOMGeometry* This,IXpsOMMatrixTransform *transform) {
    return This->lpVtbl->SetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMGeometry_GetTransformLookup(IXpsOMGeometry* This,LPWSTR *lookup) {
    return This->lpVtbl->GetTransformLookup(This,lookup);
}
static inline HRESULT IXpsOMGeometry_SetTransformLookup(IXpsOMGeometry* This,LPCWSTR lookup) {
    return This->lpVtbl->SetTransformLookup(This,lookup);
}
static inline HRESULT IXpsOMGeometry_Clone(IXpsOMGeometry* This,IXpsOMGeometry **geometry) {
    return This->lpVtbl->Clone(This,geometry);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGeometry_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGlyphs interface
 */
#ifndef __IXpsOMGlyphs_INTERFACE_DEFINED__
#define __IXpsOMGlyphs_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGlyphs, 0x819b3199, 0x0a5a, 0x4b64, 0xbe,0xc7, 0xa9,0xe1,0x7e,0x78,0x0d,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("819b3199-0a5a-4b64-bec7-a9e17e780de2")
IXpsOMGlyphs : public IXpsOMVisual
{
    virtual HRESULT STDMETHODCALLTYPE GetUnicodeString(
        LPWSTR *unicodeString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphIndexCount(
        UINT32 *indexCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphIndices(
        UINT32 *indexCount,
        XPS_GLYPH_INDEX *glyphIndices) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphMappingCount(
        UINT32 *glyphMappingCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphMappings(
        UINT32 *glyphMappingCount,
        XPS_GLYPH_MAPPING *glyphMappings) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProhibitedCaretStopCount(
        UINT32 *prohibitedCaretStopCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProhibitedCaretStops(
        UINT32 *prohibitedCaretStopCount,
        UINT32 *prohibitedCaretStops) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBidiLevel(
        UINT32 *bidiLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsSideways(
        WINBOOL *isSideways) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceFontName(
        LPWSTR *deviceFontName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStyleSimulations(
        XPS_STYLE_SIMULATION *styleSimulations) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStyleSimulations(
        XPS_STYLE_SIMULATION styleSimulations) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOrigin(
        XPS_POINT *origin) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOrigin(
        const XPS_POINT *origin) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontRenderingEmSize(
        FLOAT *fontRenderingEmSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontRenderingEmSize(
        FLOAT fontRenderingEmSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontResource(
        IXpsOMFontResource **fontResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontResource(
        IXpsOMFontResource *fontResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFaceIndex(
        SHORT *fontFaceIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontFaceIndex(
        SHORT fontFaceIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFillBrush(
        IXpsOMBrush **fillBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFillBrushLocal(
        IXpsOMBrush **fillBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFillBrushLocal(
        IXpsOMBrush *fillBrush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFillBrushLookup(
        LPWSTR *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFillBrushLookup(
        LPCWSTR key) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlyphsEditor(
        IXpsOMGlyphsEditor **editor) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMGlyphs **glyphs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGlyphs, 0x819b3199, 0x0a5a, 0x4b64, 0xbe,0xc7, 0xa9,0xe1,0x7e,0x78,0x0d,0xe2)
#endif
#else
typedef struct IXpsOMGlyphsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGlyphs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGlyphs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGlyphs *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMGlyphs *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMGlyphs *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMVisual methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMGlyphs *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMGlyphs *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMGlyphs *This,
        IXpsOMMatrixTransform *matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMGlyphs *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMGlyphs *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometry)(
        IXpsOMGlyphs *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLocal)(
        IXpsOMGlyphs *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLocal)(
        IXpsOMGlyphs *This,
        IXpsOMGeometry *clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLookup)(
        IXpsOMGlyphs *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLookup)(
        IXpsOMGlyphs *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMGlyphs *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMGlyphs *This,
        FLOAT opacity);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrush)(
        IXpsOMGlyphs *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLocal)(
        IXpsOMGlyphs *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLocal)(
        IXpsOMGlyphs *This,
        IXpsOMBrush *opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLookup)(
        IXpsOMGlyphs *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLookup)(
        IXpsOMGlyphs *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IXpsOMGlyphs *This,
        LPWSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IXpsOMGlyphs *This,
        LPCWSTR name);

    HRESULT (STDMETHODCALLTYPE *GetIsHyperlinkTarget)(
        IXpsOMGlyphs *This,
        WINBOOL *isHyperlink);

    HRESULT (STDMETHODCALLTYPE *SetIsHyperlinkTarget)(
        IXpsOMGlyphs *This,
        WINBOOL isHyperlink);

    HRESULT (STDMETHODCALLTYPE *GetHyperlinkNavigateUri)(
        IXpsOMGlyphs *This,
        IUri **hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *SetHyperlinkNavigateUri)(
        IXpsOMGlyphs *This,
        IUri *hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IXpsOMGlyphs *This,
        LPWSTR *language);

    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IXpsOMGlyphs *This,
        LPCWSTR language);

    /*** IXpsOMGlyphs methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUnicodeString)(
        IXpsOMGlyphs *This,
        LPWSTR *unicodeString);

    HRESULT (STDMETHODCALLTYPE *GetGlyphIndexCount)(
        IXpsOMGlyphs *This,
        UINT32 *indexCount);

    HRESULT (STDMETHODCALLTYPE *GetGlyphIndices)(
        IXpsOMGlyphs *This,
        UINT32 *indexCount,
        XPS_GLYPH_INDEX *glyphIndices);

    HRESULT (STDMETHODCALLTYPE *GetGlyphMappingCount)(
        IXpsOMGlyphs *This,
        UINT32 *glyphMappingCount);

    HRESULT (STDMETHODCALLTYPE *GetGlyphMappings)(
        IXpsOMGlyphs *This,
        UINT32 *glyphMappingCount,
        XPS_GLYPH_MAPPING *glyphMappings);

    HRESULT (STDMETHODCALLTYPE *GetProhibitedCaretStopCount)(
        IXpsOMGlyphs *This,
        UINT32 *prohibitedCaretStopCount);

    HRESULT (STDMETHODCALLTYPE *GetProhibitedCaretStops)(
        IXpsOMGlyphs *This,
        UINT32 *prohibitedCaretStopCount,
        UINT32 *prohibitedCaretStops);

    HRESULT (STDMETHODCALLTYPE *GetBidiLevel)(
        IXpsOMGlyphs *This,
        UINT32 *bidiLevel);

    HRESULT (STDMETHODCALLTYPE *GetIsSideways)(
        IXpsOMGlyphs *This,
        WINBOOL *isSideways);

    HRESULT (STDMETHODCALLTYPE *GetDeviceFontName)(
        IXpsOMGlyphs *This,
        LPWSTR *deviceFontName);

    HRESULT (STDMETHODCALLTYPE *GetStyleSimulations)(
        IXpsOMGlyphs *This,
        XPS_STYLE_SIMULATION *styleSimulations);

    HRESULT (STDMETHODCALLTYPE *SetStyleSimulations)(
        IXpsOMGlyphs *This,
        XPS_STYLE_SIMULATION styleSimulations);

    HRESULT (STDMETHODCALLTYPE *GetOrigin)(
        IXpsOMGlyphs *This,
        XPS_POINT *origin);

    HRESULT (STDMETHODCALLTYPE *SetOrigin)(
        IXpsOMGlyphs *This,
        const XPS_POINT *origin);

    HRESULT (STDMETHODCALLTYPE *GetFontRenderingEmSize)(
        IXpsOMGlyphs *This,
        FLOAT *fontRenderingEmSize);

    HRESULT (STDMETHODCALLTYPE *SetFontRenderingEmSize)(
        IXpsOMGlyphs *This,
        FLOAT fontRenderingEmSize);

    HRESULT (STDMETHODCALLTYPE *GetFontResource)(
        IXpsOMGlyphs *This,
        IXpsOMFontResource **fontResource);

    HRESULT (STDMETHODCALLTYPE *SetFontResource)(
        IXpsOMGlyphs *This,
        IXpsOMFontResource *fontResource);

    HRESULT (STDMETHODCALLTYPE *GetFontFaceIndex)(
        IXpsOMGlyphs *This,
        SHORT *fontFaceIndex);

    HRESULT (STDMETHODCALLTYPE *SetFontFaceIndex)(
        IXpsOMGlyphs *This,
        SHORT fontFaceIndex);

    HRESULT (STDMETHODCALLTYPE *GetFillBrush)(
        IXpsOMGlyphs *This,
        IXpsOMBrush **fillBrush);

    HRESULT (STDMETHODCALLTYPE *GetFillBrushLocal)(
        IXpsOMGlyphs *This,
        IXpsOMBrush **fillBrush);

    HRESULT (STDMETHODCALLTYPE *SetFillBrushLocal)(
        IXpsOMGlyphs *This,
        IXpsOMBrush *fillBrush);

    HRESULT (STDMETHODCALLTYPE *GetFillBrushLookup)(
        IXpsOMGlyphs *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetFillBrushLookup)(
        IXpsOMGlyphs *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetGlyphsEditor)(
        IXpsOMGlyphs *This,
        IXpsOMGlyphsEditor **editor);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMGlyphs *This,
        IXpsOMGlyphs **glyphs);

    END_INTERFACE
} IXpsOMGlyphsVtbl;

interface IXpsOMGlyphs {
    CONST_VTBL IXpsOMGlyphsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGlyphs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGlyphs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGlyphs_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMGlyphs_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMGlyphs_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMVisual methods ***/
#define IXpsOMGlyphs_GetTransform(This,matrixTransform) (This)->lpVtbl->GetTransform(This,matrixTransform)
#define IXpsOMGlyphs_GetTransformLocal(This,matrixTransform) (This)->lpVtbl->GetTransformLocal(This,matrixTransform)
#define IXpsOMGlyphs_SetTransformLocal(This,matrixTransform) (This)->lpVtbl->SetTransformLocal(This,matrixTransform)
#define IXpsOMGlyphs_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMGlyphs_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMGlyphs_GetClipGeometry(This,clipGeometry) (This)->lpVtbl->GetClipGeometry(This,clipGeometry)
#define IXpsOMGlyphs_GetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->GetClipGeometryLocal(This,clipGeometry)
#define IXpsOMGlyphs_SetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->SetClipGeometryLocal(This,clipGeometry)
#define IXpsOMGlyphs_GetClipGeometryLookup(This,key) (This)->lpVtbl->GetClipGeometryLookup(This,key)
#define IXpsOMGlyphs_SetClipGeometryLookup(This,key) (This)->lpVtbl->SetClipGeometryLookup(This,key)
#define IXpsOMGlyphs_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMGlyphs_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
#define IXpsOMGlyphs_GetOpacityMaskBrush(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush)
#define IXpsOMGlyphs_GetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMGlyphs_SetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMGlyphs_GetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->GetOpacityMaskBrushLookup(This,key)
#define IXpsOMGlyphs_SetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->SetOpacityMaskBrushLookup(This,key)
#define IXpsOMGlyphs_GetName(This,name) (This)->lpVtbl->GetName(This,name)
#define IXpsOMGlyphs_SetName(This,name) (This)->lpVtbl->SetName(This,name)
#define IXpsOMGlyphs_GetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMGlyphs_SetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMGlyphs_GetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMGlyphs_SetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMGlyphs_GetLanguage(This,language) (This)->lpVtbl->GetLanguage(This,language)
#define IXpsOMGlyphs_SetLanguage(This,language) (This)->lpVtbl->SetLanguage(This,language)
/*** IXpsOMGlyphs methods ***/
#define IXpsOMGlyphs_GetUnicodeString(This,unicodeString) (This)->lpVtbl->GetUnicodeString(This,unicodeString)
#define IXpsOMGlyphs_GetGlyphIndexCount(This,indexCount) (This)->lpVtbl->GetGlyphIndexCount(This,indexCount)
#define IXpsOMGlyphs_GetGlyphIndices(This,indexCount,glyphIndices) (This)->lpVtbl->GetGlyphIndices(This,indexCount,glyphIndices)
#define IXpsOMGlyphs_GetGlyphMappingCount(This,glyphMappingCount) (This)->lpVtbl->GetGlyphMappingCount(This,glyphMappingCount)
#define IXpsOMGlyphs_GetGlyphMappings(This,glyphMappingCount,glyphMappings) (This)->lpVtbl->GetGlyphMappings(This,glyphMappingCount,glyphMappings)
#define IXpsOMGlyphs_GetProhibitedCaretStopCount(This,prohibitedCaretStopCount) (This)->lpVtbl->GetProhibitedCaretStopCount(This,prohibitedCaretStopCount)
#define IXpsOMGlyphs_GetProhibitedCaretStops(This,prohibitedCaretStopCount,prohibitedCaretStops) (This)->lpVtbl->GetProhibitedCaretStops(This,prohibitedCaretStopCount,prohibitedCaretStops)
#define IXpsOMGlyphs_GetBidiLevel(This,bidiLevel) (This)->lpVtbl->GetBidiLevel(This,bidiLevel)
#define IXpsOMGlyphs_GetIsSideways(This,isSideways) (This)->lpVtbl->GetIsSideways(This,isSideways)
#define IXpsOMGlyphs_GetDeviceFontName(This,deviceFontName) (This)->lpVtbl->GetDeviceFontName(This,deviceFontName)
#define IXpsOMGlyphs_GetStyleSimulations(This,styleSimulations) (This)->lpVtbl->GetStyleSimulations(This,styleSimulations)
#define IXpsOMGlyphs_SetStyleSimulations(This,styleSimulations) (This)->lpVtbl->SetStyleSimulations(This,styleSimulations)
#define IXpsOMGlyphs_GetOrigin(This,origin) (This)->lpVtbl->GetOrigin(This,origin)
#define IXpsOMGlyphs_SetOrigin(This,origin) (This)->lpVtbl->SetOrigin(This,origin)
#define IXpsOMGlyphs_GetFontRenderingEmSize(This,fontRenderingEmSize) (This)->lpVtbl->GetFontRenderingEmSize(This,fontRenderingEmSize)
#define IXpsOMGlyphs_SetFontRenderingEmSize(This,fontRenderingEmSize) (This)->lpVtbl->SetFontRenderingEmSize(This,fontRenderingEmSize)
#define IXpsOMGlyphs_GetFontResource(This,fontResource) (This)->lpVtbl->GetFontResource(This,fontResource)
#define IXpsOMGlyphs_SetFontResource(This,fontResource) (This)->lpVtbl->SetFontResource(This,fontResource)
#define IXpsOMGlyphs_GetFontFaceIndex(This,fontFaceIndex) (This)->lpVtbl->GetFontFaceIndex(This,fontFaceIndex)
#define IXpsOMGlyphs_SetFontFaceIndex(This,fontFaceIndex) (This)->lpVtbl->SetFontFaceIndex(This,fontFaceIndex)
#define IXpsOMGlyphs_GetFillBrush(This,fillBrush) (This)->lpVtbl->GetFillBrush(This,fillBrush)
#define IXpsOMGlyphs_GetFillBrushLocal(This,fillBrush) (This)->lpVtbl->GetFillBrushLocal(This,fillBrush)
#define IXpsOMGlyphs_SetFillBrushLocal(This,fillBrush) (This)->lpVtbl->SetFillBrushLocal(This,fillBrush)
#define IXpsOMGlyphs_GetFillBrushLookup(This,key) (This)->lpVtbl->GetFillBrushLookup(This,key)
#define IXpsOMGlyphs_SetFillBrushLookup(This,key) (This)->lpVtbl->SetFillBrushLookup(This,key)
#define IXpsOMGlyphs_GetGlyphsEditor(This,editor) (This)->lpVtbl->GetGlyphsEditor(This,editor)
#define IXpsOMGlyphs_Clone(This,glyphs) (This)->lpVtbl->Clone(This,glyphs)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGlyphs_QueryInterface(IXpsOMGlyphs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGlyphs_AddRef(IXpsOMGlyphs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGlyphs_Release(IXpsOMGlyphs* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMGlyphs_GetOwner(IXpsOMGlyphs* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMGlyphs_GetType(IXpsOMGlyphs* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMVisual methods ***/
static inline HRESULT IXpsOMGlyphs_GetTransform(IXpsOMGlyphs* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransform(This,matrixTransform);
}
static inline HRESULT IXpsOMGlyphs_GetTransformLocal(IXpsOMGlyphs* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMGlyphs_SetTransformLocal(IXpsOMGlyphs* This,IXpsOMMatrixTransform *matrixTransform) {
    return This->lpVtbl->SetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMGlyphs_GetTransformLookup(IXpsOMGlyphs* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_SetTransformLookup(IXpsOMGlyphs* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_GetClipGeometry(IXpsOMGlyphs* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometry(This,clipGeometry);
}
static inline HRESULT IXpsOMGlyphs_GetClipGeometryLocal(IXpsOMGlyphs* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMGlyphs_SetClipGeometryLocal(IXpsOMGlyphs* This,IXpsOMGeometry *clipGeometry) {
    return This->lpVtbl->SetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMGlyphs_GetClipGeometryLookup(IXpsOMGlyphs* This,LPWSTR *key) {
    return This->lpVtbl->GetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_SetClipGeometryLookup(IXpsOMGlyphs* This,LPCWSTR key) {
    return This->lpVtbl->SetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_GetOpacity(IXpsOMGlyphs* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMGlyphs_SetOpacity(IXpsOMGlyphs* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
static inline HRESULT IXpsOMGlyphs_GetOpacityMaskBrush(IXpsOMGlyphs* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMGlyphs_GetOpacityMaskBrushLocal(IXpsOMGlyphs* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMGlyphs_SetOpacityMaskBrushLocal(IXpsOMGlyphs* This,IXpsOMBrush *opacityMaskBrush) {
    return This->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMGlyphs_GetOpacityMaskBrushLookup(IXpsOMGlyphs* This,LPWSTR *key) {
    return This->lpVtbl->GetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_SetOpacityMaskBrushLookup(IXpsOMGlyphs* This,LPCWSTR key) {
    return This->lpVtbl->SetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_GetName(IXpsOMGlyphs* This,LPWSTR *name) {
    return This->lpVtbl->GetName(This,name);
}
static inline HRESULT IXpsOMGlyphs_SetName(IXpsOMGlyphs* This,LPCWSTR name) {
    return This->lpVtbl->SetName(This,name);
}
static inline HRESULT IXpsOMGlyphs_GetIsHyperlinkTarget(IXpsOMGlyphs* This,WINBOOL *isHyperlink) {
    return This->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMGlyphs_SetIsHyperlinkTarget(IXpsOMGlyphs* This,WINBOOL isHyperlink) {
    return This->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMGlyphs_GetHyperlinkNavigateUri(IXpsOMGlyphs* This,IUri **hyperlinkUri) {
    return This->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMGlyphs_SetHyperlinkNavigateUri(IXpsOMGlyphs* This,IUri *hyperlinkUri) {
    return This->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMGlyphs_GetLanguage(IXpsOMGlyphs* This,LPWSTR *language) {
    return This->lpVtbl->GetLanguage(This,language);
}
static inline HRESULT IXpsOMGlyphs_SetLanguage(IXpsOMGlyphs* This,LPCWSTR language) {
    return This->lpVtbl->SetLanguage(This,language);
}
/*** IXpsOMGlyphs methods ***/
static inline HRESULT IXpsOMGlyphs_GetUnicodeString(IXpsOMGlyphs* This,LPWSTR *unicodeString) {
    return This->lpVtbl->GetUnicodeString(This,unicodeString);
}
static inline HRESULT IXpsOMGlyphs_GetGlyphIndexCount(IXpsOMGlyphs* This,UINT32 *indexCount) {
    return This->lpVtbl->GetGlyphIndexCount(This,indexCount);
}
static inline HRESULT IXpsOMGlyphs_GetGlyphIndices(IXpsOMGlyphs* This,UINT32 *indexCount,XPS_GLYPH_INDEX *glyphIndices) {
    return This->lpVtbl->GetGlyphIndices(This,indexCount,glyphIndices);
}
static inline HRESULT IXpsOMGlyphs_GetGlyphMappingCount(IXpsOMGlyphs* This,UINT32 *glyphMappingCount) {
    return This->lpVtbl->GetGlyphMappingCount(This,glyphMappingCount);
}
static inline HRESULT IXpsOMGlyphs_GetGlyphMappings(IXpsOMGlyphs* This,UINT32 *glyphMappingCount,XPS_GLYPH_MAPPING *glyphMappings) {
    return This->lpVtbl->GetGlyphMappings(This,glyphMappingCount,glyphMappings);
}
static inline HRESULT IXpsOMGlyphs_GetProhibitedCaretStopCount(IXpsOMGlyphs* This,UINT32 *prohibitedCaretStopCount) {
    return This->lpVtbl->GetProhibitedCaretStopCount(This,prohibitedCaretStopCount);
}
static inline HRESULT IXpsOMGlyphs_GetProhibitedCaretStops(IXpsOMGlyphs* This,UINT32 *prohibitedCaretStopCount,UINT32 *prohibitedCaretStops) {
    return This->lpVtbl->GetProhibitedCaretStops(This,prohibitedCaretStopCount,prohibitedCaretStops);
}
static inline HRESULT IXpsOMGlyphs_GetBidiLevel(IXpsOMGlyphs* This,UINT32 *bidiLevel) {
    return This->lpVtbl->GetBidiLevel(This,bidiLevel);
}
static inline HRESULT IXpsOMGlyphs_GetIsSideways(IXpsOMGlyphs* This,WINBOOL *isSideways) {
    return This->lpVtbl->GetIsSideways(This,isSideways);
}
static inline HRESULT IXpsOMGlyphs_GetDeviceFontName(IXpsOMGlyphs* This,LPWSTR *deviceFontName) {
    return This->lpVtbl->GetDeviceFontName(This,deviceFontName);
}
static inline HRESULT IXpsOMGlyphs_GetStyleSimulations(IXpsOMGlyphs* This,XPS_STYLE_SIMULATION *styleSimulations) {
    return This->lpVtbl->GetStyleSimulations(This,styleSimulations);
}
static inline HRESULT IXpsOMGlyphs_SetStyleSimulations(IXpsOMGlyphs* This,XPS_STYLE_SIMULATION styleSimulations) {
    return This->lpVtbl->SetStyleSimulations(This,styleSimulations);
}
static inline HRESULT IXpsOMGlyphs_GetOrigin(IXpsOMGlyphs* This,XPS_POINT *origin) {
    return This->lpVtbl->GetOrigin(This,origin);
}
static inline HRESULT IXpsOMGlyphs_SetOrigin(IXpsOMGlyphs* This,const XPS_POINT *origin) {
    return This->lpVtbl->SetOrigin(This,origin);
}
static inline HRESULT IXpsOMGlyphs_GetFontRenderingEmSize(IXpsOMGlyphs* This,FLOAT *fontRenderingEmSize) {
    return This->lpVtbl->GetFontRenderingEmSize(This,fontRenderingEmSize);
}
static inline HRESULT IXpsOMGlyphs_SetFontRenderingEmSize(IXpsOMGlyphs* This,FLOAT fontRenderingEmSize) {
    return This->lpVtbl->SetFontRenderingEmSize(This,fontRenderingEmSize);
}
static inline HRESULT IXpsOMGlyphs_GetFontResource(IXpsOMGlyphs* This,IXpsOMFontResource **fontResource) {
    return This->lpVtbl->GetFontResource(This,fontResource);
}
static inline HRESULT IXpsOMGlyphs_SetFontResource(IXpsOMGlyphs* This,IXpsOMFontResource *fontResource) {
    return This->lpVtbl->SetFontResource(This,fontResource);
}
static inline HRESULT IXpsOMGlyphs_GetFontFaceIndex(IXpsOMGlyphs* This,SHORT *fontFaceIndex) {
    return This->lpVtbl->GetFontFaceIndex(This,fontFaceIndex);
}
static inline HRESULT IXpsOMGlyphs_SetFontFaceIndex(IXpsOMGlyphs* This,SHORT fontFaceIndex) {
    return This->lpVtbl->SetFontFaceIndex(This,fontFaceIndex);
}
static inline HRESULT IXpsOMGlyphs_GetFillBrush(IXpsOMGlyphs* This,IXpsOMBrush **fillBrush) {
    return This->lpVtbl->GetFillBrush(This,fillBrush);
}
static inline HRESULT IXpsOMGlyphs_GetFillBrushLocal(IXpsOMGlyphs* This,IXpsOMBrush **fillBrush) {
    return This->lpVtbl->GetFillBrushLocal(This,fillBrush);
}
static inline HRESULT IXpsOMGlyphs_SetFillBrushLocal(IXpsOMGlyphs* This,IXpsOMBrush *fillBrush) {
    return This->lpVtbl->SetFillBrushLocal(This,fillBrush);
}
static inline HRESULT IXpsOMGlyphs_GetFillBrushLookup(IXpsOMGlyphs* This,LPWSTR *key) {
    return This->lpVtbl->GetFillBrushLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_SetFillBrushLookup(IXpsOMGlyphs* This,LPCWSTR key) {
    return This->lpVtbl->SetFillBrushLookup(This,key);
}
static inline HRESULT IXpsOMGlyphs_GetGlyphsEditor(IXpsOMGlyphs* This,IXpsOMGlyphsEditor **editor) {
    return This->lpVtbl->GetGlyphsEditor(This,editor);
}
static inline HRESULT IXpsOMGlyphs_Clone(IXpsOMGlyphs* This,IXpsOMGlyphs **glyphs) {
    return This->lpVtbl->Clone(This,glyphs);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGlyphs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPath interface
 */
#ifndef __IXpsOMPath_INTERFACE_DEFINED__
#define __IXpsOMPath_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPath, 0x37d38bb6, 0x3ee9, 0x4110, 0x93,0x12, 0x14,0xb1,0x94,0x16,0x33,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("37d38bb6-3ee9-4110-9312-14b194163337")
IXpsOMPath : public IXpsOMVisual
{
    virtual HRESULT STDMETHODCALLTYPE GetGeometry(
        IXpsOMGeometry **geometry) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGeometryLocal(
        IXpsOMGeometry **geometry) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGeometryLocal(
        IXpsOMGeometry *geometry) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGeometryLookup(
        LPWSTR *lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGeometryLookup(
        LPCWSTR lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAccessibilityShortDescription(
        LPWSTR *shortDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAccessibilityShortDescription(
        LPCWSTR shortDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAccessibilityLongDescription(
        LPWSTR *longDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAccessibilityLongDescription(
        LPCWSTR longDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSnapsToPixels(
        WINBOOL *snapsToPixels) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSnapsToPixels(
        WINBOOL snapsToPixels) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeBrush(
        IXpsOMBrush **brush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeBrushLocal(
        IXpsOMBrush **brush) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeBrushLocal(
        IXpsOMBrush *brush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeBrushLookup(
        LPWSTR *lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeBrushLookup(
        LPCWSTR lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeDashes(
        IXpsOMDashCollection **strokeDashes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeDashCap(
        XPS_DASH_CAP *strokeDashCap) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeDashCap(
        XPS_DASH_CAP strokeDashCap) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeDashOffset(
        FLOAT *strokeDashOffset) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeDashOffset(
        FLOAT strokeDashOffset) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeStartLineCap(
        XPS_LINE_CAP *strokeStartLineCap) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeStartLineCap(
        XPS_LINE_CAP strokeStartLineCap) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeEndLineCap(
        XPS_LINE_CAP *strokeEndLineCap) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeEndLineCap(
        XPS_LINE_CAP strokeEndLineCap) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeLineJoin(
        XPS_LINE_JOIN *strokeLineJoin) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeLineJoin(
        XPS_LINE_JOIN strokeLineJoin) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeMiterLimit(
        FLOAT *strokeMiterLimit) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeMiterLimit(
        FLOAT strokeMiterLimit) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokeThickness(
        FLOAT *strokeThickness) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStrokeThickness(
        FLOAT strokeThickness) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFillBrush(
        IXpsOMBrush **brush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFillBrushLocal(
        IXpsOMBrush **brush) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFillBrushLocal(
        IXpsOMBrush *brush) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFillBrushLookup(
        LPWSTR *lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFillBrushLookup(
        LPCWSTR lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMPath **path) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPath, 0x37d38bb6, 0x3ee9, 0x4110, 0x93,0x12, 0x14,0xb1,0x94,0x16,0x33,0x37)
#endif
#else
typedef struct IXpsOMPathVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPath *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPath *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPath *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMPath *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMPath *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMVisual methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMPath *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMPath *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMPath *This,
        IXpsOMMatrixTransform *matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMPath *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMPath *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometry)(
        IXpsOMPath *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLocal)(
        IXpsOMPath *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLocal)(
        IXpsOMPath *This,
        IXpsOMGeometry *clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLookup)(
        IXpsOMPath *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLookup)(
        IXpsOMPath *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMPath *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMPath *This,
        FLOAT opacity);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrush)(
        IXpsOMPath *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLocal)(
        IXpsOMPath *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLocal)(
        IXpsOMPath *This,
        IXpsOMBrush *opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLookup)(
        IXpsOMPath *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLookup)(
        IXpsOMPath *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IXpsOMPath *This,
        LPWSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IXpsOMPath *This,
        LPCWSTR name);

    HRESULT (STDMETHODCALLTYPE *GetIsHyperlinkTarget)(
        IXpsOMPath *This,
        WINBOOL *isHyperlink);

    HRESULT (STDMETHODCALLTYPE *SetIsHyperlinkTarget)(
        IXpsOMPath *This,
        WINBOOL isHyperlink);

    HRESULT (STDMETHODCALLTYPE *GetHyperlinkNavigateUri)(
        IXpsOMPath *This,
        IUri **hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *SetHyperlinkNavigateUri)(
        IXpsOMPath *This,
        IUri *hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IXpsOMPath *This,
        LPWSTR *language);

    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IXpsOMPath *This,
        LPCWSTR language);

    /*** IXpsOMPath methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGeometry)(
        IXpsOMPath *This,
        IXpsOMGeometry **geometry);

    HRESULT (STDMETHODCALLTYPE *GetGeometryLocal)(
        IXpsOMPath *This,
        IXpsOMGeometry **geometry);

    HRESULT (STDMETHODCALLTYPE *SetGeometryLocal)(
        IXpsOMPath *This,
        IXpsOMGeometry *geometry);

    HRESULT (STDMETHODCALLTYPE *GetGeometryLookup)(
        IXpsOMPath *This,
        LPWSTR *lookup);

    HRESULT (STDMETHODCALLTYPE *SetGeometryLookup)(
        IXpsOMPath *This,
        LPCWSTR lookup);

    HRESULT (STDMETHODCALLTYPE *GetAccessibilityShortDescription)(
        IXpsOMPath *This,
        LPWSTR *shortDescription);

    HRESULT (STDMETHODCALLTYPE *SetAccessibilityShortDescription)(
        IXpsOMPath *This,
        LPCWSTR shortDescription);

    HRESULT (STDMETHODCALLTYPE *GetAccessibilityLongDescription)(
        IXpsOMPath *This,
        LPWSTR *longDescription);

    HRESULT (STDMETHODCALLTYPE *SetAccessibilityLongDescription)(
        IXpsOMPath *This,
        LPCWSTR longDescription);

    HRESULT (STDMETHODCALLTYPE *GetSnapsToPixels)(
        IXpsOMPath *This,
        WINBOOL *snapsToPixels);

    HRESULT (STDMETHODCALLTYPE *SetSnapsToPixels)(
        IXpsOMPath *This,
        WINBOOL snapsToPixels);

    HRESULT (STDMETHODCALLTYPE *GetStrokeBrush)(
        IXpsOMPath *This,
        IXpsOMBrush **brush);

    HRESULT (STDMETHODCALLTYPE *GetStrokeBrushLocal)(
        IXpsOMPath *This,
        IXpsOMBrush **brush);

    HRESULT (STDMETHODCALLTYPE *SetStrokeBrushLocal)(
        IXpsOMPath *This,
        IXpsOMBrush *brush);

    HRESULT (STDMETHODCALLTYPE *GetStrokeBrushLookup)(
        IXpsOMPath *This,
        LPWSTR *lookup);

    HRESULT (STDMETHODCALLTYPE *SetStrokeBrushLookup)(
        IXpsOMPath *This,
        LPCWSTR lookup);

    HRESULT (STDMETHODCALLTYPE *GetStrokeDashes)(
        IXpsOMPath *This,
        IXpsOMDashCollection **strokeDashes);

    HRESULT (STDMETHODCALLTYPE *GetStrokeDashCap)(
        IXpsOMPath *This,
        XPS_DASH_CAP *strokeDashCap);

    HRESULT (STDMETHODCALLTYPE *SetStrokeDashCap)(
        IXpsOMPath *This,
        XPS_DASH_CAP strokeDashCap);

    HRESULT (STDMETHODCALLTYPE *GetStrokeDashOffset)(
        IXpsOMPath *This,
        FLOAT *strokeDashOffset);

    HRESULT (STDMETHODCALLTYPE *SetStrokeDashOffset)(
        IXpsOMPath *This,
        FLOAT strokeDashOffset);

    HRESULT (STDMETHODCALLTYPE *GetStrokeStartLineCap)(
        IXpsOMPath *This,
        XPS_LINE_CAP *strokeStartLineCap);

    HRESULT (STDMETHODCALLTYPE *SetStrokeStartLineCap)(
        IXpsOMPath *This,
        XPS_LINE_CAP strokeStartLineCap);

    HRESULT (STDMETHODCALLTYPE *GetStrokeEndLineCap)(
        IXpsOMPath *This,
        XPS_LINE_CAP *strokeEndLineCap);

    HRESULT (STDMETHODCALLTYPE *SetStrokeEndLineCap)(
        IXpsOMPath *This,
        XPS_LINE_CAP strokeEndLineCap);

    HRESULT (STDMETHODCALLTYPE *GetStrokeLineJoin)(
        IXpsOMPath *This,
        XPS_LINE_JOIN *strokeLineJoin);

    HRESULT (STDMETHODCALLTYPE *SetStrokeLineJoin)(
        IXpsOMPath *This,
        XPS_LINE_JOIN strokeLineJoin);

    HRESULT (STDMETHODCALLTYPE *GetStrokeMiterLimit)(
        IXpsOMPath *This,
        FLOAT *strokeMiterLimit);

    HRESULT (STDMETHODCALLTYPE *SetStrokeMiterLimit)(
        IXpsOMPath *This,
        FLOAT strokeMiterLimit);

    HRESULT (STDMETHODCALLTYPE *GetStrokeThickness)(
        IXpsOMPath *This,
        FLOAT *strokeThickness);

    HRESULT (STDMETHODCALLTYPE *SetStrokeThickness)(
        IXpsOMPath *This,
        FLOAT strokeThickness);

    HRESULT (STDMETHODCALLTYPE *GetFillBrush)(
        IXpsOMPath *This,
        IXpsOMBrush **brush);

    HRESULT (STDMETHODCALLTYPE *GetFillBrushLocal)(
        IXpsOMPath *This,
        IXpsOMBrush **brush);

    HRESULT (STDMETHODCALLTYPE *SetFillBrushLocal)(
        IXpsOMPath *This,
        IXpsOMBrush *brush);

    HRESULT (STDMETHODCALLTYPE *GetFillBrushLookup)(
        IXpsOMPath *This,
        LPWSTR *lookup);

    HRESULT (STDMETHODCALLTYPE *SetFillBrushLookup)(
        IXpsOMPath *This,
        LPCWSTR lookup);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMPath *This,
        IXpsOMPath **path);

    END_INTERFACE
} IXpsOMPathVtbl;

interface IXpsOMPath {
    CONST_VTBL IXpsOMPathVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPath_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPath_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPath_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMPath_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMPath_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMVisual methods ***/
#define IXpsOMPath_GetTransform(This,matrixTransform) (This)->lpVtbl->GetTransform(This,matrixTransform)
#define IXpsOMPath_GetTransformLocal(This,matrixTransform) (This)->lpVtbl->GetTransformLocal(This,matrixTransform)
#define IXpsOMPath_SetTransformLocal(This,matrixTransform) (This)->lpVtbl->SetTransformLocal(This,matrixTransform)
#define IXpsOMPath_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMPath_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMPath_GetClipGeometry(This,clipGeometry) (This)->lpVtbl->GetClipGeometry(This,clipGeometry)
#define IXpsOMPath_GetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->GetClipGeometryLocal(This,clipGeometry)
#define IXpsOMPath_SetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->SetClipGeometryLocal(This,clipGeometry)
#define IXpsOMPath_GetClipGeometryLookup(This,key) (This)->lpVtbl->GetClipGeometryLookup(This,key)
#define IXpsOMPath_SetClipGeometryLookup(This,key) (This)->lpVtbl->SetClipGeometryLookup(This,key)
#define IXpsOMPath_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMPath_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
#define IXpsOMPath_GetOpacityMaskBrush(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush)
#define IXpsOMPath_GetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMPath_SetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMPath_GetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->GetOpacityMaskBrushLookup(This,key)
#define IXpsOMPath_SetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->SetOpacityMaskBrushLookup(This,key)
#define IXpsOMPath_GetName(This,name) (This)->lpVtbl->GetName(This,name)
#define IXpsOMPath_SetName(This,name) (This)->lpVtbl->SetName(This,name)
#define IXpsOMPath_GetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMPath_SetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMPath_GetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMPath_SetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMPath_GetLanguage(This,language) (This)->lpVtbl->GetLanguage(This,language)
#define IXpsOMPath_SetLanguage(This,language) (This)->lpVtbl->SetLanguage(This,language)
/*** IXpsOMPath methods ***/
#define IXpsOMPath_GetGeometry(This,geometry) (This)->lpVtbl->GetGeometry(This,geometry)
#define IXpsOMPath_GetGeometryLocal(This,geometry) (This)->lpVtbl->GetGeometryLocal(This,geometry)
#define IXpsOMPath_SetGeometryLocal(This,geometry) (This)->lpVtbl->SetGeometryLocal(This,geometry)
#define IXpsOMPath_GetGeometryLookup(This,lookup) (This)->lpVtbl->GetGeometryLookup(This,lookup)
#define IXpsOMPath_SetGeometryLookup(This,lookup) (This)->lpVtbl->SetGeometryLookup(This,lookup)
#define IXpsOMPath_GetAccessibilityShortDescription(This,shortDescription) (This)->lpVtbl->GetAccessibilityShortDescription(This,shortDescription)
#define IXpsOMPath_SetAccessibilityShortDescription(This,shortDescription) (This)->lpVtbl->SetAccessibilityShortDescription(This,shortDescription)
#define IXpsOMPath_GetAccessibilityLongDescription(This,longDescription) (This)->lpVtbl->GetAccessibilityLongDescription(This,longDescription)
#define IXpsOMPath_SetAccessibilityLongDescription(This,longDescription) (This)->lpVtbl->SetAccessibilityLongDescription(This,longDescription)
#define IXpsOMPath_GetSnapsToPixels(This,snapsToPixels) (This)->lpVtbl->GetSnapsToPixels(This,snapsToPixels)
#define IXpsOMPath_SetSnapsToPixels(This,snapsToPixels) (This)->lpVtbl->SetSnapsToPixels(This,snapsToPixels)
#define IXpsOMPath_GetStrokeBrush(This,brush) (This)->lpVtbl->GetStrokeBrush(This,brush)
#define IXpsOMPath_GetStrokeBrushLocal(This,brush) (This)->lpVtbl->GetStrokeBrushLocal(This,brush)
#define IXpsOMPath_SetStrokeBrushLocal(This,brush) (This)->lpVtbl->SetStrokeBrushLocal(This,brush)
#define IXpsOMPath_GetStrokeBrushLookup(This,lookup) (This)->lpVtbl->GetStrokeBrushLookup(This,lookup)
#define IXpsOMPath_SetStrokeBrushLookup(This,lookup) (This)->lpVtbl->SetStrokeBrushLookup(This,lookup)
#define IXpsOMPath_GetStrokeDashes(This,strokeDashes) (This)->lpVtbl->GetStrokeDashes(This,strokeDashes)
#define IXpsOMPath_GetStrokeDashCap(This,strokeDashCap) (This)->lpVtbl->GetStrokeDashCap(This,strokeDashCap)
#define IXpsOMPath_SetStrokeDashCap(This,strokeDashCap) (This)->lpVtbl->SetStrokeDashCap(This,strokeDashCap)
#define IXpsOMPath_GetStrokeDashOffset(This,strokeDashOffset) (This)->lpVtbl->GetStrokeDashOffset(This,strokeDashOffset)
#define IXpsOMPath_SetStrokeDashOffset(This,strokeDashOffset) (This)->lpVtbl->SetStrokeDashOffset(This,strokeDashOffset)
#define IXpsOMPath_GetStrokeStartLineCap(This,strokeStartLineCap) (This)->lpVtbl->GetStrokeStartLineCap(This,strokeStartLineCap)
#define IXpsOMPath_SetStrokeStartLineCap(This,strokeStartLineCap) (This)->lpVtbl->SetStrokeStartLineCap(This,strokeStartLineCap)
#define IXpsOMPath_GetStrokeEndLineCap(This,strokeEndLineCap) (This)->lpVtbl->GetStrokeEndLineCap(This,strokeEndLineCap)
#define IXpsOMPath_SetStrokeEndLineCap(This,strokeEndLineCap) (This)->lpVtbl->SetStrokeEndLineCap(This,strokeEndLineCap)
#define IXpsOMPath_GetStrokeLineJoin(This,strokeLineJoin) (This)->lpVtbl->GetStrokeLineJoin(This,strokeLineJoin)
#define IXpsOMPath_SetStrokeLineJoin(This,strokeLineJoin) (This)->lpVtbl->SetStrokeLineJoin(This,strokeLineJoin)
#define IXpsOMPath_GetStrokeMiterLimit(This,strokeMiterLimit) (This)->lpVtbl->GetStrokeMiterLimit(This,strokeMiterLimit)
#define IXpsOMPath_SetStrokeMiterLimit(This,strokeMiterLimit) (This)->lpVtbl->SetStrokeMiterLimit(This,strokeMiterLimit)
#define IXpsOMPath_GetStrokeThickness(This,strokeThickness) (This)->lpVtbl->GetStrokeThickness(This,strokeThickness)
#define IXpsOMPath_SetStrokeThickness(This,strokeThickness) (This)->lpVtbl->SetStrokeThickness(This,strokeThickness)
#define IXpsOMPath_GetFillBrush(This,brush) (This)->lpVtbl->GetFillBrush(This,brush)
#define IXpsOMPath_GetFillBrushLocal(This,brush) (This)->lpVtbl->GetFillBrushLocal(This,brush)
#define IXpsOMPath_SetFillBrushLocal(This,brush) (This)->lpVtbl->SetFillBrushLocal(This,brush)
#define IXpsOMPath_GetFillBrushLookup(This,lookup) (This)->lpVtbl->GetFillBrushLookup(This,lookup)
#define IXpsOMPath_SetFillBrushLookup(This,lookup) (This)->lpVtbl->SetFillBrushLookup(This,lookup)
#define IXpsOMPath_Clone(This,path) (This)->lpVtbl->Clone(This,path)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPath_QueryInterface(IXpsOMPath* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPath_AddRef(IXpsOMPath* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPath_Release(IXpsOMPath* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMPath_GetOwner(IXpsOMPath* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMPath_GetType(IXpsOMPath* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMVisual methods ***/
static inline HRESULT IXpsOMPath_GetTransform(IXpsOMPath* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransform(This,matrixTransform);
}
static inline HRESULT IXpsOMPath_GetTransformLocal(IXpsOMPath* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMPath_SetTransformLocal(IXpsOMPath* This,IXpsOMMatrixTransform *matrixTransform) {
    return This->lpVtbl->SetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMPath_GetTransformLookup(IXpsOMPath* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMPath_SetTransformLookup(IXpsOMPath* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMPath_GetClipGeometry(IXpsOMPath* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometry(This,clipGeometry);
}
static inline HRESULT IXpsOMPath_GetClipGeometryLocal(IXpsOMPath* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMPath_SetClipGeometryLocal(IXpsOMPath* This,IXpsOMGeometry *clipGeometry) {
    return This->lpVtbl->SetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMPath_GetClipGeometryLookup(IXpsOMPath* This,LPWSTR *key) {
    return This->lpVtbl->GetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMPath_SetClipGeometryLookup(IXpsOMPath* This,LPCWSTR key) {
    return This->lpVtbl->SetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMPath_GetOpacity(IXpsOMPath* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMPath_SetOpacity(IXpsOMPath* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
static inline HRESULT IXpsOMPath_GetOpacityMaskBrush(IXpsOMPath* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMPath_GetOpacityMaskBrushLocal(IXpsOMPath* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMPath_SetOpacityMaskBrushLocal(IXpsOMPath* This,IXpsOMBrush *opacityMaskBrush) {
    return This->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMPath_GetOpacityMaskBrushLookup(IXpsOMPath* This,LPWSTR *key) {
    return This->lpVtbl->GetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMPath_SetOpacityMaskBrushLookup(IXpsOMPath* This,LPCWSTR key) {
    return This->lpVtbl->SetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMPath_GetName(IXpsOMPath* This,LPWSTR *name) {
    return This->lpVtbl->GetName(This,name);
}
static inline HRESULT IXpsOMPath_SetName(IXpsOMPath* This,LPCWSTR name) {
    return This->lpVtbl->SetName(This,name);
}
static inline HRESULT IXpsOMPath_GetIsHyperlinkTarget(IXpsOMPath* This,WINBOOL *isHyperlink) {
    return This->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMPath_SetIsHyperlinkTarget(IXpsOMPath* This,WINBOOL isHyperlink) {
    return This->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMPath_GetHyperlinkNavigateUri(IXpsOMPath* This,IUri **hyperlinkUri) {
    return This->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMPath_SetHyperlinkNavigateUri(IXpsOMPath* This,IUri *hyperlinkUri) {
    return This->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMPath_GetLanguage(IXpsOMPath* This,LPWSTR *language) {
    return This->lpVtbl->GetLanguage(This,language);
}
static inline HRESULT IXpsOMPath_SetLanguage(IXpsOMPath* This,LPCWSTR language) {
    return This->lpVtbl->SetLanguage(This,language);
}
/*** IXpsOMPath methods ***/
static inline HRESULT IXpsOMPath_GetGeometry(IXpsOMPath* This,IXpsOMGeometry **geometry) {
    return This->lpVtbl->GetGeometry(This,geometry);
}
static inline HRESULT IXpsOMPath_GetGeometryLocal(IXpsOMPath* This,IXpsOMGeometry **geometry) {
    return This->lpVtbl->GetGeometryLocal(This,geometry);
}
static inline HRESULT IXpsOMPath_SetGeometryLocal(IXpsOMPath* This,IXpsOMGeometry *geometry) {
    return This->lpVtbl->SetGeometryLocal(This,geometry);
}
static inline HRESULT IXpsOMPath_GetGeometryLookup(IXpsOMPath* This,LPWSTR *lookup) {
    return This->lpVtbl->GetGeometryLookup(This,lookup);
}
static inline HRESULT IXpsOMPath_SetGeometryLookup(IXpsOMPath* This,LPCWSTR lookup) {
    return This->lpVtbl->SetGeometryLookup(This,lookup);
}
static inline HRESULT IXpsOMPath_GetAccessibilityShortDescription(IXpsOMPath* This,LPWSTR *shortDescription) {
    return This->lpVtbl->GetAccessibilityShortDescription(This,shortDescription);
}
static inline HRESULT IXpsOMPath_SetAccessibilityShortDescription(IXpsOMPath* This,LPCWSTR shortDescription) {
    return This->lpVtbl->SetAccessibilityShortDescription(This,shortDescription);
}
static inline HRESULT IXpsOMPath_GetAccessibilityLongDescription(IXpsOMPath* This,LPWSTR *longDescription) {
    return This->lpVtbl->GetAccessibilityLongDescription(This,longDescription);
}
static inline HRESULT IXpsOMPath_SetAccessibilityLongDescription(IXpsOMPath* This,LPCWSTR longDescription) {
    return This->lpVtbl->SetAccessibilityLongDescription(This,longDescription);
}
static inline HRESULT IXpsOMPath_GetSnapsToPixels(IXpsOMPath* This,WINBOOL *snapsToPixels) {
    return This->lpVtbl->GetSnapsToPixels(This,snapsToPixels);
}
static inline HRESULT IXpsOMPath_SetSnapsToPixels(IXpsOMPath* This,WINBOOL snapsToPixels) {
    return This->lpVtbl->SetSnapsToPixels(This,snapsToPixels);
}
static inline HRESULT IXpsOMPath_GetStrokeBrush(IXpsOMPath* This,IXpsOMBrush **brush) {
    return This->lpVtbl->GetStrokeBrush(This,brush);
}
static inline HRESULT IXpsOMPath_GetStrokeBrushLocal(IXpsOMPath* This,IXpsOMBrush **brush) {
    return This->lpVtbl->GetStrokeBrushLocal(This,brush);
}
static inline HRESULT IXpsOMPath_SetStrokeBrushLocal(IXpsOMPath* This,IXpsOMBrush *brush) {
    return This->lpVtbl->SetStrokeBrushLocal(This,brush);
}
static inline HRESULT IXpsOMPath_GetStrokeBrushLookup(IXpsOMPath* This,LPWSTR *lookup) {
    return This->lpVtbl->GetStrokeBrushLookup(This,lookup);
}
static inline HRESULT IXpsOMPath_SetStrokeBrushLookup(IXpsOMPath* This,LPCWSTR lookup) {
    return This->lpVtbl->SetStrokeBrushLookup(This,lookup);
}
static inline HRESULT IXpsOMPath_GetStrokeDashes(IXpsOMPath* This,IXpsOMDashCollection **strokeDashes) {
    return This->lpVtbl->GetStrokeDashes(This,strokeDashes);
}
static inline HRESULT IXpsOMPath_GetStrokeDashCap(IXpsOMPath* This,XPS_DASH_CAP *strokeDashCap) {
    return This->lpVtbl->GetStrokeDashCap(This,strokeDashCap);
}
static inline HRESULT IXpsOMPath_SetStrokeDashCap(IXpsOMPath* This,XPS_DASH_CAP strokeDashCap) {
    return This->lpVtbl->SetStrokeDashCap(This,strokeDashCap);
}
static inline HRESULT IXpsOMPath_GetStrokeDashOffset(IXpsOMPath* This,FLOAT *strokeDashOffset) {
    return This->lpVtbl->GetStrokeDashOffset(This,strokeDashOffset);
}
static inline HRESULT IXpsOMPath_SetStrokeDashOffset(IXpsOMPath* This,FLOAT strokeDashOffset) {
    return This->lpVtbl->SetStrokeDashOffset(This,strokeDashOffset);
}
static inline HRESULT IXpsOMPath_GetStrokeStartLineCap(IXpsOMPath* This,XPS_LINE_CAP *strokeStartLineCap) {
    return This->lpVtbl->GetStrokeStartLineCap(This,strokeStartLineCap);
}
static inline HRESULT IXpsOMPath_SetStrokeStartLineCap(IXpsOMPath* This,XPS_LINE_CAP strokeStartLineCap) {
    return This->lpVtbl->SetStrokeStartLineCap(This,strokeStartLineCap);
}
static inline HRESULT IXpsOMPath_GetStrokeEndLineCap(IXpsOMPath* This,XPS_LINE_CAP *strokeEndLineCap) {
    return This->lpVtbl->GetStrokeEndLineCap(This,strokeEndLineCap);
}
static inline HRESULT IXpsOMPath_SetStrokeEndLineCap(IXpsOMPath* This,XPS_LINE_CAP strokeEndLineCap) {
    return This->lpVtbl->SetStrokeEndLineCap(This,strokeEndLineCap);
}
static inline HRESULT IXpsOMPath_GetStrokeLineJoin(IXpsOMPath* This,XPS_LINE_JOIN *strokeLineJoin) {
    return This->lpVtbl->GetStrokeLineJoin(This,strokeLineJoin);
}
static inline HRESULT IXpsOMPath_SetStrokeLineJoin(IXpsOMPath* This,XPS_LINE_JOIN strokeLineJoin) {
    return This->lpVtbl->SetStrokeLineJoin(This,strokeLineJoin);
}
static inline HRESULT IXpsOMPath_GetStrokeMiterLimit(IXpsOMPath* This,FLOAT *strokeMiterLimit) {
    return This->lpVtbl->GetStrokeMiterLimit(This,strokeMiterLimit);
}
static inline HRESULT IXpsOMPath_SetStrokeMiterLimit(IXpsOMPath* This,FLOAT strokeMiterLimit) {
    return This->lpVtbl->SetStrokeMiterLimit(This,strokeMiterLimit);
}
static inline HRESULT IXpsOMPath_GetStrokeThickness(IXpsOMPath* This,FLOAT *strokeThickness) {
    return This->lpVtbl->GetStrokeThickness(This,strokeThickness);
}
static inline HRESULT IXpsOMPath_SetStrokeThickness(IXpsOMPath* This,FLOAT strokeThickness) {
    return This->lpVtbl->SetStrokeThickness(This,strokeThickness);
}
static inline HRESULT IXpsOMPath_GetFillBrush(IXpsOMPath* This,IXpsOMBrush **brush) {
    return This->lpVtbl->GetFillBrush(This,brush);
}
static inline HRESULT IXpsOMPath_GetFillBrushLocal(IXpsOMPath* This,IXpsOMBrush **brush) {
    return This->lpVtbl->GetFillBrushLocal(This,brush);
}
static inline HRESULT IXpsOMPath_SetFillBrushLocal(IXpsOMPath* This,IXpsOMBrush *brush) {
    return This->lpVtbl->SetFillBrushLocal(This,brush);
}
static inline HRESULT IXpsOMPath_GetFillBrushLookup(IXpsOMPath* This,LPWSTR *lookup) {
    return This->lpVtbl->GetFillBrushLookup(This,lookup);
}
static inline HRESULT IXpsOMPath_SetFillBrushLookup(IXpsOMPath* This,LPCWSTR lookup) {
    return This->lpVtbl->SetFillBrushLookup(This,lookup);
}
static inline HRESULT IXpsOMPath_Clone(IXpsOMPath* This,IXpsOMPath **path) {
    return This->lpVtbl->Clone(This,path);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPath_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMSolidColorBrush interface
 */
#ifndef __IXpsOMSolidColorBrush_INTERFACE_DEFINED__
#define __IXpsOMSolidColorBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMSolidColorBrush, 0xa06f9f05, 0x3be9, 0x4763, 0x98,0xa8, 0x09,0x4f,0xc6,0x72,0xe4,0x88);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a06f9f05-3be9-4763-98a8-094fc672e488")
IXpsOMSolidColorBrush : public IXpsOMBrush
{
    virtual HRESULT STDMETHODCALLTYPE GetColor(
        XPS_COLOR *color,
        IXpsOMColorProfileResource **colorProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColor(
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMSolidColorBrush **solidColorBrush) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMSolidColorBrush, 0xa06f9f05, 0x3be9, 0x4763, 0x98,0xa8, 0x09,0x4f,0xc6,0x72,0xe4,0x88)
#endif
#else
typedef struct IXpsOMSolidColorBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMSolidColorBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMSolidColorBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMSolidColorBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMSolidColorBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMSolidColorBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMSolidColorBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMSolidColorBrush *This,
        FLOAT opacity);

    /*** IXpsOMSolidColorBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetColor)(
        IXpsOMSolidColorBrush *This,
        XPS_COLOR *color,
        IXpsOMColorProfileResource **colorProfile);

    HRESULT (STDMETHODCALLTYPE *SetColor)(
        IXpsOMSolidColorBrush *This,
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMSolidColorBrush *This,
        IXpsOMSolidColorBrush **solidColorBrush);

    END_INTERFACE
} IXpsOMSolidColorBrushVtbl;

interface IXpsOMSolidColorBrush {
    CONST_VTBL IXpsOMSolidColorBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMSolidColorBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMSolidColorBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMSolidColorBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMSolidColorBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMSolidColorBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMSolidColorBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMSolidColorBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
/*** IXpsOMSolidColorBrush methods ***/
#define IXpsOMSolidColorBrush_GetColor(This,color,colorProfile) (This)->lpVtbl->GetColor(This,color,colorProfile)
#define IXpsOMSolidColorBrush_SetColor(This,color,colorProfile) (This)->lpVtbl->SetColor(This,color,colorProfile)
#define IXpsOMSolidColorBrush_Clone(This,solidColorBrush) (This)->lpVtbl->Clone(This,solidColorBrush)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMSolidColorBrush_QueryInterface(IXpsOMSolidColorBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMSolidColorBrush_AddRef(IXpsOMSolidColorBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMSolidColorBrush_Release(IXpsOMSolidColorBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMSolidColorBrush_GetOwner(IXpsOMSolidColorBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMSolidColorBrush_GetType(IXpsOMSolidColorBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMSolidColorBrush_GetOpacity(IXpsOMSolidColorBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMSolidColorBrush_SetOpacity(IXpsOMSolidColorBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
/*** IXpsOMSolidColorBrush methods ***/
static inline HRESULT IXpsOMSolidColorBrush_GetColor(IXpsOMSolidColorBrush* This,XPS_COLOR *color,IXpsOMColorProfileResource **colorProfile) {
    return This->lpVtbl->GetColor(This,color,colorProfile);
}
static inline HRESULT IXpsOMSolidColorBrush_SetColor(IXpsOMSolidColorBrush* This,const XPS_COLOR *color,IXpsOMColorProfileResource *colorProfile) {
    return This->lpVtbl->SetColor(This,color,colorProfile);
}
static inline HRESULT IXpsOMSolidColorBrush_Clone(IXpsOMSolidColorBrush* This,IXpsOMSolidColorBrush **solidColorBrush) {
    return This->lpVtbl->Clone(This,solidColorBrush);
}
#endif
#endif

#endif


#endif  /* __IXpsOMSolidColorBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMTileBrush interface
 */
#ifndef __IXpsOMTileBrush_INTERFACE_DEFINED__
#define __IXpsOMTileBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMTileBrush, 0x0fc2328d, 0xd722, 0x4a54, 0xb2,0xec, 0xbe,0x90,0x21,0x8a,0x78,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0fc2328d-d722-4a54-b2ec-be90218a789e")
IXpsOMTileBrush : public IXpsOMBrush
{
    virtual HRESULT STDMETHODCALLTYPE GetTransform(
        IXpsOMMatrixTransform **transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLocal(
        IXpsOMMatrixTransform **transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLocal(
        IXpsOMMatrixTransform *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLookup(
        LPWSTR *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLookup(
        LPCWSTR key) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetViewbox(
        XPS_RECT *viewbox) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetViewbox(
        const XPS_RECT *viewbox) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetViewport(
        XPS_RECT *viewport) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetViewport(
        const XPS_RECT *viewport) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTileMode(
        XPS_TILE_MODE *tileMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTileMode(
        XPS_TILE_MODE tileMode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMTileBrush, 0x0fc2328d, 0xd722, 0x4a54, 0xb2,0xec, 0xbe,0x90,0x21,0x8a,0x78,0x9e)
#endif
#else
typedef struct IXpsOMTileBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMTileBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMTileBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMTileBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMTileBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMTileBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMTileBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMTileBrush *This,
        FLOAT opacity);

    /*** IXpsOMTileBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMTileBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMTileBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMTileBrush *This,
        IXpsOMMatrixTransform *transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMTileBrush *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMTileBrush *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetViewbox)(
        IXpsOMTileBrush *This,
        XPS_RECT *viewbox);

    HRESULT (STDMETHODCALLTYPE *SetViewbox)(
        IXpsOMTileBrush *This,
        const XPS_RECT *viewbox);

    HRESULT (STDMETHODCALLTYPE *GetViewport)(
        IXpsOMTileBrush *This,
        XPS_RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *SetViewport)(
        IXpsOMTileBrush *This,
        const XPS_RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *GetTileMode)(
        IXpsOMTileBrush *This,
        XPS_TILE_MODE *tileMode);

    HRESULT (STDMETHODCALLTYPE *SetTileMode)(
        IXpsOMTileBrush *This,
        XPS_TILE_MODE tileMode);

    END_INTERFACE
} IXpsOMTileBrushVtbl;

interface IXpsOMTileBrush {
    CONST_VTBL IXpsOMTileBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMTileBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMTileBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMTileBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMTileBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMTileBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMTileBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMTileBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
/*** IXpsOMTileBrush methods ***/
#define IXpsOMTileBrush_GetTransform(This,transform) (This)->lpVtbl->GetTransform(This,transform)
#define IXpsOMTileBrush_GetTransformLocal(This,transform) (This)->lpVtbl->GetTransformLocal(This,transform)
#define IXpsOMTileBrush_SetTransformLocal(This,transform) (This)->lpVtbl->SetTransformLocal(This,transform)
#define IXpsOMTileBrush_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMTileBrush_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMTileBrush_GetViewbox(This,viewbox) (This)->lpVtbl->GetViewbox(This,viewbox)
#define IXpsOMTileBrush_SetViewbox(This,viewbox) (This)->lpVtbl->SetViewbox(This,viewbox)
#define IXpsOMTileBrush_GetViewport(This,viewport) (This)->lpVtbl->GetViewport(This,viewport)
#define IXpsOMTileBrush_SetViewport(This,viewport) (This)->lpVtbl->SetViewport(This,viewport)
#define IXpsOMTileBrush_GetTileMode(This,tileMode) (This)->lpVtbl->GetTileMode(This,tileMode)
#define IXpsOMTileBrush_SetTileMode(This,tileMode) (This)->lpVtbl->SetTileMode(This,tileMode)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMTileBrush_QueryInterface(IXpsOMTileBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMTileBrush_AddRef(IXpsOMTileBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMTileBrush_Release(IXpsOMTileBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMTileBrush_GetOwner(IXpsOMTileBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMTileBrush_GetType(IXpsOMTileBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMTileBrush_GetOpacity(IXpsOMTileBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMTileBrush_SetOpacity(IXpsOMTileBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
/*** IXpsOMTileBrush methods ***/
static inline HRESULT IXpsOMTileBrush_GetTransform(IXpsOMTileBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransform(This,transform);
}
static inline HRESULT IXpsOMTileBrush_GetTransformLocal(IXpsOMTileBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMTileBrush_SetTransformLocal(IXpsOMTileBrush* This,IXpsOMMatrixTransform *transform) {
    return This->lpVtbl->SetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMTileBrush_GetTransformLookup(IXpsOMTileBrush* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMTileBrush_SetTransformLookup(IXpsOMTileBrush* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMTileBrush_GetViewbox(IXpsOMTileBrush* This,XPS_RECT *viewbox) {
    return This->lpVtbl->GetViewbox(This,viewbox);
}
static inline HRESULT IXpsOMTileBrush_SetViewbox(IXpsOMTileBrush* This,const XPS_RECT *viewbox) {
    return This->lpVtbl->SetViewbox(This,viewbox);
}
static inline HRESULT IXpsOMTileBrush_GetViewport(IXpsOMTileBrush* This,XPS_RECT *viewport) {
    return This->lpVtbl->GetViewport(This,viewport);
}
static inline HRESULT IXpsOMTileBrush_SetViewport(IXpsOMTileBrush* This,const XPS_RECT *viewport) {
    return This->lpVtbl->SetViewport(This,viewport);
}
static inline HRESULT IXpsOMTileBrush_GetTileMode(IXpsOMTileBrush* This,XPS_TILE_MODE *tileMode) {
    return This->lpVtbl->GetTileMode(This,tileMode);
}
static inline HRESULT IXpsOMTileBrush_SetTileMode(IXpsOMTileBrush* This,XPS_TILE_MODE tileMode) {
    return This->lpVtbl->SetTileMode(This,tileMode);
}
#endif
#endif

#endif


#endif  /* __IXpsOMTileBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMGradientBrush interface
 */
#ifndef __IXpsOMGradientBrush_INTERFACE_DEFINED__
#define __IXpsOMGradientBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMGradientBrush, 0xedb59622, 0x61a2, 0x42c3, 0xba,0xce, 0xac,0xf2,0x28,0x6c,0x06,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("edb59622-61a2-42c3-bace-acf2286c06bf")
IXpsOMGradientBrush : public IXpsOMBrush
{
    virtual HRESULT STDMETHODCALLTYPE GetGradientStops(
        IXpsOMGradientStopCollection **gradientStops) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransform(
        IXpsOMMatrixTransform **transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLocal(
        IXpsOMMatrixTransform **transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLocal(
        IXpsOMMatrixTransform *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformLookup(
        LPWSTR *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransformLookup(
        LPCWSTR key) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSpreadMethod(
        XPS_SPREAD_METHOD *spreadMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSpreadMethod(
        XPS_SPREAD_METHOD spreadMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorInterpolationMode(
        XPS_COLOR_INTERPOLATION *colorInterpolationMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColorInterpolationMode(
        XPS_COLOR_INTERPOLATION colorInterpolationMode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMGradientBrush, 0xedb59622, 0x61a2, 0x42c3, 0xba,0xce, 0xac,0xf2,0x28,0x6c,0x06,0xbf)
#endif
#else
typedef struct IXpsOMGradientBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMGradientBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMGradientBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMGradientBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMGradientBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMGradientBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMGradientBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMGradientBrush *This,
        FLOAT opacity);

    /*** IXpsOMGradientBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGradientStops)(
        IXpsOMGradientBrush *This,
        IXpsOMGradientStopCollection **gradientStops);

    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMGradientBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMGradientBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMGradientBrush *This,
        IXpsOMMatrixTransform *transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMGradientBrush *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMGradientBrush *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetSpreadMethod)(
        IXpsOMGradientBrush *This,
        XPS_SPREAD_METHOD *spreadMethod);

    HRESULT (STDMETHODCALLTYPE *SetSpreadMethod)(
        IXpsOMGradientBrush *This,
        XPS_SPREAD_METHOD spreadMethod);

    HRESULT (STDMETHODCALLTYPE *GetColorInterpolationMode)(
        IXpsOMGradientBrush *This,
        XPS_COLOR_INTERPOLATION *colorInterpolationMode);

    HRESULT (STDMETHODCALLTYPE *SetColorInterpolationMode)(
        IXpsOMGradientBrush *This,
        XPS_COLOR_INTERPOLATION colorInterpolationMode);

    END_INTERFACE
} IXpsOMGradientBrushVtbl;

interface IXpsOMGradientBrush {
    CONST_VTBL IXpsOMGradientBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMGradientBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMGradientBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMGradientBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMGradientBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMGradientBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMGradientBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMGradientBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
/*** IXpsOMGradientBrush methods ***/
#define IXpsOMGradientBrush_GetGradientStops(This,gradientStops) (This)->lpVtbl->GetGradientStops(This,gradientStops)
#define IXpsOMGradientBrush_GetTransform(This,transform) (This)->lpVtbl->GetTransform(This,transform)
#define IXpsOMGradientBrush_GetTransformLocal(This,transform) (This)->lpVtbl->GetTransformLocal(This,transform)
#define IXpsOMGradientBrush_SetTransformLocal(This,transform) (This)->lpVtbl->SetTransformLocal(This,transform)
#define IXpsOMGradientBrush_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMGradientBrush_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMGradientBrush_GetSpreadMethod(This,spreadMethod) (This)->lpVtbl->GetSpreadMethod(This,spreadMethod)
#define IXpsOMGradientBrush_SetSpreadMethod(This,spreadMethod) (This)->lpVtbl->SetSpreadMethod(This,spreadMethod)
#define IXpsOMGradientBrush_GetColorInterpolationMode(This,colorInterpolationMode) (This)->lpVtbl->GetColorInterpolationMode(This,colorInterpolationMode)
#define IXpsOMGradientBrush_SetColorInterpolationMode(This,colorInterpolationMode) (This)->lpVtbl->SetColorInterpolationMode(This,colorInterpolationMode)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMGradientBrush_QueryInterface(IXpsOMGradientBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMGradientBrush_AddRef(IXpsOMGradientBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMGradientBrush_Release(IXpsOMGradientBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMGradientBrush_GetOwner(IXpsOMGradientBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMGradientBrush_GetType(IXpsOMGradientBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMGradientBrush_GetOpacity(IXpsOMGradientBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMGradientBrush_SetOpacity(IXpsOMGradientBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
/*** IXpsOMGradientBrush methods ***/
static inline HRESULT IXpsOMGradientBrush_GetGradientStops(IXpsOMGradientBrush* This,IXpsOMGradientStopCollection **gradientStops) {
    return This->lpVtbl->GetGradientStops(This,gradientStops);
}
static inline HRESULT IXpsOMGradientBrush_GetTransform(IXpsOMGradientBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransform(This,transform);
}
static inline HRESULT IXpsOMGradientBrush_GetTransformLocal(IXpsOMGradientBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMGradientBrush_SetTransformLocal(IXpsOMGradientBrush* This,IXpsOMMatrixTransform *transform) {
    return This->lpVtbl->SetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMGradientBrush_GetTransformLookup(IXpsOMGradientBrush* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMGradientBrush_SetTransformLookup(IXpsOMGradientBrush* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMGradientBrush_GetSpreadMethod(IXpsOMGradientBrush* This,XPS_SPREAD_METHOD *spreadMethod) {
    return This->lpVtbl->GetSpreadMethod(This,spreadMethod);
}
static inline HRESULT IXpsOMGradientBrush_SetSpreadMethod(IXpsOMGradientBrush* This,XPS_SPREAD_METHOD spreadMethod) {
    return This->lpVtbl->SetSpreadMethod(This,spreadMethod);
}
static inline HRESULT IXpsOMGradientBrush_GetColorInterpolationMode(IXpsOMGradientBrush* This,XPS_COLOR_INTERPOLATION *colorInterpolationMode) {
    return This->lpVtbl->GetColorInterpolationMode(This,colorInterpolationMode);
}
static inline HRESULT IXpsOMGradientBrush_SetColorInterpolationMode(IXpsOMGradientBrush* This,XPS_COLOR_INTERPOLATION colorInterpolationMode) {
    return This->lpVtbl->SetColorInterpolationMode(This,colorInterpolationMode);
}
#endif
#endif

#endif


#endif  /* __IXpsOMGradientBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMVisualBrush interface
 */
#ifndef __IXpsOMVisualBrush_INTERFACE_DEFINED__
#define __IXpsOMVisualBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMVisualBrush, 0x97e294af, 0x5b37, 0x46b4, 0x80,0x57, 0x87,0x4d,0x2f,0x64,0x11,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("97e294af-5b37-46b4-8057-874d2f64119b")
IXpsOMVisualBrush : public IXpsOMTileBrush
{
    virtual HRESULT STDMETHODCALLTYPE GetVisual(
        IXpsOMVisual **visual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVisualLocal(
        IXpsOMVisual **visual) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVisualLocal(
        IXpsOMVisual *visual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVisualLookup(
        LPWSTR *lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVisualLookup(
        LPCWSTR lookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMVisualBrush **visualBrush) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMVisualBrush, 0x97e294af, 0x5b37, 0x46b4, 0x80,0x57, 0x87,0x4d,0x2f,0x64,0x11,0x9b)
#endif
#else
typedef struct IXpsOMVisualBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMVisualBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMVisualBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMVisualBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMVisualBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMVisualBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMVisualBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMVisualBrush *This,
        FLOAT opacity);

    /*** IXpsOMTileBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMVisualBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMVisualBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMVisualBrush *This,
        IXpsOMMatrixTransform *transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMVisualBrush *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMVisualBrush *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetViewbox)(
        IXpsOMVisualBrush *This,
        XPS_RECT *viewbox);

    HRESULT (STDMETHODCALLTYPE *SetViewbox)(
        IXpsOMVisualBrush *This,
        const XPS_RECT *viewbox);

    HRESULT (STDMETHODCALLTYPE *GetViewport)(
        IXpsOMVisualBrush *This,
        XPS_RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *SetViewport)(
        IXpsOMVisualBrush *This,
        const XPS_RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *GetTileMode)(
        IXpsOMVisualBrush *This,
        XPS_TILE_MODE *tileMode);

    HRESULT (STDMETHODCALLTYPE *SetTileMode)(
        IXpsOMVisualBrush *This,
        XPS_TILE_MODE tileMode);

    /*** IXpsOMVisualBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVisual)(
        IXpsOMVisualBrush *This,
        IXpsOMVisual **visual);

    HRESULT (STDMETHODCALLTYPE *GetVisualLocal)(
        IXpsOMVisualBrush *This,
        IXpsOMVisual **visual);

    HRESULT (STDMETHODCALLTYPE *SetVisualLocal)(
        IXpsOMVisualBrush *This,
        IXpsOMVisual *visual);

    HRESULT (STDMETHODCALLTYPE *GetVisualLookup)(
        IXpsOMVisualBrush *This,
        LPWSTR *lookup);

    HRESULT (STDMETHODCALLTYPE *SetVisualLookup)(
        IXpsOMVisualBrush *This,
        LPCWSTR lookup);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMVisualBrush *This,
        IXpsOMVisualBrush **visualBrush);

    END_INTERFACE
} IXpsOMVisualBrushVtbl;

interface IXpsOMVisualBrush {
    CONST_VTBL IXpsOMVisualBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMVisualBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMVisualBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMVisualBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMVisualBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMVisualBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMVisualBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMVisualBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
/*** IXpsOMTileBrush methods ***/
#define IXpsOMVisualBrush_GetTransform(This,transform) (This)->lpVtbl->GetTransform(This,transform)
#define IXpsOMVisualBrush_GetTransformLocal(This,transform) (This)->lpVtbl->GetTransformLocal(This,transform)
#define IXpsOMVisualBrush_SetTransformLocal(This,transform) (This)->lpVtbl->SetTransformLocal(This,transform)
#define IXpsOMVisualBrush_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMVisualBrush_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMVisualBrush_GetViewbox(This,viewbox) (This)->lpVtbl->GetViewbox(This,viewbox)
#define IXpsOMVisualBrush_SetViewbox(This,viewbox) (This)->lpVtbl->SetViewbox(This,viewbox)
#define IXpsOMVisualBrush_GetViewport(This,viewport) (This)->lpVtbl->GetViewport(This,viewport)
#define IXpsOMVisualBrush_SetViewport(This,viewport) (This)->lpVtbl->SetViewport(This,viewport)
#define IXpsOMVisualBrush_GetTileMode(This,tileMode) (This)->lpVtbl->GetTileMode(This,tileMode)
#define IXpsOMVisualBrush_SetTileMode(This,tileMode) (This)->lpVtbl->SetTileMode(This,tileMode)
/*** IXpsOMVisualBrush methods ***/
#define IXpsOMVisualBrush_GetVisual(This,visual) (This)->lpVtbl->GetVisual(This,visual)
#define IXpsOMVisualBrush_GetVisualLocal(This,visual) (This)->lpVtbl->GetVisualLocal(This,visual)
#define IXpsOMVisualBrush_SetVisualLocal(This,visual) (This)->lpVtbl->SetVisualLocal(This,visual)
#define IXpsOMVisualBrush_GetVisualLookup(This,lookup) (This)->lpVtbl->GetVisualLookup(This,lookup)
#define IXpsOMVisualBrush_SetVisualLookup(This,lookup) (This)->lpVtbl->SetVisualLookup(This,lookup)
#define IXpsOMVisualBrush_Clone(This,visualBrush) (This)->lpVtbl->Clone(This,visualBrush)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMVisualBrush_QueryInterface(IXpsOMVisualBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMVisualBrush_AddRef(IXpsOMVisualBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMVisualBrush_Release(IXpsOMVisualBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMVisualBrush_GetOwner(IXpsOMVisualBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMVisualBrush_GetType(IXpsOMVisualBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMVisualBrush_GetOpacity(IXpsOMVisualBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMVisualBrush_SetOpacity(IXpsOMVisualBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
/*** IXpsOMTileBrush methods ***/
static inline HRESULT IXpsOMVisualBrush_GetTransform(IXpsOMVisualBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransform(This,transform);
}
static inline HRESULT IXpsOMVisualBrush_GetTransformLocal(IXpsOMVisualBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMVisualBrush_SetTransformLocal(IXpsOMVisualBrush* This,IXpsOMMatrixTransform *transform) {
    return This->lpVtbl->SetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMVisualBrush_GetTransformLookup(IXpsOMVisualBrush* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMVisualBrush_SetTransformLookup(IXpsOMVisualBrush* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMVisualBrush_GetViewbox(IXpsOMVisualBrush* This,XPS_RECT *viewbox) {
    return This->lpVtbl->GetViewbox(This,viewbox);
}
static inline HRESULT IXpsOMVisualBrush_SetViewbox(IXpsOMVisualBrush* This,const XPS_RECT *viewbox) {
    return This->lpVtbl->SetViewbox(This,viewbox);
}
static inline HRESULT IXpsOMVisualBrush_GetViewport(IXpsOMVisualBrush* This,XPS_RECT *viewport) {
    return This->lpVtbl->GetViewport(This,viewport);
}
static inline HRESULT IXpsOMVisualBrush_SetViewport(IXpsOMVisualBrush* This,const XPS_RECT *viewport) {
    return This->lpVtbl->SetViewport(This,viewport);
}
static inline HRESULT IXpsOMVisualBrush_GetTileMode(IXpsOMVisualBrush* This,XPS_TILE_MODE *tileMode) {
    return This->lpVtbl->GetTileMode(This,tileMode);
}
static inline HRESULT IXpsOMVisualBrush_SetTileMode(IXpsOMVisualBrush* This,XPS_TILE_MODE tileMode) {
    return This->lpVtbl->SetTileMode(This,tileMode);
}
/*** IXpsOMVisualBrush methods ***/
static inline HRESULT IXpsOMVisualBrush_GetVisual(IXpsOMVisualBrush* This,IXpsOMVisual **visual) {
    return This->lpVtbl->GetVisual(This,visual);
}
static inline HRESULT IXpsOMVisualBrush_GetVisualLocal(IXpsOMVisualBrush* This,IXpsOMVisual **visual) {
    return This->lpVtbl->GetVisualLocal(This,visual);
}
static inline HRESULT IXpsOMVisualBrush_SetVisualLocal(IXpsOMVisualBrush* This,IXpsOMVisual *visual) {
    return This->lpVtbl->SetVisualLocal(This,visual);
}
static inline HRESULT IXpsOMVisualBrush_GetVisualLookup(IXpsOMVisualBrush* This,LPWSTR *lookup) {
    return This->lpVtbl->GetVisualLookup(This,lookup);
}
static inline HRESULT IXpsOMVisualBrush_SetVisualLookup(IXpsOMVisualBrush* This,LPCWSTR lookup) {
    return This->lpVtbl->SetVisualLookup(This,lookup);
}
static inline HRESULT IXpsOMVisualBrush_Clone(IXpsOMVisualBrush* This,IXpsOMVisualBrush **visualBrush) {
    return This->lpVtbl->Clone(This,visualBrush);
}
#endif
#endif

#endif


#endif  /* __IXpsOMVisualBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMImageBrush interface
 */
#ifndef __IXpsOMImageBrush_INTERFACE_DEFINED__
#define __IXpsOMImageBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMImageBrush, 0x3df0b466, 0xd382, 0x49ef, 0x85,0x50, 0xdd,0x94,0xc8,0x02,0x42,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3df0b466-d382-49ef-8550-dd94c80242e4")
IXpsOMImageBrush : public IXpsOMTileBrush
{
    virtual HRESULT STDMETHODCALLTYPE GetImageResource(
        IXpsOMImageResource **imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetImageResource(
        IXpsOMImageResource *imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorProfileResource(
        IXpsOMColorProfileResource **colorProfileResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColorProfileResource(
        IXpsOMColorProfileResource *colorProfileResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMImageBrush **imageBrush) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMImageBrush, 0x3df0b466, 0xd382, 0x49ef, 0x85,0x50, 0xdd,0x94,0xc8,0x02,0x42,0xe4)
#endif
#else
typedef struct IXpsOMImageBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMImageBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMImageBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMImageBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMImageBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMImageBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMImageBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMImageBrush *This,
        FLOAT opacity);

    /*** IXpsOMTileBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMImageBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMImageBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMImageBrush *This,
        IXpsOMMatrixTransform *transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMImageBrush *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMImageBrush *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetViewbox)(
        IXpsOMImageBrush *This,
        XPS_RECT *viewbox);

    HRESULT (STDMETHODCALLTYPE *SetViewbox)(
        IXpsOMImageBrush *This,
        const XPS_RECT *viewbox);

    HRESULT (STDMETHODCALLTYPE *GetViewport)(
        IXpsOMImageBrush *This,
        XPS_RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *SetViewport)(
        IXpsOMImageBrush *This,
        const XPS_RECT *viewport);

    HRESULT (STDMETHODCALLTYPE *GetTileMode)(
        IXpsOMImageBrush *This,
        XPS_TILE_MODE *tileMode);

    HRESULT (STDMETHODCALLTYPE *SetTileMode)(
        IXpsOMImageBrush *This,
        XPS_TILE_MODE tileMode);

    /*** IXpsOMImageBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetImageResource)(
        IXpsOMImageBrush *This,
        IXpsOMImageResource **imageResource);

    HRESULT (STDMETHODCALLTYPE *SetImageResource)(
        IXpsOMImageBrush *This,
        IXpsOMImageResource *imageResource);

    HRESULT (STDMETHODCALLTYPE *GetColorProfileResource)(
        IXpsOMImageBrush *This,
        IXpsOMColorProfileResource **colorProfileResource);

    HRESULT (STDMETHODCALLTYPE *SetColorProfileResource)(
        IXpsOMImageBrush *This,
        IXpsOMColorProfileResource *colorProfileResource);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMImageBrush *This,
        IXpsOMImageBrush **imageBrush);

    END_INTERFACE
} IXpsOMImageBrushVtbl;

interface IXpsOMImageBrush {
    CONST_VTBL IXpsOMImageBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMImageBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMImageBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMImageBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMImageBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMImageBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMImageBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMImageBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
/*** IXpsOMTileBrush methods ***/
#define IXpsOMImageBrush_GetTransform(This,transform) (This)->lpVtbl->GetTransform(This,transform)
#define IXpsOMImageBrush_GetTransformLocal(This,transform) (This)->lpVtbl->GetTransformLocal(This,transform)
#define IXpsOMImageBrush_SetTransformLocal(This,transform) (This)->lpVtbl->SetTransformLocal(This,transform)
#define IXpsOMImageBrush_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMImageBrush_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMImageBrush_GetViewbox(This,viewbox) (This)->lpVtbl->GetViewbox(This,viewbox)
#define IXpsOMImageBrush_SetViewbox(This,viewbox) (This)->lpVtbl->SetViewbox(This,viewbox)
#define IXpsOMImageBrush_GetViewport(This,viewport) (This)->lpVtbl->GetViewport(This,viewport)
#define IXpsOMImageBrush_SetViewport(This,viewport) (This)->lpVtbl->SetViewport(This,viewport)
#define IXpsOMImageBrush_GetTileMode(This,tileMode) (This)->lpVtbl->GetTileMode(This,tileMode)
#define IXpsOMImageBrush_SetTileMode(This,tileMode) (This)->lpVtbl->SetTileMode(This,tileMode)
/*** IXpsOMImageBrush methods ***/
#define IXpsOMImageBrush_GetImageResource(This,imageResource) (This)->lpVtbl->GetImageResource(This,imageResource)
#define IXpsOMImageBrush_SetImageResource(This,imageResource) (This)->lpVtbl->SetImageResource(This,imageResource)
#define IXpsOMImageBrush_GetColorProfileResource(This,colorProfileResource) (This)->lpVtbl->GetColorProfileResource(This,colorProfileResource)
#define IXpsOMImageBrush_SetColorProfileResource(This,colorProfileResource) (This)->lpVtbl->SetColorProfileResource(This,colorProfileResource)
#define IXpsOMImageBrush_Clone(This,imageBrush) (This)->lpVtbl->Clone(This,imageBrush)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMImageBrush_QueryInterface(IXpsOMImageBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMImageBrush_AddRef(IXpsOMImageBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMImageBrush_Release(IXpsOMImageBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMImageBrush_GetOwner(IXpsOMImageBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMImageBrush_GetType(IXpsOMImageBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMImageBrush_GetOpacity(IXpsOMImageBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMImageBrush_SetOpacity(IXpsOMImageBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
/*** IXpsOMTileBrush methods ***/
static inline HRESULT IXpsOMImageBrush_GetTransform(IXpsOMImageBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransform(This,transform);
}
static inline HRESULT IXpsOMImageBrush_GetTransformLocal(IXpsOMImageBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMImageBrush_SetTransformLocal(IXpsOMImageBrush* This,IXpsOMMatrixTransform *transform) {
    return This->lpVtbl->SetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMImageBrush_GetTransformLookup(IXpsOMImageBrush* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMImageBrush_SetTransformLookup(IXpsOMImageBrush* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMImageBrush_GetViewbox(IXpsOMImageBrush* This,XPS_RECT *viewbox) {
    return This->lpVtbl->GetViewbox(This,viewbox);
}
static inline HRESULT IXpsOMImageBrush_SetViewbox(IXpsOMImageBrush* This,const XPS_RECT *viewbox) {
    return This->lpVtbl->SetViewbox(This,viewbox);
}
static inline HRESULT IXpsOMImageBrush_GetViewport(IXpsOMImageBrush* This,XPS_RECT *viewport) {
    return This->lpVtbl->GetViewport(This,viewport);
}
static inline HRESULT IXpsOMImageBrush_SetViewport(IXpsOMImageBrush* This,const XPS_RECT *viewport) {
    return This->lpVtbl->SetViewport(This,viewport);
}
static inline HRESULT IXpsOMImageBrush_GetTileMode(IXpsOMImageBrush* This,XPS_TILE_MODE *tileMode) {
    return This->lpVtbl->GetTileMode(This,tileMode);
}
static inline HRESULT IXpsOMImageBrush_SetTileMode(IXpsOMImageBrush* This,XPS_TILE_MODE tileMode) {
    return This->lpVtbl->SetTileMode(This,tileMode);
}
/*** IXpsOMImageBrush methods ***/
static inline HRESULT IXpsOMImageBrush_GetImageResource(IXpsOMImageBrush* This,IXpsOMImageResource **imageResource) {
    return This->lpVtbl->GetImageResource(This,imageResource);
}
static inline HRESULT IXpsOMImageBrush_SetImageResource(IXpsOMImageBrush* This,IXpsOMImageResource *imageResource) {
    return This->lpVtbl->SetImageResource(This,imageResource);
}
static inline HRESULT IXpsOMImageBrush_GetColorProfileResource(IXpsOMImageBrush* This,IXpsOMColorProfileResource **colorProfileResource) {
    return This->lpVtbl->GetColorProfileResource(This,colorProfileResource);
}
static inline HRESULT IXpsOMImageBrush_SetColorProfileResource(IXpsOMImageBrush* This,IXpsOMColorProfileResource *colorProfileResource) {
    return This->lpVtbl->SetColorProfileResource(This,colorProfileResource);
}
static inline HRESULT IXpsOMImageBrush_Clone(IXpsOMImageBrush* This,IXpsOMImageBrush **imageBrush) {
    return This->lpVtbl->Clone(This,imageBrush);
}
#endif
#endif

#endif


#endif  /* __IXpsOMImageBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMLinearGradientBrush interface
 */
#ifndef __IXpsOMLinearGradientBrush_INTERFACE_DEFINED__
#define __IXpsOMLinearGradientBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMLinearGradientBrush, 0x005e279f, 0xc30d, 0x40ff, 0x93,0xec, 0x19,0x50,0xd3,0xc5,0x28,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("005e279f-c30d-40ff-93ec-1950d3c528db")
IXpsOMLinearGradientBrush : public IXpsOMGradientBrush
{
    virtual HRESULT STDMETHODCALLTYPE GetStartPoint(
        XPS_POINT *startPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStartPoint(
        const XPS_POINT *startPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEndPoint(
        XPS_POINT *endPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEndPoint(
        const XPS_POINT *endPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMLinearGradientBrush **linearGradientBrush) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMLinearGradientBrush, 0x005e279f, 0xc30d, 0x40ff, 0x93,0xec, 0x19,0x50,0xd3,0xc5,0x28,0xdb)
#endif
#else
typedef struct IXpsOMLinearGradientBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMLinearGradientBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMLinearGradientBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMLinearGradientBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMLinearGradientBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMLinearGradientBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMLinearGradientBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMLinearGradientBrush *This,
        FLOAT opacity);

    /*** IXpsOMGradientBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGradientStops)(
        IXpsOMLinearGradientBrush *This,
        IXpsOMGradientStopCollection **gradientStops);

    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMLinearGradientBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMLinearGradientBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMLinearGradientBrush *This,
        IXpsOMMatrixTransform *transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMLinearGradientBrush *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMLinearGradientBrush *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetSpreadMethod)(
        IXpsOMLinearGradientBrush *This,
        XPS_SPREAD_METHOD *spreadMethod);

    HRESULT (STDMETHODCALLTYPE *SetSpreadMethod)(
        IXpsOMLinearGradientBrush *This,
        XPS_SPREAD_METHOD spreadMethod);

    HRESULT (STDMETHODCALLTYPE *GetColorInterpolationMode)(
        IXpsOMLinearGradientBrush *This,
        XPS_COLOR_INTERPOLATION *colorInterpolationMode);

    HRESULT (STDMETHODCALLTYPE *SetColorInterpolationMode)(
        IXpsOMLinearGradientBrush *This,
        XPS_COLOR_INTERPOLATION colorInterpolationMode);

    /*** IXpsOMLinearGradientBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStartPoint)(
        IXpsOMLinearGradientBrush *This,
        XPS_POINT *startPoint);

    HRESULT (STDMETHODCALLTYPE *SetStartPoint)(
        IXpsOMLinearGradientBrush *This,
        const XPS_POINT *startPoint);

    HRESULT (STDMETHODCALLTYPE *GetEndPoint)(
        IXpsOMLinearGradientBrush *This,
        XPS_POINT *endPoint);

    HRESULT (STDMETHODCALLTYPE *SetEndPoint)(
        IXpsOMLinearGradientBrush *This,
        const XPS_POINT *endPoint);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMLinearGradientBrush *This,
        IXpsOMLinearGradientBrush **linearGradientBrush);

    END_INTERFACE
} IXpsOMLinearGradientBrushVtbl;

interface IXpsOMLinearGradientBrush {
    CONST_VTBL IXpsOMLinearGradientBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMLinearGradientBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMLinearGradientBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMLinearGradientBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMLinearGradientBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMLinearGradientBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMLinearGradientBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMLinearGradientBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
/*** IXpsOMGradientBrush methods ***/
#define IXpsOMLinearGradientBrush_GetGradientStops(This,gradientStops) (This)->lpVtbl->GetGradientStops(This,gradientStops)
#define IXpsOMLinearGradientBrush_GetTransform(This,transform) (This)->lpVtbl->GetTransform(This,transform)
#define IXpsOMLinearGradientBrush_GetTransformLocal(This,transform) (This)->lpVtbl->GetTransformLocal(This,transform)
#define IXpsOMLinearGradientBrush_SetTransformLocal(This,transform) (This)->lpVtbl->SetTransformLocal(This,transform)
#define IXpsOMLinearGradientBrush_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMLinearGradientBrush_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMLinearGradientBrush_GetSpreadMethod(This,spreadMethod) (This)->lpVtbl->GetSpreadMethod(This,spreadMethod)
#define IXpsOMLinearGradientBrush_SetSpreadMethod(This,spreadMethod) (This)->lpVtbl->SetSpreadMethod(This,spreadMethod)
#define IXpsOMLinearGradientBrush_GetColorInterpolationMode(This,colorInterpolationMode) (This)->lpVtbl->GetColorInterpolationMode(This,colorInterpolationMode)
#define IXpsOMLinearGradientBrush_SetColorInterpolationMode(This,colorInterpolationMode) (This)->lpVtbl->SetColorInterpolationMode(This,colorInterpolationMode)
/*** IXpsOMLinearGradientBrush methods ***/
#define IXpsOMLinearGradientBrush_GetStartPoint(This,startPoint) (This)->lpVtbl->GetStartPoint(This,startPoint)
#define IXpsOMLinearGradientBrush_SetStartPoint(This,startPoint) (This)->lpVtbl->SetStartPoint(This,startPoint)
#define IXpsOMLinearGradientBrush_GetEndPoint(This,endPoint) (This)->lpVtbl->GetEndPoint(This,endPoint)
#define IXpsOMLinearGradientBrush_SetEndPoint(This,endPoint) (This)->lpVtbl->SetEndPoint(This,endPoint)
#define IXpsOMLinearGradientBrush_Clone(This,linearGradientBrush) (This)->lpVtbl->Clone(This,linearGradientBrush)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMLinearGradientBrush_QueryInterface(IXpsOMLinearGradientBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMLinearGradientBrush_AddRef(IXpsOMLinearGradientBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMLinearGradientBrush_Release(IXpsOMLinearGradientBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMLinearGradientBrush_GetOwner(IXpsOMLinearGradientBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMLinearGradientBrush_GetType(IXpsOMLinearGradientBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMLinearGradientBrush_GetOpacity(IXpsOMLinearGradientBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMLinearGradientBrush_SetOpacity(IXpsOMLinearGradientBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
/*** IXpsOMGradientBrush methods ***/
static inline HRESULT IXpsOMLinearGradientBrush_GetGradientStops(IXpsOMLinearGradientBrush* This,IXpsOMGradientStopCollection **gradientStops) {
    return This->lpVtbl->GetGradientStops(This,gradientStops);
}
static inline HRESULT IXpsOMLinearGradientBrush_GetTransform(IXpsOMLinearGradientBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransform(This,transform);
}
static inline HRESULT IXpsOMLinearGradientBrush_GetTransformLocal(IXpsOMLinearGradientBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMLinearGradientBrush_SetTransformLocal(IXpsOMLinearGradientBrush* This,IXpsOMMatrixTransform *transform) {
    return This->lpVtbl->SetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMLinearGradientBrush_GetTransformLookup(IXpsOMLinearGradientBrush* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMLinearGradientBrush_SetTransformLookup(IXpsOMLinearGradientBrush* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMLinearGradientBrush_GetSpreadMethod(IXpsOMLinearGradientBrush* This,XPS_SPREAD_METHOD *spreadMethod) {
    return This->lpVtbl->GetSpreadMethod(This,spreadMethod);
}
static inline HRESULT IXpsOMLinearGradientBrush_SetSpreadMethod(IXpsOMLinearGradientBrush* This,XPS_SPREAD_METHOD spreadMethod) {
    return This->lpVtbl->SetSpreadMethod(This,spreadMethod);
}
static inline HRESULT IXpsOMLinearGradientBrush_GetColorInterpolationMode(IXpsOMLinearGradientBrush* This,XPS_COLOR_INTERPOLATION *colorInterpolationMode) {
    return This->lpVtbl->GetColorInterpolationMode(This,colorInterpolationMode);
}
static inline HRESULT IXpsOMLinearGradientBrush_SetColorInterpolationMode(IXpsOMLinearGradientBrush* This,XPS_COLOR_INTERPOLATION colorInterpolationMode) {
    return This->lpVtbl->SetColorInterpolationMode(This,colorInterpolationMode);
}
/*** IXpsOMLinearGradientBrush methods ***/
static inline HRESULT IXpsOMLinearGradientBrush_GetStartPoint(IXpsOMLinearGradientBrush* This,XPS_POINT *startPoint) {
    return This->lpVtbl->GetStartPoint(This,startPoint);
}
static inline HRESULT IXpsOMLinearGradientBrush_SetStartPoint(IXpsOMLinearGradientBrush* This,const XPS_POINT *startPoint) {
    return This->lpVtbl->SetStartPoint(This,startPoint);
}
static inline HRESULT IXpsOMLinearGradientBrush_GetEndPoint(IXpsOMLinearGradientBrush* This,XPS_POINT *endPoint) {
    return This->lpVtbl->GetEndPoint(This,endPoint);
}
static inline HRESULT IXpsOMLinearGradientBrush_SetEndPoint(IXpsOMLinearGradientBrush* This,const XPS_POINT *endPoint) {
    return This->lpVtbl->SetEndPoint(This,endPoint);
}
static inline HRESULT IXpsOMLinearGradientBrush_Clone(IXpsOMLinearGradientBrush* This,IXpsOMLinearGradientBrush **linearGradientBrush) {
    return This->lpVtbl->Clone(This,linearGradientBrush);
}
#endif
#endif

#endif


#endif  /* __IXpsOMLinearGradientBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMRadialGradientBrush interface
 */
#ifndef __IXpsOMRadialGradientBrush_INTERFACE_DEFINED__
#define __IXpsOMRadialGradientBrush_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMRadialGradientBrush, 0x75f207e5, 0x08bf, 0x413c, 0x96,0xb1, 0xb8,0x2b,0x40,0x64,0x17,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("75f207e5-08bf-413c-96b1-b82b4064176b")
IXpsOMRadialGradientBrush : public IXpsOMGradientBrush
{
    virtual HRESULT STDMETHODCALLTYPE GetCenter(
        XPS_POINT *center) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCenter(
        const XPS_POINT *center) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRadiiSizes(
        XPS_SIZE *radiiSizes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRadiiSizes(
        const XPS_SIZE *radiiSizes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGradientOrigin(
        XPS_POINT *origin) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGradientOrigin(
        const XPS_POINT *origin) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMRadialGradientBrush **radialGradientBrush) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMRadialGradientBrush, 0x75f207e5, 0x08bf, 0x413c, 0x96,0xb1, 0xb8,0x2b,0x40,0x64,0x17,0x6b)
#endif
#else
typedef struct IXpsOMRadialGradientBrushVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMRadialGradientBrush *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMRadialGradientBrush *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMRadialGradientBrush *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMRadialGradientBrush *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMRadialGradientBrush *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMRadialGradientBrush *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMRadialGradientBrush *This,
        FLOAT opacity);

    /*** IXpsOMGradientBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGradientStops)(
        IXpsOMRadialGradientBrush *This,
        IXpsOMGradientStopCollection **gradientStops);

    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMRadialGradientBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMRadialGradientBrush *This,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMRadialGradientBrush *This,
        IXpsOMMatrixTransform *transform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMRadialGradientBrush *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMRadialGradientBrush *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetSpreadMethod)(
        IXpsOMRadialGradientBrush *This,
        XPS_SPREAD_METHOD *spreadMethod);

    HRESULT (STDMETHODCALLTYPE *SetSpreadMethod)(
        IXpsOMRadialGradientBrush *This,
        XPS_SPREAD_METHOD spreadMethod);

    HRESULT (STDMETHODCALLTYPE *GetColorInterpolationMode)(
        IXpsOMRadialGradientBrush *This,
        XPS_COLOR_INTERPOLATION *colorInterpolationMode);

    HRESULT (STDMETHODCALLTYPE *SetColorInterpolationMode)(
        IXpsOMRadialGradientBrush *This,
        XPS_COLOR_INTERPOLATION colorInterpolationMode);

    /*** IXpsOMRadialGradientBrush methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCenter)(
        IXpsOMRadialGradientBrush *This,
        XPS_POINT *center);

    HRESULT (STDMETHODCALLTYPE *SetCenter)(
        IXpsOMRadialGradientBrush *This,
        const XPS_POINT *center);

    HRESULT (STDMETHODCALLTYPE *GetRadiiSizes)(
        IXpsOMRadialGradientBrush *This,
        XPS_SIZE *radiiSizes);

    HRESULT (STDMETHODCALLTYPE *SetRadiiSizes)(
        IXpsOMRadialGradientBrush *This,
        const XPS_SIZE *radiiSizes);

    HRESULT (STDMETHODCALLTYPE *GetGradientOrigin)(
        IXpsOMRadialGradientBrush *This,
        XPS_POINT *origin);

    HRESULT (STDMETHODCALLTYPE *SetGradientOrigin)(
        IXpsOMRadialGradientBrush *This,
        const XPS_POINT *origin);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMRadialGradientBrush *This,
        IXpsOMRadialGradientBrush **radialGradientBrush);

    END_INTERFACE
} IXpsOMRadialGradientBrushVtbl;

interface IXpsOMRadialGradientBrush {
    CONST_VTBL IXpsOMRadialGradientBrushVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMRadialGradientBrush_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMRadialGradientBrush_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMRadialGradientBrush_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMRadialGradientBrush_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMRadialGradientBrush_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMBrush methods ***/
#define IXpsOMRadialGradientBrush_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMRadialGradientBrush_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
/*** IXpsOMGradientBrush methods ***/
#define IXpsOMRadialGradientBrush_GetGradientStops(This,gradientStops) (This)->lpVtbl->GetGradientStops(This,gradientStops)
#define IXpsOMRadialGradientBrush_GetTransform(This,transform) (This)->lpVtbl->GetTransform(This,transform)
#define IXpsOMRadialGradientBrush_GetTransformLocal(This,transform) (This)->lpVtbl->GetTransformLocal(This,transform)
#define IXpsOMRadialGradientBrush_SetTransformLocal(This,transform) (This)->lpVtbl->SetTransformLocal(This,transform)
#define IXpsOMRadialGradientBrush_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMRadialGradientBrush_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMRadialGradientBrush_GetSpreadMethod(This,spreadMethod) (This)->lpVtbl->GetSpreadMethod(This,spreadMethod)
#define IXpsOMRadialGradientBrush_SetSpreadMethod(This,spreadMethod) (This)->lpVtbl->SetSpreadMethod(This,spreadMethod)
#define IXpsOMRadialGradientBrush_GetColorInterpolationMode(This,colorInterpolationMode) (This)->lpVtbl->GetColorInterpolationMode(This,colorInterpolationMode)
#define IXpsOMRadialGradientBrush_SetColorInterpolationMode(This,colorInterpolationMode) (This)->lpVtbl->SetColorInterpolationMode(This,colorInterpolationMode)
/*** IXpsOMRadialGradientBrush methods ***/
#define IXpsOMRadialGradientBrush_GetCenter(This,center) (This)->lpVtbl->GetCenter(This,center)
#define IXpsOMRadialGradientBrush_SetCenter(This,center) (This)->lpVtbl->SetCenter(This,center)
#define IXpsOMRadialGradientBrush_GetRadiiSizes(This,radiiSizes) (This)->lpVtbl->GetRadiiSizes(This,radiiSizes)
#define IXpsOMRadialGradientBrush_SetRadiiSizes(This,radiiSizes) (This)->lpVtbl->SetRadiiSizes(This,radiiSizes)
#define IXpsOMRadialGradientBrush_GetGradientOrigin(This,origin) (This)->lpVtbl->GetGradientOrigin(This,origin)
#define IXpsOMRadialGradientBrush_SetGradientOrigin(This,origin) (This)->lpVtbl->SetGradientOrigin(This,origin)
#define IXpsOMRadialGradientBrush_Clone(This,radialGradientBrush) (This)->lpVtbl->Clone(This,radialGradientBrush)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMRadialGradientBrush_QueryInterface(IXpsOMRadialGradientBrush* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMRadialGradientBrush_AddRef(IXpsOMRadialGradientBrush* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMRadialGradientBrush_Release(IXpsOMRadialGradientBrush* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMRadialGradientBrush_GetOwner(IXpsOMRadialGradientBrush* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetType(IXpsOMRadialGradientBrush* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMBrush methods ***/
static inline HRESULT IXpsOMRadialGradientBrush_GetOpacity(IXpsOMRadialGradientBrush* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetOpacity(IXpsOMRadialGradientBrush* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
/*** IXpsOMGradientBrush methods ***/
static inline HRESULT IXpsOMRadialGradientBrush_GetGradientStops(IXpsOMRadialGradientBrush* This,IXpsOMGradientStopCollection **gradientStops) {
    return This->lpVtbl->GetGradientStops(This,gradientStops);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetTransform(IXpsOMRadialGradientBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransform(This,transform);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetTransformLocal(IXpsOMRadialGradientBrush* This,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->GetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetTransformLocal(IXpsOMRadialGradientBrush* This,IXpsOMMatrixTransform *transform) {
    return This->lpVtbl->SetTransformLocal(This,transform);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetTransformLookup(IXpsOMRadialGradientBrush* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetTransformLookup(IXpsOMRadialGradientBrush* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetSpreadMethod(IXpsOMRadialGradientBrush* This,XPS_SPREAD_METHOD *spreadMethod) {
    return This->lpVtbl->GetSpreadMethod(This,spreadMethod);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetSpreadMethod(IXpsOMRadialGradientBrush* This,XPS_SPREAD_METHOD spreadMethod) {
    return This->lpVtbl->SetSpreadMethod(This,spreadMethod);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetColorInterpolationMode(IXpsOMRadialGradientBrush* This,XPS_COLOR_INTERPOLATION *colorInterpolationMode) {
    return This->lpVtbl->GetColorInterpolationMode(This,colorInterpolationMode);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetColorInterpolationMode(IXpsOMRadialGradientBrush* This,XPS_COLOR_INTERPOLATION colorInterpolationMode) {
    return This->lpVtbl->SetColorInterpolationMode(This,colorInterpolationMode);
}
/*** IXpsOMRadialGradientBrush methods ***/
static inline HRESULT IXpsOMRadialGradientBrush_GetCenter(IXpsOMRadialGradientBrush* This,XPS_POINT *center) {
    return This->lpVtbl->GetCenter(This,center);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetCenter(IXpsOMRadialGradientBrush* This,const XPS_POINT *center) {
    return This->lpVtbl->SetCenter(This,center);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetRadiiSizes(IXpsOMRadialGradientBrush* This,XPS_SIZE *radiiSizes) {
    return This->lpVtbl->GetRadiiSizes(This,radiiSizes);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetRadiiSizes(IXpsOMRadialGradientBrush* This,const XPS_SIZE *radiiSizes) {
    return This->lpVtbl->SetRadiiSizes(This,radiiSizes);
}
static inline HRESULT IXpsOMRadialGradientBrush_GetGradientOrigin(IXpsOMRadialGradientBrush* This,XPS_POINT *origin) {
    return This->lpVtbl->GetGradientOrigin(This,origin);
}
static inline HRESULT IXpsOMRadialGradientBrush_SetGradientOrigin(IXpsOMRadialGradientBrush* This,const XPS_POINT *origin) {
    return This->lpVtbl->SetGradientOrigin(This,origin);
}
static inline HRESULT IXpsOMRadialGradientBrush_Clone(IXpsOMRadialGradientBrush* This,IXpsOMRadialGradientBrush **radialGradientBrush) {
    return This->lpVtbl->Clone(This,radialGradientBrush);
}
#endif
#endif

#endif


#endif  /* __IXpsOMRadialGradientBrush_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMResource interface
 */
#ifndef __IXpsOMResource_INTERFACE_DEFINED__
#define __IXpsOMResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMResource, 0xda2ac0a2, 0x73a2, 0x4975, 0xad,0x14, 0x74,0x09,0x7c,0x3f,0xf3,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("da2ac0a2-73a2-4975-ad14-74097c3ff3a5")
IXpsOMResource : public IXpsOMPart
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMResource, 0xda2ac0a2, 0x73a2, 0x4975, 0xad,0x14, 0x74,0x09,0x7c,0x3f,0xf3,0xa5)
#endif
#else
typedef struct IXpsOMResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMResource *This,
        IOpcPartUri *partUri);

    END_INTERFACE
} IXpsOMResourceVtbl;

interface IXpsOMResource {
    CONST_VTBL IXpsOMResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMResource_QueryInterface(IXpsOMResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMResource_AddRef(IXpsOMResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMResource_Release(IXpsOMResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMResource_GetPartName(IXpsOMResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMResource_SetPartName(IXpsOMResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
#endif
#endif

#endif


#endif  /* __IXpsOMResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMFontResource interface
 */
#ifndef __IXpsOMFontResource_INTERFACE_DEFINED__
#define __IXpsOMFontResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMFontResource, 0xa8c45708, 0x47d9, 0x4af4, 0x8d,0x20, 0x33,0xb4,0x8c,0x9b,0x84,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a8c45708-47d9-4af4-8d20-33b48c9b8485")
IXpsOMFontResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **readerStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContent(
        IStream *sourceStream,
        XPS_FONT_EMBEDDING embeddingOption,
        IOpcPartUri *partName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEmbeddingOption(
        XPS_FONT_EMBEDDING *embeddingOption) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMFontResource, 0xa8c45708, 0x47d9, 0x4af4, 0x8d,0x20, 0x33,0xb4,0x8c,0x9b,0x84,0x85)
#endif
#else
typedef struct IXpsOMFontResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMFontResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMFontResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMFontResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMFontResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMFontResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMFontResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IXpsOMFontResource *This,
        IStream **readerStream);

    HRESULT (STDMETHODCALLTYPE *SetContent)(
        IXpsOMFontResource *This,
        IStream *sourceStream,
        XPS_FONT_EMBEDDING embeddingOption,
        IOpcPartUri *partName);

    HRESULT (STDMETHODCALLTYPE *GetEmbeddingOption)(
        IXpsOMFontResource *This,
        XPS_FONT_EMBEDDING *embeddingOption);

    END_INTERFACE
} IXpsOMFontResourceVtbl;

interface IXpsOMFontResource {
    CONST_VTBL IXpsOMFontResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMFontResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMFontResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMFontResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMFontResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMFontResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMFontResource methods ***/
#define IXpsOMFontResource_GetStream(This,readerStream) (This)->lpVtbl->GetStream(This,readerStream)
#define IXpsOMFontResource_SetContent(This,sourceStream,embeddingOption,partName) (This)->lpVtbl->SetContent(This,sourceStream,embeddingOption,partName)
#define IXpsOMFontResource_GetEmbeddingOption(This,embeddingOption) (This)->lpVtbl->GetEmbeddingOption(This,embeddingOption)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMFontResource_QueryInterface(IXpsOMFontResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMFontResource_AddRef(IXpsOMFontResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMFontResource_Release(IXpsOMFontResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMFontResource_GetPartName(IXpsOMFontResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMFontResource_SetPartName(IXpsOMFontResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMFontResource methods ***/
static inline HRESULT IXpsOMFontResource_GetStream(IXpsOMFontResource* This,IStream **readerStream) {
    return This->lpVtbl->GetStream(This,readerStream);
}
static inline HRESULT IXpsOMFontResource_SetContent(IXpsOMFontResource* This,IStream *sourceStream,XPS_FONT_EMBEDDING embeddingOption,IOpcPartUri *partName) {
    return This->lpVtbl->SetContent(This,sourceStream,embeddingOption,partName);
}
static inline HRESULT IXpsOMFontResource_GetEmbeddingOption(IXpsOMFontResource* This,XPS_FONT_EMBEDDING *embeddingOption) {
    return This->lpVtbl->GetEmbeddingOption(This,embeddingOption);
}
#endif
#endif

#endif


#endif  /* __IXpsOMFontResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMImageResource interface
 */
#ifndef __IXpsOMImageResource_INTERFACE_DEFINED__
#define __IXpsOMImageResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMImageResource, 0x3db8417d, 0xae50, 0x485e, 0x9a,0x44, 0xd7,0x75,0x8f,0x78,0xa2,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3db8417d-ae50-485e-9a44-d7758f78a23f")
IXpsOMImageResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **readerStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContent(
        IStream *sourceStream,
        XPS_IMAGE_TYPE imageType,
        IOpcPartUri *partName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImageType(
        XPS_IMAGE_TYPE *imageType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMImageResource, 0x3db8417d, 0xae50, 0x485e, 0x9a,0x44, 0xd7,0x75,0x8f,0x78,0xa2,0x3f)
#endif
#else
typedef struct IXpsOMImageResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMImageResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMImageResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMImageResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMImageResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMImageResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMImageResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IXpsOMImageResource *This,
        IStream **readerStream);

    HRESULT (STDMETHODCALLTYPE *SetContent)(
        IXpsOMImageResource *This,
        IStream *sourceStream,
        XPS_IMAGE_TYPE imageType,
        IOpcPartUri *partName);

    HRESULT (STDMETHODCALLTYPE *GetImageType)(
        IXpsOMImageResource *This,
        XPS_IMAGE_TYPE *imageType);

    END_INTERFACE
} IXpsOMImageResourceVtbl;

interface IXpsOMImageResource {
    CONST_VTBL IXpsOMImageResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMImageResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMImageResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMImageResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMImageResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMImageResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMImageResource methods ***/
#define IXpsOMImageResource_GetStream(This,readerStream) (This)->lpVtbl->GetStream(This,readerStream)
#define IXpsOMImageResource_SetContent(This,sourceStream,imageType,partName) (This)->lpVtbl->SetContent(This,sourceStream,imageType,partName)
#define IXpsOMImageResource_GetImageType(This,imageType) (This)->lpVtbl->GetImageType(This,imageType)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMImageResource_QueryInterface(IXpsOMImageResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMImageResource_AddRef(IXpsOMImageResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMImageResource_Release(IXpsOMImageResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMImageResource_GetPartName(IXpsOMImageResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMImageResource_SetPartName(IXpsOMImageResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMImageResource methods ***/
static inline HRESULT IXpsOMImageResource_GetStream(IXpsOMImageResource* This,IStream **readerStream) {
    return This->lpVtbl->GetStream(This,readerStream);
}
static inline HRESULT IXpsOMImageResource_SetContent(IXpsOMImageResource* This,IStream *sourceStream,XPS_IMAGE_TYPE imageType,IOpcPartUri *partName) {
    return This->lpVtbl->SetContent(This,sourceStream,imageType,partName);
}
static inline HRESULT IXpsOMImageResource_GetImageType(IXpsOMImageResource* This,XPS_IMAGE_TYPE *imageType) {
    return This->lpVtbl->GetImageType(This,imageType);
}
#endif
#endif

#endif


#endif  /* __IXpsOMImageResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMColorProfileResource interface
 */
#ifndef __IXpsOMColorProfileResource_INTERFACE_DEFINED__
#define __IXpsOMColorProfileResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMColorProfileResource, 0x67bd7d69, 0x1eef, 0x4bb1, 0xb5,0xe7, 0x6f,0x4f,0x87,0xbe,0x8a,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("67bd7d69-1eef-4bb1-b5e7-6f4f87be8abe")
IXpsOMColorProfileResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContent(
        IStream *sourceStream,
        IOpcPartUri *partName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMColorProfileResource, 0x67bd7d69, 0x1eef, 0x4bb1, 0xb5,0xe7, 0x6f,0x4f,0x87,0xbe,0x8a,0xbe)
#endif
#else
typedef struct IXpsOMColorProfileResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMColorProfileResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMColorProfileResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMColorProfileResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMColorProfileResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMColorProfileResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMColorProfileResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IXpsOMColorProfileResource *This,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *SetContent)(
        IXpsOMColorProfileResource *This,
        IStream *sourceStream,
        IOpcPartUri *partName);

    END_INTERFACE
} IXpsOMColorProfileResourceVtbl;

interface IXpsOMColorProfileResource {
    CONST_VTBL IXpsOMColorProfileResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMColorProfileResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMColorProfileResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMColorProfileResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMColorProfileResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMColorProfileResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMColorProfileResource methods ***/
#define IXpsOMColorProfileResource_GetStream(This,stream) (This)->lpVtbl->GetStream(This,stream)
#define IXpsOMColorProfileResource_SetContent(This,sourceStream,partName) (This)->lpVtbl->SetContent(This,sourceStream,partName)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMColorProfileResource_QueryInterface(IXpsOMColorProfileResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMColorProfileResource_AddRef(IXpsOMColorProfileResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMColorProfileResource_Release(IXpsOMColorProfileResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMColorProfileResource_GetPartName(IXpsOMColorProfileResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMColorProfileResource_SetPartName(IXpsOMColorProfileResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMColorProfileResource methods ***/
static inline HRESULT IXpsOMColorProfileResource_GetStream(IXpsOMColorProfileResource* This,IStream **stream) {
    return This->lpVtbl->GetStream(This,stream);
}
static inline HRESULT IXpsOMColorProfileResource_SetContent(IXpsOMColorProfileResource* This,IStream *sourceStream,IOpcPartUri *partName) {
    return This->lpVtbl->SetContent(This,sourceStream,partName);
}
#endif
#endif

#endif


#endif  /* __IXpsOMColorProfileResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPrintTicketResource interface
 */
#ifndef __IXpsOMPrintTicketResource_INTERFACE_DEFINED__
#define __IXpsOMPrintTicketResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPrintTicketResource, 0xe7ff32d2, 0x34aa, 0x499b, 0xbb,0xe9, 0x9c,0xd4,0xee,0x6c,0x59,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e7ff32d2-34aa-499b-bbe9-9cd4ee6c59f7")
IXpsOMPrintTicketResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContent(
        IStream *sourceStream,
        IOpcPartUri *partName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPrintTicketResource, 0xe7ff32d2, 0x34aa, 0x499b, 0xbb,0xe9, 0x9c,0xd4,0xee,0x6c,0x59,0xf7)
#endif
#else
typedef struct IXpsOMPrintTicketResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPrintTicketResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPrintTicketResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPrintTicketResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMPrintTicketResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMPrintTicketResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMPrintTicketResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IXpsOMPrintTicketResource *This,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *SetContent)(
        IXpsOMPrintTicketResource *This,
        IStream *sourceStream,
        IOpcPartUri *partName);

    END_INTERFACE
} IXpsOMPrintTicketResourceVtbl;

interface IXpsOMPrintTicketResource {
    CONST_VTBL IXpsOMPrintTicketResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPrintTicketResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPrintTicketResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPrintTicketResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMPrintTicketResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMPrintTicketResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMPrintTicketResource methods ***/
#define IXpsOMPrintTicketResource_GetStream(This,stream) (This)->lpVtbl->GetStream(This,stream)
#define IXpsOMPrintTicketResource_SetContent(This,sourceStream,partName) (This)->lpVtbl->SetContent(This,sourceStream,partName)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPrintTicketResource_QueryInterface(IXpsOMPrintTicketResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPrintTicketResource_AddRef(IXpsOMPrintTicketResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPrintTicketResource_Release(IXpsOMPrintTicketResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMPrintTicketResource_GetPartName(IXpsOMPrintTicketResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMPrintTicketResource_SetPartName(IXpsOMPrintTicketResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMPrintTicketResource methods ***/
static inline HRESULT IXpsOMPrintTicketResource_GetStream(IXpsOMPrintTicketResource* This,IStream **stream) {
    return This->lpVtbl->GetStream(This,stream);
}
static inline HRESULT IXpsOMPrintTicketResource_SetContent(IXpsOMPrintTicketResource* This,IStream *sourceStream,IOpcPartUri *partName) {
    return This->lpVtbl->SetContent(This,sourceStream,partName);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPrintTicketResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMRemoteDictionaryResource interface
 */
#ifndef __IXpsOMRemoteDictionaryResource_INTERFACE_DEFINED__
#define __IXpsOMRemoteDictionaryResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMRemoteDictionaryResource, 0xc9bd7cd4, 0xe16a, 0x4bf8, 0x8c,0x84, 0xc9,0x50,0xaf,0x7a,0x30,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c9bd7cd4-e16a-4bf8-8c84-c950af7a3061")
IXpsOMRemoteDictionaryResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetDictionary(
        IXpsOMDictionary **dictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDictionary(
        IXpsOMDictionary *dictionary) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMRemoteDictionaryResource, 0xc9bd7cd4, 0xe16a, 0x4bf8, 0x8c,0x84, 0xc9,0x50,0xaf,0x7a,0x30,0x61)
#endif
#else
typedef struct IXpsOMRemoteDictionaryResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMRemoteDictionaryResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMRemoteDictionaryResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMRemoteDictionaryResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMRemoteDictionaryResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMRemoteDictionaryResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMRemoteDictionaryResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDictionary)(
        IXpsOMRemoteDictionaryResource *This,
        IXpsOMDictionary **dictionary);

    HRESULT (STDMETHODCALLTYPE *SetDictionary)(
        IXpsOMRemoteDictionaryResource *This,
        IXpsOMDictionary *dictionary);

    END_INTERFACE
} IXpsOMRemoteDictionaryResourceVtbl;

interface IXpsOMRemoteDictionaryResource {
    CONST_VTBL IXpsOMRemoteDictionaryResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMRemoteDictionaryResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMRemoteDictionaryResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMRemoteDictionaryResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMRemoteDictionaryResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMRemoteDictionaryResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMRemoteDictionaryResource methods ***/
#define IXpsOMRemoteDictionaryResource_GetDictionary(This,dictionary) (This)->lpVtbl->GetDictionary(This,dictionary)
#define IXpsOMRemoteDictionaryResource_SetDictionary(This,dictionary) (This)->lpVtbl->SetDictionary(This,dictionary)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResource_QueryInterface(IXpsOMRemoteDictionaryResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMRemoteDictionaryResource_AddRef(IXpsOMRemoteDictionaryResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMRemoteDictionaryResource_Release(IXpsOMRemoteDictionaryResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResource_GetPartName(IXpsOMRemoteDictionaryResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMRemoteDictionaryResource_SetPartName(IXpsOMRemoteDictionaryResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMRemoteDictionaryResource methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResource_GetDictionary(IXpsOMRemoteDictionaryResource* This,IXpsOMDictionary **dictionary) {
    return This->lpVtbl->GetDictionary(This,dictionary);
}
static inline HRESULT IXpsOMRemoteDictionaryResource_SetDictionary(IXpsOMRemoteDictionaryResource* This,IXpsOMDictionary *dictionary) {
    return This->lpVtbl->SetDictionary(This,dictionary);
}
#endif
#endif

#endif


#endif  /* __IXpsOMRemoteDictionaryResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMDocumentStructureResource interface
 */
#ifndef __IXpsOMDocumentStructureResource_INTERFACE_DEFINED__
#define __IXpsOMDocumentStructureResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMDocumentStructureResource, 0x85febc8a, 0x6b63, 0x48a9, 0xaf,0x07, 0x70,0x64,0xe4,0xec,0xff,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("85febc8a-6b63-48a9-af07-7064e4ecff30")
IXpsOMDocumentStructureResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMDocument **owner) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContent(
        IStream *sourceStream,
        IOpcPartUri *partName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMDocumentStructureResource, 0x85febc8a, 0x6b63, 0x48a9, 0xaf,0x07, 0x70,0x64,0xe4,0xec,0xff,0x30)
#endif
#else
typedef struct IXpsOMDocumentStructureResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMDocumentStructureResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMDocumentStructureResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMDocumentStructureResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMDocumentStructureResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMDocumentStructureResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMDocumentStructureResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMDocumentStructureResource *This,
        IXpsOMDocument **owner);

    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IXpsOMDocumentStructureResource *This,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *SetContent)(
        IXpsOMDocumentStructureResource *This,
        IStream *sourceStream,
        IOpcPartUri *partName);

    END_INTERFACE
} IXpsOMDocumentStructureResourceVtbl;

interface IXpsOMDocumentStructureResource {
    CONST_VTBL IXpsOMDocumentStructureResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMDocumentStructureResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMDocumentStructureResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMDocumentStructureResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMDocumentStructureResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMDocumentStructureResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMDocumentStructureResource methods ***/
#define IXpsOMDocumentStructureResource_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMDocumentStructureResource_GetStream(This,stream) (This)->lpVtbl->GetStream(This,stream)
#define IXpsOMDocumentStructureResource_SetContent(This,sourceStream,partName) (This)->lpVtbl->SetContent(This,sourceStream,partName)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMDocumentStructureResource_QueryInterface(IXpsOMDocumentStructureResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMDocumentStructureResource_AddRef(IXpsOMDocumentStructureResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMDocumentStructureResource_Release(IXpsOMDocumentStructureResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMDocumentStructureResource_GetPartName(IXpsOMDocumentStructureResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMDocumentStructureResource_SetPartName(IXpsOMDocumentStructureResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMDocumentStructureResource methods ***/
static inline HRESULT IXpsOMDocumentStructureResource_GetOwner(IXpsOMDocumentStructureResource* This,IXpsOMDocument **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMDocumentStructureResource_GetStream(IXpsOMDocumentStructureResource* This,IStream **stream) {
    return This->lpVtbl->GetStream(This,stream);
}
static inline HRESULT IXpsOMDocumentStructureResource_SetContent(IXpsOMDocumentStructureResource* This,IStream *sourceStream,IOpcPartUri *partName) {
    return This->lpVtbl->SetContent(This,sourceStream,partName);
}
#endif
#endif

#endif


#endif  /* __IXpsOMDocumentStructureResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMStoryFragmentsResource interface
 */
#ifndef __IXpsOMStoryFragmentsResource_INTERFACE_DEFINED__
#define __IXpsOMStoryFragmentsResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMStoryFragmentsResource, 0xc2b3ca09, 0x0473, 0x4282, 0x87,0xae, 0x17,0x80,0x86,0x32,0x23,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c2b3ca09-0473-4282-87ae-1780863223f0")
IXpsOMStoryFragmentsResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMPageReference **owner) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContent(
        IStream *sourceStream,
        IOpcPartUri *partName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMStoryFragmentsResource, 0xc2b3ca09, 0x0473, 0x4282, 0x87,0xae, 0x17,0x80,0x86,0x32,0x23,0xf0)
#endif
#else
typedef struct IXpsOMStoryFragmentsResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMStoryFragmentsResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMStoryFragmentsResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMStoryFragmentsResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMStoryFragmentsResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMStoryFragmentsResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMStoryFragmentsResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMStoryFragmentsResource *This,
        IXpsOMPageReference **owner);

    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IXpsOMStoryFragmentsResource *This,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *SetContent)(
        IXpsOMStoryFragmentsResource *This,
        IStream *sourceStream,
        IOpcPartUri *partName);

    END_INTERFACE
} IXpsOMStoryFragmentsResourceVtbl;

interface IXpsOMStoryFragmentsResource {
    CONST_VTBL IXpsOMStoryFragmentsResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMStoryFragmentsResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMStoryFragmentsResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMStoryFragmentsResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMStoryFragmentsResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMStoryFragmentsResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMStoryFragmentsResource methods ***/
#define IXpsOMStoryFragmentsResource_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMStoryFragmentsResource_GetStream(This,stream) (This)->lpVtbl->GetStream(This,stream)
#define IXpsOMStoryFragmentsResource_SetContent(This,sourceStream,partName) (This)->lpVtbl->SetContent(This,sourceStream,partName)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMStoryFragmentsResource_QueryInterface(IXpsOMStoryFragmentsResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMStoryFragmentsResource_AddRef(IXpsOMStoryFragmentsResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMStoryFragmentsResource_Release(IXpsOMStoryFragmentsResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMStoryFragmentsResource_GetPartName(IXpsOMStoryFragmentsResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMStoryFragmentsResource_SetPartName(IXpsOMStoryFragmentsResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMStoryFragmentsResource methods ***/
static inline HRESULT IXpsOMStoryFragmentsResource_GetOwner(IXpsOMStoryFragmentsResource* This,IXpsOMPageReference **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMStoryFragmentsResource_GetStream(IXpsOMStoryFragmentsResource* This,IStream **stream) {
    return This->lpVtbl->GetStream(This,stream);
}
static inline HRESULT IXpsOMStoryFragmentsResource_SetContent(IXpsOMStoryFragmentsResource* This,IStream *sourceStream,IOpcPartUri *partName) {
    return This->lpVtbl->SetContent(This,sourceStream,partName);
}
#endif
#endif

#endif


#endif  /* __IXpsOMStoryFragmentsResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMSignatureBlockResource interface
 */
#ifndef __IXpsOMSignatureBlockResource_INTERFACE_DEFINED__
#define __IXpsOMSignatureBlockResource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMSignatureBlockResource, 0x4776ad35, 0x2e04, 0x4357, 0x87,0x43, 0xeb,0xf6,0xc1,0x71,0xa9,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4776ad35-2e04-4357-8743-ebf6c171a905")
IXpsOMSignatureBlockResource : public IXpsOMResource
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMDocument **owner) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContent(
        IStream *sourceStream,
        IOpcPartUri *partName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMSignatureBlockResource, 0x4776ad35, 0x2e04, 0x4357, 0x87,0x43, 0xeb,0xf6,0xc1,0x71,0xa9,0x05)
#endif
#else
typedef struct IXpsOMSignatureBlockResourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMSignatureBlockResource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMSignatureBlockResource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMSignatureBlockResource *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMSignatureBlockResource *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMSignatureBlockResource *This,
        IOpcPartUri *partUri);

    /*** IXpsOMSignatureBlockResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMSignatureBlockResource *This,
        IXpsOMDocument **owner);

    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IXpsOMSignatureBlockResource *This,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *SetContent)(
        IXpsOMSignatureBlockResource *This,
        IStream *sourceStream,
        IOpcPartUri *partName);

    END_INTERFACE
} IXpsOMSignatureBlockResourceVtbl;

interface IXpsOMSignatureBlockResource {
    CONST_VTBL IXpsOMSignatureBlockResourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMSignatureBlockResource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMSignatureBlockResource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMSignatureBlockResource_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMSignatureBlockResource_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMSignatureBlockResource_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMSignatureBlockResource methods ***/
#define IXpsOMSignatureBlockResource_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMSignatureBlockResource_GetStream(This,stream) (This)->lpVtbl->GetStream(This,stream)
#define IXpsOMSignatureBlockResource_SetContent(This,sourceStream,partName) (This)->lpVtbl->SetContent(This,sourceStream,partName)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMSignatureBlockResource_QueryInterface(IXpsOMSignatureBlockResource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMSignatureBlockResource_AddRef(IXpsOMSignatureBlockResource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMSignatureBlockResource_Release(IXpsOMSignatureBlockResource* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMSignatureBlockResource_GetPartName(IXpsOMSignatureBlockResource* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMSignatureBlockResource_SetPartName(IXpsOMSignatureBlockResource* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMSignatureBlockResource methods ***/
static inline HRESULT IXpsOMSignatureBlockResource_GetOwner(IXpsOMSignatureBlockResource* This,IXpsOMDocument **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMSignatureBlockResource_GetStream(IXpsOMSignatureBlockResource* This,IStream **stream) {
    return This->lpVtbl->GetStream(This,stream);
}
static inline HRESULT IXpsOMSignatureBlockResource_SetContent(IXpsOMSignatureBlockResource* This,IStream *sourceStream,IOpcPartUri *partName) {
    return This->lpVtbl->SetContent(This,sourceStream,partName);
}
#endif
#endif

#endif


#endif  /* __IXpsOMSignatureBlockResource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMCanvas interface
 */
#ifndef __IXpsOMCanvas_INTERFACE_DEFINED__
#define __IXpsOMCanvas_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMCanvas, 0x221d1452, 0x331e, 0x47c6, 0x87,0xe9, 0x6c,0xce,0xfb,0x9b,0x5b,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("221d1452-331e-47c6-87e9-6ccefb9b5ba3")
IXpsOMCanvas : public IXpsOMVisual
{
    virtual HRESULT STDMETHODCALLTYPE GetVisuals(
        IXpsOMVisualCollection **visuals) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUseAliasedEdgeMode(
        WINBOOL *useAliasedEdgeMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUseAliasedEdgeMode(
        WINBOOL useAliasedEdgeMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAccessibilityShortDescription(
        LPWSTR *shortDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAccessibilityShortDescription(
        LPCWSTR shortDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAccessibilityLongDescription(
        LPWSTR *longDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAccessibilityLongDescription(
        LPCWSTR longDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDictionary(
        IXpsOMDictionary **resourceDictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDictionaryLocal(
        IXpsOMDictionary **resourceDictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDictionaryLocal(
        IXpsOMDictionary *resourceDictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDictionaryResource(
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDictionaryResource(
        IXpsOMRemoteDictionaryResource *remoteDictionaryResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMCanvas **canvas) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMCanvas, 0x221d1452, 0x331e, 0x47c6, 0x87,0xe9, 0x6c,0xce,0xfb,0x9b,0x5b,0xa3)
#endif
#else
typedef struct IXpsOMCanvasVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMCanvas *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMCanvas *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMCanvas *This);

    /*** IXpsOMShareable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMCanvas *This,
        IUnknown **owner);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IXpsOMCanvas *This,
        XPS_OBJECT_TYPE *type);

    /*** IXpsOMVisual methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IXpsOMCanvas *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLocal)(
        IXpsOMCanvas *This,
        IXpsOMMatrixTransform **matrixTransform);

    HRESULT (STDMETHODCALLTYPE *SetTransformLocal)(
        IXpsOMCanvas *This,
        IXpsOMMatrixTransform *matrixTransform);

    HRESULT (STDMETHODCALLTYPE *GetTransformLookup)(
        IXpsOMCanvas *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetTransformLookup)(
        IXpsOMCanvas *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometry)(
        IXpsOMCanvas *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLocal)(
        IXpsOMCanvas *This,
        IXpsOMGeometry **clipGeometry);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLocal)(
        IXpsOMCanvas *This,
        IXpsOMGeometry *clipGeometry);

    HRESULT (STDMETHODCALLTYPE *GetClipGeometryLookup)(
        IXpsOMCanvas *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetClipGeometryLookup)(
        IXpsOMCanvas *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetOpacity)(
        IXpsOMCanvas *This,
        FLOAT *opacity);

    HRESULT (STDMETHODCALLTYPE *SetOpacity)(
        IXpsOMCanvas *This,
        FLOAT opacity);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrush)(
        IXpsOMCanvas *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLocal)(
        IXpsOMCanvas *This,
        IXpsOMBrush **opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLocal)(
        IXpsOMCanvas *This,
        IXpsOMBrush *opacityMaskBrush);

    HRESULT (STDMETHODCALLTYPE *GetOpacityMaskBrushLookup)(
        IXpsOMCanvas *This,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *SetOpacityMaskBrushLookup)(
        IXpsOMCanvas *This,
        LPCWSTR key);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IXpsOMCanvas *This,
        LPWSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IXpsOMCanvas *This,
        LPCWSTR name);

    HRESULT (STDMETHODCALLTYPE *GetIsHyperlinkTarget)(
        IXpsOMCanvas *This,
        WINBOOL *isHyperlink);

    HRESULT (STDMETHODCALLTYPE *SetIsHyperlinkTarget)(
        IXpsOMCanvas *This,
        WINBOOL isHyperlink);

    HRESULT (STDMETHODCALLTYPE *GetHyperlinkNavigateUri)(
        IXpsOMCanvas *This,
        IUri **hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *SetHyperlinkNavigateUri)(
        IXpsOMCanvas *This,
        IUri *hyperlinkUri);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IXpsOMCanvas *This,
        LPWSTR *language);

    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IXpsOMCanvas *This,
        LPCWSTR language);

    /*** IXpsOMCanvas methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVisuals)(
        IXpsOMCanvas *This,
        IXpsOMVisualCollection **visuals);

    HRESULT (STDMETHODCALLTYPE *GetUseAliasedEdgeMode)(
        IXpsOMCanvas *This,
        WINBOOL *useAliasedEdgeMode);

    HRESULT (STDMETHODCALLTYPE *SetUseAliasedEdgeMode)(
        IXpsOMCanvas *This,
        WINBOOL useAliasedEdgeMode);

    HRESULT (STDMETHODCALLTYPE *GetAccessibilityShortDescription)(
        IXpsOMCanvas *This,
        LPWSTR *shortDescription);

    HRESULT (STDMETHODCALLTYPE *SetAccessibilityShortDescription)(
        IXpsOMCanvas *This,
        LPCWSTR shortDescription);

    HRESULT (STDMETHODCALLTYPE *GetAccessibilityLongDescription)(
        IXpsOMCanvas *This,
        LPWSTR *longDescription);

    HRESULT (STDMETHODCALLTYPE *SetAccessibilityLongDescription)(
        IXpsOMCanvas *This,
        LPCWSTR longDescription);

    HRESULT (STDMETHODCALLTYPE *GetDictionary)(
        IXpsOMCanvas *This,
        IXpsOMDictionary **resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *GetDictionaryLocal)(
        IXpsOMCanvas *This,
        IXpsOMDictionary **resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *SetDictionaryLocal)(
        IXpsOMCanvas *This,
        IXpsOMDictionary *resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *GetDictionaryResource)(
        IXpsOMCanvas *This,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *SetDictionaryResource)(
        IXpsOMCanvas *This,
        IXpsOMRemoteDictionaryResource *remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMCanvas *This,
        IXpsOMCanvas **canvas);

    END_INTERFACE
} IXpsOMCanvasVtbl;

interface IXpsOMCanvas {
    CONST_VTBL IXpsOMCanvasVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMCanvas_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMCanvas_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMCanvas_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMShareable methods ***/
#define IXpsOMCanvas_GetOwner(This,owner) (This)->lpVtbl->GetOwner(This,owner)
#define IXpsOMCanvas_GetType(This,type) (This)->lpVtbl->GetType(This,type)
/*** IXpsOMVisual methods ***/
#define IXpsOMCanvas_GetTransform(This,matrixTransform) (This)->lpVtbl->GetTransform(This,matrixTransform)
#define IXpsOMCanvas_GetTransformLocal(This,matrixTransform) (This)->lpVtbl->GetTransformLocal(This,matrixTransform)
#define IXpsOMCanvas_SetTransformLocal(This,matrixTransform) (This)->lpVtbl->SetTransformLocal(This,matrixTransform)
#define IXpsOMCanvas_GetTransformLookup(This,key) (This)->lpVtbl->GetTransformLookup(This,key)
#define IXpsOMCanvas_SetTransformLookup(This,key) (This)->lpVtbl->SetTransformLookup(This,key)
#define IXpsOMCanvas_GetClipGeometry(This,clipGeometry) (This)->lpVtbl->GetClipGeometry(This,clipGeometry)
#define IXpsOMCanvas_GetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->GetClipGeometryLocal(This,clipGeometry)
#define IXpsOMCanvas_SetClipGeometryLocal(This,clipGeometry) (This)->lpVtbl->SetClipGeometryLocal(This,clipGeometry)
#define IXpsOMCanvas_GetClipGeometryLookup(This,key) (This)->lpVtbl->GetClipGeometryLookup(This,key)
#define IXpsOMCanvas_SetClipGeometryLookup(This,key) (This)->lpVtbl->SetClipGeometryLookup(This,key)
#define IXpsOMCanvas_GetOpacity(This,opacity) (This)->lpVtbl->GetOpacity(This,opacity)
#define IXpsOMCanvas_SetOpacity(This,opacity) (This)->lpVtbl->SetOpacity(This,opacity)
#define IXpsOMCanvas_GetOpacityMaskBrush(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush)
#define IXpsOMCanvas_GetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMCanvas_SetOpacityMaskBrushLocal(This,opacityMaskBrush) (This)->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush)
#define IXpsOMCanvas_GetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->GetOpacityMaskBrushLookup(This,key)
#define IXpsOMCanvas_SetOpacityMaskBrushLookup(This,key) (This)->lpVtbl->SetOpacityMaskBrushLookup(This,key)
#define IXpsOMCanvas_GetName(This,name) (This)->lpVtbl->GetName(This,name)
#define IXpsOMCanvas_SetName(This,name) (This)->lpVtbl->SetName(This,name)
#define IXpsOMCanvas_GetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMCanvas_SetIsHyperlinkTarget(This,isHyperlink) (This)->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink)
#define IXpsOMCanvas_GetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMCanvas_SetHyperlinkNavigateUri(This,hyperlinkUri) (This)->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri)
#define IXpsOMCanvas_GetLanguage(This,language) (This)->lpVtbl->GetLanguage(This,language)
#define IXpsOMCanvas_SetLanguage(This,language) (This)->lpVtbl->SetLanguage(This,language)
/*** IXpsOMCanvas methods ***/
#define IXpsOMCanvas_GetVisuals(This,visuals) (This)->lpVtbl->GetVisuals(This,visuals)
#define IXpsOMCanvas_GetUseAliasedEdgeMode(This,useAliasedEdgeMode) (This)->lpVtbl->GetUseAliasedEdgeMode(This,useAliasedEdgeMode)
#define IXpsOMCanvas_SetUseAliasedEdgeMode(This,useAliasedEdgeMode) (This)->lpVtbl->SetUseAliasedEdgeMode(This,useAliasedEdgeMode)
#define IXpsOMCanvas_GetAccessibilityShortDescription(This,shortDescription) (This)->lpVtbl->GetAccessibilityShortDescription(This,shortDescription)
#define IXpsOMCanvas_SetAccessibilityShortDescription(This,shortDescription) (This)->lpVtbl->SetAccessibilityShortDescription(This,shortDescription)
#define IXpsOMCanvas_GetAccessibilityLongDescription(This,longDescription) (This)->lpVtbl->GetAccessibilityLongDescription(This,longDescription)
#define IXpsOMCanvas_SetAccessibilityLongDescription(This,longDescription) (This)->lpVtbl->SetAccessibilityLongDescription(This,longDescription)
#define IXpsOMCanvas_GetDictionary(This,resourceDictionary) (This)->lpVtbl->GetDictionary(This,resourceDictionary)
#define IXpsOMCanvas_GetDictionaryLocal(This,resourceDictionary) (This)->lpVtbl->GetDictionaryLocal(This,resourceDictionary)
#define IXpsOMCanvas_SetDictionaryLocal(This,resourceDictionary) (This)->lpVtbl->SetDictionaryLocal(This,resourceDictionary)
#define IXpsOMCanvas_GetDictionaryResource(This,remoteDictionaryResource) (This)->lpVtbl->GetDictionaryResource(This,remoteDictionaryResource)
#define IXpsOMCanvas_SetDictionaryResource(This,remoteDictionaryResource) (This)->lpVtbl->SetDictionaryResource(This,remoteDictionaryResource)
#define IXpsOMCanvas_Clone(This,canvas) (This)->lpVtbl->Clone(This,canvas)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMCanvas_QueryInterface(IXpsOMCanvas* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMCanvas_AddRef(IXpsOMCanvas* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMCanvas_Release(IXpsOMCanvas* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMShareable methods ***/
static inline HRESULT IXpsOMCanvas_GetOwner(IXpsOMCanvas* This,IUnknown **owner) {
    return This->lpVtbl->GetOwner(This,owner);
}
static inline HRESULT IXpsOMCanvas_GetType(IXpsOMCanvas* This,XPS_OBJECT_TYPE *type) {
    return This->lpVtbl->GetType(This,type);
}
/*** IXpsOMVisual methods ***/
static inline HRESULT IXpsOMCanvas_GetTransform(IXpsOMCanvas* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransform(This,matrixTransform);
}
static inline HRESULT IXpsOMCanvas_GetTransformLocal(IXpsOMCanvas* This,IXpsOMMatrixTransform **matrixTransform) {
    return This->lpVtbl->GetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMCanvas_SetTransformLocal(IXpsOMCanvas* This,IXpsOMMatrixTransform *matrixTransform) {
    return This->lpVtbl->SetTransformLocal(This,matrixTransform);
}
static inline HRESULT IXpsOMCanvas_GetTransformLookup(IXpsOMCanvas* This,LPWSTR *key) {
    return This->lpVtbl->GetTransformLookup(This,key);
}
static inline HRESULT IXpsOMCanvas_SetTransformLookup(IXpsOMCanvas* This,LPCWSTR key) {
    return This->lpVtbl->SetTransformLookup(This,key);
}
static inline HRESULT IXpsOMCanvas_GetClipGeometry(IXpsOMCanvas* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometry(This,clipGeometry);
}
static inline HRESULT IXpsOMCanvas_GetClipGeometryLocal(IXpsOMCanvas* This,IXpsOMGeometry **clipGeometry) {
    return This->lpVtbl->GetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMCanvas_SetClipGeometryLocal(IXpsOMCanvas* This,IXpsOMGeometry *clipGeometry) {
    return This->lpVtbl->SetClipGeometryLocal(This,clipGeometry);
}
static inline HRESULT IXpsOMCanvas_GetClipGeometryLookup(IXpsOMCanvas* This,LPWSTR *key) {
    return This->lpVtbl->GetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMCanvas_SetClipGeometryLookup(IXpsOMCanvas* This,LPCWSTR key) {
    return This->lpVtbl->SetClipGeometryLookup(This,key);
}
static inline HRESULT IXpsOMCanvas_GetOpacity(IXpsOMCanvas* This,FLOAT *opacity) {
    return This->lpVtbl->GetOpacity(This,opacity);
}
static inline HRESULT IXpsOMCanvas_SetOpacity(IXpsOMCanvas* This,FLOAT opacity) {
    return This->lpVtbl->SetOpacity(This,opacity);
}
static inline HRESULT IXpsOMCanvas_GetOpacityMaskBrush(IXpsOMCanvas* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrush(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMCanvas_GetOpacityMaskBrushLocal(IXpsOMCanvas* This,IXpsOMBrush **opacityMaskBrush) {
    return This->lpVtbl->GetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMCanvas_SetOpacityMaskBrushLocal(IXpsOMCanvas* This,IXpsOMBrush *opacityMaskBrush) {
    return This->lpVtbl->SetOpacityMaskBrushLocal(This,opacityMaskBrush);
}
static inline HRESULT IXpsOMCanvas_GetOpacityMaskBrushLookup(IXpsOMCanvas* This,LPWSTR *key) {
    return This->lpVtbl->GetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMCanvas_SetOpacityMaskBrushLookup(IXpsOMCanvas* This,LPCWSTR key) {
    return This->lpVtbl->SetOpacityMaskBrushLookup(This,key);
}
static inline HRESULT IXpsOMCanvas_GetName(IXpsOMCanvas* This,LPWSTR *name) {
    return This->lpVtbl->GetName(This,name);
}
static inline HRESULT IXpsOMCanvas_SetName(IXpsOMCanvas* This,LPCWSTR name) {
    return This->lpVtbl->SetName(This,name);
}
static inline HRESULT IXpsOMCanvas_GetIsHyperlinkTarget(IXpsOMCanvas* This,WINBOOL *isHyperlink) {
    return This->lpVtbl->GetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMCanvas_SetIsHyperlinkTarget(IXpsOMCanvas* This,WINBOOL isHyperlink) {
    return This->lpVtbl->SetIsHyperlinkTarget(This,isHyperlink);
}
static inline HRESULT IXpsOMCanvas_GetHyperlinkNavigateUri(IXpsOMCanvas* This,IUri **hyperlinkUri) {
    return This->lpVtbl->GetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMCanvas_SetHyperlinkNavigateUri(IXpsOMCanvas* This,IUri *hyperlinkUri) {
    return This->lpVtbl->SetHyperlinkNavigateUri(This,hyperlinkUri);
}
static inline HRESULT IXpsOMCanvas_GetLanguage(IXpsOMCanvas* This,LPWSTR *language) {
    return This->lpVtbl->GetLanguage(This,language);
}
static inline HRESULT IXpsOMCanvas_SetLanguage(IXpsOMCanvas* This,LPCWSTR language) {
    return This->lpVtbl->SetLanguage(This,language);
}
/*** IXpsOMCanvas methods ***/
static inline HRESULT IXpsOMCanvas_GetVisuals(IXpsOMCanvas* This,IXpsOMVisualCollection **visuals) {
    return This->lpVtbl->GetVisuals(This,visuals);
}
static inline HRESULT IXpsOMCanvas_GetUseAliasedEdgeMode(IXpsOMCanvas* This,WINBOOL *useAliasedEdgeMode) {
    return This->lpVtbl->GetUseAliasedEdgeMode(This,useAliasedEdgeMode);
}
static inline HRESULT IXpsOMCanvas_SetUseAliasedEdgeMode(IXpsOMCanvas* This,WINBOOL useAliasedEdgeMode) {
    return This->lpVtbl->SetUseAliasedEdgeMode(This,useAliasedEdgeMode);
}
static inline HRESULT IXpsOMCanvas_GetAccessibilityShortDescription(IXpsOMCanvas* This,LPWSTR *shortDescription) {
    return This->lpVtbl->GetAccessibilityShortDescription(This,shortDescription);
}
static inline HRESULT IXpsOMCanvas_SetAccessibilityShortDescription(IXpsOMCanvas* This,LPCWSTR shortDescription) {
    return This->lpVtbl->SetAccessibilityShortDescription(This,shortDescription);
}
static inline HRESULT IXpsOMCanvas_GetAccessibilityLongDescription(IXpsOMCanvas* This,LPWSTR *longDescription) {
    return This->lpVtbl->GetAccessibilityLongDescription(This,longDescription);
}
static inline HRESULT IXpsOMCanvas_SetAccessibilityLongDescription(IXpsOMCanvas* This,LPCWSTR longDescription) {
    return This->lpVtbl->SetAccessibilityLongDescription(This,longDescription);
}
static inline HRESULT IXpsOMCanvas_GetDictionary(IXpsOMCanvas* This,IXpsOMDictionary **resourceDictionary) {
    return This->lpVtbl->GetDictionary(This,resourceDictionary);
}
static inline HRESULT IXpsOMCanvas_GetDictionaryLocal(IXpsOMCanvas* This,IXpsOMDictionary **resourceDictionary) {
    return This->lpVtbl->GetDictionaryLocal(This,resourceDictionary);
}
static inline HRESULT IXpsOMCanvas_SetDictionaryLocal(IXpsOMCanvas* This,IXpsOMDictionary *resourceDictionary) {
    return This->lpVtbl->SetDictionaryLocal(This,resourceDictionary);
}
static inline HRESULT IXpsOMCanvas_GetDictionaryResource(IXpsOMCanvas* This,IXpsOMRemoteDictionaryResource **remoteDictionaryResource) {
    return This->lpVtbl->GetDictionaryResource(This,remoteDictionaryResource);
}
static inline HRESULT IXpsOMCanvas_SetDictionaryResource(IXpsOMCanvas* This,IXpsOMRemoteDictionaryResource *remoteDictionaryResource) {
    return This->lpVtbl->SetDictionaryResource(This,remoteDictionaryResource);
}
static inline HRESULT IXpsOMCanvas_Clone(IXpsOMCanvas* This,IXpsOMCanvas **canvas) {
    return This->lpVtbl->Clone(This,canvas);
}
#endif
#endif

#endif


#endif  /* __IXpsOMCanvas_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPage interface
 */
#ifndef __IXpsOMPage_INTERFACE_DEFINED__
#define __IXpsOMPage_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPage, 0xd3e18888, 0xf120, 0x4fee, 0x8c,0x68, 0x35,0x29,0x6e,0xae,0x91,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d3e18888-f120-4fee-8c68-35296eae91d4")
IXpsOMPage : public IXpsOMPart
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMPageReference **pageReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVisuals(
        IXpsOMVisualCollection **visuals) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPageDimensions(
        XPS_SIZE *pageDimensions) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPageDimensions(
        const XPS_SIZE *pageDimensions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentBox(
        XPS_RECT *contentBox) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContentBox(
        const XPS_RECT *contentBox) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBleedBox(
        XPS_RECT *bleedBox) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBleedBox(
        const XPS_RECT *bleedBox) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguage(
        LPWSTR *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLanguage(
        LPCWSTR language) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetName(
        LPWSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetName(
        LPCWSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIsHyperlinkTarget(
        WINBOOL *isHyperlinkTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIsHyperlinkTarget(
        WINBOOL isHyperlinkTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDictionary(
        IXpsOMDictionary **resourceDictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDictionaryLocal(
        IXpsOMDictionary **resourceDictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDictionaryLocal(
        IXpsOMDictionary *resourceDictionary) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDictionaryResource(
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDictionaryResource(
        IXpsOMRemoteDictionaryResource *remoteDictionaryResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write(
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GenerateUnusedLookupKey(
        XPS_OBJECT_TYPE type,
        LPWSTR *key) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMPage **page) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPage, 0xd3e18888, 0xf120, 0x4fee, 0x8c,0x68, 0x35,0x29,0x6e,0xae,0x91,0xd4)
#endif
#else
typedef struct IXpsOMPageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPage *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMPage *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMPage *This,
        IOpcPartUri *partUri);

    /*** IXpsOMPage methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMPage *This,
        IXpsOMPageReference **pageReference);

    HRESULT (STDMETHODCALLTYPE *GetVisuals)(
        IXpsOMPage *This,
        IXpsOMVisualCollection **visuals);

    HRESULT (STDMETHODCALLTYPE *GetPageDimensions)(
        IXpsOMPage *This,
        XPS_SIZE *pageDimensions);

    HRESULT (STDMETHODCALLTYPE *SetPageDimensions)(
        IXpsOMPage *This,
        const XPS_SIZE *pageDimensions);

    HRESULT (STDMETHODCALLTYPE *GetContentBox)(
        IXpsOMPage *This,
        XPS_RECT *contentBox);

    HRESULT (STDMETHODCALLTYPE *SetContentBox)(
        IXpsOMPage *This,
        const XPS_RECT *contentBox);

    HRESULT (STDMETHODCALLTYPE *GetBleedBox)(
        IXpsOMPage *This,
        XPS_RECT *bleedBox);

    HRESULT (STDMETHODCALLTYPE *SetBleedBox)(
        IXpsOMPage *This,
        const XPS_RECT *bleedBox);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IXpsOMPage *This,
        LPWSTR *language);

    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IXpsOMPage *This,
        LPCWSTR language);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IXpsOMPage *This,
        LPWSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IXpsOMPage *This,
        LPCWSTR name);

    HRESULT (STDMETHODCALLTYPE *GetIsHyperlinkTarget)(
        IXpsOMPage *This,
        WINBOOL *isHyperlinkTarget);

    HRESULT (STDMETHODCALLTYPE *SetIsHyperlinkTarget)(
        IXpsOMPage *This,
        WINBOOL isHyperlinkTarget);

    HRESULT (STDMETHODCALLTYPE *GetDictionary)(
        IXpsOMPage *This,
        IXpsOMDictionary **resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *GetDictionaryLocal)(
        IXpsOMPage *This,
        IXpsOMDictionary **resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *SetDictionaryLocal)(
        IXpsOMPage *This,
        IXpsOMDictionary *resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *GetDictionaryResource)(
        IXpsOMPage *This,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *SetDictionaryResource)(
        IXpsOMPage *This,
        IXpsOMRemoteDictionaryResource *remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IXpsOMPage *This,
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize);

    HRESULT (STDMETHODCALLTYPE *GenerateUnusedLookupKey)(
        IXpsOMPage *This,
        XPS_OBJECT_TYPE type,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMPage *This,
        IXpsOMPage **page);

    END_INTERFACE
} IXpsOMPageVtbl;

interface IXpsOMPage {
    CONST_VTBL IXpsOMPageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPage_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMPage_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMPage_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMPage methods ***/
#define IXpsOMPage_GetOwner(This,pageReference) (This)->lpVtbl->GetOwner(This,pageReference)
#define IXpsOMPage_GetVisuals(This,visuals) (This)->lpVtbl->GetVisuals(This,visuals)
#define IXpsOMPage_GetPageDimensions(This,pageDimensions) (This)->lpVtbl->GetPageDimensions(This,pageDimensions)
#define IXpsOMPage_SetPageDimensions(This,pageDimensions) (This)->lpVtbl->SetPageDimensions(This,pageDimensions)
#define IXpsOMPage_GetContentBox(This,contentBox) (This)->lpVtbl->GetContentBox(This,contentBox)
#define IXpsOMPage_SetContentBox(This,contentBox) (This)->lpVtbl->SetContentBox(This,contentBox)
#define IXpsOMPage_GetBleedBox(This,bleedBox) (This)->lpVtbl->GetBleedBox(This,bleedBox)
#define IXpsOMPage_SetBleedBox(This,bleedBox) (This)->lpVtbl->SetBleedBox(This,bleedBox)
#define IXpsOMPage_GetLanguage(This,language) (This)->lpVtbl->GetLanguage(This,language)
#define IXpsOMPage_SetLanguage(This,language) (This)->lpVtbl->SetLanguage(This,language)
#define IXpsOMPage_GetName(This,name) (This)->lpVtbl->GetName(This,name)
#define IXpsOMPage_SetName(This,name) (This)->lpVtbl->SetName(This,name)
#define IXpsOMPage_GetIsHyperlinkTarget(This,isHyperlinkTarget) (This)->lpVtbl->GetIsHyperlinkTarget(This,isHyperlinkTarget)
#define IXpsOMPage_SetIsHyperlinkTarget(This,isHyperlinkTarget) (This)->lpVtbl->SetIsHyperlinkTarget(This,isHyperlinkTarget)
#define IXpsOMPage_GetDictionary(This,resourceDictionary) (This)->lpVtbl->GetDictionary(This,resourceDictionary)
#define IXpsOMPage_GetDictionaryLocal(This,resourceDictionary) (This)->lpVtbl->GetDictionaryLocal(This,resourceDictionary)
#define IXpsOMPage_SetDictionaryLocal(This,resourceDictionary) (This)->lpVtbl->SetDictionaryLocal(This,resourceDictionary)
#define IXpsOMPage_GetDictionaryResource(This,remoteDictionaryResource) (This)->lpVtbl->GetDictionaryResource(This,remoteDictionaryResource)
#define IXpsOMPage_SetDictionaryResource(This,remoteDictionaryResource) (This)->lpVtbl->SetDictionaryResource(This,remoteDictionaryResource)
#define IXpsOMPage_Write(This,stream,optimizeMarkupSize) (This)->lpVtbl->Write(This,stream,optimizeMarkupSize)
#define IXpsOMPage_GenerateUnusedLookupKey(This,type,key) (This)->lpVtbl->GenerateUnusedLookupKey(This,type,key)
#define IXpsOMPage_Clone(This,page) (This)->lpVtbl->Clone(This,page)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPage_QueryInterface(IXpsOMPage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPage_AddRef(IXpsOMPage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPage_Release(IXpsOMPage* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMPage_GetPartName(IXpsOMPage* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMPage_SetPartName(IXpsOMPage* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMPage methods ***/
static inline HRESULT IXpsOMPage_GetOwner(IXpsOMPage* This,IXpsOMPageReference **pageReference) {
    return This->lpVtbl->GetOwner(This,pageReference);
}
static inline HRESULT IXpsOMPage_GetVisuals(IXpsOMPage* This,IXpsOMVisualCollection **visuals) {
    return This->lpVtbl->GetVisuals(This,visuals);
}
static inline HRESULT IXpsOMPage_GetPageDimensions(IXpsOMPage* This,XPS_SIZE *pageDimensions) {
    return This->lpVtbl->GetPageDimensions(This,pageDimensions);
}
static inline HRESULT IXpsOMPage_SetPageDimensions(IXpsOMPage* This,const XPS_SIZE *pageDimensions) {
    return This->lpVtbl->SetPageDimensions(This,pageDimensions);
}
static inline HRESULT IXpsOMPage_GetContentBox(IXpsOMPage* This,XPS_RECT *contentBox) {
    return This->lpVtbl->GetContentBox(This,contentBox);
}
static inline HRESULT IXpsOMPage_SetContentBox(IXpsOMPage* This,const XPS_RECT *contentBox) {
    return This->lpVtbl->SetContentBox(This,contentBox);
}
static inline HRESULT IXpsOMPage_GetBleedBox(IXpsOMPage* This,XPS_RECT *bleedBox) {
    return This->lpVtbl->GetBleedBox(This,bleedBox);
}
static inline HRESULT IXpsOMPage_SetBleedBox(IXpsOMPage* This,const XPS_RECT *bleedBox) {
    return This->lpVtbl->SetBleedBox(This,bleedBox);
}
static inline HRESULT IXpsOMPage_GetLanguage(IXpsOMPage* This,LPWSTR *language) {
    return This->lpVtbl->GetLanguage(This,language);
}
static inline HRESULT IXpsOMPage_SetLanguage(IXpsOMPage* This,LPCWSTR language) {
    return This->lpVtbl->SetLanguage(This,language);
}
static inline HRESULT IXpsOMPage_GetName(IXpsOMPage* This,LPWSTR *name) {
    return This->lpVtbl->GetName(This,name);
}
static inline HRESULT IXpsOMPage_SetName(IXpsOMPage* This,LPCWSTR name) {
    return This->lpVtbl->SetName(This,name);
}
static inline HRESULT IXpsOMPage_GetIsHyperlinkTarget(IXpsOMPage* This,WINBOOL *isHyperlinkTarget) {
    return This->lpVtbl->GetIsHyperlinkTarget(This,isHyperlinkTarget);
}
static inline HRESULT IXpsOMPage_SetIsHyperlinkTarget(IXpsOMPage* This,WINBOOL isHyperlinkTarget) {
    return This->lpVtbl->SetIsHyperlinkTarget(This,isHyperlinkTarget);
}
static inline HRESULT IXpsOMPage_GetDictionary(IXpsOMPage* This,IXpsOMDictionary **resourceDictionary) {
    return This->lpVtbl->GetDictionary(This,resourceDictionary);
}
static inline HRESULT IXpsOMPage_GetDictionaryLocal(IXpsOMPage* This,IXpsOMDictionary **resourceDictionary) {
    return This->lpVtbl->GetDictionaryLocal(This,resourceDictionary);
}
static inline HRESULT IXpsOMPage_SetDictionaryLocal(IXpsOMPage* This,IXpsOMDictionary *resourceDictionary) {
    return This->lpVtbl->SetDictionaryLocal(This,resourceDictionary);
}
static inline HRESULT IXpsOMPage_GetDictionaryResource(IXpsOMPage* This,IXpsOMRemoteDictionaryResource **remoteDictionaryResource) {
    return This->lpVtbl->GetDictionaryResource(This,remoteDictionaryResource);
}
static inline HRESULT IXpsOMPage_SetDictionaryResource(IXpsOMPage* This,IXpsOMRemoteDictionaryResource *remoteDictionaryResource) {
    return This->lpVtbl->SetDictionaryResource(This,remoteDictionaryResource);
}
static inline HRESULT IXpsOMPage_Write(IXpsOMPage* This,ISequentialStream *stream,WINBOOL optimizeMarkupSize) {
    return This->lpVtbl->Write(This,stream,optimizeMarkupSize);
}
static inline HRESULT IXpsOMPage_GenerateUnusedLookupKey(IXpsOMPage* This,XPS_OBJECT_TYPE type,LPWSTR *key) {
    return This->lpVtbl->GenerateUnusedLookupKey(This,type,key);
}
static inline HRESULT IXpsOMPage_Clone(IXpsOMPage* This,IXpsOMPage **page) {
    return This->lpVtbl->Clone(This,page);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMDocument interface
 */
#ifndef __IXpsOMDocument_INTERFACE_DEFINED__
#define __IXpsOMDocument_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMDocument, 0x2c2c94cb, 0xac5f, 0x4254, 0x8e,0xe9, 0x23,0x94,0x83,0x09,0xd9,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2c2c94cb-ac5f-4254-8ee9-23948309d9f0")
IXpsOMDocument : public IXpsOMPart
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMDocumentSequence **documentSequence) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPageReferences(
        IXpsOMPageReferenceCollection **pageReferences) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrintTicketResource(
        IXpsOMPrintTicketResource **printTicketResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrintTicketResource(
        IXpsOMPrintTicketResource *printTicketResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentStructureResource(
        IXpsOMDocumentStructureResource **documentStructureResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDocumentStructureResource(
        IXpsOMDocumentStructureResource *documentStructureResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureBlockResources(
        IXpsOMSignatureBlockResourceCollection **signatureBlockResources) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMDocument **document) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMDocument, 0x2c2c94cb, 0xac5f, 0x4254, 0x8e,0xe9, 0x23,0x94,0x83,0x09,0xd9,0xf0)
#endif
#else
typedef struct IXpsOMDocumentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMDocument *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMDocument *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMDocument *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMDocument *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMDocument *This,
        IOpcPartUri *partUri);

    /*** IXpsOMDocument methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMDocument *This,
        IXpsOMDocumentSequence **documentSequence);

    HRESULT (STDMETHODCALLTYPE *GetPageReferences)(
        IXpsOMDocument *This,
        IXpsOMPageReferenceCollection **pageReferences);

    HRESULT (STDMETHODCALLTYPE *GetPrintTicketResource)(
        IXpsOMDocument *This,
        IXpsOMPrintTicketResource **printTicketResource);

    HRESULT (STDMETHODCALLTYPE *SetPrintTicketResource)(
        IXpsOMDocument *This,
        IXpsOMPrintTicketResource *printTicketResource);

    HRESULT (STDMETHODCALLTYPE *GetDocumentStructureResource)(
        IXpsOMDocument *This,
        IXpsOMDocumentStructureResource **documentStructureResource);

    HRESULT (STDMETHODCALLTYPE *SetDocumentStructureResource)(
        IXpsOMDocument *This,
        IXpsOMDocumentStructureResource *documentStructureResource);

    HRESULT (STDMETHODCALLTYPE *GetSignatureBlockResources)(
        IXpsOMDocument *This,
        IXpsOMSignatureBlockResourceCollection **signatureBlockResources);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMDocument *This,
        IXpsOMDocument **document);

    END_INTERFACE
} IXpsOMDocumentVtbl;

interface IXpsOMDocument {
    CONST_VTBL IXpsOMDocumentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMDocument_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMDocument_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMDocument_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMDocument methods ***/
#define IXpsOMDocument_GetOwner(This,documentSequence) (This)->lpVtbl->GetOwner(This,documentSequence)
#define IXpsOMDocument_GetPageReferences(This,pageReferences) (This)->lpVtbl->GetPageReferences(This,pageReferences)
#define IXpsOMDocument_GetPrintTicketResource(This,printTicketResource) (This)->lpVtbl->GetPrintTicketResource(This,printTicketResource)
#define IXpsOMDocument_SetPrintTicketResource(This,printTicketResource) (This)->lpVtbl->SetPrintTicketResource(This,printTicketResource)
#define IXpsOMDocument_GetDocumentStructureResource(This,documentStructureResource) (This)->lpVtbl->GetDocumentStructureResource(This,documentStructureResource)
#define IXpsOMDocument_SetDocumentStructureResource(This,documentStructureResource) (This)->lpVtbl->SetDocumentStructureResource(This,documentStructureResource)
#define IXpsOMDocument_GetSignatureBlockResources(This,signatureBlockResources) (This)->lpVtbl->GetSignatureBlockResources(This,signatureBlockResources)
#define IXpsOMDocument_Clone(This,document) (This)->lpVtbl->Clone(This,document)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMDocument_QueryInterface(IXpsOMDocument* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMDocument_AddRef(IXpsOMDocument* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMDocument_Release(IXpsOMDocument* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMDocument_GetPartName(IXpsOMDocument* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMDocument_SetPartName(IXpsOMDocument* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMDocument methods ***/
static inline HRESULT IXpsOMDocument_GetOwner(IXpsOMDocument* This,IXpsOMDocumentSequence **documentSequence) {
    return This->lpVtbl->GetOwner(This,documentSequence);
}
static inline HRESULT IXpsOMDocument_GetPageReferences(IXpsOMDocument* This,IXpsOMPageReferenceCollection **pageReferences) {
    return This->lpVtbl->GetPageReferences(This,pageReferences);
}
static inline HRESULT IXpsOMDocument_GetPrintTicketResource(IXpsOMDocument* This,IXpsOMPrintTicketResource **printTicketResource) {
    return This->lpVtbl->GetPrintTicketResource(This,printTicketResource);
}
static inline HRESULT IXpsOMDocument_SetPrintTicketResource(IXpsOMDocument* This,IXpsOMPrintTicketResource *printTicketResource) {
    return This->lpVtbl->SetPrintTicketResource(This,printTicketResource);
}
static inline HRESULT IXpsOMDocument_GetDocumentStructureResource(IXpsOMDocument* This,IXpsOMDocumentStructureResource **documentStructureResource) {
    return This->lpVtbl->GetDocumentStructureResource(This,documentStructureResource);
}
static inline HRESULT IXpsOMDocument_SetDocumentStructureResource(IXpsOMDocument* This,IXpsOMDocumentStructureResource *documentStructureResource) {
    return This->lpVtbl->SetDocumentStructureResource(This,documentStructureResource);
}
static inline HRESULT IXpsOMDocument_GetSignatureBlockResources(IXpsOMDocument* This,IXpsOMSignatureBlockResourceCollection **signatureBlockResources) {
    return This->lpVtbl->GetSignatureBlockResources(This,signatureBlockResources);
}
static inline HRESULT IXpsOMDocument_Clone(IXpsOMDocument* This,IXpsOMDocument **document) {
    return This->lpVtbl->Clone(This,document);
}
#endif
#endif

#endif


#endif  /* __IXpsOMDocument_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMDocumentSequence interface
 */
#ifndef __IXpsOMDocumentSequence_INTERFACE_DEFINED__
#define __IXpsOMDocumentSequence_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMDocumentSequence, 0x56492eb4, 0xd8d5, 0x425e, 0x82,0x56, 0x4c,0x2b,0x64,0xad,0x02,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56492eb4-d8d5-425e-8256-4c2b64ad0264")
IXpsOMDocumentSequence : public IXpsOMPart
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMPackage **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocuments(
        IXpsOMDocumentCollection **documents) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrintTicketResource(
        IXpsOMPrintTicketResource **printTicketResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPrintTicketResource(
        IXpsOMPrintTicketResource *printTicketResource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMDocumentSequence, 0x56492eb4, 0xd8d5, 0x425e, 0x82,0x56, 0x4c,0x2b,0x64,0xad,0x02,0x64)
#endif
#else
typedef struct IXpsOMDocumentSequenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMDocumentSequence *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMDocumentSequence *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMDocumentSequence *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMDocumentSequence *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMDocumentSequence *This,
        IOpcPartUri *partUri);

    /*** IXpsOMDocumentSequence methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMDocumentSequence *This,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *GetDocuments)(
        IXpsOMDocumentSequence *This,
        IXpsOMDocumentCollection **documents);

    HRESULT (STDMETHODCALLTYPE *GetPrintTicketResource)(
        IXpsOMDocumentSequence *This,
        IXpsOMPrintTicketResource **printTicketResource);

    HRESULT (STDMETHODCALLTYPE *SetPrintTicketResource)(
        IXpsOMDocumentSequence *This,
        IXpsOMPrintTicketResource *printTicketResource);

    END_INTERFACE
} IXpsOMDocumentSequenceVtbl;

interface IXpsOMDocumentSequence {
    CONST_VTBL IXpsOMDocumentSequenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMDocumentSequence_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMDocumentSequence_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMDocumentSequence_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMDocumentSequence_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMDocumentSequence_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMDocumentSequence methods ***/
#define IXpsOMDocumentSequence_GetOwner(This,package) (This)->lpVtbl->GetOwner(This,package)
#define IXpsOMDocumentSequence_GetDocuments(This,documents) (This)->lpVtbl->GetDocuments(This,documents)
#define IXpsOMDocumentSequence_GetPrintTicketResource(This,printTicketResource) (This)->lpVtbl->GetPrintTicketResource(This,printTicketResource)
#define IXpsOMDocumentSequence_SetPrintTicketResource(This,printTicketResource) (This)->lpVtbl->SetPrintTicketResource(This,printTicketResource)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMDocumentSequence_QueryInterface(IXpsOMDocumentSequence* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMDocumentSequence_AddRef(IXpsOMDocumentSequence* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMDocumentSequence_Release(IXpsOMDocumentSequence* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMDocumentSequence_GetPartName(IXpsOMDocumentSequence* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMDocumentSequence_SetPartName(IXpsOMDocumentSequence* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMDocumentSequence methods ***/
static inline HRESULT IXpsOMDocumentSequence_GetOwner(IXpsOMDocumentSequence* This,IXpsOMPackage **package) {
    return This->lpVtbl->GetOwner(This,package);
}
static inline HRESULT IXpsOMDocumentSequence_GetDocuments(IXpsOMDocumentSequence* This,IXpsOMDocumentCollection **documents) {
    return This->lpVtbl->GetDocuments(This,documents);
}
static inline HRESULT IXpsOMDocumentSequence_GetPrintTicketResource(IXpsOMDocumentSequence* This,IXpsOMPrintTicketResource **printTicketResource) {
    return This->lpVtbl->GetPrintTicketResource(This,printTicketResource);
}
static inline HRESULT IXpsOMDocumentSequence_SetPrintTicketResource(IXpsOMDocumentSequence* This,IXpsOMPrintTicketResource *printTicketResource) {
    return This->lpVtbl->SetPrintTicketResource(This,printTicketResource);
}
#endif
#endif

#endif


#endif  /* __IXpsOMDocumentSequence_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMCoreProperties interface
 */
#ifndef __IXpsOMCoreProperties_INTERFACE_DEFINED__
#define __IXpsOMCoreProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMCoreProperties, 0x3340fe8f, 0x4027, 0x4aa1, 0x8f,0x5f, 0xd3,0x5a,0xe4,0x5f,0xe5,0x97);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3340fe8f-4027-4aa1-8f5f-d35ae45fe597")
IXpsOMCoreProperties : public IXpsOMPart
{
    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        IXpsOMPackage **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCategory(
        LPWSTR *category) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCategory(
        LPCWSTR category) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentStatus(
        LPWSTR *contentStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContentStatus(
        LPCWSTR contentStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentType(
        LPWSTR *contentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContentType(
        LPCWSTR contentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCreated(
        SYSTEMTIME *created) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCreated(
        const SYSTEMTIME *created) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCreator(
        LPWSTR *creator) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCreator(
        LPCWSTR creator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        LPWSTR *description) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDescription(
        LPCWSTR description) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIdentifier(
        LPWSTR *identifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIdentifier(
        LPCWSTR identifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetKeywords(
        LPWSTR *keywords) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetKeywords(
        LPCWSTR keywords) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguage(
        LPWSTR *language) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLanguage(
        LPCWSTR language) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastModifiedBy(
        LPWSTR *lastModifiedBy) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLastModifiedBy(
        LPCWSTR lastModifiedBy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastPrinted(
        SYSTEMTIME *lastPrinted) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLastPrinted(
        const SYSTEMTIME *lastPrinted) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetModified(
        SYSTEMTIME *modified) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetModified(
        const SYSTEMTIME *modified) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRevision(
        LPWSTR *revision) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRevision(
        LPCWSTR revision) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubject(
        LPWSTR *subject) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSubject(
        LPCWSTR subject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTitle(
        LPWSTR *title) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTitle(
        LPCWSTR title) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVersion(
        LPWSTR *version) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVersion(
        LPCWSTR version) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IXpsOMCoreProperties **coreProperties) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMCoreProperties, 0x3340fe8f, 0x4027, 0x4aa1, 0x8f,0x5f, 0xd3,0x5a,0xe4,0x5f,0xe5,0x97)
#endif
#else
typedef struct IXpsOMCorePropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMCoreProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMCoreProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMCoreProperties *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMCoreProperties *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMCoreProperties *This,
        IOpcPartUri *partUri);

    /*** IXpsOMCoreProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMCoreProperties *This,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *GetCategory)(
        IXpsOMCoreProperties *This,
        LPWSTR *category);

    HRESULT (STDMETHODCALLTYPE *SetCategory)(
        IXpsOMCoreProperties *This,
        LPCWSTR category);

    HRESULT (STDMETHODCALLTYPE *GetContentStatus)(
        IXpsOMCoreProperties *This,
        LPWSTR *contentStatus);

    HRESULT (STDMETHODCALLTYPE *SetContentStatus)(
        IXpsOMCoreProperties *This,
        LPCWSTR contentStatus);

    HRESULT (STDMETHODCALLTYPE *GetContentType)(
        IXpsOMCoreProperties *This,
        LPWSTR *contentType);

    HRESULT (STDMETHODCALLTYPE *SetContentType)(
        IXpsOMCoreProperties *This,
        LPCWSTR contentType);

    HRESULT (STDMETHODCALLTYPE *GetCreated)(
        IXpsOMCoreProperties *This,
        SYSTEMTIME *created);

    HRESULT (STDMETHODCALLTYPE *SetCreated)(
        IXpsOMCoreProperties *This,
        const SYSTEMTIME *created);

    HRESULT (STDMETHODCALLTYPE *GetCreator)(
        IXpsOMCoreProperties *This,
        LPWSTR *creator);

    HRESULT (STDMETHODCALLTYPE *SetCreator)(
        IXpsOMCoreProperties *This,
        LPCWSTR creator);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IXpsOMCoreProperties *This,
        LPWSTR *description);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        IXpsOMCoreProperties *This,
        LPCWSTR description);

    HRESULT (STDMETHODCALLTYPE *GetIdentifier)(
        IXpsOMCoreProperties *This,
        LPWSTR *identifier);

    HRESULT (STDMETHODCALLTYPE *SetIdentifier)(
        IXpsOMCoreProperties *This,
        LPCWSTR identifier);

    HRESULT (STDMETHODCALLTYPE *GetKeywords)(
        IXpsOMCoreProperties *This,
        LPWSTR *keywords);

    HRESULT (STDMETHODCALLTYPE *SetKeywords)(
        IXpsOMCoreProperties *This,
        LPCWSTR keywords);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IXpsOMCoreProperties *This,
        LPWSTR *language);

    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IXpsOMCoreProperties *This,
        LPCWSTR language);

    HRESULT (STDMETHODCALLTYPE *GetLastModifiedBy)(
        IXpsOMCoreProperties *This,
        LPWSTR *lastModifiedBy);

    HRESULT (STDMETHODCALLTYPE *SetLastModifiedBy)(
        IXpsOMCoreProperties *This,
        LPCWSTR lastModifiedBy);

    HRESULT (STDMETHODCALLTYPE *GetLastPrinted)(
        IXpsOMCoreProperties *This,
        SYSTEMTIME *lastPrinted);

    HRESULT (STDMETHODCALLTYPE *SetLastPrinted)(
        IXpsOMCoreProperties *This,
        const SYSTEMTIME *lastPrinted);

    HRESULT (STDMETHODCALLTYPE *GetModified)(
        IXpsOMCoreProperties *This,
        SYSTEMTIME *modified);

    HRESULT (STDMETHODCALLTYPE *SetModified)(
        IXpsOMCoreProperties *This,
        const SYSTEMTIME *modified);

    HRESULT (STDMETHODCALLTYPE *GetRevision)(
        IXpsOMCoreProperties *This,
        LPWSTR *revision);

    HRESULT (STDMETHODCALLTYPE *SetRevision)(
        IXpsOMCoreProperties *This,
        LPCWSTR revision);

    HRESULT (STDMETHODCALLTYPE *GetSubject)(
        IXpsOMCoreProperties *This,
        LPWSTR *subject);

    HRESULT (STDMETHODCALLTYPE *SetSubject)(
        IXpsOMCoreProperties *This,
        LPCWSTR subject);

    HRESULT (STDMETHODCALLTYPE *GetTitle)(
        IXpsOMCoreProperties *This,
        LPWSTR *title);

    HRESULT (STDMETHODCALLTYPE *SetTitle)(
        IXpsOMCoreProperties *This,
        LPCWSTR title);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IXpsOMCoreProperties *This,
        LPWSTR *version);

    HRESULT (STDMETHODCALLTYPE *SetVersion)(
        IXpsOMCoreProperties *This,
        LPCWSTR version);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMCoreProperties *This,
        IXpsOMCoreProperties **coreProperties);

    END_INTERFACE
} IXpsOMCorePropertiesVtbl;

interface IXpsOMCoreProperties {
    CONST_VTBL IXpsOMCorePropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMCoreProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMCoreProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMCoreProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMCoreProperties_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMCoreProperties_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMCoreProperties methods ***/
#define IXpsOMCoreProperties_GetOwner(This,package) (This)->lpVtbl->GetOwner(This,package)
#define IXpsOMCoreProperties_GetCategory(This,category) (This)->lpVtbl->GetCategory(This,category)
#define IXpsOMCoreProperties_SetCategory(This,category) (This)->lpVtbl->SetCategory(This,category)
#define IXpsOMCoreProperties_GetContentStatus(This,contentStatus) (This)->lpVtbl->GetContentStatus(This,contentStatus)
#define IXpsOMCoreProperties_SetContentStatus(This,contentStatus) (This)->lpVtbl->SetContentStatus(This,contentStatus)
#define IXpsOMCoreProperties_GetContentType(This,contentType) (This)->lpVtbl->GetContentType(This,contentType)
#define IXpsOMCoreProperties_SetContentType(This,contentType) (This)->lpVtbl->SetContentType(This,contentType)
#define IXpsOMCoreProperties_GetCreated(This,created) (This)->lpVtbl->GetCreated(This,created)
#define IXpsOMCoreProperties_SetCreated(This,created) (This)->lpVtbl->SetCreated(This,created)
#define IXpsOMCoreProperties_GetCreator(This,creator) (This)->lpVtbl->GetCreator(This,creator)
#define IXpsOMCoreProperties_SetCreator(This,creator) (This)->lpVtbl->SetCreator(This,creator)
#define IXpsOMCoreProperties_GetDescription(This,description) (This)->lpVtbl->GetDescription(This,description)
#define IXpsOMCoreProperties_SetDescription(This,description) (This)->lpVtbl->SetDescription(This,description)
#define IXpsOMCoreProperties_GetIdentifier(This,identifier) (This)->lpVtbl->GetIdentifier(This,identifier)
#define IXpsOMCoreProperties_SetIdentifier(This,identifier) (This)->lpVtbl->SetIdentifier(This,identifier)
#define IXpsOMCoreProperties_GetKeywords(This,keywords) (This)->lpVtbl->GetKeywords(This,keywords)
#define IXpsOMCoreProperties_SetKeywords(This,keywords) (This)->lpVtbl->SetKeywords(This,keywords)
#define IXpsOMCoreProperties_GetLanguage(This,language) (This)->lpVtbl->GetLanguage(This,language)
#define IXpsOMCoreProperties_SetLanguage(This,language) (This)->lpVtbl->SetLanguage(This,language)
#define IXpsOMCoreProperties_GetLastModifiedBy(This,lastModifiedBy) (This)->lpVtbl->GetLastModifiedBy(This,lastModifiedBy)
#define IXpsOMCoreProperties_SetLastModifiedBy(This,lastModifiedBy) (This)->lpVtbl->SetLastModifiedBy(This,lastModifiedBy)
#define IXpsOMCoreProperties_GetLastPrinted(This,lastPrinted) (This)->lpVtbl->GetLastPrinted(This,lastPrinted)
#define IXpsOMCoreProperties_SetLastPrinted(This,lastPrinted) (This)->lpVtbl->SetLastPrinted(This,lastPrinted)
#define IXpsOMCoreProperties_GetModified(This,modified) (This)->lpVtbl->GetModified(This,modified)
#define IXpsOMCoreProperties_SetModified(This,modified) (This)->lpVtbl->SetModified(This,modified)
#define IXpsOMCoreProperties_GetRevision(This,revision) (This)->lpVtbl->GetRevision(This,revision)
#define IXpsOMCoreProperties_SetRevision(This,revision) (This)->lpVtbl->SetRevision(This,revision)
#define IXpsOMCoreProperties_GetSubject(This,subject) (This)->lpVtbl->GetSubject(This,subject)
#define IXpsOMCoreProperties_SetSubject(This,subject) (This)->lpVtbl->SetSubject(This,subject)
#define IXpsOMCoreProperties_GetTitle(This,title) (This)->lpVtbl->GetTitle(This,title)
#define IXpsOMCoreProperties_SetTitle(This,title) (This)->lpVtbl->SetTitle(This,title)
#define IXpsOMCoreProperties_GetVersion(This,version) (This)->lpVtbl->GetVersion(This,version)
#define IXpsOMCoreProperties_SetVersion(This,version) (This)->lpVtbl->SetVersion(This,version)
#define IXpsOMCoreProperties_Clone(This,coreProperties) (This)->lpVtbl->Clone(This,coreProperties)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMCoreProperties_QueryInterface(IXpsOMCoreProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMCoreProperties_AddRef(IXpsOMCoreProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMCoreProperties_Release(IXpsOMCoreProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMCoreProperties_GetPartName(IXpsOMCoreProperties* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMCoreProperties_SetPartName(IXpsOMCoreProperties* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMCoreProperties methods ***/
static inline HRESULT IXpsOMCoreProperties_GetOwner(IXpsOMCoreProperties* This,IXpsOMPackage **package) {
    return This->lpVtbl->GetOwner(This,package);
}
static inline HRESULT IXpsOMCoreProperties_GetCategory(IXpsOMCoreProperties* This,LPWSTR *category) {
    return This->lpVtbl->GetCategory(This,category);
}
static inline HRESULT IXpsOMCoreProperties_SetCategory(IXpsOMCoreProperties* This,LPCWSTR category) {
    return This->lpVtbl->SetCategory(This,category);
}
static inline HRESULT IXpsOMCoreProperties_GetContentStatus(IXpsOMCoreProperties* This,LPWSTR *contentStatus) {
    return This->lpVtbl->GetContentStatus(This,contentStatus);
}
static inline HRESULT IXpsOMCoreProperties_SetContentStatus(IXpsOMCoreProperties* This,LPCWSTR contentStatus) {
    return This->lpVtbl->SetContentStatus(This,contentStatus);
}
static inline HRESULT IXpsOMCoreProperties_GetContentType(IXpsOMCoreProperties* This,LPWSTR *contentType) {
    return This->lpVtbl->GetContentType(This,contentType);
}
static inline HRESULT IXpsOMCoreProperties_SetContentType(IXpsOMCoreProperties* This,LPCWSTR contentType) {
    return This->lpVtbl->SetContentType(This,contentType);
}
static inline HRESULT IXpsOMCoreProperties_GetCreated(IXpsOMCoreProperties* This,SYSTEMTIME *created) {
    return This->lpVtbl->GetCreated(This,created);
}
static inline HRESULT IXpsOMCoreProperties_SetCreated(IXpsOMCoreProperties* This,const SYSTEMTIME *created) {
    return This->lpVtbl->SetCreated(This,created);
}
static inline HRESULT IXpsOMCoreProperties_GetCreator(IXpsOMCoreProperties* This,LPWSTR *creator) {
    return This->lpVtbl->GetCreator(This,creator);
}
static inline HRESULT IXpsOMCoreProperties_SetCreator(IXpsOMCoreProperties* This,LPCWSTR creator) {
    return This->lpVtbl->SetCreator(This,creator);
}
static inline HRESULT IXpsOMCoreProperties_GetDescription(IXpsOMCoreProperties* This,LPWSTR *description) {
    return This->lpVtbl->GetDescription(This,description);
}
static inline HRESULT IXpsOMCoreProperties_SetDescription(IXpsOMCoreProperties* This,LPCWSTR description) {
    return This->lpVtbl->SetDescription(This,description);
}
static inline HRESULT IXpsOMCoreProperties_GetIdentifier(IXpsOMCoreProperties* This,LPWSTR *identifier) {
    return This->lpVtbl->GetIdentifier(This,identifier);
}
static inline HRESULT IXpsOMCoreProperties_SetIdentifier(IXpsOMCoreProperties* This,LPCWSTR identifier) {
    return This->lpVtbl->SetIdentifier(This,identifier);
}
static inline HRESULT IXpsOMCoreProperties_GetKeywords(IXpsOMCoreProperties* This,LPWSTR *keywords) {
    return This->lpVtbl->GetKeywords(This,keywords);
}
static inline HRESULT IXpsOMCoreProperties_SetKeywords(IXpsOMCoreProperties* This,LPCWSTR keywords) {
    return This->lpVtbl->SetKeywords(This,keywords);
}
static inline HRESULT IXpsOMCoreProperties_GetLanguage(IXpsOMCoreProperties* This,LPWSTR *language) {
    return This->lpVtbl->GetLanguage(This,language);
}
static inline HRESULT IXpsOMCoreProperties_SetLanguage(IXpsOMCoreProperties* This,LPCWSTR language) {
    return This->lpVtbl->SetLanguage(This,language);
}
static inline HRESULT IXpsOMCoreProperties_GetLastModifiedBy(IXpsOMCoreProperties* This,LPWSTR *lastModifiedBy) {
    return This->lpVtbl->GetLastModifiedBy(This,lastModifiedBy);
}
static inline HRESULT IXpsOMCoreProperties_SetLastModifiedBy(IXpsOMCoreProperties* This,LPCWSTR lastModifiedBy) {
    return This->lpVtbl->SetLastModifiedBy(This,lastModifiedBy);
}
static inline HRESULT IXpsOMCoreProperties_GetLastPrinted(IXpsOMCoreProperties* This,SYSTEMTIME *lastPrinted) {
    return This->lpVtbl->GetLastPrinted(This,lastPrinted);
}
static inline HRESULT IXpsOMCoreProperties_SetLastPrinted(IXpsOMCoreProperties* This,const SYSTEMTIME *lastPrinted) {
    return This->lpVtbl->SetLastPrinted(This,lastPrinted);
}
static inline HRESULT IXpsOMCoreProperties_GetModified(IXpsOMCoreProperties* This,SYSTEMTIME *modified) {
    return This->lpVtbl->GetModified(This,modified);
}
static inline HRESULT IXpsOMCoreProperties_SetModified(IXpsOMCoreProperties* This,const SYSTEMTIME *modified) {
    return This->lpVtbl->SetModified(This,modified);
}
static inline HRESULT IXpsOMCoreProperties_GetRevision(IXpsOMCoreProperties* This,LPWSTR *revision) {
    return This->lpVtbl->GetRevision(This,revision);
}
static inline HRESULT IXpsOMCoreProperties_SetRevision(IXpsOMCoreProperties* This,LPCWSTR revision) {
    return This->lpVtbl->SetRevision(This,revision);
}
static inline HRESULT IXpsOMCoreProperties_GetSubject(IXpsOMCoreProperties* This,LPWSTR *subject) {
    return This->lpVtbl->GetSubject(This,subject);
}
static inline HRESULT IXpsOMCoreProperties_SetSubject(IXpsOMCoreProperties* This,LPCWSTR subject) {
    return This->lpVtbl->SetSubject(This,subject);
}
static inline HRESULT IXpsOMCoreProperties_GetTitle(IXpsOMCoreProperties* This,LPWSTR *title) {
    return This->lpVtbl->GetTitle(This,title);
}
static inline HRESULT IXpsOMCoreProperties_SetTitle(IXpsOMCoreProperties* This,LPCWSTR title) {
    return This->lpVtbl->SetTitle(This,title);
}
static inline HRESULT IXpsOMCoreProperties_GetVersion(IXpsOMCoreProperties* This,LPWSTR *version) {
    return This->lpVtbl->GetVersion(This,version);
}
static inline HRESULT IXpsOMCoreProperties_SetVersion(IXpsOMCoreProperties* This,LPCWSTR version) {
    return This->lpVtbl->SetVersion(This,version);
}
static inline HRESULT IXpsOMCoreProperties_Clone(IXpsOMCoreProperties* This,IXpsOMCoreProperties **coreProperties) {
    return This->lpVtbl->Clone(This,coreProperties);
}
#endif
#endif

#endif


#endif  /* __IXpsOMCoreProperties_INTERFACE_DEFINED__ */

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IXpsOMThumbnailGenerator interface
 */
#ifndef __IXpsOMThumbnailGenerator_INTERFACE_DEFINED__
#define __IXpsOMThumbnailGenerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMThumbnailGenerator, 0x15b873d5, 0x1971, 0x41e8, 0x83,0xa3, 0x65,0x78,0x40,0x30,0x64,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("15b873d5-1971-41e8-83a3-6578403064c7")
IXpsOMThumbnailGenerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GenerateThumbnail(
        IXpsOMPage *page,
        XPS_IMAGE_TYPE thumbnailType,
        XPS_THUMBNAIL_SIZE thumbnailSize,
        IOpcPartUri *imageResourcePartName,
        IXpsOMImageResource **imageResource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMThumbnailGenerator, 0x15b873d5, 0x1971, 0x41e8, 0x83,0xa3, 0x65,0x78,0x40,0x30,0x64,0xc7)
#endif
#else
typedef struct IXpsOMThumbnailGeneratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMThumbnailGenerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMThumbnailGenerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMThumbnailGenerator *This);

    /*** IXpsOMThumbnailGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GenerateThumbnail)(
        IXpsOMThumbnailGenerator *This,
        IXpsOMPage *page,
        XPS_IMAGE_TYPE thumbnailType,
        XPS_THUMBNAIL_SIZE thumbnailSize,
        IOpcPartUri *imageResourcePartName,
        IXpsOMImageResource **imageResource);

    END_INTERFACE
} IXpsOMThumbnailGeneratorVtbl;

interface IXpsOMThumbnailGenerator {
    CONST_VTBL IXpsOMThumbnailGeneratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMThumbnailGenerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMThumbnailGenerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMThumbnailGenerator_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMThumbnailGenerator methods ***/
#define IXpsOMThumbnailGenerator_GenerateThumbnail(This,page,thumbnailType,thumbnailSize,imageResourcePartName,imageResource) (This)->lpVtbl->GenerateThumbnail(This,page,thumbnailType,thumbnailSize,imageResourcePartName,imageResource)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMThumbnailGenerator_QueryInterface(IXpsOMThumbnailGenerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMThumbnailGenerator_AddRef(IXpsOMThumbnailGenerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMThumbnailGenerator_Release(IXpsOMThumbnailGenerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMThumbnailGenerator methods ***/
static inline HRESULT IXpsOMThumbnailGenerator_GenerateThumbnail(IXpsOMThumbnailGenerator* This,IXpsOMPage *page,XPS_IMAGE_TYPE thumbnailType,XPS_THUMBNAIL_SIZE thumbnailSize,IOpcPartUri *imageResourcePartName,IXpsOMImageResource **imageResource) {
    return This->lpVtbl->GenerateThumbnail(This,page,thumbnailType,thumbnailSize,imageResourcePartName,imageResource);
}
#endif
#endif

#endif


#endif  /* __IXpsOMThumbnailGenerator_INTERFACE_DEFINED__ */

#endif
#ifndef __MSXPS_LIBRARY_DEFINED__
#define __MSXPS_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_MSXPS, 0xb47491a0, 0xcf33, 0x4fe4, 0x9a,0x48, 0xb0,0xac,0xda,0xe2,0x07,0xe8);

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * XpsOMObjectFactory coclass
 */

DEFINE_GUID(CLSID_XpsOMObjectFactory, 0xe974d26d, 0x3d9b, 0x4d47, 0x88,0xcc, 0x38,0x72,0xf2,0xdc,0x35,0x85);

#ifdef __cplusplus
class DECLSPEC_UUID("e974d26d-3d9b-4d47-88cc-3872f2dc3585") XpsOMObjectFactory;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(XpsOMObjectFactory, 0xe974d26d, 0x3d9b, 0x4d47, 0x88,0xcc, 0x38,0x72,0xf2,0xdc,0x35,0x85)
#endif
#endif

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * XpsOMThumbnailGenerator coclass
 */

DEFINE_GUID(CLSID_XpsOMThumbnailGenerator, 0x7e4a23e2, 0xb969, 0x4761, 0xbe,0x35, 0x1a,0x8c,0xed,0x58,0xe3,0x23);

#ifdef __cplusplus
class DECLSPEC_UUID("7e4a23e2-b969-4761-be35-1a8ced58e323") XpsOMThumbnailGenerator;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(XpsOMThumbnailGenerator, 0x7e4a23e2, 0xb969, 0x4761, 0xbe,0x35, 0x1a,0x8c,0xed,0x58,0xe3,0x23)
#endif
#endif

#endif
#endif /* __MSXPS_LIBRARY_DEFINED__ */
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __xpsobjectmodel_h__ */
