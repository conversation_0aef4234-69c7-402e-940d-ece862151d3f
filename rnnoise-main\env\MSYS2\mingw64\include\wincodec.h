/*** Autogenerated by WIDL 10.12 from include/wincodec.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wincodec_h__
#define __wincodec_h__

/* Forward declarations */

#ifndef __IWICColorContext_FWD_DEFINED__
#define __IWICColorContext_FWD_DEFINED__
typedef interface IWICColorContext IWICColorContext;
#ifdef __cplusplus
interface IWICColorContext;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapSource_FWD_DEFINED__
#define __IWICBitmapSource_FWD_DEFINED__
typedef interface IWICBitmapSource IWICBitmapSource;
#ifdef __cplusplus
interface IWICBitmapSource;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapLock_FWD_DEFINED__
#define __IWICBitmapLock_FWD_DEFINED__
typedef interface IWICBitmapLock IWICBitmapLock;
#ifdef __cplusplus
interface IWICBitmapLock;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapFlipRotator_FWD_DEFINED__
#define __IWICBitmapFlipRotator_FWD_DEFINED__
typedef interface IWICBitmapFlipRotator IWICBitmapFlipRotator;
#ifdef __cplusplus
interface IWICBitmapFlipRotator;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmap_FWD_DEFINED__
#define __IWICBitmap_FWD_DEFINED__
typedef interface IWICBitmap IWICBitmap;
#ifdef __cplusplus
interface IWICBitmap;
#endif /* __cplusplus */
#endif

#ifndef __IWICPalette_FWD_DEFINED__
#define __IWICPalette_FWD_DEFINED__
typedef interface IWICPalette IWICPalette;
#ifdef __cplusplus
interface IWICPalette;
#endif /* __cplusplus */
#endif

#ifndef __IWICComponentInfo_FWD_DEFINED__
#define __IWICComponentInfo_FWD_DEFINED__
typedef interface IWICComponentInfo IWICComponentInfo;
#ifdef __cplusplus
interface IWICComponentInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataQueryReader_FWD_DEFINED__
#define __IWICMetadataQueryReader_FWD_DEFINED__
typedef interface IWICMetadataQueryReader IWICMetadataQueryReader;
#ifdef __cplusplus
interface IWICMetadataQueryReader;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataQueryWriter_FWD_DEFINED__
#define __IWICMetadataQueryWriter_FWD_DEFINED__
typedef interface IWICMetadataQueryWriter IWICMetadataQueryWriter;
#ifdef __cplusplus
interface IWICMetadataQueryWriter;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapFrameDecode_FWD_DEFINED__
#define __IWICBitmapFrameDecode_FWD_DEFINED__
typedef interface IWICBitmapFrameDecode IWICBitmapFrameDecode;
#ifdef __cplusplus
interface IWICBitmapFrameDecode;
#endif /* __cplusplus */
#endif

#ifndef __IWICPixelFormatInfo_FWD_DEFINED__
#define __IWICPixelFormatInfo_FWD_DEFINED__
typedef interface IWICPixelFormatInfo IWICPixelFormatInfo;
#ifdef __cplusplus
interface IWICPixelFormatInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICPixelFormatInfo2_FWD_DEFINED__
#define __IWICPixelFormatInfo2_FWD_DEFINED__
typedef interface IWICPixelFormatInfo2 IWICPixelFormatInfo2;
#ifdef __cplusplus
interface IWICPixelFormatInfo2;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapCodecInfo_FWD_DEFINED__
#define __IWICBitmapCodecInfo_FWD_DEFINED__
typedef interface IWICBitmapCodecInfo IWICBitmapCodecInfo;
#ifdef __cplusplus
interface IWICBitmapCodecInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapDecoderInfo_FWD_DEFINED__
#define __IWICBitmapDecoderInfo_FWD_DEFINED__
typedef interface IWICBitmapDecoderInfo IWICBitmapDecoderInfo;
#ifdef __cplusplus
interface IWICBitmapDecoderInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapDecoder_FWD_DEFINED__
#define __IWICBitmapDecoder_FWD_DEFINED__
typedef interface IWICBitmapDecoder IWICBitmapDecoder;
#ifdef __cplusplus
interface IWICBitmapDecoder;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapFrameEncode_FWD_DEFINED__
#define __IWICBitmapFrameEncode_FWD_DEFINED__
typedef interface IWICBitmapFrameEncode IWICBitmapFrameEncode;
#ifdef __cplusplus
interface IWICBitmapFrameEncode;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapEncoderInfo_FWD_DEFINED__
#define __IWICBitmapEncoderInfo_FWD_DEFINED__
typedef interface IWICBitmapEncoderInfo IWICBitmapEncoderInfo;
#ifdef __cplusplus
interface IWICBitmapEncoderInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapEncoder_FWD_DEFINED__
#define __IWICBitmapEncoder_FWD_DEFINED__
typedef interface IWICBitmapEncoder IWICBitmapEncoder;
#ifdef __cplusplus
interface IWICBitmapEncoder;
#endif /* __cplusplus */
#endif

#ifndef __IWICFormatConverter_FWD_DEFINED__
#define __IWICFormatConverter_FWD_DEFINED__
typedef interface IWICFormatConverter IWICFormatConverter;
#ifdef __cplusplus
interface IWICFormatConverter;
#endif /* __cplusplus */
#endif

#ifndef __IWICFormatConverterInfo_FWD_DEFINED__
#define __IWICFormatConverterInfo_FWD_DEFINED__
typedef interface IWICFormatConverterInfo IWICFormatConverterInfo;
#ifdef __cplusplus
interface IWICFormatConverterInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICStream_FWD_DEFINED__
#define __IWICStream_FWD_DEFINED__
typedef interface IWICStream IWICStream;
#ifdef __cplusplus
interface IWICStream;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapScaler_FWD_DEFINED__
#define __IWICBitmapScaler_FWD_DEFINED__
typedef interface IWICBitmapScaler IWICBitmapScaler;
#ifdef __cplusplus
interface IWICBitmapScaler;
#endif /* __cplusplus */
#endif

#ifndef __IWICBitmapClipper_FWD_DEFINED__
#define __IWICBitmapClipper_FWD_DEFINED__
typedef interface IWICBitmapClipper IWICBitmapClipper;
#ifdef __cplusplus
interface IWICBitmapClipper;
#endif /* __cplusplus */
#endif

#ifndef __IWICColorTransform_FWD_DEFINED__
#define __IWICColorTransform_FWD_DEFINED__
typedef interface IWICColorTransform IWICColorTransform;
#ifdef __cplusplus
interface IWICColorTransform;
#endif /* __cplusplus */
#endif

#ifndef __IWICFastMetadataEncoder_FWD_DEFINED__
#define __IWICFastMetadataEncoder_FWD_DEFINED__
typedef interface IWICFastMetadataEncoder IWICFastMetadataEncoder;
#ifdef __cplusplus
interface IWICFastMetadataEncoder;
#endif /* __cplusplus */
#endif

#ifndef __IWICImageEncoder_FWD_DEFINED__
#define __IWICImageEncoder_FWD_DEFINED__
typedef interface IWICImageEncoder IWICImageEncoder;
#ifdef __cplusplus
interface IWICImageEncoder;
#endif /* __cplusplus */
#endif

#ifndef __IWICImagingFactory_FWD_DEFINED__
#define __IWICImagingFactory_FWD_DEFINED__
typedef interface IWICImagingFactory IWICImagingFactory;
#ifdef __cplusplus
interface IWICImagingFactory;
#endif /* __cplusplus */
#endif

#ifndef __IWICImagingFactory2_FWD_DEFINED__
#define __IWICImagingFactory2_FWD_DEFINED__
typedef interface IWICImagingFactory2 IWICImagingFactory2;
#ifdef __cplusplus
interface IWICImagingFactory2;
#endif /* __cplusplus */
#endif

#ifndef __IWICEnumMetadataItem_FWD_DEFINED__
#define __IWICEnumMetadataItem_FWD_DEFINED__
typedef interface IWICEnumMetadataItem IWICEnumMetadataItem;
#ifdef __cplusplus
interface IWICEnumMetadataItem;
#endif /* __cplusplus */
#endif

#ifndef __IWICDdsDecoder_FWD_DEFINED__
#define __IWICDdsDecoder_FWD_DEFINED__
typedef interface IWICDdsDecoder IWICDdsDecoder;
#ifdef __cplusplus
interface IWICDdsDecoder;
#endif /* __cplusplus */
#endif

#ifndef __IWICDdsEncoder_FWD_DEFINED__
#define __IWICDdsEncoder_FWD_DEFINED__
typedef interface IWICDdsEncoder IWICDdsEncoder;
#ifdef __cplusplus
interface IWICDdsEncoder;
#endif /* __cplusplus */
#endif

#ifndef __IWICDdsFrameDecode_FWD_DEFINED__
#define __IWICDdsFrameDecode_FWD_DEFINED__
typedef interface IWICDdsFrameDecode IWICDdsFrameDecode;
#ifdef __cplusplus
interface IWICDdsFrameDecode;
#endif /* __cplusplus */
#endif

#ifndef __IWICWineDecoder_FWD_DEFINED__
#define __IWICWineDecoder_FWD_DEFINED__
typedef interface IWICWineDecoder IWICWineDecoder;
#ifdef __cplusplus
interface IWICWineDecoder;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <wtypes.h>
#include <propidl.h>
#include <ocidl.h>
#include <dxgiformat.h>

#ifdef __cplusplus
extern "C" {
#endif

#include "dcommon.h"
#define WINCODEC_SDK_VERSION 0x0236
typedef enum WICDecodeOptions {
    WICDecodeMetadataCacheOnDemand = 0x0,
    WICDecodeMetadataCacheOnLoad = 0x1,
    WICMETADATACACHEOPTION_FORCE_DWORD = 0x7fffffff
} WICDecodeOptions;
typedef enum WICBitmapCreateCacheOption {
    WICBitmapNoCache = 0x0,
    WICBitmapCacheOnDemand = 0x1,
    WICBitmapCacheOnLoad = 0x2,
    WICBITMAPCREATECACHEOPTION_FORCE_DWORD = 0x7fffffff
} WICBitmapCreateCacheOption;
typedef enum WICBitmapAlphaChannelOption {
    WICBitmapUseAlpha = 0x0,
    WICBitmapUsePremultipliedAlpha = 0x1,
    WICBitmapIgnoreAlpha = 0x2,
    WICBITMAPALPHACHANNELOPTIONS_FORCE_DWORD = 0x7fffffff
} WICBitmapAlphaChannelOption;
typedef enum WICBitmapDecoderCapabilities {
    WICBitmapDecoderCapabilitySameEncoder = 0x1,
    WICBitmapDecoderCapabilityCanDecodeAllImages = 0x2,
    WICBitmapDecoderCapabilityCanDecodeSomeImages = 0x4,
    WICBitmapDecoderCapabilityCanEnumerateMetadata = 0x8,
    WICBitmapDecoderCapabilityCanDecodeThumbnail = 0x10
} WICBitmapDecoderCapabilities;
typedef enum WICBitmapDitherType {
    WICBitmapDitherTypeNone = 0x0,
    WICBitmapDitherTypeSolid = 0x0,
    WICBitmapDitherTypeOrdered4x4 = 0x1,
    WICBitmapDitherTypeOrdered8x8 = 0x2,
    WICBitmapDitherTypeOrdered16x16 = 0x3,
    WICBitmapDitherTypeSpiral4x4 = 0x4,
    WICBitmapDitherTypeSpiral8x8 = 0x5,
    WICBitmapDitherTypeDualSpiral4x4 = 0x6,
    WICBitmapDitherTypeDualSpiral8x8 = 0x7,
    WICBitmapDitherTypeErrorDiffusion = 0x8,
    WICBITMAPDITHERTYPE_FORCE_DWORD = 0x7fffffff
} WICBitmapDitherType;
typedef enum WICBitmapEncoderCacheOption {
    WICBitmapEncoderCacheInMemory = 0x0,
    WICBitmapEncoderCacheTempFile = 0x1,
    WICBitmapEncoderNoCache = 0x2,
    WICBITMAPENCODERCACHEOPTION_FORCE_DWORD = 0x7fffffff
} WICBitmapEncoderCacheOption;
typedef enum WICBitmapInterpolationMode {
    WICBitmapInterpolationModeNearestNeighbor = 0x0,
    WICBitmapInterpolationModeLinear = 0x1,
    WICBitmapInterpolationModeCubic = 0x2,
    WICBitmapInterpolationModeFant = 0x3,
    WICBITMAPINTERPOLATIONMODE_FORCE_DWORD = 0x7fffffff
} WICBitmapInterpolationMode;
typedef enum WICBitmapLockFlags {
    WICBitmapLockRead = 0x1,
    WICBitmapLockWrite = 0x2,
    WICBITMAPLOCKFLAGS_FORCE_DWORD = 0x7fffffff
} WICBitmapLockFlags;
typedef enum WICBitmapPaletteType {
    WICBitmapPaletteTypeCustom = 0x0,
    WICBitmapPaletteTypeMedianCut = 0x1,
    WICBitmapPaletteTypeFixedBW = 0x2,
    WICBitmapPaletteTypeFixedHalftone8 = 0x3,
    WICBitmapPaletteTypeFixedHalftone27 = 0x4,
    WICBitmapPaletteTypeFixedHalftone64 = 0x5,
    WICBitmapPaletteTypeFixedHalftone125 = 0x6,
    WICBitmapPaletteTypeFixedHalftone216 = 0x7,
    WICBitmapPaletteTypeFixedWebPalette = WICBitmapPaletteTypeFixedHalftone216,
    WICBitmapPaletteTypeFixedHalftone252 = 0x8,
    WICBitmapPaletteTypeFixedHalftone256 = 0x9,
    WICBitmapPaletteTypeFixedGray4 = 0xa,
    WICBitmapPaletteTypeFixedGray16 = 0xb,
    WICBitmapPaletteTypeFixedGray256 = 0xc,
    WICBITMAPPALETTETYPE_FORCE_DWORD = 0x7fffffff
} WICBitmapPaletteType;
typedef enum WICBitmapTransformOptions {
    WICBitmapTransformRotate0 = 0x0,
    WICBitmapTransformRotate90 = 0x1,
    WICBitmapTransformRotate180 = 0x2,
    WICBitmapTransformRotate270 = 0x3,
    WICBitmapTransformFlipHorizontal = 0x8,
    WICBitmapTransformFlipVertical = 0x10,
    WICBITMAPTRANSFORMOPTIONS_FORCE_DWORD = 0x7fffffff
} WICBitmapTransformOptions;
typedef enum WICColorContextType {
    WICColorContextUninitialized = 0x0,
    WICColorContextProfile = 0x1,
    WICColorContextExifColorSpace = 0x2
} WICColorContextType;
typedef enum WICComponentType {
    WICDecoder = 0x1,
    WICEncoder = 0x2,
    WICPixelFormatConverter = 0x4,
    WICMetadataReader = 0x8,
    WICMetadataWriter = 0x10,
    WICPixelFormat = 0x20,
    WICCOMPONENTTYPE_FORCE_DWORD = 0x7fffffff
} WICComponentType;
typedef enum WICComponentSigning {
    WICComponentSigned = 0x1,
    WICComponentUnsigned = 0x2,
    WICComponentSafe = 0x4,
    WICComponentDisabled = 0x80000000
} WICComponentSigning;
typedef enum WICComponentEnumerateOptions {
    WICComponentEnumerateDefault = 0x0,
    WICComponentEnumerateRefresh = 0x1,
    WICComponentEnumerateBuiltInOnly = 0x20000000,
    WICComponentEnumerateUnsigned = 0x40000000,
    WICComponentEnumerateDisabled = 0x80000000
} WICComponentEnumerateOptions;
typedef enum WICJpegYCrCbSubsamplingOption {
    WICJpegYCrCbSubsamplingDefault = 0x0,
    WICJpegYCrCbSubsampling420 = 0x1,
    WICJpegYCrCbSubsampling422 = 0x2,
    WICJpegYCrCbSubsampling444 = 0x3,
    WICJpegYCrCbSubsampling440 = 0x4
} WICJpegYCrCbSubsamplingOption;
typedef enum WICPixelFormatNumericRepresentation {
    WICPixelFormatNumericRepresentationUnspecified = 0x0,
    WICPixelFormatNumericRepresentationIndexed = 0x1,
    WICPixelFormatNumericRepresentationUnsignedInteger = 0x2,
    WICPixelFormatNumericRepresentationSignedInteger = 0x3,
    WICPixelFormatNumericRepresentationFixed = 0x4,
    WICPixelFormatNumericRepresentationFloat = 0x5,
    WICPIXELFORMATNUMERICREPRESENTATION_FORCE_DWORD = 0x7fffffff
} WICPixelFormatNumericRepresentation;
typedef enum WICTiffCompressionOption {
    WICTiffCompressionDontCare = 0x0,
    WICTiffCompressionNone = 0x1,
    WICTiffCompressionCCITT3 = 0x2,
    WICTiffCompressionCCITT4 = 0x3,
    WICTiffCompressionLZW = 0x4,
    WICTiffCompressionRLE = 0x5,
    WICTiffCompressionZIP = 0x6,
    WICTiffCompressionLZWHDifferencing = 0x7,
    WICTIFFCOMPRESSIONOPTION_FORCE_DWORD = 0x7fffffff
} WICTiffCompressionOption;
typedef enum WICPngFilterOption {
    WICPngFilterUnspecified = 0,
    WICPngFilterNone = 1,
    WICPngFilterSub = 2,
    WICPngFilterUp = 3,
    WICPngFilterAverage = 4,
    WICPngFilterPaeth = 5,
    WICPngFilterAdaptive = 6,
    WICPNFFILTEROPTION_FORCE_DWORD = 0x7fffffff
} WICPngFilterOption;
typedef enum WICSectionAccessLevel {
    WICSectionAccessLevelRead = 0x1,
    WICSectionAccessLevelReadWrite = 0x3,
    WICSectionAccessLevel_FORCE_DWORD = 0x7fffffff
} WICSectionAccessLevel;
typedef enum WICDdsDimension {
    WICDdsTexture1D = 0x0,
    WICDdsTexture2D = 0x1,
    WICDdsTexture3D = 0x2,
    WICDdsTextureCube = 0x3,
    WICDDSTEXTURE_FORCE_DWORD = 0x7fffffff
} WICDdsDimension;
typedef enum WICDdsAlphaMode {
    WICDdsAlphaModeUnknown = 0x0,
    WICDdsAlphaModeStraight = 0x1,
    WICDdsAlphaModePremultiplied = 0x2,
    WICDdsAlphaModeOpaque = 0x3,
    WICDdsAlphaModeCustom = 0x4,
    WICDDSALPHAMODE_FORCE_DWORD = 0x7fffffff
} WICDdsAlphaMode;
typedef GUID WICPixelFormatGUID;
typedef REFGUID REFWICPixelFormatGUID;
DEFINE_GUID(GUID_WICPixelFormatDontCare, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x00);
#define GUID_WICPixelFormatUndefined GUID_WICPixelFormatDontCare
DEFINE_GUID(GUID_WICPixelFormat1bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x01);
DEFINE_GUID(GUID_WICPixelFormat2bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x02);
DEFINE_GUID(GUID_WICPixelFormat4bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x03);
DEFINE_GUID(GUID_WICPixelFormat8bppIndexed, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x04);
DEFINE_GUID(GUID_WICPixelFormatBlackWhite, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x05);
DEFINE_GUID(GUID_WICPixelFormat2bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x06);
DEFINE_GUID(GUID_WICPixelFormat4bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x07);
DEFINE_GUID(GUID_WICPixelFormat8bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x08);
DEFINE_GUID(GUID_WICPixelFormat16bppGray, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0b);
DEFINE_GUID(GUID_WICPixelFormat8bppAlpha, 0xe6cd0116,0xeeba,0x4161,0xaa,0x85,0x27,0xdd,0x9f,0xb3,0xa8,0x95);
DEFINE_GUID(GUID_WICPixelFormat16bppBGR555, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x09);
DEFINE_GUID(GUID_WICPixelFormat16bppBGR565, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0a);
DEFINE_GUID(GUID_WICPixelFormat16bppBGRA5551, 0x05ec7c2b,0xf1e6,0x4961,0xad,0x46,0xe1,0xcc,0x81,0x0a,0x87,0xd2);
DEFINE_GUID(GUID_WICPixelFormat24bppBGR, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0c);
DEFINE_GUID(GUID_WICPixelFormat24bppRGB, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0d);
DEFINE_GUID(GUID_WICPixelFormat32bppBGR, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0e);
DEFINE_GUID(GUID_WICPixelFormat32bppBGRA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x0f);
DEFINE_GUID(GUID_WICPixelFormat32bppPBGRA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x10);
DEFINE_GUID(GUID_WICPixelFormat32bppRGB, 0xd98c6b95,0x3efe,0x47d6,0xbb,0x25,0xeb,0x17,0x48,0xab,0x0c,0xf1);
DEFINE_GUID(GUID_WICPixelFormat32bppRGBA, 0xf5c7ad2d,0x6a8d,0x43dd,0xa7,0xa8,0xa2,0x99,0x35,0x26,0x1a,0xe9);
DEFINE_GUID(GUID_WICPixelFormat32bppPRGBA, 0x3cc4a650,0xa527,0x4d37,0xa9,0x16,0x31,0x42,0xc7,0xeb,0xed,0xba);
DEFINE_GUID(GUID_WICPixelFormat32bppGrayFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x11);
DEFINE_GUID(GUID_WICPixelFormat48bppRGB, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x15);
DEFINE_GUID(GUID_WICPixelFormat48bppBGR, 0xe605a384,0xb468,0x46ce,0xbb,0x2e,0x36,0xf1,0x80,0xe6,0x43,0x13);
DEFINE_GUID(GUID_WICPixelFormat64bppRGB, 0xa1182111,0x186d,0x4d42,0xbc,0x6a,0x9c,0x83,0x03,0xa8,0xdf,0xf9);
DEFINE_GUID(GUID_WICPixelFormat64bppRGBA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x16);
DEFINE_GUID(GUID_WICPixelFormat64bppBGRA, 0x1562ff7c,0xd352,0x46f9,0x97,0x9e,0x42,0x97,0x6b,0x79,0x22,0x46);
DEFINE_GUID(GUID_WICPixelFormat64bppPRGBA, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x17);
DEFINE_GUID(GUID_WICPixelFormat64bppPBGRA, 0x8c518e8e,0xa4ec,0x468b,0xae,0x70,0xc9,0xa3,0x5a,0x9c,0x55,0x30);
DEFINE_GUID(GUID_WICPixelFormat16bppGrayFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x13);
DEFINE_GUID(GUID_WICPixelFormat32bppBGR101010, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x14);
DEFINE_GUID(GUID_WICPixelFormat48bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x12);
DEFINE_GUID(GUID_WICPixelFormat48bppBGRFixedPoint, 0x49ca140e,0xcab6,0x493b,0x9d,0xdf,0x60,0x18,0x7c,0x37,0x53,0x2a);
DEFINE_GUID(GUID_WICPixelFormat96bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x18);
DEFINE_GUID(GUID_WICPixelFormat96bppRGBFloat, 0xe3fed78f,0xe8db,0x4acf,0x84,0xc1,0xe9,0x7f,0x61,0x36,0xb3,0x27);
DEFINE_GUID(GUID_WICPixelFormat128bppRGBAFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x19);
DEFINE_GUID(GUID_WICPixelFormat128bppPRGBAFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1a);
DEFINE_GUID(GUID_WICPixelFormat128bppRGBFloat, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1b);
DEFINE_GUID(GUID_WICPixelFormat32bppCMYK, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1c);
DEFINE_GUID(GUID_WICPixelFormat64bppRGBAFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1d);
DEFINE_GUID(GUID_WICPixelFormat64bppBGRAFixedPoint, 0x356de33c,0x54d2,0x4a23,0xbb,0x4,0x9b,0x7b,0xf9,0xb1,0xd4,0x2d);
DEFINE_GUID(GUID_WICPixelFormat64bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x40);
DEFINE_GUID(GUID_WICPixelFormat128bppRGBAFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1e);
DEFINE_GUID(GUID_WICPixelFormat128bppRGBFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x41);
DEFINE_GUID(GUID_WICPixelFormat64bppRGBAHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3a);
DEFINE_GUID(GUID_WICPixelFormat64bppPRGBAHalf, 0x58ad26c2,0xc623,0x4d9d,0xb3,0x20,0x38,0x7e,0x49,0xf8,0xc4,0x42);
DEFINE_GUID(GUID_WICPixelFormat64bppRGBHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x42);
DEFINE_GUID(GUID_WICPixelFormat48bppRGBHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3b);
DEFINE_GUID(GUID_WICPixelFormat32bppRGBE, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3d);
DEFINE_GUID(GUID_WICPixelFormat16bppGrayHalf, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3e);
DEFINE_GUID(GUID_WICPixelFormat32bppGrayFixedPoint, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x3f);
DEFINE_GUID(GUID_WICPixelFormat32bppRGBA1010102, 0x25238d72,0xfcf9,0x4522,0xb5,0x14,0x55,0x78,0xe5,0xad,0x55,0xe0);
DEFINE_GUID(GUID_WICPixelFormat32bppRGBA1010102XR, 0x00de6b9a,0xc101,0x434b,0xb5,0x02,0xd0,0x16,0x5e,0xe1,0x12,0x2c);
DEFINE_GUID(GUID_WICPixelFormat32bppR10G10B10A2, 0x604e1bb5,0x8a3c,0x4b65,0xb1,0x1c,0xbc,0x0b,0x8d,0xd7,0x5b,0x7f);
DEFINE_GUID(GUID_WICPixelFormat32bppR10G10B10A2HDR10, 0x9c215c5d,0x1acc,0x4f0e,0xa4,0xbc,0x70,0xfb,0x3a,0xe8,0xfd,0x28);
DEFINE_GUID(GUID_WICPixelFormat64bppCMYK, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x1f);
DEFINE_GUID(GUID_WICPixelFormat24bpp3Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x20);
DEFINE_GUID(GUID_WICPixelFormat32bpp4Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x21);
DEFINE_GUID(GUID_WICPixelFormat40bpp5Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x22);
DEFINE_GUID(GUID_WICPixelFormat48bpp6Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x23);
DEFINE_GUID(GUID_WICPixelFormat56bpp7Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x24);
DEFINE_GUID(GUID_WICPixelFormat64bpp8Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x25);
DEFINE_GUID(GUID_WICPixelFormat48bpp3Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x26);
DEFINE_GUID(GUID_WICPixelFormat64bpp4Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x27);
DEFINE_GUID(GUID_WICPixelFormat80bpp5Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x28);
DEFINE_GUID(GUID_WICPixelFormat96bpp6Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x29);
DEFINE_GUID(GUID_WICPixelFormat112bpp7Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2a);
DEFINE_GUID(GUID_WICPixelFormat128bpp8Channels, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2b);
DEFINE_GUID(GUID_WICPixelFormat40bppCMYKAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2c);
DEFINE_GUID(GUID_WICPixelFormat80bppCMYKAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2d);
DEFINE_GUID(GUID_WICPixelFormat32bpp3ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2e);
DEFINE_GUID(GUID_WICPixelFormat40bpp4ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x2f);
DEFINE_GUID(GUID_WICPixelFormat48bpp5ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x30);
DEFINE_GUID(GUID_WICPixelFormat56bpp6ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x31);
DEFINE_GUID(GUID_WICPixelFormat64bpp7ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x32);
DEFINE_GUID(GUID_WICPixelFormat72bpp8ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x33);
DEFINE_GUID(GUID_WICPixelFormat64bpp3ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x34);
DEFINE_GUID(GUID_WICPixelFormat80bpp4ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x35);
DEFINE_GUID(GUID_WICPixelFormat96bpp5ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x36);
DEFINE_GUID(GUID_WICPixelFormat112bpp6ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x37);
DEFINE_GUID(GUID_WICPixelFormat128bpp7ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x38);
DEFINE_GUID(GUID_WICPixelFormat144bpp8ChannelsAlpha, 0x6fddc324,0x4e03,0x4bfe,0xb1,0x85,0x3d,0x77,0x76,0x8d,0xc9,0x39);
DEFINE_GUID(GUID_WICPixelFormat8bppY, 0x91b4db54,0x2df9,0x42f0,0xb4,0x49,0x29,0x09,0xbb,0x3d,0xf8,0x8e);
DEFINE_GUID(GUID_WICPixelFormat8bppCb, 0x1339f224,0x6bfe,0x4c3e,0x93,0x02,0xe4,0xf3,0xa6,0xd0,0xca,0x2a);
DEFINE_GUID(GUID_WICPixelFormat8bppCr, 0xb8145053,0x2116,0x49f0,0x88,0x35,0xed,0x84,0x4b,0x20,0x5c,0x51);
DEFINE_GUID(GUID_WICPixelFormat16bppCbCr, 0xff95ba6e,0x11e0,0x4263,0xbb,0x45,0x01,0x72,0x1f,0x34,0x60,0xa4);
DEFINE_GUID(GUID_WICPixelFormat16bppYQuantizedDctCoefficients, 0xa355f433,0x48e8,0x4a42,0x84,0xd8,0xe2,0xaa,0x26,0xca,0x80,0xa4);
DEFINE_GUID(GUID_WICPixelFormat16bppCbQuantizedDctCoefficients, 0xd2c4ff61,0x56a5,0x49c2,0x8b,0x5c,0x4c,0x19,0x25,0x96,0x48,0x37);
DEFINE_GUID(GUID_WICPixelFormat16bppCrQuantizedDctCoefficients, 0x2fe354f0,0x1680,0x42d8,0x92,0x31,0xe7,0x3c,0x05,0x65,0xbf,0xc1);
#if 0
typedef DWORD *D2D1_PIXEL_FORMAT;
#endif
typedef struct WICRect {
    INT X;
    INT Y;
    INT Width;
    INT Height;
} WICRect;
typedef struct WICBitmapPattern {
    ULARGE_INTEGER Position;
    ULONG Length;
    BYTE *Pattern;
    BYTE *Mask;
    WINBOOL EndOfStream;
} WICBitmapPattern;
typedef struct WICImageParameters {
    D2D1_PIXEL_FORMAT PixelFormat;
    FLOAT DpiX;
    FLOAT DpiY;
    FLOAT Top;
    FLOAT Left;
    UINT32 PixelWidth;
    UINT32 PixelHeight;
} WICImageParameters;
typedef struct WICDdsParameters {
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT DxgiFormat;
    WICDdsDimension Dimension;
    WICDdsAlphaMode AlphaMode;
} WICDdsParameters;
typedef struct WICDdsFormatInfo {
    DXGI_FORMAT DxgiFormat;
    UINT BytesPerBlock;
    UINT BlockWidth;
    UINT BlockHeight;
} WICDdsFormatInfo;
typedef UINT32 WICColor;
#ifndef __ID2D1Device_FWD_DEFINED__
#define __ID2D1Device_FWD_DEFINED__
typedef interface ID2D1Device ID2D1Device;
#ifdef __cplusplus
interface ID2D1Device;
#endif /* __cplusplus */
#endif

#ifndef __ID2D1Image_FWD_DEFINED__
#define __ID2D1Image_FWD_DEFINED__
typedef interface ID2D1Image ID2D1Image;
#ifdef __cplusplus
interface ID2D1Image;
#endif /* __cplusplus */
#endif

#ifndef __IWICPalette_FWD_DEFINED__
#define __IWICPalette_FWD_DEFINED__
typedef interface IWICPalette IWICPalette;
#ifdef __cplusplus
interface IWICPalette;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IWICColorContext interface
 */
#ifndef __IWICColorContext_INTERFACE_DEFINED__
#define __IWICColorContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICColorContext, 0x3c613a02, 0x34b2, 0x44ea, 0x9a,0x7c, 0x45,0xae,0xa9,0xc6,0xfd,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3c613a02-34b2-44ea-9a7c-45aea9c6fd6d")
IWICColorContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitializeFromFilename(
        LPCWSTR wzFilename) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeFromMemory(
        const BYTE *pbBuffer,
        UINT cbBufferSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeFromExifColorSpace(
        UINT value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        WICColorContextType *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProfileBytes(
        UINT cbBuffer,
        BYTE *pbBuffer,
        UINT *pcbActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExifColorSpace(
        UINT *pValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICColorContext, 0x3c613a02, 0x34b2, 0x44ea, 0x9a,0x7c, 0x45,0xae,0xa9,0xc6,0xfd,0x6d)
#endif
#else
typedef struct IWICColorContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICColorContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICColorContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICColorContext *This);

    /*** IWICColorContext methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializeFromFilename)(
        IWICColorContext *This,
        LPCWSTR wzFilename);

    HRESULT (STDMETHODCALLTYPE *InitializeFromMemory)(
        IWICColorContext *This,
        const BYTE *pbBuffer,
        UINT cbBufferSize);

    HRESULT (STDMETHODCALLTYPE *InitializeFromExifColorSpace)(
        IWICColorContext *This,
        UINT value);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IWICColorContext *This,
        WICColorContextType *pType);

    HRESULT (STDMETHODCALLTYPE *GetProfileBytes)(
        IWICColorContext *This,
        UINT cbBuffer,
        BYTE *pbBuffer,
        UINT *pcbActual);

    HRESULT (STDMETHODCALLTYPE *GetExifColorSpace)(
        IWICColorContext *This,
        UINT *pValue);

    END_INTERFACE
} IWICColorContextVtbl;

interface IWICColorContext {
    CONST_VTBL IWICColorContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICColorContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICColorContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICColorContext_Release(This) (This)->lpVtbl->Release(This)
/*** IWICColorContext methods ***/
#define IWICColorContext_InitializeFromFilename(This,wzFilename) (This)->lpVtbl->InitializeFromFilename(This,wzFilename)
#define IWICColorContext_InitializeFromMemory(This,pbBuffer,cbBufferSize) (This)->lpVtbl->InitializeFromMemory(This,pbBuffer,cbBufferSize)
#define IWICColorContext_InitializeFromExifColorSpace(This,value) (This)->lpVtbl->InitializeFromExifColorSpace(This,value)
#define IWICColorContext_GetType(This,pType) (This)->lpVtbl->GetType(This,pType)
#define IWICColorContext_GetProfileBytes(This,cbBuffer,pbBuffer,pcbActual) (This)->lpVtbl->GetProfileBytes(This,cbBuffer,pbBuffer,pcbActual)
#define IWICColorContext_GetExifColorSpace(This,pValue) (This)->lpVtbl->GetExifColorSpace(This,pValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICColorContext_QueryInterface(IWICColorContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICColorContext_AddRef(IWICColorContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICColorContext_Release(IWICColorContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICColorContext methods ***/
static inline HRESULT IWICColorContext_InitializeFromFilename(IWICColorContext* This,LPCWSTR wzFilename) {
    return This->lpVtbl->InitializeFromFilename(This,wzFilename);
}
static inline HRESULT IWICColorContext_InitializeFromMemory(IWICColorContext* This,const BYTE *pbBuffer,UINT cbBufferSize) {
    return This->lpVtbl->InitializeFromMemory(This,pbBuffer,cbBufferSize);
}
static inline HRESULT IWICColorContext_InitializeFromExifColorSpace(IWICColorContext* This,UINT value) {
    return This->lpVtbl->InitializeFromExifColorSpace(This,value);
}
static inline HRESULT IWICColorContext_GetType(IWICColorContext* This,WICColorContextType *pType) {
    return This->lpVtbl->GetType(This,pType);
}
static inline HRESULT IWICColorContext_GetProfileBytes(IWICColorContext* This,UINT cbBuffer,BYTE *pbBuffer,UINT *pcbActual) {
    return This->lpVtbl->GetProfileBytes(This,cbBuffer,pbBuffer,pcbActual);
}
static inline HRESULT IWICColorContext_GetExifColorSpace(IWICColorContext* This,UINT *pValue) {
    return This->lpVtbl->GetExifColorSpace(This,pValue);
}
#endif
#endif

#endif


#endif  /* __IWICColorContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapSource interface
 */
#ifndef __IWICBitmapSource_INTERFACE_DEFINED__
#define __IWICBitmapSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapSource, 0x00000120, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000120-a8f2-4877-ba0a-fd2b6645fb94")
IWICBitmapSource : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSize(
        UINT *puiWidth,
        UINT *puiHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPixelFormat(
        WICPixelFormatGUID *pPixelFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResolution(
        double *pDpiX,
        double *pDpiY) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyPalette(
        IWICPalette *pIPalette) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyPixels(
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapSource, 0x00000120, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICBitmapSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapSource *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICBitmapSource *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICBitmapSource *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICBitmapSource *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICBitmapSource *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICBitmapSource *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    END_INTERFACE
} IWICBitmapSourceVtbl;

interface IWICBitmapSource {
    CONST_VTBL IWICBitmapSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapSource_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICBitmapSource_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICBitmapSource_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICBitmapSource_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICBitmapSource_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICBitmapSource_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapSource_QueryInterface(IWICBitmapSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapSource_AddRef(IWICBitmapSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapSource_Release(IWICBitmapSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICBitmapSource_GetSize(IWICBitmapSource* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICBitmapSource_GetPixelFormat(IWICBitmapSource* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICBitmapSource_GetResolution(IWICBitmapSource* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICBitmapSource_CopyPalette(IWICBitmapSource* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapSource_CopyPixels(IWICBitmapSource* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapSource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapLock interface
 */
#ifndef __IWICBitmapLock_INTERFACE_DEFINED__
#define __IWICBitmapLock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapLock, 0x00000123, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000123-a8f2-4877-ba0a-fd2b6645fb94")
IWICBitmapLock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSize(
        UINT *pWidth,
        UINT *pHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStride(
        UINT *pcbStride) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDataPointer(
        UINT *pcbBufferSize,
        BYTE **ppbData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPixelFormat(
        WICPixelFormatGUID *pPixelFormat) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapLock, 0x00000123, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICBitmapLockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapLock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapLock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapLock *This);

    /*** IWICBitmapLock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICBitmapLock *This,
        UINT *pWidth,
        UINT *pHeight);

    HRESULT (STDMETHODCALLTYPE *GetStride)(
        IWICBitmapLock *This,
        UINT *pcbStride);

    HRESULT (STDMETHODCALLTYPE *GetDataPointer)(
        IWICBitmapLock *This,
        UINT *pcbBufferSize,
        BYTE **ppbData);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICBitmapLock *This,
        WICPixelFormatGUID *pPixelFormat);

    END_INTERFACE
} IWICBitmapLockVtbl;

interface IWICBitmapLock {
    CONST_VTBL IWICBitmapLockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapLock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapLock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapLock_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapLock methods ***/
#define IWICBitmapLock_GetSize(This,pWidth,pHeight) (This)->lpVtbl->GetSize(This,pWidth,pHeight)
#define IWICBitmapLock_GetStride(This,pcbStride) (This)->lpVtbl->GetStride(This,pcbStride)
#define IWICBitmapLock_GetDataPointer(This,pcbBufferSize,ppbData) (This)->lpVtbl->GetDataPointer(This,pcbBufferSize,ppbData)
#define IWICBitmapLock_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapLock_QueryInterface(IWICBitmapLock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapLock_AddRef(IWICBitmapLock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapLock_Release(IWICBitmapLock* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapLock methods ***/
static inline HRESULT IWICBitmapLock_GetSize(IWICBitmapLock* This,UINT *pWidth,UINT *pHeight) {
    return This->lpVtbl->GetSize(This,pWidth,pHeight);
}
static inline HRESULT IWICBitmapLock_GetStride(IWICBitmapLock* This,UINT *pcbStride) {
    return This->lpVtbl->GetStride(This,pcbStride);
}
static inline HRESULT IWICBitmapLock_GetDataPointer(IWICBitmapLock* This,UINT *pcbBufferSize,BYTE **ppbData) {
    return This->lpVtbl->GetDataPointer(This,pcbBufferSize,ppbData);
}
static inline HRESULT IWICBitmapLock_GetPixelFormat(IWICBitmapLock* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapLock_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapFlipRotator interface
 */
#ifndef __IWICBitmapFlipRotator_INTERFACE_DEFINED__
#define __IWICBitmapFlipRotator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapFlipRotator, 0x5009834f, 0x2d6a, 0x41ce, 0x9e,0x1b, 0x17,0xc5,0xaf,0xf7,0xa7,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5009834f-2d6a-41ce-9e1b-17c5aff7a782")
IWICBitmapFlipRotator : public IWICBitmapSource
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IWICBitmapSource *pISource,
        WICBitmapTransformOptions options) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapFlipRotator, 0x5009834f, 0x2d6a, 0x41ce, 0x9e,0x1b, 0x17,0xc5,0xaf,0xf7,0xa7,0x82)
#endif
#else
typedef struct IWICBitmapFlipRotatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapFlipRotator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapFlipRotator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapFlipRotator *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICBitmapFlipRotator *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICBitmapFlipRotator *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICBitmapFlipRotator *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICBitmapFlipRotator *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICBitmapFlipRotator *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    /*** IWICBitmapFlipRotator methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICBitmapFlipRotator *This,
        IWICBitmapSource *pISource,
        WICBitmapTransformOptions options);

    END_INTERFACE
} IWICBitmapFlipRotatorVtbl;

interface IWICBitmapFlipRotator {
    CONST_VTBL IWICBitmapFlipRotatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapFlipRotator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapFlipRotator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapFlipRotator_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICBitmapFlipRotator_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICBitmapFlipRotator_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICBitmapFlipRotator_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICBitmapFlipRotator_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICBitmapFlipRotator_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
/*** IWICBitmapFlipRotator methods ***/
#define IWICBitmapFlipRotator_Initialize(This,pISource,options) (This)->lpVtbl->Initialize(This,pISource,options)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapFlipRotator_QueryInterface(IWICBitmapFlipRotator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapFlipRotator_AddRef(IWICBitmapFlipRotator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapFlipRotator_Release(IWICBitmapFlipRotator* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICBitmapFlipRotator_GetSize(IWICBitmapFlipRotator* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICBitmapFlipRotator_GetPixelFormat(IWICBitmapFlipRotator* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICBitmapFlipRotator_GetResolution(IWICBitmapFlipRotator* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICBitmapFlipRotator_CopyPalette(IWICBitmapFlipRotator* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapFlipRotator_CopyPixels(IWICBitmapFlipRotator* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
/*** IWICBitmapFlipRotator methods ***/
static inline HRESULT IWICBitmapFlipRotator_Initialize(IWICBitmapFlipRotator* This,IWICBitmapSource *pISource,WICBitmapTransformOptions options) {
    return This->lpVtbl->Initialize(This,pISource,options);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapFlipRotator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmap interface
 */
#ifndef __IWICBitmap_INTERFACE_DEFINED__
#define __IWICBitmap_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmap, 0x00000121, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000121-a8f2-4877-ba0a-fd2b6645fb94")
IWICBitmap : public IWICBitmapSource
{
    virtual HRESULT STDMETHODCALLTYPE Lock(
        const WICRect *prcLock,
        DWORD flags,
        IWICBitmapLock **ppILock) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPalette(
        IWICPalette *pIPalette) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetResolution(
        double dpiX,
        double dpiY) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmap, 0x00000121, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICBitmapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmap *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICBitmap *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICBitmap *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICBitmap *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICBitmap *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICBitmap *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    /*** IWICBitmap methods ***/
    HRESULT (STDMETHODCALLTYPE *Lock)(
        IWICBitmap *This,
        const WICRect *prcLock,
        DWORD flags,
        IWICBitmapLock **ppILock);

    HRESULT (STDMETHODCALLTYPE *SetPalette)(
        IWICBitmap *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *SetResolution)(
        IWICBitmap *This,
        double dpiX,
        double dpiY);

    END_INTERFACE
} IWICBitmapVtbl;

interface IWICBitmap {
    CONST_VTBL IWICBitmapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmap_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICBitmap_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICBitmap_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICBitmap_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICBitmap_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICBitmap_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
/*** IWICBitmap methods ***/
#define IWICBitmap_Lock(This,prcLock,flags,ppILock) (This)->lpVtbl->Lock(This,prcLock,flags,ppILock)
#define IWICBitmap_SetPalette(This,pIPalette) (This)->lpVtbl->SetPalette(This,pIPalette)
#define IWICBitmap_SetResolution(This,dpiX,dpiY) (This)->lpVtbl->SetResolution(This,dpiX,dpiY)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmap_QueryInterface(IWICBitmap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmap_AddRef(IWICBitmap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmap_Release(IWICBitmap* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICBitmap_GetSize(IWICBitmap* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICBitmap_GetPixelFormat(IWICBitmap* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICBitmap_GetResolution(IWICBitmap* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICBitmap_CopyPalette(IWICBitmap* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICBitmap_CopyPixels(IWICBitmap* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
/*** IWICBitmap methods ***/
static inline HRESULT IWICBitmap_Lock(IWICBitmap* This,const WICRect *prcLock,DWORD flags,IWICBitmapLock **ppILock) {
    return This->lpVtbl->Lock(This,prcLock,flags,ppILock);
}
static inline HRESULT IWICBitmap_SetPalette(IWICBitmap* This,IWICPalette *pIPalette) {
    return This->lpVtbl->SetPalette(This,pIPalette);
}
static inline HRESULT IWICBitmap_SetResolution(IWICBitmap* This,double dpiX,double dpiY) {
    return This->lpVtbl->SetResolution(This,dpiX,dpiY);
}
#endif
#endif

#endif


#endif  /* __IWICBitmap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICPalette interface
 */
#ifndef __IWICPalette_INTERFACE_DEFINED__
#define __IWICPalette_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICPalette, 0x00000040, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000040-a8f2-4877-ba0a-fd2b6645fb94")
IWICPalette : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitializePredefined(
        WICBitmapPaletteType ePaletteType,
        WINBOOL fAddTransparentColor) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeCustom(
        WICColor *pColors,
        UINT colorCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeFromBitmap(
        IWICBitmapSource *pISurface,
        UINT colorCount,
        WINBOOL fAddTransparentColor) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeFromPalette(
        IWICPalette *pIPalette) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        WICBitmapPaletteType *pePaletteType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorCount(
        UINT *pcCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColors(
        UINT colorCount,
        WICColor *pColors,
        UINT *pcActualColors) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsBlackWhite(
        WINBOOL *pfIsBlackWhite) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsGrayscale(
        WINBOOL *pfIsGrayscale) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasAlpha(
        WINBOOL *pfHasAlpha) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICPalette, 0x00000040, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICPaletteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICPalette *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICPalette *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICPalette *This);

    /*** IWICPalette methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializePredefined)(
        IWICPalette *This,
        WICBitmapPaletteType ePaletteType,
        WINBOOL fAddTransparentColor);

    HRESULT (STDMETHODCALLTYPE *InitializeCustom)(
        IWICPalette *This,
        WICColor *pColors,
        UINT colorCount);

    HRESULT (STDMETHODCALLTYPE *InitializeFromBitmap)(
        IWICPalette *This,
        IWICBitmapSource *pISurface,
        UINT colorCount,
        WINBOOL fAddTransparentColor);

    HRESULT (STDMETHODCALLTYPE *InitializeFromPalette)(
        IWICPalette *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IWICPalette *This,
        WICBitmapPaletteType *pePaletteType);

    HRESULT (STDMETHODCALLTYPE *GetColorCount)(
        IWICPalette *This,
        UINT *pcCount);

    HRESULT (STDMETHODCALLTYPE *GetColors)(
        IWICPalette *This,
        UINT colorCount,
        WICColor *pColors,
        UINT *pcActualColors);

    HRESULT (STDMETHODCALLTYPE *IsBlackWhite)(
        IWICPalette *This,
        WINBOOL *pfIsBlackWhite);

    HRESULT (STDMETHODCALLTYPE *IsGrayscale)(
        IWICPalette *This,
        WINBOOL *pfIsGrayscale);

    HRESULT (STDMETHODCALLTYPE *HasAlpha)(
        IWICPalette *This,
        WINBOOL *pfHasAlpha);

    END_INTERFACE
} IWICPaletteVtbl;

interface IWICPalette {
    CONST_VTBL IWICPaletteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICPalette_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICPalette_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICPalette_Release(This) (This)->lpVtbl->Release(This)
/*** IWICPalette methods ***/
#define IWICPalette_InitializePredefined(This,ePaletteType,fAddTransparentColor) (This)->lpVtbl->InitializePredefined(This,ePaletteType,fAddTransparentColor)
#define IWICPalette_InitializeCustom(This,pColors,colorCount) (This)->lpVtbl->InitializeCustom(This,pColors,colorCount)
#define IWICPalette_InitializeFromBitmap(This,pISurface,colorCount,fAddTransparentColor) (This)->lpVtbl->InitializeFromBitmap(This,pISurface,colorCount,fAddTransparentColor)
#define IWICPalette_InitializeFromPalette(This,pIPalette) (This)->lpVtbl->InitializeFromPalette(This,pIPalette)
#define IWICPalette_GetType(This,pePaletteType) (This)->lpVtbl->GetType(This,pePaletteType)
#define IWICPalette_GetColorCount(This,pcCount) (This)->lpVtbl->GetColorCount(This,pcCount)
#define IWICPalette_GetColors(This,colorCount,pColors,pcActualColors) (This)->lpVtbl->GetColors(This,colorCount,pColors,pcActualColors)
#define IWICPalette_IsBlackWhite(This,pfIsBlackWhite) (This)->lpVtbl->IsBlackWhite(This,pfIsBlackWhite)
#define IWICPalette_IsGrayscale(This,pfIsGrayscale) (This)->lpVtbl->IsGrayscale(This,pfIsGrayscale)
#define IWICPalette_HasAlpha(This,pfHasAlpha) (This)->lpVtbl->HasAlpha(This,pfHasAlpha)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICPalette_QueryInterface(IWICPalette* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICPalette_AddRef(IWICPalette* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICPalette_Release(IWICPalette* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICPalette methods ***/
static inline HRESULT IWICPalette_InitializePredefined(IWICPalette* This,WICBitmapPaletteType ePaletteType,WINBOOL fAddTransparentColor) {
    return This->lpVtbl->InitializePredefined(This,ePaletteType,fAddTransparentColor);
}
static inline HRESULT IWICPalette_InitializeCustom(IWICPalette* This,WICColor *pColors,UINT colorCount) {
    return This->lpVtbl->InitializeCustom(This,pColors,colorCount);
}
static inline HRESULT IWICPalette_InitializeFromBitmap(IWICPalette* This,IWICBitmapSource *pISurface,UINT colorCount,WINBOOL fAddTransparentColor) {
    return This->lpVtbl->InitializeFromBitmap(This,pISurface,colorCount,fAddTransparentColor);
}
static inline HRESULT IWICPalette_InitializeFromPalette(IWICPalette* This,IWICPalette *pIPalette) {
    return This->lpVtbl->InitializeFromPalette(This,pIPalette);
}
static inline HRESULT IWICPalette_GetType(IWICPalette* This,WICBitmapPaletteType *pePaletteType) {
    return This->lpVtbl->GetType(This,pePaletteType);
}
static inline HRESULT IWICPalette_GetColorCount(IWICPalette* This,UINT *pcCount) {
    return This->lpVtbl->GetColorCount(This,pcCount);
}
static inline HRESULT IWICPalette_GetColors(IWICPalette* This,UINT colorCount,WICColor *pColors,UINT *pcActualColors) {
    return This->lpVtbl->GetColors(This,colorCount,pColors,pcActualColors);
}
static inline HRESULT IWICPalette_IsBlackWhite(IWICPalette* This,WINBOOL *pfIsBlackWhite) {
    return This->lpVtbl->IsBlackWhite(This,pfIsBlackWhite);
}
static inline HRESULT IWICPalette_IsGrayscale(IWICPalette* This,WINBOOL *pfIsGrayscale) {
    return This->lpVtbl->IsGrayscale(This,pfIsGrayscale);
}
static inline HRESULT IWICPalette_HasAlpha(IWICPalette* This,WINBOOL *pfHasAlpha) {
    return This->lpVtbl->HasAlpha(This,pfHasAlpha);
}
#endif
#endif

#endif


#endif  /* __IWICPalette_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICComponentInfo interface
 */
#ifndef __IWICComponentInfo_INTERFACE_DEFINED__
#define __IWICComponentInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICComponentInfo, 0x23bc3f0a, 0x698b, 0x4357, 0x88,0x6b, 0xf2,0x4d,0x50,0x67,0x13,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("23bc3f0a-698b-4357-886b-f24d50671334")
IWICComponentInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetComponentType(
        WICComponentType *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCLSID(
        CLSID *pclsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSigningStatus(
        DWORD *pStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAuthor(
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVendorGUID(
        GUID *pguidVendor) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVersion(
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSpecVersion(
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFriendlyName(
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICComponentInfo, 0x23bc3f0a, 0x698b, 0x4357, 0x88,0x6b, 0xf2,0x4d,0x50,0x67,0x13,0x34)
#endif
#else
typedef struct IWICComponentInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICComponentInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICComponentInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICComponentInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICComponentInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICComponentInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICComponentInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICComponentInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICComponentInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICComponentInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICComponentInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICComponentInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    END_INTERFACE
} IWICComponentInfoVtbl;

interface IWICComponentInfo {
    CONST_VTBL IWICComponentInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICComponentInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICComponentInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICComponentInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICComponentInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICComponentInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICComponentInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICComponentInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICComponentInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICComponentInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICComponentInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICComponentInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICComponentInfo_QueryInterface(IWICComponentInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICComponentInfo_AddRef(IWICComponentInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICComponentInfo_Release(IWICComponentInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICComponentInfo_GetComponentType(IWICComponentInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICComponentInfo_GetCLSID(IWICComponentInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICComponentInfo_GetSigningStatus(IWICComponentInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICComponentInfo_GetAuthor(IWICComponentInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICComponentInfo_GetVendorGUID(IWICComponentInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICComponentInfo_GetVersion(IWICComponentInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICComponentInfo_GetSpecVersion(IWICComponentInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICComponentInfo_GetFriendlyName(IWICComponentInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
#endif
#endif

#endif


#endif  /* __IWICComponentInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataQueryReader interface
 */
#ifndef __IWICMetadataQueryReader_INTERFACE_DEFINED__
#define __IWICMetadataQueryReader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataQueryReader, 0x30989668, 0xe1c9, 0x4597, 0xb3,0x95, 0x45,0x8e,0xed,0xb8,0x08,0xdf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("30989668-e1c9-4597-b395-458eedb808df")
IWICMetadataQueryReader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetContainerFormat(
        GUID *pguidContainerFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocation(
        UINT cchMaxLength,
        WCHAR *wzNamespace,
        UINT *pcchActualLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadataByName(
        LPCWSTR wzName,
        PROPVARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IEnumString **ppIEnumString) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataQueryReader, 0x30989668, 0xe1c9, 0x4597, 0xb3,0x95, 0x45,0x8e,0xed,0xb8,0x08,0xdf)
#endif
#else
typedef struct IWICMetadataQueryReaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataQueryReader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataQueryReader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataQueryReader *This);

    /*** IWICMetadataQueryReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICMetadataQueryReader *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetLocation)(
        IWICMetadataQueryReader *This,
        UINT cchMaxLength,
        WCHAR *wzNamespace,
        UINT *pcchActualLength);

    HRESULT (STDMETHODCALLTYPE *GetMetadataByName)(
        IWICMetadataQueryReader *This,
        LPCWSTR wzName,
        PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IWICMetadataQueryReader *This,
        IEnumString **ppIEnumString);

    END_INTERFACE
} IWICMetadataQueryReaderVtbl;

interface IWICMetadataQueryReader {
    CONST_VTBL IWICMetadataQueryReaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataQueryReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataQueryReader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataQueryReader_Release(This) (This)->lpVtbl->Release(This)
/*** IWICMetadataQueryReader methods ***/
#define IWICMetadataQueryReader_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICMetadataQueryReader_GetLocation(This,cchMaxLength,wzNamespace,pcchActualLength) (This)->lpVtbl->GetLocation(This,cchMaxLength,wzNamespace,pcchActualLength)
#define IWICMetadataQueryReader_GetMetadataByName(This,wzName,pvarValue) (This)->lpVtbl->GetMetadataByName(This,wzName,pvarValue)
#define IWICMetadataQueryReader_GetEnumerator(This,ppIEnumString) (This)->lpVtbl->GetEnumerator(This,ppIEnumString)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataQueryReader_QueryInterface(IWICMetadataQueryReader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataQueryReader_AddRef(IWICMetadataQueryReader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataQueryReader_Release(IWICMetadataQueryReader* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICMetadataQueryReader methods ***/
static inline HRESULT IWICMetadataQueryReader_GetContainerFormat(IWICMetadataQueryReader* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICMetadataQueryReader_GetLocation(IWICMetadataQueryReader* This,UINT cchMaxLength,WCHAR *wzNamespace,UINT *pcchActualLength) {
    return This->lpVtbl->GetLocation(This,cchMaxLength,wzNamespace,pcchActualLength);
}
static inline HRESULT IWICMetadataQueryReader_GetMetadataByName(IWICMetadataQueryReader* This,LPCWSTR wzName,PROPVARIANT *pvarValue) {
    return This->lpVtbl->GetMetadataByName(This,wzName,pvarValue);
}
static inline HRESULT IWICMetadataQueryReader_GetEnumerator(IWICMetadataQueryReader* This,IEnumString **ppIEnumString) {
    return This->lpVtbl->GetEnumerator(This,ppIEnumString);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataQueryReader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataQueryWriter interface
 */
#ifndef __IWICMetadataQueryWriter_INTERFACE_DEFINED__
#define __IWICMetadataQueryWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataQueryWriter, 0xa721791a, 0x0def, 0x4d06, 0xbd,0x91, 0x21,0x18,0xbf,0x1d,0xb1,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a721791a-0def-4d06-bd91-2118bf1db10b")
IWICMetadataQueryWriter : public IWICMetadataQueryReader
{
    virtual HRESULT STDMETHODCALLTYPE SetMetadataByName(
        LPCWSTR wzName,
        const PROPVARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveMetadataByName(
        LPCWSTR wzName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataQueryWriter, 0xa721791a, 0x0def, 0x4d06, 0xbd,0x91, 0x21,0x18,0xbf,0x1d,0xb1,0x0b)
#endif
#else
typedef struct IWICMetadataQueryWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataQueryWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataQueryWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataQueryWriter *This);

    /*** IWICMetadataQueryReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICMetadataQueryWriter *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetLocation)(
        IWICMetadataQueryWriter *This,
        UINT cchMaxLength,
        WCHAR *wzNamespace,
        UINT *pcchActualLength);

    HRESULT (STDMETHODCALLTYPE *GetMetadataByName)(
        IWICMetadataQueryWriter *This,
        LPCWSTR wzName,
        PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IWICMetadataQueryWriter *This,
        IEnumString **ppIEnumString);

    /*** IWICMetadataQueryWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMetadataByName)(
        IWICMetadataQueryWriter *This,
        LPCWSTR wzName,
        const PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *RemoveMetadataByName)(
        IWICMetadataQueryWriter *This,
        LPCWSTR wzName);

    END_INTERFACE
} IWICMetadataQueryWriterVtbl;

interface IWICMetadataQueryWriter {
    CONST_VTBL IWICMetadataQueryWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataQueryWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataQueryWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataQueryWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IWICMetadataQueryReader methods ***/
#define IWICMetadataQueryWriter_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICMetadataQueryWriter_GetLocation(This,cchMaxLength,wzNamespace,pcchActualLength) (This)->lpVtbl->GetLocation(This,cchMaxLength,wzNamespace,pcchActualLength)
#define IWICMetadataQueryWriter_GetMetadataByName(This,wzName,pvarValue) (This)->lpVtbl->GetMetadataByName(This,wzName,pvarValue)
#define IWICMetadataQueryWriter_GetEnumerator(This,ppIEnumString) (This)->lpVtbl->GetEnumerator(This,ppIEnumString)
/*** IWICMetadataQueryWriter methods ***/
#define IWICMetadataQueryWriter_SetMetadataByName(This,wzName,pvarValue) (This)->lpVtbl->SetMetadataByName(This,wzName,pvarValue)
#define IWICMetadataQueryWriter_RemoveMetadataByName(This,wzName) (This)->lpVtbl->RemoveMetadataByName(This,wzName)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataQueryWriter_QueryInterface(IWICMetadataQueryWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataQueryWriter_AddRef(IWICMetadataQueryWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataQueryWriter_Release(IWICMetadataQueryWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICMetadataQueryReader methods ***/
static inline HRESULT IWICMetadataQueryWriter_GetContainerFormat(IWICMetadataQueryWriter* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICMetadataQueryWriter_GetLocation(IWICMetadataQueryWriter* This,UINT cchMaxLength,WCHAR *wzNamespace,UINT *pcchActualLength) {
    return This->lpVtbl->GetLocation(This,cchMaxLength,wzNamespace,pcchActualLength);
}
static inline HRESULT IWICMetadataQueryWriter_GetMetadataByName(IWICMetadataQueryWriter* This,LPCWSTR wzName,PROPVARIANT *pvarValue) {
    return This->lpVtbl->GetMetadataByName(This,wzName,pvarValue);
}
static inline HRESULT IWICMetadataQueryWriter_GetEnumerator(IWICMetadataQueryWriter* This,IEnumString **ppIEnumString) {
    return This->lpVtbl->GetEnumerator(This,ppIEnumString);
}
/*** IWICMetadataQueryWriter methods ***/
static inline HRESULT IWICMetadataQueryWriter_SetMetadataByName(IWICMetadataQueryWriter* This,LPCWSTR wzName,const PROPVARIANT *pvarValue) {
    return This->lpVtbl->SetMetadataByName(This,wzName,pvarValue);
}
static inline HRESULT IWICMetadataQueryWriter_RemoveMetadataByName(IWICMetadataQueryWriter* This,LPCWSTR wzName) {
    return This->lpVtbl->RemoveMetadataByName(This,wzName);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataQueryWriter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapFrameDecode interface
 */
#ifndef __IWICBitmapFrameDecode_INTERFACE_DEFINED__
#define __IWICBitmapFrameDecode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapFrameDecode, 0x3b16811b, 0x6a43, 0x4ec9, 0xa8,0x13, 0x3d,0x93,0x0c,0x13,0xb9,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3b16811b-6a43-4ec9-a813-3d930c13b940")
IWICBitmapFrameDecode : public IWICBitmapSource
{
    virtual HRESULT STDMETHODCALLTYPE GetMetadataQueryReader(
        IWICMetadataQueryReader **ppIMetadataQueryReader) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorContexts(
        UINT cCount,
        IWICColorContext **ppIColorContexts,
        UINT *pcActualCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetThumbnail(
        IWICBitmapSource **ppIThumbnail) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapFrameDecode, 0x3b16811b, 0x6a43, 0x4ec9, 0xa8,0x13, 0x3d,0x93,0x0c,0x13,0xb9,0x40)
#endif
#else
typedef struct IWICBitmapFrameDecodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapFrameDecode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapFrameDecode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapFrameDecode *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICBitmapFrameDecode *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICBitmapFrameDecode *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICBitmapFrameDecode *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICBitmapFrameDecode *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICBitmapFrameDecode *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    /*** IWICBitmapFrameDecode methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadataQueryReader)(
        IWICBitmapFrameDecode *This,
        IWICMetadataQueryReader **ppIMetadataQueryReader);

    HRESULT (STDMETHODCALLTYPE *GetColorContexts)(
        IWICBitmapFrameDecode *This,
        UINT cCount,
        IWICColorContext **ppIColorContexts,
        UINT *pcActualCount);

    HRESULT (STDMETHODCALLTYPE *GetThumbnail)(
        IWICBitmapFrameDecode *This,
        IWICBitmapSource **ppIThumbnail);

    END_INTERFACE
} IWICBitmapFrameDecodeVtbl;

interface IWICBitmapFrameDecode {
    CONST_VTBL IWICBitmapFrameDecodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapFrameDecode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapFrameDecode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapFrameDecode_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICBitmapFrameDecode_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICBitmapFrameDecode_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICBitmapFrameDecode_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICBitmapFrameDecode_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICBitmapFrameDecode_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
/*** IWICBitmapFrameDecode methods ***/
#define IWICBitmapFrameDecode_GetMetadataQueryReader(This,ppIMetadataQueryReader) (This)->lpVtbl->GetMetadataQueryReader(This,ppIMetadataQueryReader)
#define IWICBitmapFrameDecode_GetColorContexts(This,cCount,ppIColorContexts,pcActualCount) (This)->lpVtbl->GetColorContexts(This,cCount,ppIColorContexts,pcActualCount)
#define IWICBitmapFrameDecode_GetThumbnail(This,ppIThumbnail) (This)->lpVtbl->GetThumbnail(This,ppIThumbnail)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapFrameDecode_QueryInterface(IWICBitmapFrameDecode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapFrameDecode_AddRef(IWICBitmapFrameDecode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapFrameDecode_Release(IWICBitmapFrameDecode* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICBitmapFrameDecode_GetSize(IWICBitmapFrameDecode* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICBitmapFrameDecode_GetPixelFormat(IWICBitmapFrameDecode* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICBitmapFrameDecode_GetResolution(IWICBitmapFrameDecode* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICBitmapFrameDecode_CopyPalette(IWICBitmapFrameDecode* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapFrameDecode_CopyPixels(IWICBitmapFrameDecode* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
/*** IWICBitmapFrameDecode methods ***/
static inline HRESULT IWICBitmapFrameDecode_GetMetadataQueryReader(IWICBitmapFrameDecode* This,IWICMetadataQueryReader **ppIMetadataQueryReader) {
    return This->lpVtbl->GetMetadataQueryReader(This,ppIMetadataQueryReader);
}
static inline HRESULT IWICBitmapFrameDecode_GetColorContexts(IWICBitmapFrameDecode* This,UINT cCount,IWICColorContext **ppIColorContexts,UINT *pcActualCount) {
    return This->lpVtbl->GetColorContexts(This,cCount,ppIColorContexts,pcActualCount);
}
static inline HRESULT IWICBitmapFrameDecode_GetThumbnail(IWICBitmapFrameDecode* This,IWICBitmapSource **ppIThumbnail) {
    return This->lpVtbl->GetThumbnail(This,ppIThumbnail);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapFrameDecode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICPixelFormatInfo interface
 */
#ifndef __IWICPixelFormatInfo_INTERFACE_DEFINED__
#define __IWICPixelFormatInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICPixelFormatInfo, 0xe8eda601, 0x3d48, 0x431a, 0xab,0x44, 0x69,0x05,0x9b,0xe8,0x8b,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e8eda601-3d48-431a-ab44-69059be88bbe")
IWICPixelFormatInfo : public IWICComponentInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetFormatGUID(
        GUID *pFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorContext(
        IWICColorContext **ppIColorContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBitsPerPixel(
        UINT *puiBitsPerPixel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelCount(
        UINT *puiChannelCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelMask(
        UINT uiChannelIndex,
        UINT cbMaskBuffer,
        BYTE *pbMaskBuffer,
        UINT *pcbActual) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICPixelFormatInfo, 0xe8eda601, 0x3d48, 0x431a, 0xab,0x44, 0x69,0x05,0x9b,0xe8,0x8b,0xbe)
#endif
#else
typedef struct IWICPixelFormatInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICPixelFormatInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICPixelFormatInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICPixelFormatInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICPixelFormatInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICPixelFormatInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICPixelFormatInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICPixelFormatInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICPixelFormatInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICPixelFormatInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICPixelFormatInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICPixelFormatInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICPixelFormatInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFormatGUID)(
        IWICPixelFormatInfo *This,
        GUID *pFormat);

    HRESULT (STDMETHODCALLTYPE *GetColorContext)(
        IWICPixelFormatInfo *This,
        IWICColorContext **ppIColorContext);

    HRESULT (STDMETHODCALLTYPE *GetBitsPerPixel)(
        IWICPixelFormatInfo *This,
        UINT *puiBitsPerPixel);

    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IWICPixelFormatInfo *This,
        UINT *puiChannelCount);

    HRESULT (STDMETHODCALLTYPE *GetChannelMask)(
        IWICPixelFormatInfo *This,
        UINT uiChannelIndex,
        UINT cbMaskBuffer,
        BYTE *pbMaskBuffer,
        UINT *pcbActual);

    END_INTERFACE
} IWICPixelFormatInfoVtbl;

interface IWICPixelFormatInfo {
    CONST_VTBL IWICPixelFormatInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICPixelFormatInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICPixelFormatInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICPixelFormatInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICPixelFormatInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICPixelFormatInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICPixelFormatInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICPixelFormatInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICPixelFormatInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICPixelFormatInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICPixelFormatInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICPixelFormatInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICPixelFormatInfo methods ***/
#define IWICPixelFormatInfo_GetFormatGUID(This,pFormat) (This)->lpVtbl->GetFormatGUID(This,pFormat)
#define IWICPixelFormatInfo_GetColorContext(This,ppIColorContext) (This)->lpVtbl->GetColorContext(This,ppIColorContext)
#define IWICPixelFormatInfo_GetBitsPerPixel(This,puiBitsPerPixel) (This)->lpVtbl->GetBitsPerPixel(This,puiBitsPerPixel)
#define IWICPixelFormatInfo_GetChannelCount(This,puiChannelCount) (This)->lpVtbl->GetChannelCount(This,puiChannelCount)
#define IWICPixelFormatInfo_GetChannelMask(This,uiChannelIndex,cbMaskBuffer,pbMaskBuffer,pcbActual) (This)->lpVtbl->GetChannelMask(This,uiChannelIndex,cbMaskBuffer,pbMaskBuffer,pcbActual)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICPixelFormatInfo_QueryInterface(IWICPixelFormatInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICPixelFormatInfo_AddRef(IWICPixelFormatInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICPixelFormatInfo_Release(IWICPixelFormatInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICPixelFormatInfo_GetComponentType(IWICPixelFormatInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICPixelFormatInfo_GetCLSID(IWICPixelFormatInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICPixelFormatInfo_GetSigningStatus(IWICPixelFormatInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICPixelFormatInfo_GetAuthor(IWICPixelFormatInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICPixelFormatInfo_GetVendorGUID(IWICPixelFormatInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICPixelFormatInfo_GetVersion(IWICPixelFormatInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICPixelFormatInfo_GetSpecVersion(IWICPixelFormatInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICPixelFormatInfo_GetFriendlyName(IWICPixelFormatInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICPixelFormatInfo methods ***/
static inline HRESULT IWICPixelFormatInfo_GetFormatGUID(IWICPixelFormatInfo* This,GUID *pFormat) {
    return This->lpVtbl->GetFormatGUID(This,pFormat);
}
static inline HRESULT IWICPixelFormatInfo_GetColorContext(IWICPixelFormatInfo* This,IWICColorContext **ppIColorContext) {
    return This->lpVtbl->GetColorContext(This,ppIColorContext);
}
static inline HRESULT IWICPixelFormatInfo_GetBitsPerPixel(IWICPixelFormatInfo* This,UINT *puiBitsPerPixel) {
    return This->lpVtbl->GetBitsPerPixel(This,puiBitsPerPixel);
}
static inline HRESULT IWICPixelFormatInfo_GetChannelCount(IWICPixelFormatInfo* This,UINT *puiChannelCount) {
    return This->lpVtbl->GetChannelCount(This,puiChannelCount);
}
static inline HRESULT IWICPixelFormatInfo_GetChannelMask(IWICPixelFormatInfo* This,UINT uiChannelIndex,UINT cbMaskBuffer,BYTE *pbMaskBuffer,UINT *pcbActual) {
    return This->lpVtbl->GetChannelMask(This,uiChannelIndex,cbMaskBuffer,pbMaskBuffer,pcbActual);
}
#endif
#endif

#endif


#endif  /* __IWICPixelFormatInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICPixelFormatInfo2 interface
 */
#ifndef __IWICPixelFormatInfo2_INTERFACE_DEFINED__
#define __IWICPixelFormatInfo2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICPixelFormatInfo2, 0xa9db33a2, 0xaf5f, 0x43c7, 0xb6,0x79, 0x74,0xf5,0x98,0x4b,0x5a,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a9db33a2-af5f-43c7-b679-74f5984b5aa4")
IWICPixelFormatInfo2 : public IWICPixelFormatInfo
{
    virtual HRESULT STDMETHODCALLTYPE SupportsTransparency(
        WINBOOL *pfSupportsTransparency) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumericRepresentation(
        WICPixelFormatNumericRepresentation *pNumericRepresentation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICPixelFormatInfo2, 0xa9db33a2, 0xaf5f, 0x43c7, 0xb6,0x79, 0x74,0xf5,0x98,0x4b,0x5a,0xa4)
#endif
#else
typedef struct IWICPixelFormatInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICPixelFormatInfo2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICPixelFormatInfo2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICPixelFormatInfo2 *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICPixelFormatInfo2 *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICPixelFormatInfo2 *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICPixelFormatInfo2 *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICPixelFormatInfo2 *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICPixelFormatInfo2 *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICPixelFormatInfo2 *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICPixelFormatInfo2 *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICPixelFormatInfo2 *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICPixelFormatInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFormatGUID)(
        IWICPixelFormatInfo2 *This,
        GUID *pFormat);

    HRESULT (STDMETHODCALLTYPE *GetColorContext)(
        IWICPixelFormatInfo2 *This,
        IWICColorContext **ppIColorContext);

    HRESULT (STDMETHODCALLTYPE *GetBitsPerPixel)(
        IWICPixelFormatInfo2 *This,
        UINT *puiBitsPerPixel);

    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IWICPixelFormatInfo2 *This,
        UINT *puiChannelCount);

    HRESULT (STDMETHODCALLTYPE *GetChannelMask)(
        IWICPixelFormatInfo2 *This,
        UINT uiChannelIndex,
        UINT cbMaskBuffer,
        BYTE *pbMaskBuffer,
        UINT *pcbActual);

    /*** IWICPixelFormatInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SupportsTransparency)(
        IWICPixelFormatInfo2 *This,
        WINBOOL *pfSupportsTransparency);

    HRESULT (STDMETHODCALLTYPE *GetNumericRepresentation)(
        IWICPixelFormatInfo2 *This,
        WICPixelFormatNumericRepresentation *pNumericRepresentation);

    END_INTERFACE
} IWICPixelFormatInfo2Vtbl;

interface IWICPixelFormatInfo2 {
    CONST_VTBL IWICPixelFormatInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICPixelFormatInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICPixelFormatInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICPixelFormatInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICPixelFormatInfo2_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICPixelFormatInfo2_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICPixelFormatInfo2_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICPixelFormatInfo2_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICPixelFormatInfo2_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICPixelFormatInfo2_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICPixelFormatInfo2_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICPixelFormatInfo2_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICPixelFormatInfo methods ***/
#define IWICPixelFormatInfo2_GetFormatGUID(This,pFormat) (This)->lpVtbl->GetFormatGUID(This,pFormat)
#define IWICPixelFormatInfo2_GetColorContext(This,ppIColorContext) (This)->lpVtbl->GetColorContext(This,ppIColorContext)
#define IWICPixelFormatInfo2_GetBitsPerPixel(This,puiBitsPerPixel) (This)->lpVtbl->GetBitsPerPixel(This,puiBitsPerPixel)
#define IWICPixelFormatInfo2_GetChannelCount(This,puiChannelCount) (This)->lpVtbl->GetChannelCount(This,puiChannelCount)
#define IWICPixelFormatInfo2_GetChannelMask(This,uiChannelIndex,cbMaskBuffer,pbMaskBuffer,pcbActual) (This)->lpVtbl->GetChannelMask(This,uiChannelIndex,cbMaskBuffer,pbMaskBuffer,pcbActual)
/*** IWICPixelFormatInfo2 methods ***/
#define IWICPixelFormatInfo2_SupportsTransparency(This,pfSupportsTransparency) (This)->lpVtbl->SupportsTransparency(This,pfSupportsTransparency)
#define IWICPixelFormatInfo2_GetNumericRepresentation(This,pNumericRepresentation) (This)->lpVtbl->GetNumericRepresentation(This,pNumericRepresentation)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICPixelFormatInfo2_QueryInterface(IWICPixelFormatInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICPixelFormatInfo2_AddRef(IWICPixelFormatInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICPixelFormatInfo2_Release(IWICPixelFormatInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICPixelFormatInfo2_GetComponentType(IWICPixelFormatInfo2* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICPixelFormatInfo2_GetCLSID(IWICPixelFormatInfo2* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICPixelFormatInfo2_GetSigningStatus(IWICPixelFormatInfo2* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICPixelFormatInfo2_GetAuthor(IWICPixelFormatInfo2* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICPixelFormatInfo2_GetVendorGUID(IWICPixelFormatInfo2* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICPixelFormatInfo2_GetVersion(IWICPixelFormatInfo2* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICPixelFormatInfo2_GetSpecVersion(IWICPixelFormatInfo2* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICPixelFormatInfo2_GetFriendlyName(IWICPixelFormatInfo2* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICPixelFormatInfo methods ***/
static inline HRESULT IWICPixelFormatInfo2_GetFormatGUID(IWICPixelFormatInfo2* This,GUID *pFormat) {
    return This->lpVtbl->GetFormatGUID(This,pFormat);
}
static inline HRESULT IWICPixelFormatInfo2_GetColorContext(IWICPixelFormatInfo2* This,IWICColorContext **ppIColorContext) {
    return This->lpVtbl->GetColorContext(This,ppIColorContext);
}
static inline HRESULT IWICPixelFormatInfo2_GetBitsPerPixel(IWICPixelFormatInfo2* This,UINT *puiBitsPerPixel) {
    return This->lpVtbl->GetBitsPerPixel(This,puiBitsPerPixel);
}
static inline HRESULT IWICPixelFormatInfo2_GetChannelCount(IWICPixelFormatInfo2* This,UINT *puiChannelCount) {
    return This->lpVtbl->GetChannelCount(This,puiChannelCount);
}
static inline HRESULT IWICPixelFormatInfo2_GetChannelMask(IWICPixelFormatInfo2* This,UINT uiChannelIndex,UINT cbMaskBuffer,BYTE *pbMaskBuffer,UINT *pcbActual) {
    return This->lpVtbl->GetChannelMask(This,uiChannelIndex,cbMaskBuffer,pbMaskBuffer,pcbActual);
}
/*** IWICPixelFormatInfo2 methods ***/
static inline HRESULT IWICPixelFormatInfo2_SupportsTransparency(IWICPixelFormatInfo2* This,WINBOOL *pfSupportsTransparency) {
    return This->lpVtbl->SupportsTransparency(This,pfSupportsTransparency);
}
static inline HRESULT IWICPixelFormatInfo2_GetNumericRepresentation(IWICPixelFormatInfo2* This,WICPixelFormatNumericRepresentation *pNumericRepresentation) {
    return This->lpVtbl->GetNumericRepresentation(This,pNumericRepresentation);
}
#endif
#endif

#endif


#endif  /* __IWICPixelFormatInfo2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapCodecInfo interface
 */
#ifndef __IWICBitmapCodecInfo_INTERFACE_DEFINED__
#define __IWICBitmapCodecInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapCodecInfo, 0xe87a44c4, 0xb76e, 0x4c47, 0x8b,0x09, 0x29,0x8e,0xb1,0x2a,0x27,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e87a44c4-b76e-4c47-8b09-298eb12a2714")
IWICBitmapCodecInfo : public IWICComponentInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetContainerFormat(
        GUID *pguidContainerFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPixelFormats(
        UINT cFormats,
        GUID *pguidPixelFormats,
        UINT *pcActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorManagementVersion(
        UINT cchColorManagementVersion,
        WCHAR *wzColorManagementVersion,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceManufacturer(
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceModels(
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMimeTypes(
        UINT cchMimeTypes,
        WCHAR *wzMimeTypes,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileExtensions(
        UINT cchFileExtensions,
        WCHAR *wzFileExtensions,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesSupportAnimation(
        WINBOOL *pfSupportAnimation) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesSupportChromaKey(
        WINBOOL *pfSupportChromaKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesSupportLossless(
        WINBOOL *pfSupportLossless) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesSupportMultiframe(
        WINBOOL *pfSupportMultiframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchesMimeType(
        LPCWSTR wzMimeType,
        WINBOOL *pfMatches) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapCodecInfo, 0xe87a44c4, 0xb76e, 0x4c47, 0x8b,0x09, 0x29,0x8e,0xb1,0x2a,0x27,0x14)
#endif
#else
typedef struct IWICBitmapCodecInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapCodecInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapCodecInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapCodecInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICBitmapCodecInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICBitmapCodecInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICBitmapCodecInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICBitmapCodecInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICBitmapCodecInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICBitmapCodecInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICBitmapCodecInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICBitmapCodecInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICBitmapCodecInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICBitmapCodecInfo *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormats)(
        IWICBitmapCodecInfo *This,
        UINT cFormats,
        GUID *pguidPixelFormats,
        UINT *pcActual);

    HRESULT (STDMETHODCALLTYPE *GetColorManagementVersion)(
        IWICBitmapCodecInfo *This,
        UINT cchColorManagementVersion,
        WCHAR *wzColorManagementVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceManufacturer)(
        IWICBitmapCodecInfo *This,
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceModels)(
        IWICBitmapCodecInfo *This,
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetMimeTypes)(
        IWICBitmapCodecInfo *This,
        UINT cchMimeTypes,
        WCHAR *wzMimeTypes,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFileExtensions)(
        IWICBitmapCodecInfo *This,
        UINT cchFileExtensions,
        WCHAR *wzFileExtensions,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *DoesSupportAnimation)(
        IWICBitmapCodecInfo *This,
        WINBOOL *pfSupportAnimation);

    HRESULT (STDMETHODCALLTYPE *DoesSupportChromaKey)(
        IWICBitmapCodecInfo *This,
        WINBOOL *pfSupportChromaKey);

    HRESULT (STDMETHODCALLTYPE *DoesSupportLossless)(
        IWICBitmapCodecInfo *This,
        WINBOOL *pfSupportLossless);

    HRESULT (STDMETHODCALLTYPE *DoesSupportMultiframe)(
        IWICBitmapCodecInfo *This,
        WINBOOL *pfSupportMultiframe);

    HRESULT (STDMETHODCALLTYPE *MatchesMimeType)(
        IWICBitmapCodecInfo *This,
        LPCWSTR wzMimeType,
        WINBOOL *pfMatches);

    END_INTERFACE
} IWICBitmapCodecInfoVtbl;

interface IWICBitmapCodecInfo {
    CONST_VTBL IWICBitmapCodecInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapCodecInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapCodecInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapCodecInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICBitmapCodecInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICBitmapCodecInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICBitmapCodecInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICBitmapCodecInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICBitmapCodecInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICBitmapCodecInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICBitmapCodecInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICBitmapCodecInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICBitmapCodecInfo methods ***/
#define IWICBitmapCodecInfo_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICBitmapCodecInfo_GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual) (This)->lpVtbl->GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual)
#define IWICBitmapCodecInfo_GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual) (This)->lpVtbl->GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual)
#define IWICBitmapCodecInfo_GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual) (This)->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual)
#define IWICBitmapCodecInfo_GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual) (This)->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual)
#define IWICBitmapCodecInfo_GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual) (This)->lpVtbl->GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual)
#define IWICBitmapCodecInfo_GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual) (This)->lpVtbl->GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual)
#define IWICBitmapCodecInfo_DoesSupportAnimation(This,pfSupportAnimation) (This)->lpVtbl->DoesSupportAnimation(This,pfSupportAnimation)
#define IWICBitmapCodecInfo_DoesSupportChromaKey(This,pfSupportChromaKey) (This)->lpVtbl->DoesSupportChromaKey(This,pfSupportChromaKey)
#define IWICBitmapCodecInfo_DoesSupportLossless(This,pfSupportLossless) (This)->lpVtbl->DoesSupportLossless(This,pfSupportLossless)
#define IWICBitmapCodecInfo_DoesSupportMultiframe(This,pfSupportMultiframe) (This)->lpVtbl->DoesSupportMultiframe(This,pfSupportMultiframe)
#define IWICBitmapCodecInfo_MatchesMimeType(This,wzMimeType,pfMatches) (This)->lpVtbl->MatchesMimeType(This,wzMimeType,pfMatches)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapCodecInfo_QueryInterface(IWICBitmapCodecInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapCodecInfo_AddRef(IWICBitmapCodecInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapCodecInfo_Release(IWICBitmapCodecInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICBitmapCodecInfo_GetComponentType(IWICBitmapCodecInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICBitmapCodecInfo_GetCLSID(IWICBitmapCodecInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICBitmapCodecInfo_GetSigningStatus(IWICBitmapCodecInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICBitmapCodecInfo_GetAuthor(IWICBitmapCodecInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetVendorGUID(IWICBitmapCodecInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICBitmapCodecInfo_GetVersion(IWICBitmapCodecInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetSpecVersion(IWICBitmapCodecInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetFriendlyName(IWICBitmapCodecInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICBitmapCodecInfo methods ***/
static inline HRESULT IWICBitmapCodecInfo_GetContainerFormat(IWICBitmapCodecInfo* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICBitmapCodecInfo_GetPixelFormats(IWICBitmapCodecInfo* This,UINT cFormats,GUID *pguidPixelFormats,UINT *pcActual) {
    return This->lpVtbl->GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetColorManagementVersion(IWICBitmapCodecInfo* This,UINT cchColorManagementVersion,WCHAR *wzColorManagementVersion,UINT *pcchActual) {
    return This->lpVtbl->GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetDeviceManufacturer(IWICBitmapCodecInfo* This,UINT cchDeviceManufacturer,WCHAR *wzDeviceManufacturer,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetDeviceModels(IWICBitmapCodecInfo* This,UINT cchDeviceModels,WCHAR *wzDeviceModels,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetMimeTypes(IWICBitmapCodecInfo* This,UINT cchMimeTypes,WCHAR *wzMimeTypes,UINT *pcchActual) {
    return This->lpVtbl->GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_GetFileExtensions(IWICBitmapCodecInfo* This,UINT cchFileExtensions,WCHAR *wzFileExtensions,UINT *pcchActual) {
    return This->lpVtbl->GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual);
}
static inline HRESULT IWICBitmapCodecInfo_DoesSupportAnimation(IWICBitmapCodecInfo* This,WINBOOL *pfSupportAnimation) {
    return This->lpVtbl->DoesSupportAnimation(This,pfSupportAnimation);
}
static inline HRESULT IWICBitmapCodecInfo_DoesSupportChromaKey(IWICBitmapCodecInfo* This,WINBOOL *pfSupportChromaKey) {
    return This->lpVtbl->DoesSupportChromaKey(This,pfSupportChromaKey);
}
static inline HRESULT IWICBitmapCodecInfo_DoesSupportLossless(IWICBitmapCodecInfo* This,WINBOOL *pfSupportLossless) {
    return This->lpVtbl->DoesSupportLossless(This,pfSupportLossless);
}
static inline HRESULT IWICBitmapCodecInfo_DoesSupportMultiframe(IWICBitmapCodecInfo* This,WINBOOL *pfSupportMultiframe) {
    return This->lpVtbl->DoesSupportMultiframe(This,pfSupportMultiframe);
}
static inline HRESULT IWICBitmapCodecInfo_MatchesMimeType(IWICBitmapCodecInfo* This,LPCWSTR wzMimeType,WINBOOL *pfMatches) {
    return This->lpVtbl->MatchesMimeType(This,wzMimeType,pfMatches);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapCodecInfo_INTERFACE_DEFINED__ */

#ifndef __IWICBitmapDecoder_FWD_DEFINED__
#define __IWICBitmapDecoder_FWD_DEFINED__
typedef interface IWICBitmapDecoder IWICBitmapDecoder;
#ifdef __cplusplus
interface IWICBitmapDecoder;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IWICBitmapDecoderInfo interface
 */
#ifndef __IWICBitmapDecoderInfo_INTERFACE_DEFINED__
#define __IWICBitmapDecoderInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapDecoderInfo, 0xd8cd007f, 0xd08f, 0x4191, 0x9b,0xfc, 0x23,0x6e,0xa7,0xf0,0xe4,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d8cd007f-d08f-4191-9bfc-236ea7f0e4b5")
IWICBitmapDecoderInfo : public IWICBitmapCodecInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetPatterns(
        UINT cbSizePatterns,
        WICBitmapPattern *pPatterns,
        UINT *pcPatterns,
        UINT *pcbPatternsActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchesPattern(
        IStream *pIStream,
        WINBOOL *pfMatches) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        IWICBitmapDecoder **ppIBitmapDecoder) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapDecoderInfo, 0xd8cd007f, 0xd08f, 0x4191, 0x9b,0xfc, 0x23,0x6e,0xa7,0xf0,0xe4,0xb5)
#endif
#else
typedef struct IWICBitmapDecoderInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapDecoderInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapDecoderInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapDecoderInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICBitmapDecoderInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICBitmapDecoderInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICBitmapDecoderInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICBitmapDecoderInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICBitmapDecoderInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICBitmapDecoderInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICBitmapDecoderInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICBitmapDecoderInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICBitmapCodecInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICBitmapDecoderInfo *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormats)(
        IWICBitmapDecoderInfo *This,
        UINT cFormats,
        GUID *pguidPixelFormats,
        UINT *pcActual);

    HRESULT (STDMETHODCALLTYPE *GetColorManagementVersion)(
        IWICBitmapDecoderInfo *This,
        UINT cchColorManagementVersion,
        WCHAR *wzColorManagementVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceManufacturer)(
        IWICBitmapDecoderInfo *This,
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceModels)(
        IWICBitmapDecoderInfo *This,
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetMimeTypes)(
        IWICBitmapDecoderInfo *This,
        UINT cchMimeTypes,
        WCHAR *wzMimeTypes,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFileExtensions)(
        IWICBitmapDecoderInfo *This,
        UINT cchFileExtensions,
        WCHAR *wzFileExtensions,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *DoesSupportAnimation)(
        IWICBitmapDecoderInfo *This,
        WINBOOL *pfSupportAnimation);

    HRESULT (STDMETHODCALLTYPE *DoesSupportChromaKey)(
        IWICBitmapDecoderInfo *This,
        WINBOOL *pfSupportChromaKey);

    HRESULT (STDMETHODCALLTYPE *DoesSupportLossless)(
        IWICBitmapDecoderInfo *This,
        WINBOOL *pfSupportLossless);

    HRESULT (STDMETHODCALLTYPE *DoesSupportMultiframe)(
        IWICBitmapDecoderInfo *This,
        WINBOOL *pfSupportMultiframe);

    HRESULT (STDMETHODCALLTYPE *MatchesMimeType)(
        IWICBitmapDecoderInfo *This,
        LPCWSTR wzMimeType,
        WINBOOL *pfMatches);

    /*** IWICBitmapDecoderInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPatterns)(
        IWICBitmapDecoderInfo *This,
        UINT cbSizePatterns,
        WICBitmapPattern *pPatterns,
        UINT *pcPatterns,
        UINT *pcbPatternsActual);

    HRESULT (STDMETHODCALLTYPE *MatchesPattern)(
        IWICBitmapDecoderInfo *This,
        IStream *pIStream,
        WINBOOL *pfMatches);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IWICBitmapDecoderInfo *This,
        IWICBitmapDecoder **ppIBitmapDecoder);

    END_INTERFACE
} IWICBitmapDecoderInfoVtbl;

interface IWICBitmapDecoderInfo {
    CONST_VTBL IWICBitmapDecoderInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapDecoderInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapDecoderInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapDecoderInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICBitmapDecoderInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICBitmapDecoderInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICBitmapDecoderInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICBitmapDecoderInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICBitmapDecoderInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICBitmapDecoderInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICBitmapDecoderInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICBitmapDecoderInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICBitmapCodecInfo methods ***/
#define IWICBitmapDecoderInfo_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICBitmapDecoderInfo_GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual) (This)->lpVtbl->GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual)
#define IWICBitmapDecoderInfo_GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual) (This)->lpVtbl->GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual)
#define IWICBitmapDecoderInfo_GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual) (This)->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual)
#define IWICBitmapDecoderInfo_GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual) (This)->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual)
#define IWICBitmapDecoderInfo_GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual) (This)->lpVtbl->GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual)
#define IWICBitmapDecoderInfo_GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual) (This)->lpVtbl->GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual)
#define IWICBitmapDecoderInfo_DoesSupportAnimation(This,pfSupportAnimation) (This)->lpVtbl->DoesSupportAnimation(This,pfSupportAnimation)
#define IWICBitmapDecoderInfo_DoesSupportChromaKey(This,pfSupportChromaKey) (This)->lpVtbl->DoesSupportChromaKey(This,pfSupportChromaKey)
#define IWICBitmapDecoderInfo_DoesSupportLossless(This,pfSupportLossless) (This)->lpVtbl->DoesSupportLossless(This,pfSupportLossless)
#define IWICBitmapDecoderInfo_DoesSupportMultiframe(This,pfSupportMultiframe) (This)->lpVtbl->DoesSupportMultiframe(This,pfSupportMultiframe)
#define IWICBitmapDecoderInfo_MatchesMimeType(This,wzMimeType,pfMatches) (This)->lpVtbl->MatchesMimeType(This,wzMimeType,pfMatches)
/*** IWICBitmapDecoderInfo methods ***/
#define IWICBitmapDecoderInfo_GetPatterns(This,cbSizePatterns,pPatterns,pcPatterns,pcbPatternsActual) (This)->lpVtbl->GetPatterns(This,cbSizePatterns,pPatterns,pcPatterns,pcbPatternsActual)
#define IWICBitmapDecoderInfo_MatchesPattern(This,pIStream,pfMatches) (This)->lpVtbl->MatchesPattern(This,pIStream,pfMatches)
#define IWICBitmapDecoderInfo_CreateInstance(This,ppIBitmapDecoder) (This)->lpVtbl->CreateInstance(This,ppIBitmapDecoder)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapDecoderInfo_QueryInterface(IWICBitmapDecoderInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapDecoderInfo_AddRef(IWICBitmapDecoderInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapDecoderInfo_Release(IWICBitmapDecoderInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICBitmapDecoderInfo_GetComponentType(IWICBitmapDecoderInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICBitmapDecoderInfo_GetCLSID(IWICBitmapDecoderInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICBitmapDecoderInfo_GetSigningStatus(IWICBitmapDecoderInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICBitmapDecoderInfo_GetAuthor(IWICBitmapDecoderInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetVendorGUID(IWICBitmapDecoderInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICBitmapDecoderInfo_GetVersion(IWICBitmapDecoderInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetSpecVersion(IWICBitmapDecoderInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetFriendlyName(IWICBitmapDecoderInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICBitmapCodecInfo methods ***/
static inline HRESULT IWICBitmapDecoderInfo_GetContainerFormat(IWICBitmapDecoderInfo* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICBitmapDecoderInfo_GetPixelFormats(IWICBitmapDecoderInfo* This,UINT cFormats,GUID *pguidPixelFormats,UINT *pcActual) {
    return This->lpVtbl->GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetColorManagementVersion(IWICBitmapDecoderInfo* This,UINT cchColorManagementVersion,WCHAR *wzColorManagementVersion,UINT *pcchActual) {
    return This->lpVtbl->GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetDeviceManufacturer(IWICBitmapDecoderInfo* This,UINT cchDeviceManufacturer,WCHAR *wzDeviceManufacturer,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetDeviceModels(IWICBitmapDecoderInfo* This,UINT cchDeviceModels,WCHAR *wzDeviceModels,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetMimeTypes(IWICBitmapDecoderInfo* This,UINT cchMimeTypes,WCHAR *wzMimeTypes,UINT *pcchActual) {
    return This->lpVtbl->GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_GetFileExtensions(IWICBitmapDecoderInfo* This,UINT cchFileExtensions,WCHAR *wzFileExtensions,UINT *pcchActual) {
    return This->lpVtbl->GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual);
}
static inline HRESULT IWICBitmapDecoderInfo_DoesSupportAnimation(IWICBitmapDecoderInfo* This,WINBOOL *pfSupportAnimation) {
    return This->lpVtbl->DoesSupportAnimation(This,pfSupportAnimation);
}
static inline HRESULT IWICBitmapDecoderInfo_DoesSupportChromaKey(IWICBitmapDecoderInfo* This,WINBOOL *pfSupportChromaKey) {
    return This->lpVtbl->DoesSupportChromaKey(This,pfSupportChromaKey);
}
static inline HRESULT IWICBitmapDecoderInfo_DoesSupportLossless(IWICBitmapDecoderInfo* This,WINBOOL *pfSupportLossless) {
    return This->lpVtbl->DoesSupportLossless(This,pfSupportLossless);
}
static inline HRESULT IWICBitmapDecoderInfo_DoesSupportMultiframe(IWICBitmapDecoderInfo* This,WINBOOL *pfSupportMultiframe) {
    return This->lpVtbl->DoesSupportMultiframe(This,pfSupportMultiframe);
}
static inline HRESULT IWICBitmapDecoderInfo_MatchesMimeType(IWICBitmapDecoderInfo* This,LPCWSTR wzMimeType,WINBOOL *pfMatches) {
    return This->lpVtbl->MatchesMimeType(This,wzMimeType,pfMatches);
}
/*** IWICBitmapDecoderInfo methods ***/
static inline HRESULT IWICBitmapDecoderInfo_GetPatterns(IWICBitmapDecoderInfo* This,UINT cbSizePatterns,WICBitmapPattern *pPatterns,UINT *pcPatterns,UINT *pcbPatternsActual) {
    return This->lpVtbl->GetPatterns(This,cbSizePatterns,pPatterns,pcPatterns,pcbPatternsActual);
}
static inline HRESULT IWICBitmapDecoderInfo_MatchesPattern(IWICBitmapDecoderInfo* This,IStream *pIStream,WINBOOL *pfMatches) {
    return This->lpVtbl->MatchesPattern(This,pIStream,pfMatches);
}
static inline HRESULT IWICBitmapDecoderInfo_CreateInstance(IWICBitmapDecoderInfo* This,IWICBitmapDecoder **ppIBitmapDecoder) {
    return This->lpVtbl->CreateInstance(This,ppIBitmapDecoder);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapDecoderInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapDecoder interface
 */
#ifndef __IWICBitmapDecoder_INTERFACE_DEFINED__
#define __IWICBitmapDecoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapDecoder, 0x9edde9e7, 0x8dee, 0x47ea, 0x99,0xdf, 0xe6,0xfa,0xf2,0xed,0x44,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9edde9e7-8dee-47ea-99df-e6faf2ed44bf")
IWICBitmapDecoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryCapability(
        IStream *pIStream,
        DWORD *pdwCapability) = 0;

    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IStream *pIStream,
        WICDecodeOptions cacheOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContainerFormat(
        GUID *pguidContainerFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDecoderInfo(
        IWICBitmapDecoderInfo **ppIDecoderInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyPalette(
        IWICPalette *pIPalette) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadataQueryReader(
        IWICMetadataQueryReader **ppIMetadataQueryReader) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreview(
        IWICBitmapSource **ppIBitmapSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorContexts(
        UINT cCount,
        IWICColorContext **ppIColorContexts,
        UINT *pcActualCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetThumbnail(
        IWICBitmapSource **ppIThumbnail) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrameCount(
        UINT *pCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrame(
        UINT index,
        IWICBitmapFrameDecode **ppIBitmapFrame) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapDecoder, 0x9edde9e7, 0x8dee, 0x47ea, 0x99,0xdf, 0xe6,0xfa,0xf2,0xed,0x44,0xbf)
#endif
#else
typedef struct IWICBitmapDecoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapDecoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapDecoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapDecoder *This);

    /*** IWICBitmapDecoder methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryCapability)(
        IWICBitmapDecoder *This,
        IStream *pIStream,
        DWORD *pdwCapability);

    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICBitmapDecoder *This,
        IStream *pIStream,
        WICDecodeOptions cacheOptions);

    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICBitmapDecoder *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetDecoderInfo)(
        IWICBitmapDecoder *This,
        IWICBitmapDecoderInfo **ppIDecoderInfo);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICBitmapDecoder *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *GetMetadataQueryReader)(
        IWICBitmapDecoder *This,
        IWICMetadataQueryReader **ppIMetadataQueryReader);

    HRESULT (STDMETHODCALLTYPE *GetPreview)(
        IWICBitmapDecoder *This,
        IWICBitmapSource **ppIBitmapSource);

    HRESULT (STDMETHODCALLTYPE *GetColorContexts)(
        IWICBitmapDecoder *This,
        UINT cCount,
        IWICColorContext **ppIColorContexts,
        UINT *pcActualCount);

    HRESULT (STDMETHODCALLTYPE *GetThumbnail)(
        IWICBitmapDecoder *This,
        IWICBitmapSource **ppIThumbnail);

    HRESULT (STDMETHODCALLTYPE *GetFrameCount)(
        IWICBitmapDecoder *This,
        UINT *pCount);

    HRESULT (STDMETHODCALLTYPE *GetFrame)(
        IWICBitmapDecoder *This,
        UINT index,
        IWICBitmapFrameDecode **ppIBitmapFrame);

    END_INTERFACE
} IWICBitmapDecoderVtbl;

interface IWICBitmapDecoder {
    CONST_VTBL IWICBitmapDecoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapDecoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapDecoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapDecoder_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapDecoder methods ***/
#define IWICBitmapDecoder_QueryCapability(This,pIStream,pdwCapability) (This)->lpVtbl->QueryCapability(This,pIStream,pdwCapability)
#define IWICBitmapDecoder_Initialize(This,pIStream,cacheOptions) (This)->lpVtbl->Initialize(This,pIStream,cacheOptions)
#define IWICBitmapDecoder_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICBitmapDecoder_GetDecoderInfo(This,ppIDecoderInfo) (This)->lpVtbl->GetDecoderInfo(This,ppIDecoderInfo)
#define IWICBitmapDecoder_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICBitmapDecoder_GetMetadataQueryReader(This,ppIMetadataQueryReader) (This)->lpVtbl->GetMetadataQueryReader(This,ppIMetadataQueryReader)
#define IWICBitmapDecoder_GetPreview(This,ppIBitmapSource) (This)->lpVtbl->GetPreview(This,ppIBitmapSource)
#define IWICBitmapDecoder_GetColorContexts(This,cCount,ppIColorContexts,pcActualCount) (This)->lpVtbl->GetColorContexts(This,cCount,ppIColorContexts,pcActualCount)
#define IWICBitmapDecoder_GetThumbnail(This,ppIThumbnail) (This)->lpVtbl->GetThumbnail(This,ppIThumbnail)
#define IWICBitmapDecoder_GetFrameCount(This,pCount) (This)->lpVtbl->GetFrameCount(This,pCount)
#define IWICBitmapDecoder_GetFrame(This,index,ppIBitmapFrame) (This)->lpVtbl->GetFrame(This,index,ppIBitmapFrame)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapDecoder_QueryInterface(IWICBitmapDecoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapDecoder_AddRef(IWICBitmapDecoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapDecoder_Release(IWICBitmapDecoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapDecoder methods ***/
static inline HRESULT IWICBitmapDecoder_QueryCapability(IWICBitmapDecoder* This,IStream *pIStream,DWORD *pdwCapability) {
    return This->lpVtbl->QueryCapability(This,pIStream,pdwCapability);
}
static inline HRESULT IWICBitmapDecoder_Initialize(IWICBitmapDecoder* This,IStream *pIStream,WICDecodeOptions cacheOptions) {
    return This->lpVtbl->Initialize(This,pIStream,cacheOptions);
}
static inline HRESULT IWICBitmapDecoder_GetContainerFormat(IWICBitmapDecoder* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICBitmapDecoder_GetDecoderInfo(IWICBitmapDecoder* This,IWICBitmapDecoderInfo **ppIDecoderInfo) {
    return This->lpVtbl->GetDecoderInfo(This,ppIDecoderInfo);
}
static inline HRESULT IWICBitmapDecoder_CopyPalette(IWICBitmapDecoder* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapDecoder_GetMetadataQueryReader(IWICBitmapDecoder* This,IWICMetadataQueryReader **ppIMetadataQueryReader) {
    return This->lpVtbl->GetMetadataQueryReader(This,ppIMetadataQueryReader);
}
static inline HRESULT IWICBitmapDecoder_GetPreview(IWICBitmapDecoder* This,IWICBitmapSource **ppIBitmapSource) {
    return This->lpVtbl->GetPreview(This,ppIBitmapSource);
}
static inline HRESULT IWICBitmapDecoder_GetColorContexts(IWICBitmapDecoder* This,UINT cCount,IWICColorContext **ppIColorContexts,UINT *pcActualCount) {
    return This->lpVtbl->GetColorContexts(This,cCount,ppIColorContexts,pcActualCount);
}
static inline HRESULT IWICBitmapDecoder_GetThumbnail(IWICBitmapDecoder* This,IWICBitmapSource **ppIThumbnail) {
    return This->lpVtbl->GetThumbnail(This,ppIThumbnail);
}
static inline HRESULT IWICBitmapDecoder_GetFrameCount(IWICBitmapDecoder* This,UINT *pCount) {
    return This->lpVtbl->GetFrameCount(This,pCount);
}
static inline HRESULT IWICBitmapDecoder_GetFrame(IWICBitmapDecoder* This,UINT index,IWICBitmapFrameDecode **ppIBitmapFrame) {
    return This->lpVtbl->GetFrame(This,index,ppIBitmapFrame);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapDecoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapFrameEncode interface
 */
#ifndef __IWICBitmapFrameEncode_INTERFACE_DEFINED__
#define __IWICBitmapFrameEncode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapFrameEncode, 0x00000105, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000105-a8f2-4877-ba0a-fd2b6645fb94")
IWICBitmapFrameEncode : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IPropertyBag2 *pIEncoderOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSize(
        UINT uiWidth,
        UINT uiHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetResolution(
        double dpiX,
        double dpiY) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPixelFormat(
        WICPixelFormatGUID *pPixelFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColorContexts(
        UINT cCount,
        IWICColorContext **ppIColorContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPalette(
        IWICPalette *pIPalette) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetThumbnail(
        IWICBitmapSource *pIThumbnail) = 0;

    virtual HRESULT STDMETHODCALLTYPE WritePixels(
        UINT lineCount,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbPixels) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteSource(
        IWICBitmapSource *pIBitmapSource,
        WICRect *prc) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadataQueryWriter(
        IWICMetadataQueryWriter **ppIMetadataQueryWriter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapFrameEncode, 0x00000105, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICBitmapFrameEncodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapFrameEncode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapFrameEncode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapFrameEncode *This);

    /*** IWICBitmapFrameEncode methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICBitmapFrameEncode *This,
        IPropertyBag2 *pIEncoderOptions);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        IWICBitmapFrameEncode *This,
        UINT uiWidth,
        UINT uiHeight);

    HRESULT (STDMETHODCALLTYPE *SetResolution)(
        IWICBitmapFrameEncode *This,
        double dpiX,
        double dpiY);

    HRESULT (STDMETHODCALLTYPE *SetPixelFormat)(
        IWICBitmapFrameEncode *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *SetColorContexts)(
        IWICBitmapFrameEncode *This,
        UINT cCount,
        IWICColorContext **ppIColorContext);

    HRESULT (STDMETHODCALLTYPE *SetPalette)(
        IWICBitmapFrameEncode *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *SetThumbnail)(
        IWICBitmapFrameEncode *This,
        IWICBitmapSource *pIThumbnail);

    HRESULT (STDMETHODCALLTYPE *WritePixels)(
        IWICBitmapFrameEncode *This,
        UINT lineCount,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbPixels);

    HRESULT (STDMETHODCALLTYPE *WriteSource)(
        IWICBitmapFrameEncode *This,
        IWICBitmapSource *pIBitmapSource,
        WICRect *prc);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWICBitmapFrameEncode *This);

    HRESULT (STDMETHODCALLTYPE *GetMetadataQueryWriter)(
        IWICBitmapFrameEncode *This,
        IWICMetadataQueryWriter **ppIMetadataQueryWriter);

    END_INTERFACE
} IWICBitmapFrameEncodeVtbl;

interface IWICBitmapFrameEncode {
    CONST_VTBL IWICBitmapFrameEncodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapFrameEncode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapFrameEncode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapFrameEncode_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapFrameEncode methods ***/
#define IWICBitmapFrameEncode_Initialize(This,pIEncoderOptions) (This)->lpVtbl->Initialize(This,pIEncoderOptions)
#define IWICBitmapFrameEncode_SetSize(This,uiWidth,uiHeight) (This)->lpVtbl->SetSize(This,uiWidth,uiHeight)
#define IWICBitmapFrameEncode_SetResolution(This,dpiX,dpiY) (This)->lpVtbl->SetResolution(This,dpiX,dpiY)
#define IWICBitmapFrameEncode_SetPixelFormat(This,pPixelFormat) (This)->lpVtbl->SetPixelFormat(This,pPixelFormat)
#define IWICBitmapFrameEncode_SetColorContexts(This,cCount,ppIColorContext) (This)->lpVtbl->SetColorContexts(This,cCount,ppIColorContext)
#define IWICBitmapFrameEncode_SetPalette(This,pIPalette) (This)->lpVtbl->SetPalette(This,pIPalette)
#define IWICBitmapFrameEncode_SetThumbnail(This,pIThumbnail) (This)->lpVtbl->SetThumbnail(This,pIThumbnail)
#define IWICBitmapFrameEncode_WritePixels(This,lineCount,cbStride,cbBufferSize,pbPixels) (This)->lpVtbl->WritePixels(This,lineCount,cbStride,cbBufferSize,pbPixels)
#define IWICBitmapFrameEncode_WriteSource(This,pIBitmapSource,prc) (This)->lpVtbl->WriteSource(This,pIBitmapSource,prc)
#define IWICBitmapFrameEncode_Commit(This) (This)->lpVtbl->Commit(This)
#define IWICBitmapFrameEncode_GetMetadataQueryWriter(This,ppIMetadataQueryWriter) (This)->lpVtbl->GetMetadataQueryWriter(This,ppIMetadataQueryWriter)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapFrameEncode_QueryInterface(IWICBitmapFrameEncode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapFrameEncode_AddRef(IWICBitmapFrameEncode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapFrameEncode_Release(IWICBitmapFrameEncode* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapFrameEncode methods ***/
static inline HRESULT IWICBitmapFrameEncode_Initialize(IWICBitmapFrameEncode* This,IPropertyBag2 *pIEncoderOptions) {
    return This->lpVtbl->Initialize(This,pIEncoderOptions);
}
static inline HRESULT IWICBitmapFrameEncode_SetSize(IWICBitmapFrameEncode* This,UINT uiWidth,UINT uiHeight) {
    return This->lpVtbl->SetSize(This,uiWidth,uiHeight);
}
static inline HRESULT IWICBitmapFrameEncode_SetResolution(IWICBitmapFrameEncode* This,double dpiX,double dpiY) {
    return This->lpVtbl->SetResolution(This,dpiX,dpiY);
}
static inline HRESULT IWICBitmapFrameEncode_SetPixelFormat(IWICBitmapFrameEncode* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->SetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICBitmapFrameEncode_SetColorContexts(IWICBitmapFrameEncode* This,UINT cCount,IWICColorContext **ppIColorContext) {
    return This->lpVtbl->SetColorContexts(This,cCount,ppIColorContext);
}
static inline HRESULT IWICBitmapFrameEncode_SetPalette(IWICBitmapFrameEncode* This,IWICPalette *pIPalette) {
    return This->lpVtbl->SetPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapFrameEncode_SetThumbnail(IWICBitmapFrameEncode* This,IWICBitmapSource *pIThumbnail) {
    return This->lpVtbl->SetThumbnail(This,pIThumbnail);
}
static inline HRESULT IWICBitmapFrameEncode_WritePixels(IWICBitmapFrameEncode* This,UINT lineCount,UINT cbStride,UINT cbBufferSize,BYTE *pbPixels) {
    return This->lpVtbl->WritePixels(This,lineCount,cbStride,cbBufferSize,pbPixels);
}
static inline HRESULT IWICBitmapFrameEncode_WriteSource(IWICBitmapFrameEncode* This,IWICBitmapSource *pIBitmapSource,WICRect *prc) {
    return This->lpVtbl->WriteSource(This,pIBitmapSource,prc);
}
static inline HRESULT IWICBitmapFrameEncode_Commit(IWICBitmapFrameEncode* This) {
    return This->lpVtbl->Commit(This);
}
static inline HRESULT IWICBitmapFrameEncode_GetMetadataQueryWriter(IWICBitmapFrameEncode* This,IWICMetadataQueryWriter **ppIMetadataQueryWriter) {
    return This->lpVtbl->GetMetadataQueryWriter(This,ppIMetadataQueryWriter);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapFrameEncode_INTERFACE_DEFINED__ */

#ifndef __IWICBitmapEncoder_FWD_DEFINED__
#define __IWICBitmapEncoder_FWD_DEFINED__
typedef interface IWICBitmapEncoder IWICBitmapEncoder;
#ifdef __cplusplus
interface IWICBitmapEncoder;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IWICBitmapEncoderInfo interface
 */
#ifndef __IWICBitmapEncoderInfo_INTERFACE_DEFINED__
#define __IWICBitmapEncoderInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapEncoderInfo, 0x94c9b4ee, 0xa09f, 0x4f92, 0x8a,0x1e, 0x4a,0x9b,0xce,0x7e,0x76,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94c9b4ee-a09f-4f92-8a1e-4a9bce7e76fb")
IWICBitmapEncoderInfo : public IWICBitmapCodecInfo
{
    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        IWICBitmapEncoder **ppIBitmapEncoder) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapEncoderInfo, 0x94c9b4ee, 0xa09f, 0x4f92, 0x8a,0x1e, 0x4a,0x9b,0xce,0x7e,0x76,0xfb)
#endif
#else
typedef struct IWICBitmapEncoderInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapEncoderInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapEncoderInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapEncoderInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICBitmapEncoderInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICBitmapEncoderInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICBitmapEncoderInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICBitmapEncoderInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICBitmapEncoderInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICBitmapEncoderInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICBitmapEncoderInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICBitmapEncoderInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICBitmapCodecInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICBitmapEncoderInfo *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormats)(
        IWICBitmapEncoderInfo *This,
        UINT cFormats,
        GUID *pguidPixelFormats,
        UINT *pcActual);

    HRESULT (STDMETHODCALLTYPE *GetColorManagementVersion)(
        IWICBitmapEncoderInfo *This,
        UINT cchColorManagementVersion,
        WCHAR *wzColorManagementVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceManufacturer)(
        IWICBitmapEncoderInfo *This,
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceModels)(
        IWICBitmapEncoderInfo *This,
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetMimeTypes)(
        IWICBitmapEncoderInfo *This,
        UINT cchMimeTypes,
        WCHAR *wzMimeTypes,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFileExtensions)(
        IWICBitmapEncoderInfo *This,
        UINT cchFileExtensions,
        WCHAR *wzFileExtensions,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *DoesSupportAnimation)(
        IWICBitmapEncoderInfo *This,
        WINBOOL *pfSupportAnimation);

    HRESULT (STDMETHODCALLTYPE *DoesSupportChromaKey)(
        IWICBitmapEncoderInfo *This,
        WINBOOL *pfSupportChromaKey);

    HRESULT (STDMETHODCALLTYPE *DoesSupportLossless)(
        IWICBitmapEncoderInfo *This,
        WINBOOL *pfSupportLossless);

    HRESULT (STDMETHODCALLTYPE *DoesSupportMultiframe)(
        IWICBitmapEncoderInfo *This,
        WINBOOL *pfSupportMultiframe);

    HRESULT (STDMETHODCALLTYPE *MatchesMimeType)(
        IWICBitmapEncoderInfo *This,
        LPCWSTR wzMimeType,
        WINBOOL *pfMatches);

    /*** IWICBitmapEncoderInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IWICBitmapEncoderInfo *This,
        IWICBitmapEncoder **ppIBitmapEncoder);

    END_INTERFACE
} IWICBitmapEncoderInfoVtbl;

interface IWICBitmapEncoderInfo {
    CONST_VTBL IWICBitmapEncoderInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapEncoderInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapEncoderInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapEncoderInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICBitmapEncoderInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICBitmapEncoderInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICBitmapEncoderInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICBitmapEncoderInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICBitmapEncoderInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICBitmapEncoderInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICBitmapEncoderInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICBitmapEncoderInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICBitmapCodecInfo methods ***/
#define IWICBitmapEncoderInfo_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICBitmapEncoderInfo_GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual) (This)->lpVtbl->GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual)
#define IWICBitmapEncoderInfo_GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual) (This)->lpVtbl->GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual)
#define IWICBitmapEncoderInfo_GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual) (This)->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual)
#define IWICBitmapEncoderInfo_GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual) (This)->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual)
#define IWICBitmapEncoderInfo_GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual) (This)->lpVtbl->GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual)
#define IWICBitmapEncoderInfo_GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual) (This)->lpVtbl->GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual)
#define IWICBitmapEncoderInfo_DoesSupportAnimation(This,pfSupportAnimation) (This)->lpVtbl->DoesSupportAnimation(This,pfSupportAnimation)
#define IWICBitmapEncoderInfo_DoesSupportChromaKey(This,pfSupportChromaKey) (This)->lpVtbl->DoesSupportChromaKey(This,pfSupportChromaKey)
#define IWICBitmapEncoderInfo_DoesSupportLossless(This,pfSupportLossless) (This)->lpVtbl->DoesSupportLossless(This,pfSupportLossless)
#define IWICBitmapEncoderInfo_DoesSupportMultiframe(This,pfSupportMultiframe) (This)->lpVtbl->DoesSupportMultiframe(This,pfSupportMultiframe)
#define IWICBitmapEncoderInfo_MatchesMimeType(This,wzMimeType,pfMatches) (This)->lpVtbl->MatchesMimeType(This,wzMimeType,pfMatches)
/*** IWICBitmapEncoderInfo methods ***/
#define IWICBitmapEncoderInfo_CreateInstance(This,ppIBitmapEncoder) (This)->lpVtbl->CreateInstance(This,ppIBitmapEncoder)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapEncoderInfo_QueryInterface(IWICBitmapEncoderInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapEncoderInfo_AddRef(IWICBitmapEncoderInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapEncoderInfo_Release(IWICBitmapEncoderInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICBitmapEncoderInfo_GetComponentType(IWICBitmapEncoderInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICBitmapEncoderInfo_GetCLSID(IWICBitmapEncoderInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICBitmapEncoderInfo_GetSigningStatus(IWICBitmapEncoderInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICBitmapEncoderInfo_GetAuthor(IWICBitmapEncoderInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetVendorGUID(IWICBitmapEncoderInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICBitmapEncoderInfo_GetVersion(IWICBitmapEncoderInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetSpecVersion(IWICBitmapEncoderInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetFriendlyName(IWICBitmapEncoderInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICBitmapCodecInfo methods ***/
static inline HRESULT IWICBitmapEncoderInfo_GetContainerFormat(IWICBitmapEncoderInfo* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICBitmapEncoderInfo_GetPixelFormats(IWICBitmapEncoderInfo* This,UINT cFormats,GUID *pguidPixelFormats,UINT *pcActual) {
    return This->lpVtbl->GetPixelFormats(This,cFormats,pguidPixelFormats,pcActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetColorManagementVersion(IWICBitmapEncoderInfo* This,UINT cchColorManagementVersion,WCHAR *wzColorManagementVersion,UINT *pcchActual) {
    return This->lpVtbl->GetColorManagementVersion(This,cchColorManagementVersion,wzColorManagementVersion,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetDeviceManufacturer(IWICBitmapEncoderInfo* This,UINT cchDeviceManufacturer,WCHAR *wzDeviceManufacturer,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetDeviceModels(IWICBitmapEncoderInfo* This,UINT cchDeviceModels,WCHAR *wzDeviceModels,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetMimeTypes(IWICBitmapEncoderInfo* This,UINT cchMimeTypes,WCHAR *wzMimeTypes,UINT *pcchActual) {
    return This->lpVtbl->GetMimeTypes(This,cchMimeTypes,wzMimeTypes,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_GetFileExtensions(IWICBitmapEncoderInfo* This,UINT cchFileExtensions,WCHAR *wzFileExtensions,UINT *pcchActual) {
    return This->lpVtbl->GetFileExtensions(This,cchFileExtensions,wzFileExtensions,pcchActual);
}
static inline HRESULT IWICBitmapEncoderInfo_DoesSupportAnimation(IWICBitmapEncoderInfo* This,WINBOOL *pfSupportAnimation) {
    return This->lpVtbl->DoesSupportAnimation(This,pfSupportAnimation);
}
static inline HRESULT IWICBitmapEncoderInfo_DoesSupportChromaKey(IWICBitmapEncoderInfo* This,WINBOOL *pfSupportChromaKey) {
    return This->lpVtbl->DoesSupportChromaKey(This,pfSupportChromaKey);
}
static inline HRESULT IWICBitmapEncoderInfo_DoesSupportLossless(IWICBitmapEncoderInfo* This,WINBOOL *pfSupportLossless) {
    return This->lpVtbl->DoesSupportLossless(This,pfSupportLossless);
}
static inline HRESULT IWICBitmapEncoderInfo_DoesSupportMultiframe(IWICBitmapEncoderInfo* This,WINBOOL *pfSupportMultiframe) {
    return This->lpVtbl->DoesSupportMultiframe(This,pfSupportMultiframe);
}
static inline HRESULT IWICBitmapEncoderInfo_MatchesMimeType(IWICBitmapEncoderInfo* This,LPCWSTR wzMimeType,WINBOOL *pfMatches) {
    return This->lpVtbl->MatchesMimeType(This,wzMimeType,pfMatches);
}
/*** IWICBitmapEncoderInfo methods ***/
static inline HRESULT IWICBitmapEncoderInfo_CreateInstance(IWICBitmapEncoderInfo* This,IWICBitmapEncoder **ppIBitmapEncoder) {
    return This->lpVtbl->CreateInstance(This,ppIBitmapEncoder);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapEncoderInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapEncoder interface
 */
#ifndef __IWICBitmapEncoder_INTERFACE_DEFINED__
#define __IWICBitmapEncoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapEncoder, 0x00000103, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000103-a8f2-4877-ba0a-fd2b6645fb94")
IWICBitmapEncoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IStream *pIStream,
        WICBitmapEncoderCacheOption cacheOption) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContainerFormat(
        GUID *pguidContainerFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEncoderInfo(
        IWICBitmapEncoderInfo **ppIEncoderInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColorContexts(
        UINT cCount,
        IWICColorContext **ppIColorContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPalette(
        IWICPalette *pIPalette) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetThumbnail(
        IWICBitmapSource *pIThumbnail) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreview(
        IWICBitmapSource *pIPreview) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNewFrame(
        IWICBitmapFrameEncode **ppIFrameEncode,
        IPropertyBag2 **ppIEncoderOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadataQueryWriter(
        IWICMetadataQueryWriter **ppIMetadataQueryWriter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapEncoder, 0x00000103, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICBitmapEncoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapEncoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapEncoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapEncoder *This);

    /*** IWICBitmapEncoder methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICBitmapEncoder *This,
        IStream *pIStream,
        WICBitmapEncoderCacheOption cacheOption);

    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICBitmapEncoder *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetEncoderInfo)(
        IWICBitmapEncoder *This,
        IWICBitmapEncoderInfo **ppIEncoderInfo);

    HRESULT (STDMETHODCALLTYPE *SetColorContexts)(
        IWICBitmapEncoder *This,
        UINT cCount,
        IWICColorContext **ppIColorContext);

    HRESULT (STDMETHODCALLTYPE *SetPalette)(
        IWICBitmapEncoder *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *SetThumbnail)(
        IWICBitmapEncoder *This,
        IWICBitmapSource *pIThumbnail);

    HRESULT (STDMETHODCALLTYPE *SetPreview)(
        IWICBitmapEncoder *This,
        IWICBitmapSource *pIPreview);

    HRESULT (STDMETHODCALLTYPE *CreateNewFrame)(
        IWICBitmapEncoder *This,
        IWICBitmapFrameEncode **ppIFrameEncode,
        IPropertyBag2 **ppIEncoderOptions);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWICBitmapEncoder *This);

    HRESULT (STDMETHODCALLTYPE *GetMetadataQueryWriter)(
        IWICBitmapEncoder *This,
        IWICMetadataQueryWriter **ppIMetadataQueryWriter);

    END_INTERFACE
} IWICBitmapEncoderVtbl;

interface IWICBitmapEncoder {
    CONST_VTBL IWICBitmapEncoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapEncoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapEncoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapEncoder_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapEncoder methods ***/
#define IWICBitmapEncoder_Initialize(This,pIStream,cacheOption) (This)->lpVtbl->Initialize(This,pIStream,cacheOption)
#define IWICBitmapEncoder_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICBitmapEncoder_GetEncoderInfo(This,ppIEncoderInfo) (This)->lpVtbl->GetEncoderInfo(This,ppIEncoderInfo)
#define IWICBitmapEncoder_SetColorContexts(This,cCount,ppIColorContext) (This)->lpVtbl->SetColorContexts(This,cCount,ppIColorContext)
#define IWICBitmapEncoder_SetPalette(This,pIPalette) (This)->lpVtbl->SetPalette(This,pIPalette)
#define IWICBitmapEncoder_SetThumbnail(This,pIThumbnail) (This)->lpVtbl->SetThumbnail(This,pIThumbnail)
#define IWICBitmapEncoder_SetPreview(This,pIPreview) (This)->lpVtbl->SetPreview(This,pIPreview)
#define IWICBitmapEncoder_CreateNewFrame(This,ppIFrameEncode,ppIEncoderOptions) (This)->lpVtbl->CreateNewFrame(This,ppIFrameEncode,ppIEncoderOptions)
#define IWICBitmapEncoder_Commit(This) (This)->lpVtbl->Commit(This)
#define IWICBitmapEncoder_GetMetadataQueryWriter(This,ppIMetadataQueryWriter) (This)->lpVtbl->GetMetadataQueryWriter(This,ppIMetadataQueryWriter)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapEncoder_QueryInterface(IWICBitmapEncoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapEncoder_AddRef(IWICBitmapEncoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapEncoder_Release(IWICBitmapEncoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapEncoder methods ***/
static inline HRESULT IWICBitmapEncoder_Initialize(IWICBitmapEncoder* This,IStream *pIStream,WICBitmapEncoderCacheOption cacheOption) {
    return This->lpVtbl->Initialize(This,pIStream,cacheOption);
}
static inline HRESULT IWICBitmapEncoder_GetContainerFormat(IWICBitmapEncoder* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICBitmapEncoder_GetEncoderInfo(IWICBitmapEncoder* This,IWICBitmapEncoderInfo **ppIEncoderInfo) {
    return This->lpVtbl->GetEncoderInfo(This,ppIEncoderInfo);
}
static inline HRESULT IWICBitmapEncoder_SetColorContexts(IWICBitmapEncoder* This,UINT cCount,IWICColorContext **ppIColorContext) {
    return This->lpVtbl->SetColorContexts(This,cCount,ppIColorContext);
}
static inline HRESULT IWICBitmapEncoder_SetPalette(IWICBitmapEncoder* This,IWICPalette *pIPalette) {
    return This->lpVtbl->SetPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapEncoder_SetThumbnail(IWICBitmapEncoder* This,IWICBitmapSource *pIThumbnail) {
    return This->lpVtbl->SetThumbnail(This,pIThumbnail);
}
static inline HRESULT IWICBitmapEncoder_SetPreview(IWICBitmapEncoder* This,IWICBitmapSource *pIPreview) {
    return This->lpVtbl->SetPreview(This,pIPreview);
}
static inline HRESULT IWICBitmapEncoder_CreateNewFrame(IWICBitmapEncoder* This,IWICBitmapFrameEncode **ppIFrameEncode,IPropertyBag2 **ppIEncoderOptions) {
    return This->lpVtbl->CreateNewFrame(This,ppIFrameEncode,ppIEncoderOptions);
}
static inline HRESULT IWICBitmapEncoder_Commit(IWICBitmapEncoder* This) {
    return This->lpVtbl->Commit(This);
}
static inline HRESULT IWICBitmapEncoder_GetMetadataQueryWriter(IWICBitmapEncoder* This,IWICMetadataQueryWriter **ppIMetadataQueryWriter) {
    return This->lpVtbl->GetMetadataQueryWriter(This,ppIMetadataQueryWriter);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapEncoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICFormatConverter interface
 */
#ifndef __IWICFormatConverter_INTERFACE_DEFINED__
#define __IWICFormatConverter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICFormatConverter, 0x00000301, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000301-a8f2-4877-ba0a-fd2b6645fb94")
IWICFormatConverter : public IWICBitmapSource
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IWICBitmapSource *pISource,
        REFWICPixelFormatGUID dstFormat,
        WICBitmapDitherType dither,
        IWICPalette *pIPalette,
        double alphaThresholdPercent,
        WICBitmapPaletteType paletteTranslate) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanConvert(
        REFWICPixelFormatGUID srcPixelFormat,
        REFWICPixelFormatGUID dstPixelFormat,
        WINBOOL *pfCanConvert) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICFormatConverter, 0x00000301, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICFormatConverterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICFormatConverter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICFormatConverter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICFormatConverter *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICFormatConverter *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICFormatConverter *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICFormatConverter *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICFormatConverter *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICFormatConverter *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    /*** IWICFormatConverter methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICFormatConverter *This,
        IWICBitmapSource *pISource,
        REFWICPixelFormatGUID dstFormat,
        WICBitmapDitherType dither,
        IWICPalette *pIPalette,
        double alphaThresholdPercent,
        WICBitmapPaletteType paletteTranslate);

    HRESULT (STDMETHODCALLTYPE *CanConvert)(
        IWICFormatConverter *This,
        REFWICPixelFormatGUID srcPixelFormat,
        REFWICPixelFormatGUID dstPixelFormat,
        WINBOOL *pfCanConvert);

    END_INTERFACE
} IWICFormatConverterVtbl;

interface IWICFormatConverter {
    CONST_VTBL IWICFormatConverterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICFormatConverter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICFormatConverter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICFormatConverter_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICFormatConverter_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICFormatConverter_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICFormatConverter_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICFormatConverter_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICFormatConverter_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
/*** IWICFormatConverter methods ***/
#define IWICFormatConverter_Initialize(This,pISource,dstFormat,dither,pIPalette,alphaThresholdPercent,paletteTranslate) (This)->lpVtbl->Initialize(This,pISource,dstFormat,dither,pIPalette,alphaThresholdPercent,paletteTranslate)
#define IWICFormatConverter_CanConvert(This,srcPixelFormat,dstPixelFormat,pfCanConvert) (This)->lpVtbl->CanConvert(This,srcPixelFormat,dstPixelFormat,pfCanConvert)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICFormatConverter_QueryInterface(IWICFormatConverter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICFormatConverter_AddRef(IWICFormatConverter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICFormatConverter_Release(IWICFormatConverter* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICFormatConverter_GetSize(IWICFormatConverter* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICFormatConverter_GetPixelFormat(IWICFormatConverter* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICFormatConverter_GetResolution(IWICFormatConverter* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICFormatConverter_CopyPalette(IWICFormatConverter* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICFormatConverter_CopyPixels(IWICFormatConverter* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
/*** IWICFormatConverter methods ***/
static inline HRESULT IWICFormatConverter_Initialize(IWICFormatConverter* This,IWICBitmapSource *pISource,REFWICPixelFormatGUID dstFormat,WICBitmapDitherType dither,IWICPalette *pIPalette,double alphaThresholdPercent,WICBitmapPaletteType paletteTranslate) {
    return This->lpVtbl->Initialize(This,pISource,dstFormat,dither,pIPalette,alphaThresholdPercent,paletteTranslate);
}
static inline HRESULT IWICFormatConverter_CanConvert(IWICFormatConverter* This,REFWICPixelFormatGUID srcPixelFormat,REFWICPixelFormatGUID dstPixelFormat,WINBOOL *pfCanConvert) {
    return This->lpVtbl->CanConvert(This,srcPixelFormat,dstPixelFormat,pfCanConvert);
}
#endif
#endif

#endif


#endif  /* __IWICFormatConverter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICFormatConverterInfo interface
 */
#ifndef __IWICFormatConverterInfo_INTERFACE_DEFINED__
#define __IWICFormatConverterInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICFormatConverterInfo, 0x9f34fb65, 0x13f4, 0x4f15, 0xbc,0x57, 0x37,0x26,0xb5,0xe5,0x3d,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9f34fb65-13f4-4f15-bc57-3726b5e53d9f")
IWICFormatConverterInfo : public IWICComponentInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetPixelFormats(
        UINT cFormats,
        WICPixelFormatGUID *pPixelFormatGUIDs,
        UINT *pcActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        IWICFormatConverter **ppIConverter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICFormatConverterInfo, 0x9f34fb65, 0x13f4, 0x4f15, 0xbc,0x57, 0x37,0x26,0xb5,0xe5,0x3d,0x9f)
#endif
#else
typedef struct IWICFormatConverterInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICFormatConverterInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICFormatConverterInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICFormatConverterInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICFormatConverterInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICFormatConverterInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICFormatConverterInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICFormatConverterInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICFormatConverterInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICFormatConverterInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICFormatConverterInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICFormatConverterInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICFormatConverterInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPixelFormats)(
        IWICFormatConverterInfo *This,
        UINT cFormats,
        WICPixelFormatGUID *pPixelFormatGUIDs,
        UINT *pcActual);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IWICFormatConverterInfo *This,
        IWICFormatConverter **ppIConverter);

    END_INTERFACE
} IWICFormatConverterInfoVtbl;

interface IWICFormatConverterInfo {
    CONST_VTBL IWICFormatConverterInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICFormatConverterInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICFormatConverterInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICFormatConverterInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICFormatConverterInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICFormatConverterInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICFormatConverterInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICFormatConverterInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICFormatConverterInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICFormatConverterInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICFormatConverterInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICFormatConverterInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICFormatConverterInfo methods ***/
#define IWICFormatConverterInfo_GetPixelFormats(This,cFormats,pPixelFormatGUIDs,pcActual) (This)->lpVtbl->GetPixelFormats(This,cFormats,pPixelFormatGUIDs,pcActual)
#define IWICFormatConverterInfo_CreateInstance(This,ppIConverter) (This)->lpVtbl->CreateInstance(This,ppIConverter)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICFormatConverterInfo_QueryInterface(IWICFormatConverterInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICFormatConverterInfo_AddRef(IWICFormatConverterInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICFormatConverterInfo_Release(IWICFormatConverterInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICFormatConverterInfo_GetComponentType(IWICFormatConverterInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICFormatConverterInfo_GetCLSID(IWICFormatConverterInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICFormatConverterInfo_GetSigningStatus(IWICFormatConverterInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICFormatConverterInfo_GetAuthor(IWICFormatConverterInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICFormatConverterInfo_GetVendorGUID(IWICFormatConverterInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICFormatConverterInfo_GetVersion(IWICFormatConverterInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICFormatConverterInfo_GetSpecVersion(IWICFormatConverterInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICFormatConverterInfo_GetFriendlyName(IWICFormatConverterInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICFormatConverterInfo methods ***/
static inline HRESULT IWICFormatConverterInfo_GetPixelFormats(IWICFormatConverterInfo* This,UINT cFormats,WICPixelFormatGUID *pPixelFormatGUIDs,UINT *pcActual) {
    return This->lpVtbl->GetPixelFormats(This,cFormats,pPixelFormatGUIDs,pcActual);
}
static inline HRESULT IWICFormatConverterInfo_CreateInstance(IWICFormatConverterInfo* This,IWICFormatConverter **ppIConverter) {
    return This->lpVtbl->CreateInstance(This,ppIConverter);
}
#endif
#endif

#endif


#endif  /* __IWICFormatConverterInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICStream interface
 */
#ifndef __IWICStream_INTERFACE_DEFINED__
#define __IWICStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICStream, 0x135ff860, 0x22b7, 0x4ddf, 0xb0,0xf6, 0x21,0x8f,0x4f,0x29,0x9a,0x43);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("135ff860-22b7-4ddf-b0f6-218f4f299a43")
IWICStream : public IStream
{
    virtual HRESULT STDMETHODCALLTYPE InitializeFromIStream(
        IStream *pIStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeFromFilename(
        LPCWSTR wzFileName,
        DWORD dwAccessMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeFromMemory(
        BYTE *pbBuffer,
        DWORD cbBufferSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeFromIStreamRegion(
        IStream *pIStream,
        ULARGE_INTEGER ulOffset,
        ULARGE_INTEGER ulMaxSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICStream, 0x135ff860, 0x22b7, 0x4ddf, 0xb0,0xf6, 0x21,0x8f,0x4f,0x29,0x9a,0x43)
#endif
#else
typedef struct IWICStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICStream *This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IWICStream *This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IWICStream *This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    /*** IStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Seek)(
        IWICStream *This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        IWICStream *This,
        ULARGE_INTEGER libNewSize);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        IWICStream *This,
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWICStream *This,
        DWORD grfCommitFlags);

    HRESULT (STDMETHODCALLTYPE *Revert)(
        IWICStream *This);

    HRESULT (STDMETHODCALLTYPE *LockRegion)(
        IWICStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *UnlockRegion)(
        IWICStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        IWICStream *This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWICStream *This,
        IStream **ppstm);

    /*** IWICStream methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializeFromIStream)(
        IWICStream *This,
        IStream *pIStream);

    HRESULT (STDMETHODCALLTYPE *InitializeFromFilename)(
        IWICStream *This,
        LPCWSTR wzFileName,
        DWORD dwAccessMode);

    HRESULT (STDMETHODCALLTYPE *InitializeFromMemory)(
        IWICStream *This,
        BYTE *pbBuffer,
        DWORD cbBufferSize);

    HRESULT (STDMETHODCALLTYPE *InitializeFromIStreamRegion)(
        IWICStream *This,
        IStream *pIStream,
        ULARGE_INTEGER ulOffset,
        ULARGE_INTEGER ulMaxSize);

    END_INTERFACE
} IWICStreamVtbl;

interface IWICStream {
    CONST_VTBL IWICStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define IWICStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IWICStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
/*** IStream methods ***/
#define IWICStream_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IWICStream_SetSize(This,libNewSize) (This)->lpVtbl->SetSize(This,libNewSize)
#define IWICStream_CopyTo(This,pstm,cb,pcbRead,pcbWritten) (This)->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten)
#define IWICStream_Commit(This,grfCommitFlags) (This)->lpVtbl->Commit(This,grfCommitFlags)
#define IWICStream_Revert(This) (This)->lpVtbl->Revert(This)
#define IWICStream_LockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->LockRegion(This,libOffset,cb,dwLockType)
#define IWICStream_UnlockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType)
#define IWICStream_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#define IWICStream_Clone(This,ppstm) (This)->lpVtbl->Clone(This,ppstm)
/*** IWICStream methods ***/
#define IWICStream_InitializeFromIStream(This,pIStream) (This)->lpVtbl->InitializeFromIStream(This,pIStream)
#define IWICStream_InitializeFromFilename(This,wzFileName,dwAccessMode) (This)->lpVtbl->InitializeFromFilename(This,wzFileName,dwAccessMode)
#define IWICStream_InitializeFromMemory(This,pbBuffer,cbBufferSize) (This)->lpVtbl->InitializeFromMemory(This,pbBuffer,cbBufferSize)
#define IWICStream_InitializeFromIStreamRegion(This,pIStream,ulOffset,ulMaxSize) (This)->lpVtbl->InitializeFromIStreamRegion(This,pIStream,ulOffset,ulMaxSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICStream_QueryInterface(IWICStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICStream_AddRef(IWICStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICStream_Release(IWICStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static inline HRESULT IWICStream_Read(IWICStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static inline HRESULT IWICStream_Write(IWICStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
/*** IStream methods ***/
static inline HRESULT IWICStream_Seek(IWICStream* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static inline HRESULT IWICStream_SetSize(IWICStream* This,ULARGE_INTEGER libNewSize) {
    return This->lpVtbl->SetSize(This,libNewSize);
}
static inline HRESULT IWICStream_CopyTo(IWICStream* This,IStream *pstm,ULARGE_INTEGER cb,ULARGE_INTEGER *pcbRead,ULARGE_INTEGER *pcbWritten) {
    return This->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten);
}
static inline HRESULT IWICStream_Commit(IWICStream* This,DWORD grfCommitFlags) {
    return This->lpVtbl->Commit(This,grfCommitFlags);
}
static inline HRESULT IWICStream_Revert(IWICStream* This) {
    return This->lpVtbl->Revert(This);
}
static inline HRESULT IWICStream_LockRegion(IWICStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->LockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IWICStream_UnlockRegion(IWICStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IWICStream_Stat(IWICStream* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
static inline HRESULT IWICStream_Clone(IWICStream* This,IStream **ppstm) {
    return This->lpVtbl->Clone(This,ppstm);
}
/*** IWICStream methods ***/
static inline HRESULT IWICStream_InitializeFromIStream(IWICStream* This,IStream *pIStream) {
    return This->lpVtbl->InitializeFromIStream(This,pIStream);
}
static inline HRESULT IWICStream_InitializeFromFilename(IWICStream* This,LPCWSTR wzFileName,DWORD dwAccessMode) {
    return This->lpVtbl->InitializeFromFilename(This,wzFileName,dwAccessMode);
}
static inline HRESULT IWICStream_InitializeFromMemory(IWICStream* This,BYTE *pbBuffer,DWORD cbBufferSize) {
    return This->lpVtbl->InitializeFromMemory(This,pbBuffer,cbBufferSize);
}
static inline HRESULT IWICStream_InitializeFromIStreamRegion(IWICStream* This,IStream *pIStream,ULARGE_INTEGER ulOffset,ULARGE_INTEGER ulMaxSize) {
    return This->lpVtbl->InitializeFromIStreamRegion(This,pIStream,ulOffset,ulMaxSize);
}
#endif
#endif

#endif


#endif  /* __IWICStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapScaler interface
 */
#ifndef __IWICBitmapScaler_INTERFACE_DEFINED__
#define __IWICBitmapScaler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapScaler, 0x00000302, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000302-a8f2-4877-ba0a-fd2b6645fb94")
IWICBitmapScaler : public IWICBitmapSource
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IWICBitmapSource *pISource,
        UINT uiWidth,
        UINT uiHeight,
        WICBitmapInterpolationMode mode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapScaler, 0x00000302, 0xa8f2, 0x4877, 0xba,0x0a, 0xfd,0x2b,0x66,0x45,0xfb,0x94)
#endif
#else
typedef struct IWICBitmapScalerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapScaler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapScaler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapScaler *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICBitmapScaler *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICBitmapScaler *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICBitmapScaler *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICBitmapScaler *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICBitmapScaler *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    /*** IWICBitmapScaler methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICBitmapScaler *This,
        IWICBitmapSource *pISource,
        UINT uiWidth,
        UINT uiHeight,
        WICBitmapInterpolationMode mode);

    END_INTERFACE
} IWICBitmapScalerVtbl;

interface IWICBitmapScaler {
    CONST_VTBL IWICBitmapScalerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapScaler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapScaler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapScaler_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICBitmapScaler_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICBitmapScaler_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICBitmapScaler_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICBitmapScaler_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICBitmapScaler_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
/*** IWICBitmapScaler methods ***/
#define IWICBitmapScaler_Initialize(This,pISource,uiWidth,uiHeight,mode) (This)->lpVtbl->Initialize(This,pISource,uiWidth,uiHeight,mode)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapScaler_QueryInterface(IWICBitmapScaler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapScaler_AddRef(IWICBitmapScaler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapScaler_Release(IWICBitmapScaler* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICBitmapScaler_GetSize(IWICBitmapScaler* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICBitmapScaler_GetPixelFormat(IWICBitmapScaler* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICBitmapScaler_GetResolution(IWICBitmapScaler* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICBitmapScaler_CopyPalette(IWICBitmapScaler* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapScaler_CopyPixels(IWICBitmapScaler* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
/*** IWICBitmapScaler methods ***/
static inline HRESULT IWICBitmapScaler_Initialize(IWICBitmapScaler* This,IWICBitmapSource *pISource,UINT uiWidth,UINT uiHeight,WICBitmapInterpolationMode mode) {
    return This->lpVtbl->Initialize(This,pISource,uiWidth,uiHeight,mode);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapScaler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICBitmapClipper interface
 */
#ifndef __IWICBitmapClipper_INTERFACE_DEFINED__
#define __IWICBitmapClipper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICBitmapClipper, 0xe4fbcf03, 0x223d, 0x4e81, 0x93,0x33, 0xd6,0x35,0x55,0x6d,0xd1,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e4fbcf03-223d-4e81-9333-d635556dd1b5")
IWICBitmapClipper : public IWICBitmapSource
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IWICBitmapSource *pISource,
        const WICRect *prc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICBitmapClipper, 0xe4fbcf03, 0x223d, 0x4e81, 0x93,0x33, 0xd6,0x35,0x55,0x6d,0xd1,0xb5)
#endif
#else
typedef struct IWICBitmapClipperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICBitmapClipper *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICBitmapClipper *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICBitmapClipper *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICBitmapClipper *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICBitmapClipper *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICBitmapClipper *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICBitmapClipper *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICBitmapClipper *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    /*** IWICBitmapClipper methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICBitmapClipper *This,
        IWICBitmapSource *pISource,
        const WICRect *prc);

    END_INTERFACE
} IWICBitmapClipperVtbl;

interface IWICBitmapClipper {
    CONST_VTBL IWICBitmapClipperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICBitmapClipper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICBitmapClipper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICBitmapClipper_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICBitmapClipper_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICBitmapClipper_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICBitmapClipper_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICBitmapClipper_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICBitmapClipper_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
/*** IWICBitmapClipper methods ***/
#define IWICBitmapClipper_Initialize(This,pISource,prc) (This)->lpVtbl->Initialize(This,pISource,prc)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICBitmapClipper_QueryInterface(IWICBitmapClipper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICBitmapClipper_AddRef(IWICBitmapClipper* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICBitmapClipper_Release(IWICBitmapClipper* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICBitmapClipper_GetSize(IWICBitmapClipper* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICBitmapClipper_GetPixelFormat(IWICBitmapClipper* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICBitmapClipper_GetResolution(IWICBitmapClipper* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICBitmapClipper_CopyPalette(IWICBitmapClipper* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICBitmapClipper_CopyPixels(IWICBitmapClipper* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
/*** IWICBitmapClipper methods ***/
static inline HRESULT IWICBitmapClipper_Initialize(IWICBitmapClipper* This,IWICBitmapSource *pISource,const WICRect *prc) {
    return This->lpVtbl->Initialize(This,pISource,prc);
}
#endif
#endif

#endif


#endif  /* __IWICBitmapClipper_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICColorTransform interface
 */
#ifndef __IWICColorTransform_INTERFACE_DEFINED__
#define __IWICColorTransform_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICColorTransform, 0xb66f034f, 0xd0e2, 0x40ab, 0xb4,0x36, 0x6d,0xe3,0x9e,0x32,0x1a,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b66f034f-d0e2-40ab-b436-6de39e321a94")
IWICColorTransform : public IWICBitmapSource
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IWICBitmapSource *pIBitmapSource,
        IWICColorContext *pIContextSource,
        IWICColorContext *pIContextDest,
        REFWICPixelFormatGUID pixelFmtDest) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICColorTransform, 0xb66f034f, 0xd0e2, 0x40ab, 0xb4,0x36, 0x6d,0xe3,0x9e,0x32,0x1a,0x94)
#endif
#else
typedef struct IWICColorTransformVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICColorTransform *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICColorTransform *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICColorTransform *This);

    /*** IWICBitmapSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IWICColorTransform *This,
        UINT *puiWidth,
        UINT *puiHeight);

    HRESULT (STDMETHODCALLTYPE *GetPixelFormat)(
        IWICColorTransform *This,
        WICPixelFormatGUID *pPixelFormat);

    HRESULT (STDMETHODCALLTYPE *GetResolution)(
        IWICColorTransform *This,
        double *pDpiX,
        double *pDpiY);

    HRESULT (STDMETHODCALLTYPE *CopyPalette)(
        IWICColorTransform *This,
        IWICPalette *pIPalette);

    HRESULT (STDMETHODCALLTYPE *CopyPixels)(
        IWICColorTransform *This,
        const WICRect *prc,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer);

    /*** IWICColorTransform methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICColorTransform *This,
        IWICBitmapSource *pIBitmapSource,
        IWICColorContext *pIContextSource,
        IWICColorContext *pIContextDest,
        REFWICPixelFormatGUID pixelFmtDest);

    END_INTERFACE
} IWICColorTransformVtbl;

interface IWICColorTransform {
    CONST_VTBL IWICColorTransformVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICColorTransform_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICColorTransform_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICColorTransform_Release(This) (This)->lpVtbl->Release(This)
/*** IWICBitmapSource methods ***/
#define IWICColorTransform_GetSize(This,puiWidth,puiHeight) (This)->lpVtbl->GetSize(This,puiWidth,puiHeight)
#define IWICColorTransform_GetPixelFormat(This,pPixelFormat) (This)->lpVtbl->GetPixelFormat(This,pPixelFormat)
#define IWICColorTransform_GetResolution(This,pDpiX,pDpiY) (This)->lpVtbl->GetResolution(This,pDpiX,pDpiY)
#define IWICColorTransform_CopyPalette(This,pIPalette) (This)->lpVtbl->CopyPalette(This,pIPalette)
#define IWICColorTransform_CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer) (This)->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer)
/*** IWICColorTransform methods ***/
#define IWICColorTransform_Initialize(This,pIBitmapSource,pIContextSource,pIContextDest,pixelFmtDest) (This)->lpVtbl->Initialize(This,pIBitmapSource,pIContextSource,pIContextDest,pixelFmtDest)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICColorTransform_QueryInterface(IWICColorTransform* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICColorTransform_AddRef(IWICColorTransform* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICColorTransform_Release(IWICColorTransform* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICBitmapSource methods ***/
static inline HRESULT IWICColorTransform_GetSize(IWICColorTransform* This,UINT *puiWidth,UINT *puiHeight) {
    return This->lpVtbl->GetSize(This,puiWidth,puiHeight);
}
static inline HRESULT IWICColorTransform_GetPixelFormat(IWICColorTransform* This,WICPixelFormatGUID *pPixelFormat) {
    return This->lpVtbl->GetPixelFormat(This,pPixelFormat);
}
static inline HRESULT IWICColorTransform_GetResolution(IWICColorTransform* This,double *pDpiX,double *pDpiY) {
    return This->lpVtbl->GetResolution(This,pDpiX,pDpiY);
}
static inline HRESULT IWICColorTransform_CopyPalette(IWICColorTransform* This,IWICPalette *pIPalette) {
    return This->lpVtbl->CopyPalette(This,pIPalette);
}
static inline HRESULT IWICColorTransform_CopyPixels(IWICColorTransform* This,const WICRect *prc,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer) {
    return This->lpVtbl->CopyPixels(This,prc,cbStride,cbBufferSize,pbBuffer);
}
/*** IWICColorTransform methods ***/
static inline HRESULT IWICColorTransform_Initialize(IWICColorTransform* This,IWICBitmapSource *pIBitmapSource,IWICColorContext *pIContextSource,IWICColorContext *pIContextDest,REFWICPixelFormatGUID pixelFmtDest) {
    return This->lpVtbl->Initialize(This,pIBitmapSource,pIContextSource,pIContextDest,pixelFmtDest);
}
#endif
#endif

#endif


#endif  /* __IWICColorTransform_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICFastMetadataEncoder interface
 */
#ifndef __IWICFastMetadataEncoder_INTERFACE_DEFINED__
#define __IWICFastMetadataEncoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICFastMetadataEncoder, 0xb84e2c09, 0x78c9, 0x4ac4, 0x8b,0xd3, 0x52,0x4a,0xe1,0x66,0x3a,0x2f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b84e2c09-78c9-4ac4-8bd3-524ae1663a2f")
IWICFastMetadataEncoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Commit(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadataQueryWriter(
        IWICMetadataQueryWriter **ppIMetadataQueryWriter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICFastMetadataEncoder, 0xb84e2c09, 0x78c9, 0x4ac4, 0x8b,0xd3, 0x52,0x4a,0xe1,0x66,0x3a,0x2f)
#endif
#else
typedef struct IWICFastMetadataEncoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICFastMetadataEncoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICFastMetadataEncoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICFastMetadataEncoder *This);

    /*** IWICFastMetadataEncoder methods ***/
    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWICFastMetadataEncoder *This);

    HRESULT (STDMETHODCALLTYPE *GetMetadataQueryWriter)(
        IWICFastMetadataEncoder *This,
        IWICMetadataQueryWriter **ppIMetadataQueryWriter);

    END_INTERFACE
} IWICFastMetadataEncoderVtbl;

interface IWICFastMetadataEncoder {
    CONST_VTBL IWICFastMetadataEncoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICFastMetadataEncoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICFastMetadataEncoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICFastMetadataEncoder_Release(This) (This)->lpVtbl->Release(This)
/*** IWICFastMetadataEncoder methods ***/
#define IWICFastMetadataEncoder_Commit(This) (This)->lpVtbl->Commit(This)
#define IWICFastMetadataEncoder_GetMetadataQueryWriter(This,ppIMetadataQueryWriter) (This)->lpVtbl->GetMetadataQueryWriter(This,ppIMetadataQueryWriter)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICFastMetadataEncoder_QueryInterface(IWICFastMetadataEncoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICFastMetadataEncoder_AddRef(IWICFastMetadataEncoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICFastMetadataEncoder_Release(IWICFastMetadataEncoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICFastMetadataEncoder methods ***/
static inline HRESULT IWICFastMetadataEncoder_Commit(IWICFastMetadataEncoder* This) {
    return This->lpVtbl->Commit(This);
}
static inline HRESULT IWICFastMetadataEncoder_GetMetadataQueryWriter(IWICFastMetadataEncoder* This,IWICMetadataQueryWriter **ppIMetadataQueryWriter) {
    return This->lpVtbl->GetMetadataQueryWriter(This,ppIMetadataQueryWriter);
}
#endif
#endif

#endif


#endif  /* __IWICFastMetadataEncoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICImageEncoder interface
 */
#ifndef __IWICImageEncoder_INTERFACE_DEFINED__
#define __IWICImageEncoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICImageEncoder, 0x04c75bf8, 0x3ce1, 0x473b, 0xac,0xc5, 0x3c,0xc4,0xf5,0xe9,0x49,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("04c75bf8-3ce1-473b-acc5-3cc4f5e94999")
IWICImageEncoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE WriteFrame(
        ID2D1Image *image,
        IWICBitmapFrameEncode *encode,
        const WICImageParameters *parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteFrameThumbnail(
        ID2D1Image *image,
        IWICBitmapFrameEncode *encode,
        const WICImageParameters *parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteThumbnail(
        ID2D1Image *image,
        IWICBitmapEncoder *encoder,
        const WICImageParameters *parameters) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICImageEncoder, 0x04c75bf8, 0x3ce1, 0x473b, 0xac,0xc5, 0x3c,0xc4,0xf5,0xe9,0x49,0x99)
#endif
#else
typedef struct IWICImageEncoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICImageEncoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICImageEncoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICImageEncoder *This);

    /*** IWICImageEncoder methods ***/
    HRESULT (STDMETHODCALLTYPE *WriteFrame)(
        IWICImageEncoder *This,
        ID2D1Image *image,
        IWICBitmapFrameEncode *encode,
        const WICImageParameters *parameters);

    HRESULT (STDMETHODCALLTYPE *WriteFrameThumbnail)(
        IWICImageEncoder *This,
        ID2D1Image *image,
        IWICBitmapFrameEncode *encode,
        const WICImageParameters *parameters);

    HRESULT (STDMETHODCALLTYPE *WriteThumbnail)(
        IWICImageEncoder *This,
        ID2D1Image *image,
        IWICBitmapEncoder *encoder,
        const WICImageParameters *parameters);

    END_INTERFACE
} IWICImageEncoderVtbl;

interface IWICImageEncoder {
    CONST_VTBL IWICImageEncoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICImageEncoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICImageEncoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICImageEncoder_Release(This) (This)->lpVtbl->Release(This)
/*** IWICImageEncoder methods ***/
#define IWICImageEncoder_WriteFrame(This,image,encode,parameters) (This)->lpVtbl->WriteFrame(This,image,encode,parameters)
#define IWICImageEncoder_WriteFrameThumbnail(This,image,encode,parameters) (This)->lpVtbl->WriteFrameThumbnail(This,image,encode,parameters)
#define IWICImageEncoder_WriteThumbnail(This,image,encoder,parameters) (This)->lpVtbl->WriteThumbnail(This,image,encoder,parameters)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICImageEncoder_QueryInterface(IWICImageEncoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICImageEncoder_AddRef(IWICImageEncoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICImageEncoder_Release(IWICImageEncoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICImageEncoder methods ***/
static inline HRESULT IWICImageEncoder_WriteFrame(IWICImageEncoder* This,ID2D1Image *image,IWICBitmapFrameEncode *encode,const WICImageParameters *parameters) {
    return This->lpVtbl->WriteFrame(This,image,encode,parameters);
}
static inline HRESULT IWICImageEncoder_WriteFrameThumbnail(IWICImageEncoder* This,ID2D1Image *image,IWICBitmapFrameEncode *encode,const WICImageParameters *parameters) {
    return This->lpVtbl->WriteFrameThumbnail(This,image,encode,parameters);
}
static inline HRESULT IWICImageEncoder_WriteThumbnail(IWICImageEncoder* This,ID2D1Image *image,IWICBitmapEncoder *encoder,const WICImageParameters *parameters) {
    return This->lpVtbl->WriteThumbnail(This,image,encoder,parameters);
}
#endif
#endif

#endif


#endif  /* __IWICImageEncoder_INTERFACE_DEFINED__ */

DEFINE_GUID(CLSID_WICImagingFactory,  0xcacaf262,0x9370,0x4615,0xa1,0x3b,0x9f,0x55,0x39,0xda,0x4c,0x0a);
DEFINE_GUID(CLSID_WICImagingFactory1, 0xcacaf262,0x9370,0x4615,0xa1,0x3b,0x9f,0x55,0x39,0xda,0x4c,0x0a);
DEFINE_GUID(CLSID_WICImagingFactory2, 0x317d06e8,0x5f24,0x433d,0xbd,0xf7,0x79,0xce,0x68,0xd8,0xab,0xc2);
/*****************************************************************************
 * IWICImagingFactory interface
 */
#ifndef __IWICImagingFactory_INTERFACE_DEFINED__
#define __IWICImagingFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICImagingFactory, 0xec5ec8a9, 0xc395, 0x4314, 0x9c,0x77, 0x54,0xd7,0xa9,0x35,0xff,0x70);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ec5ec8a9-c395-4314-9c77-54d7a935ff70")
IWICImagingFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateDecoderFromFilename(
        LPCWSTR wzFilename,
        const GUID *pguidVendor,
        DWORD dwDesiredAccess,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDecoderFromStream(
        IStream *pIStream,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDecoderFromFileHandle(
        ULONG_PTR hFile,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateComponentInfo(
        REFCLSID clsidComponent,
        IWICComponentInfo **ppIInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDecoder(
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapDecoder **ppIDecoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateEncoder(
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapEncoder **ppIEncoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePalette(
        IWICPalette **ppIPalette) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFormatConverter(
        IWICFormatConverter **ppIFormatConverter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapScaler(
        IWICBitmapScaler **ppIBitmapScaler) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapClipper(
        IWICBitmapClipper **ppIBitmapClipper) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapFlipRotator(
        IWICBitmapFlipRotator **ppIBitmapFlipRotator) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStream(
        IWICStream **ppIWICStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateColorContext(
        IWICColorContext **ppIWICColorContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateColorTransformer(
        IWICColorTransform **ppIWICColorTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmap(
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapFromSource(
        IWICBitmapSource *piBitmapSource,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapFromSourceRect(
        IWICBitmapSource *piBitmapSource,
        UINT x,
        UINT y,
        UINT width,
        UINT height,
        IWICBitmap **ppIBitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapFromMemory(
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer,
        IWICBitmap **ppIBitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapFromHBITMAP(
        HBITMAP hBitmap,
        HPALETTE hPalette,
        WICBitmapAlphaChannelOption options,
        IWICBitmap **ppIBitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBitmapFromHICON(
        HICON hIcon,
        IWICBitmap **ppIBitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateComponentEnumerator(
        DWORD componentTypes,
        DWORD options,
        IEnumUnknown **ppIEnumUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFastMetadataEncoderFromDecoder(
        IWICBitmapDecoder *pIDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFastMetadataEncoderFromFrameDecode(
        IWICBitmapFrameDecode *pIFrameDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQueryWriter(
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQueryWriterFromReader(
        IWICMetadataQueryReader *pIQueryReader,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICImagingFactory, 0xec5ec8a9, 0xc395, 0x4314, 0x9c,0x77, 0x54,0xd7,0xa9,0x35,0xff,0x70)
#endif
#else
typedef struct IWICImagingFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICImagingFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICImagingFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICImagingFactory *This);

    /*** IWICImagingFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromFilename)(
        IWICImagingFactory *This,
        LPCWSTR wzFilename,
        const GUID *pguidVendor,
        DWORD dwDesiredAccess,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromStream)(
        IWICImagingFactory *This,
        IStream *pIStream,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromFileHandle)(
        IWICImagingFactory *This,
        ULONG_PTR hFile,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateComponentInfo)(
        IWICImagingFactory *This,
        REFCLSID clsidComponent,
        IWICComponentInfo **ppIInfo);

    HRESULT (STDMETHODCALLTYPE *CreateDecoder)(
        IWICImagingFactory *This,
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateEncoder)(
        IWICImagingFactory *This,
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapEncoder **ppIEncoder);

    HRESULT (STDMETHODCALLTYPE *CreatePalette)(
        IWICImagingFactory *This,
        IWICPalette **ppIPalette);

    HRESULT (STDMETHODCALLTYPE *CreateFormatConverter)(
        IWICImagingFactory *This,
        IWICFormatConverter **ppIFormatConverter);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapScaler)(
        IWICImagingFactory *This,
        IWICBitmapScaler **ppIBitmapScaler);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapClipper)(
        IWICImagingFactory *This,
        IWICBitmapClipper **ppIBitmapClipper);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFlipRotator)(
        IWICImagingFactory *This,
        IWICBitmapFlipRotator **ppIBitmapFlipRotator);

    HRESULT (STDMETHODCALLTYPE *CreateStream)(
        IWICImagingFactory *This,
        IWICStream **ppIWICStream);

    HRESULT (STDMETHODCALLTYPE *CreateColorContext)(
        IWICImagingFactory *This,
        IWICColorContext **ppIWICColorContext);

    HRESULT (STDMETHODCALLTYPE *CreateColorTransformer)(
        IWICImagingFactory *This,
        IWICColorTransform **ppIWICColorTransform);

    HRESULT (STDMETHODCALLTYPE *CreateBitmap)(
        IWICImagingFactory *This,
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromSource)(
        IWICImagingFactory *This,
        IWICBitmapSource *piBitmapSource,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromSourceRect)(
        IWICImagingFactory *This,
        IWICBitmapSource *piBitmapSource,
        UINT x,
        UINT y,
        UINT width,
        UINT height,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromMemory)(
        IWICImagingFactory *This,
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromHBITMAP)(
        IWICImagingFactory *This,
        HBITMAP hBitmap,
        HPALETTE hPalette,
        WICBitmapAlphaChannelOption options,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromHICON)(
        IWICImagingFactory *This,
        HICON hIcon,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateComponentEnumerator)(
        IWICImagingFactory *This,
        DWORD componentTypes,
        DWORD options,
        IEnumUnknown **ppIEnumUnknown);

    HRESULT (STDMETHODCALLTYPE *CreateFastMetadataEncoderFromDecoder)(
        IWICImagingFactory *This,
        IWICBitmapDecoder *pIDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT (STDMETHODCALLTYPE *CreateFastMetadataEncoderFromFrameDecode)(
        IWICImagingFactory *This,
        IWICBitmapFrameDecode *pIFrameDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT (STDMETHODCALLTYPE *CreateQueryWriter)(
        IWICImagingFactory *This,
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter);

    HRESULT (STDMETHODCALLTYPE *CreateQueryWriterFromReader)(
        IWICImagingFactory *This,
        IWICMetadataQueryReader *pIQueryReader,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter);

    END_INTERFACE
} IWICImagingFactoryVtbl;

interface IWICImagingFactory {
    CONST_VTBL IWICImagingFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICImagingFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICImagingFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICImagingFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IWICImagingFactory methods ***/
#define IWICImagingFactory_CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder)
#define IWICImagingFactory_CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder)
#define IWICImagingFactory_CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder)
#define IWICImagingFactory_CreateComponentInfo(This,clsidComponent,ppIInfo) (This)->lpVtbl->CreateComponentInfo(This,clsidComponent,ppIInfo)
#define IWICImagingFactory_CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder) (This)->lpVtbl->CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder)
#define IWICImagingFactory_CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder) (This)->lpVtbl->CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder)
#define IWICImagingFactory_CreatePalette(This,ppIPalette) (This)->lpVtbl->CreatePalette(This,ppIPalette)
#define IWICImagingFactory_CreateFormatConverter(This,ppIFormatConverter) (This)->lpVtbl->CreateFormatConverter(This,ppIFormatConverter)
#define IWICImagingFactory_CreateBitmapScaler(This,ppIBitmapScaler) (This)->lpVtbl->CreateBitmapScaler(This,ppIBitmapScaler)
#define IWICImagingFactory_CreateBitmapClipper(This,ppIBitmapClipper) (This)->lpVtbl->CreateBitmapClipper(This,ppIBitmapClipper)
#define IWICImagingFactory_CreateBitmapFlipRotator(This,ppIBitmapFlipRotator) (This)->lpVtbl->CreateBitmapFlipRotator(This,ppIBitmapFlipRotator)
#define IWICImagingFactory_CreateStream(This,ppIWICStream) (This)->lpVtbl->CreateStream(This,ppIWICStream)
#define IWICImagingFactory_CreateColorContext(This,ppIWICColorContext) (This)->lpVtbl->CreateColorContext(This,ppIWICColorContext)
#define IWICImagingFactory_CreateColorTransformer(This,ppIWICColorTransform) (This)->lpVtbl->CreateColorTransformer(This,ppIWICColorTransform)
#define IWICImagingFactory_CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap) (This)->lpVtbl->CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap)
#define IWICImagingFactory_CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap) (This)->lpVtbl->CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap)
#define IWICImagingFactory_CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap) (This)->lpVtbl->CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap)
#define IWICImagingFactory_CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap) (This)->lpVtbl->CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap)
#define IWICImagingFactory_CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap) (This)->lpVtbl->CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap)
#define IWICImagingFactory_CreateBitmapFromHICON(This,hIcon,ppIBitmap) (This)->lpVtbl->CreateBitmapFromHICON(This,hIcon,ppIBitmap)
#define IWICImagingFactory_CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown) (This)->lpVtbl->CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown)
#define IWICImagingFactory_CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder) (This)->lpVtbl->CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder)
#define IWICImagingFactory_CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder) (This)->lpVtbl->CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder)
#define IWICImagingFactory_CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter) (This)->lpVtbl->CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter)
#define IWICImagingFactory_CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter) (This)->lpVtbl->CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICImagingFactory_QueryInterface(IWICImagingFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICImagingFactory_AddRef(IWICImagingFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICImagingFactory_Release(IWICImagingFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICImagingFactory methods ***/
static inline HRESULT IWICImagingFactory_CreateDecoderFromFilename(IWICImagingFactory* This,LPCWSTR wzFilename,const GUID *pguidVendor,DWORD dwDesiredAccess,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICImagingFactory_CreateDecoderFromStream(IWICImagingFactory* This,IStream *pIStream,const GUID *pguidVendor,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICImagingFactory_CreateDecoderFromFileHandle(IWICImagingFactory* This,ULONG_PTR hFile,const GUID *pguidVendor,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICImagingFactory_CreateComponentInfo(IWICImagingFactory* This,REFCLSID clsidComponent,IWICComponentInfo **ppIInfo) {
    return This->lpVtbl->CreateComponentInfo(This,clsidComponent,ppIInfo);
}
static inline HRESULT IWICImagingFactory_CreateDecoder(IWICImagingFactory* This,REFGUID guidContainerFormat,const GUID *pguidVendor,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder);
}
static inline HRESULT IWICImagingFactory_CreateEncoder(IWICImagingFactory* This,REFGUID guidContainerFormat,const GUID *pguidVendor,IWICBitmapEncoder **ppIEncoder) {
    return This->lpVtbl->CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder);
}
static inline HRESULT IWICImagingFactory_CreatePalette(IWICImagingFactory* This,IWICPalette **ppIPalette) {
    return This->lpVtbl->CreatePalette(This,ppIPalette);
}
static inline HRESULT IWICImagingFactory_CreateFormatConverter(IWICImagingFactory* This,IWICFormatConverter **ppIFormatConverter) {
    return This->lpVtbl->CreateFormatConverter(This,ppIFormatConverter);
}
static inline HRESULT IWICImagingFactory_CreateBitmapScaler(IWICImagingFactory* This,IWICBitmapScaler **ppIBitmapScaler) {
    return This->lpVtbl->CreateBitmapScaler(This,ppIBitmapScaler);
}
static inline HRESULT IWICImagingFactory_CreateBitmapClipper(IWICImagingFactory* This,IWICBitmapClipper **ppIBitmapClipper) {
    return This->lpVtbl->CreateBitmapClipper(This,ppIBitmapClipper);
}
static inline HRESULT IWICImagingFactory_CreateBitmapFlipRotator(IWICImagingFactory* This,IWICBitmapFlipRotator **ppIBitmapFlipRotator) {
    return This->lpVtbl->CreateBitmapFlipRotator(This,ppIBitmapFlipRotator);
}
static inline HRESULT IWICImagingFactory_CreateStream(IWICImagingFactory* This,IWICStream **ppIWICStream) {
    return This->lpVtbl->CreateStream(This,ppIWICStream);
}
static inline HRESULT IWICImagingFactory_CreateColorContext(IWICImagingFactory* This,IWICColorContext **ppIWICColorContext) {
    return This->lpVtbl->CreateColorContext(This,ppIWICColorContext);
}
static inline HRESULT IWICImagingFactory_CreateColorTransformer(IWICImagingFactory* This,IWICColorTransform **ppIWICColorTransform) {
    return This->lpVtbl->CreateColorTransformer(This,ppIWICColorTransform);
}
static inline HRESULT IWICImagingFactory_CreateBitmap(IWICImagingFactory* This,UINT uiWidth,UINT uiHeight,REFWICPixelFormatGUID pixelFormat,WICBitmapCreateCacheOption option,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap);
}
static inline HRESULT IWICImagingFactory_CreateBitmapFromSource(IWICImagingFactory* This,IWICBitmapSource *piBitmapSource,WICBitmapCreateCacheOption option,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap);
}
static inline HRESULT IWICImagingFactory_CreateBitmapFromSourceRect(IWICImagingFactory* This,IWICBitmapSource *piBitmapSource,UINT x,UINT y,UINT width,UINT height,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap);
}
static inline HRESULT IWICImagingFactory_CreateBitmapFromMemory(IWICImagingFactory* This,UINT uiWidth,UINT uiHeight,REFWICPixelFormatGUID pixelFormat,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap);
}
static inline HRESULT IWICImagingFactory_CreateBitmapFromHBITMAP(IWICImagingFactory* This,HBITMAP hBitmap,HPALETTE hPalette,WICBitmapAlphaChannelOption options,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap);
}
static inline HRESULT IWICImagingFactory_CreateBitmapFromHICON(IWICImagingFactory* This,HICON hIcon,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromHICON(This,hIcon,ppIBitmap);
}
static inline HRESULT IWICImagingFactory_CreateComponentEnumerator(IWICImagingFactory* This,DWORD componentTypes,DWORD options,IEnumUnknown **ppIEnumUnknown) {
    return This->lpVtbl->CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown);
}
static inline HRESULT IWICImagingFactory_CreateFastMetadataEncoderFromDecoder(IWICImagingFactory* This,IWICBitmapDecoder *pIDecoder,IWICFastMetadataEncoder **ppIFastEncoder) {
    return This->lpVtbl->CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder);
}
static inline HRESULT IWICImagingFactory_CreateFastMetadataEncoderFromFrameDecode(IWICImagingFactory* This,IWICBitmapFrameDecode *pIFrameDecoder,IWICFastMetadataEncoder **ppIFastEncoder) {
    return This->lpVtbl->CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder);
}
static inline HRESULT IWICImagingFactory_CreateQueryWriter(IWICImagingFactory* This,REFGUID guidMetadataFormat,const GUID *pguidVendor,IWICMetadataQueryWriter **ppIQueryWriter) {
    return This->lpVtbl->CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter);
}
static inline HRESULT IWICImagingFactory_CreateQueryWriterFromReader(IWICImagingFactory* This,IWICMetadataQueryReader *pIQueryReader,const GUID *pguidVendor,IWICMetadataQueryWriter **ppIQueryWriter) {
    return This->lpVtbl->CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter);
}
#endif
#endif

#endif


#endif  /* __IWICImagingFactory_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICImagingFactory2 interface
 */
#ifndef __IWICImagingFactory2_INTERFACE_DEFINED__
#define __IWICImagingFactory2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICImagingFactory2, 0x7b816b45, 0x1996, 0x4476, 0xb1,0x32, 0xde,0x9e,0x24,0x7c,0x8a,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7b816b45-1996-4476-b132-de9e247c8af0")
IWICImagingFactory2 : public IWICImagingFactory
{
    virtual HRESULT STDMETHODCALLTYPE CreateImageEncoder(
        ID2D1Device *device,
        IWICImageEncoder **encoder) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICImagingFactory2, 0x7b816b45, 0x1996, 0x4476, 0xb1,0x32, 0xde,0x9e,0x24,0x7c,0x8a,0xf0)
#endif
#else
typedef struct IWICImagingFactory2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICImagingFactory2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICImagingFactory2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICImagingFactory2 *This);

    /*** IWICImagingFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromFilename)(
        IWICImagingFactory2 *This,
        LPCWSTR wzFilename,
        const GUID *pguidVendor,
        DWORD dwDesiredAccess,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromStream)(
        IWICImagingFactory2 *This,
        IStream *pIStream,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromFileHandle)(
        IWICImagingFactory2 *This,
        ULONG_PTR hFile,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateComponentInfo)(
        IWICImagingFactory2 *This,
        REFCLSID clsidComponent,
        IWICComponentInfo **ppIInfo);

    HRESULT (STDMETHODCALLTYPE *CreateDecoder)(
        IWICImagingFactory2 *This,
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateEncoder)(
        IWICImagingFactory2 *This,
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapEncoder **ppIEncoder);

    HRESULT (STDMETHODCALLTYPE *CreatePalette)(
        IWICImagingFactory2 *This,
        IWICPalette **ppIPalette);

    HRESULT (STDMETHODCALLTYPE *CreateFormatConverter)(
        IWICImagingFactory2 *This,
        IWICFormatConverter **ppIFormatConverter);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapScaler)(
        IWICImagingFactory2 *This,
        IWICBitmapScaler **ppIBitmapScaler);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapClipper)(
        IWICImagingFactory2 *This,
        IWICBitmapClipper **ppIBitmapClipper);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFlipRotator)(
        IWICImagingFactory2 *This,
        IWICBitmapFlipRotator **ppIBitmapFlipRotator);

    HRESULT (STDMETHODCALLTYPE *CreateStream)(
        IWICImagingFactory2 *This,
        IWICStream **ppIWICStream);

    HRESULT (STDMETHODCALLTYPE *CreateColorContext)(
        IWICImagingFactory2 *This,
        IWICColorContext **ppIWICColorContext);

    HRESULT (STDMETHODCALLTYPE *CreateColorTransformer)(
        IWICImagingFactory2 *This,
        IWICColorTransform **ppIWICColorTransform);

    HRESULT (STDMETHODCALLTYPE *CreateBitmap)(
        IWICImagingFactory2 *This,
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromSource)(
        IWICImagingFactory2 *This,
        IWICBitmapSource *piBitmapSource,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromSourceRect)(
        IWICImagingFactory2 *This,
        IWICBitmapSource *piBitmapSource,
        UINT x,
        UINT y,
        UINT width,
        UINT height,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromMemory)(
        IWICImagingFactory2 *This,
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromHBITMAP)(
        IWICImagingFactory2 *This,
        HBITMAP hBitmap,
        HPALETTE hPalette,
        WICBitmapAlphaChannelOption options,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromHICON)(
        IWICImagingFactory2 *This,
        HICON hIcon,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateComponentEnumerator)(
        IWICImagingFactory2 *This,
        DWORD componentTypes,
        DWORD options,
        IEnumUnknown **ppIEnumUnknown);

    HRESULT (STDMETHODCALLTYPE *CreateFastMetadataEncoderFromDecoder)(
        IWICImagingFactory2 *This,
        IWICBitmapDecoder *pIDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT (STDMETHODCALLTYPE *CreateFastMetadataEncoderFromFrameDecode)(
        IWICImagingFactory2 *This,
        IWICBitmapFrameDecode *pIFrameDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT (STDMETHODCALLTYPE *CreateQueryWriter)(
        IWICImagingFactory2 *This,
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter);

    HRESULT (STDMETHODCALLTYPE *CreateQueryWriterFromReader)(
        IWICImagingFactory2 *This,
        IWICMetadataQueryReader *pIQueryReader,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter);

    /*** IWICImagingFactory2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateImageEncoder)(
        IWICImagingFactory2 *This,
        ID2D1Device *device,
        IWICImageEncoder **encoder);

    END_INTERFACE
} IWICImagingFactory2Vtbl;

interface IWICImagingFactory2 {
    CONST_VTBL IWICImagingFactory2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICImagingFactory2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICImagingFactory2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICImagingFactory2_Release(This) (This)->lpVtbl->Release(This)
/*** IWICImagingFactory methods ***/
#define IWICImagingFactory2_CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder)
#define IWICImagingFactory2_CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder)
#define IWICImagingFactory2_CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder)
#define IWICImagingFactory2_CreateComponentInfo(This,clsidComponent,ppIInfo) (This)->lpVtbl->CreateComponentInfo(This,clsidComponent,ppIInfo)
#define IWICImagingFactory2_CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder) (This)->lpVtbl->CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder)
#define IWICImagingFactory2_CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder) (This)->lpVtbl->CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder)
#define IWICImagingFactory2_CreatePalette(This,ppIPalette) (This)->lpVtbl->CreatePalette(This,ppIPalette)
#define IWICImagingFactory2_CreateFormatConverter(This,ppIFormatConverter) (This)->lpVtbl->CreateFormatConverter(This,ppIFormatConverter)
#define IWICImagingFactory2_CreateBitmapScaler(This,ppIBitmapScaler) (This)->lpVtbl->CreateBitmapScaler(This,ppIBitmapScaler)
#define IWICImagingFactory2_CreateBitmapClipper(This,ppIBitmapClipper) (This)->lpVtbl->CreateBitmapClipper(This,ppIBitmapClipper)
#define IWICImagingFactory2_CreateBitmapFlipRotator(This,ppIBitmapFlipRotator) (This)->lpVtbl->CreateBitmapFlipRotator(This,ppIBitmapFlipRotator)
#define IWICImagingFactory2_CreateStream(This,ppIWICStream) (This)->lpVtbl->CreateStream(This,ppIWICStream)
#define IWICImagingFactory2_CreateColorContext(This,ppIWICColorContext) (This)->lpVtbl->CreateColorContext(This,ppIWICColorContext)
#define IWICImagingFactory2_CreateColorTransformer(This,ppIWICColorTransform) (This)->lpVtbl->CreateColorTransformer(This,ppIWICColorTransform)
#define IWICImagingFactory2_CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap) (This)->lpVtbl->CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap)
#define IWICImagingFactory2_CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap) (This)->lpVtbl->CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap)
#define IWICImagingFactory2_CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap) (This)->lpVtbl->CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap)
#define IWICImagingFactory2_CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap) (This)->lpVtbl->CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap)
#define IWICImagingFactory2_CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap) (This)->lpVtbl->CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap)
#define IWICImagingFactory2_CreateBitmapFromHICON(This,hIcon,ppIBitmap) (This)->lpVtbl->CreateBitmapFromHICON(This,hIcon,ppIBitmap)
#define IWICImagingFactory2_CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown) (This)->lpVtbl->CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown)
#define IWICImagingFactory2_CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder) (This)->lpVtbl->CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder)
#define IWICImagingFactory2_CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder) (This)->lpVtbl->CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder)
#define IWICImagingFactory2_CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter) (This)->lpVtbl->CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter)
#define IWICImagingFactory2_CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter) (This)->lpVtbl->CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter)
/*** IWICImagingFactory2 methods ***/
#define IWICImagingFactory2_CreateImageEncoder(This,device,encoder) (This)->lpVtbl->CreateImageEncoder(This,device,encoder)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICImagingFactory2_QueryInterface(IWICImagingFactory2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICImagingFactory2_AddRef(IWICImagingFactory2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICImagingFactory2_Release(IWICImagingFactory2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICImagingFactory methods ***/
static inline HRESULT IWICImagingFactory2_CreateDecoderFromFilename(IWICImagingFactory2* This,LPCWSTR wzFilename,const GUID *pguidVendor,DWORD dwDesiredAccess,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICImagingFactory2_CreateDecoderFromStream(IWICImagingFactory2* This,IStream *pIStream,const GUID *pguidVendor,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICImagingFactory2_CreateDecoderFromFileHandle(IWICImagingFactory2* This,ULONG_PTR hFile,const GUID *pguidVendor,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICImagingFactory2_CreateComponentInfo(IWICImagingFactory2* This,REFCLSID clsidComponent,IWICComponentInfo **ppIInfo) {
    return This->lpVtbl->CreateComponentInfo(This,clsidComponent,ppIInfo);
}
static inline HRESULT IWICImagingFactory2_CreateDecoder(IWICImagingFactory2* This,REFGUID guidContainerFormat,const GUID *pguidVendor,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder);
}
static inline HRESULT IWICImagingFactory2_CreateEncoder(IWICImagingFactory2* This,REFGUID guidContainerFormat,const GUID *pguidVendor,IWICBitmapEncoder **ppIEncoder) {
    return This->lpVtbl->CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder);
}
static inline HRESULT IWICImagingFactory2_CreatePalette(IWICImagingFactory2* This,IWICPalette **ppIPalette) {
    return This->lpVtbl->CreatePalette(This,ppIPalette);
}
static inline HRESULT IWICImagingFactory2_CreateFormatConverter(IWICImagingFactory2* This,IWICFormatConverter **ppIFormatConverter) {
    return This->lpVtbl->CreateFormatConverter(This,ppIFormatConverter);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapScaler(IWICImagingFactory2* This,IWICBitmapScaler **ppIBitmapScaler) {
    return This->lpVtbl->CreateBitmapScaler(This,ppIBitmapScaler);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapClipper(IWICImagingFactory2* This,IWICBitmapClipper **ppIBitmapClipper) {
    return This->lpVtbl->CreateBitmapClipper(This,ppIBitmapClipper);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapFlipRotator(IWICImagingFactory2* This,IWICBitmapFlipRotator **ppIBitmapFlipRotator) {
    return This->lpVtbl->CreateBitmapFlipRotator(This,ppIBitmapFlipRotator);
}
static inline HRESULT IWICImagingFactory2_CreateStream(IWICImagingFactory2* This,IWICStream **ppIWICStream) {
    return This->lpVtbl->CreateStream(This,ppIWICStream);
}
static inline HRESULT IWICImagingFactory2_CreateColorContext(IWICImagingFactory2* This,IWICColorContext **ppIWICColorContext) {
    return This->lpVtbl->CreateColorContext(This,ppIWICColorContext);
}
static inline HRESULT IWICImagingFactory2_CreateColorTransformer(IWICImagingFactory2* This,IWICColorTransform **ppIWICColorTransform) {
    return This->lpVtbl->CreateColorTransformer(This,ppIWICColorTransform);
}
static inline HRESULT IWICImagingFactory2_CreateBitmap(IWICImagingFactory2* This,UINT uiWidth,UINT uiHeight,REFWICPixelFormatGUID pixelFormat,WICBitmapCreateCacheOption option,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapFromSource(IWICImagingFactory2* This,IWICBitmapSource *piBitmapSource,WICBitmapCreateCacheOption option,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapFromSourceRect(IWICImagingFactory2* This,IWICBitmapSource *piBitmapSource,UINT x,UINT y,UINT width,UINT height,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapFromMemory(IWICImagingFactory2* This,UINT uiWidth,UINT uiHeight,REFWICPixelFormatGUID pixelFormat,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapFromHBITMAP(IWICImagingFactory2* This,HBITMAP hBitmap,HPALETTE hPalette,WICBitmapAlphaChannelOption options,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap);
}
static inline HRESULT IWICImagingFactory2_CreateBitmapFromHICON(IWICImagingFactory2* This,HICON hIcon,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromHICON(This,hIcon,ppIBitmap);
}
static inline HRESULT IWICImagingFactory2_CreateComponentEnumerator(IWICImagingFactory2* This,DWORD componentTypes,DWORD options,IEnumUnknown **ppIEnumUnknown) {
    return This->lpVtbl->CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown);
}
static inline HRESULT IWICImagingFactory2_CreateFastMetadataEncoderFromDecoder(IWICImagingFactory2* This,IWICBitmapDecoder *pIDecoder,IWICFastMetadataEncoder **ppIFastEncoder) {
    return This->lpVtbl->CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder);
}
static inline HRESULT IWICImagingFactory2_CreateFastMetadataEncoderFromFrameDecode(IWICImagingFactory2* This,IWICBitmapFrameDecode *pIFrameDecoder,IWICFastMetadataEncoder **ppIFastEncoder) {
    return This->lpVtbl->CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder);
}
static inline HRESULT IWICImagingFactory2_CreateQueryWriter(IWICImagingFactory2* This,REFGUID guidMetadataFormat,const GUID *pguidVendor,IWICMetadataQueryWriter **ppIQueryWriter) {
    return This->lpVtbl->CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter);
}
static inline HRESULT IWICImagingFactory2_CreateQueryWriterFromReader(IWICImagingFactory2* This,IWICMetadataQueryReader *pIQueryReader,const GUID *pguidVendor,IWICMetadataQueryWriter **ppIQueryWriter) {
    return This->lpVtbl->CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter);
}
/*** IWICImagingFactory2 methods ***/
static inline HRESULT IWICImagingFactory2_CreateImageEncoder(IWICImagingFactory2* This,ID2D1Device *device,IWICImageEncoder **encoder) {
    return This->lpVtbl->CreateImageEncoder(This,device,encoder);
}
#endif
#endif

#endif


#endif  /* __IWICImagingFactory2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICEnumMetadataItem interface
 */
#ifndef __IWICEnumMetadataItem_INTERFACE_DEFINED__
#define __IWICEnumMetadataItem_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICEnumMetadataItem, 0xdc2bb46d, 0x3f07, 0x481e, 0x86,0x25, 0x22,0x0c,0x4a,0xed,0xbb,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dc2bb46d-3f07-481e-8625-220c4aedbb33")
IWICEnumMetadataItem : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        PROPVARIANT *rgeltSchema,
        PROPVARIANT *rgeltId,
        PROPVARIANT *rgeltValue,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IWICEnumMetadataItem **ppIEnumMetadataItem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICEnumMetadataItem, 0xdc2bb46d, 0x3f07, 0x481e, 0x86,0x25, 0x22,0x0c,0x4a,0xed,0xbb,0x33)
#endif
#else
typedef struct IWICEnumMetadataItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICEnumMetadataItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICEnumMetadataItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICEnumMetadataItem *This);

    /*** IWICEnumMetadataItem methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IWICEnumMetadataItem *This,
        ULONG celt,
        PROPVARIANT *rgeltSchema,
        PROPVARIANT *rgeltId,
        PROPVARIANT *rgeltValue,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IWICEnumMetadataItem *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IWICEnumMetadataItem *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWICEnumMetadataItem *This,
        IWICEnumMetadataItem **ppIEnumMetadataItem);

    END_INTERFACE
} IWICEnumMetadataItemVtbl;

interface IWICEnumMetadataItem {
    CONST_VTBL IWICEnumMetadataItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICEnumMetadataItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICEnumMetadataItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICEnumMetadataItem_Release(This) (This)->lpVtbl->Release(This)
/*** IWICEnumMetadataItem methods ***/
#define IWICEnumMetadataItem_Next(This,celt,rgeltSchema,rgeltId,rgeltValue,pceltFetched) (This)->lpVtbl->Next(This,celt,rgeltSchema,rgeltId,rgeltValue,pceltFetched)
#define IWICEnumMetadataItem_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IWICEnumMetadataItem_Reset(This) (This)->lpVtbl->Reset(This)
#define IWICEnumMetadataItem_Clone(This,ppIEnumMetadataItem) (This)->lpVtbl->Clone(This,ppIEnumMetadataItem)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICEnumMetadataItem_QueryInterface(IWICEnumMetadataItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICEnumMetadataItem_AddRef(IWICEnumMetadataItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICEnumMetadataItem_Release(IWICEnumMetadataItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICEnumMetadataItem methods ***/
static inline HRESULT IWICEnumMetadataItem_Next(IWICEnumMetadataItem* This,ULONG celt,PROPVARIANT *rgeltSchema,PROPVARIANT *rgeltId,PROPVARIANT *rgeltValue,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgeltSchema,rgeltId,rgeltValue,pceltFetched);
}
static inline HRESULT IWICEnumMetadataItem_Skip(IWICEnumMetadataItem* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IWICEnumMetadataItem_Reset(IWICEnumMetadataItem* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IWICEnumMetadataItem_Clone(IWICEnumMetadataItem* This,IWICEnumMetadataItem **ppIEnumMetadataItem) {
    return This->lpVtbl->Clone(This,ppIEnumMetadataItem);
}
#endif
#endif

#endif


#endif  /* __IWICEnumMetadataItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICDdsDecoder interface
 */
#ifndef __IWICDdsDecoder_INTERFACE_DEFINED__
#define __IWICDdsDecoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICDdsDecoder, 0x409cd537, 0x8532, 0x40cb, 0x97,0x74, 0xe2,0xfe,0xb2,0xdf,0x4e,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("409cd537-8532-40cb-9774-e2feb2df4e9c")
IWICDdsDecoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetParameters(
        WICDdsParameters *parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrame(
        UINT arrayIndex,
        UINT mipLevel,
        UINT sliceIndex,
        IWICBitmapFrameDecode **bitmapFrame) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICDdsDecoder, 0x409cd537, 0x8532, 0x40cb, 0x97,0x74, 0xe2,0xfe,0xb2,0xdf,0x4e,0x9c)
#endif
#else
typedef struct IWICDdsDecoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICDdsDecoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICDdsDecoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICDdsDecoder *This);

    /*** IWICDdsDecoder methods ***/
    HRESULT (STDMETHODCALLTYPE *GetParameters)(
        IWICDdsDecoder *This,
        WICDdsParameters *parameters);

    HRESULT (STDMETHODCALLTYPE *GetFrame)(
        IWICDdsDecoder *This,
        UINT arrayIndex,
        UINT mipLevel,
        UINT sliceIndex,
        IWICBitmapFrameDecode **bitmapFrame);

    END_INTERFACE
} IWICDdsDecoderVtbl;

interface IWICDdsDecoder {
    CONST_VTBL IWICDdsDecoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICDdsDecoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICDdsDecoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICDdsDecoder_Release(This) (This)->lpVtbl->Release(This)
/*** IWICDdsDecoder methods ***/
#define IWICDdsDecoder_GetParameters(This,parameters) (This)->lpVtbl->GetParameters(This,parameters)
#define IWICDdsDecoder_GetFrame(This,arrayIndex,mipLevel,sliceIndex,bitmapFrame) (This)->lpVtbl->GetFrame(This,arrayIndex,mipLevel,sliceIndex,bitmapFrame)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICDdsDecoder_QueryInterface(IWICDdsDecoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICDdsDecoder_AddRef(IWICDdsDecoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICDdsDecoder_Release(IWICDdsDecoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICDdsDecoder methods ***/
static inline HRESULT IWICDdsDecoder_GetParameters(IWICDdsDecoder* This,WICDdsParameters *parameters) {
    return This->lpVtbl->GetParameters(This,parameters);
}
static inline HRESULT IWICDdsDecoder_GetFrame(IWICDdsDecoder* This,UINT arrayIndex,UINT mipLevel,UINT sliceIndex,IWICBitmapFrameDecode **bitmapFrame) {
    return This->lpVtbl->GetFrame(This,arrayIndex,mipLevel,sliceIndex,bitmapFrame);
}
#endif
#endif

#endif


#endif  /* __IWICDdsDecoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICDdsEncoder interface
 */
#ifndef __IWICDdsEncoder_INTERFACE_DEFINED__
#define __IWICDdsEncoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICDdsEncoder, 0x5cacdb4c, 0x407e, 0x41b3, 0xb9,0x36, 0xd0,0xf0,0x10,0xcd,0x67,0x32);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5cacdb4c-407e-41b3-b936-d0f010cd6732")
IWICDdsEncoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetParameters(
        WICDdsParameters *parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParameters(
        WICDdsParameters *parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNewFrame(
        IWICBitmapFrameEncode **frameEncode,
        UINT *arrayIndex,
        UINT *mipLevel,
        UINT *sliceIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICDdsEncoder, 0x5cacdb4c, 0x407e, 0x41b3, 0xb9,0x36, 0xd0,0xf0,0x10,0xcd,0x67,0x32)
#endif
#else
typedef struct IWICDdsEncoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICDdsEncoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICDdsEncoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICDdsEncoder *This);

    /*** IWICDdsEncoder methods ***/
    HRESULT (STDMETHODCALLTYPE *SetParameters)(
        IWICDdsEncoder *This,
        WICDdsParameters *parameters);

    HRESULT (STDMETHODCALLTYPE *GetParameters)(
        IWICDdsEncoder *This,
        WICDdsParameters *parameters);

    HRESULT (STDMETHODCALLTYPE *CreateNewFrame)(
        IWICDdsEncoder *This,
        IWICBitmapFrameEncode **frameEncode,
        UINT *arrayIndex,
        UINT *mipLevel,
        UINT *sliceIndex);

    END_INTERFACE
} IWICDdsEncoderVtbl;

interface IWICDdsEncoder {
    CONST_VTBL IWICDdsEncoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICDdsEncoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICDdsEncoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICDdsEncoder_Release(This) (This)->lpVtbl->Release(This)
/*** IWICDdsEncoder methods ***/
#define IWICDdsEncoder_SetParameters(This,parameters) (This)->lpVtbl->SetParameters(This,parameters)
#define IWICDdsEncoder_GetParameters(This,parameters) (This)->lpVtbl->GetParameters(This,parameters)
#define IWICDdsEncoder_CreateNewFrame(This,frameEncode,arrayIndex,mipLevel,sliceIndex) (This)->lpVtbl->CreateNewFrame(This,frameEncode,arrayIndex,mipLevel,sliceIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICDdsEncoder_QueryInterface(IWICDdsEncoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICDdsEncoder_AddRef(IWICDdsEncoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICDdsEncoder_Release(IWICDdsEncoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICDdsEncoder methods ***/
static inline HRESULT IWICDdsEncoder_SetParameters(IWICDdsEncoder* This,WICDdsParameters *parameters) {
    return This->lpVtbl->SetParameters(This,parameters);
}
static inline HRESULT IWICDdsEncoder_GetParameters(IWICDdsEncoder* This,WICDdsParameters *parameters) {
    return This->lpVtbl->GetParameters(This,parameters);
}
static inline HRESULT IWICDdsEncoder_CreateNewFrame(IWICDdsEncoder* This,IWICBitmapFrameEncode **frameEncode,UINT *arrayIndex,UINT *mipLevel,UINT *sliceIndex) {
    return This->lpVtbl->CreateNewFrame(This,frameEncode,arrayIndex,mipLevel,sliceIndex);
}
#endif
#endif

#endif


#endif  /* __IWICDdsEncoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICDdsFrameDecode interface
 */
#ifndef __IWICDdsFrameDecode_INTERFACE_DEFINED__
#define __IWICDdsFrameDecode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICDdsFrameDecode, 0x3d4c0c61, 0x18a4, 0x41e4, 0xbd,0x80, 0x48,0x1a,0x4f,0xc9,0xf4,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3d4c0c61-18a4-41e4-bd80-481a4fc9f464")
IWICDdsFrameDecode : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSizeInBlocks(
        UINT *widthInBlocks,
        UINT *heightInBlocks) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFormatInfo(
        WICDdsFormatInfo *formatInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyBlocks(
        const WICRect *boundsInBlocks,
        UINT stride,
        UINT bufferSize,
        BYTE *buffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICDdsFrameDecode, 0x3d4c0c61, 0x18a4, 0x41e4, 0xbd,0x80, 0x48,0x1a,0x4f,0xc9,0xf4,0x64)
#endif
#else
typedef struct IWICDdsFrameDecodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICDdsFrameDecode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICDdsFrameDecode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICDdsFrameDecode *This);

    /*** IWICDdsFrameDecode methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSizeInBlocks)(
        IWICDdsFrameDecode *This,
        UINT *widthInBlocks,
        UINT *heightInBlocks);

    HRESULT (STDMETHODCALLTYPE *GetFormatInfo)(
        IWICDdsFrameDecode *This,
        WICDdsFormatInfo *formatInfo);

    HRESULT (STDMETHODCALLTYPE *CopyBlocks)(
        IWICDdsFrameDecode *This,
        const WICRect *boundsInBlocks,
        UINT stride,
        UINT bufferSize,
        BYTE *buffer);

    END_INTERFACE
} IWICDdsFrameDecodeVtbl;

interface IWICDdsFrameDecode {
    CONST_VTBL IWICDdsFrameDecodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICDdsFrameDecode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICDdsFrameDecode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICDdsFrameDecode_Release(This) (This)->lpVtbl->Release(This)
/*** IWICDdsFrameDecode methods ***/
#define IWICDdsFrameDecode_GetSizeInBlocks(This,widthInBlocks,heightInBlocks) (This)->lpVtbl->GetSizeInBlocks(This,widthInBlocks,heightInBlocks)
#define IWICDdsFrameDecode_GetFormatInfo(This,formatInfo) (This)->lpVtbl->GetFormatInfo(This,formatInfo)
#define IWICDdsFrameDecode_CopyBlocks(This,boundsInBlocks,stride,bufferSize,buffer) (This)->lpVtbl->CopyBlocks(This,boundsInBlocks,stride,bufferSize,buffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICDdsFrameDecode_QueryInterface(IWICDdsFrameDecode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICDdsFrameDecode_AddRef(IWICDdsFrameDecode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICDdsFrameDecode_Release(IWICDdsFrameDecode* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICDdsFrameDecode methods ***/
static inline HRESULT IWICDdsFrameDecode_GetSizeInBlocks(IWICDdsFrameDecode* This,UINT *widthInBlocks,UINT *heightInBlocks) {
    return This->lpVtbl->GetSizeInBlocks(This,widthInBlocks,heightInBlocks);
}
static inline HRESULT IWICDdsFrameDecode_GetFormatInfo(IWICDdsFrameDecode* This,WICDdsFormatInfo *formatInfo) {
    return This->lpVtbl->GetFormatInfo(This,formatInfo);
}
static inline HRESULT IWICDdsFrameDecode_CopyBlocks(IWICDdsFrameDecode* This,const WICRect *boundsInBlocks,UINT stride,UINT bufferSize,BYTE *buffer) {
    return This->lpVtbl->CopyBlocks(This,boundsInBlocks,stride,bufferSize,buffer);
}
#endif
#endif

#endif


#endif  /* __IWICDdsFrameDecode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICWineDecoder interface
 */
#ifndef __IWICWineDecoder_INTERFACE_DEFINED__
#define __IWICWineDecoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICWineDecoder, 0xb9bd430d, 0x28a8, 0x41d3, 0xa1,0xf5, 0xf3,0x6e,0xe0,0x28,0x40,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b9bd430d-28a8-41d3-a1f5-f36ee02840bf")
IWICWineDecoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IStream *stream,
        WICDecodeOptions options) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICWineDecoder, 0xb9bd430d, 0x28a8, 0x41d3, 0xa1,0xf5, 0xf3,0x6e,0xe0,0x28,0x40,0xbf)
#endif
#else
typedef struct IWICWineDecoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICWineDecoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICWineDecoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICWineDecoder *This);

    /*** IWICWineDecoder methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWICWineDecoder *This,
        IStream *stream,
        WICDecodeOptions options);

    END_INTERFACE
} IWICWineDecoderVtbl;

interface IWICWineDecoder {
    CONST_VTBL IWICWineDecoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICWineDecoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICWineDecoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICWineDecoder_Release(This) (This)->lpVtbl->Release(This)
/*** IWICWineDecoder methods ***/
#define IWICWineDecoder_Initialize(This,stream,options) (This)->lpVtbl->Initialize(This,stream,options)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICWineDecoder_QueryInterface(IWICWineDecoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICWineDecoder_AddRef(IWICWineDecoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICWineDecoder_Release(IWICWineDecoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICWineDecoder methods ***/
static inline HRESULT IWICWineDecoder_Initialize(IWICWineDecoder* This,IStream *stream,WICDecodeOptions options) {
    return This->lpVtbl->Initialize(This,stream,options);
}
#endif
#endif

#endif


#endif  /* __IWICWineDecoder_INTERFACE_DEFINED__ */

HRESULT WINAPI WICConvertBitmapSource(REFWICPixelFormatGUID dstFormat, IWICBitmapSource *pISrc, IWICBitmapSource **ppIDst);
HRESULT WINAPI WICCreateBitmapFromSection(UINT width, UINT height, REFWICPixelFormatGUID format, HANDLE section, UINT stride, UINT offset, IWICBitmap **bitmap);
HRESULT WINAPI WICCreateBitmapFromSectionEx(UINT width, UINT height, REFWICPixelFormatGUID format, HANDLE section, UINT stride, UINT offset, WICSectionAccessLevel access, IWICBitmap **bitmap);
HRESULT WINAPI WICMapGuidToShortName(REFGUID,UINT,WCHAR *,UINT *);
HRESULT WINAPI WICMapShortNameToGuid(PCWSTR,GUID *);
HRESULT WINAPI WICMapSchemaToName(REFGUID,LPWSTR,UINT,WCHAR *,UINT *);
DEFINE_GUID(CLSID_WICBmpDecoder, 0x6b462062,0x7cbf,0x400d,0x9f,0xdb,0x81,0x3d,0xd1,0x0f,0x27,0x78);
DEFINE_GUID(CLSID_WICPngDecoder, 0x389ea17b,0x5078,0x4cde,0xb6,0xef,0x25,0xc1,0x51,0x75,0xc7,0x51);
DEFINE_GUID(CLSID_WICPngDecoder1, 0x389ea17b,0x5078,0x4cde,0xb6,0xef,0x25,0xc1,0x51,0x75,0xc7,0x51);
DEFINE_GUID(CLSID_WICPngDecoder2, 0xe018945b,0xaa86,0x4008,0x9b,0xd4,0x67,0x77,0xa1,0xe4,0x0c,0x11);
DEFINE_GUID(CLSID_WICIcoDecoder, 0xc61bfcdf,0x2e0f,0x4aad,0xa8,0xd7,0xe0,0x6b,0xaf,0xeb,0xcd,0xfe);
DEFINE_GUID(CLSID_WICJpegDecoder, 0x9456a480,0xe88b,0x43ea,0x9e,0x73,0x0b,0x2d,0x9b,0x71,0xb1,0xca);
DEFINE_GUID(CLSID_WICGifDecoder, 0x381dda3c,0x9ce9,0x4834,0xa2,0x3e,0x1f,0x98,0xf8,0xfc,0x52,0xbe);
DEFINE_GUID(CLSID_WICTiffDecoder, 0xb54e85d9,0xfe23,0x499f,0x8b,0x88,0x6a,0xce,0xa7,0x13,0x75,0x2b);
DEFINE_GUID(CLSID_WICWmpDecoder, 0xa26cec36,0x234c,0x4950,0xae,0x16,0xe3,0x4a,0xac,0xe7,0x1d,0x0d);
DEFINE_GUID(CLSID_WICDdsDecoder, 0x9053699f,0xa341,0x429d,0x9e,0x90,0xee,0x43,0x7c,0xf8,0x0c,0x73);
DEFINE_GUID(CLSID_WICBmpEncoder, 0x69be8bb4,0xd66d,0x47c8,0x86,0x5a,0xed,0x15,0x89,0x43,0x37,0x82);
DEFINE_GUID(CLSID_WICPngEncoder, 0x27949969,0x876a,0x41d7,0x94,0x47,0x56,0x8f,0x6a,0x35,0xa4,0xdc);
DEFINE_GUID(CLSID_WICJpegEncoder, 0x1a34f5c1,0x4a5a,0x46dc,0xb6,0x44,0x1f,0x45,0x67,0xe7,0xa6,0x76);
DEFINE_GUID(CLSID_WICGifEncoder, 0x114f5598,0x0b22,0x40a0,0x86,0xa1,0xc8,0x3e,0xa4,0x95,0xad,0xbd);
DEFINE_GUID(CLSID_WICTiffEncoder, 0x0131be10,0x2001,0x4c5f,0xa9,0xb0,0xcc,0x88,0xfa,0xb6,0x4c,0xe8);
DEFINE_GUID(CLSID_WICWmpEncoder, 0xac4ce3cb,0xe1c1,0x44cd,0x82,0x15,0x5a,0x16,0x65,0x50,0x9e,0xc2);
DEFINE_GUID(CLSID_WICDdsEncoder, 0xa61dde94,0x66ce,0x4ac1,0x88,0x1b,0x71,0x68,0x05,0x88,0x89,0x5e);
DEFINE_GUID(CLSID_WICAdngDecoder, 0x981d9411,0x909e,0x42a7,0x8f,0x5d,0xa7,0x47,0xff,0x05,0x2e,0xdb);
DEFINE_GUID(CLSID_WICJpegQualcommPhoneEncoder, 0x68ed5c62,0xf534,0x4979,0xb2,0xb3,0x68,0x6a,0x12,0xb2,0xb3,0x4c);
DEFINE_GUID(CLSID_WICHeifDecoder, 0xe9a4a80a,0x44fe,0x4de4,0x89,0x71,0x71,0x50,0xb1,0x0a,0x51,0x99);
DEFINE_GUID(CLSID_WICHeifEncoder, 0x0dbecec1,0x9eb3,0x4860,0x9c,0x6f,0xdd,0xbe,0x86,0x63,0x45,0x75);
DEFINE_GUID(CLSID_WICWebpDecoder, 0x7693e886,0x51c9,0x4070,0x84,0x19,0x9f,0x70,0x73,0x8e,0xc8,0xfa);
DEFINE_GUID(CLSID_WICRAWDecoder, 0x41945702,0x8302,0x44a6,0x94,0x45,0xac,0x98,0xe8,0xaf,0xa0,0x86);
DEFINE_GUID(CLSID_WICDefaultFormatConverter, 0x1a3f11dc,0xb514,0x4b17,0x8c,0x5f,0x21,0x54,0x51,0x38,0x52,0xf1);
DEFINE_GUID(CLSID_WICFormatConverterHighColor, 0xac75d454,0x9f37,0x48f8,0xb9,0x72,0x4e,0x19,0xbc,0x85,0x60,0x11);
DEFINE_GUID(CLSID_WICFormatConverterNChannel, 0xc17cabb2,0xd4a3,0x47d7,0xa5,0x57,0x33,0x9b,0x2e,0xfb,0xd4,0xf1);
DEFINE_GUID(CLSID_WICFormatConverterWMPhoto, 0x9cb5172b,0xd600,0x46ba,0xab,0x77,0x77,0xbb,0x7e,0x3a,0x00,0xd9);
DEFINE_GUID(CLSID_WICPlanarFormatConverter, 0x184132b8,0x32f8,0x4784,0x91,0x31,0xdd,0x72,0x24,0xb2,0x34,0x38);
DEFINE_GUID(GUID_ContainerFormatBmp, 0x0af1d87e,0xfcfe,0x4188,0xbd,0xeb,0xa7,0x90,0x64,0x71,0xcb,0xe3);
DEFINE_GUID(GUID_ContainerFormatPng, 0x1b7cfaf4,0x713f,0x473c,0xbb,0xcd,0x61,0x37,0x42,0x5f,0xae,0xaf);
DEFINE_GUID(GUID_ContainerFormatIco, 0xa3a860c4,0x338f,0x4c17,0x91,0x9a,0xfb,0xa4,0xb5,0x62,0x8f,0x21);
DEFINE_GUID(GUID_ContainerFormatJpeg, 0x19e4a5aa,0x5662,0x4fc5,0xa0,0xc0,0x17,0x58,0x02,0x8e,0x10,0x57);
DEFINE_GUID(GUID_ContainerFormatTiff, 0x163bcc30,0xe2e9,0x4f0b,0x96,0x1d,0xa3,0xe9,0xfd,0xb7,0x88,0xa3);
DEFINE_GUID(GUID_ContainerFormatGif, 0x1f8a5601,0x7d4d,0x4cbd,0x9c,0x82,0x1b,0xc8,0xd4,0xee,0xb9,0xa5);
DEFINE_GUID(GUID_ContainerFormatWmp, 0x57a37caa,0x367a,0x4540,0x91,0x6b,0xf1,0x83,0xc5,0x09,0x3a,0x4b);
DEFINE_GUID(GUID_ContainerFormatDds, 0x9967cb95,0x2e85,0x4ac8,0x8c,0xa2,0x83,0xd7,0xcc,0xd4,0x25,0xc9);
DEFINE_GUID(GUID_ContainerFormatAdng, 0xf3ff6d0d,0x38c0,0x41c4,0xb1,0xfe,0x1f,0x38,0x24,0xf1,0x7b,0x84);
DEFINE_GUID(GUID_ContainerFormatHeif, 0xe1e62521,0x6787,0x405b,0xa3,0x39,0x50,0x07,0x15,0xb5,0x76,0x3f);
DEFINE_GUID(GUID_ContainerFormatWebp, 0xe094b0e2,0x67f2,0x45b3,0xb0,0xea,0x11,0x53,0x37,0xca,0x7c,0xf3);
DEFINE_GUID(GUID_ContainerFormatRaw,  0xfe99ce60,0xf19c,0x433c,0xa3,0xae,0x00,0xac,0xef,0xa9,0xca,0x21);
DEFINE_GUID(GUID_VendorMicrosoft, 0xf0e749ca,0xedef,0x4589,0xa7,0x3a,0xee,0x0e,0x62,0x6a,0x2a,0x2b);
DEFINE_GUID(GUID_VendorMicrosoftBuiltIn, 0x257a30fd,0x6b6,0x462b,0xae,0xa4,0x63,0xf7,0xb,0x86,0xe5,0x33);
DEFINE_GUID(CLSID_WICImagingCategories, 0xfae3d380,0xfea4,0x4623,0x8c,0x75,0xc6,0xb6,0x11,0x10,0xb6,0x81);
DEFINE_GUID(CATID_WICBitmapDecoders, 0x7ed96837,0x96f0,0x4812,0xb2,0x11,0xf1,0x3c,0x24,0x11,0x7e,0xd3);
DEFINE_GUID(CATID_WICBitmapEncoders, 0xac757296,0x3522,0x4e11,0x98,0x62,0xc1,0x7b,0xe5,0xa1,0x76,0x7e);
DEFINE_GUID(CATID_WICFormatConverters, 0x7835eae8,0xbf14,0x49d1,0x93,0xce,0x53,0x3a,0x40,0x7b,0x22,0x48);
DEFINE_GUID(CATID_WICMetadataReader, 0x05af94d8,0x7174,0x4cd2,0xbe,0x4a,0x41,0x24,0xb8,0x0e,0xe4,0xb8);
DEFINE_GUID(CATID_WICMetadataWriter, 0xabe3b9a4,0x257d,0x4b97,0xbd,0x1a,0x29,0x4a,0xf4,0x96,0x22,0x2e);
DEFINE_GUID(CATID_WICPixelFormats, 0x2b46e70f,0xcda7,0x473e,0x89,0xf6,0xdc,0x96,0x30,0xa2,0x39,0x0b);
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);
ULONG           __RPC_USER HBITMAP_UserSize     (ULONG *, ULONG, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserMarshal  (ULONG *, unsigned char *, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserUnmarshal(ULONG *, unsigned char *, HBITMAP *);
void            __RPC_USER HBITMAP_UserFree     (ULONG *, HBITMAP *);
ULONG           __RPC_USER HPALETTE_UserSize     (ULONG *, ULONG, HPALETTE *);
unsigned char * __RPC_USER HPALETTE_UserMarshal  (ULONG *, unsigned char *, HPALETTE *);
unsigned char * __RPC_USER HPALETTE_UserUnmarshal(ULONG *, unsigned char *, HPALETTE *);
void            __RPC_USER HPALETTE_UserFree     (ULONG *, HPALETTE *);
ULONG           __RPC_USER HICON_UserSize     (ULONG *, ULONG, HICON *);
unsigned char * __RPC_USER HICON_UserMarshal  (ULONG *, unsigned char *, HICON *);
unsigned char * __RPC_USER HICON_UserUnmarshal(ULONG *, unsigned char *, HICON *);
void            __RPC_USER HICON_UserFree     (ULONG *, HICON *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wincodec_h__ */
