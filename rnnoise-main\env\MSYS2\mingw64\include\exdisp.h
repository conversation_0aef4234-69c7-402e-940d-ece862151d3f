/*** Autogenerated by WIDL 10.12 from include/exdisp.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __exdisp_h__
#define __exdisp_h__

/* Forward declarations */

#ifndef __IWebBrowser_FWD_DEFINED__
#define __IWebBrowser_FWD_DEFINED__
typedef interface IWebBrowser IWebBrowser;
#ifdef __cplusplus
interface IWebBrowser;
#endif /* __cplusplus */
#endif

#ifndef __DWebBrowserEvents_FWD_DEFINED__
#define __DWebBrowserEvents_FWD_DEFINED__
typedef interface DWebBrowserEvents DWebBrowserEvents;
#ifdef __cplusplus
interface DWebBrowserEvents;
#endif /* __cplusplus */
#endif

#ifndef __IWebBrowserApp_FWD_DEFINED__
#define __IWebBrowserApp_FWD_DEFINED__
typedef interface IWebBrowserApp IWebBrowserApp;
#ifdef __cplusplus
interface IWebBrowserApp;
#endif /* __cplusplus */
#endif

#ifndef __IWebBrowser2_FWD_DEFINED__
#define __IWebBrowser2_FWD_DEFINED__
typedef interface IWebBrowser2 IWebBrowser2;
#ifdef __cplusplus
interface IWebBrowser2;
#endif /* __cplusplus */
#endif

#ifndef __DWebBrowserEvents2_FWD_DEFINED__
#define __DWebBrowserEvents2_FWD_DEFINED__
typedef interface DWebBrowserEvents2 DWebBrowserEvents2;
#ifdef __cplusplus
interface DWebBrowserEvents2;
#endif /* __cplusplus */
#endif

#ifndef __WebBrowser_V1_FWD_DEFINED__
#define __WebBrowser_V1_FWD_DEFINED__
#ifdef __cplusplus
typedef class WebBrowser_V1 WebBrowser_V1;
#else
typedef struct WebBrowser_V1 WebBrowser_V1;
#endif /* defined __cplusplus */
#endif /* defined __WebBrowser_V1_FWD_DEFINED__ */

#ifndef __WebBrowser_FWD_DEFINED__
#define __WebBrowser_FWD_DEFINED__
#ifdef __cplusplus
typedef class WebBrowser WebBrowser;
#else
typedef struct WebBrowser WebBrowser;
#endif /* defined __cplusplus */
#endif /* defined __WebBrowser_FWD_DEFINED__ */

#ifndef __InternetExplorer_FWD_DEFINED__
#define __InternetExplorer_FWD_DEFINED__
#ifdef __cplusplus
typedef class InternetExplorer InternetExplorer;
#else
typedef struct InternetExplorer InternetExplorer;
#endif /* defined __cplusplus */
#endif /* defined __InternetExplorer_FWD_DEFINED__ */

#ifndef __ShellBrowserWindow_FWD_DEFINED__
#define __ShellBrowserWindow_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellBrowserWindow ShellBrowserWindow;
#else
typedef struct ShellBrowserWindow ShellBrowserWindow;
#endif /* defined __cplusplus */
#endif /* defined __ShellBrowserWindow_FWD_DEFINED__ */

#ifndef __DShellWindowsEvents_FWD_DEFINED__
#define __DShellWindowsEvents_FWD_DEFINED__
typedef interface DShellWindowsEvents DShellWindowsEvents;
#ifdef __cplusplus
interface DShellWindowsEvents;
#endif /* __cplusplus */
#endif

#ifndef __IShellWindows_FWD_DEFINED__
#define __IShellWindows_FWD_DEFINED__
typedef interface IShellWindows IShellWindows;
#ifdef __cplusplus
interface IShellWindows;
#endif /* __cplusplus */
#endif

#ifndef __ShellWindows_FWD_DEFINED__
#define __ShellWindows_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellWindows ShellWindows;
#else
typedef struct ShellWindows ShellWindows;
#endif /* defined __cplusplus */
#endif /* defined __ShellWindows_FWD_DEFINED__ */

#ifndef __IShellUIHelper_FWD_DEFINED__
#define __IShellUIHelper_FWD_DEFINED__
typedef interface IShellUIHelper IShellUIHelper;
#ifdef __cplusplus
interface IShellUIHelper;
#endif /* __cplusplus */
#endif

#ifndef __IShellUIHelper2_FWD_DEFINED__
#define __IShellUIHelper2_FWD_DEFINED__
typedef interface IShellUIHelper2 IShellUIHelper2;
#ifdef __cplusplus
interface IShellUIHelper2;
#endif /* __cplusplus */
#endif

#ifndef __ShellUIHelper_FWD_DEFINED__
#define __ShellUIHelper_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellUIHelper ShellUIHelper;
#else
typedef struct ShellUIHelper ShellUIHelper;
#endif /* defined __cplusplus */
#endif /* defined __ShellUIHelper_FWD_DEFINED__ */

#ifndef __DShellNameSpaceEvents_FWD_DEFINED__
#define __DShellNameSpaceEvents_FWD_DEFINED__
typedef interface DShellNameSpaceEvents DShellNameSpaceEvents;
#ifdef __cplusplus
interface DShellNameSpaceEvents;
#endif /* __cplusplus */
#endif

#ifndef __IShellFavoritesNameSpace_FWD_DEFINED__
#define __IShellFavoritesNameSpace_FWD_DEFINED__
typedef interface IShellFavoritesNameSpace IShellFavoritesNameSpace;
#ifdef __cplusplus
interface IShellFavoritesNameSpace;
#endif /* __cplusplus */
#endif

#ifndef __IShellNameSpace_FWD_DEFINED__
#define __IShellNameSpace_FWD_DEFINED__
typedef interface IShellNameSpace IShellNameSpace;
#ifdef __cplusplus
interface IShellNameSpace;
#endif /* __cplusplus */
#endif

#ifndef __ShellShellNameSpace_FWD_DEFINED__
#define __ShellShellNameSpace_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellShellNameSpace ShellShellNameSpace;
#else
typedef struct ShellShellNameSpace ShellShellNameSpace;
#endif /* defined __cplusplus */
#endif /* defined __ShellShellNameSpace_FWD_DEFINED__ */

#ifndef __ShellNameSpace_FWD_DEFINED__
#define __ShellNameSpace_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellNameSpace ShellNameSpace;
#else
typedef struct ShellNameSpace ShellNameSpace;
#endif /* defined __cplusplus */
#endif /* defined __ShellNameSpace_FWD_DEFINED__ */

#ifndef __IScriptErrorList_FWD_DEFINED__
#define __IScriptErrorList_FWD_DEFINED__
typedef interface IScriptErrorList IScriptErrorList;
#ifdef __cplusplus
interface IScriptErrorList;
#endif /* __cplusplus */
#endif

#ifndef __CScriptErrorList_FWD_DEFINED__
#define __CScriptErrorList_FWD_DEFINED__
#ifdef __cplusplus
typedef class CScriptErrorList CScriptErrorList;
#else
typedef struct CScriptErrorList CScriptErrorList;
#endif /* defined __cplusplus */
#endif /* defined __CScriptErrorList_FWD_DEFINED__ */

#ifndef __ISearch_FWD_DEFINED__
#define __ISearch_FWD_DEFINED__
typedef interface ISearch ISearch;
#ifdef __cplusplus
interface ISearch;
#endif /* __cplusplus */
#endif

#ifndef __ISearches_FWD_DEFINED__
#define __ISearches_FWD_DEFINED__
typedef interface ISearches ISearches;
#ifdef __cplusplus
interface ISearches;
#endif /* __cplusplus */
#endif

#ifndef __ISearchAssistantOC_FWD_DEFINED__
#define __ISearchAssistantOC_FWD_DEFINED__
typedef interface ISearchAssistantOC ISearchAssistantOC;
#ifdef __cplusplus
interface ISearchAssistantOC;
#endif /* __cplusplus */
#endif

#ifndef __ISearchAssistantOC2_FWD_DEFINED__
#define __ISearchAssistantOC2_FWD_DEFINED__
typedef interface ISearchAssistantOC2 ISearchAssistantOC2;
#ifdef __cplusplus
interface ISearchAssistantOC2;
#endif /* __cplusplus */
#endif

#ifndef __ISearchAssistantOC3_FWD_DEFINED__
#define __ISearchAssistantOC3_FWD_DEFINED__
typedef interface ISearchAssistantOC3 ISearchAssistantOC3;
#ifdef __cplusplus
interface ISearchAssistantOC3;
#endif /* __cplusplus */
#endif

#ifndef ___SearchAssistantEvents_FWD_DEFINED__
#define ___SearchAssistantEvents_FWD_DEFINED__
typedef interface _SearchAssistantEvents _SearchAssistantEvents;
#ifdef __cplusplus
interface _SearchAssistantEvents;
#endif /* __cplusplus */
#endif

#ifndef __ShellSearchAssistantOC_FWD_DEFINED__
#define __ShellSearchAssistantOC_FWD_DEFINED__
#ifdef __cplusplus
typedef class ShellSearchAssistantOC ShellSearchAssistantOC;
#else
typedef struct ShellSearchAssistantOC ShellSearchAssistantOC;
#endif /* defined __cplusplus */
#endif /* defined __ShellSearchAssistantOC_FWD_DEFINED__ */

#ifndef __SearchAssistantOC_FWD_DEFINED__
#define __SearchAssistantOC_FWD_DEFINED__
#ifdef __cplusplus
typedef class SearchAssistantOC SearchAssistantOC;
#else
typedef struct SearchAssistantOC SearchAssistantOC;
#endif /* defined __cplusplus */
#endif /* defined __SearchAssistantOC_FWD_DEFINED__ */

/* Headers for imported files */

#include <ocidl.h>
#include <docobj.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifdef WINE_NO_UNICODE_MACROS
#undef FindText
#endif
#ifndef __SHDocVw_LIBRARY_DEFINED__
#define __SHDocVw_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_SHDocVw, 0xeab22ac0, 0x30c1, 0x11cf, 0xa7,0xeb, 0x00,0x00,0xc0,0x5b,0xae,0x0b);

/*****************************************************************************
 * IWebBrowser interface
 */
#ifndef __IWebBrowser_INTERFACE_DEFINED__
#define __IWebBrowser_INTERFACE_DEFINED__

typedef enum BrowserNavConstants {
    navOpenInNewWindow = 0x1,
    navNoHistory = 0x2,
    navNoReadFromCache = 0x4,
    navNoWriteToCache = 0x8,
    navAllowAutosearch = 0x10,
    navBrowserBar = 0x20,
    navHyperlink = 0x40,
    navEnforceRestricted = 0x80,
    navNewWindowsManaged = 0x100,
    navUntrustedForDownload = 0x200,
    navTrustedForActiveX = 0x400,
    navOpenInNewTab = 0x800,
    navOpenInBackgroundTab = 0x1000,
    navKeepWordWheelText = 0x2000,
    navVirtualTab = 0x4000,
    navBlockRedirectsXDomain = 0x8000,
    navOpenNewForegroundTab = 0x10000,
    navTravelLogScreenshot = 0x20000,
    navDeferUnload = 0x40000,
    navSpeculative = 0x80000,
    navSuggestNewWindow = 0x100000,
    navSuggestNewTab = 0x200000,
    navReserved1 = 0x400000,
    navHomepageNavigate = 0x800000,
    navRefresh = 0x1000000,
    navHostNavigation = 0x2000000,
    navReserved2 = 0x4000000,
    navReserved3 = 0x8000000,
    navReserved4 = 0x10000000
} BrowserNavConstants;
typedef enum RefreshConstants {
    REFRESH_NORMAL = 0,
    REFRESH_IFEXPIRED = 1,
    REFRESH_COMPLETELY = 3
} RefreshConstants;
DEFINE_GUID(IID_IWebBrowser, 0xeab22ac1, 0x30c1, 0x11cf, 0xa7,0xeb, 0x00,0x00,0xc0,0x5b,0xae,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eab22ac1-30c1-11cf-a7eb-0000c05bae0b")
IWebBrowser : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GoBack(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GoForward(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GoHome(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GoSearch(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Navigate(
        BSTR URL,
        VARIANT *Flags,
        VARIANT *TargetFrameName,
        VARIANT *PostData,
        VARIANT *Headers) = 0;

    virtual HRESULT STDMETHODCALLTYPE Refresh(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Refresh2(
        VARIANT *Level) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IDispatch **ppDisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        IDispatch **ppDisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Container(
        IDispatch **ppDisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Document(
        IDispatch **ppDisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TopLevelContainer(
        VARIANT_BOOL *pBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        BSTR *Type) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Left(
        LONG *pl) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Left(
        LONG Left) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Top(
        LONG *pl) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Top(
        LONG Top) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Width(
        LONG *pl) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Width(
        LONG Width) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Height(
        LONG *pl) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Height(
        LONG Height) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocationName(
        BSTR *LocationName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocationURL(
        BSTR *LocationURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Busy(
        VARIANT_BOOL *pBool) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWebBrowser, 0xeab22ac1, 0x30c1, 0x11cf, 0xa7,0xeb, 0x00,0x00,0xc0,0x5b,0xae,0x0b)
#endif
#else
typedef struct IWebBrowserVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWebBrowser *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWebBrowser *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWebBrowser *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWebBrowser *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWebBrowser *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWebBrowser *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWebBrowser *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWebBrowser methods ***/
    HRESULT (STDMETHODCALLTYPE *GoBack)(
        IWebBrowser *This);

    HRESULT (STDMETHODCALLTYPE *GoForward)(
        IWebBrowser *This);

    HRESULT (STDMETHODCALLTYPE *GoHome)(
        IWebBrowser *This);

    HRESULT (STDMETHODCALLTYPE *GoSearch)(
        IWebBrowser *This);

    HRESULT (STDMETHODCALLTYPE *Navigate)(
        IWebBrowser *This,
        BSTR URL,
        VARIANT *Flags,
        VARIANT *TargetFrameName,
        VARIANT *PostData,
        VARIANT *Headers);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWebBrowser *This);

    HRESULT (STDMETHODCALLTYPE *Refresh2)(
        IWebBrowser *This,
        VARIANT *Level);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IWebBrowser *This);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IWebBrowser *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IWebBrowser *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Container)(
        IWebBrowser *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Document)(
        IWebBrowser *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_TopLevelContainer)(
        IWebBrowser *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWebBrowser *This,
        BSTR *Type);

    HRESULT (STDMETHODCALLTYPE *get_Left)(
        IWebBrowser *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Left)(
        IWebBrowser *This,
        LONG Left);

    HRESULT (STDMETHODCALLTYPE *get_Top)(
        IWebBrowser *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Top)(
        IWebBrowser *This,
        LONG Top);

    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IWebBrowser *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Width)(
        IWebBrowser *This,
        LONG Width);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IWebBrowser *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Height)(
        IWebBrowser *This,
        LONG Height);

    HRESULT (STDMETHODCALLTYPE *get_LocationName)(
        IWebBrowser *This,
        BSTR *LocationName);

    HRESULT (STDMETHODCALLTYPE *get_LocationURL)(
        IWebBrowser *This,
        BSTR *LocationURL);

    HRESULT (STDMETHODCALLTYPE *get_Busy)(
        IWebBrowser *This,
        VARIANT_BOOL *pBool);

    END_INTERFACE
} IWebBrowserVtbl;

interface IWebBrowser {
    CONST_VTBL IWebBrowserVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWebBrowser_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWebBrowser_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWebBrowser_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWebBrowser_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWebBrowser_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWebBrowser_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWebBrowser_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWebBrowser methods ***/
#define IWebBrowser_GoBack(This) (This)->lpVtbl->GoBack(This)
#define IWebBrowser_GoForward(This) (This)->lpVtbl->GoForward(This)
#define IWebBrowser_GoHome(This) (This)->lpVtbl->GoHome(This)
#define IWebBrowser_GoSearch(This) (This)->lpVtbl->GoSearch(This)
#define IWebBrowser_Navigate(This,URL,Flags,TargetFrameName,PostData,Headers) (This)->lpVtbl->Navigate(This,URL,Flags,TargetFrameName,PostData,Headers)
#define IWebBrowser_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWebBrowser_Refresh2(This,Level) (This)->lpVtbl->Refresh2(This,Level)
#define IWebBrowser_Stop(This) (This)->lpVtbl->Stop(This)
#define IWebBrowser_get_Application(This,ppDisp) (This)->lpVtbl->get_Application(This,ppDisp)
#define IWebBrowser_get_Parent(This,ppDisp) (This)->lpVtbl->get_Parent(This,ppDisp)
#define IWebBrowser_get_Container(This,ppDisp) (This)->lpVtbl->get_Container(This,ppDisp)
#define IWebBrowser_get_Document(This,ppDisp) (This)->lpVtbl->get_Document(This,ppDisp)
#define IWebBrowser_get_TopLevelContainer(This,pBool) (This)->lpVtbl->get_TopLevelContainer(This,pBool)
#define IWebBrowser_get_Type(This,Type) (This)->lpVtbl->get_Type(This,Type)
#define IWebBrowser_get_Left(This,pl) (This)->lpVtbl->get_Left(This,pl)
#define IWebBrowser_put_Left(This,Left) (This)->lpVtbl->put_Left(This,Left)
#define IWebBrowser_get_Top(This,pl) (This)->lpVtbl->get_Top(This,pl)
#define IWebBrowser_put_Top(This,Top) (This)->lpVtbl->put_Top(This,Top)
#define IWebBrowser_get_Width(This,pl) (This)->lpVtbl->get_Width(This,pl)
#define IWebBrowser_put_Width(This,Width) (This)->lpVtbl->put_Width(This,Width)
#define IWebBrowser_get_Height(This,pl) (This)->lpVtbl->get_Height(This,pl)
#define IWebBrowser_put_Height(This,Height) (This)->lpVtbl->put_Height(This,Height)
#define IWebBrowser_get_LocationName(This,LocationName) (This)->lpVtbl->get_LocationName(This,LocationName)
#define IWebBrowser_get_LocationURL(This,LocationURL) (This)->lpVtbl->get_LocationURL(This,LocationURL)
#define IWebBrowser_get_Busy(This,pBool) (This)->lpVtbl->get_Busy(This,pBool)
#else
/*** IUnknown methods ***/
static inline HRESULT IWebBrowser_QueryInterface(IWebBrowser* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWebBrowser_AddRef(IWebBrowser* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWebBrowser_Release(IWebBrowser* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWebBrowser_GetTypeInfoCount(IWebBrowser* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWebBrowser_GetTypeInfo(IWebBrowser* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWebBrowser_GetIDsOfNames(IWebBrowser* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWebBrowser_Invoke(IWebBrowser* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWebBrowser methods ***/
static inline HRESULT IWebBrowser_GoBack(IWebBrowser* This) {
    return This->lpVtbl->GoBack(This);
}
static inline HRESULT IWebBrowser_GoForward(IWebBrowser* This) {
    return This->lpVtbl->GoForward(This);
}
static inline HRESULT IWebBrowser_GoHome(IWebBrowser* This) {
    return This->lpVtbl->GoHome(This);
}
static inline HRESULT IWebBrowser_GoSearch(IWebBrowser* This) {
    return This->lpVtbl->GoSearch(This);
}
static inline HRESULT IWebBrowser_Navigate(IWebBrowser* This,BSTR URL,VARIANT *Flags,VARIANT *TargetFrameName,VARIANT *PostData,VARIANT *Headers) {
    return This->lpVtbl->Navigate(This,URL,Flags,TargetFrameName,PostData,Headers);
}
static inline HRESULT IWebBrowser_Refresh(IWebBrowser* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWebBrowser_Refresh2(IWebBrowser* This,VARIANT *Level) {
    return This->lpVtbl->Refresh2(This,Level);
}
static inline HRESULT IWebBrowser_Stop(IWebBrowser* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IWebBrowser_get_Application(IWebBrowser* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Application(This,ppDisp);
}
static inline HRESULT IWebBrowser_get_Parent(IWebBrowser* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Parent(This,ppDisp);
}
static inline HRESULT IWebBrowser_get_Container(IWebBrowser* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Container(This,ppDisp);
}
static inline HRESULT IWebBrowser_get_Document(IWebBrowser* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Document(This,ppDisp);
}
static inline HRESULT IWebBrowser_get_TopLevelContainer(IWebBrowser* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_TopLevelContainer(This,pBool);
}
static inline HRESULT IWebBrowser_get_Type(IWebBrowser* This,BSTR *Type) {
    return This->lpVtbl->get_Type(This,Type);
}
static inline HRESULT IWebBrowser_get_Left(IWebBrowser* This,LONG *pl) {
    return This->lpVtbl->get_Left(This,pl);
}
static inline HRESULT IWebBrowser_put_Left(IWebBrowser* This,LONG Left) {
    return This->lpVtbl->put_Left(This,Left);
}
static inline HRESULT IWebBrowser_get_Top(IWebBrowser* This,LONG *pl) {
    return This->lpVtbl->get_Top(This,pl);
}
static inline HRESULT IWebBrowser_put_Top(IWebBrowser* This,LONG Top) {
    return This->lpVtbl->put_Top(This,Top);
}
static inline HRESULT IWebBrowser_get_Width(IWebBrowser* This,LONG *pl) {
    return This->lpVtbl->get_Width(This,pl);
}
static inline HRESULT IWebBrowser_put_Width(IWebBrowser* This,LONG Width) {
    return This->lpVtbl->put_Width(This,Width);
}
static inline HRESULT IWebBrowser_get_Height(IWebBrowser* This,LONG *pl) {
    return This->lpVtbl->get_Height(This,pl);
}
static inline HRESULT IWebBrowser_put_Height(IWebBrowser* This,LONG Height) {
    return This->lpVtbl->put_Height(This,Height);
}
static inline HRESULT IWebBrowser_get_LocationName(IWebBrowser* This,BSTR *LocationName) {
    return This->lpVtbl->get_LocationName(This,LocationName);
}
static inline HRESULT IWebBrowser_get_LocationURL(IWebBrowser* This,BSTR *LocationURL) {
    return This->lpVtbl->get_LocationURL(This,LocationURL);
}
static inline HRESULT IWebBrowser_get_Busy(IWebBrowser* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_Busy(This,pBool);
}
#endif
#endif

#endif


#endif  /* __IWebBrowser_INTERFACE_DEFINED__ */

/*****************************************************************************
 * DWebBrowserEvents dispinterface
 */
#ifndef __DWebBrowserEvents_DISPINTERFACE_DEFINED__
#define __DWebBrowserEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID_DWebBrowserEvents, 0xeab22ac2, 0x30c1, 0x11cf, 0xa7,0xeb, 0x00,0x00,0xc0,0x5b,0xae,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eab22ac2-30c1-11cf-a7eb-0000c05bae0b")
DWebBrowserEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DWebBrowserEvents, 0xeab22ac2, 0x30c1, 0x11cf, 0xa7,0xeb, 0x00,0x00,0xc0,0x5b,0xae,0x0b)
#endif
#else
typedef struct DWebBrowserEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        DWebBrowserEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        DWebBrowserEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        DWebBrowserEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        DWebBrowserEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        DWebBrowserEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        DWebBrowserEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        DWebBrowserEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} DWebBrowserEventsVtbl;

interface DWebBrowserEvents {
    CONST_VTBL DWebBrowserEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define DWebBrowserEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define DWebBrowserEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define DWebBrowserEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define DWebBrowserEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define DWebBrowserEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define DWebBrowserEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define DWebBrowserEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT DWebBrowserEvents_QueryInterface(DWebBrowserEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG DWebBrowserEvents_AddRef(DWebBrowserEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG DWebBrowserEvents_Release(DWebBrowserEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT DWebBrowserEvents_GetTypeInfoCount(DWebBrowserEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT DWebBrowserEvents_GetTypeInfo(DWebBrowserEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT DWebBrowserEvents_GetIDsOfNames(DWebBrowserEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT DWebBrowserEvents_Invoke(DWebBrowserEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* __DWebBrowserEvents_DISPINTERFACE_DEFINED__ */

typedef enum CommandStateChangeConstants {
    CSC_UPDATECOMMANDS = -1,
    CSC_NAVIGATEFORWARD = 1,
    CSC_NAVIGATEBACK = 2
} CommandStateChangeConstants;
/*****************************************************************************
 * IWebBrowserApp interface
 */
#ifndef __IWebBrowserApp_INTERFACE_DEFINED__
#define __IWebBrowserApp_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWebBrowserApp, 0x0002df05, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0002df05-0000-0000-c000-000000000046")
IWebBrowserApp : public IWebBrowser
{
    virtual HRESULT STDMETHODCALLTYPE Quit(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClientToWindow(
        int *pcx,
        int *pcy) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutProperty(
        BSTR Property,
        VARIANT vtValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        BSTR Property,
        VARIANT *pvtValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *Name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HWND(
        SHANDLE_PTR *pHWND) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FullName(
        BSTR *FullName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *Path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Visible(
        VARIANT_BOOL *pBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Visible(
        VARIANT_BOOL Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StatusBar(
        VARIANT_BOOL *pBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StatusBar(
        VARIANT_BOOL Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StatusText(
        BSTR *StatusText) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StatusText(
        BSTR StatusText) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ToolBar(
        int *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ToolBar(
        int Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MenuBar(
        VARIANT_BOOL *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MenuBar(
        VARIANT_BOOL Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FullScreen(
        VARIANT_BOOL *pbFullScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FullScreen(
        VARIANT_BOOL bFullScreen) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWebBrowserApp, 0x0002df05, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IWebBrowserAppVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWebBrowserApp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWebBrowserApp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWebBrowserApp *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWebBrowserApp *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWebBrowserApp *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWebBrowserApp *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWebBrowserApp *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWebBrowser methods ***/
    HRESULT (STDMETHODCALLTYPE *GoBack)(
        IWebBrowserApp *This);

    HRESULT (STDMETHODCALLTYPE *GoForward)(
        IWebBrowserApp *This);

    HRESULT (STDMETHODCALLTYPE *GoHome)(
        IWebBrowserApp *This);

    HRESULT (STDMETHODCALLTYPE *GoSearch)(
        IWebBrowserApp *This);

    HRESULT (STDMETHODCALLTYPE *Navigate)(
        IWebBrowserApp *This,
        BSTR URL,
        VARIANT *Flags,
        VARIANT *TargetFrameName,
        VARIANT *PostData,
        VARIANT *Headers);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWebBrowserApp *This);

    HRESULT (STDMETHODCALLTYPE *Refresh2)(
        IWebBrowserApp *This,
        VARIANT *Level);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IWebBrowserApp *This);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IWebBrowserApp *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IWebBrowserApp *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Container)(
        IWebBrowserApp *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Document)(
        IWebBrowserApp *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_TopLevelContainer)(
        IWebBrowserApp *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWebBrowserApp *This,
        BSTR *Type);

    HRESULT (STDMETHODCALLTYPE *get_Left)(
        IWebBrowserApp *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Left)(
        IWebBrowserApp *This,
        LONG Left);

    HRESULT (STDMETHODCALLTYPE *get_Top)(
        IWebBrowserApp *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Top)(
        IWebBrowserApp *This,
        LONG Top);

    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IWebBrowserApp *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Width)(
        IWebBrowserApp *This,
        LONG Width);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IWebBrowserApp *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Height)(
        IWebBrowserApp *This,
        LONG Height);

    HRESULT (STDMETHODCALLTYPE *get_LocationName)(
        IWebBrowserApp *This,
        BSTR *LocationName);

    HRESULT (STDMETHODCALLTYPE *get_LocationURL)(
        IWebBrowserApp *This,
        BSTR *LocationURL);

    HRESULT (STDMETHODCALLTYPE *get_Busy)(
        IWebBrowserApp *This,
        VARIANT_BOOL *pBool);

    /*** IWebBrowserApp methods ***/
    HRESULT (STDMETHODCALLTYPE *Quit)(
        IWebBrowserApp *This);

    HRESULT (STDMETHODCALLTYPE *ClientToWindow)(
        IWebBrowserApp *This,
        int *pcx,
        int *pcy);

    HRESULT (STDMETHODCALLTYPE *PutProperty)(
        IWebBrowserApp *This,
        BSTR Property,
        VARIANT vtValue);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IWebBrowserApp *This,
        BSTR Property,
        VARIANT *pvtValue);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWebBrowserApp *This,
        BSTR *Name);

    HRESULT (STDMETHODCALLTYPE *get_HWND)(
        IWebBrowserApp *This,
        SHANDLE_PTR *pHWND);

    HRESULT (STDMETHODCALLTYPE *get_FullName)(
        IWebBrowserApp *This,
        BSTR *FullName);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IWebBrowserApp *This,
        BSTR *Path);

    HRESULT (STDMETHODCALLTYPE *get_Visible)(
        IWebBrowserApp *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *put_Visible)(
        IWebBrowserApp *This,
        VARIANT_BOOL Value);

    HRESULT (STDMETHODCALLTYPE *get_StatusBar)(
        IWebBrowserApp *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *put_StatusBar)(
        IWebBrowserApp *This,
        VARIANT_BOOL Value);

    HRESULT (STDMETHODCALLTYPE *get_StatusText)(
        IWebBrowserApp *This,
        BSTR *StatusText);

    HRESULT (STDMETHODCALLTYPE *put_StatusText)(
        IWebBrowserApp *This,
        BSTR StatusText);

    HRESULT (STDMETHODCALLTYPE *get_ToolBar)(
        IWebBrowserApp *This,
        int *Value);

    HRESULT (STDMETHODCALLTYPE *put_ToolBar)(
        IWebBrowserApp *This,
        int Value);

    HRESULT (STDMETHODCALLTYPE *get_MenuBar)(
        IWebBrowserApp *This,
        VARIANT_BOOL *Value);

    HRESULT (STDMETHODCALLTYPE *put_MenuBar)(
        IWebBrowserApp *This,
        VARIANT_BOOL Value);

    HRESULT (STDMETHODCALLTYPE *get_FullScreen)(
        IWebBrowserApp *This,
        VARIANT_BOOL *pbFullScreen);

    HRESULT (STDMETHODCALLTYPE *put_FullScreen)(
        IWebBrowserApp *This,
        VARIANT_BOOL bFullScreen);

    END_INTERFACE
} IWebBrowserAppVtbl;

interface IWebBrowserApp {
    CONST_VTBL IWebBrowserAppVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWebBrowserApp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWebBrowserApp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWebBrowserApp_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWebBrowserApp_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWebBrowserApp_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWebBrowserApp_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWebBrowserApp_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWebBrowser methods ***/
#define IWebBrowserApp_GoBack(This) (This)->lpVtbl->GoBack(This)
#define IWebBrowserApp_GoForward(This) (This)->lpVtbl->GoForward(This)
#define IWebBrowserApp_GoHome(This) (This)->lpVtbl->GoHome(This)
#define IWebBrowserApp_GoSearch(This) (This)->lpVtbl->GoSearch(This)
#define IWebBrowserApp_Navigate(This,URL,Flags,TargetFrameName,PostData,Headers) (This)->lpVtbl->Navigate(This,URL,Flags,TargetFrameName,PostData,Headers)
#define IWebBrowserApp_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWebBrowserApp_Refresh2(This,Level) (This)->lpVtbl->Refresh2(This,Level)
#define IWebBrowserApp_Stop(This) (This)->lpVtbl->Stop(This)
#define IWebBrowserApp_get_Application(This,ppDisp) (This)->lpVtbl->get_Application(This,ppDisp)
#define IWebBrowserApp_get_Parent(This,ppDisp) (This)->lpVtbl->get_Parent(This,ppDisp)
#define IWebBrowserApp_get_Container(This,ppDisp) (This)->lpVtbl->get_Container(This,ppDisp)
#define IWebBrowserApp_get_Document(This,ppDisp) (This)->lpVtbl->get_Document(This,ppDisp)
#define IWebBrowserApp_get_TopLevelContainer(This,pBool) (This)->lpVtbl->get_TopLevelContainer(This,pBool)
#define IWebBrowserApp_get_Type(This,Type) (This)->lpVtbl->get_Type(This,Type)
#define IWebBrowserApp_get_Left(This,pl) (This)->lpVtbl->get_Left(This,pl)
#define IWebBrowserApp_put_Left(This,Left) (This)->lpVtbl->put_Left(This,Left)
#define IWebBrowserApp_get_Top(This,pl) (This)->lpVtbl->get_Top(This,pl)
#define IWebBrowserApp_put_Top(This,Top) (This)->lpVtbl->put_Top(This,Top)
#define IWebBrowserApp_get_Width(This,pl) (This)->lpVtbl->get_Width(This,pl)
#define IWebBrowserApp_put_Width(This,Width) (This)->lpVtbl->put_Width(This,Width)
#define IWebBrowserApp_get_Height(This,pl) (This)->lpVtbl->get_Height(This,pl)
#define IWebBrowserApp_put_Height(This,Height) (This)->lpVtbl->put_Height(This,Height)
#define IWebBrowserApp_get_LocationName(This,LocationName) (This)->lpVtbl->get_LocationName(This,LocationName)
#define IWebBrowserApp_get_LocationURL(This,LocationURL) (This)->lpVtbl->get_LocationURL(This,LocationURL)
#define IWebBrowserApp_get_Busy(This,pBool) (This)->lpVtbl->get_Busy(This,pBool)
/*** IWebBrowserApp methods ***/
#define IWebBrowserApp_Quit(This) (This)->lpVtbl->Quit(This)
#define IWebBrowserApp_ClientToWindow(This,pcx,pcy) (This)->lpVtbl->ClientToWindow(This,pcx,pcy)
#define IWebBrowserApp_PutProperty(This,Property,vtValue) (This)->lpVtbl->PutProperty(This,Property,vtValue)
#define IWebBrowserApp_GetProperty(This,Property,pvtValue) (This)->lpVtbl->GetProperty(This,Property,pvtValue)
#define IWebBrowserApp_get_Name(This,Name) (This)->lpVtbl->get_Name(This,Name)
#define IWebBrowserApp_get_HWND(This,pHWND) (This)->lpVtbl->get_HWND(This,pHWND)
#define IWebBrowserApp_get_FullName(This,FullName) (This)->lpVtbl->get_FullName(This,FullName)
#define IWebBrowserApp_get_Path(This,Path) (This)->lpVtbl->get_Path(This,Path)
#define IWebBrowserApp_get_Visible(This,pBool) (This)->lpVtbl->get_Visible(This,pBool)
#define IWebBrowserApp_put_Visible(This,Value) (This)->lpVtbl->put_Visible(This,Value)
#define IWebBrowserApp_get_StatusBar(This,pBool) (This)->lpVtbl->get_StatusBar(This,pBool)
#define IWebBrowserApp_put_StatusBar(This,Value) (This)->lpVtbl->put_StatusBar(This,Value)
#define IWebBrowserApp_get_StatusText(This,StatusText) (This)->lpVtbl->get_StatusText(This,StatusText)
#define IWebBrowserApp_put_StatusText(This,StatusText) (This)->lpVtbl->put_StatusText(This,StatusText)
#define IWebBrowserApp_get_ToolBar(This,Value) (This)->lpVtbl->get_ToolBar(This,Value)
#define IWebBrowserApp_put_ToolBar(This,Value) (This)->lpVtbl->put_ToolBar(This,Value)
#define IWebBrowserApp_get_MenuBar(This,Value) (This)->lpVtbl->get_MenuBar(This,Value)
#define IWebBrowserApp_put_MenuBar(This,Value) (This)->lpVtbl->put_MenuBar(This,Value)
#define IWebBrowserApp_get_FullScreen(This,pbFullScreen) (This)->lpVtbl->get_FullScreen(This,pbFullScreen)
#define IWebBrowserApp_put_FullScreen(This,bFullScreen) (This)->lpVtbl->put_FullScreen(This,bFullScreen)
#else
/*** IUnknown methods ***/
static inline HRESULT IWebBrowserApp_QueryInterface(IWebBrowserApp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWebBrowserApp_AddRef(IWebBrowserApp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWebBrowserApp_Release(IWebBrowserApp* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWebBrowserApp_GetTypeInfoCount(IWebBrowserApp* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWebBrowserApp_GetTypeInfo(IWebBrowserApp* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWebBrowserApp_GetIDsOfNames(IWebBrowserApp* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWebBrowserApp_Invoke(IWebBrowserApp* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWebBrowser methods ***/
static inline HRESULT IWebBrowserApp_GoBack(IWebBrowserApp* This) {
    return This->lpVtbl->GoBack(This);
}
static inline HRESULT IWebBrowserApp_GoForward(IWebBrowserApp* This) {
    return This->lpVtbl->GoForward(This);
}
static inline HRESULT IWebBrowserApp_GoHome(IWebBrowserApp* This) {
    return This->lpVtbl->GoHome(This);
}
static inline HRESULT IWebBrowserApp_GoSearch(IWebBrowserApp* This) {
    return This->lpVtbl->GoSearch(This);
}
static inline HRESULT IWebBrowserApp_Navigate(IWebBrowserApp* This,BSTR URL,VARIANT *Flags,VARIANT *TargetFrameName,VARIANT *PostData,VARIANT *Headers) {
    return This->lpVtbl->Navigate(This,URL,Flags,TargetFrameName,PostData,Headers);
}
static inline HRESULT IWebBrowserApp_Refresh(IWebBrowserApp* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWebBrowserApp_Refresh2(IWebBrowserApp* This,VARIANT *Level) {
    return This->lpVtbl->Refresh2(This,Level);
}
static inline HRESULT IWebBrowserApp_Stop(IWebBrowserApp* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IWebBrowserApp_get_Application(IWebBrowserApp* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Application(This,ppDisp);
}
static inline HRESULT IWebBrowserApp_get_Parent(IWebBrowserApp* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Parent(This,ppDisp);
}
static inline HRESULT IWebBrowserApp_get_Container(IWebBrowserApp* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Container(This,ppDisp);
}
static inline HRESULT IWebBrowserApp_get_Document(IWebBrowserApp* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Document(This,ppDisp);
}
static inline HRESULT IWebBrowserApp_get_TopLevelContainer(IWebBrowserApp* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_TopLevelContainer(This,pBool);
}
static inline HRESULT IWebBrowserApp_get_Type(IWebBrowserApp* This,BSTR *Type) {
    return This->lpVtbl->get_Type(This,Type);
}
static inline HRESULT IWebBrowserApp_get_Left(IWebBrowserApp* This,LONG *pl) {
    return This->lpVtbl->get_Left(This,pl);
}
static inline HRESULT IWebBrowserApp_put_Left(IWebBrowserApp* This,LONG Left) {
    return This->lpVtbl->put_Left(This,Left);
}
static inline HRESULT IWebBrowserApp_get_Top(IWebBrowserApp* This,LONG *pl) {
    return This->lpVtbl->get_Top(This,pl);
}
static inline HRESULT IWebBrowserApp_put_Top(IWebBrowserApp* This,LONG Top) {
    return This->lpVtbl->put_Top(This,Top);
}
static inline HRESULT IWebBrowserApp_get_Width(IWebBrowserApp* This,LONG *pl) {
    return This->lpVtbl->get_Width(This,pl);
}
static inline HRESULT IWebBrowserApp_put_Width(IWebBrowserApp* This,LONG Width) {
    return This->lpVtbl->put_Width(This,Width);
}
static inline HRESULT IWebBrowserApp_get_Height(IWebBrowserApp* This,LONG *pl) {
    return This->lpVtbl->get_Height(This,pl);
}
static inline HRESULT IWebBrowserApp_put_Height(IWebBrowserApp* This,LONG Height) {
    return This->lpVtbl->put_Height(This,Height);
}
static inline HRESULT IWebBrowserApp_get_LocationName(IWebBrowserApp* This,BSTR *LocationName) {
    return This->lpVtbl->get_LocationName(This,LocationName);
}
static inline HRESULT IWebBrowserApp_get_LocationURL(IWebBrowserApp* This,BSTR *LocationURL) {
    return This->lpVtbl->get_LocationURL(This,LocationURL);
}
static inline HRESULT IWebBrowserApp_get_Busy(IWebBrowserApp* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_Busy(This,pBool);
}
/*** IWebBrowserApp methods ***/
static inline HRESULT IWebBrowserApp_Quit(IWebBrowserApp* This) {
    return This->lpVtbl->Quit(This);
}
static inline HRESULT IWebBrowserApp_ClientToWindow(IWebBrowserApp* This,int *pcx,int *pcy) {
    return This->lpVtbl->ClientToWindow(This,pcx,pcy);
}
static inline HRESULT IWebBrowserApp_PutProperty(IWebBrowserApp* This,BSTR Property,VARIANT vtValue) {
    return This->lpVtbl->PutProperty(This,Property,vtValue);
}
static inline HRESULT IWebBrowserApp_GetProperty(IWebBrowserApp* This,BSTR Property,VARIANT *pvtValue) {
    return This->lpVtbl->GetProperty(This,Property,pvtValue);
}
static inline HRESULT IWebBrowserApp_get_Name(IWebBrowserApp* This,BSTR *Name) {
    return This->lpVtbl->get_Name(This,Name);
}
static inline HRESULT IWebBrowserApp_get_HWND(IWebBrowserApp* This,SHANDLE_PTR *pHWND) {
    return This->lpVtbl->get_HWND(This,pHWND);
}
static inline HRESULT IWebBrowserApp_get_FullName(IWebBrowserApp* This,BSTR *FullName) {
    return This->lpVtbl->get_FullName(This,FullName);
}
static inline HRESULT IWebBrowserApp_get_Path(IWebBrowserApp* This,BSTR *Path) {
    return This->lpVtbl->get_Path(This,Path);
}
static inline HRESULT IWebBrowserApp_get_Visible(IWebBrowserApp* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_Visible(This,pBool);
}
static inline HRESULT IWebBrowserApp_put_Visible(IWebBrowserApp* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_Visible(This,Value);
}
static inline HRESULT IWebBrowserApp_get_StatusBar(IWebBrowserApp* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_StatusBar(This,pBool);
}
static inline HRESULT IWebBrowserApp_put_StatusBar(IWebBrowserApp* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_StatusBar(This,Value);
}
static inline HRESULT IWebBrowserApp_get_StatusText(IWebBrowserApp* This,BSTR *StatusText) {
    return This->lpVtbl->get_StatusText(This,StatusText);
}
static inline HRESULT IWebBrowserApp_put_StatusText(IWebBrowserApp* This,BSTR StatusText) {
    return This->lpVtbl->put_StatusText(This,StatusText);
}
static inline HRESULT IWebBrowserApp_get_ToolBar(IWebBrowserApp* This,int *Value) {
    return This->lpVtbl->get_ToolBar(This,Value);
}
static inline HRESULT IWebBrowserApp_put_ToolBar(IWebBrowserApp* This,int Value) {
    return This->lpVtbl->put_ToolBar(This,Value);
}
static inline HRESULT IWebBrowserApp_get_MenuBar(IWebBrowserApp* This,VARIANT_BOOL *Value) {
    return This->lpVtbl->get_MenuBar(This,Value);
}
static inline HRESULT IWebBrowserApp_put_MenuBar(IWebBrowserApp* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_MenuBar(This,Value);
}
static inline HRESULT IWebBrowserApp_get_FullScreen(IWebBrowserApp* This,VARIANT_BOOL *pbFullScreen) {
    return This->lpVtbl->get_FullScreen(This,pbFullScreen);
}
static inline HRESULT IWebBrowserApp_put_FullScreen(IWebBrowserApp* This,VARIANT_BOOL bFullScreen) {
    return This->lpVtbl->put_FullScreen(This,bFullScreen);
}
#endif
#endif

#endif


#endif  /* __IWebBrowserApp_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWebBrowser2 interface
 */
#ifndef __IWebBrowser2_INTERFACE_DEFINED__
#define __IWebBrowser2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWebBrowser2, 0xd30c1661, 0xcdaf, 0x11d0, 0x8a,0x3e, 0x00,0xc0,0x4f,0xc9,0xe2,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d30c1661-cdaf-11d0-8a3e-00c04fc9e26e")
IWebBrowser2 : public IWebBrowserApp
{
    virtual HRESULT STDMETHODCALLTYPE Navigate2(
        VARIANT *URL,
        VARIANT *Flags,
        VARIANT *TargetFrameName,
        VARIANT *PostData,
        VARIANT *Headers) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryStatusWB(
        OLECMDID cmdID,
        OLECMDF *pcmdf) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecWB(
        OLECMDID cmdID,
        OLECMDEXECOPT cmdexecopt,
        VARIANT *pvaIn,
        VARIANT *pvaOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowBrowserBar(
        VARIANT *pvaClsid,
        VARIANT *pvarShow,
        VARIANT *pvarSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReadyState(
        READYSTATE *plReadyState) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Offline(
        VARIANT_BOOL *pbOffline) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Offline(
        VARIANT_BOOL bOffline) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Silent(
        VARIANT_BOOL *pbSilent) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Silent(
        VARIANT_BOOL bSilent) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RegisterAsBrowser(
        VARIANT_BOOL *pbRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RegisterAsBrowser(
        VARIANT_BOOL bRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RegisterAsDropTarget(
        VARIANT_BOOL *pbRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RegisterAsDropTarget(
        VARIANT_BOOL bRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TheaterMode(
        VARIANT_BOOL *pbRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_TheaterMode(
        VARIANT_BOOL bRegister) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AddressBar(
        VARIANT_BOOL *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AddressBar(
        VARIANT_BOOL Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Resizable(
        VARIANT_BOOL *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Resizable(
        VARIANT_BOOL Value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWebBrowser2, 0xd30c1661, 0xcdaf, 0x11d0, 0x8a,0x3e, 0x00,0xc0,0x4f,0xc9,0xe2,0x6e)
#endif
#else
typedef struct IWebBrowser2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWebBrowser2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWebBrowser2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWebBrowser2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWebBrowser2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWebBrowser2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWebBrowser2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWebBrowser2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWebBrowser methods ***/
    HRESULT (STDMETHODCALLTYPE *GoBack)(
        IWebBrowser2 *This);

    HRESULT (STDMETHODCALLTYPE *GoForward)(
        IWebBrowser2 *This);

    HRESULT (STDMETHODCALLTYPE *GoHome)(
        IWebBrowser2 *This);

    HRESULT (STDMETHODCALLTYPE *GoSearch)(
        IWebBrowser2 *This);

    HRESULT (STDMETHODCALLTYPE *Navigate)(
        IWebBrowser2 *This,
        BSTR URL,
        VARIANT *Flags,
        VARIANT *TargetFrameName,
        VARIANT *PostData,
        VARIANT *Headers);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWebBrowser2 *This);

    HRESULT (STDMETHODCALLTYPE *Refresh2)(
        IWebBrowser2 *This,
        VARIANT *Level);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IWebBrowser2 *This);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IWebBrowser2 *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        IWebBrowser2 *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Container)(
        IWebBrowser2 *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_Document)(
        IWebBrowser2 *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *get_TopLevelContainer)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWebBrowser2 *This,
        BSTR *Type);

    HRESULT (STDMETHODCALLTYPE *get_Left)(
        IWebBrowser2 *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Left)(
        IWebBrowser2 *This,
        LONG Left);

    HRESULT (STDMETHODCALLTYPE *get_Top)(
        IWebBrowser2 *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Top)(
        IWebBrowser2 *This,
        LONG Top);

    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IWebBrowser2 *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Width)(
        IWebBrowser2 *This,
        LONG Width);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IWebBrowser2 *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *put_Height)(
        IWebBrowser2 *This,
        LONG Height);

    HRESULT (STDMETHODCALLTYPE *get_LocationName)(
        IWebBrowser2 *This,
        BSTR *LocationName);

    HRESULT (STDMETHODCALLTYPE *get_LocationURL)(
        IWebBrowser2 *This,
        BSTR *LocationURL);

    HRESULT (STDMETHODCALLTYPE *get_Busy)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pBool);

    /*** IWebBrowserApp methods ***/
    HRESULT (STDMETHODCALLTYPE *Quit)(
        IWebBrowser2 *This);

    HRESULT (STDMETHODCALLTYPE *ClientToWindow)(
        IWebBrowser2 *This,
        int *pcx,
        int *pcy);

    HRESULT (STDMETHODCALLTYPE *PutProperty)(
        IWebBrowser2 *This,
        BSTR Property,
        VARIANT vtValue);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IWebBrowser2 *This,
        BSTR Property,
        VARIANT *pvtValue);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWebBrowser2 *This,
        BSTR *Name);

    HRESULT (STDMETHODCALLTYPE *get_HWND)(
        IWebBrowser2 *This,
        SHANDLE_PTR *pHWND);

    HRESULT (STDMETHODCALLTYPE *get_FullName)(
        IWebBrowser2 *This,
        BSTR *FullName);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IWebBrowser2 *This,
        BSTR *Path);

    HRESULT (STDMETHODCALLTYPE *get_Visible)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *put_Visible)(
        IWebBrowser2 *This,
        VARIANT_BOOL Value);

    HRESULT (STDMETHODCALLTYPE *get_StatusBar)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *put_StatusBar)(
        IWebBrowser2 *This,
        VARIANT_BOOL Value);

    HRESULT (STDMETHODCALLTYPE *get_StatusText)(
        IWebBrowser2 *This,
        BSTR *StatusText);

    HRESULT (STDMETHODCALLTYPE *put_StatusText)(
        IWebBrowser2 *This,
        BSTR StatusText);

    HRESULT (STDMETHODCALLTYPE *get_ToolBar)(
        IWebBrowser2 *This,
        int *Value);

    HRESULT (STDMETHODCALLTYPE *put_ToolBar)(
        IWebBrowser2 *This,
        int Value);

    HRESULT (STDMETHODCALLTYPE *get_MenuBar)(
        IWebBrowser2 *This,
        VARIANT_BOOL *Value);

    HRESULT (STDMETHODCALLTYPE *put_MenuBar)(
        IWebBrowser2 *This,
        VARIANT_BOOL Value);

    HRESULT (STDMETHODCALLTYPE *get_FullScreen)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pbFullScreen);

    HRESULT (STDMETHODCALLTYPE *put_FullScreen)(
        IWebBrowser2 *This,
        VARIANT_BOOL bFullScreen);

    /*** IWebBrowser2 methods ***/
    HRESULT (STDMETHODCALLTYPE *Navigate2)(
        IWebBrowser2 *This,
        VARIANT *URL,
        VARIANT *Flags,
        VARIANT *TargetFrameName,
        VARIANT *PostData,
        VARIANT *Headers);

    HRESULT (STDMETHODCALLTYPE *QueryStatusWB)(
        IWebBrowser2 *This,
        OLECMDID cmdID,
        OLECMDF *pcmdf);

    HRESULT (STDMETHODCALLTYPE *ExecWB)(
        IWebBrowser2 *This,
        OLECMDID cmdID,
        OLECMDEXECOPT cmdexecopt,
        VARIANT *pvaIn,
        VARIANT *pvaOut);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserBar)(
        IWebBrowser2 *This,
        VARIANT *pvaClsid,
        VARIANT *pvarShow,
        VARIANT *pvarSize);

    HRESULT (STDMETHODCALLTYPE *get_ReadyState)(
        IWebBrowser2 *This,
        READYSTATE *plReadyState);

    HRESULT (STDMETHODCALLTYPE *get_Offline)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pbOffline);

    HRESULT (STDMETHODCALLTYPE *put_Offline)(
        IWebBrowser2 *This,
        VARIANT_BOOL bOffline);

    HRESULT (STDMETHODCALLTYPE *get_Silent)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pbSilent);

    HRESULT (STDMETHODCALLTYPE *put_Silent)(
        IWebBrowser2 *This,
        VARIANT_BOOL bSilent);

    HRESULT (STDMETHODCALLTYPE *get_RegisterAsBrowser)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pbRegister);

    HRESULT (STDMETHODCALLTYPE *put_RegisterAsBrowser)(
        IWebBrowser2 *This,
        VARIANT_BOOL bRegister);

    HRESULT (STDMETHODCALLTYPE *get_RegisterAsDropTarget)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pbRegister);

    HRESULT (STDMETHODCALLTYPE *put_RegisterAsDropTarget)(
        IWebBrowser2 *This,
        VARIANT_BOOL bRegister);

    HRESULT (STDMETHODCALLTYPE *get_TheaterMode)(
        IWebBrowser2 *This,
        VARIANT_BOOL *pbRegister);

    HRESULT (STDMETHODCALLTYPE *put_TheaterMode)(
        IWebBrowser2 *This,
        VARIANT_BOOL bRegister);

    HRESULT (STDMETHODCALLTYPE *get_AddressBar)(
        IWebBrowser2 *This,
        VARIANT_BOOL *Value);

    HRESULT (STDMETHODCALLTYPE *put_AddressBar)(
        IWebBrowser2 *This,
        VARIANT_BOOL Value);

    HRESULT (STDMETHODCALLTYPE *get_Resizable)(
        IWebBrowser2 *This,
        VARIANT_BOOL *Value);

    HRESULT (STDMETHODCALLTYPE *put_Resizable)(
        IWebBrowser2 *This,
        VARIANT_BOOL Value);

    END_INTERFACE
} IWebBrowser2Vtbl;

interface IWebBrowser2 {
    CONST_VTBL IWebBrowser2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWebBrowser2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWebBrowser2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWebBrowser2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWebBrowser2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWebBrowser2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWebBrowser2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWebBrowser2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWebBrowser methods ***/
#define IWebBrowser2_GoBack(This) (This)->lpVtbl->GoBack(This)
#define IWebBrowser2_GoForward(This) (This)->lpVtbl->GoForward(This)
#define IWebBrowser2_GoHome(This) (This)->lpVtbl->GoHome(This)
#define IWebBrowser2_GoSearch(This) (This)->lpVtbl->GoSearch(This)
#define IWebBrowser2_Navigate(This,URL,Flags,TargetFrameName,PostData,Headers) (This)->lpVtbl->Navigate(This,URL,Flags,TargetFrameName,PostData,Headers)
#define IWebBrowser2_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWebBrowser2_Refresh2(This,Level) (This)->lpVtbl->Refresh2(This,Level)
#define IWebBrowser2_Stop(This) (This)->lpVtbl->Stop(This)
#define IWebBrowser2_get_Application(This,ppDisp) (This)->lpVtbl->get_Application(This,ppDisp)
#define IWebBrowser2_get_Parent(This,ppDisp) (This)->lpVtbl->get_Parent(This,ppDisp)
#define IWebBrowser2_get_Container(This,ppDisp) (This)->lpVtbl->get_Container(This,ppDisp)
#define IWebBrowser2_get_Document(This,ppDisp) (This)->lpVtbl->get_Document(This,ppDisp)
#define IWebBrowser2_get_TopLevelContainer(This,pBool) (This)->lpVtbl->get_TopLevelContainer(This,pBool)
#define IWebBrowser2_get_Type(This,Type) (This)->lpVtbl->get_Type(This,Type)
#define IWebBrowser2_get_Left(This,pl) (This)->lpVtbl->get_Left(This,pl)
#define IWebBrowser2_put_Left(This,Left) (This)->lpVtbl->put_Left(This,Left)
#define IWebBrowser2_get_Top(This,pl) (This)->lpVtbl->get_Top(This,pl)
#define IWebBrowser2_put_Top(This,Top) (This)->lpVtbl->put_Top(This,Top)
#define IWebBrowser2_get_Width(This,pl) (This)->lpVtbl->get_Width(This,pl)
#define IWebBrowser2_put_Width(This,Width) (This)->lpVtbl->put_Width(This,Width)
#define IWebBrowser2_get_Height(This,pl) (This)->lpVtbl->get_Height(This,pl)
#define IWebBrowser2_put_Height(This,Height) (This)->lpVtbl->put_Height(This,Height)
#define IWebBrowser2_get_LocationName(This,LocationName) (This)->lpVtbl->get_LocationName(This,LocationName)
#define IWebBrowser2_get_LocationURL(This,LocationURL) (This)->lpVtbl->get_LocationURL(This,LocationURL)
#define IWebBrowser2_get_Busy(This,pBool) (This)->lpVtbl->get_Busy(This,pBool)
/*** IWebBrowserApp methods ***/
#define IWebBrowser2_Quit(This) (This)->lpVtbl->Quit(This)
#define IWebBrowser2_ClientToWindow(This,pcx,pcy) (This)->lpVtbl->ClientToWindow(This,pcx,pcy)
#define IWebBrowser2_PutProperty(This,Property,vtValue) (This)->lpVtbl->PutProperty(This,Property,vtValue)
#define IWebBrowser2_GetProperty(This,Property,pvtValue) (This)->lpVtbl->GetProperty(This,Property,pvtValue)
#define IWebBrowser2_get_Name(This,Name) (This)->lpVtbl->get_Name(This,Name)
#define IWebBrowser2_get_HWND(This,pHWND) (This)->lpVtbl->get_HWND(This,pHWND)
#define IWebBrowser2_get_FullName(This,FullName) (This)->lpVtbl->get_FullName(This,FullName)
#define IWebBrowser2_get_Path(This,Path) (This)->lpVtbl->get_Path(This,Path)
#define IWebBrowser2_get_Visible(This,pBool) (This)->lpVtbl->get_Visible(This,pBool)
#define IWebBrowser2_put_Visible(This,Value) (This)->lpVtbl->put_Visible(This,Value)
#define IWebBrowser2_get_StatusBar(This,pBool) (This)->lpVtbl->get_StatusBar(This,pBool)
#define IWebBrowser2_put_StatusBar(This,Value) (This)->lpVtbl->put_StatusBar(This,Value)
#define IWebBrowser2_get_StatusText(This,StatusText) (This)->lpVtbl->get_StatusText(This,StatusText)
#define IWebBrowser2_put_StatusText(This,StatusText) (This)->lpVtbl->put_StatusText(This,StatusText)
#define IWebBrowser2_get_ToolBar(This,Value) (This)->lpVtbl->get_ToolBar(This,Value)
#define IWebBrowser2_put_ToolBar(This,Value) (This)->lpVtbl->put_ToolBar(This,Value)
#define IWebBrowser2_get_MenuBar(This,Value) (This)->lpVtbl->get_MenuBar(This,Value)
#define IWebBrowser2_put_MenuBar(This,Value) (This)->lpVtbl->put_MenuBar(This,Value)
#define IWebBrowser2_get_FullScreen(This,pbFullScreen) (This)->lpVtbl->get_FullScreen(This,pbFullScreen)
#define IWebBrowser2_put_FullScreen(This,bFullScreen) (This)->lpVtbl->put_FullScreen(This,bFullScreen)
/*** IWebBrowser2 methods ***/
#define IWebBrowser2_Navigate2(This,URL,Flags,TargetFrameName,PostData,Headers) (This)->lpVtbl->Navigate2(This,URL,Flags,TargetFrameName,PostData,Headers)
#define IWebBrowser2_QueryStatusWB(This,cmdID,pcmdf) (This)->lpVtbl->QueryStatusWB(This,cmdID,pcmdf)
#define IWebBrowser2_ExecWB(This,cmdID,cmdexecopt,pvaIn,pvaOut) (This)->lpVtbl->ExecWB(This,cmdID,cmdexecopt,pvaIn,pvaOut)
#define IWebBrowser2_ShowBrowserBar(This,pvaClsid,pvarShow,pvarSize) (This)->lpVtbl->ShowBrowserBar(This,pvaClsid,pvarShow,pvarSize)
#define IWebBrowser2_get_ReadyState(This,plReadyState) (This)->lpVtbl->get_ReadyState(This,plReadyState)
#define IWebBrowser2_get_Offline(This,pbOffline) (This)->lpVtbl->get_Offline(This,pbOffline)
#define IWebBrowser2_put_Offline(This,bOffline) (This)->lpVtbl->put_Offline(This,bOffline)
#define IWebBrowser2_get_Silent(This,pbSilent) (This)->lpVtbl->get_Silent(This,pbSilent)
#define IWebBrowser2_put_Silent(This,bSilent) (This)->lpVtbl->put_Silent(This,bSilent)
#define IWebBrowser2_get_RegisterAsBrowser(This,pbRegister) (This)->lpVtbl->get_RegisterAsBrowser(This,pbRegister)
#define IWebBrowser2_put_RegisterAsBrowser(This,bRegister) (This)->lpVtbl->put_RegisterAsBrowser(This,bRegister)
#define IWebBrowser2_get_RegisterAsDropTarget(This,pbRegister) (This)->lpVtbl->get_RegisterAsDropTarget(This,pbRegister)
#define IWebBrowser2_put_RegisterAsDropTarget(This,bRegister) (This)->lpVtbl->put_RegisterAsDropTarget(This,bRegister)
#define IWebBrowser2_get_TheaterMode(This,pbRegister) (This)->lpVtbl->get_TheaterMode(This,pbRegister)
#define IWebBrowser2_put_TheaterMode(This,bRegister) (This)->lpVtbl->put_TheaterMode(This,bRegister)
#define IWebBrowser2_get_AddressBar(This,Value) (This)->lpVtbl->get_AddressBar(This,Value)
#define IWebBrowser2_put_AddressBar(This,Value) (This)->lpVtbl->put_AddressBar(This,Value)
#define IWebBrowser2_get_Resizable(This,Value) (This)->lpVtbl->get_Resizable(This,Value)
#define IWebBrowser2_put_Resizable(This,Value) (This)->lpVtbl->put_Resizable(This,Value)
#else
/*** IUnknown methods ***/
static inline HRESULT IWebBrowser2_QueryInterface(IWebBrowser2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWebBrowser2_AddRef(IWebBrowser2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWebBrowser2_Release(IWebBrowser2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWebBrowser2_GetTypeInfoCount(IWebBrowser2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWebBrowser2_GetTypeInfo(IWebBrowser2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWebBrowser2_GetIDsOfNames(IWebBrowser2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWebBrowser2_Invoke(IWebBrowser2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWebBrowser methods ***/
static inline HRESULT IWebBrowser2_GoBack(IWebBrowser2* This) {
    return This->lpVtbl->GoBack(This);
}
static inline HRESULT IWebBrowser2_GoForward(IWebBrowser2* This) {
    return This->lpVtbl->GoForward(This);
}
static inline HRESULT IWebBrowser2_GoHome(IWebBrowser2* This) {
    return This->lpVtbl->GoHome(This);
}
static inline HRESULT IWebBrowser2_GoSearch(IWebBrowser2* This) {
    return This->lpVtbl->GoSearch(This);
}
static inline HRESULT IWebBrowser2_Navigate(IWebBrowser2* This,BSTR URL,VARIANT *Flags,VARIANT *TargetFrameName,VARIANT *PostData,VARIANT *Headers) {
    return This->lpVtbl->Navigate(This,URL,Flags,TargetFrameName,PostData,Headers);
}
static inline HRESULT IWebBrowser2_Refresh(IWebBrowser2* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWebBrowser2_Refresh2(IWebBrowser2* This,VARIANT *Level) {
    return This->lpVtbl->Refresh2(This,Level);
}
static inline HRESULT IWebBrowser2_Stop(IWebBrowser2* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IWebBrowser2_get_Application(IWebBrowser2* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Application(This,ppDisp);
}
static inline HRESULT IWebBrowser2_get_Parent(IWebBrowser2* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Parent(This,ppDisp);
}
static inline HRESULT IWebBrowser2_get_Container(IWebBrowser2* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Container(This,ppDisp);
}
static inline HRESULT IWebBrowser2_get_Document(IWebBrowser2* This,IDispatch **ppDisp) {
    return This->lpVtbl->get_Document(This,ppDisp);
}
static inline HRESULT IWebBrowser2_get_TopLevelContainer(IWebBrowser2* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_TopLevelContainer(This,pBool);
}
static inline HRESULT IWebBrowser2_get_Type(IWebBrowser2* This,BSTR *Type) {
    return This->lpVtbl->get_Type(This,Type);
}
static inline HRESULT IWebBrowser2_get_Left(IWebBrowser2* This,LONG *pl) {
    return This->lpVtbl->get_Left(This,pl);
}
static inline HRESULT IWebBrowser2_put_Left(IWebBrowser2* This,LONG Left) {
    return This->lpVtbl->put_Left(This,Left);
}
static inline HRESULT IWebBrowser2_get_Top(IWebBrowser2* This,LONG *pl) {
    return This->lpVtbl->get_Top(This,pl);
}
static inline HRESULT IWebBrowser2_put_Top(IWebBrowser2* This,LONG Top) {
    return This->lpVtbl->put_Top(This,Top);
}
static inline HRESULT IWebBrowser2_get_Width(IWebBrowser2* This,LONG *pl) {
    return This->lpVtbl->get_Width(This,pl);
}
static inline HRESULT IWebBrowser2_put_Width(IWebBrowser2* This,LONG Width) {
    return This->lpVtbl->put_Width(This,Width);
}
static inline HRESULT IWebBrowser2_get_Height(IWebBrowser2* This,LONG *pl) {
    return This->lpVtbl->get_Height(This,pl);
}
static inline HRESULT IWebBrowser2_put_Height(IWebBrowser2* This,LONG Height) {
    return This->lpVtbl->put_Height(This,Height);
}
static inline HRESULT IWebBrowser2_get_LocationName(IWebBrowser2* This,BSTR *LocationName) {
    return This->lpVtbl->get_LocationName(This,LocationName);
}
static inline HRESULT IWebBrowser2_get_LocationURL(IWebBrowser2* This,BSTR *LocationURL) {
    return This->lpVtbl->get_LocationURL(This,LocationURL);
}
static inline HRESULT IWebBrowser2_get_Busy(IWebBrowser2* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_Busy(This,pBool);
}
/*** IWebBrowserApp methods ***/
static inline HRESULT IWebBrowser2_Quit(IWebBrowser2* This) {
    return This->lpVtbl->Quit(This);
}
static inline HRESULT IWebBrowser2_ClientToWindow(IWebBrowser2* This,int *pcx,int *pcy) {
    return This->lpVtbl->ClientToWindow(This,pcx,pcy);
}
static inline HRESULT IWebBrowser2_PutProperty(IWebBrowser2* This,BSTR Property,VARIANT vtValue) {
    return This->lpVtbl->PutProperty(This,Property,vtValue);
}
static inline HRESULT IWebBrowser2_GetProperty(IWebBrowser2* This,BSTR Property,VARIANT *pvtValue) {
    return This->lpVtbl->GetProperty(This,Property,pvtValue);
}
static inline HRESULT IWebBrowser2_get_Name(IWebBrowser2* This,BSTR *Name) {
    return This->lpVtbl->get_Name(This,Name);
}
static inline HRESULT IWebBrowser2_get_HWND(IWebBrowser2* This,SHANDLE_PTR *pHWND) {
    return This->lpVtbl->get_HWND(This,pHWND);
}
static inline HRESULT IWebBrowser2_get_FullName(IWebBrowser2* This,BSTR *FullName) {
    return This->lpVtbl->get_FullName(This,FullName);
}
static inline HRESULT IWebBrowser2_get_Path(IWebBrowser2* This,BSTR *Path) {
    return This->lpVtbl->get_Path(This,Path);
}
static inline HRESULT IWebBrowser2_get_Visible(IWebBrowser2* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_Visible(This,pBool);
}
static inline HRESULT IWebBrowser2_put_Visible(IWebBrowser2* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_Visible(This,Value);
}
static inline HRESULT IWebBrowser2_get_StatusBar(IWebBrowser2* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_StatusBar(This,pBool);
}
static inline HRESULT IWebBrowser2_put_StatusBar(IWebBrowser2* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_StatusBar(This,Value);
}
static inline HRESULT IWebBrowser2_get_StatusText(IWebBrowser2* This,BSTR *StatusText) {
    return This->lpVtbl->get_StatusText(This,StatusText);
}
static inline HRESULT IWebBrowser2_put_StatusText(IWebBrowser2* This,BSTR StatusText) {
    return This->lpVtbl->put_StatusText(This,StatusText);
}
static inline HRESULT IWebBrowser2_get_ToolBar(IWebBrowser2* This,int *Value) {
    return This->lpVtbl->get_ToolBar(This,Value);
}
static inline HRESULT IWebBrowser2_put_ToolBar(IWebBrowser2* This,int Value) {
    return This->lpVtbl->put_ToolBar(This,Value);
}
static inline HRESULT IWebBrowser2_get_MenuBar(IWebBrowser2* This,VARIANT_BOOL *Value) {
    return This->lpVtbl->get_MenuBar(This,Value);
}
static inline HRESULT IWebBrowser2_put_MenuBar(IWebBrowser2* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_MenuBar(This,Value);
}
static inline HRESULT IWebBrowser2_get_FullScreen(IWebBrowser2* This,VARIANT_BOOL *pbFullScreen) {
    return This->lpVtbl->get_FullScreen(This,pbFullScreen);
}
static inline HRESULT IWebBrowser2_put_FullScreen(IWebBrowser2* This,VARIANT_BOOL bFullScreen) {
    return This->lpVtbl->put_FullScreen(This,bFullScreen);
}
/*** IWebBrowser2 methods ***/
static inline HRESULT IWebBrowser2_Navigate2(IWebBrowser2* This,VARIANT *URL,VARIANT *Flags,VARIANT *TargetFrameName,VARIANT *PostData,VARIANT *Headers) {
    return This->lpVtbl->Navigate2(This,URL,Flags,TargetFrameName,PostData,Headers);
}
static inline HRESULT IWebBrowser2_QueryStatusWB(IWebBrowser2* This,OLECMDID cmdID,OLECMDF *pcmdf) {
    return This->lpVtbl->QueryStatusWB(This,cmdID,pcmdf);
}
static inline HRESULT IWebBrowser2_ExecWB(IWebBrowser2* This,OLECMDID cmdID,OLECMDEXECOPT cmdexecopt,VARIANT *pvaIn,VARIANT *pvaOut) {
    return This->lpVtbl->ExecWB(This,cmdID,cmdexecopt,pvaIn,pvaOut);
}
static inline HRESULT IWebBrowser2_ShowBrowserBar(IWebBrowser2* This,VARIANT *pvaClsid,VARIANT *pvarShow,VARIANT *pvarSize) {
    return This->lpVtbl->ShowBrowserBar(This,pvaClsid,pvarShow,pvarSize);
}
static inline HRESULT IWebBrowser2_get_ReadyState(IWebBrowser2* This,READYSTATE *plReadyState) {
    return This->lpVtbl->get_ReadyState(This,plReadyState);
}
static inline HRESULT IWebBrowser2_get_Offline(IWebBrowser2* This,VARIANT_BOOL *pbOffline) {
    return This->lpVtbl->get_Offline(This,pbOffline);
}
static inline HRESULT IWebBrowser2_put_Offline(IWebBrowser2* This,VARIANT_BOOL bOffline) {
    return This->lpVtbl->put_Offline(This,bOffline);
}
static inline HRESULT IWebBrowser2_get_Silent(IWebBrowser2* This,VARIANT_BOOL *pbSilent) {
    return This->lpVtbl->get_Silent(This,pbSilent);
}
static inline HRESULT IWebBrowser2_put_Silent(IWebBrowser2* This,VARIANT_BOOL bSilent) {
    return This->lpVtbl->put_Silent(This,bSilent);
}
static inline HRESULT IWebBrowser2_get_RegisterAsBrowser(IWebBrowser2* This,VARIANT_BOOL *pbRegister) {
    return This->lpVtbl->get_RegisterAsBrowser(This,pbRegister);
}
static inline HRESULT IWebBrowser2_put_RegisterAsBrowser(IWebBrowser2* This,VARIANT_BOOL bRegister) {
    return This->lpVtbl->put_RegisterAsBrowser(This,bRegister);
}
static inline HRESULT IWebBrowser2_get_RegisterAsDropTarget(IWebBrowser2* This,VARIANT_BOOL *pbRegister) {
    return This->lpVtbl->get_RegisterAsDropTarget(This,pbRegister);
}
static inline HRESULT IWebBrowser2_put_RegisterAsDropTarget(IWebBrowser2* This,VARIANT_BOOL bRegister) {
    return This->lpVtbl->put_RegisterAsDropTarget(This,bRegister);
}
static inline HRESULT IWebBrowser2_get_TheaterMode(IWebBrowser2* This,VARIANT_BOOL *pbRegister) {
    return This->lpVtbl->get_TheaterMode(This,pbRegister);
}
static inline HRESULT IWebBrowser2_put_TheaterMode(IWebBrowser2* This,VARIANT_BOOL bRegister) {
    return This->lpVtbl->put_TheaterMode(This,bRegister);
}
static inline HRESULT IWebBrowser2_get_AddressBar(IWebBrowser2* This,VARIANT_BOOL *Value) {
    return This->lpVtbl->get_AddressBar(This,Value);
}
static inline HRESULT IWebBrowser2_put_AddressBar(IWebBrowser2* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_AddressBar(This,Value);
}
static inline HRESULT IWebBrowser2_get_Resizable(IWebBrowser2* This,VARIANT_BOOL *Value) {
    return This->lpVtbl->get_Resizable(This,Value);
}
static inline HRESULT IWebBrowser2_put_Resizable(IWebBrowser2* This,VARIANT_BOOL Value) {
    return This->lpVtbl->put_Resizable(This,Value);
}
#endif
#endif

#endif


#endif  /* __IWebBrowser2_INTERFACE_DEFINED__ */

typedef enum SecureLockIconConstants {
    secureLockIconUnsecure = 0,
    secureLockIconMixed = 1,
    secureLockIconSecureUnknownBits = 2,
    secureLockIconSecure40Bit = 3,
    secureLockIconSecure56Bit = 4,
    secureLockIconSecureFortezza = 5,
    secureLockIconSecure128Bit = 6
} SecureLockIconConstants;
/*****************************************************************************
 * DWebBrowserEvents2 dispinterface
 */
#ifndef __DWebBrowserEvents2_DISPINTERFACE_DEFINED__
#define __DWebBrowserEvents2_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID_DWebBrowserEvents2, 0x34a715a0, 0x6587, 0x11d0, 0x92,0x4a, 0x00,0x20,0xaf,0xc7,0xac,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("34a715a0-6587-11d0-924a-0020afc7ac4d")
DWebBrowserEvents2 : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DWebBrowserEvents2, 0x34a715a0, 0x6587, 0x11d0, 0x92,0x4a, 0x00,0x20,0xaf,0xc7,0xac,0x4d)
#endif
#else
typedef struct DWebBrowserEvents2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        DWebBrowserEvents2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        DWebBrowserEvents2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        DWebBrowserEvents2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        DWebBrowserEvents2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        DWebBrowserEvents2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        DWebBrowserEvents2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        DWebBrowserEvents2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} DWebBrowserEvents2Vtbl;

interface DWebBrowserEvents2 {
    CONST_VTBL DWebBrowserEvents2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define DWebBrowserEvents2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define DWebBrowserEvents2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define DWebBrowserEvents2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define DWebBrowserEvents2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define DWebBrowserEvents2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define DWebBrowserEvents2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define DWebBrowserEvents2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT DWebBrowserEvents2_QueryInterface(DWebBrowserEvents2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG DWebBrowserEvents2_AddRef(DWebBrowserEvents2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG DWebBrowserEvents2_Release(DWebBrowserEvents2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT DWebBrowserEvents2_GetTypeInfoCount(DWebBrowserEvents2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT DWebBrowserEvents2_GetTypeInfo(DWebBrowserEvents2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT DWebBrowserEvents2_GetIDsOfNames(DWebBrowserEvents2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT DWebBrowserEvents2_Invoke(DWebBrowserEvents2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* __DWebBrowserEvents2_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * WebBrowser_V1 coclass
 */

DEFINE_GUID(CLSID_WebBrowser_V1, 0xeab22ac3, 0x30c1, 0x11cf, 0xa7,0xeb, 0x00,0x00,0xc0,0x5b,0xae,0x0b);

#ifdef __cplusplus
class DECLSPEC_UUID("eab22ac3-30c1-11cf-a7eb-0000c05bae0b") WebBrowser_V1;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WebBrowser_V1, 0xeab22ac3, 0x30c1, 0x11cf, 0xa7,0xeb, 0x00,0x00,0xc0,0x5b,0xae,0x0b)
#endif
#endif

/*****************************************************************************
 * WebBrowser coclass
 */

DEFINE_GUID(CLSID_WebBrowser, 0x8856f961, 0x340a, 0x11d0, 0xa9,0x6b, 0x00,0xc0,0x4f,0xd7,0x05,0xa2);

#ifdef __cplusplus
class DECLSPEC_UUID("8856f961-340a-11d0-a96b-00c04fd705a2") WebBrowser;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WebBrowser, 0x8856f961, 0x340a, 0x11d0, 0xa9,0x6b, 0x00,0xc0,0x4f,0xd7,0x05,0xa2)
#endif
#endif

/*****************************************************************************
 * InternetExplorer coclass
 */

DEFINE_GUID(CLSID_InternetExplorer, 0x0002df01, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);

#ifdef __cplusplus
class DECLSPEC_UUID("0002df01-0000-0000-c000-000000000046") InternetExplorer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(InternetExplorer, 0x0002df01, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#endif

/*****************************************************************************
 * ShellBrowserWindow coclass
 */

DEFINE_GUID(CLSID_ShellBrowserWindow, 0xc08afd90, 0xf2a1, 0x11d1, 0x84,0x55, 0x00,0xa0,0xc9,0x1f,0x38,0x80);

#ifdef __cplusplus
class DECLSPEC_UUID("c08afd90-f2a1-11d1-8455-00a0c91f3880") ShellBrowserWindow;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellBrowserWindow, 0xc08afd90, 0xf2a1, 0x11d1, 0x84,0x55, 0x00,0xa0,0xc9,0x1f,0x38,0x80)
#endif
#endif

typedef enum ShellWindowTypeConstants {
    SWC_EXPLORER = 0,
    SWC_BROWSER = 1,
    SWC_3RDPARTY = 2,
    SWC_CALLBACK = 4,
    SWC_DESKTOP = 8
} ShellWindowTypeConstants;
typedef enum ShellWindowFindWindowOptions {
    SWFO_NEEDDISPATCH = 1,
    SWFO_INCLUDEPENDING = 2,
    SWFO_COOKIEPASSED = 4
} ShellWindowFindWindowOptions;
/*****************************************************************************
 * DShellWindowsEvents dispinterface
 */
#ifndef __DShellWindowsEvents_DISPINTERFACE_DEFINED__
#define __DShellWindowsEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID_DShellWindowsEvents, 0xfe4106e0, 0x399a, 0x11d0, 0xa4,0x8c, 0x00,0xa0,0xc9,0x0a,0x8f,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fe4106e0-399a-11d0-a48c-00a0c90a8f39")
DShellWindowsEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DShellWindowsEvents, 0xfe4106e0, 0x399a, 0x11d0, 0xa4,0x8c, 0x00,0xa0,0xc9,0x0a,0x8f,0x39)
#endif
#else
typedef struct DShellWindowsEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        DShellWindowsEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        DShellWindowsEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        DShellWindowsEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        DShellWindowsEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        DShellWindowsEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        DShellWindowsEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        DShellWindowsEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} DShellWindowsEventsVtbl;

interface DShellWindowsEvents {
    CONST_VTBL DShellWindowsEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define DShellWindowsEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define DShellWindowsEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define DShellWindowsEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define DShellWindowsEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define DShellWindowsEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define DShellWindowsEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define DShellWindowsEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT DShellWindowsEvents_QueryInterface(DShellWindowsEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG DShellWindowsEvents_AddRef(DShellWindowsEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG DShellWindowsEvents_Release(DShellWindowsEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT DShellWindowsEvents_GetTypeInfoCount(DShellWindowsEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT DShellWindowsEvents_GetTypeInfo(DShellWindowsEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT DShellWindowsEvents_GetIDsOfNames(DShellWindowsEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT DShellWindowsEvents_Invoke(DShellWindowsEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* __DShellWindowsEvents_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellWindows interface
 */
#ifndef __IShellWindows_INTERFACE_DEFINED__
#define __IShellWindows_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellWindows, 0x85cb6900, 0x4d95, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("85cb6900-4d95-11cf-960c-0080c7f4ee85")
IShellWindows : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *Count) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        VARIANT index,
        IDispatch **Folder) = 0;

    virtual HRESULT STDMETHODCALLTYPE _NewEnum(
        IUnknown **ppunk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Register(
        IDispatch *pid,
        LONG hWnd,
        int swClass,
        LONG *plCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterPending(
        LONG lThreadId,
        VARIANT *pvarloc,
        VARIANT *pvarlocRoot,
        int swClass,
        LONG *plCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Revoke(
        LONG lCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnNavigate(
        LONG lCookie,
        VARIANT *pvarLoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnActivated(
        LONG lCookie,
        VARIANT_BOOL fActive) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindWindowSW(
        VARIANT *pvarLoc,
        VARIANT *pvarLocRoot,
        int swClass,
        LONG *phwnd,
        int swfwOptions,
        IDispatch **ppdispOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnCreated(
        LONG lCookie,
        IUnknown *punk) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessAttachDetach(
        VARIANT_BOOL fAttach) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellWindows, 0x85cb6900, 0x4d95, 0x11cf, 0x96,0x0c, 0x00,0x80,0xc7,0xf4,0xee,0x85)
#endif
#else
typedef struct IShellWindowsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellWindows *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellWindows *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellWindows *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellWindows *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellWindows *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellWindows *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellWindows *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellWindows methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IShellWindows *This,
        LONG *Count);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IShellWindows *This,
        VARIANT index,
        IDispatch **Folder);

    HRESULT (STDMETHODCALLTYPE *_NewEnum)(
        IShellWindows *This,
        IUnknown **ppunk);

    HRESULT (STDMETHODCALLTYPE *Register)(
        IShellWindows *This,
        IDispatch *pid,
        LONG hWnd,
        int swClass,
        LONG *plCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterPending)(
        IShellWindows *This,
        LONG lThreadId,
        VARIANT *pvarloc,
        VARIANT *pvarlocRoot,
        int swClass,
        LONG *plCookie);

    HRESULT (STDMETHODCALLTYPE *Revoke)(
        IShellWindows *This,
        LONG lCookie);

    HRESULT (STDMETHODCALLTYPE *OnNavigate)(
        IShellWindows *This,
        LONG lCookie,
        VARIANT *pvarLoc);

    HRESULT (STDMETHODCALLTYPE *OnActivated)(
        IShellWindows *This,
        LONG lCookie,
        VARIANT_BOOL fActive);

    HRESULT (STDMETHODCALLTYPE *FindWindowSW)(
        IShellWindows *This,
        VARIANT *pvarLoc,
        VARIANT *pvarLocRoot,
        int swClass,
        LONG *phwnd,
        int swfwOptions,
        IDispatch **ppdispOut);

    HRESULT (STDMETHODCALLTYPE *OnCreated)(
        IShellWindows *This,
        LONG lCookie,
        IUnknown *punk);

    HRESULT (STDMETHODCALLTYPE *ProcessAttachDetach)(
        IShellWindows *This,
        VARIANT_BOOL fAttach);

    END_INTERFACE
} IShellWindowsVtbl;

interface IShellWindows {
    CONST_VTBL IShellWindowsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellWindows_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellWindows_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellWindows_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellWindows_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellWindows_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellWindows_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellWindows_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellWindows methods ***/
#define IShellWindows_get_Count(This,Count) (This)->lpVtbl->get_Count(This,Count)
#define IShellWindows_Item(This,index,Folder) (This)->lpVtbl->Item(This,index,Folder)
#define IShellWindows__NewEnum(This,ppunk) (This)->lpVtbl->_NewEnum(This,ppunk)
#define IShellWindows_Register(This,pid,hWnd,swClass,plCookie) (This)->lpVtbl->Register(This,pid,hWnd,swClass,plCookie)
#define IShellWindows_RegisterPending(This,lThreadId,pvarloc,pvarlocRoot,swClass,plCookie) (This)->lpVtbl->RegisterPending(This,lThreadId,pvarloc,pvarlocRoot,swClass,plCookie)
#define IShellWindows_Revoke(This,lCookie) (This)->lpVtbl->Revoke(This,lCookie)
#define IShellWindows_OnNavigate(This,lCookie,pvarLoc) (This)->lpVtbl->OnNavigate(This,lCookie,pvarLoc)
#define IShellWindows_OnActivated(This,lCookie,fActive) (This)->lpVtbl->OnActivated(This,lCookie,fActive)
#define IShellWindows_FindWindowSW(This,pvarLoc,pvarLocRoot,swClass,phwnd,swfwOptions,ppdispOut) (This)->lpVtbl->FindWindowSW(This,pvarLoc,pvarLocRoot,swClass,phwnd,swfwOptions,ppdispOut)
#define IShellWindows_OnCreated(This,lCookie,punk) (This)->lpVtbl->OnCreated(This,lCookie,punk)
#define IShellWindows_ProcessAttachDetach(This,fAttach) (This)->lpVtbl->ProcessAttachDetach(This,fAttach)
#else
/*** IUnknown methods ***/
static inline HRESULT IShellWindows_QueryInterface(IShellWindows* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IShellWindows_AddRef(IShellWindows* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IShellWindows_Release(IShellWindows* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IShellWindows_GetTypeInfoCount(IShellWindows* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IShellWindows_GetTypeInfo(IShellWindows* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IShellWindows_GetIDsOfNames(IShellWindows* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IShellWindows_Invoke(IShellWindows* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellWindows methods ***/
static inline HRESULT IShellWindows_get_Count(IShellWindows* This,LONG *Count) {
    return This->lpVtbl->get_Count(This,Count);
}
static inline HRESULT IShellWindows_Item(IShellWindows* This,VARIANT index,IDispatch **Folder) {
    return This->lpVtbl->Item(This,index,Folder);
}
static inline HRESULT IShellWindows__NewEnum(IShellWindows* This,IUnknown **ppunk) {
    return This->lpVtbl->_NewEnum(This,ppunk);
}
static inline HRESULT IShellWindows_Register(IShellWindows* This,IDispatch *pid,LONG hWnd,int swClass,LONG *plCookie) {
    return This->lpVtbl->Register(This,pid,hWnd,swClass,plCookie);
}
static inline HRESULT IShellWindows_RegisterPending(IShellWindows* This,LONG lThreadId,VARIANT *pvarloc,VARIANT *pvarlocRoot,int swClass,LONG *plCookie) {
    return This->lpVtbl->RegisterPending(This,lThreadId,pvarloc,pvarlocRoot,swClass,plCookie);
}
static inline HRESULT IShellWindows_Revoke(IShellWindows* This,LONG lCookie) {
    return This->lpVtbl->Revoke(This,lCookie);
}
static inline HRESULT IShellWindows_OnNavigate(IShellWindows* This,LONG lCookie,VARIANT *pvarLoc) {
    return This->lpVtbl->OnNavigate(This,lCookie,pvarLoc);
}
static inline HRESULT IShellWindows_OnActivated(IShellWindows* This,LONG lCookie,VARIANT_BOOL fActive) {
    return This->lpVtbl->OnActivated(This,lCookie,fActive);
}
static inline HRESULT IShellWindows_FindWindowSW(IShellWindows* This,VARIANT *pvarLoc,VARIANT *pvarLocRoot,int swClass,LONG *phwnd,int swfwOptions,IDispatch **ppdispOut) {
    return This->lpVtbl->FindWindowSW(This,pvarLoc,pvarLocRoot,swClass,phwnd,swfwOptions,ppdispOut);
}
static inline HRESULT IShellWindows_OnCreated(IShellWindows* This,LONG lCookie,IUnknown *punk) {
    return This->lpVtbl->OnCreated(This,lCookie,punk);
}
static inline HRESULT IShellWindows_ProcessAttachDetach(IShellWindows* This,VARIANT_BOOL fAttach) {
    return This->lpVtbl->ProcessAttachDetach(This,fAttach);
}
#endif
#endif

#endif


#endif  /* __IShellWindows_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellWindows coclass
 */

DEFINE_GUID(CLSID_ShellWindows, 0x9ba05972, 0xf6a8, 0x11cf, 0xa4,0x42, 0x00,0xa0,0xc9,0x0a,0x8f,0x39);

#ifdef __cplusplus
class DECLSPEC_UUID("9ba05972-f6a8-11cf-a442-00a0c90a8f39") ShellWindows;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellWindows, 0x9ba05972, 0xf6a8, 0x11cf, 0xa4,0x42, 0x00,0xa0,0xc9,0x0a,0x8f,0x39)
#endif
#endif

/*****************************************************************************
 * IShellUIHelper interface
 */
#ifndef __IShellUIHelper_INTERFACE_DEFINED__
#define __IShellUIHelper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellUIHelper, 0x729fe2f8, 0x1ea8, 0x11d1, 0x8f,0x85, 0x00,0xc0,0x4f,0xc2,0xfb,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("729fe2f8-1ea8-11d1-8f85-00c04fc2fbe1")
IShellUIHelper : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE ResetFirstBootMode(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetSafeMode(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshOfflineDesktop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddFavorite(
        BSTR URL,
        VARIANT *Title) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddChannel(
        BSTR URL) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDesktopComponent(
        BSTR URL,
        BSTR Type,
        VARIANT *Left,
        VARIANT *Top,
        VARIANT *Width,
        VARIANT *Height) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSubscribed(
        BSTR URL,
        VARIANT_BOOL *pBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE NavigateAndFind(
        BSTR URL,
        BSTR strQuery,
        VARIANT *varTargetFrame) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportExportFavorites(
        VARIANT_BOOL fImport,
        BSTR strImpExpPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE AutoCompleteSaveForm(
        VARIANT *Form) = 0;

    virtual HRESULT STDMETHODCALLTYPE AutoScan(
        BSTR strSearch,
        BSTR strFailureUrl,
        VARIANT *pvarTargetFrame) = 0;

    virtual HRESULT STDMETHODCALLTYPE AutoCompleteAttach(
        VARIANT *Reserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowBrowserUI(
        BSTR bstrName,
        VARIANT *pvarIn,
        VARIANT *pvarOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellUIHelper, 0x729fe2f8, 0x1ea8, 0x11d1, 0x8f,0x85, 0x00,0xc0,0x4f,0xc2,0xfb,0xe1)
#endif
#else
typedef struct IShellUIHelperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellUIHelper *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellUIHelper *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellUIHelper *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellUIHelper *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellUIHelper *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellUIHelper *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellUIHelper *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellUIHelper methods ***/
    HRESULT (STDMETHODCALLTYPE *ResetFirstBootMode)(
        IShellUIHelper *This);

    HRESULT (STDMETHODCALLTYPE *ResetSafeMode)(
        IShellUIHelper *This);

    HRESULT (STDMETHODCALLTYPE *RefreshOfflineDesktop)(
        IShellUIHelper *This);

    HRESULT (STDMETHODCALLTYPE *AddFavorite)(
        IShellUIHelper *This,
        BSTR URL,
        VARIANT *Title);

    HRESULT (STDMETHODCALLTYPE *AddChannel)(
        IShellUIHelper *This,
        BSTR URL);

    HRESULT (STDMETHODCALLTYPE *AddDesktopComponent)(
        IShellUIHelper *This,
        BSTR URL,
        BSTR Type,
        VARIANT *Left,
        VARIANT *Top,
        VARIANT *Width,
        VARIANT *Height);

    HRESULT (STDMETHODCALLTYPE *IsSubscribed)(
        IShellUIHelper *This,
        BSTR URL,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *NavigateAndFind)(
        IShellUIHelper *This,
        BSTR URL,
        BSTR strQuery,
        VARIANT *varTargetFrame);

    HRESULT (STDMETHODCALLTYPE *ImportExportFavorites)(
        IShellUIHelper *This,
        VARIANT_BOOL fImport,
        BSTR strImpExpPath);

    HRESULT (STDMETHODCALLTYPE *AutoCompleteSaveForm)(
        IShellUIHelper *This,
        VARIANT *Form);

    HRESULT (STDMETHODCALLTYPE *AutoScan)(
        IShellUIHelper *This,
        BSTR strSearch,
        BSTR strFailureUrl,
        VARIANT *pvarTargetFrame);

    HRESULT (STDMETHODCALLTYPE *AutoCompleteAttach)(
        IShellUIHelper *This,
        VARIANT *Reserved);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserUI)(
        IShellUIHelper *This,
        BSTR bstrName,
        VARIANT *pvarIn,
        VARIANT *pvarOut);

    END_INTERFACE
} IShellUIHelperVtbl;

interface IShellUIHelper {
    CONST_VTBL IShellUIHelperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellUIHelper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellUIHelper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellUIHelper_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellUIHelper_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellUIHelper_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellUIHelper_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellUIHelper_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellUIHelper methods ***/
#define IShellUIHelper_ResetFirstBootMode(This) (This)->lpVtbl->ResetFirstBootMode(This)
#define IShellUIHelper_ResetSafeMode(This) (This)->lpVtbl->ResetSafeMode(This)
#define IShellUIHelper_RefreshOfflineDesktop(This) (This)->lpVtbl->RefreshOfflineDesktop(This)
#define IShellUIHelper_AddFavorite(This,URL,Title) (This)->lpVtbl->AddFavorite(This,URL,Title)
#define IShellUIHelper_AddChannel(This,URL) (This)->lpVtbl->AddChannel(This,URL)
#define IShellUIHelper_AddDesktopComponent(This,URL,Type,Left,Top,Width,Height) (This)->lpVtbl->AddDesktopComponent(This,URL,Type,Left,Top,Width,Height)
#define IShellUIHelper_IsSubscribed(This,URL,pBool) (This)->lpVtbl->IsSubscribed(This,URL,pBool)
#define IShellUIHelper_NavigateAndFind(This,URL,strQuery,varTargetFrame) (This)->lpVtbl->NavigateAndFind(This,URL,strQuery,varTargetFrame)
#define IShellUIHelper_ImportExportFavorites(This,fImport,strImpExpPath) (This)->lpVtbl->ImportExportFavorites(This,fImport,strImpExpPath)
#define IShellUIHelper_AutoCompleteSaveForm(This,Form) (This)->lpVtbl->AutoCompleteSaveForm(This,Form)
#define IShellUIHelper_AutoScan(This,strSearch,strFailureUrl,pvarTargetFrame) (This)->lpVtbl->AutoScan(This,strSearch,strFailureUrl,pvarTargetFrame)
#define IShellUIHelper_AutoCompleteAttach(This,Reserved) (This)->lpVtbl->AutoCompleteAttach(This,Reserved)
#define IShellUIHelper_ShowBrowserUI(This,bstrName,pvarIn,pvarOut) (This)->lpVtbl->ShowBrowserUI(This,bstrName,pvarIn,pvarOut)
#else
/*** IUnknown methods ***/
static inline HRESULT IShellUIHelper_QueryInterface(IShellUIHelper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IShellUIHelper_AddRef(IShellUIHelper* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IShellUIHelper_Release(IShellUIHelper* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IShellUIHelper_GetTypeInfoCount(IShellUIHelper* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IShellUIHelper_GetTypeInfo(IShellUIHelper* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IShellUIHelper_GetIDsOfNames(IShellUIHelper* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IShellUIHelper_Invoke(IShellUIHelper* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellUIHelper methods ***/
static inline HRESULT IShellUIHelper_ResetFirstBootMode(IShellUIHelper* This) {
    return This->lpVtbl->ResetFirstBootMode(This);
}
static inline HRESULT IShellUIHelper_ResetSafeMode(IShellUIHelper* This) {
    return This->lpVtbl->ResetSafeMode(This);
}
static inline HRESULT IShellUIHelper_RefreshOfflineDesktop(IShellUIHelper* This) {
    return This->lpVtbl->RefreshOfflineDesktop(This);
}
static inline HRESULT IShellUIHelper_AddFavorite(IShellUIHelper* This,BSTR URL,VARIANT *Title) {
    return This->lpVtbl->AddFavorite(This,URL,Title);
}
static inline HRESULT IShellUIHelper_AddChannel(IShellUIHelper* This,BSTR URL) {
    return This->lpVtbl->AddChannel(This,URL);
}
static inline HRESULT IShellUIHelper_AddDesktopComponent(IShellUIHelper* This,BSTR URL,BSTR Type,VARIANT *Left,VARIANT *Top,VARIANT *Width,VARIANT *Height) {
    return This->lpVtbl->AddDesktopComponent(This,URL,Type,Left,Top,Width,Height);
}
static inline HRESULT IShellUIHelper_IsSubscribed(IShellUIHelper* This,BSTR URL,VARIANT_BOOL *pBool) {
    return This->lpVtbl->IsSubscribed(This,URL,pBool);
}
static inline HRESULT IShellUIHelper_NavigateAndFind(IShellUIHelper* This,BSTR URL,BSTR strQuery,VARIANT *varTargetFrame) {
    return This->lpVtbl->NavigateAndFind(This,URL,strQuery,varTargetFrame);
}
static inline HRESULT IShellUIHelper_ImportExportFavorites(IShellUIHelper* This,VARIANT_BOOL fImport,BSTR strImpExpPath) {
    return This->lpVtbl->ImportExportFavorites(This,fImport,strImpExpPath);
}
static inline HRESULT IShellUIHelper_AutoCompleteSaveForm(IShellUIHelper* This,VARIANT *Form) {
    return This->lpVtbl->AutoCompleteSaveForm(This,Form);
}
static inline HRESULT IShellUIHelper_AutoScan(IShellUIHelper* This,BSTR strSearch,BSTR strFailureUrl,VARIANT *pvarTargetFrame) {
    return This->lpVtbl->AutoScan(This,strSearch,strFailureUrl,pvarTargetFrame);
}
static inline HRESULT IShellUIHelper_AutoCompleteAttach(IShellUIHelper* This,VARIANT *Reserved) {
    return This->lpVtbl->AutoCompleteAttach(This,Reserved);
}
static inline HRESULT IShellUIHelper_ShowBrowserUI(IShellUIHelper* This,BSTR bstrName,VARIANT *pvarIn,VARIANT *pvarOut) {
    return This->lpVtbl->ShowBrowserUI(This,bstrName,pvarIn,pvarOut);
}
#endif
#endif

#endif


#endif  /* __IShellUIHelper_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellUIHelper2 interface
 */
#ifndef __IShellUIHelper2_INTERFACE_DEFINED__
#define __IShellUIHelper2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellUIHelper2, 0xa7fe6eda, 0x1932, 0x4281, 0xb8,0x81, 0x87,0xb3,0x1b,0x8b,0xc5,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a7fe6eda-1932-4281-b881-87b31b8bc52c")
IShellUIHelper2 : public IShellUIHelper
{
    virtual HRESULT STDMETHODCALLTYPE AddSearchProvider(
        BSTR URL) = 0;

    virtual HRESULT STDMETHODCALLTYPE RunOnceShown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SkipRunOnce(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CustomizeSettings(
        VARIANT_BOOL fSQM,
        VARIANT_BOOL fPhishing,
        BSTR bstrLocale) = 0;

    virtual HRESULT STDMETHODCALLTYPE SqmEnabled(
        VARIANT_BOOL *pfEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE PhishingEnabled(
        VARIANT_BOOL *pfEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE BrandImageUri(
        BSTR *pbstrUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE SkipTabsWelcome(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DiagnoseConnection(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CustomizeClearType(
        VARIANT_BOOL fSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSearchProviderInstalled(
        BSTR URL,
        DWORD *pdwResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSearchMigrated(
        VARIANT_BOOL *pfMigrated) = 0;

    virtual HRESULT STDMETHODCALLTYPE DefaultSearchProvider(
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE RunOnceRequiredSettingsComplete(
        VARIANT_BOOL fComplete) = 0;

    virtual HRESULT STDMETHODCALLTYPE RunOnceHasShown(
        VARIANT_BOOL *pfShown) = 0;

    virtual HRESULT STDMETHODCALLTYPE SearchGuideUrl(
        BSTR *pbstrUrl) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellUIHelper2, 0xa7fe6eda, 0x1932, 0x4281, 0xb8,0x81, 0x87,0xb3,0x1b,0x8b,0xc5,0x2c)
#endif
#else
typedef struct IShellUIHelper2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellUIHelper2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellUIHelper2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellUIHelper2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellUIHelper2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellUIHelper2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellUIHelper2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellUIHelper2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellUIHelper methods ***/
    HRESULT (STDMETHODCALLTYPE *ResetFirstBootMode)(
        IShellUIHelper2 *This);

    HRESULT (STDMETHODCALLTYPE *ResetSafeMode)(
        IShellUIHelper2 *This);

    HRESULT (STDMETHODCALLTYPE *RefreshOfflineDesktop)(
        IShellUIHelper2 *This);

    HRESULT (STDMETHODCALLTYPE *AddFavorite)(
        IShellUIHelper2 *This,
        BSTR URL,
        VARIANT *Title);

    HRESULT (STDMETHODCALLTYPE *AddChannel)(
        IShellUIHelper2 *This,
        BSTR URL);

    HRESULT (STDMETHODCALLTYPE *AddDesktopComponent)(
        IShellUIHelper2 *This,
        BSTR URL,
        BSTR Type,
        VARIANT *Left,
        VARIANT *Top,
        VARIANT *Width,
        VARIANT *Height);

    HRESULT (STDMETHODCALLTYPE *IsSubscribed)(
        IShellUIHelper2 *This,
        BSTR URL,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *NavigateAndFind)(
        IShellUIHelper2 *This,
        BSTR URL,
        BSTR strQuery,
        VARIANT *varTargetFrame);

    HRESULT (STDMETHODCALLTYPE *ImportExportFavorites)(
        IShellUIHelper2 *This,
        VARIANT_BOOL fImport,
        BSTR strImpExpPath);

    HRESULT (STDMETHODCALLTYPE *AutoCompleteSaveForm)(
        IShellUIHelper2 *This,
        VARIANT *Form);

    HRESULT (STDMETHODCALLTYPE *AutoScan)(
        IShellUIHelper2 *This,
        BSTR strSearch,
        BSTR strFailureUrl,
        VARIANT *pvarTargetFrame);

    HRESULT (STDMETHODCALLTYPE *AutoCompleteAttach)(
        IShellUIHelper2 *This,
        VARIANT *Reserved);

    HRESULT (STDMETHODCALLTYPE *ShowBrowserUI)(
        IShellUIHelper2 *This,
        BSTR bstrName,
        VARIANT *pvarIn,
        VARIANT *pvarOut);

    /*** IShellUIHelper2 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddSearchProvider)(
        IShellUIHelper2 *This,
        BSTR URL);

    HRESULT (STDMETHODCALLTYPE *RunOnceShown)(
        IShellUIHelper2 *This);

    HRESULT (STDMETHODCALLTYPE *SkipRunOnce)(
        IShellUIHelper2 *This);

    HRESULT (STDMETHODCALLTYPE *CustomizeSettings)(
        IShellUIHelper2 *This,
        VARIANT_BOOL fSQM,
        VARIANT_BOOL fPhishing,
        BSTR bstrLocale);

    HRESULT (STDMETHODCALLTYPE *SqmEnabled)(
        IShellUIHelper2 *This,
        VARIANT_BOOL *pfEnabled);

    HRESULT (STDMETHODCALLTYPE *PhishingEnabled)(
        IShellUIHelper2 *This,
        VARIANT_BOOL *pfEnabled);

    HRESULT (STDMETHODCALLTYPE *BrandImageUri)(
        IShellUIHelper2 *This,
        BSTR *pbstrUri);

    HRESULT (STDMETHODCALLTYPE *SkipTabsWelcome)(
        IShellUIHelper2 *This);

    HRESULT (STDMETHODCALLTYPE *DiagnoseConnection)(
        IShellUIHelper2 *This);

    HRESULT (STDMETHODCALLTYPE *CustomizeClearType)(
        IShellUIHelper2 *This,
        VARIANT_BOOL fSet);

    HRESULT (STDMETHODCALLTYPE *IsSearchProviderInstalled)(
        IShellUIHelper2 *This,
        BSTR URL,
        DWORD *pdwResult);

    HRESULT (STDMETHODCALLTYPE *IsSearchMigrated)(
        IShellUIHelper2 *This,
        VARIANT_BOOL *pfMigrated);

    HRESULT (STDMETHODCALLTYPE *DefaultSearchProvider)(
        IShellUIHelper2 *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *RunOnceRequiredSettingsComplete)(
        IShellUIHelper2 *This,
        VARIANT_BOOL fComplete);

    HRESULT (STDMETHODCALLTYPE *RunOnceHasShown)(
        IShellUIHelper2 *This,
        VARIANT_BOOL *pfShown);

    HRESULT (STDMETHODCALLTYPE *SearchGuideUrl)(
        IShellUIHelper2 *This,
        BSTR *pbstrUrl);

    END_INTERFACE
} IShellUIHelper2Vtbl;

interface IShellUIHelper2 {
    CONST_VTBL IShellUIHelper2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellUIHelper2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellUIHelper2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellUIHelper2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellUIHelper2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellUIHelper2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellUIHelper2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellUIHelper2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellUIHelper methods ***/
#define IShellUIHelper2_ResetFirstBootMode(This) (This)->lpVtbl->ResetFirstBootMode(This)
#define IShellUIHelper2_ResetSafeMode(This) (This)->lpVtbl->ResetSafeMode(This)
#define IShellUIHelper2_RefreshOfflineDesktop(This) (This)->lpVtbl->RefreshOfflineDesktop(This)
#define IShellUIHelper2_AddFavorite(This,URL,Title) (This)->lpVtbl->AddFavorite(This,URL,Title)
#define IShellUIHelper2_AddChannel(This,URL) (This)->lpVtbl->AddChannel(This,URL)
#define IShellUIHelper2_AddDesktopComponent(This,URL,Type,Left,Top,Width,Height) (This)->lpVtbl->AddDesktopComponent(This,URL,Type,Left,Top,Width,Height)
#define IShellUIHelper2_IsSubscribed(This,URL,pBool) (This)->lpVtbl->IsSubscribed(This,URL,pBool)
#define IShellUIHelper2_NavigateAndFind(This,URL,strQuery,varTargetFrame) (This)->lpVtbl->NavigateAndFind(This,URL,strQuery,varTargetFrame)
#define IShellUIHelper2_ImportExportFavorites(This,fImport,strImpExpPath) (This)->lpVtbl->ImportExportFavorites(This,fImport,strImpExpPath)
#define IShellUIHelper2_AutoCompleteSaveForm(This,Form) (This)->lpVtbl->AutoCompleteSaveForm(This,Form)
#define IShellUIHelper2_AutoScan(This,strSearch,strFailureUrl,pvarTargetFrame) (This)->lpVtbl->AutoScan(This,strSearch,strFailureUrl,pvarTargetFrame)
#define IShellUIHelper2_AutoCompleteAttach(This,Reserved) (This)->lpVtbl->AutoCompleteAttach(This,Reserved)
#define IShellUIHelper2_ShowBrowserUI(This,bstrName,pvarIn,pvarOut) (This)->lpVtbl->ShowBrowserUI(This,bstrName,pvarIn,pvarOut)
/*** IShellUIHelper2 methods ***/
#define IShellUIHelper2_AddSearchProvider(This,URL) (This)->lpVtbl->AddSearchProvider(This,URL)
#define IShellUIHelper2_RunOnceShown(This) (This)->lpVtbl->RunOnceShown(This)
#define IShellUIHelper2_SkipRunOnce(This) (This)->lpVtbl->SkipRunOnce(This)
#define IShellUIHelper2_CustomizeSettings(This,fSQM,fPhishing,bstrLocale) (This)->lpVtbl->CustomizeSettings(This,fSQM,fPhishing,bstrLocale)
#define IShellUIHelper2_SqmEnabled(This,pfEnabled) (This)->lpVtbl->SqmEnabled(This,pfEnabled)
#define IShellUIHelper2_PhishingEnabled(This,pfEnabled) (This)->lpVtbl->PhishingEnabled(This,pfEnabled)
#define IShellUIHelper2_BrandImageUri(This,pbstrUri) (This)->lpVtbl->BrandImageUri(This,pbstrUri)
#define IShellUIHelper2_SkipTabsWelcome(This) (This)->lpVtbl->SkipTabsWelcome(This)
#define IShellUIHelper2_DiagnoseConnection(This) (This)->lpVtbl->DiagnoseConnection(This)
#define IShellUIHelper2_CustomizeClearType(This,fSet) (This)->lpVtbl->CustomizeClearType(This,fSet)
#define IShellUIHelper2_IsSearchProviderInstalled(This,URL,pdwResult) (This)->lpVtbl->IsSearchProviderInstalled(This,URL,pdwResult)
#define IShellUIHelper2_IsSearchMigrated(This,pfMigrated) (This)->lpVtbl->IsSearchMigrated(This,pfMigrated)
#define IShellUIHelper2_DefaultSearchProvider(This,pbstrName) (This)->lpVtbl->DefaultSearchProvider(This,pbstrName)
#define IShellUIHelper2_RunOnceRequiredSettingsComplete(This,fComplete) (This)->lpVtbl->RunOnceRequiredSettingsComplete(This,fComplete)
#define IShellUIHelper2_RunOnceHasShown(This,pfShown) (This)->lpVtbl->RunOnceHasShown(This,pfShown)
#define IShellUIHelper2_SearchGuideUrl(This,pbstrUrl) (This)->lpVtbl->SearchGuideUrl(This,pbstrUrl)
#else
/*** IUnknown methods ***/
static inline HRESULT IShellUIHelper2_QueryInterface(IShellUIHelper2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IShellUIHelper2_AddRef(IShellUIHelper2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IShellUIHelper2_Release(IShellUIHelper2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IShellUIHelper2_GetTypeInfoCount(IShellUIHelper2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IShellUIHelper2_GetTypeInfo(IShellUIHelper2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IShellUIHelper2_GetIDsOfNames(IShellUIHelper2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IShellUIHelper2_Invoke(IShellUIHelper2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellUIHelper methods ***/
static inline HRESULT IShellUIHelper2_ResetFirstBootMode(IShellUIHelper2* This) {
    return This->lpVtbl->ResetFirstBootMode(This);
}
static inline HRESULT IShellUIHelper2_ResetSafeMode(IShellUIHelper2* This) {
    return This->lpVtbl->ResetSafeMode(This);
}
static inline HRESULT IShellUIHelper2_RefreshOfflineDesktop(IShellUIHelper2* This) {
    return This->lpVtbl->RefreshOfflineDesktop(This);
}
static inline HRESULT IShellUIHelper2_AddFavorite(IShellUIHelper2* This,BSTR URL,VARIANT *Title) {
    return This->lpVtbl->AddFavorite(This,URL,Title);
}
static inline HRESULT IShellUIHelper2_AddChannel(IShellUIHelper2* This,BSTR URL) {
    return This->lpVtbl->AddChannel(This,URL);
}
static inline HRESULT IShellUIHelper2_AddDesktopComponent(IShellUIHelper2* This,BSTR URL,BSTR Type,VARIANT *Left,VARIANT *Top,VARIANT *Width,VARIANT *Height) {
    return This->lpVtbl->AddDesktopComponent(This,URL,Type,Left,Top,Width,Height);
}
static inline HRESULT IShellUIHelper2_IsSubscribed(IShellUIHelper2* This,BSTR URL,VARIANT_BOOL *pBool) {
    return This->lpVtbl->IsSubscribed(This,URL,pBool);
}
static inline HRESULT IShellUIHelper2_NavigateAndFind(IShellUIHelper2* This,BSTR URL,BSTR strQuery,VARIANT *varTargetFrame) {
    return This->lpVtbl->NavigateAndFind(This,URL,strQuery,varTargetFrame);
}
static inline HRESULT IShellUIHelper2_ImportExportFavorites(IShellUIHelper2* This,VARIANT_BOOL fImport,BSTR strImpExpPath) {
    return This->lpVtbl->ImportExportFavorites(This,fImport,strImpExpPath);
}
static inline HRESULT IShellUIHelper2_AutoCompleteSaveForm(IShellUIHelper2* This,VARIANT *Form) {
    return This->lpVtbl->AutoCompleteSaveForm(This,Form);
}
static inline HRESULT IShellUIHelper2_AutoScan(IShellUIHelper2* This,BSTR strSearch,BSTR strFailureUrl,VARIANT *pvarTargetFrame) {
    return This->lpVtbl->AutoScan(This,strSearch,strFailureUrl,pvarTargetFrame);
}
static inline HRESULT IShellUIHelper2_AutoCompleteAttach(IShellUIHelper2* This,VARIANT *Reserved) {
    return This->lpVtbl->AutoCompleteAttach(This,Reserved);
}
static inline HRESULT IShellUIHelper2_ShowBrowserUI(IShellUIHelper2* This,BSTR bstrName,VARIANT *pvarIn,VARIANT *pvarOut) {
    return This->lpVtbl->ShowBrowserUI(This,bstrName,pvarIn,pvarOut);
}
/*** IShellUIHelper2 methods ***/
static inline HRESULT IShellUIHelper2_AddSearchProvider(IShellUIHelper2* This,BSTR URL) {
    return This->lpVtbl->AddSearchProvider(This,URL);
}
static inline HRESULT IShellUIHelper2_RunOnceShown(IShellUIHelper2* This) {
    return This->lpVtbl->RunOnceShown(This);
}
static inline HRESULT IShellUIHelper2_SkipRunOnce(IShellUIHelper2* This) {
    return This->lpVtbl->SkipRunOnce(This);
}
static inline HRESULT IShellUIHelper2_CustomizeSettings(IShellUIHelper2* This,VARIANT_BOOL fSQM,VARIANT_BOOL fPhishing,BSTR bstrLocale) {
    return This->lpVtbl->CustomizeSettings(This,fSQM,fPhishing,bstrLocale);
}
static inline HRESULT IShellUIHelper2_SqmEnabled(IShellUIHelper2* This,VARIANT_BOOL *pfEnabled) {
    return This->lpVtbl->SqmEnabled(This,pfEnabled);
}
static inline HRESULT IShellUIHelper2_PhishingEnabled(IShellUIHelper2* This,VARIANT_BOOL *pfEnabled) {
    return This->lpVtbl->PhishingEnabled(This,pfEnabled);
}
static inline HRESULT IShellUIHelper2_BrandImageUri(IShellUIHelper2* This,BSTR *pbstrUri) {
    return This->lpVtbl->BrandImageUri(This,pbstrUri);
}
static inline HRESULT IShellUIHelper2_SkipTabsWelcome(IShellUIHelper2* This) {
    return This->lpVtbl->SkipTabsWelcome(This);
}
static inline HRESULT IShellUIHelper2_DiagnoseConnection(IShellUIHelper2* This) {
    return This->lpVtbl->DiagnoseConnection(This);
}
static inline HRESULT IShellUIHelper2_CustomizeClearType(IShellUIHelper2* This,VARIANT_BOOL fSet) {
    return This->lpVtbl->CustomizeClearType(This,fSet);
}
static inline HRESULT IShellUIHelper2_IsSearchProviderInstalled(IShellUIHelper2* This,BSTR URL,DWORD *pdwResult) {
    return This->lpVtbl->IsSearchProviderInstalled(This,URL,pdwResult);
}
static inline HRESULT IShellUIHelper2_IsSearchMigrated(IShellUIHelper2* This,VARIANT_BOOL *pfMigrated) {
    return This->lpVtbl->IsSearchMigrated(This,pfMigrated);
}
static inline HRESULT IShellUIHelper2_DefaultSearchProvider(IShellUIHelper2* This,BSTR *pbstrName) {
    return This->lpVtbl->DefaultSearchProvider(This,pbstrName);
}
static inline HRESULT IShellUIHelper2_RunOnceRequiredSettingsComplete(IShellUIHelper2* This,VARIANT_BOOL fComplete) {
    return This->lpVtbl->RunOnceRequiredSettingsComplete(This,fComplete);
}
static inline HRESULT IShellUIHelper2_RunOnceHasShown(IShellUIHelper2* This,VARIANT_BOOL *pfShown) {
    return This->lpVtbl->RunOnceHasShown(This,pfShown);
}
static inline HRESULT IShellUIHelper2_SearchGuideUrl(IShellUIHelper2* This,BSTR *pbstrUrl) {
    return This->lpVtbl->SearchGuideUrl(This,pbstrUrl);
}
#endif
#endif

#endif


#endif  /* __IShellUIHelper2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellUIHelper coclass
 */

DEFINE_GUID(CLSID_ShellUIHelper, 0x64ab4bb7, 0x111e, 0x11d1, 0x8f,0x79, 0x00,0xc0,0x4f,0xc2,0xfb,0xe1);

#ifdef __cplusplus
class DECLSPEC_UUID("64ab4bb7-111e-11d1-8f79-00c04fc2fbe1") ShellUIHelper;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellUIHelper, 0x64ab4bb7, 0x111e, 0x11d1, 0x8f,0x79, 0x00,0xc0,0x4f,0xc2,0xfb,0xe1)
#endif
#endif

/*****************************************************************************
 * DShellNameSpaceEvents dispinterface
 */
#ifndef __DShellNameSpaceEvents_DISPINTERFACE_DEFINED__
#define __DShellNameSpaceEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID_DShellNameSpaceEvents, 0x55136806, 0xb2de, 0x11d1, 0xb9,0xf2, 0x00,0xa0,0xc9,0x8b,0xc5,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("55136806-b2de-11d1-b9f2-00a0c98bc547")
DShellNameSpaceEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DShellNameSpaceEvents, 0x55136806, 0xb2de, 0x11d1, 0xb9,0xf2, 0x00,0xa0,0xc9,0x8b,0xc5,0x47)
#endif
#else
typedef struct DShellNameSpaceEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        DShellNameSpaceEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        DShellNameSpaceEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        DShellNameSpaceEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        DShellNameSpaceEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        DShellNameSpaceEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        DShellNameSpaceEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        DShellNameSpaceEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} DShellNameSpaceEventsVtbl;

interface DShellNameSpaceEvents {
    CONST_VTBL DShellNameSpaceEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define DShellNameSpaceEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define DShellNameSpaceEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define DShellNameSpaceEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define DShellNameSpaceEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define DShellNameSpaceEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define DShellNameSpaceEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define DShellNameSpaceEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT DShellNameSpaceEvents_QueryInterface(DShellNameSpaceEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG DShellNameSpaceEvents_AddRef(DShellNameSpaceEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG DShellNameSpaceEvents_Release(DShellNameSpaceEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT DShellNameSpaceEvents_GetTypeInfoCount(DShellNameSpaceEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT DShellNameSpaceEvents_GetTypeInfo(DShellNameSpaceEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT DShellNameSpaceEvents_GetIDsOfNames(DShellNameSpaceEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT DShellNameSpaceEvents_Invoke(DShellNameSpaceEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* __DShellNameSpaceEvents_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellFavoritesNameSpace interface
 */
#ifndef __IShellFavoritesNameSpace_INTERFACE_DEFINED__
#define __IShellFavoritesNameSpace_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellFavoritesNameSpace, 0x55136804, 0xb2de, 0x11d1, 0xb9,0xf2, 0x00,0xa0,0xc9,0x8b,0xc5,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("55136804-b2de-11d1-b9f2-00a0c98bc547")
IShellFavoritesNameSpace : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE MoveSelectionUp(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveSelectionDown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetSort(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE NewFolder(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Synchronize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Import(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Export(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE InvokeContextMenuCommand(
        BSTR strCommand) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveSelectionTo(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SubscriptionsEnabled(
        VARIANT_BOOL *pBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSubscriptionForSelection(
        VARIANT_BOOL *pBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteSubscriptionForSelection(
        VARIANT_BOOL *pBool) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRoot(
        BSTR bstrFullPath) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellFavoritesNameSpace, 0x55136804, 0xb2de, 0x11d1, 0xb9,0xf2, 0x00,0xa0,0xc9,0x8b,0xc5,0x47)
#endif
#else
typedef struct IShellFavoritesNameSpaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellFavoritesNameSpace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellFavoritesNameSpace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellFavoritesNameSpace *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellFavoritesNameSpace *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellFavoritesNameSpace *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellFavoritesNameSpace *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellFavoritesNameSpace *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellFavoritesNameSpace methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveSelectionUp)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *MoveSelectionDown)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *ResetSort)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *NewFolder)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *Synchronize)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *Import)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *Export)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *InvokeContextMenuCommand)(
        IShellFavoritesNameSpace *This,
        BSTR strCommand);

    HRESULT (STDMETHODCALLTYPE *MoveSelectionTo)(
        IShellFavoritesNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *get_SubscriptionsEnabled)(
        IShellFavoritesNameSpace *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *CreateSubscriptionForSelection)(
        IShellFavoritesNameSpace *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *DeleteSubscriptionForSelection)(
        IShellFavoritesNameSpace *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *SetRoot)(
        IShellFavoritesNameSpace *This,
        BSTR bstrFullPath);

    END_INTERFACE
} IShellFavoritesNameSpaceVtbl;

interface IShellFavoritesNameSpace {
    CONST_VTBL IShellFavoritesNameSpaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellFavoritesNameSpace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellFavoritesNameSpace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellFavoritesNameSpace_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellFavoritesNameSpace_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellFavoritesNameSpace_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellFavoritesNameSpace_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellFavoritesNameSpace_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellFavoritesNameSpace methods ***/
#define IShellFavoritesNameSpace_MoveSelectionUp(This) (This)->lpVtbl->MoveSelectionUp(This)
#define IShellFavoritesNameSpace_MoveSelectionDown(This) (This)->lpVtbl->MoveSelectionDown(This)
#define IShellFavoritesNameSpace_ResetSort(This) (This)->lpVtbl->ResetSort(This)
#define IShellFavoritesNameSpace_NewFolder(This) (This)->lpVtbl->NewFolder(This)
#define IShellFavoritesNameSpace_Synchronize(This) (This)->lpVtbl->Synchronize(This)
#define IShellFavoritesNameSpace_Import(This) (This)->lpVtbl->Import(This)
#define IShellFavoritesNameSpace_Export(This) (This)->lpVtbl->Export(This)
#define IShellFavoritesNameSpace_InvokeContextMenuCommand(This,strCommand) (This)->lpVtbl->InvokeContextMenuCommand(This,strCommand)
#define IShellFavoritesNameSpace_MoveSelectionTo(This) (This)->lpVtbl->MoveSelectionTo(This)
#define IShellFavoritesNameSpace_get_SubscriptionsEnabled(This,pBool) (This)->lpVtbl->get_SubscriptionsEnabled(This,pBool)
#define IShellFavoritesNameSpace_CreateSubscriptionForSelection(This,pBool) (This)->lpVtbl->CreateSubscriptionForSelection(This,pBool)
#define IShellFavoritesNameSpace_DeleteSubscriptionForSelection(This,pBool) (This)->lpVtbl->DeleteSubscriptionForSelection(This,pBool)
#define IShellFavoritesNameSpace_SetRoot(This,bstrFullPath) (This)->lpVtbl->SetRoot(This,bstrFullPath)
#else
/*** IUnknown methods ***/
static inline HRESULT IShellFavoritesNameSpace_QueryInterface(IShellFavoritesNameSpace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IShellFavoritesNameSpace_AddRef(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IShellFavoritesNameSpace_Release(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IShellFavoritesNameSpace_GetTypeInfoCount(IShellFavoritesNameSpace* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IShellFavoritesNameSpace_GetTypeInfo(IShellFavoritesNameSpace* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IShellFavoritesNameSpace_GetIDsOfNames(IShellFavoritesNameSpace* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IShellFavoritesNameSpace_Invoke(IShellFavoritesNameSpace* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellFavoritesNameSpace methods ***/
static inline HRESULT IShellFavoritesNameSpace_MoveSelectionUp(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->MoveSelectionUp(This);
}
static inline HRESULT IShellFavoritesNameSpace_MoveSelectionDown(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->MoveSelectionDown(This);
}
static inline HRESULT IShellFavoritesNameSpace_ResetSort(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->ResetSort(This);
}
static inline HRESULT IShellFavoritesNameSpace_NewFolder(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->NewFolder(This);
}
static inline HRESULT IShellFavoritesNameSpace_Synchronize(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->Synchronize(This);
}
static inline HRESULT IShellFavoritesNameSpace_Import(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->Import(This);
}
static inline HRESULT IShellFavoritesNameSpace_Export(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->Export(This);
}
static inline HRESULT IShellFavoritesNameSpace_InvokeContextMenuCommand(IShellFavoritesNameSpace* This,BSTR strCommand) {
    return This->lpVtbl->InvokeContextMenuCommand(This,strCommand);
}
static inline HRESULT IShellFavoritesNameSpace_MoveSelectionTo(IShellFavoritesNameSpace* This) {
    return This->lpVtbl->MoveSelectionTo(This);
}
static inline HRESULT IShellFavoritesNameSpace_get_SubscriptionsEnabled(IShellFavoritesNameSpace* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_SubscriptionsEnabled(This,pBool);
}
static inline HRESULT IShellFavoritesNameSpace_CreateSubscriptionForSelection(IShellFavoritesNameSpace* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->CreateSubscriptionForSelection(This,pBool);
}
static inline HRESULT IShellFavoritesNameSpace_DeleteSubscriptionForSelection(IShellFavoritesNameSpace* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->DeleteSubscriptionForSelection(This,pBool);
}
static inline HRESULT IShellFavoritesNameSpace_SetRoot(IShellFavoritesNameSpace* This,BSTR bstrFullPath) {
    return This->lpVtbl->SetRoot(This,bstrFullPath);
}
#endif
#endif

#endif


#endif  /* __IShellFavoritesNameSpace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IShellNameSpace interface
 */
#ifndef __IShellNameSpace_INTERFACE_DEFINED__
#define __IShellNameSpace_INTERFACE_DEFINED__

DEFINE_GUID(IID_IShellNameSpace, 0xe572d3c9, 0x37be, 0x4ae2, 0x82,0x5d, 0xd5,0x21,0x76,0x3e,0x31,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e572d3c9-37be-4ae2-825d-d521763e3108")
IShellNameSpace : public IShellFavoritesNameSpace
{
    virtual HRESULT STDMETHODCALLTYPE get_EnumOptions(
        LONG *pgrfEnumFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EnumOptions(
        LONG pgrfEnumFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SelectedItem(
        IDispatch **pItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SelectedItem(
        IDispatch *pItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Root(
        VARIANT *pvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Root(
        VARIANT pvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Depth(
        int *piDepth) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Depth(
        int piDepth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Mode(
        unsigned int *puMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Mode(
        unsigned int puMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Flags(
        ULONG *pdwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Flags(
        ULONG pdwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_TVFlags(
        ULONG dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TVFlags(
        ULONG *dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Columns(
        BSTR *bstrColumns) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Columns(
        BSTR bstrColumns) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CountViewTypes(
        int *piTypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetViewType(
        int iType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectedItems(
        IDispatch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Expand(
        VARIANT var,
        int iDepth) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnselectAll(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IShellNameSpace, 0xe572d3c9, 0x37be, 0x4ae2, 0x82,0x5d, 0xd5,0x21,0x76,0x3e,0x31,0x08)
#endif
#else
typedef struct IShellNameSpaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IShellNameSpace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IShellNameSpace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IShellNameSpace *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IShellNameSpace *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IShellNameSpace *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IShellNameSpace *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IShellNameSpace *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IShellFavoritesNameSpace methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveSelectionUp)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *MoveSelectionDown)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *ResetSort)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *NewFolder)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *Synchronize)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *Import)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *Export)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *InvokeContextMenuCommand)(
        IShellNameSpace *This,
        BSTR strCommand);

    HRESULT (STDMETHODCALLTYPE *MoveSelectionTo)(
        IShellNameSpace *This);

    HRESULT (STDMETHODCALLTYPE *get_SubscriptionsEnabled)(
        IShellNameSpace *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *CreateSubscriptionForSelection)(
        IShellNameSpace *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *DeleteSubscriptionForSelection)(
        IShellNameSpace *This,
        VARIANT_BOOL *pBool);

    HRESULT (STDMETHODCALLTYPE *SetRoot)(
        IShellNameSpace *This,
        BSTR bstrFullPath);

    /*** IShellNameSpace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_EnumOptions)(
        IShellNameSpace *This,
        LONG *pgrfEnumFlags);

    HRESULT (STDMETHODCALLTYPE *put_EnumOptions)(
        IShellNameSpace *This,
        LONG pgrfEnumFlags);

    HRESULT (STDMETHODCALLTYPE *get_SelectedItem)(
        IShellNameSpace *This,
        IDispatch **pItem);

    HRESULT (STDMETHODCALLTYPE *put_SelectedItem)(
        IShellNameSpace *This,
        IDispatch *pItem);

    HRESULT (STDMETHODCALLTYPE *get_Root)(
        IShellNameSpace *This,
        VARIANT *pvar);

    HRESULT (STDMETHODCALLTYPE *put_Root)(
        IShellNameSpace *This,
        VARIANT pvar);

    HRESULT (STDMETHODCALLTYPE *get_Depth)(
        IShellNameSpace *This,
        int *piDepth);

    HRESULT (STDMETHODCALLTYPE *put_Depth)(
        IShellNameSpace *This,
        int piDepth);

    HRESULT (STDMETHODCALLTYPE *get_Mode)(
        IShellNameSpace *This,
        unsigned int *puMode);

    HRESULT (STDMETHODCALLTYPE *put_Mode)(
        IShellNameSpace *This,
        unsigned int puMode);

    HRESULT (STDMETHODCALLTYPE *get_Flags)(
        IShellNameSpace *This,
        ULONG *pdwFlags);

    HRESULT (STDMETHODCALLTYPE *put_Flags)(
        IShellNameSpace *This,
        ULONG pdwFlags);

    HRESULT (STDMETHODCALLTYPE *put_TVFlags)(
        IShellNameSpace *This,
        ULONG dwFlags);

    HRESULT (STDMETHODCALLTYPE *get_TVFlags)(
        IShellNameSpace *This,
        ULONG *dwFlags);

    HRESULT (STDMETHODCALLTYPE *get_Columns)(
        IShellNameSpace *This,
        BSTR *bstrColumns);

    HRESULT (STDMETHODCALLTYPE *put_Columns)(
        IShellNameSpace *This,
        BSTR bstrColumns);

    HRESULT (STDMETHODCALLTYPE *get_CountViewTypes)(
        IShellNameSpace *This,
        int *piTypes);

    HRESULT (STDMETHODCALLTYPE *SetViewType)(
        IShellNameSpace *This,
        int iType);

    HRESULT (STDMETHODCALLTYPE *SelectedItems)(
        IShellNameSpace *This,
        IDispatch **ppid);

    HRESULT (STDMETHODCALLTYPE *Expand)(
        IShellNameSpace *This,
        VARIANT var,
        int iDepth);

    HRESULT (STDMETHODCALLTYPE *UnselectAll)(
        IShellNameSpace *This);

    END_INTERFACE
} IShellNameSpaceVtbl;

interface IShellNameSpace {
    CONST_VTBL IShellNameSpaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IShellNameSpace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IShellNameSpace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IShellNameSpace_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IShellNameSpace_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IShellNameSpace_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IShellNameSpace_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IShellNameSpace_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IShellFavoritesNameSpace methods ***/
#define IShellNameSpace_MoveSelectionUp(This) (This)->lpVtbl->MoveSelectionUp(This)
#define IShellNameSpace_MoveSelectionDown(This) (This)->lpVtbl->MoveSelectionDown(This)
#define IShellNameSpace_ResetSort(This) (This)->lpVtbl->ResetSort(This)
#define IShellNameSpace_NewFolder(This) (This)->lpVtbl->NewFolder(This)
#define IShellNameSpace_Synchronize(This) (This)->lpVtbl->Synchronize(This)
#define IShellNameSpace_Import(This) (This)->lpVtbl->Import(This)
#define IShellNameSpace_Export(This) (This)->lpVtbl->Export(This)
#define IShellNameSpace_InvokeContextMenuCommand(This,strCommand) (This)->lpVtbl->InvokeContextMenuCommand(This,strCommand)
#define IShellNameSpace_MoveSelectionTo(This) (This)->lpVtbl->MoveSelectionTo(This)
#define IShellNameSpace_get_SubscriptionsEnabled(This,pBool) (This)->lpVtbl->get_SubscriptionsEnabled(This,pBool)
#define IShellNameSpace_CreateSubscriptionForSelection(This,pBool) (This)->lpVtbl->CreateSubscriptionForSelection(This,pBool)
#define IShellNameSpace_DeleteSubscriptionForSelection(This,pBool) (This)->lpVtbl->DeleteSubscriptionForSelection(This,pBool)
#define IShellNameSpace_SetRoot(This,bstrFullPath) (This)->lpVtbl->SetRoot(This,bstrFullPath)
/*** IShellNameSpace methods ***/
#define IShellNameSpace_get_EnumOptions(This,pgrfEnumFlags) (This)->lpVtbl->get_EnumOptions(This,pgrfEnumFlags)
#define IShellNameSpace_put_EnumOptions(This,pgrfEnumFlags) (This)->lpVtbl->put_EnumOptions(This,pgrfEnumFlags)
#define IShellNameSpace_get_SelectedItem(This,pItem) (This)->lpVtbl->get_SelectedItem(This,pItem)
#define IShellNameSpace_put_SelectedItem(This,pItem) (This)->lpVtbl->put_SelectedItem(This,pItem)
#define IShellNameSpace_get_Root(This,pvar) (This)->lpVtbl->get_Root(This,pvar)
#define IShellNameSpace_put_Root(This,pvar) (This)->lpVtbl->put_Root(This,pvar)
#define IShellNameSpace_get_Depth(This,piDepth) (This)->lpVtbl->get_Depth(This,piDepth)
#define IShellNameSpace_put_Depth(This,piDepth) (This)->lpVtbl->put_Depth(This,piDepth)
#define IShellNameSpace_get_Mode(This,puMode) (This)->lpVtbl->get_Mode(This,puMode)
#define IShellNameSpace_put_Mode(This,puMode) (This)->lpVtbl->put_Mode(This,puMode)
#define IShellNameSpace_get_Flags(This,pdwFlags) (This)->lpVtbl->get_Flags(This,pdwFlags)
#define IShellNameSpace_put_Flags(This,pdwFlags) (This)->lpVtbl->put_Flags(This,pdwFlags)
#define IShellNameSpace_put_TVFlags(This,dwFlags) (This)->lpVtbl->put_TVFlags(This,dwFlags)
#define IShellNameSpace_get_TVFlags(This,dwFlags) (This)->lpVtbl->get_TVFlags(This,dwFlags)
#define IShellNameSpace_get_Columns(This,bstrColumns) (This)->lpVtbl->get_Columns(This,bstrColumns)
#define IShellNameSpace_put_Columns(This,bstrColumns) (This)->lpVtbl->put_Columns(This,bstrColumns)
#define IShellNameSpace_get_CountViewTypes(This,piTypes) (This)->lpVtbl->get_CountViewTypes(This,piTypes)
#define IShellNameSpace_SetViewType(This,iType) (This)->lpVtbl->SetViewType(This,iType)
#define IShellNameSpace_SelectedItems(This,ppid) (This)->lpVtbl->SelectedItems(This,ppid)
#define IShellNameSpace_Expand(This,var,iDepth) (This)->lpVtbl->Expand(This,var,iDepth)
#define IShellNameSpace_UnselectAll(This) (This)->lpVtbl->UnselectAll(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IShellNameSpace_QueryInterface(IShellNameSpace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IShellNameSpace_AddRef(IShellNameSpace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IShellNameSpace_Release(IShellNameSpace* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IShellNameSpace_GetTypeInfoCount(IShellNameSpace* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IShellNameSpace_GetTypeInfo(IShellNameSpace* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IShellNameSpace_GetIDsOfNames(IShellNameSpace* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IShellNameSpace_Invoke(IShellNameSpace* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IShellFavoritesNameSpace methods ***/
static inline HRESULT IShellNameSpace_MoveSelectionUp(IShellNameSpace* This) {
    return This->lpVtbl->MoveSelectionUp(This);
}
static inline HRESULT IShellNameSpace_MoveSelectionDown(IShellNameSpace* This) {
    return This->lpVtbl->MoveSelectionDown(This);
}
static inline HRESULT IShellNameSpace_ResetSort(IShellNameSpace* This) {
    return This->lpVtbl->ResetSort(This);
}
static inline HRESULT IShellNameSpace_NewFolder(IShellNameSpace* This) {
    return This->lpVtbl->NewFolder(This);
}
static inline HRESULT IShellNameSpace_Synchronize(IShellNameSpace* This) {
    return This->lpVtbl->Synchronize(This);
}
static inline HRESULT IShellNameSpace_Import(IShellNameSpace* This) {
    return This->lpVtbl->Import(This);
}
static inline HRESULT IShellNameSpace_Export(IShellNameSpace* This) {
    return This->lpVtbl->Export(This);
}
static inline HRESULT IShellNameSpace_InvokeContextMenuCommand(IShellNameSpace* This,BSTR strCommand) {
    return This->lpVtbl->InvokeContextMenuCommand(This,strCommand);
}
static inline HRESULT IShellNameSpace_MoveSelectionTo(IShellNameSpace* This) {
    return This->lpVtbl->MoveSelectionTo(This);
}
static inline HRESULT IShellNameSpace_get_SubscriptionsEnabled(IShellNameSpace* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->get_SubscriptionsEnabled(This,pBool);
}
static inline HRESULT IShellNameSpace_CreateSubscriptionForSelection(IShellNameSpace* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->CreateSubscriptionForSelection(This,pBool);
}
static inline HRESULT IShellNameSpace_DeleteSubscriptionForSelection(IShellNameSpace* This,VARIANT_BOOL *pBool) {
    return This->lpVtbl->DeleteSubscriptionForSelection(This,pBool);
}
static inline HRESULT IShellNameSpace_SetRoot(IShellNameSpace* This,BSTR bstrFullPath) {
    return This->lpVtbl->SetRoot(This,bstrFullPath);
}
/*** IShellNameSpace methods ***/
static inline HRESULT IShellNameSpace_get_EnumOptions(IShellNameSpace* This,LONG *pgrfEnumFlags) {
    return This->lpVtbl->get_EnumOptions(This,pgrfEnumFlags);
}
static inline HRESULT IShellNameSpace_put_EnumOptions(IShellNameSpace* This,LONG pgrfEnumFlags) {
    return This->lpVtbl->put_EnumOptions(This,pgrfEnumFlags);
}
static inline HRESULT IShellNameSpace_get_SelectedItem(IShellNameSpace* This,IDispatch **pItem) {
    return This->lpVtbl->get_SelectedItem(This,pItem);
}
static inline HRESULT IShellNameSpace_put_SelectedItem(IShellNameSpace* This,IDispatch *pItem) {
    return This->lpVtbl->put_SelectedItem(This,pItem);
}
static inline HRESULT IShellNameSpace_get_Root(IShellNameSpace* This,VARIANT *pvar) {
    return This->lpVtbl->get_Root(This,pvar);
}
static inline HRESULT IShellNameSpace_put_Root(IShellNameSpace* This,VARIANT pvar) {
    return This->lpVtbl->put_Root(This,pvar);
}
static inline HRESULT IShellNameSpace_get_Depth(IShellNameSpace* This,int *piDepth) {
    return This->lpVtbl->get_Depth(This,piDepth);
}
static inline HRESULT IShellNameSpace_put_Depth(IShellNameSpace* This,int piDepth) {
    return This->lpVtbl->put_Depth(This,piDepth);
}
static inline HRESULT IShellNameSpace_get_Mode(IShellNameSpace* This,unsigned int *puMode) {
    return This->lpVtbl->get_Mode(This,puMode);
}
static inline HRESULT IShellNameSpace_put_Mode(IShellNameSpace* This,unsigned int puMode) {
    return This->lpVtbl->put_Mode(This,puMode);
}
static inline HRESULT IShellNameSpace_get_Flags(IShellNameSpace* This,ULONG *pdwFlags) {
    return This->lpVtbl->get_Flags(This,pdwFlags);
}
static inline HRESULT IShellNameSpace_put_Flags(IShellNameSpace* This,ULONG pdwFlags) {
    return This->lpVtbl->put_Flags(This,pdwFlags);
}
static inline HRESULT IShellNameSpace_put_TVFlags(IShellNameSpace* This,ULONG dwFlags) {
    return This->lpVtbl->put_TVFlags(This,dwFlags);
}
static inline HRESULT IShellNameSpace_get_TVFlags(IShellNameSpace* This,ULONG *dwFlags) {
    return This->lpVtbl->get_TVFlags(This,dwFlags);
}
static inline HRESULT IShellNameSpace_get_Columns(IShellNameSpace* This,BSTR *bstrColumns) {
    return This->lpVtbl->get_Columns(This,bstrColumns);
}
static inline HRESULT IShellNameSpace_put_Columns(IShellNameSpace* This,BSTR bstrColumns) {
    return This->lpVtbl->put_Columns(This,bstrColumns);
}
static inline HRESULT IShellNameSpace_get_CountViewTypes(IShellNameSpace* This,int *piTypes) {
    return This->lpVtbl->get_CountViewTypes(This,piTypes);
}
static inline HRESULT IShellNameSpace_SetViewType(IShellNameSpace* This,int iType) {
    return This->lpVtbl->SetViewType(This,iType);
}
static inline HRESULT IShellNameSpace_SelectedItems(IShellNameSpace* This,IDispatch **ppid) {
    return This->lpVtbl->SelectedItems(This,ppid);
}
static inline HRESULT IShellNameSpace_Expand(IShellNameSpace* This,VARIANT var,int iDepth) {
    return This->lpVtbl->Expand(This,var,iDepth);
}
static inline HRESULT IShellNameSpace_UnselectAll(IShellNameSpace* This) {
    return This->lpVtbl->UnselectAll(This);
}
#endif
#endif

#endif


#endif  /* __IShellNameSpace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellShellNameSpace coclass
 */

DEFINE_GUID(CLSID_ShellShellNameSpace, 0x2f2f1f96, 0x2bc1, 0x4b1c, 0xbe,0x28, 0xea,0x37,0x74,0xf4,0x67,0x6a);

#ifdef __cplusplus
class DECLSPEC_UUID("2f2f1f96-2bc1-4b1c-be28-ea3774f4676a") ShellShellNameSpace;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellShellNameSpace, 0x2f2f1f96, 0x2bc1, 0x4b1c, 0xbe,0x28, 0xea,0x37,0x74,0xf4,0x67,0x6a)
#endif
#endif

/*****************************************************************************
 * ShellNameSpace coclass
 */

DEFINE_GUID(CLSID_ShellNameSpace, 0x55136805, 0xb2de, 0x11d1, 0xb9,0xf2, 0x00,0xa0,0xc9,0x8b,0xc5,0x47);

#ifdef __cplusplus
class DECLSPEC_UUID("55136805-b2de-11d1-b9f2-00a0c98bc547") ShellNameSpace;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellNameSpace, 0x55136805, 0xb2de, 0x11d1, 0xb9,0xf2, 0x00,0xa0,0xc9,0x8b,0xc5,0x47)
#endif
#endif

/*****************************************************************************
 * IScriptErrorList interface
 */
#ifndef __IScriptErrorList_INTERFACE_DEFINED__
#define __IScriptErrorList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IScriptErrorList, 0xf3470f24, 0x15fd, 0x11d2, 0xbb,0x2e, 0x00,0x80,0x5f,0xf7,0xef,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f3470f24-15fd-11d2-bb2e-00805ff7efca")
IScriptErrorList : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE advanceError(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE retreatError(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE canAdvanceError(
        LONG *pfCanAdvance) = 0;

    virtual HRESULT STDMETHODCALLTYPE canRetreatError(
        LONG *pfCanRetreat) = 0;

    virtual HRESULT STDMETHODCALLTYPE getErrorLine(
        LONG *plLine) = 0;

    virtual HRESULT STDMETHODCALLTYPE getErrorChar(
        LONG *plChar) = 0;

    virtual HRESULT STDMETHODCALLTYPE getErrorCode(
        LONG *plCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE getErrorMsg(
        BSTR *pstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE getErrorUrl(
        BSTR *pstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAlwaysShowLockState(
        LONG *pfAlwaysShowLocked) = 0;

    virtual HRESULT STDMETHODCALLTYPE getDetailsPaneOpen(
        LONG *pfDetailsPaneOpen) = 0;

    virtual HRESULT STDMETHODCALLTYPE setDetailsPaneOpen(
        LONG fDetailsPaneOpen) = 0;

    virtual HRESULT STDMETHODCALLTYPE getPerErrorDisplay(
        LONG *pfPerErrorDisplay) = 0;

    virtual HRESULT STDMETHODCALLTYPE setPerErrorDisplay(
        LONG fPerErrorDisplay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IScriptErrorList, 0xf3470f24, 0x15fd, 0x11d2, 0xbb,0x2e, 0x00,0x80,0x5f,0xf7,0xef,0xca)
#endif
#else
typedef struct IScriptErrorListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IScriptErrorList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IScriptErrorList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IScriptErrorList *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IScriptErrorList *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IScriptErrorList *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IScriptErrorList *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IScriptErrorList *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IScriptErrorList methods ***/
    HRESULT (STDMETHODCALLTYPE *advanceError)(
        IScriptErrorList *This);

    HRESULT (STDMETHODCALLTYPE *retreatError)(
        IScriptErrorList *This);

    HRESULT (STDMETHODCALLTYPE *canAdvanceError)(
        IScriptErrorList *This,
        LONG *pfCanAdvance);

    HRESULT (STDMETHODCALLTYPE *canRetreatError)(
        IScriptErrorList *This,
        LONG *pfCanRetreat);

    HRESULT (STDMETHODCALLTYPE *getErrorLine)(
        IScriptErrorList *This,
        LONG *plLine);

    HRESULT (STDMETHODCALLTYPE *getErrorChar)(
        IScriptErrorList *This,
        LONG *plChar);

    HRESULT (STDMETHODCALLTYPE *getErrorCode)(
        IScriptErrorList *This,
        LONG *plCode);

    HRESULT (STDMETHODCALLTYPE *getErrorMsg)(
        IScriptErrorList *This,
        BSTR *pstr);

    HRESULT (STDMETHODCALLTYPE *getErrorUrl)(
        IScriptErrorList *This,
        BSTR *pstr);

    HRESULT (STDMETHODCALLTYPE *getAlwaysShowLockState)(
        IScriptErrorList *This,
        LONG *pfAlwaysShowLocked);

    HRESULT (STDMETHODCALLTYPE *getDetailsPaneOpen)(
        IScriptErrorList *This,
        LONG *pfDetailsPaneOpen);

    HRESULT (STDMETHODCALLTYPE *setDetailsPaneOpen)(
        IScriptErrorList *This,
        LONG fDetailsPaneOpen);

    HRESULT (STDMETHODCALLTYPE *getPerErrorDisplay)(
        IScriptErrorList *This,
        LONG *pfPerErrorDisplay);

    HRESULT (STDMETHODCALLTYPE *setPerErrorDisplay)(
        IScriptErrorList *This,
        LONG fPerErrorDisplay);

    END_INTERFACE
} IScriptErrorListVtbl;

interface IScriptErrorList {
    CONST_VTBL IScriptErrorListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IScriptErrorList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IScriptErrorList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IScriptErrorList_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IScriptErrorList_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IScriptErrorList_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IScriptErrorList_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IScriptErrorList_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IScriptErrorList methods ***/
#define IScriptErrorList_advanceError(This) (This)->lpVtbl->advanceError(This)
#define IScriptErrorList_retreatError(This) (This)->lpVtbl->retreatError(This)
#define IScriptErrorList_canAdvanceError(This,pfCanAdvance) (This)->lpVtbl->canAdvanceError(This,pfCanAdvance)
#define IScriptErrorList_canRetreatError(This,pfCanRetreat) (This)->lpVtbl->canRetreatError(This,pfCanRetreat)
#define IScriptErrorList_getErrorLine(This,plLine) (This)->lpVtbl->getErrorLine(This,plLine)
#define IScriptErrorList_getErrorChar(This,plChar) (This)->lpVtbl->getErrorChar(This,plChar)
#define IScriptErrorList_getErrorCode(This,plCode) (This)->lpVtbl->getErrorCode(This,plCode)
#define IScriptErrorList_getErrorMsg(This,pstr) (This)->lpVtbl->getErrorMsg(This,pstr)
#define IScriptErrorList_getErrorUrl(This,pstr) (This)->lpVtbl->getErrorUrl(This,pstr)
#define IScriptErrorList_getAlwaysShowLockState(This,pfAlwaysShowLocked) (This)->lpVtbl->getAlwaysShowLockState(This,pfAlwaysShowLocked)
#define IScriptErrorList_getDetailsPaneOpen(This,pfDetailsPaneOpen) (This)->lpVtbl->getDetailsPaneOpen(This,pfDetailsPaneOpen)
#define IScriptErrorList_setDetailsPaneOpen(This,fDetailsPaneOpen) (This)->lpVtbl->setDetailsPaneOpen(This,fDetailsPaneOpen)
#define IScriptErrorList_getPerErrorDisplay(This,pfPerErrorDisplay) (This)->lpVtbl->getPerErrorDisplay(This,pfPerErrorDisplay)
#define IScriptErrorList_setPerErrorDisplay(This,fPerErrorDisplay) (This)->lpVtbl->setPerErrorDisplay(This,fPerErrorDisplay)
#else
/*** IUnknown methods ***/
static inline HRESULT IScriptErrorList_QueryInterface(IScriptErrorList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IScriptErrorList_AddRef(IScriptErrorList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IScriptErrorList_Release(IScriptErrorList* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IScriptErrorList_GetTypeInfoCount(IScriptErrorList* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IScriptErrorList_GetTypeInfo(IScriptErrorList* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IScriptErrorList_GetIDsOfNames(IScriptErrorList* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IScriptErrorList_Invoke(IScriptErrorList* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IScriptErrorList methods ***/
static inline HRESULT IScriptErrorList_advanceError(IScriptErrorList* This) {
    return This->lpVtbl->advanceError(This);
}
static inline HRESULT IScriptErrorList_retreatError(IScriptErrorList* This) {
    return This->lpVtbl->retreatError(This);
}
static inline HRESULT IScriptErrorList_canAdvanceError(IScriptErrorList* This,LONG *pfCanAdvance) {
    return This->lpVtbl->canAdvanceError(This,pfCanAdvance);
}
static inline HRESULT IScriptErrorList_canRetreatError(IScriptErrorList* This,LONG *pfCanRetreat) {
    return This->lpVtbl->canRetreatError(This,pfCanRetreat);
}
static inline HRESULT IScriptErrorList_getErrorLine(IScriptErrorList* This,LONG *plLine) {
    return This->lpVtbl->getErrorLine(This,plLine);
}
static inline HRESULT IScriptErrorList_getErrorChar(IScriptErrorList* This,LONG *plChar) {
    return This->lpVtbl->getErrorChar(This,plChar);
}
static inline HRESULT IScriptErrorList_getErrorCode(IScriptErrorList* This,LONG *plCode) {
    return This->lpVtbl->getErrorCode(This,plCode);
}
static inline HRESULT IScriptErrorList_getErrorMsg(IScriptErrorList* This,BSTR *pstr) {
    return This->lpVtbl->getErrorMsg(This,pstr);
}
static inline HRESULT IScriptErrorList_getErrorUrl(IScriptErrorList* This,BSTR *pstr) {
    return This->lpVtbl->getErrorUrl(This,pstr);
}
static inline HRESULT IScriptErrorList_getAlwaysShowLockState(IScriptErrorList* This,LONG *pfAlwaysShowLocked) {
    return This->lpVtbl->getAlwaysShowLockState(This,pfAlwaysShowLocked);
}
static inline HRESULT IScriptErrorList_getDetailsPaneOpen(IScriptErrorList* This,LONG *pfDetailsPaneOpen) {
    return This->lpVtbl->getDetailsPaneOpen(This,pfDetailsPaneOpen);
}
static inline HRESULT IScriptErrorList_setDetailsPaneOpen(IScriptErrorList* This,LONG fDetailsPaneOpen) {
    return This->lpVtbl->setDetailsPaneOpen(This,fDetailsPaneOpen);
}
static inline HRESULT IScriptErrorList_getPerErrorDisplay(IScriptErrorList* This,LONG *pfPerErrorDisplay) {
    return This->lpVtbl->getPerErrorDisplay(This,pfPerErrorDisplay);
}
static inline HRESULT IScriptErrorList_setPerErrorDisplay(IScriptErrorList* This,LONG fPerErrorDisplay) {
    return This->lpVtbl->setPerErrorDisplay(This,fPerErrorDisplay);
}
#endif
#endif

#endif


#endif  /* __IScriptErrorList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * CScriptErrorList coclass
 */

DEFINE_GUID(CLSID_CScriptErrorList, 0xefd01300, 0x160f, 0x11d2, 0xbb,0x2e, 0x00,0x80,0x5f,0xf7,0xef,0xca);

#ifdef __cplusplus
class DECLSPEC_UUID("efd01300-160f-11d2-bb2e-00805ff7efca") CScriptErrorList;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CScriptErrorList, 0xefd01300, 0x160f, 0x11d2, 0xbb,0x2e, 0x00,0x80,0x5f,0xf7,0xef,0xca)
#endif
#endif

/*****************************************************************************
 * ISearch interface
 */
#ifndef __ISearch_INTERFACE_DEFINED__
#define __ISearch_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISearch, 0xba9239a4, 0x3dd5, 0x11d2, 0xbf,0x8b, 0x00,0xc0,0x4f,0xb9,0x36,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ba9239a4-3dd5-11d2-bf8b-00c04fb93661")
ISearch : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Title(
        BSTR *pbstrTitle) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        BSTR *pbstrId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_URL(
        BSTR *pbstrUrl) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISearch, 0xba9239a4, 0x3dd5, 0x11d2, 0xbf,0x8b, 0x00,0xc0,0x4f,0xb9,0x36,0x61)
#endif
#else
typedef struct ISearchVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISearch *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISearch *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISearch *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISearch *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISearch *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISearch *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISearch *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISearch methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Title)(
        ISearch *This,
        BSTR *pbstrTitle);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        ISearch *This,
        BSTR *pbstrId);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        ISearch *This,
        BSTR *pbstrUrl);

    END_INTERFACE
} ISearchVtbl;

interface ISearch {
    CONST_VTBL ISearchVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISearch_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISearch_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISearch_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISearch_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISearch_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISearch_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISearch_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISearch methods ***/
#define ISearch_get_Title(This,pbstrTitle) (This)->lpVtbl->get_Title(This,pbstrTitle)
#define ISearch_get_Id(This,pbstrId) (This)->lpVtbl->get_Id(This,pbstrId)
#define ISearch_get_URL(This,pbstrUrl) (This)->lpVtbl->get_URL(This,pbstrUrl)
#else
/*** IUnknown methods ***/
static inline HRESULT ISearch_QueryInterface(ISearch* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISearch_AddRef(ISearch* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISearch_Release(ISearch* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISearch_GetTypeInfoCount(ISearch* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISearch_GetTypeInfo(ISearch* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISearch_GetIDsOfNames(ISearch* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISearch_Invoke(ISearch* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISearch methods ***/
static inline HRESULT ISearch_get_Title(ISearch* This,BSTR *pbstrTitle) {
    return This->lpVtbl->get_Title(This,pbstrTitle);
}
static inline HRESULT ISearch_get_Id(ISearch* This,BSTR *pbstrId) {
    return This->lpVtbl->get_Id(This,pbstrId);
}
static inline HRESULT ISearch_get_URL(ISearch* This,BSTR *pbstrUrl) {
    return This->lpVtbl->get_URL(This,pbstrUrl);
}
#endif
#endif

#endif


#endif  /* __ISearch_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISearches interface
 */
#ifndef __ISearches_INTERFACE_DEFINED__
#define __ISearches_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISearches, 0x47c922a2, 0x3dd5, 0x11d2, 0xbf,0x8b, 0x00,0xc0,0x4f,0xb9,0x36,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("47c922a2-3dd5-11d2-bf8b-00c04fb93661")
ISearches : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Default(
        BSTR *pbstrDefault) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        VARIANT index,
        ISearch **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE _NewEnum(
        IUnknown **ppunk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISearches, 0x47c922a2, 0x3dd5, 0x11d2, 0xbf,0x8b, 0x00,0xc0,0x4f,0xb9,0x36,0x61)
#endif
#else
typedef struct ISearchesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISearches *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISearches *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISearches *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISearches *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISearches *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISearches *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISearches *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISearches methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISearches *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_Default)(
        ISearches *This,
        BSTR *pbstrDefault);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISearches *This,
        VARIANT index,
        ISearch **ppid);

    HRESULT (STDMETHODCALLTYPE *_NewEnum)(
        ISearches *This,
        IUnknown **ppunk);

    END_INTERFACE
} ISearchesVtbl;

interface ISearches {
    CONST_VTBL ISearchesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISearches_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISearches_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISearches_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISearches_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISearches_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISearches_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISearches_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISearches methods ***/
#define ISearches_get_Count(This,plCount) (This)->lpVtbl->get_Count(This,plCount)
#define ISearches_get_Default(This,pbstrDefault) (This)->lpVtbl->get_Default(This,pbstrDefault)
#define ISearches_Item(This,index,ppid) (This)->lpVtbl->Item(This,index,ppid)
#define ISearches__NewEnum(This,ppunk) (This)->lpVtbl->_NewEnum(This,ppunk)
#else
/*** IUnknown methods ***/
static inline HRESULT ISearches_QueryInterface(ISearches* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISearches_AddRef(ISearches* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISearches_Release(ISearches* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISearches_GetTypeInfoCount(ISearches* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISearches_GetTypeInfo(ISearches* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISearches_GetIDsOfNames(ISearches* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISearches_Invoke(ISearches* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISearches methods ***/
static inline HRESULT ISearches_get_Count(ISearches* This,LONG *plCount) {
    return This->lpVtbl->get_Count(This,plCount);
}
static inline HRESULT ISearches_get_Default(ISearches* This,BSTR *pbstrDefault) {
    return This->lpVtbl->get_Default(This,pbstrDefault);
}
static inline HRESULT ISearches_Item(ISearches* This,VARIANT index,ISearch **ppid) {
    return This->lpVtbl->Item(This,index,ppid);
}
static inline HRESULT ISearches__NewEnum(ISearches* This,IUnknown **ppunk) {
    return This->lpVtbl->_NewEnum(This,ppunk);
}
#endif
#endif

#endif


#endif  /* __ISearches_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISearchAssistantOC interface
 */
#ifndef __ISearchAssistantOC_INTERFACE_DEFINED__
#define __ISearchAssistantOC_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISearchAssistantOC, 0x72423e8f, 0x8011, 0x11d2, 0xbe,0x79, 0x00,0xa0,0xc9,0xa8,0x3d,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72423e8f-8011-11d2-be79-00a0c9a83da1")
ISearchAssistantOC : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE AddNextMenuItem(
        BSTR bstrText,
        LONG idItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultSearchUrl(
        BSTR bstrUrl) = 0;

    virtual HRESULT STDMETHODCALLTYPE NavigateToDefaultSearch(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRestricted(
        BSTR bstrGuid,
        VARIANT_BOOL *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ShellFeaturesEnabled(
        VARIANT_BOOL *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SearchAssistantDefault(
        VARIANT_BOOL *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Searches(
        ISearches **ppid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InWebFolder(
        VARIANT_BOOL *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutProperty(
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR bstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR *pbstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EventHandled(
        VARIANT_BOOL rhs) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetNextMenu(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindOnWeb(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindFilesOrFolders(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindComputer(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindPrinter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindPeople(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSearchAssistantURL(
        VARIANT_BOOL bSubstitute,
        VARIANT_BOOL bCustomize,
        BSTR *pbstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifySearchSettingsChanged(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ASProvider(
        BSTR pProvider) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ASProvider(
        BSTR *pProvider) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ASSetting(
        int pSetting) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ASSetting(
        int *pSetting) = 0;

    virtual HRESULT STDMETHODCALLTYPE NETDetectNextNavigate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutFindText(
        BSTR FindText) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Version(
        int *pVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE EncodeString(
        BSTR bstrValue,
        BSTR bstrCharSet,
        VARIANT_BOOL bUseUTF8,
        BSTR *pbstrResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISearchAssistantOC, 0x72423e8f, 0x8011, 0x11d2, 0xbe,0x79, 0x00,0xa0,0xc9,0xa8,0x3d,0xa1)
#endif
#else
typedef struct ISearchAssistantOCVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISearchAssistantOC *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISearchAssistantOC *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISearchAssistantOC *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISearchAssistantOC *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISearchAssistantOC *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISearchAssistantOC *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISearchAssistantOC *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISearchAssistantOC methods ***/
    HRESULT (STDMETHODCALLTYPE *AddNextMenuItem)(
        ISearchAssistantOC *This,
        BSTR bstrText,
        LONG idItem);

    HRESULT (STDMETHODCALLTYPE *SetDefaultSearchUrl)(
        ISearchAssistantOC *This,
        BSTR bstrUrl);

    HRESULT (STDMETHODCALLTYPE *NavigateToDefaultSearch)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        ISearchAssistantOC *This,
        BSTR bstrGuid,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_ShellFeaturesEnabled)(
        ISearchAssistantOC *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_SearchAssistantDefault)(
        ISearchAssistantOC *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_Searches)(
        ISearchAssistantOC *This,
        ISearches **ppid);

    HRESULT (STDMETHODCALLTYPE *get_InWebFolder)(
        ISearchAssistantOC *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *PutProperty)(
        ISearchAssistantOC *This,
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR bstrValue);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        ISearchAssistantOC *This,
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *put_EventHandled)(
        ISearchAssistantOC *This,
        VARIANT_BOOL rhs);

    HRESULT (STDMETHODCALLTYPE *ResetNextMenu)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *FindOnWeb)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *FindFilesOrFolders)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *FindPeople)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *GetSearchAssistantURL)(
        ISearchAssistantOC *This,
        VARIANT_BOOL bSubstitute,
        VARIANT_BOOL bCustomize,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *NotifySearchSettingsChanged)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *put_ASProvider)(
        ISearchAssistantOC *This,
        BSTR pProvider);

    HRESULT (STDMETHODCALLTYPE *get_ASProvider)(
        ISearchAssistantOC *This,
        BSTR *pProvider);

    HRESULT (STDMETHODCALLTYPE *put_ASSetting)(
        ISearchAssistantOC *This,
        int pSetting);

    HRESULT (STDMETHODCALLTYPE *get_ASSetting)(
        ISearchAssistantOC *This,
        int *pSetting);

    HRESULT (STDMETHODCALLTYPE *NETDetectNextNavigate)(
        ISearchAssistantOC *This);

    HRESULT (STDMETHODCALLTYPE *PutFindText)(
        ISearchAssistantOC *This,
        BSTR FindText);

    HRESULT (STDMETHODCALLTYPE *get_Version)(
        ISearchAssistantOC *This,
        int *pVersion);

    HRESULT (STDMETHODCALLTYPE *EncodeString)(
        ISearchAssistantOC *This,
        BSTR bstrValue,
        BSTR bstrCharSet,
        VARIANT_BOOL bUseUTF8,
        BSTR *pbstrResult);

    END_INTERFACE
} ISearchAssistantOCVtbl;

interface ISearchAssistantOC {
    CONST_VTBL ISearchAssistantOCVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISearchAssistantOC_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISearchAssistantOC_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISearchAssistantOC_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISearchAssistantOC_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISearchAssistantOC_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISearchAssistantOC_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISearchAssistantOC_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISearchAssistantOC methods ***/
#define ISearchAssistantOC_AddNextMenuItem(This,bstrText,idItem) (This)->lpVtbl->AddNextMenuItem(This,bstrText,idItem)
#define ISearchAssistantOC_SetDefaultSearchUrl(This,bstrUrl) (This)->lpVtbl->SetDefaultSearchUrl(This,bstrUrl)
#define ISearchAssistantOC_NavigateToDefaultSearch(This) (This)->lpVtbl->NavigateToDefaultSearch(This)
#define ISearchAssistantOC_IsRestricted(This,bstrGuid,pVal) (This)->lpVtbl->IsRestricted(This,bstrGuid,pVal)
#define ISearchAssistantOC_get_ShellFeaturesEnabled(This,pVal) (This)->lpVtbl->get_ShellFeaturesEnabled(This,pVal)
#define ISearchAssistantOC_get_SearchAssistantDefault(This,pVal) (This)->lpVtbl->get_SearchAssistantDefault(This,pVal)
#define ISearchAssistantOC_get_Searches(This,ppid) (This)->lpVtbl->get_Searches(This,ppid)
#define ISearchAssistantOC_get_InWebFolder(This,pVal) (This)->lpVtbl->get_InWebFolder(This,pVal)
#define ISearchAssistantOC_PutProperty(This,bPerLocale,bstrName,bstrValue) (This)->lpVtbl->PutProperty(This,bPerLocale,bstrName,bstrValue)
#define ISearchAssistantOC_GetProperty(This,bPerLocale,bstrName,pbstrValue) (This)->lpVtbl->GetProperty(This,bPerLocale,bstrName,pbstrValue)
#define ISearchAssistantOC_put_EventHandled(This,rhs) (This)->lpVtbl->put_EventHandled(This,rhs)
#define ISearchAssistantOC_ResetNextMenu(This) (This)->lpVtbl->ResetNextMenu(This)
#define ISearchAssistantOC_FindOnWeb(This) (This)->lpVtbl->FindOnWeb(This)
#define ISearchAssistantOC_FindFilesOrFolders(This) (This)->lpVtbl->FindFilesOrFolders(This)
#define ISearchAssistantOC_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define ISearchAssistantOC_FindPrinter(This) (This)->lpVtbl->FindPrinter(This)
#define ISearchAssistantOC_FindPeople(This) (This)->lpVtbl->FindPeople(This)
#define ISearchAssistantOC_GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue) (This)->lpVtbl->GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue)
#define ISearchAssistantOC_NotifySearchSettingsChanged(This) (This)->lpVtbl->NotifySearchSettingsChanged(This)
#define ISearchAssistantOC_put_ASProvider(This,pProvider) (This)->lpVtbl->put_ASProvider(This,pProvider)
#define ISearchAssistantOC_get_ASProvider(This,pProvider) (This)->lpVtbl->get_ASProvider(This,pProvider)
#define ISearchAssistantOC_put_ASSetting(This,pSetting) (This)->lpVtbl->put_ASSetting(This,pSetting)
#define ISearchAssistantOC_get_ASSetting(This,pSetting) (This)->lpVtbl->get_ASSetting(This,pSetting)
#define ISearchAssistantOC_NETDetectNextNavigate(This) (This)->lpVtbl->NETDetectNextNavigate(This)
#define ISearchAssistantOC_PutFindText(This,FindText) (This)->lpVtbl->PutFindText(This,FindText)
#define ISearchAssistantOC_get_Version(This,pVersion) (This)->lpVtbl->get_Version(This,pVersion)
#define ISearchAssistantOC_EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult) (This)->lpVtbl->EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult)
#else
/*** IUnknown methods ***/
static inline HRESULT ISearchAssistantOC_QueryInterface(ISearchAssistantOC* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISearchAssistantOC_AddRef(ISearchAssistantOC* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISearchAssistantOC_Release(ISearchAssistantOC* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISearchAssistantOC_GetTypeInfoCount(ISearchAssistantOC* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISearchAssistantOC_GetTypeInfo(ISearchAssistantOC* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISearchAssistantOC_GetIDsOfNames(ISearchAssistantOC* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISearchAssistantOC_Invoke(ISearchAssistantOC* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISearchAssistantOC methods ***/
static inline HRESULT ISearchAssistantOC_AddNextMenuItem(ISearchAssistantOC* This,BSTR bstrText,LONG idItem) {
    return This->lpVtbl->AddNextMenuItem(This,bstrText,idItem);
}
static inline HRESULT ISearchAssistantOC_SetDefaultSearchUrl(ISearchAssistantOC* This,BSTR bstrUrl) {
    return This->lpVtbl->SetDefaultSearchUrl(This,bstrUrl);
}
static inline HRESULT ISearchAssistantOC_NavigateToDefaultSearch(ISearchAssistantOC* This) {
    return This->lpVtbl->NavigateToDefaultSearch(This);
}
static inline HRESULT ISearchAssistantOC_IsRestricted(ISearchAssistantOC* This,BSTR bstrGuid,VARIANT_BOOL *pVal) {
    return This->lpVtbl->IsRestricted(This,bstrGuid,pVal);
}
static inline HRESULT ISearchAssistantOC_get_ShellFeaturesEnabled(ISearchAssistantOC* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_ShellFeaturesEnabled(This,pVal);
}
static inline HRESULT ISearchAssistantOC_get_SearchAssistantDefault(ISearchAssistantOC* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_SearchAssistantDefault(This,pVal);
}
static inline HRESULT ISearchAssistantOC_get_Searches(ISearchAssistantOC* This,ISearches **ppid) {
    return This->lpVtbl->get_Searches(This,ppid);
}
static inline HRESULT ISearchAssistantOC_get_InWebFolder(ISearchAssistantOC* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_InWebFolder(This,pVal);
}
static inline HRESULT ISearchAssistantOC_PutProperty(ISearchAssistantOC* This,VARIANT_BOOL bPerLocale,BSTR bstrName,BSTR bstrValue) {
    return This->lpVtbl->PutProperty(This,bPerLocale,bstrName,bstrValue);
}
static inline HRESULT ISearchAssistantOC_GetProperty(ISearchAssistantOC* This,VARIANT_BOOL bPerLocale,BSTR bstrName,BSTR *pbstrValue) {
    return This->lpVtbl->GetProperty(This,bPerLocale,bstrName,pbstrValue);
}
static inline HRESULT ISearchAssistantOC_put_EventHandled(ISearchAssistantOC* This,VARIANT_BOOL rhs) {
    return This->lpVtbl->put_EventHandled(This,rhs);
}
static inline HRESULT ISearchAssistantOC_ResetNextMenu(ISearchAssistantOC* This) {
    return This->lpVtbl->ResetNextMenu(This);
}
static inline HRESULT ISearchAssistantOC_FindOnWeb(ISearchAssistantOC* This) {
    return This->lpVtbl->FindOnWeb(This);
}
static inline HRESULT ISearchAssistantOC_FindFilesOrFolders(ISearchAssistantOC* This) {
    return This->lpVtbl->FindFilesOrFolders(This);
}
static inline HRESULT ISearchAssistantOC_FindComputer(ISearchAssistantOC* This) {
    return This->lpVtbl->FindComputer(This);
}
static inline HRESULT ISearchAssistantOC_FindPrinter(ISearchAssistantOC* This) {
    return This->lpVtbl->FindPrinter(This);
}
static inline HRESULT ISearchAssistantOC_FindPeople(ISearchAssistantOC* This) {
    return This->lpVtbl->FindPeople(This);
}
static inline HRESULT ISearchAssistantOC_GetSearchAssistantURL(ISearchAssistantOC* This,VARIANT_BOOL bSubstitute,VARIANT_BOOL bCustomize,BSTR *pbstrValue) {
    return This->lpVtbl->GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue);
}
static inline HRESULT ISearchAssistantOC_NotifySearchSettingsChanged(ISearchAssistantOC* This) {
    return This->lpVtbl->NotifySearchSettingsChanged(This);
}
static inline HRESULT ISearchAssistantOC_put_ASProvider(ISearchAssistantOC* This,BSTR pProvider) {
    return This->lpVtbl->put_ASProvider(This,pProvider);
}
static inline HRESULT ISearchAssistantOC_get_ASProvider(ISearchAssistantOC* This,BSTR *pProvider) {
    return This->lpVtbl->get_ASProvider(This,pProvider);
}
static inline HRESULT ISearchAssistantOC_put_ASSetting(ISearchAssistantOC* This,int pSetting) {
    return This->lpVtbl->put_ASSetting(This,pSetting);
}
static inline HRESULT ISearchAssistantOC_get_ASSetting(ISearchAssistantOC* This,int *pSetting) {
    return This->lpVtbl->get_ASSetting(This,pSetting);
}
static inline HRESULT ISearchAssistantOC_NETDetectNextNavigate(ISearchAssistantOC* This) {
    return This->lpVtbl->NETDetectNextNavigate(This);
}
static inline HRESULT ISearchAssistantOC_PutFindText(ISearchAssistantOC* This,BSTR FindText) {
    return This->lpVtbl->PutFindText(This,FindText);
}
static inline HRESULT ISearchAssistantOC_get_Version(ISearchAssistantOC* This,int *pVersion) {
    return This->lpVtbl->get_Version(This,pVersion);
}
static inline HRESULT ISearchAssistantOC_EncodeString(ISearchAssistantOC* This,BSTR bstrValue,BSTR bstrCharSet,VARIANT_BOOL bUseUTF8,BSTR *pbstrResult) {
    return This->lpVtbl->EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult);
}
#endif
#endif

#endif


#endif  /* __ISearchAssistantOC_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISearchAssistantOC2 interface
 */
#ifndef __ISearchAssistantOC2_INTERFACE_DEFINED__
#define __ISearchAssistantOC2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISearchAssistantOC2, 0x72423e8f, 0x8011, 0x11d2, 0xbe,0x79, 0x00,0xa0,0xc9,0xa8,0x3d,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72423e8f-8011-11d2-be79-00a0c9a83da2")
ISearchAssistantOC2 : public ISearchAssistantOC
{
    virtual HRESULT STDMETHODCALLTYPE get_ShowFindPrinter(
        VARIANT_BOOL *pbShowFindPrinter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISearchAssistantOC2, 0x72423e8f, 0x8011, 0x11d2, 0xbe,0x79, 0x00,0xa0,0xc9,0xa8,0x3d,0xa2)
#endif
#else
typedef struct ISearchAssistantOC2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISearchAssistantOC2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISearchAssistantOC2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISearchAssistantOC2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISearchAssistantOC2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISearchAssistantOC2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISearchAssistantOC2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISearchAssistantOC2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISearchAssistantOC methods ***/
    HRESULT (STDMETHODCALLTYPE *AddNextMenuItem)(
        ISearchAssistantOC2 *This,
        BSTR bstrText,
        LONG idItem);

    HRESULT (STDMETHODCALLTYPE *SetDefaultSearchUrl)(
        ISearchAssistantOC2 *This,
        BSTR bstrUrl);

    HRESULT (STDMETHODCALLTYPE *NavigateToDefaultSearch)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        ISearchAssistantOC2 *This,
        BSTR bstrGuid,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_ShellFeaturesEnabled)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_SearchAssistantDefault)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_Searches)(
        ISearchAssistantOC2 *This,
        ISearches **ppid);

    HRESULT (STDMETHODCALLTYPE *get_InWebFolder)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *PutProperty)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR bstrValue);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *put_EventHandled)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL rhs);

    HRESULT (STDMETHODCALLTYPE *ResetNextMenu)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *FindOnWeb)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *FindFilesOrFolders)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *FindPeople)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *GetSearchAssistantURL)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL bSubstitute,
        VARIANT_BOOL bCustomize,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *NotifySearchSettingsChanged)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *put_ASProvider)(
        ISearchAssistantOC2 *This,
        BSTR pProvider);

    HRESULT (STDMETHODCALLTYPE *get_ASProvider)(
        ISearchAssistantOC2 *This,
        BSTR *pProvider);

    HRESULT (STDMETHODCALLTYPE *put_ASSetting)(
        ISearchAssistantOC2 *This,
        int pSetting);

    HRESULT (STDMETHODCALLTYPE *get_ASSetting)(
        ISearchAssistantOC2 *This,
        int *pSetting);

    HRESULT (STDMETHODCALLTYPE *NETDetectNextNavigate)(
        ISearchAssistantOC2 *This);

    HRESULT (STDMETHODCALLTYPE *PutFindText)(
        ISearchAssistantOC2 *This,
        BSTR FindText);

    HRESULT (STDMETHODCALLTYPE *get_Version)(
        ISearchAssistantOC2 *This,
        int *pVersion);

    HRESULT (STDMETHODCALLTYPE *EncodeString)(
        ISearchAssistantOC2 *This,
        BSTR bstrValue,
        BSTR bstrCharSet,
        VARIANT_BOOL bUseUTF8,
        BSTR *pbstrResult);

    /*** ISearchAssistantOC2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ShowFindPrinter)(
        ISearchAssistantOC2 *This,
        VARIANT_BOOL *pbShowFindPrinter);

    END_INTERFACE
} ISearchAssistantOC2Vtbl;

interface ISearchAssistantOC2 {
    CONST_VTBL ISearchAssistantOC2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISearchAssistantOC2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISearchAssistantOC2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISearchAssistantOC2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISearchAssistantOC2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISearchAssistantOC2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISearchAssistantOC2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISearchAssistantOC2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISearchAssistantOC methods ***/
#define ISearchAssistantOC2_AddNextMenuItem(This,bstrText,idItem) (This)->lpVtbl->AddNextMenuItem(This,bstrText,idItem)
#define ISearchAssistantOC2_SetDefaultSearchUrl(This,bstrUrl) (This)->lpVtbl->SetDefaultSearchUrl(This,bstrUrl)
#define ISearchAssistantOC2_NavigateToDefaultSearch(This) (This)->lpVtbl->NavigateToDefaultSearch(This)
#define ISearchAssistantOC2_IsRestricted(This,bstrGuid,pVal) (This)->lpVtbl->IsRestricted(This,bstrGuid,pVal)
#define ISearchAssistantOC2_get_ShellFeaturesEnabled(This,pVal) (This)->lpVtbl->get_ShellFeaturesEnabled(This,pVal)
#define ISearchAssistantOC2_get_SearchAssistantDefault(This,pVal) (This)->lpVtbl->get_SearchAssistantDefault(This,pVal)
#define ISearchAssistantOC2_get_Searches(This,ppid) (This)->lpVtbl->get_Searches(This,ppid)
#define ISearchAssistantOC2_get_InWebFolder(This,pVal) (This)->lpVtbl->get_InWebFolder(This,pVal)
#define ISearchAssistantOC2_PutProperty(This,bPerLocale,bstrName,bstrValue) (This)->lpVtbl->PutProperty(This,bPerLocale,bstrName,bstrValue)
#define ISearchAssistantOC2_GetProperty(This,bPerLocale,bstrName,pbstrValue) (This)->lpVtbl->GetProperty(This,bPerLocale,bstrName,pbstrValue)
#define ISearchAssistantOC2_put_EventHandled(This,rhs) (This)->lpVtbl->put_EventHandled(This,rhs)
#define ISearchAssistantOC2_ResetNextMenu(This) (This)->lpVtbl->ResetNextMenu(This)
#define ISearchAssistantOC2_FindOnWeb(This) (This)->lpVtbl->FindOnWeb(This)
#define ISearchAssistantOC2_FindFilesOrFolders(This) (This)->lpVtbl->FindFilesOrFolders(This)
#define ISearchAssistantOC2_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define ISearchAssistantOC2_FindPrinter(This) (This)->lpVtbl->FindPrinter(This)
#define ISearchAssistantOC2_FindPeople(This) (This)->lpVtbl->FindPeople(This)
#define ISearchAssistantOC2_GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue) (This)->lpVtbl->GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue)
#define ISearchAssistantOC2_NotifySearchSettingsChanged(This) (This)->lpVtbl->NotifySearchSettingsChanged(This)
#define ISearchAssistantOC2_put_ASProvider(This,pProvider) (This)->lpVtbl->put_ASProvider(This,pProvider)
#define ISearchAssistantOC2_get_ASProvider(This,pProvider) (This)->lpVtbl->get_ASProvider(This,pProvider)
#define ISearchAssistantOC2_put_ASSetting(This,pSetting) (This)->lpVtbl->put_ASSetting(This,pSetting)
#define ISearchAssistantOC2_get_ASSetting(This,pSetting) (This)->lpVtbl->get_ASSetting(This,pSetting)
#define ISearchAssistantOC2_NETDetectNextNavigate(This) (This)->lpVtbl->NETDetectNextNavigate(This)
#define ISearchAssistantOC2_PutFindText(This,FindText) (This)->lpVtbl->PutFindText(This,FindText)
#define ISearchAssistantOC2_get_Version(This,pVersion) (This)->lpVtbl->get_Version(This,pVersion)
#define ISearchAssistantOC2_EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult) (This)->lpVtbl->EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult)
/*** ISearchAssistantOC2 methods ***/
#define ISearchAssistantOC2_get_ShowFindPrinter(This,pbShowFindPrinter) (This)->lpVtbl->get_ShowFindPrinter(This,pbShowFindPrinter)
#else
/*** IUnknown methods ***/
static inline HRESULT ISearchAssistantOC2_QueryInterface(ISearchAssistantOC2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISearchAssistantOC2_AddRef(ISearchAssistantOC2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISearchAssistantOC2_Release(ISearchAssistantOC2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISearchAssistantOC2_GetTypeInfoCount(ISearchAssistantOC2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISearchAssistantOC2_GetTypeInfo(ISearchAssistantOC2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISearchAssistantOC2_GetIDsOfNames(ISearchAssistantOC2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISearchAssistantOC2_Invoke(ISearchAssistantOC2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISearchAssistantOC methods ***/
static inline HRESULT ISearchAssistantOC2_AddNextMenuItem(ISearchAssistantOC2* This,BSTR bstrText,LONG idItem) {
    return This->lpVtbl->AddNextMenuItem(This,bstrText,idItem);
}
static inline HRESULT ISearchAssistantOC2_SetDefaultSearchUrl(ISearchAssistantOC2* This,BSTR bstrUrl) {
    return This->lpVtbl->SetDefaultSearchUrl(This,bstrUrl);
}
static inline HRESULT ISearchAssistantOC2_NavigateToDefaultSearch(ISearchAssistantOC2* This) {
    return This->lpVtbl->NavigateToDefaultSearch(This);
}
static inline HRESULT ISearchAssistantOC2_IsRestricted(ISearchAssistantOC2* This,BSTR bstrGuid,VARIANT_BOOL *pVal) {
    return This->lpVtbl->IsRestricted(This,bstrGuid,pVal);
}
static inline HRESULT ISearchAssistantOC2_get_ShellFeaturesEnabled(ISearchAssistantOC2* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_ShellFeaturesEnabled(This,pVal);
}
static inline HRESULT ISearchAssistantOC2_get_SearchAssistantDefault(ISearchAssistantOC2* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_SearchAssistantDefault(This,pVal);
}
static inline HRESULT ISearchAssistantOC2_get_Searches(ISearchAssistantOC2* This,ISearches **ppid) {
    return This->lpVtbl->get_Searches(This,ppid);
}
static inline HRESULT ISearchAssistantOC2_get_InWebFolder(ISearchAssistantOC2* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_InWebFolder(This,pVal);
}
static inline HRESULT ISearchAssistantOC2_PutProperty(ISearchAssistantOC2* This,VARIANT_BOOL bPerLocale,BSTR bstrName,BSTR bstrValue) {
    return This->lpVtbl->PutProperty(This,bPerLocale,bstrName,bstrValue);
}
static inline HRESULT ISearchAssistantOC2_GetProperty(ISearchAssistantOC2* This,VARIANT_BOOL bPerLocale,BSTR bstrName,BSTR *pbstrValue) {
    return This->lpVtbl->GetProperty(This,bPerLocale,bstrName,pbstrValue);
}
static inline HRESULT ISearchAssistantOC2_put_EventHandled(ISearchAssistantOC2* This,VARIANT_BOOL rhs) {
    return This->lpVtbl->put_EventHandled(This,rhs);
}
static inline HRESULT ISearchAssistantOC2_ResetNextMenu(ISearchAssistantOC2* This) {
    return This->lpVtbl->ResetNextMenu(This);
}
static inline HRESULT ISearchAssistantOC2_FindOnWeb(ISearchAssistantOC2* This) {
    return This->lpVtbl->FindOnWeb(This);
}
static inline HRESULT ISearchAssistantOC2_FindFilesOrFolders(ISearchAssistantOC2* This) {
    return This->lpVtbl->FindFilesOrFolders(This);
}
static inline HRESULT ISearchAssistantOC2_FindComputer(ISearchAssistantOC2* This) {
    return This->lpVtbl->FindComputer(This);
}
static inline HRESULT ISearchAssistantOC2_FindPrinter(ISearchAssistantOC2* This) {
    return This->lpVtbl->FindPrinter(This);
}
static inline HRESULT ISearchAssistantOC2_FindPeople(ISearchAssistantOC2* This) {
    return This->lpVtbl->FindPeople(This);
}
static inline HRESULT ISearchAssistantOC2_GetSearchAssistantURL(ISearchAssistantOC2* This,VARIANT_BOOL bSubstitute,VARIANT_BOOL bCustomize,BSTR *pbstrValue) {
    return This->lpVtbl->GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue);
}
static inline HRESULT ISearchAssistantOC2_NotifySearchSettingsChanged(ISearchAssistantOC2* This) {
    return This->lpVtbl->NotifySearchSettingsChanged(This);
}
static inline HRESULT ISearchAssistantOC2_put_ASProvider(ISearchAssistantOC2* This,BSTR pProvider) {
    return This->lpVtbl->put_ASProvider(This,pProvider);
}
static inline HRESULT ISearchAssistantOC2_get_ASProvider(ISearchAssistantOC2* This,BSTR *pProvider) {
    return This->lpVtbl->get_ASProvider(This,pProvider);
}
static inline HRESULT ISearchAssistantOC2_put_ASSetting(ISearchAssistantOC2* This,int pSetting) {
    return This->lpVtbl->put_ASSetting(This,pSetting);
}
static inline HRESULT ISearchAssistantOC2_get_ASSetting(ISearchAssistantOC2* This,int *pSetting) {
    return This->lpVtbl->get_ASSetting(This,pSetting);
}
static inline HRESULT ISearchAssistantOC2_NETDetectNextNavigate(ISearchAssistantOC2* This) {
    return This->lpVtbl->NETDetectNextNavigate(This);
}
static inline HRESULT ISearchAssistantOC2_PutFindText(ISearchAssistantOC2* This,BSTR FindText) {
    return This->lpVtbl->PutFindText(This,FindText);
}
static inline HRESULT ISearchAssistantOC2_get_Version(ISearchAssistantOC2* This,int *pVersion) {
    return This->lpVtbl->get_Version(This,pVersion);
}
static inline HRESULT ISearchAssistantOC2_EncodeString(ISearchAssistantOC2* This,BSTR bstrValue,BSTR bstrCharSet,VARIANT_BOOL bUseUTF8,BSTR *pbstrResult) {
    return This->lpVtbl->EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult);
}
/*** ISearchAssistantOC2 methods ***/
static inline HRESULT ISearchAssistantOC2_get_ShowFindPrinter(ISearchAssistantOC2* This,VARIANT_BOOL *pbShowFindPrinter) {
    return This->lpVtbl->get_ShowFindPrinter(This,pbShowFindPrinter);
}
#endif
#endif

#endif


#endif  /* __ISearchAssistantOC2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISearchAssistantOC3 interface
 */
#ifndef __ISearchAssistantOC3_INTERFACE_DEFINED__
#define __ISearchAssistantOC3_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISearchAssistantOC3, 0x72423e8f, 0x8011, 0x11d2, 0xbe,0x79, 0x00,0xa0,0xc9,0xa8,0x3d,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72423e8f-8011-11d2-be79-00a0c9a83da3")
ISearchAssistantOC3 : public ISearchAssistantOC2
{
    virtual HRESULT STDMETHODCALLTYPE get_SearchCompanionAvailable(
        VARIANT_BOOL *pbAvailable) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UseSearchCompanion(
        VARIANT_BOOL pbUseSC) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UseSearchCompanion(
        VARIANT_BOOL *pbUseSC) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISearchAssistantOC3, 0x72423e8f, 0x8011, 0x11d2, 0xbe,0x79, 0x00,0xa0,0xc9,0xa8,0x3d,0xa3)
#endif
#else
typedef struct ISearchAssistantOC3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISearchAssistantOC3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISearchAssistantOC3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISearchAssistantOC3 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISearchAssistantOC3 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISearchAssistantOC3 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISearchAssistantOC3 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISearchAssistantOC3 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISearchAssistantOC methods ***/
    HRESULT (STDMETHODCALLTYPE *AddNextMenuItem)(
        ISearchAssistantOC3 *This,
        BSTR bstrText,
        LONG idItem);

    HRESULT (STDMETHODCALLTYPE *SetDefaultSearchUrl)(
        ISearchAssistantOC3 *This,
        BSTR bstrUrl);

    HRESULT (STDMETHODCALLTYPE *NavigateToDefaultSearch)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *IsRestricted)(
        ISearchAssistantOC3 *This,
        BSTR bstrGuid,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_ShellFeaturesEnabled)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_SearchAssistantDefault)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *get_Searches)(
        ISearchAssistantOC3 *This,
        ISearches **ppid);

    HRESULT (STDMETHODCALLTYPE *get_InWebFolder)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL *pVal);

    HRESULT (STDMETHODCALLTYPE *PutProperty)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR bstrValue);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL bPerLocale,
        BSTR bstrName,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *put_EventHandled)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL rhs);

    HRESULT (STDMETHODCALLTYPE *ResetNextMenu)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *FindOnWeb)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *FindFilesOrFolders)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *FindComputer)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *FindPrinter)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *FindPeople)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *GetSearchAssistantURL)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL bSubstitute,
        VARIANT_BOOL bCustomize,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *NotifySearchSettingsChanged)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *put_ASProvider)(
        ISearchAssistantOC3 *This,
        BSTR pProvider);

    HRESULT (STDMETHODCALLTYPE *get_ASProvider)(
        ISearchAssistantOC3 *This,
        BSTR *pProvider);

    HRESULT (STDMETHODCALLTYPE *put_ASSetting)(
        ISearchAssistantOC3 *This,
        int pSetting);

    HRESULT (STDMETHODCALLTYPE *get_ASSetting)(
        ISearchAssistantOC3 *This,
        int *pSetting);

    HRESULT (STDMETHODCALLTYPE *NETDetectNextNavigate)(
        ISearchAssistantOC3 *This);

    HRESULT (STDMETHODCALLTYPE *PutFindText)(
        ISearchAssistantOC3 *This,
        BSTR FindText);

    HRESULT (STDMETHODCALLTYPE *get_Version)(
        ISearchAssistantOC3 *This,
        int *pVersion);

    HRESULT (STDMETHODCALLTYPE *EncodeString)(
        ISearchAssistantOC3 *This,
        BSTR bstrValue,
        BSTR bstrCharSet,
        VARIANT_BOOL bUseUTF8,
        BSTR *pbstrResult);

    /*** ISearchAssistantOC2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ShowFindPrinter)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL *pbShowFindPrinter);

    /*** ISearchAssistantOC3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SearchCompanionAvailable)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL *pbAvailable);

    HRESULT (STDMETHODCALLTYPE *put_UseSearchCompanion)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL pbUseSC);

    HRESULT (STDMETHODCALLTYPE *get_UseSearchCompanion)(
        ISearchAssistantOC3 *This,
        VARIANT_BOOL *pbUseSC);

    END_INTERFACE
} ISearchAssistantOC3Vtbl;

interface ISearchAssistantOC3 {
    CONST_VTBL ISearchAssistantOC3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISearchAssistantOC3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISearchAssistantOC3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISearchAssistantOC3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISearchAssistantOC3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISearchAssistantOC3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISearchAssistantOC3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISearchAssistantOC3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISearchAssistantOC methods ***/
#define ISearchAssistantOC3_AddNextMenuItem(This,bstrText,idItem) (This)->lpVtbl->AddNextMenuItem(This,bstrText,idItem)
#define ISearchAssistantOC3_SetDefaultSearchUrl(This,bstrUrl) (This)->lpVtbl->SetDefaultSearchUrl(This,bstrUrl)
#define ISearchAssistantOC3_NavigateToDefaultSearch(This) (This)->lpVtbl->NavigateToDefaultSearch(This)
#define ISearchAssistantOC3_IsRestricted(This,bstrGuid,pVal) (This)->lpVtbl->IsRestricted(This,bstrGuid,pVal)
#define ISearchAssistantOC3_get_ShellFeaturesEnabled(This,pVal) (This)->lpVtbl->get_ShellFeaturesEnabled(This,pVal)
#define ISearchAssistantOC3_get_SearchAssistantDefault(This,pVal) (This)->lpVtbl->get_SearchAssistantDefault(This,pVal)
#define ISearchAssistantOC3_get_Searches(This,ppid) (This)->lpVtbl->get_Searches(This,ppid)
#define ISearchAssistantOC3_get_InWebFolder(This,pVal) (This)->lpVtbl->get_InWebFolder(This,pVal)
#define ISearchAssistantOC3_PutProperty(This,bPerLocale,bstrName,bstrValue) (This)->lpVtbl->PutProperty(This,bPerLocale,bstrName,bstrValue)
#define ISearchAssistantOC3_GetProperty(This,bPerLocale,bstrName,pbstrValue) (This)->lpVtbl->GetProperty(This,bPerLocale,bstrName,pbstrValue)
#define ISearchAssistantOC3_put_EventHandled(This,rhs) (This)->lpVtbl->put_EventHandled(This,rhs)
#define ISearchAssistantOC3_ResetNextMenu(This) (This)->lpVtbl->ResetNextMenu(This)
#define ISearchAssistantOC3_FindOnWeb(This) (This)->lpVtbl->FindOnWeb(This)
#define ISearchAssistantOC3_FindFilesOrFolders(This) (This)->lpVtbl->FindFilesOrFolders(This)
#define ISearchAssistantOC3_FindComputer(This) (This)->lpVtbl->FindComputer(This)
#define ISearchAssistantOC3_FindPrinter(This) (This)->lpVtbl->FindPrinter(This)
#define ISearchAssistantOC3_FindPeople(This) (This)->lpVtbl->FindPeople(This)
#define ISearchAssistantOC3_GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue) (This)->lpVtbl->GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue)
#define ISearchAssistantOC3_NotifySearchSettingsChanged(This) (This)->lpVtbl->NotifySearchSettingsChanged(This)
#define ISearchAssistantOC3_put_ASProvider(This,pProvider) (This)->lpVtbl->put_ASProvider(This,pProvider)
#define ISearchAssistantOC3_get_ASProvider(This,pProvider) (This)->lpVtbl->get_ASProvider(This,pProvider)
#define ISearchAssistantOC3_put_ASSetting(This,pSetting) (This)->lpVtbl->put_ASSetting(This,pSetting)
#define ISearchAssistantOC3_get_ASSetting(This,pSetting) (This)->lpVtbl->get_ASSetting(This,pSetting)
#define ISearchAssistantOC3_NETDetectNextNavigate(This) (This)->lpVtbl->NETDetectNextNavigate(This)
#define ISearchAssistantOC3_PutFindText(This,FindText) (This)->lpVtbl->PutFindText(This,FindText)
#define ISearchAssistantOC3_get_Version(This,pVersion) (This)->lpVtbl->get_Version(This,pVersion)
#define ISearchAssistantOC3_EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult) (This)->lpVtbl->EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult)
/*** ISearchAssistantOC2 methods ***/
#define ISearchAssistantOC3_get_ShowFindPrinter(This,pbShowFindPrinter) (This)->lpVtbl->get_ShowFindPrinter(This,pbShowFindPrinter)
/*** ISearchAssistantOC3 methods ***/
#define ISearchAssistantOC3_get_SearchCompanionAvailable(This,pbAvailable) (This)->lpVtbl->get_SearchCompanionAvailable(This,pbAvailable)
#define ISearchAssistantOC3_put_UseSearchCompanion(This,pbUseSC) (This)->lpVtbl->put_UseSearchCompanion(This,pbUseSC)
#define ISearchAssistantOC3_get_UseSearchCompanion(This,pbUseSC) (This)->lpVtbl->get_UseSearchCompanion(This,pbUseSC)
#else
/*** IUnknown methods ***/
static inline HRESULT ISearchAssistantOC3_QueryInterface(ISearchAssistantOC3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISearchAssistantOC3_AddRef(ISearchAssistantOC3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISearchAssistantOC3_Release(ISearchAssistantOC3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISearchAssistantOC3_GetTypeInfoCount(ISearchAssistantOC3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISearchAssistantOC3_GetTypeInfo(ISearchAssistantOC3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISearchAssistantOC3_GetIDsOfNames(ISearchAssistantOC3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISearchAssistantOC3_Invoke(ISearchAssistantOC3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISearchAssistantOC methods ***/
static inline HRESULT ISearchAssistantOC3_AddNextMenuItem(ISearchAssistantOC3* This,BSTR bstrText,LONG idItem) {
    return This->lpVtbl->AddNextMenuItem(This,bstrText,idItem);
}
static inline HRESULT ISearchAssistantOC3_SetDefaultSearchUrl(ISearchAssistantOC3* This,BSTR bstrUrl) {
    return This->lpVtbl->SetDefaultSearchUrl(This,bstrUrl);
}
static inline HRESULT ISearchAssistantOC3_NavigateToDefaultSearch(ISearchAssistantOC3* This) {
    return This->lpVtbl->NavigateToDefaultSearch(This);
}
static inline HRESULT ISearchAssistantOC3_IsRestricted(ISearchAssistantOC3* This,BSTR bstrGuid,VARIANT_BOOL *pVal) {
    return This->lpVtbl->IsRestricted(This,bstrGuid,pVal);
}
static inline HRESULT ISearchAssistantOC3_get_ShellFeaturesEnabled(ISearchAssistantOC3* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_ShellFeaturesEnabled(This,pVal);
}
static inline HRESULT ISearchAssistantOC3_get_SearchAssistantDefault(ISearchAssistantOC3* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_SearchAssistantDefault(This,pVal);
}
static inline HRESULT ISearchAssistantOC3_get_Searches(ISearchAssistantOC3* This,ISearches **ppid) {
    return This->lpVtbl->get_Searches(This,ppid);
}
static inline HRESULT ISearchAssistantOC3_get_InWebFolder(ISearchAssistantOC3* This,VARIANT_BOOL *pVal) {
    return This->lpVtbl->get_InWebFolder(This,pVal);
}
static inline HRESULT ISearchAssistantOC3_PutProperty(ISearchAssistantOC3* This,VARIANT_BOOL bPerLocale,BSTR bstrName,BSTR bstrValue) {
    return This->lpVtbl->PutProperty(This,bPerLocale,bstrName,bstrValue);
}
static inline HRESULT ISearchAssistantOC3_GetProperty(ISearchAssistantOC3* This,VARIANT_BOOL bPerLocale,BSTR bstrName,BSTR *pbstrValue) {
    return This->lpVtbl->GetProperty(This,bPerLocale,bstrName,pbstrValue);
}
static inline HRESULT ISearchAssistantOC3_put_EventHandled(ISearchAssistantOC3* This,VARIANT_BOOL rhs) {
    return This->lpVtbl->put_EventHandled(This,rhs);
}
static inline HRESULT ISearchAssistantOC3_ResetNextMenu(ISearchAssistantOC3* This) {
    return This->lpVtbl->ResetNextMenu(This);
}
static inline HRESULT ISearchAssistantOC3_FindOnWeb(ISearchAssistantOC3* This) {
    return This->lpVtbl->FindOnWeb(This);
}
static inline HRESULT ISearchAssistantOC3_FindFilesOrFolders(ISearchAssistantOC3* This) {
    return This->lpVtbl->FindFilesOrFolders(This);
}
static inline HRESULT ISearchAssistantOC3_FindComputer(ISearchAssistantOC3* This) {
    return This->lpVtbl->FindComputer(This);
}
static inline HRESULT ISearchAssistantOC3_FindPrinter(ISearchAssistantOC3* This) {
    return This->lpVtbl->FindPrinter(This);
}
static inline HRESULT ISearchAssistantOC3_FindPeople(ISearchAssistantOC3* This) {
    return This->lpVtbl->FindPeople(This);
}
static inline HRESULT ISearchAssistantOC3_GetSearchAssistantURL(ISearchAssistantOC3* This,VARIANT_BOOL bSubstitute,VARIANT_BOOL bCustomize,BSTR *pbstrValue) {
    return This->lpVtbl->GetSearchAssistantURL(This,bSubstitute,bCustomize,pbstrValue);
}
static inline HRESULT ISearchAssistantOC3_NotifySearchSettingsChanged(ISearchAssistantOC3* This) {
    return This->lpVtbl->NotifySearchSettingsChanged(This);
}
static inline HRESULT ISearchAssistantOC3_put_ASProvider(ISearchAssistantOC3* This,BSTR pProvider) {
    return This->lpVtbl->put_ASProvider(This,pProvider);
}
static inline HRESULT ISearchAssistantOC3_get_ASProvider(ISearchAssistantOC3* This,BSTR *pProvider) {
    return This->lpVtbl->get_ASProvider(This,pProvider);
}
static inline HRESULT ISearchAssistantOC3_put_ASSetting(ISearchAssistantOC3* This,int pSetting) {
    return This->lpVtbl->put_ASSetting(This,pSetting);
}
static inline HRESULT ISearchAssistantOC3_get_ASSetting(ISearchAssistantOC3* This,int *pSetting) {
    return This->lpVtbl->get_ASSetting(This,pSetting);
}
static inline HRESULT ISearchAssistantOC3_NETDetectNextNavigate(ISearchAssistantOC3* This) {
    return This->lpVtbl->NETDetectNextNavigate(This);
}
static inline HRESULT ISearchAssistantOC3_PutFindText(ISearchAssistantOC3* This,BSTR FindText) {
    return This->lpVtbl->PutFindText(This,FindText);
}
static inline HRESULT ISearchAssistantOC3_get_Version(ISearchAssistantOC3* This,int *pVersion) {
    return This->lpVtbl->get_Version(This,pVersion);
}
static inline HRESULT ISearchAssistantOC3_EncodeString(ISearchAssistantOC3* This,BSTR bstrValue,BSTR bstrCharSet,VARIANT_BOOL bUseUTF8,BSTR *pbstrResult) {
    return This->lpVtbl->EncodeString(This,bstrValue,bstrCharSet,bUseUTF8,pbstrResult);
}
/*** ISearchAssistantOC2 methods ***/
static inline HRESULT ISearchAssistantOC3_get_ShowFindPrinter(ISearchAssistantOC3* This,VARIANT_BOOL *pbShowFindPrinter) {
    return This->lpVtbl->get_ShowFindPrinter(This,pbShowFindPrinter);
}
/*** ISearchAssistantOC3 methods ***/
static inline HRESULT ISearchAssistantOC3_get_SearchCompanionAvailable(ISearchAssistantOC3* This,VARIANT_BOOL *pbAvailable) {
    return This->lpVtbl->get_SearchCompanionAvailable(This,pbAvailable);
}
static inline HRESULT ISearchAssistantOC3_put_UseSearchCompanion(ISearchAssistantOC3* This,VARIANT_BOOL pbUseSC) {
    return This->lpVtbl->put_UseSearchCompanion(This,pbUseSC);
}
static inline HRESULT ISearchAssistantOC3_get_UseSearchCompanion(ISearchAssistantOC3* This,VARIANT_BOOL *pbUseSC) {
    return This->lpVtbl->get_UseSearchCompanion(This,pbUseSC);
}
#endif
#endif

#endif


#endif  /* __ISearchAssistantOC3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * _SearchAssistantEvents dispinterface
 */
#ifndef ___SearchAssistantEvents_DISPINTERFACE_DEFINED__
#define ___SearchAssistantEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID__SearchAssistantEvents, 0x1611fdda, 0x445b, 0x11d2, 0x85,0xde, 0x00,0xc0,0x4f,0xa3,0x5c,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1611fdda-445b-11d2-85de-00c04fa35c89")
_SearchAssistantEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(_SearchAssistantEvents, 0x1611fdda, 0x445b, 0x11d2, 0x85,0xde, 0x00,0xc0,0x4f,0xa3,0x5c,0x89)
#endif
#else
typedef struct _SearchAssistantEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        _SearchAssistantEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        _SearchAssistantEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        _SearchAssistantEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        _SearchAssistantEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        _SearchAssistantEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        _SearchAssistantEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        _SearchAssistantEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} _SearchAssistantEventsVtbl;

interface _SearchAssistantEvents {
    CONST_VTBL _SearchAssistantEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define _SearchAssistantEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define _SearchAssistantEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define _SearchAssistantEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define _SearchAssistantEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define _SearchAssistantEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define _SearchAssistantEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define _SearchAssistantEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT _SearchAssistantEvents_QueryInterface(_SearchAssistantEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG _SearchAssistantEvents_AddRef(_SearchAssistantEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG _SearchAssistantEvents_Release(_SearchAssistantEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT _SearchAssistantEvents_GetTypeInfoCount(_SearchAssistantEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT _SearchAssistantEvents_GetTypeInfo(_SearchAssistantEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT _SearchAssistantEvents_GetIDsOfNames(_SearchAssistantEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT _SearchAssistantEvents_Invoke(_SearchAssistantEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* ___SearchAssistantEvents_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * ShellSearchAssistantOC coclass
 */

DEFINE_GUID(CLSID_ShellSearchAssistantOC, 0x2e71fd0f, 0xaab1, 0x42c0, 0x91,0x46, 0x6d,0x2c,0x4e,0xdc,0xf0,0x7d);

#ifdef __cplusplus
class DECLSPEC_UUID("2e71fd0f-aab1-42c0-9146-6d2c4edcf07d") ShellSearchAssistantOC;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ShellSearchAssistantOC, 0x2e71fd0f, 0xaab1, 0x42c0, 0x91,0x46, 0x6d,0x2c,0x4e,0xdc,0xf0,0x7d)
#endif
#endif

/*****************************************************************************
 * SearchAssistantOC coclass
 */

DEFINE_GUID(CLSID_SearchAssistantOC, 0xb45ff030, 0x4447, 0x11d2, 0x85,0xde, 0x00,0xc0,0x4f,0xa3,0x5c,0x89);

#ifdef __cplusplus
class DECLSPEC_UUID("b45ff030-4447-11d2-85de-00c04fa35c89") SearchAssistantOC;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SearchAssistantOC, 0xb45ff030, 0x4447, 0x11d2, 0x85,0xde, 0x00,0xc0,0x4f,0xa3,0x5c,0x89)
#endif
#endif

#endif /* __SHDocVw_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __exdisp_h__ */
