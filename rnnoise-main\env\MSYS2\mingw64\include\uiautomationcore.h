/*** Autogenerated by WIDL 10.12 from include/uiautomationcore.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __uiautomationcore_h__
#define __uiautomationcore_h__

/* Forward declarations */

#ifndef __IRawElementProviderSimple_FWD_DEFINED__
#define __IRawElementProviderSimple_FWD_DEFINED__
typedef interface IRawElementProviderSimple IRawElementProviderSimple;
#ifdef __cplusplus
interface IRawElementProviderSimple;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderSimple2_FWD_DEFINED__
#define __IRawElementProviderSimple2_FWD_DEFINED__
typedef interface IRawElementProviderSimple2 IRawElementProviderSimple2;
#ifdef __cplusplus
interface IRawElementProviderSimple2;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderSimple3_FWD_DEFINED__
#define __IRawElementProviderSimple3_FWD_DEFINED__
typedef interface IRawElementProviderSimple3 IRawElementProviderSimple3;
#ifdef __cplusplus
interface IRawElementProviderSimple3;
#endif /* __cplusplus */
#endif

#ifndef __IAccessibleEx_FWD_DEFINED__
#define __IAccessibleEx_FWD_DEFINED__
typedef interface IAccessibleEx IAccessibleEx;
#ifdef __cplusplus
interface IAccessibleEx;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderFragment_FWD_DEFINED__
#define __IRawElementProviderFragment_FWD_DEFINED__
typedef interface IRawElementProviderFragment IRawElementProviderFragment;
#ifdef __cplusplus
interface IRawElementProviderFragment;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderFragmentRoot_FWD_DEFINED__
#define __IRawElementProviderFragmentRoot_FWD_DEFINED__
typedef interface IRawElementProviderFragmentRoot IRawElementProviderFragmentRoot;
#ifdef __cplusplus
interface IRawElementProviderFragmentRoot;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderHwndOverride_FWD_DEFINED__
#define __IRawElementProviderHwndOverride_FWD_DEFINED__
typedef interface IRawElementProviderHwndOverride IRawElementProviderHwndOverride;
#ifdef __cplusplus
interface IRawElementProviderHwndOverride;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderAdviseEvents_FWD_DEFINED__
#define __IRawElementProviderAdviseEvents_FWD_DEFINED__
typedef interface IRawElementProviderAdviseEvents IRawElementProviderAdviseEvents;
#ifdef __cplusplus
interface IRawElementProviderAdviseEvents;
#endif /* __cplusplus */
#endif

#ifndef __IProxyProviderWinEventSink_FWD_DEFINED__
#define __IProxyProviderWinEventSink_FWD_DEFINED__
typedef interface IProxyProviderWinEventSink IProxyProviderWinEventSink;
#ifdef __cplusplus
interface IProxyProviderWinEventSink;
#endif /* __cplusplus */
#endif

#ifndef __IProxyProviderWinEventHandler_FWD_DEFINED__
#define __IProxyProviderWinEventHandler_FWD_DEFINED__
typedef interface IProxyProviderWinEventHandler IProxyProviderWinEventHandler;
#ifdef __cplusplus
interface IProxyProviderWinEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderWindowlessSite_FWD_DEFINED__
#define __IRawElementProviderWindowlessSite_FWD_DEFINED__
typedef interface IRawElementProviderWindowlessSite IRawElementProviderWindowlessSite;
#ifdef __cplusplus
interface IRawElementProviderWindowlessSite;
#endif /* __cplusplus */
#endif

#ifndef __IAccessibleHostingElementProviders_FWD_DEFINED__
#define __IAccessibleHostingElementProviders_FWD_DEFINED__
typedef interface IAccessibleHostingElementProviders IAccessibleHostingElementProviders;
#ifdef __cplusplus
interface IAccessibleHostingElementProviders;
#endif /* __cplusplus */
#endif

#ifndef __IRawElementProviderHostingAccessibles_FWD_DEFINED__
#define __IRawElementProviderHostingAccessibles_FWD_DEFINED__
typedef interface IRawElementProviderHostingAccessibles IRawElementProviderHostingAccessibles;
#ifdef __cplusplus
interface IRawElementProviderHostingAccessibles;
#endif /* __cplusplus */
#endif

#ifndef __ILegacyIAccessibleProvider_FWD_DEFINED__
#define __ILegacyIAccessibleProvider_FWD_DEFINED__
typedef interface ILegacyIAccessibleProvider ILegacyIAccessibleProvider;
#ifdef __cplusplus
interface ILegacyIAccessibleProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDockProvider_FWD_DEFINED__
#define __IDockProvider_FWD_DEFINED__
typedef interface IDockProvider IDockProvider;
#ifdef __cplusplus
interface IDockProvider;
#endif /* __cplusplus */
#endif

#ifndef __IExpandCollapseProvider_FWD_DEFINED__
#define __IExpandCollapseProvider_FWD_DEFINED__
typedef interface IExpandCollapseProvider IExpandCollapseProvider;
#ifdef __cplusplus
interface IExpandCollapseProvider;
#endif /* __cplusplus */
#endif

#ifndef __IGridProvider_FWD_DEFINED__
#define __IGridProvider_FWD_DEFINED__
typedef interface IGridProvider IGridProvider;
#ifdef __cplusplus
interface IGridProvider;
#endif /* __cplusplus */
#endif

#ifndef __IGridItemProvider_FWD_DEFINED__
#define __IGridItemProvider_FWD_DEFINED__
typedef interface IGridItemProvider IGridItemProvider;
#ifdef __cplusplus
interface IGridItemProvider;
#endif /* __cplusplus */
#endif

#ifndef __IInvokeProvider_FWD_DEFINED__
#define __IInvokeProvider_FWD_DEFINED__
typedef interface IInvokeProvider IInvokeProvider;
#ifdef __cplusplus
interface IInvokeProvider;
#endif /* __cplusplus */
#endif

#ifndef __IMultipleViewProvider_FWD_DEFINED__
#define __IMultipleViewProvider_FWD_DEFINED__
typedef interface IMultipleViewProvider IMultipleViewProvider;
#ifdef __cplusplus
interface IMultipleViewProvider;
#endif /* __cplusplus */
#endif

#ifndef __IRangeValueProvider_FWD_DEFINED__
#define __IRangeValueProvider_FWD_DEFINED__
typedef interface IRangeValueProvider IRangeValueProvider;
#ifdef __cplusplus
interface IRangeValueProvider;
#endif /* __cplusplus */
#endif

#ifndef __IScrollItemProvider_FWD_DEFINED__
#define __IScrollItemProvider_FWD_DEFINED__
typedef interface IScrollItemProvider IScrollItemProvider;
#ifdef __cplusplus
interface IScrollItemProvider;
#endif /* __cplusplus */
#endif

#ifndef __ISelectionProvider_FWD_DEFINED__
#define __ISelectionProvider_FWD_DEFINED__
typedef interface ISelectionProvider ISelectionProvider;
#ifdef __cplusplus
interface ISelectionProvider;
#endif /* __cplusplus */
#endif

#ifndef __ISelectionProvider2_FWD_DEFINED__
#define __ISelectionProvider2_FWD_DEFINED__
typedef interface ISelectionProvider2 ISelectionProvider2;
#ifdef __cplusplus
interface ISelectionProvider2;
#endif /* __cplusplus */
#endif

#ifndef __IScrollProvider_FWD_DEFINED__
#define __IScrollProvider_FWD_DEFINED__
typedef interface IScrollProvider IScrollProvider;
#ifdef __cplusplus
interface IScrollProvider;
#endif /* __cplusplus */
#endif

#ifndef __ISelectionItemProvider_FWD_DEFINED__
#define __ISelectionItemProvider_FWD_DEFINED__
typedef interface ISelectionItemProvider ISelectionItemProvider;
#ifdef __cplusplus
interface ISelectionItemProvider;
#endif /* __cplusplus */
#endif

#ifndef __ISynchronizedInputProvider_FWD_DEFINED__
#define __ISynchronizedInputProvider_FWD_DEFINED__
typedef interface ISynchronizedInputProvider ISynchronizedInputProvider;
#ifdef __cplusplus
interface ISynchronizedInputProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITableProvider_FWD_DEFINED__
#define __ITableProvider_FWD_DEFINED__
typedef interface ITableProvider ITableProvider;
#ifdef __cplusplus
interface ITableProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITableItemProvider_FWD_DEFINED__
#define __ITableItemProvider_FWD_DEFINED__
typedef interface ITableItemProvider ITableItemProvider;
#ifdef __cplusplus
interface ITableItemProvider;
#endif /* __cplusplus */
#endif

#ifndef __IToggleProvider_FWD_DEFINED__
#define __IToggleProvider_FWD_DEFINED__
typedef interface IToggleProvider IToggleProvider;
#ifdef __cplusplus
interface IToggleProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITransformProvider_FWD_DEFINED__
#define __ITransformProvider_FWD_DEFINED__
typedef interface ITransformProvider ITransformProvider;
#ifdef __cplusplus
interface ITransformProvider;
#endif /* __cplusplus */
#endif

#ifndef __IValueProvider_FWD_DEFINED__
#define __IValueProvider_FWD_DEFINED__
typedef interface IValueProvider IValueProvider;
#ifdef __cplusplus
interface IValueProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWindowProvider_FWD_DEFINED__
#define __IWindowProvider_FWD_DEFINED__
typedef interface IWindowProvider IWindowProvider;
#ifdef __cplusplus
interface IWindowProvider;
#endif /* __cplusplus */
#endif

#ifndef __IItemContainerProvider_FWD_DEFINED__
#define __IItemContainerProvider_FWD_DEFINED__
typedef interface IItemContainerProvider IItemContainerProvider;
#ifdef __cplusplus
interface IItemContainerProvider;
#endif /* __cplusplus */
#endif

#ifndef __IVirtualizedItemProvider_FWD_DEFINED__
#define __IVirtualizedItemProvider_FWD_DEFINED__
typedef interface IVirtualizedItemProvider IVirtualizedItemProvider;
#ifdef __cplusplus
interface IVirtualizedItemProvider;
#endif /* __cplusplus */
#endif

#ifndef __IObjectModelProvider_FWD_DEFINED__
#define __IObjectModelProvider_FWD_DEFINED__
typedef interface IObjectModelProvider IObjectModelProvider;
#ifdef __cplusplus
interface IObjectModelProvider;
#endif /* __cplusplus */
#endif

#ifndef __IAnnotationProvider_FWD_DEFINED__
#define __IAnnotationProvider_FWD_DEFINED__
typedef interface IAnnotationProvider IAnnotationProvider;
#ifdef __cplusplus
interface IAnnotationProvider;
#endif /* __cplusplus */
#endif

#ifndef __IStylesProvider_FWD_DEFINED__
#define __IStylesProvider_FWD_DEFINED__
typedef interface IStylesProvider IStylesProvider;
#ifdef __cplusplus
interface IStylesProvider;
#endif /* __cplusplus */
#endif

#ifndef __ISpreadsheetProvider_FWD_DEFINED__
#define __ISpreadsheetProvider_FWD_DEFINED__
typedef interface ISpreadsheetProvider ISpreadsheetProvider;
#ifdef __cplusplus
interface ISpreadsheetProvider;
#endif /* __cplusplus */
#endif

#ifndef __ISpreadsheetItemProvider_FWD_DEFINED__
#define __ISpreadsheetItemProvider_FWD_DEFINED__
typedef interface ISpreadsheetItemProvider ISpreadsheetItemProvider;
#ifdef __cplusplus
interface ISpreadsheetItemProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITransformProvider2_FWD_DEFINED__
#define __ITransformProvider2_FWD_DEFINED__
typedef interface ITransformProvider2 ITransformProvider2;
#ifdef __cplusplus
interface ITransformProvider2;
#endif /* __cplusplus */
#endif

#ifndef __IDragProvider_FWD_DEFINED__
#define __IDragProvider_FWD_DEFINED__
typedef interface IDragProvider IDragProvider;
#ifdef __cplusplus
interface IDragProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDropTargetProvider_FWD_DEFINED__
#define __IDropTargetProvider_FWD_DEFINED__
typedef interface IDropTargetProvider IDropTargetProvider;
#ifdef __cplusplus
interface IDropTargetProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITextProvider_FWD_DEFINED__
#define __ITextProvider_FWD_DEFINED__
typedef interface ITextProvider ITextProvider;
#ifdef __cplusplus
interface ITextProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITextProvider2_FWD_DEFINED__
#define __ITextProvider2_FWD_DEFINED__
typedef interface ITextProvider2 ITextProvider2;
#ifdef __cplusplus
interface ITextProvider2;
#endif /* __cplusplus */
#endif

#ifndef __ITextEditProvider_FWD_DEFINED__
#define __ITextEditProvider_FWD_DEFINED__
typedef interface ITextEditProvider ITextEditProvider;
#ifdef __cplusplus
interface ITextEditProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITextRangeProvider_FWD_DEFINED__
#define __ITextRangeProvider_FWD_DEFINED__
typedef interface ITextRangeProvider ITextRangeProvider;
#ifdef __cplusplus
interface ITextRangeProvider;
#endif /* __cplusplus */
#endif

#ifndef __ITextRangeProvider2_FWD_DEFINED__
#define __ITextRangeProvider2_FWD_DEFINED__
typedef interface ITextRangeProvider2 ITextRangeProvider2;
#ifdef __cplusplus
interface ITextRangeProvider2;
#endif /* __cplusplus */
#endif

#ifndef __ITextChildProvider_FWD_DEFINED__
#define __ITextChildProvider_FWD_DEFINED__
typedef interface ITextChildProvider ITextChildProvider;
#ifdef __cplusplus
interface ITextChildProvider;
#endif /* __cplusplus */
#endif

#ifndef __ICustomNavigationProvider_FWD_DEFINED__
#define __ICustomNavigationProvider_FWD_DEFINED__
typedef interface ICustomNavigationProvider ICustomNavigationProvider;
#ifdef __cplusplus
interface ICustomNavigationProvider;
#endif /* __cplusplus */
#endif

#ifndef __IUIAutomationPatternInstance_FWD_DEFINED__
#define __IUIAutomationPatternInstance_FWD_DEFINED__
typedef interface IUIAutomationPatternInstance IUIAutomationPatternInstance;
#ifdef __cplusplus
interface IUIAutomationPatternInstance;
#endif /* __cplusplus */
#endif

#ifndef __IUIAutomationPatternHandler_FWD_DEFINED__
#define __IUIAutomationPatternHandler_FWD_DEFINED__
typedef interface IUIAutomationPatternHandler IUIAutomationPatternHandler;
#ifdef __cplusplus
interface IUIAutomationPatternHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAutomationRegistrar_FWD_DEFINED__
#define __IUIAutomationRegistrar_FWD_DEFINED__
typedef interface IUIAutomationRegistrar IUIAutomationRegistrar;
#ifdef __cplusplus
interface IUIAutomationRegistrar;
#endif /* __cplusplus */
#endif

#ifndef __CUIAutomationRegistrar_FWD_DEFINED__
#define __CUIAutomationRegistrar_FWD_DEFINED__
#ifdef __cplusplus
typedef class CUIAutomationRegistrar CUIAutomationRegistrar;
#else
typedef struct CUIAutomationRegistrar CUIAutomationRegistrar;
#endif /* defined __cplusplus */
#endif /* defined __CUIAutomationRegistrar_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <oleacc.h>

#ifdef __cplusplus
extern "C" {
#endif

enum NavigateDirection {
    NavigateDirection_Parent = 0x0,
    NavigateDirection_NextSibling = 0x1,
    NavigateDirection_PreviousSibling = 0x2,
    NavigateDirection_FirstChild = 0x3,
    NavigateDirection_LastChild = 0x4
};
enum ProviderOptions {
    ProviderOptions_ClientSideProvider = 0x1,
    ProviderOptions_ServerSideProvider = 0x2,
    ProviderOptions_NonClientAreaProvider = 0x4,
    ProviderOptions_OverrideProvider = 0x8,
    ProviderOptions_ProviderOwnsSetFocus = 0x10,
    ProviderOptions_UseComThreading = 0x20,
    ProviderOptions_RefuseNonClientSupport = 0x40,
    ProviderOptions_HasNativeIAccessible = 0x80,
    ProviderOptions_UseClientCoordinates = 0x100
};
enum StructureChangeType {
    StructureChangeType_ChildAdded = 0x0,
    StructureChangeType_ChildRemoved = 0x1,
    StructureChangeType_ChildrenInvalidated = 0x2,
    StructureChangeType_ChildrenBulkAdded = 0x3,
    StructureChangeType_ChildrenBulkRemoved = 0x4,
    StructureChangeType_ChildrenReordered = 0x5
};
enum TextEditChangeType {
    TextEditChangeType_None = 0x0,
    TextEditChangeType_AutoCorrect = 0x1,
    TextEditChangeType_Composition = 0x2,
    TextEditChangeType_CompositionFinalized = 0x3,
    TextEditChangeType_AutoComplete = 0x4
};
enum OrientationType {
    OrientationType_None = 0x0,
    OrientationType_Horizontal = 0x1,
    OrientationType_Vertical = 0x2
};
enum DockPosition {
    DockPosition_Top = 0x0,
    DockPosition_Left = 0x1,
    DockPosition_Bottom = 0x2,
    DockPosition_Right = 0x3,
    DockPosition_Fill = 0x4,
    DockPosition_None = 0x5
};
enum ExpandCollapseState {
    ExpandCollapseState_Collapsed = 0x0,
    ExpandCollapseState_Expanded = 0x1,
    ExpandCollapseState_PartiallyExpanded = 0x2,
    ExpandCollapseState_LeafNode = 0x3
};
enum ScrollAmount {
    ScrollAmount_LargeDecrement = 0x0,
    ScrollAmount_SmallDecrement = 0x1,
    ScrollAmount_NoAmount = 0x2,
    ScrollAmount_LargeIncrement = 0x3,
    ScrollAmount_SmallIncrement = 0x4
};
enum RowOrColumnMajor {
    RowOrColumnMajor_RowMajor = 0x0,
    RowOrColumnMajor_ColumnMajor = 0x1,
    RowOrColumnMajor_Indeterminate = 0x2
};
enum ToggleState {
    ToggleState_Off = 0x0,
    ToggleState_On = 0x1,
    ToggleState_Indeterminate = 0x2
};
enum WindowVisualState {
    WindowVisualState_Normal = 0x0,
    WindowVisualState_Maximized = 0x1,
    WindowVisualState_Minimized = 0x2
};
enum SynchronizedInputType {
    SynchronizedInputType_KeyUp = 0x1,
    SynchronizedInputType_KeyDown = 0x2,
    SynchronizedInputType_LeftMouseUp = 0x4,
    SynchronizedInputType_LeftMouseDown = 0x8,
    SynchronizedInputType_RightMouseUp = 0x10,
    SynchronizedInputType_RightMouseDown = 0x20
};
DEFINE_ENUM_FLAG_OPERATORS(SynchronizedInputType)
enum WindowInteractionState {
    WindowInteractionState_Running = 0x0,
    WindowInteractionState_Closing = 0x1,
    WindowInteractionState_ReadyForUserInteraction = 0x2,
    WindowInteractionState_BlockedByModalWindow = 0x3,
    WindowInteractionState_NotResponding = 0x4
};
enum TextUnit {
    TextUnit_Character = 0x0,
    TextUnit_Format = 0x1,
    TextUnit_Word = 0x2,
    TextUnit_Line = 0x3,
    TextUnit_Paragraph = 0x4,
    TextUnit_Page = 0x5,
    TextUnit_Document = 0x6
};
enum TextPatternRangeEndpoint {
    TextPatternRangeEndpoint_Start = 0x0,
    TextPatternRangeEndpoint_End = 0x1
};
enum SupportedTextSelection {
    SupportedTextSelection_None = 0x0,
    SupportedTextSelection_Single = 0x1,
    SupportedTextSelection_Multiple = 0x2
};
enum LiveSetting {
    Off = 0x0,
    Polite = 0x1,
    Assertive = 0x2
};
enum ZoomUnit {
    ZoomUnit_NoAmount = 0x0,
    ZoomUnit_LargeDecrement = 0x1,
    ZoomUnit_SmallDecrement = 0x2,
    ZoomUnit_LargeIncrement = 0x3,
    ZoomUnit_SmallIncrement = 0x4
};
enum NotificationProcessing {
    NotificationProcessing_ImportantAll = 0x0,
    NotificationProcessing_ImportantMostRecent = 0x1,
    NotificationProcessing_All = 0x2,
    NotificationProcessing_MostRecent = 0x3,
    NotificationProcessing_CurrentThenMostRecent = 0x4
};
enum NotificationKind {
    NotificationKind_ItemAdded = 0x0,
    NotificationKind_ItemRemoved = 0x1,
    NotificationKind_ActionCompleted = 0x2,
    NotificationKind_ActionAborted = 0x3,
    NotificationKind_Other = 0x4
};
typedef int PROPERTYID;
typedef int PATTERNID;
typedef int EVENTID;
typedef int TEXTATTRIBUTEID;
typedef int CONTROLTYPEID;
typedef int LANDMARKTYPEID;
typedef int METADATAID;
typedef int HEADINGLEVELID;
struct UiaRect {
    double left;
    double top;
    double width;
    double height;
};
struct UiaPoint {
    double x;
    double y;
};
struct UiaChangeInfo {
    int uiaId;
    VARIANT payload;
    VARIANT extraInfo;
};
#ifndef __UIA_LIBRARY_DEFINED__
#define __UIA_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_UIA, 0x930299ce, 0x9965, 0x4dec, 0xb0,0xf4, 0xa5,0x48,0x48,0xd4,0xb6,0x67);

/*****************************************************************************
 * IRawElementProviderSimple interface
 */
#ifndef __IRawElementProviderSimple_INTERFACE_DEFINED__
#define __IRawElementProviderSimple_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderSimple, 0xd6dd68d1, 0x86fd, 0x4332, 0x86,0x66, 0x9a,0xbe,0xde,0xa2,0xd2,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d6dd68d1-86fd-4332-8666-9abedea2d24c")
IRawElementProviderSimple : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_ProviderOptions(
        enum ProviderOptions *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPatternProvider(
        PATTERNID patternId,
        IUnknown **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyValue(
        PROPERTYID propertyId,
        VARIANT *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HostRawElementProvider(
        IRawElementProviderSimple **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderSimple, 0xd6dd68d1, 0x86fd, 0x4332, 0x86,0x66, 0x9a,0xbe,0xde,0xa2,0xd2,0x4c)
#endif
#else
typedef struct IRawElementProviderSimpleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderSimple *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderSimple *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderSimple *This);

    /*** IRawElementProviderSimple methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProviderOptions)(
        IRawElementProviderSimple *This,
        enum ProviderOptions *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetPatternProvider)(
        IRawElementProviderSimple *This,
        PATTERNID patternId,
        IUnknown **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetPropertyValue)(
        IRawElementProviderSimple *This,
        PROPERTYID propertyId,
        VARIANT *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_HostRawElementProvider)(
        IRawElementProviderSimple *This,
        IRawElementProviderSimple **pRetVal);

    END_INTERFACE
} IRawElementProviderSimpleVtbl;

interface IRawElementProviderSimple {
    CONST_VTBL IRawElementProviderSimpleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderSimple_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderSimple_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderSimple_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderSimple methods ***/
#define IRawElementProviderSimple_get_ProviderOptions(This,pRetVal) (This)->lpVtbl->get_ProviderOptions(This,pRetVal)
#define IRawElementProviderSimple_GetPatternProvider(This,patternId,pRetVal) (This)->lpVtbl->GetPatternProvider(This,patternId,pRetVal)
#define IRawElementProviderSimple_GetPropertyValue(This,propertyId,pRetVal) (This)->lpVtbl->GetPropertyValue(This,propertyId,pRetVal)
#define IRawElementProviderSimple_get_HostRawElementProvider(This,pRetVal) (This)->lpVtbl->get_HostRawElementProvider(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderSimple_QueryInterface(IRawElementProviderSimple* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderSimple_AddRef(IRawElementProviderSimple* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderSimple_Release(IRawElementProviderSimple* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderSimple methods ***/
static inline HRESULT IRawElementProviderSimple_get_ProviderOptions(IRawElementProviderSimple* This,enum ProviderOptions *pRetVal) {
    return This->lpVtbl->get_ProviderOptions(This,pRetVal);
}
static inline HRESULT IRawElementProviderSimple_GetPatternProvider(IRawElementProviderSimple* This,PATTERNID patternId,IUnknown **pRetVal) {
    return This->lpVtbl->GetPatternProvider(This,patternId,pRetVal);
}
static inline HRESULT IRawElementProviderSimple_GetPropertyValue(IRawElementProviderSimple* This,PROPERTYID propertyId,VARIANT *pRetVal) {
    return This->lpVtbl->GetPropertyValue(This,propertyId,pRetVal);
}
static inline HRESULT IRawElementProviderSimple_get_HostRawElementProvider(IRawElementProviderSimple* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->get_HostRawElementProvider(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderSimple_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRawElementProviderSimple2 interface
 */
#ifndef __IRawElementProviderSimple2_INTERFACE_DEFINED__
#define __IRawElementProviderSimple2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderSimple2, 0xa0a839a9, 0x8da1, 0x4a82, 0x80,0x6a, 0x8e,0x0d,0x44,0xe7,0x9f,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a0a839a9-8da1-4a82-806a-8e0d44e79f56")
IRawElementProviderSimple2 : public IRawElementProviderSimple
{
    virtual HRESULT STDMETHODCALLTYPE ShowContextMenu(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderSimple2, 0xa0a839a9, 0x8da1, 0x4a82, 0x80,0x6a, 0x8e,0x0d,0x44,0xe7,0x9f,0x56)
#endif
#else
typedef struct IRawElementProviderSimple2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderSimple2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderSimple2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderSimple2 *This);

    /*** IRawElementProviderSimple methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProviderOptions)(
        IRawElementProviderSimple2 *This,
        enum ProviderOptions *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetPatternProvider)(
        IRawElementProviderSimple2 *This,
        PATTERNID patternId,
        IUnknown **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetPropertyValue)(
        IRawElementProviderSimple2 *This,
        PROPERTYID propertyId,
        VARIANT *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_HostRawElementProvider)(
        IRawElementProviderSimple2 *This,
        IRawElementProviderSimple **pRetVal);

    /*** IRawElementProviderSimple2 methods ***/
    HRESULT (STDMETHODCALLTYPE *ShowContextMenu)(
        IRawElementProviderSimple2 *This);

    END_INTERFACE
} IRawElementProviderSimple2Vtbl;

interface IRawElementProviderSimple2 {
    CONST_VTBL IRawElementProviderSimple2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderSimple2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderSimple2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderSimple2_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderSimple methods ***/
#define IRawElementProviderSimple2_get_ProviderOptions(This,pRetVal) (This)->lpVtbl->get_ProviderOptions(This,pRetVal)
#define IRawElementProviderSimple2_GetPatternProvider(This,patternId,pRetVal) (This)->lpVtbl->GetPatternProvider(This,patternId,pRetVal)
#define IRawElementProviderSimple2_GetPropertyValue(This,propertyId,pRetVal) (This)->lpVtbl->GetPropertyValue(This,propertyId,pRetVal)
#define IRawElementProviderSimple2_get_HostRawElementProvider(This,pRetVal) (This)->lpVtbl->get_HostRawElementProvider(This,pRetVal)
/*** IRawElementProviderSimple2 methods ***/
#define IRawElementProviderSimple2_ShowContextMenu(This) (This)->lpVtbl->ShowContextMenu(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderSimple2_QueryInterface(IRawElementProviderSimple2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderSimple2_AddRef(IRawElementProviderSimple2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderSimple2_Release(IRawElementProviderSimple2* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderSimple methods ***/
static inline HRESULT IRawElementProviderSimple2_get_ProviderOptions(IRawElementProviderSimple2* This,enum ProviderOptions *pRetVal) {
    return This->lpVtbl->get_ProviderOptions(This,pRetVal);
}
static inline HRESULT IRawElementProviderSimple2_GetPatternProvider(IRawElementProviderSimple2* This,PATTERNID patternId,IUnknown **pRetVal) {
    return This->lpVtbl->GetPatternProvider(This,patternId,pRetVal);
}
static inline HRESULT IRawElementProviderSimple2_GetPropertyValue(IRawElementProviderSimple2* This,PROPERTYID propertyId,VARIANT *pRetVal) {
    return This->lpVtbl->GetPropertyValue(This,propertyId,pRetVal);
}
static inline HRESULT IRawElementProviderSimple2_get_HostRawElementProvider(IRawElementProviderSimple2* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->get_HostRawElementProvider(This,pRetVal);
}
/*** IRawElementProviderSimple2 methods ***/
static inline HRESULT IRawElementProviderSimple2_ShowContextMenu(IRawElementProviderSimple2* This) {
    return This->lpVtbl->ShowContextMenu(This);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderSimple2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRawElementProviderSimple3 interface
 */
#ifndef __IRawElementProviderSimple3_INTERFACE_DEFINED__
#define __IRawElementProviderSimple3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderSimple3, 0xfcf5d820, 0xd7ec, 0x4613, 0xbd,0xf6, 0x42,0xa8,0x4c,0xe7,0xda,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fcf5d820-d7ec-4613-bdf6-42a84ce7daaf")
IRawElementProviderSimple3 : public IRawElementProviderSimple2
{
    virtual HRESULT STDMETHODCALLTYPE GetMetadataValue(
        int targetId,
        METADATAID metadataId,
        VARIANT *returnVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderSimple3, 0xfcf5d820, 0xd7ec, 0x4613, 0xbd,0xf6, 0x42,0xa8,0x4c,0xe7,0xda,0xaf)
#endif
#else
typedef struct IRawElementProviderSimple3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderSimple3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderSimple3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderSimple3 *This);

    /*** IRawElementProviderSimple methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProviderOptions)(
        IRawElementProviderSimple3 *This,
        enum ProviderOptions *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetPatternProvider)(
        IRawElementProviderSimple3 *This,
        PATTERNID patternId,
        IUnknown **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetPropertyValue)(
        IRawElementProviderSimple3 *This,
        PROPERTYID propertyId,
        VARIANT *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_HostRawElementProvider)(
        IRawElementProviderSimple3 *This,
        IRawElementProviderSimple **pRetVal);

    /*** IRawElementProviderSimple2 methods ***/
    HRESULT (STDMETHODCALLTYPE *ShowContextMenu)(
        IRawElementProviderSimple3 *This);

    /*** IRawElementProviderSimple3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadataValue)(
        IRawElementProviderSimple3 *This,
        int targetId,
        METADATAID metadataId,
        VARIANT *returnVal);

    END_INTERFACE
} IRawElementProviderSimple3Vtbl;

interface IRawElementProviderSimple3 {
    CONST_VTBL IRawElementProviderSimple3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderSimple3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderSimple3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderSimple3_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderSimple methods ***/
#define IRawElementProviderSimple3_get_ProviderOptions(This,pRetVal) (This)->lpVtbl->get_ProviderOptions(This,pRetVal)
#define IRawElementProviderSimple3_GetPatternProvider(This,patternId,pRetVal) (This)->lpVtbl->GetPatternProvider(This,patternId,pRetVal)
#define IRawElementProviderSimple3_GetPropertyValue(This,propertyId,pRetVal) (This)->lpVtbl->GetPropertyValue(This,propertyId,pRetVal)
#define IRawElementProviderSimple3_get_HostRawElementProvider(This,pRetVal) (This)->lpVtbl->get_HostRawElementProvider(This,pRetVal)
/*** IRawElementProviderSimple2 methods ***/
#define IRawElementProviderSimple3_ShowContextMenu(This) (This)->lpVtbl->ShowContextMenu(This)
/*** IRawElementProviderSimple3 methods ***/
#define IRawElementProviderSimple3_GetMetadataValue(This,targetId,metadataId,returnVal) (This)->lpVtbl->GetMetadataValue(This,targetId,metadataId,returnVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderSimple3_QueryInterface(IRawElementProviderSimple3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderSimple3_AddRef(IRawElementProviderSimple3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderSimple3_Release(IRawElementProviderSimple3* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderSimple methods ***/
static inline HRESULT IRawElementProviderSimple3_get_ProviderOptions(IRawElementProviderSimple3* This,enum ProviderOptions *pRetVal) {
    return This->lpVtbl->get_ProviderOptions(This,pRetVal);
}
static inline HRESULT IRawElementProviderSimple3_GetPatternProvider(IRawElementProviderSimple3* This,PATTERNID patternId,IUnknown **pRetVal) {
    return This->lpVtbl->GetPatternProvider(This,patternId,pRetVal);
}
static inline HRESULT IRawElementProviderSimple3_GetPropertyValue(IRawElementProviderSimple3* This,PROPERTYID propertyId,VARIANT *pRetVal) {
    return This->lpVtbl->GetPropertyValue(This,propertyId,pRetVal);
}
static inline HRESULT IRawElementProviderSimple3_get_HostRawElementProvider(IRawElementProviderSimple3* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->get_HostRawElementProvider(This,pRetVal);
}
/*** IRawElementProviderSimple2 methods ***/
static inline HRESULT IRawElementProviderSimple3_ShowContextMenu(IRawElementProviderSimple3* This) {
    return This->lpVtbl->ShowContextMenu(This);
}
/*** IRawElementProviderSimple3 methods ***/
static inline HRESULT IRawElementProviderSimple3_GetMetadataValue(IRawElementProviderSimple3* This,int targetId,METADATAID metadataId,VARIANT *returnVal) {
    return This->lpVtbl->GetMetadataValue(This,targetId,metadataId,returnVal);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderSimple3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAccessibleEx interface
 */
#ifndef __IAccessibleEx_INTERFACE_DEFINED__
#define __IAccessibleEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAccessibleEx, 0xf8b80ada, 0x2c44, 0x48d0, 0x89,0xbe, 0x5f,0xf2,0x3c,0x9c,0xd8,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f8b80ada-2c44-48d0-89be-5ff23c9cd875")
IAccessibleEx : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetObjectForChild(
        LONG idChild,
        IAccessibleEx **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIAccessiblePair(
        IAccessible **ppAcc,
        LONG *pidChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRuntimeId(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConvertReturnedElement(
        IRawElementProviderSimple *pIn,
        IAccessibleEx **ppRetValOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAccessibleEx, 0xf8b80ada, 0x2c44, 0x48d0, 0x89,0xbe, 0x5f,0xf2,0x3c,0x9c,0xd8,0x75)
#endif
#else
typedef struct IAccessibleExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAccessibleEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAccessibleEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAccessibleEx *This);

    /*** IAccessibleEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetObjectForChild)(
        IAccessibleEx *This,
        LONG idChild,
        IAccessibleEx **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetIAccessiblePair)(
        IAccessibleEx *This,
        IAccessible **ppAcc,
        LONG *pidChild);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeId)(
        IAccessibleEx *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *ConvertReturnedElement)(
        IAccessibleEx *This,
        IRawElementProviderSimple *pIn,
        IAccessibleEx **ppRetValOut);

    END_INTERFACE
} IAccessibleExVtbl;

interface IAccessibleEx {
    CONST_VTBL IAccessibleExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAccessibleEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAccessibleEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAccessibleEx_Release(This) (This)->lpVtbl->Release(This)
/*** IAccessibleEx methods ***/
#define IAccessibleEx_GetObjectForChild(This,idChild,pRetVal) (This)->lpVtbl->GetObjectForChild(This,idChild,pRetVal)
#define IAccessibleEx_GetIAccessiblePair(This,ppAcc,pidChild) (This)->lpVtbl->GetIAccessiblePair(This,ppAcc,pidChild)
#define IAccessibleEx_GetRuntimeId(This,pRetVal) (This)->lpVtbl->GetRuntimeId(This,pRetVal)
#define IAccessibleEx_ConvertReturnedElement(This,pIn,ppRetValOut) (This)->lpVtbl->ConvertReturnedElement(This,pIn,ppRetValOut)
#else
/*** IUnknown methods ***/
static inline HRESULT IAccessibleEx_QueryInterface(IAccessibleEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAccessibleEx_AddRef(IAccessibleEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAccessibleEx_Release(IAccessibleEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IAccessibleEx methods ***/
static inline HRESULT IAccessibleEx_GetObjectForChild(IAccessibleEx* This,LONG idChild,IAccessibleEx **pRetVal) {
    return This->lpVtbl->GetObjectForChild(This,idChild,pRetVal);
}
static inline HRESULT IAccessibleEx_GetIAccessiblePair(IAccessibleEx* This,IAccessible **ppAcc,LONG *pidChild) {
    return This->lpVtbl->GetIAccessiblePair(This,ppAcc,pidChild);
}
static inline HRESULT IAccessibleEx_GetRuntimeId(IAccessibleEx* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetRuntimeId(This,pRetVal);
}
static inline HRESULT IAccessibleEx_ConvertReturnedElement(IAccessibleEx* This,IRawElementProviderSimple *pIn,IAccessibleEx **ppRetValOut) {
    return This->lpVtbl->ConvertReturnedElement(This,pIn,ppRetValOut);
}
#endif
#endif

#endif


#endif  /* __IAccessibleEx_INTERFACE_DEFINED__ */

#ifndef __IRawElementProviderFragmentRoot_FWD_DEFINED__
#define __IRawElementProviderFragmentRoot_FWD_DEFINED__
typedef interface IRawElementProviderFragmentRoot IRawElementProviderFragmentRoot;
#ifdef __cplusplus
interface IRawElementProviderFragmentRoot;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IRawElementProviderFragment interface
 */
#ifndef __IRawElementProviderFragment_INTERFACE_DEFINED__
#define __IRawElementProviderFragment_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderFragment, 0xf7063da8, 0x8359, 0x439c, 0x92,0x97, 0xbb,0xc5,0x29,0x9a,0x7d,0x87);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f7063da8-8359-439c-9297-bbc5299a7d87")
IRawElementProviderFragment : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Navigate(
        enum NavigateDirection direction,
        IRawElementProviderFragment **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRuntimeId(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BoundingRectangle(
        struct UiaRect *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEmbeddedFragmentRoots(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFocus(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FragmentRoot(
        IRawElementProviderFragmentRoot **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderFragment, 0xf7063da8, 0x8359, 0x439c, 0x92,0x97, 0xbb,0xc5,0x29,0x9a,0x7d,0x87)
#endif
#else
typedef struct IRawElementProviderFragmentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderFragment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderFragment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderFragment *This);

    /*** IRawElementProviderFragment methods ***/
    HRESULT (STDMETHODCALLTYPE *Navigate)(
        IRawElementProviderFragment *This,
        enum NavigateDirection direction,
        IRawElementProviderFragment **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeId)(
        IRawElementProviderFragment *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_BoundingRectangle)(
        IRawElementProviderFragment *This,
        struct UiaRect *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetEmbeddedFragmentRoots)(
        IRawElementProviderFragment *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetFocus)(
        IRawElementProviderFragment *This);

    HRESULT (STDMETHODCALLTYPE *get_FragmentRoot)(
        IRawElementProviderFragment *This,
        IRawElementProviderFragmentRoot **pRetVal);

    END_INTERFACE
} IRawElementProviderFragmentVtbl;

interface IRawElementProviderFragment {
    CONST_VTBL IRawElementProviderFragmentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderFragment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderFragment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderFragment_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderFragment methods ***/
#define IRawElementProviderFragment_Navigate(This,direction,pRetVal) (This)->lpVtbl->Navigate(This,direction,pRetVal)
#define IRawElementProviderFragment_GetRuntimeId(This,pRetVal) (This)->lpVtbl->GetRuntimeId(This,pRetVal)
#define IRawElementProviderFragment_get_BoundingRectangle(This,pRetVal) (This)->lpVtbl->get_BoundingRectangle(This,pRetVal)
#define IRawElementProviderFragment_GetEmbeddedFragmentRoots(This,pRetVal) (This)->lpVtbl->GetEmbeddedFragmentRoots(This,pRetVal)
#define IRawElementProviderFragment_SetFocus(This) (This)->lpVtbl->SetFocus(This)
#define IRawElementProviderFragment_get_FragmentRoot(This,pRetVal) (This)->lpVtbl->get_FragmentRoot(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderFragment_QueryInterface(IRawElementProviderFragment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderFragment_AddRef(IRawElementProviderFragment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderFragment_Release(IRawElementProviderFragment* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderFragment methods ***/
static inline HRESULT IRawElementProviderFragment_Navigate(IRawElementProviderFragment* This,enum NavigateDirection direction,IRawElementProviderFragment **pRetVal) {
    return This->lpVtbl->Navigate(This,direction,pRetVal);
}
static inline HRESULT IRawElementProviderFragment_GetRuntimeId(IRawElementProviderFragment* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetRuntimeId(This,pRetVal);
}
static inline HRESULT IRawElementProviderFragment_get_BoundingRectangle(IRawElementProviderFragment* This,struct UiaRect *pRetVal) {
    return This->lpVtbl->get_BoundingRectangle(This,pRetVal);
}
static inline HRESULT IRawElementProviderFragment_GetEmbeddedFragmentRoots(IRawElementProviderFragment* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetEmbeddedFragmentRoots(This,pRetVal);
}
static inline HRESULT IRawElementProviderFragment_SetFocus(IRawElementProviderFragment* This) {
    return This->lpVtbl->SetFocus(This);
}
static inline HRESULT IRawElementProviderFragment_get_FragmentRoot(IRawElementProviderFragment* This,IRawElementProviderFragmentRoot **pRetVal) {
    return This->lpVtbl->get_FragmentRoot(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderFragment_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRawElementProviderFragmentRoot interface
 */
#ifndef __IRawElementProviderFragmentRoot_INTERFACE_DEFINED__
#define __IRawElementProviderFragmentRoot_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderFragmentRoot, 0x620ce2a5, 0xab8f, 0x40a9, 0x86,0xcb, 0xde,0x3c,0x75,0x59,0x9b,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("620ce2a5-ab8f-40a9-86cb-de3c75599b58")
IRawElementProviderFragmentRoot : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ElementProviderFromPoint(
        double x,
        double y,
        IRawElementProviderFragment **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFocus(
        IRawElementProviderFragment **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderFragmentRoot, 0x620ce2a5, 0xab8f, 0x40a9, 0x86,0xcb, 0xde,0x3c,0x75,0x59,0x9b,0x58)
#endif
#else
typedef struct IRawElementProviderFragmentRootVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderFragmentRoot *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderFragmentRoot *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderFragmentRoot *This);

    /*** IRawElementProviderFragmentRoot methods ***/
    HRESULT (STDMETHODCALLTYPE *ElementProviderFromPoint)(
        IRawElementProviderFragmentRoot *This,
        double x,
        double y,
        IRawElementProviderFragment **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetFocus)(
        IRawElementProviderFragmentRoot *This,
        IRawElementProviderFragment **pRetVal);

    END_INTERFACE
} IRawElementProviderFragmentRootVtbl;

interface IRawElementProviderFragmentRoot {
    CONST_VTBL IRawElementProviderFragmentRootVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderFragmentRoot_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderFragmentRoot_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderFragmentRoot_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderFragmentRoot methods ***/
#define IRawElementProviderFragmentRoot_ElementProviderFromPoint(This,x,y,pRetVal) (This)->lpVtbl->ElementProviderFromPoint(This,x,y,pRetVal)
#define IRawElementProviderFragmentRoot_GetFocus(This,pRetVal) (This)->lpVtbl->GetFocus(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderFragmentRoot_QueryInterface(IRawElementProviderFragmentRoot* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderFragmentRoot_AddRef(IRawElementProviderFragmentRoot* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderFragmentRoot_Release(IRawElementProviderFragmentRoot* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderFragmentRoot methods ***/
static inline HRESULT IRawElementProviderFragmentRoot_ElementProviderFromPoint(IRawElementProviderFragmentRoot* This,double x,double y,IRawElementProviderFragment **pRetVal) {
    return This->lpVtbl->ElementProviderFromPoint(This,x,y,pRetVal);
}
static inline HRESULT IRawElementProviderFragmentRoot_GetFocus(IRawElementProviderFragmentRoot* This,IRawElementProviderFragment **pRetVal) {
    return This->lpVtbl->GetFocus(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderFragmentRoot_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRawElementProviderHwndOverride interface
 */
#ifndef __IRawElementProviderHwndOverride_INTERFACE_DEFINED__
#define __IRawElementProviderHwndOverride_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderHwndOverride, 0x1d5df27c, 0x8947, 0x4425, 0xb8,0xd9, 0x79,0x78,0x7b,0xb4,0x60,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1d5df27c-8947-4425-b8d9-79787bb460b8")
IRawElementProviderHwndOverride : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOverrideProviderForHwnd(
        HWND hwnd,
        IRawElementProviderSimple **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderHwndOverride, 0x1d5df27c, 0x8947, 0x4425, 0xb8,0xd9, 0x79,0x78,0x7b,0xb4,0x60,0xb8)
#endif
#else
typedef struct IRawElementProviderHwndOverrideVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderHwndOverride *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderHwndOverride *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderHwndOverride *This);

    /*** IRawElementProviderHwndOverride methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOverrideProviderForHwnd)(
        IRawElementProviderHwndOverride *This,
        HWND hwnd,
        IRawElementProviderSimple **pRetVal);

    END_INTERFACE
} IRawElementProviderHwndOverrideVtbl;

interface IRawElementProviderHwndOverride {
    CONST_VTBL IRawElementProviderHwndOverrideVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderHwndOverride_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderHwndOverride_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderHwndOverride_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderHwndOverride methods ***/
#define IRawElementProviderHwndOverride_GetOverrideProviderForHwnd(This,hwnd,pRetVal) (This)->lpVtbl->GetOverrideProviderForHwnd(This,hwnd,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderHwndOverride_QueryInterface(IRawElementProviderHwndOverride* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderHwndOverride_AddRef(IRawElementProviderHwndOverride* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderHwndOverride_Release(IRawElementProviderHwndOverride* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderHwndOverride methods ***/
static inline HRESULT IRawElementProviderHwndOverride_GetOverrideProviderForHwnd(IRawElementProviderHwndOverride* This,HWND hwnd,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->GetOverrideProviderForHwnd(This,hwnd,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderHwndOverride_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRawElementProviderAdviseEvents interface
 */
#ifndef __IRawElementProviderAdviseEvents_INTERFACE_DEFINED__
#define __IRawElementProviderAdviseEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderAdviseEvents, 0xa407b27b, 0x0f6d, 0x4427, 0x92,0x92, 0x47,0x3c,0x7b,0xf9,0x32,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a407b27b-0f6d-4427-9292-473c7bf93258")
IRawElementProviderAdviseEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseEventAdded(
        EVENTID eventId,
        SAFEARRAY *propertyIDs) = 0;

    virtual HRESULT STDMETHODCALLTYPE AdviseEventRemoved(
        EVENTID eventId,
        SAFEARRAY *propertyIDs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderAdviseEvents, 0xa407b27b, 0x0f6d, 0x4427, 0x92,0x92, 0x47,0x3c,0x7b,0xf9,0x32,0x58)
#endif
#else
typedef struct IRawElementProviderAdviseEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderAdviseEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderAdviseEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderAdviseEvents *This);

    /*** IRawElementProviderAdviseEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseEventAdded)(
        IRawElementProviderAdviseEvents *This,
        EVENTID eventId,
        SAFEARRAY *propertyIDs);

    HRESULT (STDMETHODCALLTYPE *AdviseEventRemoved)(
        IRawElementProviderAdviseEvents *This,
        EVENTID eventId,
        SAFEARRAY *propertyIDs);

    END_INTERFACE
} IRawElementProviderAdviseEventsVtbl;

interface IRawElementProviderAdviseEvents {
    CONST_VTBL IRawElementProviderAdviseEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderAdviseEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderAdviseEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderAdviseEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderAdviseEvents methods ***/
#define IRawElementProviderAdviseEvents_AdviseEventAdded(This,eventId,propertyIDs) (This)->lpVtbl->AdviseEventAdded(This,eventId,propertyIDs)
#define IRawElementProviderAdviseEvents_AdviseEventRemoved(This,eventId,propertyIDs) (This)->lpVtbl->AdviseEventRemoved(This,eventId,propertyIDs)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderAdviseEvents_QueryInterface(IRawElementProviderAdviseEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderAdviseEvents_AddRef(IRawElementProviderAdviseEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderAdviseEvents_Release(IRawElementProviderAdviseEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderAdviseEvents methods ***/
static inline HRESULT IRawElementProviderAdviseEvents_AdviseEventAdded(IRawElementProviderAdviseEvents* This,EVENTID eventId,SAFEARRAY *propertyIDs) {
    return This->lpVtbl->AdviseEventAdded(This,eventId,propertyIDs);
}
static inline HRESULT IRawElementProviderAdviseEvents_AdviseEventRemoved(IRawElementProviderAdviseEvents* This,EVENTID eventId,SAFEARRAY *propertyIDs) {
    return This->lpVtbl->AdviseEventRemoved(This,eventId,propertyIDs);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderAdviseEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IProxyProviderWinEventSink interface
 */
#ifndef __IProxyProviderWinEventSink_INTERFACE_DEFINED__
#define __IProxyProviderWinEventSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProxyProviderWinEventSink, 0x4fd82b78, 0xa43e, 0x46ac, 0x98,0x03, 0x0a,0x69,0x69,0xc7,0xc1,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4fd82b78-a43e-46ac-9803-0a6969c7c183")
IProxyProviderWinEventSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddAutomationPropertyChangedEvent(
        IRawElementProviderSimple *pProvider,
        PROPERTYID id,
        VARIANT newValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddAutomationEvent(
        IRawElementProviderSimple *pProvider,
        EVENTID id) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStructureChangedEvent(
        IRawElementProviderSimple *pProvider,
        enum StructureChangeType structureChangeType,
        SAFEARRAY *runtimeId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProxyProviderWinEventSink, 0x4fd82b78, 0xa43e, 0x46ac, 0x98,0x03, 0x0a,0x69,0x69,0xc7,0xc1,0x83)
#endif
#else
typedef struct IProxyProviderWinEventSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProxyProviderWinEventSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProxyProviderWinEventSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProxyProviderWinEventSink *This);

    /*** IProxyProviderWinEventSink methods ***/
    HRESULT (STDMETHODCALLTYPE *AddAutomationPropertyChangedEvent)(
        IProxyProviderWinEventSink *This,
        IRawElementProviderSimple *pProvider,
        PROPERTYID id,
        VARIANT newValue);

    HRESULT (STDMETHODCALLTYPE *AddAutomationEvent)(
        IProxyProviderWinEventSink *This,
        IRawElementProviderSimple *pProvider,
        EVENTID id);

    HRESULT (STDMETHODCALLTYPE *AddStructureChangedEvent)(
        IProxyProviderWinEventSink *This,
        IRawElementProviderSimple *pProvider,
        enum StructureChangeType structureChangeType,
        SAFEARRAY *runtimeId);

    END_INTERFACE
} IProxyProviderWinEventSinkVtbl;

interface IProxyProviderWinEventSink {
    CONST_VTBL IProxyProviderWinEventSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProxyProviderWinEventSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProxyProviderWinEventSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProxyProviderWinEventSink_Release(This) (This)->lpVtbl->Release(This)
/*** IProxyProviderWinEventSink methods ***/
#define IProxyProviderWinEventSink_AddAutomationPropertyChangedEvent(This,pProvider,id,newValue) (This)->lpVtbl->AddAutomationPropertyChangedEvent(This,pProvider,id,newValue)
#define IProxyProviderWinEventSink_AddAutomationEvent(This,pProvider,id) (This)->lpVtbl->AddAutomationEvent(This,pProvider,id)
#define IProxyProviderWinEventSink_AddStructureChangedEvent(This,pProvider,structureChangeType,runtimeId) (This)->lpVtbl->AddStructureChangedEvent(This,pProvider,structureChangeType,runtimeId)
#else
/*** IUnknown methods ***/
static inline HRESULT IProxyProviderWinEventSink_QueryInterface(IProxyProviderWinEventSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProxyProviderWinEventSink_AddRef(IProxyProviderWinEventSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProxyProviderWinEventSink_Release(IProxyProviderWinEventSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IProxyProviderWinEventSink methods ***/
static inline HRESULT IProxyProviderWinEventSink_AddAutomationPropertyChangedEvent(IProxyProviderWinEventSink* This,IRawElementProviderSimple *pProvider,PROPERTYID id,VARIANT newValue) {
    return This->lpVtbl->AddAutomationPropertyChangedEvent(This,pProvider,id,newValue);
}
static inline HRESULT IProxyProviderWinEventSink_AddAutomationEvent(IProxyProviderWinEventSink* This,IRawElementProviderSimple *pProvider,EVENTID id) {
    return This->lpVtbl->AddAutomationEvent(This,pProvider,id);
}
static inline HRESULT IProxyProviderWinEventSink_AddStructureChangedEvent(IProxyProviderWinEventSink* This,IRawElementProviderSimple *pProvider,enum StructureChangeType structureChangeType,SAFEARRAY *runtimeId) {
    return This->lpVtbl->AddStructureChangedEvent(This,pProvider,structureChangeType,runtimeId);
}
#endif
#endif

#endif


#endif  /* __IProxyProviderWinEventSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IProxyProviderWinEventHandler interface
 */
#ifndef __IProxyProviderWinEventHandler_INTERFACE_DEFINED__
#define __IProxyProviderWinEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProxyProviderWinEventHandler, 0x89592ad4, 0xf4e0, 0x43d5, 0xa3,0xb6, 0xba,0xd7,0xe1,0x11,0xb4,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("89592ad4-f4e0-43d5-a3b6-bad7e111b435")
IProxyProviderWinEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RespondToWinEvent(
        DWORD idWinEvent,
        HWND hwnd,
        LONG idObject,
        LONG idChild,
        IProxyProviderWinEventSink *pSink) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProxyProviderWinEventHandler, 0x89592ad4, 0xf4e0, 0x43d5, 0xa3,0xb6, 0xba,0xd7,0xe1,0x11,0xb4,0x35)
#endif
#else
typedef struct IProxyProviderWinEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProxyProviderWinEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProxyProviderWinEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProxyProviderWinEventHandler *This);

    /*** IProxyProviderWinEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *RespondToWinEvent)(
        IProxyProviderWinEventHandler *This,
        DWORD idWinEvent,
        HWND hwnd,
        LONG idObject,
        LONG idChild,
        IProxyProviderWinEventSink *pSink);

    END_INTERFACE
} IProxyProviderWinEventHandlerVtbl;

interface IProxyProviderWinEventHandler {
    CONST_VTBL IProxyProviderWinEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProxyProviderWinEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProxyProviderWinEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProxyProviderWinEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IProxyProviderWinEventHandler methods ***/
#define IProxyProviderWinEventHandler_RespondToWinEvent(This,idWinEvent,hwnd,idObject,idChild,pSink) (This)->lpVtbl->RespondToWinEvent(This,idWinEvent,hwnd,idObject,idChild,pSink)
#else
/*** IUnknown methods ***/
static inline HRESULT IProxyProviderWinEventHandler_QueryInterface(IProxyProviderWinEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProxyProviderWinEventHandler_AddRef(IProxyProviderWinEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProxyProviderWinEventHandler_Release(IProxyProviderWinEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IProxyProviderWinEventHandler methods ***/
static inline HRESULT IProxyProviderWinEventHandler_RespondToWinEvent(IProxyProviderWinEventHandler* This,DWORD idWinEvent,HWND hwnd,LONG idObject,LONG idChild,IProxyProviderWinEventSink *pSink) {
    return This->lpVtbl->RespondToWinEvent(This,idWinEvent,hwnd,idObject,idChild,pSink);
}
#endif
#endif

#endif


#endif  /* __IProxyProviderWinEventHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRawElementProviderWindowlessSite interface
 */
#ifndef __IRawElementProviderWindowlessSite_INTERFACE_DEFINED__
#define __IRawElementProviderWindowlessSite_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderWindowlessSite, 0x0a2a93cc, 0xbfad, 0x42ac, 0x9b,0x2e, 0x09,0x91,0xfb,0x0d,0x3e,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0a2a93cc-bfad-42ac-9b2e-0991fb0d3ea0")
IRawElementProviderWindowlessSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetAdjacentFragment(
        enum NavigateDirection direction,
        IRawElementProviderFragment **ppParent) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRuntimeIdPrefix(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderWindowlessSite, 0x0a2a93cc, 0xbfad, 0x42ac, 0x9b,0x2e, 0x09,0x91,0xfb,0x0d,0x3e,0xa0)
#endif
#else
typedef struct IRawElementProviderWindowlessSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderWindowlessSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderWindowlessSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderWindowlessSite *This);

    /*** IRawElementProviderWindowlessSite methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAdjacentFragment)(
        IRawElementProviderWindowlessSite *This,
        enum NavigateDirection direction,
        IRawElementProviderFragment **ppParent);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeIdPrefix)(
        IRawElementProviderWindowlessSite *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} IRawElementProviderWindowlessSiteVtbl;

interface IRawElementProviderWindowlessSite {
    CONST_VTBL IRawElementProviderWindowlessSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderWindowlessSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderWindowlessSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderWindowlessSite_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderWindowlessSite methods ***/
#define IRawElementProviderWindowlessSite_GetAdjacentFragment(This,direction,ppParent) (This)->lpVtbl->GetAdjacentFragment(This,direction,ppParent)
#define IRawElementProviderWindowlessSite_GetRuntimeIdPrefix(This,pRetVal) (This)->lpVtbl->GetRuntimeIdPrefix(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderWindowlessSite_QueryInterface(IRawElementProviderWindowlessSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderWindowlessSite_AddRef(IRawElementProviderWindowlessSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderWindowlessSite_Release(IRawElementProviderWindowlessSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderWindowlessSite methods ***/
static inline HRESULT IRawElementProviderWindowlessSite_GetAdjacentFragment(IRawElementProviderWindowlessSite* This,enum NavigateDirection direction,IRawElementProviderFragment **ppParent) {
    return This->lpVtbl->GetAdjacentFragment(This,direction,ppParent);
}
static inline HRESULT IRawElementProviderWindowlessSite_GetRuntimeIdPrefix(IRawElementProviderWindowlessSite* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetRuntimeIdPrefix(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderWindowlessSite_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAccessibleHostingElementProviders interface
 */
#ifndef __IAccessibleHostingElementProviders_INTERFACE_DEFINED__
#define __IAccessibleHostingElementProviders_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAccessibleHostingElementProviders, 0x33ac331b, 0x943e, 0x4020, 0xb2,0x95, 0xdb,0x37,0x78,0x49,0x74,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("33ac331b-943e-4020-b295-db37784974a3")
IAccessibleHostingElementProviders : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEmbeddedFragmentRoots(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectIdForProvider(
        IRawElementProviderSimple *pProvider,
        LONG *pidObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAccessibleHostingElementProviders, 0x33ac331b, 0x943e, 0x4020, 0xb2,0x95, 0xdb,0x37,0x78,0x49,0x74,0xa3)
#endif
#else
typedef struct IAccessibleHostingElementProvidersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAccessibleHostingElementProviders *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAccessibleHostingElementProviders *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAccessibleHostingElementProviders *This);

    /*** IAccessibleHostingElementProviders methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEmbeddedFragmentRoots)(
        IAccessibleHostingElementProviders *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetObjectIdForProvider)(
        IAccessibleHostingElementProviders *This,
        IRawElementProviderSimple *pProvider,
        LONG *pidObject);

    END_INTERFACE
} IAccessibleHostingElementProvidersVtbl;

interface IAccessibleHostingElementProviders {
    CONST_VTBL IAccessibleHostingElementProvidersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAccessibleHostingElementProviders_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAccessibleHostingElementProviders_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAccessibleHostingElementProviders_Release(This) (This)->lpVtbl->Release(This)
/*** IAccessibleHostingElementProviders methods ***/
#define IAccessibleHostingElementProviders_GetEmbeddedFragmentRoots(This,pRetVal) (This)->lpVtbl->GetEmbeddedFragmentRoots(This,pRetVal)
#define IAccessibleHostingElementProviders_GetObjectIdForProvider(This,pProvider,pidObject) (This)->lpVtbl->GetObjectIdForProvider(This,pProvider,pidObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IAccessibleHostingElementProviders_QueryInterface(IAccessibleHostingElementProviders* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAccessibleHostingElementProviders_AddRef(IAccessibleHostingElementProviders* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAccessibleHostingElementProviders_Release(IAccessibleHostingElementProviders* This) {
    return This->lpVtbl->Release(This);
}
/*** IAccessibleHostingElementProviders methods ***/
static inline HRESULT IAccessibleHostingElementProviders_GetEmbeddedFragmentRoots(IAccessibleHostingElementProviders* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetEmbeddedFragmentRoots(This,pRetVal);
}
static inline HRESULT IAccessibleHostingElementProviders_GetObjectIdForProvider(IAccessibleHostingElementProviders* This,IRawElementProviderSimple *pProvider,LONG *pidObject) {
    return This->lpVtbl->GetObjectIdForProvider(This,pProvider,pidObject);
}
#endif
#endif

#endif


#endif  /* __IAccessibleHostingElementProviders_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRawElementProviderHostingAccessibles interface
 */
#ifndef __IRawElementProviderHostingAccessibles_INTERFACE_DEFINED__
#define __IRawElementProviderHostingAccessibles_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRawElementProviderHostingAccessibles, 0x24be0b07, 0xd37d, 0x487a, 0x98,0xcf, 0xa1,0x3e,0xd4,0x65,0xe9,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("24be0b07-d37d-487a-98cf-a13ed465e9b3")
IRawElementProviderHostingAccessibles : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEmbeddedAccessibles(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRawElementProviderHostingAccessibles, 0x24be0b07, 0xd37d, 0x487a, 0x98,0xcf, 0xa1,0x3e,0xd4,0x65,0xe9,0xb3)
#endif
#else
typedef struct IRawElementProviderHostingAccessiblesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRawElementProviderHostingAccessibles *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRawElementProviderHostingAccessibles *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRawElementProviderHostingAccessibles *This);

    /*** IRawElementProviderHostingAccessibles methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEmbeddedAccessibles)(
        IRawElementProviderHostingAccessibles *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} IRawElementProviderHostingAccessiblesVtbl;

interface IRawElementProviderHostingAccessibles {
    CONST_VTBL IRawElementProviderHostingAccessiblesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRawElementProviderHostingAccessibles_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRawElementProviderHostingAccessibles_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRawElementProviderHostingAccessibles_Release(This) (This)->lpVtbl->Release(This)
/*** IRawElementProviderHostingAccessibles methods ***/
#define IRawElementProviderHostingAccessibles_GetEmbeddedAccessibles(This,pRetVal) (This)->lpVtbl->GetEmbeddedAccessibles(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRawElementProviderHostingAccessibles_QueryInterface(IRawElementProviderHostingAccessibles* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRawElementProviderHostingAccessibles_AddRef(IRawElementProviderHostingAccessibles* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRawElementProviderHostingAccessibles_Release(IRawElementProviderHostingAccessibles* This) {
    return This->lpVtbl->Release(This);
}
/*** IRawElementProviderHostingAccessibles methods ***/
static inline HRESULT IRawElementProviderHostingAccessibles_GetEmbeddedAccessibles(IRawElementProviderHostingAccessibles* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetEmbeddedAccessibles(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRawElementProviderHostingAccessibles_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ILegacyIAccessibleProvider interface
 */
#ifndef __ILegacyIAccessibleProvider_INTERFACE_DEFINED__
#define __ILegacyIAccessibleProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ILegacyIAccessibleProvider, 0xe44c3566, 0x915d, 0x4070, 0x99,0xc6, 0x04,0x7b,0xff,0x5a,0x08,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e44c3566-915d-4070-99c6-047bff5a08f5")
ILegacyIAccessibleProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Select(
        LONG flagsSelect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoDefaultAction(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        LPCWSTR szValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIAccessible(
        IAccessible **ppAccessible) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ChildId(
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Value(
        BSTR *pszValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *pszDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Role(
        DWORD *pdwRole) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_State(
        DWORD *pdwState) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Help(
        BSTR *pszHelp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_KeyboardShortcut(
        BSTR *pszKeyboardShortcut) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSelection(
        SAFEARRAY **pvarSelectedChildren) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DefaultAction(
        BSTR *pszDefaultAction) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILegacyIAccessibleProvider, 0xe44c3566, 0x915d, 0x4070, 0x99,0xc6, 0x04,0x7b,0xff,0x5a,0x08,0xf5)
#endif
#else
typedef struct ILegacyIAccessibleProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILegacyIAccessibleProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILegacyIAccessibleProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILegacyIAccessibleProvider *This);

    /*** ILegacyIAccessibleProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Select)(
        ILegacyIAccessibleProvider *This,
        LONG flagsSelect);

    HRESULT (STDMETHODCALLTYPE *DoDefaultAction)(
        ILegacyIAccessibleProvider *This);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        ILegacyIAccessibleProvider *This,
        LPCWSTR szValue);

    HRESULT (STDMETHODCALLTYPE *GetIAccessible)(
        ILegacyIAccessibleProvider *This,
        IAccessible **ppAccessible);

    HRESULT (STDMETHODCALLTYPE *get_ChildId)(
        ILegacyIAccessibleProvider *This,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ILegacyIAccessibleProvider *This,
        BSTR *pszName);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        ILegacyIAccessibleProvider *This,
        BSTR *pszValue);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        ILegacyIAccessibleProvider *This,
        BSTR *pszDescription);

    HRESULT (STDMETHODCALLTYPE *get_Role)(
        ILegacyIAccessibleProvider *This,
        DWORD *pdwRole);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        ILegacyIAccessibleProvider *This,
        DWORD *pdwState);

    HRESULT (STDMETHODCALLTYPE *get_Help)(
        ILegacyIAccessibleProvider *This,
        BSTR *pszHelp);

    HRESULT (STDMETHODCALLTYPE *get_KeyboardShortcut)(
        ILegacyIAccessibleProvider *This,
        BSTR *pszKeyboardShortcut);

    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        ILegacyIAccessibleProvider *This,
        SAFEARRAY **pvarSelectedChildren);

    HRESULT (STDMETHODCALLTYPE *get_DefaultAction)(
        ILegacyIAccessibleProvider *This,
        BSTR *pszDefaultAction);

    END_INTERFACE
} ILegacyIAccessibleProviderVtbl;

interface ILegacyIAccessibleProvider {
    CONST_VTBL ILegacyIAccessibleProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILegacyIAccessibleProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILegacyIAccessibleProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILegacyIAccessibleProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ILegacyIAccessibleProvider methods ***/
#define ILegacyIAccessibleProvider_Select(This,flagsSelect) (This)->lpVtbl->Select(This,flagsSelect)
#define ILegacyIAccessibleProvider_DoDefaultAction(This) (This)->lpVtbl->DoDefaultAction(This)
#define ILegacyIAccessibleProvider_SetValue(This,szValue) (This)->lpVtbl->SetValue(This,szValue)
#define ILegacyIAccessibleProvider_GetIAccessible(This,ppAccessible) (This)->lpVtbl->GetIAccessible(This,ppAccessible)
#define ILegacyIAccessibleProvider_get_ChildId(This,pRetVal) (This)->lpVtbl->get_ChildId(This,pRetVal)
#define ILegacyIAccessibleProvider_get_Name(This,pszName) (This)->lpVtbl->get_Name(This,pszName)
#define ILegacyIAccessibleProvider_get_Value(This,pszValue) (This)->lpVtbl->get_Value(This,pszValue)
#define ILegacyIAccessibleProvider_get_Description(This,pszDescription) (This)->lpVtbl->get_Description(This,pszDescription)
#define ILegacyIAccessibleProvider_get_Role(This,pdwRole) (This)->lpVtbl->get_Role(This,pdwRole)
#define ILegacyIAccessibleProvider_get_State(This,pdwState) (This)->lpVtbl->get_State(This,pdwState)
#define ILegacyIAccessibleProvider_get_Help(This,pszHelp) (This)->lpVtbl->get_Help(This,pszHelp)
#define ILegacyIAccessibleProvider_get_KeyboardShortcut(This,pszKeyboardShortcut) (This)->lpVtbl->get_KeyboardShortcut(This,pszKeyboardShortcut)
#define ILegacyIAccessibleProvider_GetSelection(This,pvarSelectedChildren) (This)->lpVtbl->GetSelection(This,pvarSelectedChildren)
#define ILegacyIAccessibleProvider_get_DefaultAction(This,pszDefaultAction) (This)->lpVtbl->get_DefaultAction(This,pszDefaultAction)
#else
/*** IUnknown methods ***/
static inline HRESULT ILegacyIAccessibleProvider_QueryInterface(ILegacyIAccessibleProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ILegacyIAccessibleProvider_AddRef(ILegacyIAccessibleProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ILegacyIAccessibleProvider_Release(ILegacyIAccessibleProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ILegacyIAccessibleProvider methods ***/
static inline HRESULT ILegacyIAccessibleProvider_Select(ILegacyIAccessibleProvider* This,LONG flagsSelect) {
    return This->lpVtbl->Select(This,flagsSelect);
}
static inline HRESULT ILegacyIAccessibleProvider_DoDefaultAction(ILegacyIAccessibleProvider* This) {
    return This->lpVtbl->DoDefaultAction(This);
}
static inline HRESULT ILegacyIAccessibleProvider_SetValue(ILegacyIAccessibleProvider* This,LPCWSTR szValue) {
    return This->lpVtbl->SetValue(This,szValue);
}
static inline HRESULT ILegacyIAccessibleProvider_GetIAccessible(ILegacyIAccessibleProvider* This,IAccessible **ppAccessible) {
    return This->lpVtbl->GetIAccessible(This,ppAccessible);
}
static inline HRESULT ILegacyIAccessibleProvider_get_ChildId(ILegacyIAccessibleProvider* This,int *pRetVal) {
    return This->lpVtbl->get_ChildId(This,pRetVal);
}
static inline HRESULT ILegacyIAccessibleProvider_get_Name(ILegacyIAccessibleProvider* This,BSTR *pszName) {
    return This->lpVtbl->get_Name(This,pszName);
}
static inline HRESULT ILegacyIAccessibleProvider_get_Value(ILegacyIAccessibleProvider* This,BSTR *pszValue) {
    return This->lpVtbl->get_Value(This,pszValue);
}
static inline HRESULT ILegacyIAccessibleProvider_get_Description(ILegacyIAccessibleProvider* This,BSTR *pszDescription) {
    return This->lpVtbl->get_Description(This,pszDescription);
}
static inline HRESULT ILegacyIAccessibleProvider_get_Role(ILegacyIAccessibleProvider* This,DWORD *pdwRole) {
    return This->lpVtbl->get_Role(This,pdwRole);
}
static inline HRESULT ILegacyIAccessibleProvider_get_State(ILegacyIAccessibleProvider* This,DWORD *pdwState) {
    return This->lpVtbl->get_State(This,pdwState);
}
static inline HRESULT ILegacyIAccessibleProvider_get_Help(ILegacyIAccessibleProvider* This,BSTR *pszHelp) {
    return This->lpVtbl->get_Help(This,pszHelp);
}
static inline HRESULT ILegacyIAccessibleProvider_get_KeyboardShortcut(ILegacyIAccessibleProvider* This,BSTR *pszKeyboardShortcut) {
    return This->lpVtbl->get_KeyboardShortcut(This,pszKeyboardShortcut);
}
static inline HRESULT ILegacyIAccessibleProvider_GetSelection(ILegacyIAccessibleProvider* This,SAFEARRAY **pvarSelectedChildren) {
    return This->lpVtbl->GetSelection(This,pvarSelectedChildren);
}
static inline HRESULT ILegacyIAccessibleProvider_get_DefaultAction(ILegacyIAccessibleProvider* This,BSTR *pszDefaultAction) {
    return This->lpVtbl->get_DefaultAction(This,pszDefaultAction);
}
#endif
#endif

#endif


#endif  /* __ILegacyIAccessibleProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDockProvider interface
 */
#ifndef __IDockProvider_INTERFACE_DEFINED__
#define __IDockProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDockProvider, 0x159bc72c, 0x4ad3, 0x485e, 0x96,0x37, 0xd7,0x05,0x2e,0xdf,0x01,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("159bc72c-4ad3-485e-9637-d7052edf0146")
IDockProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetDockPosition(
        enum DockPosition dockPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DockPosition(
        enum DockPosition *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDockProvider, 0x159bc72c, 0x4ad3, 0x485e, 0x96,0x37, 0xd7,0x05,0x2e,0xdf,0x01,0x46)
#endif
#else
typedef struct IDockProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDockProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDockProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDockProvider *This);

    /*** IDockProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDockPosition)(
        IDockProvider *This,
        enum DockPosition dockPosition);

    HRESULT (STDMETHODCALLTYPE *get_DockPosition)(
        IDockProvider *This,
        enum DockPosition *pRetVal);

    END_INTERFACE
} IDockProviderVtbl;

interface IDockProvider {
    CONST_VTBL IDockProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDockProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDockProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDockProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IDockProvider methods ***/
#define IDockProvider_SetDockPosition(This,dockPosition) (This)->lpVtbl->SetDockPosition(This,dockPosition)
#define IDockProvider_get_DockPosition(This,pRetVal) (This)->lpVtbl->get_DockPosition(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IDockProvider_QueryInterface(IDockProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDockProvider_AddRef(IDockProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDockProvider_Release(IDockProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IDockProvider methods ***/
static inline HRESULT IDockProvider_SetDockPosition(IDockProvider* This,enum DockPosition dockPosition) {
    return This->lpVtbl->SetDockPosition(This,dockPosition);
}
static inline HRESULT IDockProvider_get_DockPosition(IDockProvider* This,enum DockPosition *pRetVal) {
    return This->lpVtbl->get_DockPosition(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IDockProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IExpandCollapseProvider interface
 */
#ifndef __IExpandCollapseProvider_INTERFACE_DEFINED__
#define __IExpandCollapseProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IExpandCollapseProvider, 0xd847d3a5, 0xcab0, 0x4a98, 0x8c,0x32, 0xec,0xb4,0x5c,0x59,0xad,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d847d3a5-cab0-4a98-8c32-ecb45c59ad24")
IExpandCollapseProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Expand(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Collapse(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExpandCollapseState(
        enum ExpandCollapseState *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IExpandCollapseProvider, 0xd847d3a5, 0xcab0, 0x4a98, 0x8c,0x32, 0xec,0xb4,0x5c,0x59,0xad,0x24)
#endif
#else
typedef struct IExpandCollapseProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IExpandCollapseProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IExpandCollapseProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IExpandCollapseProvider *This);

    /*** IExpandCollapseProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Expand)(
        IExpandCollapseProvider *This);

    HRESULT (STDMETHODCALLTYPE *Collapse)(
        IExpandCollapseProvider *This);

    HRESULT (STDMETHODCALLTYPE *get_ExpandCollapseState)(
        IExpandCollapseProvider *This,
        enum ExpandCollapseState *pRetVal);

    END_INTERFACE
} IExpandCollapseProviderVtbl;

interface IExpandCollapseProvider {
    CONST_VTBL IExpandCollapseProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IExpandCollapseProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExpandCollapseProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExpandCollapseProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IExpandCollapseProvider methods ***/
#define IExpandCollapseProvider_Expand(This) (This)->lpVtbl->Expand(This)
#define IExpandCollapseProvider_Collapse(This) (This)->lpVtbl->Collapse(This)
#define IExpandCollapseProvider_get_ExpandCollapseState(This,pRetVal) (This)->lpVtbl->get_ExpandCollapseState(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IExpandCollapseProvider_QueryInterface(IExpandCollapseProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IExpandCollapseProvider_AddRef(IExpandCollapseProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IExpandCollapseProvider_Release(IExpandCollapseProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IExpandCollapseProvider methods ***/
static inline HRESULT IExpandCollapseProvider_Expand(IExpandCollapseProvider* This) {
    return This->lpVtbl->Expand(This);
}
static inline HRESULT IExpandCollapseProvider_Collapse(IExpandCollapseProvider* This) {
    return This->lpVtbl->Collapse(This);
}
static inline HRESULT IExpandCollapseProvider_get_ExpandCollapseState(IExpandCollapseProvider* This,enum ExpandCollapseState *pRetVal) {
    return This->lpVtbl->get_ExpandCollapseState(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IExpandCollapseProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IGridProvider interface
 */
#ifndef __IGridProvider_INTERFACE_DEFINED__
#define __IGridProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IGridProvider, 0xb17d6187, 0x0907, 0x464b, 0xa1,0x68, 0x0e,0xf1,0x7a,0x15,0x72,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b17d6187-0907-464b-a168-0ef17a1572b1")
IGridProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetItem(
        int row,
        int column,
        IRawElementProviderSimple **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RowCount(
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ColumnCount(
        int *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGridProvider, 0xb17d6187, 0x0907, 0x464b, 0xa1,0x68, 0x0e,0xf1,0x7a,0x15,0x72,0xb1)
#endif
#else
typedef struct IGridProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGridProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGridProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGridProvider *This);

    /*** IGridProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IGridProvider *This,
        int row,
        int column,
        IRawElementProviderSimple **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_RowCount)(
        IGridProvider *This,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_ColumnCount)(
        IGridProvider *This,
        int *pRetVal);

    END_INTERFACE
} IGridProviderVtbl;

interface IGridProvider {
    CONST_VTBL IGridProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGridProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGridProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGridProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IGridProvider methods ***/
#define IGridProvider_GetItem(This,row,column,pRetVal) (This)->lpVtbl->GetItem(This,row,column,pRetVal)
#define IGridProvider_get_RowCount(This,pRetVal) (This)->lpVtbl->get_RowCount(This,pRetVal)
#define IGridProvider_get_ColumnCount(This,pRetVal) (This)->lpVtbl->get_ColumnCount(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IGridProvider_QueryInterface(IGridProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IGridProvider_AddRef(IGridProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IGridProvider_Release(IGridProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IGridProvider methods ***/
static inline HRESULT IGridProvider_GetItem(IGridProvider* This,int row,int column,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->GetItem(This,row,column,pRetVal);
}
static inline HRESULT IGridProvider_get_RowCount(IGridProvider* This,int *pRetVal) {
    return This->lpVtbl->get_RowCount(This,pRetVal);
}
static inline HRESULT IGridProvider_get_ColumnCount(IGridProvider* This,int *pRetVal) {
    return This->lpVtbl->get_ColumnCount(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IGridProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IGridItemProvider interface
 */
#ifndef __IGridItemProvider_INTERFACE_DEFINED__
#define __IGridItemProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IGridItemProvider, 0xd02541f1, 0xfb81, 0x4d64, 0xae,0x32, 0xf5,0x20,0xf8,0xa6,0xdb,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d02541f1-fb81-4d64-ae32-f520f8a6dbd1")
IGridItemProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Row(
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Column(
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RowSpan(
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ColumnSpan(
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ContainingGrid(
        IRawElementProviderSimple **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGridItemProvider, 0xd02541f1, 0xfb81, 0x4d64, 0xae,0x32, 0xf5,0x20,0xf8,0xa6,0xdb,0xd1)
#endif
#else
typedef struct IGridItemProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGridItemProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGridItemProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGridItemProvider *This);

    /*** IGridItemProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Row)(
        IGridItemProvider *This,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Column)(
        IGridItemProvider *This,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_RowSpan)(
        IGridItemProvider *This,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_ColumnSpan)(
        IGridItemProvider *This,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_ContainingGrid)(
        IGridItemProvider *This,
        IRawElementProviderSimple **pRetVal);

    END_INTERFACE
} IGridItemProviderVtbl;

interface IGridItemProvider {
    CONST_VTBL IGridItemProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGridItemProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGridItemProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGridItemProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IGridItemProvider methods ***/
#define IGridItemProvider_get_Row(This,pRetVal) (This)->lpVtbl->get_Row(This,pRetVal)
#define IGridItemProvider_get_Column(This,pRetVal) (This)->lpVtbl->get_Column(This,pRetVal)
#define IGridItemProvider_get_RowSpan(This,pRetVal) (This)->lpVtbl->get_RowSpan(This,pRetVal)
#define IGridItemProvider_get_ColumnSpan(This,pRetVal) (This)->lpVtbl->get_ColumnSpan(This,pRetVal)
#define IGridItemProvider_get_ContainingGrid(This,pRetVal) (This)->lpVtbl->get_ContainingGrid(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IGridItemProvider_QueryInterface(IGridItemProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IGridItemProvider_AddRef(IGridItemProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IGridItemProvider_Release(IGridItemProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IGridItemProvider methods ***/
static inline HRESULT IGridItemProvider_get_Row(IGridItemProvider* This,int *pRetVal) {
    return This->lpVtbl->get_Row(This,pRetVal);
}
static inline HRESULT IGridItemProvider_get_Column(IGridItemProvider* This,int *pRetVal) {
    return This->lpVtbl->get_Column(This,pRetVal);
}
static inline HRESULT IGridItemProvider_get_RowSpan(IGridItemProvider* This,int *pRetVal) {
    return This->lpVtbl->get_RowSpan(This,pRetVal);
}
static inline HRESULT IGridItemProvider_get_ColumnSpan(IGridItemProvider* This,int *pRetVal) {
    return This->lpVtbl->get_ColumnSpan(This,pRetVal);
}
static inline HRESULT IGridItemProvider_get_ContainingGrid(IGridItemProvider* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->get_ContainingGrid(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IGridItemProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInvokeProvider interface
 */
#ifndef __IInvokeProvider_INTERFACE_DEFINED__
#define __IInvokeProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInvokeProvider, 0x54fcb24b, 0xe18e, 0x47a2, 0xb4,0xd3, 0xec,0xcb,0xe7,0x75,0x99,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("54fcb24b-e18e-47a2-b4d3-eccbe77599a2")
IInvokeProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Invoke(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInvokeProvider, 0x54fcb24b, 0xe18e, 0x47a2, 0xb4,0xd3, 0xec,0xcb,0xe7,0x75,0x99,0xa2)
#endif
#else
typedef struct IInvokeProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInvokeProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInvokeProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInvokeProvider *This);

    /*** IInvokeProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInvokeProvider *This);

    END_INTERFACE
} IInvokeProviderVtbl;

interface IInvokeProvider {
    CONST_VTBL IInvokeProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInvokeProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInvokeProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInvokeProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IInvokeProvider methods ***/
#define IInvokeProvider_Invoke(This) (This)->lpVtbl->Invoke(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInvokeProvider_QueryInterface(IInvokeProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInvokeProvider_AddRef(IInvokeProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInvokeProvider_Release(IInvokeProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IInvokeProvider methods ***/
static inline HRESULT IInvokeProvider_Invoke(IInvokeProvider* This) {
    return This->lpVtbl->Invoke(This);
}
#endif
#endif

#endif


#endif  /* __IInvokeProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMultipleViewProvider interface
 */
#ifndef __IMultipleViewProvider_INTERFACE_DEFINED__
#define __IMultipleViewProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMultipleViewProvider, 0x6278cab1, 0xb556, 0x4a1a, 0xb4,0xe0, 0x41,0x8a,0xcc,0x52,0x32,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6278cab1-b556-4a1a-b4e0-418acc523201")
IMultipleViewProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetViewName(
        int viewId,
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentView(
        int viewId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentView(
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedViews(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMultipleViewProvider, 0x6278cab1, 0xb556, 0x4a1a, 0xb4,0xe0, 0x41,0x8a,0xcc,0x52,0x32,0x01)
#endif
#else
typedef struct IMultipleViewProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMultipleViewProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMultipleViewProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMultipleViewProvider *This);

    /*** IMultipleViewProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetViewName)(
        IMultipleViewProvider *This,
        int viewId,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *SetCurrentView)(
        IMultipleViewProvider *This,
        int viewId);

    HRESULT (STDMETHODCALLTYPE *get_CurrentView)(
        IMultipleViewProvider *This,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetSupportedViews)(
        IMultipleViewProvider *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} IMultipleViewProviderVtbl;

interface IMultipleViewProvider {
    CONST_VTBL IMultipleViewProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMultipleViewProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMultipleViewProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMultipleViewProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IMultipleViewProvider methods ***/
#define IMultipleViewProvider_GetViewName(This,viewId,pRetVal) (This)->lpVtbl->GetViewName(This,viewId,pRetVal)
#define IMultipleViewProvider_SetCurrentView(This,viewId) (This)->lpVtbl->SetCurrentView(This,viewId)
#define IMultipleViewProvider_get_CurrentView(This,pRetVal) (This)->lpVtbl->get_CurrentView(This,pRetVal)
#define IMultipleViewProvider_GetSupportedViews(This,pRetVal) (This)->lpVtbl->GetSupportedViews(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IMultipleViewProvider_QueryInterface(IMultipleViewProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMultipleViewProvider_AddRef(IMultipleViewProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMultipleViewProvider_Release(IMultipleViewProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IMultipleViewProvider methods ***/
static inline HRESULT IMultipleViewProvider_GetViewName(IMultipleViewProvider* This,int viewId,BSTR *pRetVal) {
    return This->lpVtbl->GetViewName(This,viewId,pRetVal);
}
static inline HRESULT IMultipleViewProvider_SetCurrentView(IMultipleViewProvider* This,int viewId) {
    return This->lpVtbl->SetCurrentView(This,viewId);
}
static inline HRESULT IMultipleViewProvider_get_CurrentView(IMultipleViewProvider* This,int *pRetVal) {
    return This->lpVtbl->get_CurrentView(This,pRetVal);
}
static inline HRESULT IMultipleViewProvider_GetSupportedViews(IMultipleViewProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetSupportedViews(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IMultipleViewProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRangeValueProvider interface
 */
#ifndef __IRangeValueProvider_INTERFACE_DEFINED__
#define __IRangeValueProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRangeValueProvider, 0x36dc7aef, 0x33e6, 0x4691, 0xaf,0xe1, 0x2b,0xe7,0x27,0x4b,0x3d,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("36dc7aef-33e6-4691-afe1-2be7274b3d33")
IRangeValueProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetValue(
        double val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Value(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsReadOnly(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Maximum(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Minimum(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LargeChange(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SmallChange(
        double *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRangeValueProvider, 0x36dc7aef, 0x33e6, 0x4691, 0xaf,0xe1, 0x2b,0xe7,0x27,0x4b,0x3d,0x33)
#endif
#else
typedef struct IRangeValueProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRangeValueProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRangeValueProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRangeValueProvider *This);

    /*** IRangeValueProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IRangeValueProvider *This,
        double val);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        IRangeValueProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_IsReadOnly)(
        IRangeValueProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Maximum)(
        IRangeValueProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Minimum)(
        IRangeValueProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_LargeChange)(
        IRangeValueProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_SmallChange)(
        IRangeValueProvider *This,
        double *pRetVal);

    END_INTERFACE
} IRangeValueProviderVtbl;

interface IRangeValueProvider {
    CONST_VTBL IRangeValueProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRangeValueProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRangeValueProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRangeValueProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IRangeValueProvider methods ***/
#define IRangeValueProvider_SetValue(This,val) (This)->lpVtbl->SetValue(This,val)
#define IRangeValueProvider_get_Value(This,pRetVal) (This)->lpVtbl->get_Value(This,pRetVal)
#define IRangeValueProvider_get_IsReadOnly(This,pRetVal) (This)->lpVtbl->get_IsReadOnly(This,pRetVal)
#define IRangeValueProvider_get_Maximum(This,pRetVal) (This)->lpVtbl->get_Maximum(This,pRetVal)
#define IRangeValueProvider_get_Minimum(This,pRetVal) (This)->lpVtbl->get_Minimum(This,pRetVal)
#define IRangeValueProvider_get_LargeChange(This,pRetVal) (This)->lpVtbl->get_LargeChange(This,pRetVal)
#define IRangeValueProvider_get_SmallChange(This,pRetVal) (This)->lpVtbl->get_SmallChange(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRangeValueProvider_QueryInterface(IRangeValueProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRangeValueProvider_AddRef(IRangeValueProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRangeValueProvider_Release(IRangeValueProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IRangeValueProvider methods ***/
static inline HRESULT IRangeValueProvider_SetValue(IRangeValueProvider* This,double val) {
    return This->lpVtbl->SetValue(This,val);
}
static inline HRESULT IRangeValueProvider_get_Value(IRangeValueProvider* This,double *pRetVal) {
    return This->lpVtbl->get_Value(This,pRetVal);
}
static inline HRESULT IRangeValueProvider_get_IsReadOnly(IRangeValueProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsReadOnly(This,pRetVal);
}
static inline HRESULT IRangeValueProvider_get_Maximum(IRangeValueProvider* This,double *pRetVal) {
    return This->lpVtbl->get_Maximum(This,pRetVal);
}
static inline HRESULT IRangeValueProvider_get_Minimum(IRangeValueProvider* This,double *pRetVal) {
    return This->lpVtbl->get_Minimum(This,pRetVal);
}
static inline HRESULT IRangeValueProvider_get_LargeChange(IRangeValueProvider* This,double *pRetVal) {
    return This->lpVtbl->get_LargeChange(This,pRetVal);
}
static inline HRESULT IRangeValueProvider_get_SmallChange(IRangeValueProvider* This,double *pRetVal) {
    return This->lpVtbl->get_SmallChange(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRangeValueProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IScrollItemProvider interface
 */
#ifndef __IScrollItemProvider_INTERFACE_DEFINED__
#define __IScrollItemProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IScrollItemProvider, 0x2360c714, 0x4bf1, 0x4b26, 0xba,0x65, 0x9b,0x21,0x31,0x61,0x27,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2360c714-4bf1-4b26-ba65-9b21316127eb")
IScrollItemProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ScrollIntoView(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IScrollItemProvider, 0x2360c714, 0x4bf1, 0x4b26, 0xba,0x65, 0x9b,0x21,0x31,0x61,0x27,0xeb)
#endif
#else
typedef struct IScrollItemProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IScrollItemProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IScrollItemProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IScrollItemProvider *This);

    /*** IScrollItemProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *ScrollIntoView)(
        IScrollItemProvider *This);

    END_INTERFACE
} IScrollItemProviderVtbl;

interface IScrollItemProvider {
    CONST_VTBL IScrollItemProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IScrollItemProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IScrollItemProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IScrollItemProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IScrollItemProvider methods ***/
#define IScrollItemProvider_ScrollIntoView(This) (This)->lpVtbl->ScrollIntoView(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IScrollItemProvider_QueryInterface(IScrollItemProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IScrollItemProvider_AddRef(IScrollItemProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IScrollItemProvider_Release(IScrollItemProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IScrollItemProvider methods ***/
static inline HRESULT IScrollItemProvider_ScrollIntoView(IScrollItemProvider* This) {
    return This->lpVtbl->ScrollIntoView(This);
}
#endif
#endif

#endif


#endif  /* __IScrollItemProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISelectionProvider interface
 */
#ifndef __ISelectionProvider_INTERFACE_DEFINED__
#define __ISelectionProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISelectionProvider, 0xfb8b03af, 0x3bdf, 0x48d4, 0xbd,0x36, 0x1a,0x65,0x79,0x3b,0xe1,0x68);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fb8b03af-3bdf-48d4-bd36-1a65793be168")
ISelectionProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSelection(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanSelectMultiple(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsSelectionRequired(
        WINBOOL *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISelectionProvider, 0xfb8b03af, 0x3bdf, 0x48d4, 0xbd,0x36, 0x1a,0x65,0x79,0x3b,0xe1,0x68)
#endif
#else
typedef struct ISelectionProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISelectionProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISelectionProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISelectionProvider *This);

    /*** ISelectionProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        ISelectionProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanSelectMultiple)(
        ISelectionProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_IsSelectionRequired)(
        ISelectionProvider *This,
        WINBOOL *pRetVal);

    END_INTERFACE
} ISelectionProviderVtbl;

interface ISelectionProvider {
    CONST_VTBL ISelectionProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISelectionProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISelectionProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISelectionProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ISelectionProvider methods ***/
#define ISelectionProvider_GetSelection(This,pRetVal) (This)->lpVtbl->GetSelection(This,pRetVal)
#define ISelectionProvider_get_CanSelectMultiple(This,pRetVal) (This)->lpVtbl->get_CanSelectMultiple(This,pRetVal)
#define ISelectionProvider_get_IsSelectionRequired(This,pRetVal) (This)->lpVtbl->get_IsSelectionRequired(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ISelectionProvider_QueryInterface(ISelectionProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISelectionProvider_AddRef(ISelectionProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISelectionProvider_Release(ISelectionProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ISelectionProvider methods ***/
static inline HRESULT ISelectionProvider_GetSelection(ISelectionProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetSelection(This,pRetVal);
}
static inline HRESULT ISelectionProvider_get_CanSelectMultiple(ISelectionProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanSelectMultiple(This,pRetVal);
}
static inline HRESULT ISelectionProvider_get_IsSelectionRequired(ISelectionProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsSelectionRequired(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ISelectionProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISelectionProvider2 interface
 */
#ifndef __ISelectionProvider2_INTERFACE_DEFINED__
#define __ISelectionProvider2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISelectionProvider2, 0x14f68475, 0xee1c, 0x44f6, 0xa8,0x69, 0xd2,0x39,0x38,0x1f,0x0f,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("14f68475-ee1c-44f6-a869-d239381f0fe7")
ISelectionProvider2 : public ISelectionProvider
{
    virtual HRESULT STDMETHODCALLTYPE get_FirstSelectedItem(
        IRawElementProviderSimple **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastSelectedItem(
        IRawElementProviderSimple **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentSelectedItem(
        IRawElementProviderSimple **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ItemCount(
        int *val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISelectionProvider2, 0x14f68475, 0xee1c, 0x44f6, 0xa8,0x69, 0xd2,0x39,0x38,0x1f,0x0f,0xe7)
#endif
#else
typedef struct ISelectionProvider2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISelectionProvider2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISelectionProvider2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISelectionProvider2 *This);

    /*** ISelectionProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        ISelectionProvider2 *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanSelectMultiple)(
        ISelectionProvider2 *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_IsSelectionRequired)(
        ISelectionProvider2 *This,
        WINBOOL *pRetVal);

    /*** ISelectionProvider2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FirstSelectedItem)(
        ISelectionProvider2 *This,
        IRawElementProviderSimple **val);

    HRESULT (STDMETHODCALLTYPE *get_LastSelectedItem)(
        ISelectionProvider2 *This,
        IRawElementProviderSimple **val);

    HRESULT (STDMETHODCALLTYPE *get_CurrentSelectedItem)(
        ISelectionProvider2 *This,
        IRawElementProviderSimple **val);

    HRESULT (STDMETHODCALLTYPE *get_ItemCount)(
        ISelectionProvider2 *This,
        int *val);

    END_INTERFACE
} ISelectionProvider2Vtbl;

interface ISelectionProvider2 {
    CONST_VTBL ISelectionProvider2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISelectionProvider2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISelectionProvider2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISelectionProvider2_Release(This) (This)->lpVtbl->Release(This)
/*** ISelectionProvider methods ***/
#define ISelectionProvider2_GetSelection(This,pRetVal) (This)->lpVtbl->GetSelection(This,pRetVal)
#define ISelectionProvider2_get_CanSelectMultiple(This,pRetVal) (This)->lpVtbl->get_CanSelectMultiple(This,pRetVal)
#define ISelectionProvider2_get_IsSelectionRequired(This,pRetVal) (This)->lpVtbl->get_IsSelectionRequired(This,pRetVal)
/*** ISelectionProvider2 methods ***/
#define ISelectionProvider2_get_FirstSelectedItem(This,val) (This)->lpVtbl->get_FirstSelectedItem(This,val)
#define ISelectionProvider2_get_LastSelectedItem(This,val) (This)->lpVtbl->get_LastSelectedItem(This,val)
#define ISelectionProvider2_get_CurrentSelectedItem(This,val) (This)->lpVtbl->get_CurrentSelectedItem(This,val)
#define ISelectionProvider2_get_ItemCount(This,val) (This)->lpVtbl->get_ItemCount(This,val)
#else
/*** IUnknown methods ***/
static inline HRESULT ISelectionProvider2_QueryInterface(ISelectionProvider2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISelectionProvider2_AddRef(ISelectionProvider2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISelectionProvider2_Release(ISelectionProvider2* This) {
    return This->lpVtbl->Release(This);
}
/*** ISelectionProvider methods ***/
static inline HRESULT ISelectionProvider2_GetSelection(ISelectionProvider2* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetSelection(This,pRetVal);
}
static inline HRESULT ISelectionProvider2_get_CanSelectMultiple(ISelectionProvider2* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanSelectMultiple(This,pRetVal);
}
static inline HRESULT ISelectionProvider2_get_IsSelectionRequired(ISelectionProvider2* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsSelectionRequired(This,pRetVal);
}
/*** ISelectionProvider2 methods ***/
static inline HRESULT ISelectionProvider2_get_FirstSelectedItem(ISelectionProvider2* This,IRawElementProviderSimple **val) {
    return This->lpVtbl->get_FirstSelectedItem(This,val);
}
static inline HRESULT ISelectionProvider2_get_LastSelectedItem(ISelectionProvider2* This,IRawElementProviderSimple **val) {
    return This->lpVtbl->get_LastSelectedItem(This,val);
}
static inline HRESULT ISelectionProvider2_get_CurrentSelectedItem(ISelectionProvider2* This,IRawElementProviderSimple **val) {
    return This->lpVtbl->get_CurrentSelectedItem(This,val);
}
static inline HRESULT ISelectionProvider2_get_ItemCount(ISelectionProvider2* This,int *val) {
    return This->lpVtbl->get_ItemCount(This,val);
}
#endif
#endif

#endif


#endif  /* __ISelectionProvider2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IScrollProvider interface
 */
#ifndef __IScrollProvider_INTERFACE_DEFINED__
#define __IScrollProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IScrollProvider, 0xb38b8077, 0x1fc3, 0x42a5, 0x8c,0xae, 0xd4,0x0c,0x22,0x15,0x05,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b38b8077-1fc3-42a5-8cae-d40c2215055a")
IScrollProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Scroll(
        enum ScrollAmount horizontalAmount,
        enum ScrollAmount verticalAmount) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetScrollPercent(
        double horizontalPercent,
        double verticalPercent) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HorizontalScrollPercent(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_VerticalScrollPercent(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HorizontalViewSize(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_VerticalViewSize(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HorizontallyScrollable(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_VerticallyScrollable(
        WINBOOL *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IScrollProvider, 0xb38b8077, 0x1fc3, 0x42a5, 0x8c,0xae, 0xd4,0x0c,0x22,0x15,0x05,0x5a)
#endif
#else
typedef struct IScrollProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IScrollProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IScrollProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IScrollProvider *This);

    /*** IScrollProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Scroll)(
        IScrollProvider *This,
        enum ScrollAmount horizontalAmount,
        enum ScrollAmount verticalAmount);

    HRESULT (STDMETHODCALLTYPE *SetScrollPercent)(
        IScrollProvider *This,
        double horizontalPercent,
        double verticalPercent);

    HRESULT (STDMETHODCALLTYPE *get_HorizontalScrollPercent)(
        IScrollProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_VerticalScrollPercent)(
        IScrollProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_HorizontalViewSize)(
        IScrollProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_VerticalViewSize)(
        IScrollProvider *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_HorizontallyScrollable)(
        IScrollProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_VerticallyScrollable)(
        IScrollProvider *This,
        WINBOOL *pRetVal);

    END_INTERFACE
} IScrollProviderVtbl;

interface IScrollProvider {
    CONST_VTBL IScrollProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IScrollProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IScrollProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IScrollProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IScrollProvider methods ***/
#define IScrollProvider_Scroll(This,horizontalAmount,verticalAmount) (This)->lpVtbl->Scroll(This,horizontalAmount,verticalAmount)
#define IScrollProvider_SetScrollPercent(This,horizontalPercent,verticalPercent) (This)->lpVtbl->SetScrollPercent(This,horizontalPercent,verticalPercent)
#define IScrollProvider_get_HorizontalScrollPercent(This,pRetVal) (This)->lpVtbl->get_HorizontalScrollPercent(This,pRetVal)
#define IScrollProvider_get_VerticalScrollPercent(This,pRetVal) (This)->lpVtbl->get_VerticalScrollPercent(This,pRetVal)
#define IScrollProvider_get_HorizontalViewSize(This,pRetVal) (This)->lpVtbl->get_HorizontalViewSize(This,pRetVal)
#define IScrollProvider_get_VerticalViewSize(This,pRetVal) (This)->lpVtbl->get_VerticalViewSize(This,pRetVal)
#define IScrollProvider_get_HorizontallyScrollable(This,pRetVal) (This)->lpVtbl->get_HorizontallyScrollable(This,pRetVal)
#define IScrollProvider_get_VerticallyScrollable(This,pRetVal) (This)->lpVtbl->get_VerticallyScrollable(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IScrollProvider_QueryInterface(IScrollProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IScrollProvider_AddRef(IScrollProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IScrollProvider_Release(IScrollProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IScrollProvider methods ***/
static inline HRESULT IScrollProvider_Scroll(IScrollProvider* This,enum ScrollAmount horizontalAmount,enum ScrollAmount verticalAmount) {
    return This->lpVtbl->Scroll(This,horizontalAmount,verticalAmount);
}
static inline HRESULT IScrollProvider_SetScrollPercent(IScrollProvider* This,double horizontalPercent,double verticalPercent) {
    return This->lpVtbl->SetScrollPercent(This,horizontalPercent,verticalPercent);
}
static inline HRESULT IScrollProvider_get_HorizontalScrollPercent(IScrollProvider* This,double *pRetVal) {
    return This->lpVtbl->get_HorizontalScrollPercent(This,pRetVal);
}
static inline HRESULT IScrollProvider_get_VerticalScrollPercent(IScrollProvider* This,double *pRetVal) {
    return This->lpVtbl->get_VerticalScrollPercent(This,pRetVal);
}
static inline HRESULT IScrollProvider_get_HorizontalViewSize(IScrollProvider* This,double *pRetVal) {
    return This->lpVtbl->get_HorizontalViewSize(This,pRetVal);
}
static inline HRESULT IScrollProvider_get_VerticalViewSize(IScrollProvider* This,double *pRetVal) {
    return This->lpVtbl->get_VerticalViewSize(This,pRetVal);
}
static inline HRESULT IScrollProvider_get_HorizontallyScrollable(IScrollProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_HorizontallyScrollable(This,pRetVal);
}
static inline HRESULT IScrollProvider_get_VerticallyScrollable(IScrollProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_VerticallyScrollable(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IScrollProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISelectionItemProvider interface
 */
#ifndef __ISelectionItemProvider_INTERFACE_DEFINED__
#define __ISelectionItemProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISelectionItemProvider, 0x2acad808, 0xb2d4, 0x452d, 0xa4,0x07, 0x91,0xff,0x1a,0xd1,0x67,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2acad808-b2d4-452d-a407-91ff1ad167b2")
ISelectionItemProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Select(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddToSelection(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveFromSelection(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsSelected(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SelectionContainer(
        IRawElementProviderSimple **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISelectionItemProvider, 0x2acad808, 0xb2d4, 0x452d, 0xa4,0x07, 0x91,0xff,0x1a,0xd1,0x67,0xb2)
#endif
#else
typedef struct ISelectionItemProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISelectionItemProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISelectionItemProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISelectionItemProvider *This);

    /*** ISelectionItemProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Select)(
        ISelectionItemProvider *This);

    HRESULT (STDMETHODCALLTYPE *AddToSelection)(
        ISelectionItemProvider *This);

    HRESULT (STDMETHODCALLTYPE *RemoveFromSelection)(
        ISelectionItemProvider *This);

    HRESULT (STDMETHODCALLTYPE *get_IsSelected)(
        ISelectionItemProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_SelectionContainer)(
        ISelectionItemProvider *This,
        IRawElementProviderSimple **pRetVal);

    END_INTERFACE
} ISelectionItemProviderVtbl;

interface ISelectionItemProvider {
    CONST_VTBL ISelectionItemProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISelectionItemProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISelectionItemProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISelectionItemProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ISelectionItemProvider methods ***/
#define ISelectionItemProvider_Select(This) (This)->lpVtbl->Select(This)
#define ISelectionItemProvider_AddToSelection(This) (This)->lpVtbl->AddToSelection(This)
#define ISelectionItemProvider_RemoveFromSelection(This) (This)->lpVtbl->RemoveFromSelection(This)
#define ISelectionItemProvider_get_IsSelected(This,pRetVal) (This)->lpVtbl->get_IsSelected(This,pRetVal)
#define ISelectionItemProvider_get_SelectionContainer(This,pRetVal) (This)->lpVtbl->get_SelectionContainer(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ISelectionItemProvider_QueryInterface(ISelectionItemProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISelectionItemProvider_AddRef(ISelectionItemProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISelectionItemProvider_Release(ISelectionItemProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ISelectionItemProvider methods ***/
static inline HRESULT ISelectionItemProvider_Select(ISelectionItemProvider* This) {
    return This->lpVtbl->Select(This);
}
static inline HRESULT ISelectionItemProvider_AddToSelection(ISelectionItemProvider* This) {
    return This->lpVtbl->AddToSelection(This);
}
static inline HRESULT ISelectionItemProvider_RemoveFromSelection(ISelectionItemProvider* This) {
    return This->lpVtbl->RemoveFromSelection(This);
}
static inline HRESULT ISelectionItemProvider_get_IsSelected(ISelectionItemProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsSelected(This,pRetVal);
}
static inline HRESULT ISelectionItemProvider_get_SelectionContainer(ISelectionItemProvider* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->get_SelectionContainer(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ISelectionItemProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISynchronizedInputProvider interface
 */
#ifndef __ISynchronizedInputProvider_INTERFACE_DEFINED__
#define __ISynchronizedInputProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizedInputProvider, 0x29db1a06, 0x02ce, 0x4cf7, 0x9b,0x42, 0x56,0x5d,0x4f,0xab,0x20,0xee);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("29db1a06-02ce-4cf7-9b42-565d4fab20ee")
ISynchronizedInputProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartListening(
        enum SynchronizedInputType inputType) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizedInputProvider, 0x29db1a06, 0x02ce, 0x4cf7, 0x9b,0x42, 0x56,0x5d,0x4f,0xab,0x20,0xee)
#endif
#else
typedef struct ISynchronizedInputProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizedInputProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizedInputProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizedInputProvider *This);

    /*** ISynchronizedInputProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *StartListening)(
        ISynchronizedInputProvider *This,
        enum SynchronizedInputType inputType);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        ISynchronizedInputProvider *This);

    END_INTERFACE
} ISynchronizedInputProviderVtbl;

interface ISynchronizedInputProvider {
    CONST_VTBL ISynchronizedInputProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizedInputProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizedInputProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizedInputProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronizedInputProvider methods ***/
#define ISynchronizedInputProvider_StartListening(This,inputType) (This)->lpVtbl->StartListening(This,inputType)
#define ISynchronizedInputProvider_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISynchronizedInputProvider_QueryInterface(ISynchronizedInputProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISynchronizedInputProvider_AddRef(ISynchronizedInputProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISynchronizedInputProvider_Release(ISynchronizedInputProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronizedInputProvider methods ***/
static inline HRESULT ISynchronizedInputProvider_StartListening(ISynchronizedInputProvider* This,enum SynchronizedInputType inputType) {
    return This->lpVtbl->StartListening(This,inputType);
}
static inline HRESULT ISynchronizedInputProvider_Cancel(ISynchronizedInputProvider* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif


#endif  /* __ISynchronizedInputProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITableProvider interface
 */
#ifndef __ITableProvider_INTERFACE_DEFINED__
#define __ITableProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITableProvider, 0x9c860395, 0x97b3, 0x490a, 0xb5,0x2a, 0x85,0x8c,0xc2,0x2a,0xf1,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c860395-97b3-490a-b52a-858cc22af166")
ITableProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRowHeaders(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColumnHeaders(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RowOrColumnMajor(
        enum RowOrColumnMajor *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITableProvider, 0x9c860395, 0x97b3, 0x490a, 0xb5,0x2a, 0x85,0x8c,0xc2,0x2a,0xf1,0x66)
#endif
#else
typedef struct ITableProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITableProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITableProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITableProvider *This);

    /*** ITableProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRowHeaders)(
        ITableProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetColumnHeaders)(
        ITableProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_RowOrColumnMajor)(
        ITableProvider *This,
        enum RowOrColumnMajor *pRetVal);

    END_INTERFACE
} ITableProviderVtbl;

interface ITableProvider {
    CONST_VTBL ITableProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITableProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITableProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITableProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITableProvider methods ***/
#define ITableProvider_GetRowHeaders(This,pRetVal) (This)->lpVtbl->GetRowHeaders(This,pRetVal)
#define ITableProvider_GetColumnHeaders(This,pRetVal) (This)->lpVtbl->GetColumnHeaders(This,pRetVal)
#define ITableProvider_get_RowOrColumnMajor(This,pRetVal) (This)->lpVtbl->get_RowOrColumnMajor(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITableProvider_QueryInterface(ITableProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITableProvider_AddRef(ITableProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITableProvider_Release(ITableProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITableProvider methods ***/
static inline HRESULT ITableProvider_GetRowHeaders(ITableProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetRowHeaders(This,pRetVal);
}
static inline HRESULT ITableProvider_GetColumnHeaders(ITableProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetColumnHeaders(This,pRetVal);
}
static inline HRESULT ITableProvider_get_RowOrColumnMajor(ITableProvider* This,enum RowOrColumnMajor *pRetVal) {
    return This->lpVtbl->get_RowOrColumnMajor(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITableProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITableItemProvider interface
 */
#ifndef __ITableItemProvider_INTERFACE_DEFINED__
#define __ITableItemProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITableItemProvider, 0xb9734fa6, 0x771f, 0x4d78, 0x9c,0x90, 0x25,0x17,0x99,0x93,0x49,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b9734fa6-771f-4d78-9c90-2517999349cd")
ITableItemProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRowHeaderItems(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColumnHeaderItems(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITableItemProvider, 0xb9734fa6, 0x771f, 0x4d78, 0x9c,0x90, 0x25,0x17,0x99,0x93,0x49,0xcd)
#endif
#else
typedef struct ITableItemProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITableItemProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITableItemProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITableItemProvider *This);

    /*** ITableItemProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRowHeaderItems)(
        ITableItemProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetColumnHeaderItems)(
        ITableItemProvider *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} ITableItemProviderVtbl;

interface ITableItemProvider {
    CONST_VTBL ITableItemProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITableItemProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITableItemProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITableItemProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITableItemProvider methods ***/
#define ITableItemProvider_GetRowHeaderItems(This,pRetVal) (This)->lpVtbl->GetRowHeaderItems(This,pRetVal)
#define ITableItemProvider_GetColumnHeaderItems(This,pRetVal) (This)->lpVtbl->GetColumnHeaderItems(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITableItemProvider_QueryInterface(ITableItemProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITableItemProvider_AddRef(ITableItemProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITableItemProvider_Release(ITableItemProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITableItemProvider methods ***/
static inline HRESULT ITableItemProvider_GetRowHeaderItems(ITableItemProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetRowHeaderItems(This,pRetVal);
}
static inline HRESULT ITableItemProvider_GetColumnHeaderItems(ITableItemProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetColumnHeaderItems(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITableItemProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IToggleProvider interface
 */
#ifndef __IToggleProvider_INTERFACE_DEFINED__
#define __IToggleProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IToggleProvider, 0x56d00bd0, 0xc4f4, 0x433c, 0xa8,0x36, 0x1a,0x52,0xa5,0x7e,0x08,0x92);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56d00bd0-c4f4-433c-a836-1a52a57e0892")
IToggleProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Toggle(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ToggleState(
        enum ToggleState *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IToggleProvider, 0x56d00bd0, 0xc4f4, 0x433c, 0xa8,0x36, 0x1a,0x52,0xa5,0x7e,0x08,0x92)
#endif
#else
typedef struct IToggleProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IToggleProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IToggleProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IToggleProvider *This);

    /*** IToggleProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Toggle)(
        IToggleProvider *This);

    HRESULT (STDMETHODCALLTYPE *get_ToggleState)(
        IToggleProvider *This,
        enum ToggleState *pRetVal);

    END_INTERFACE
} IToggleProviderVtbl;

interface IToggleProvider {
    CONST_VTBL IToggleProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IToggleProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IToggleProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IToggleProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IToggleProvider methods ***/
#define IToggleProvider_Toggle(This) (This)->lpVtbl->Toggle(This)
#define IToggleProvider_get_ToggleState(This,pRetVal) (This)->lpVtbl->get_ToggleState(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IToggleProvider_QueryInterface(IToggleProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IToggleProvider_AddRef(IToggleProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IToggleProvider_Release(IToggleProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IToggleProvider methods ***/
static inline HRESULT IToggleProvider_Toggle(IToggleProvider* This) {
    return This->lpVtbl->Toggle(This);
}
static inline HRESULT IToggleProvider_get_ToggleState(IToggleProvider* This,enum ToggleState *pRetVal) {
    return This->lpVtbl->get_ToggleState(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IToggleProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITransformProvider interface
 */
#ifndef __ITransformProvider_INTERFACE_DEFINED__
#define __ITransformProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITransformProvider, 0x6829ddc4, 0x4f91, 0x4ffa, 0xb8,0x6f, 0xbd,0x3e,0x29,0x87,0xcb,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6829ddc4-4f91-4ffa-b86f-bd3e2987cb4c")
ITransformProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Move(
        double x,
        double y) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resize(
        double width,
        double height) = 0;

    virtual HRESULT STDMETHODCALLTYPE Rotate(
        double degrees) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanMove(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanResize(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanRotate(
        WINBOOL *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITransformProvider, 0x6829ddc4, 0x4f91, 0x4ffa, 0xb8,0x6f, 0xbd,0x3e,0x29,0x87,0xcb,0x4c)
#endif
#else
typedef struct ITransformProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITransformProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITransformProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITransformProvider *This);

    /*** ITransformProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Move)(
        ITransformProvider *This,
        double x,
        double y);

    HRESULT (STDMETHODCALLTYPE *Resize)(
        ITransformProvider *This,
        double width,
        double height);

    HRESULT (STDMETHODCALLTYPE *Rotate)(
        ITransformProvider *This,
        double degrees);

    HRESULT (STDMETHODCALLTYPE *get_CanMove)(
        ITransformProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanResize)(
        ITransformProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanRotate)(
        ITransformProvider *This,
        WINBOOL *pRetVal);

    END_INTERFACE
} ITransformProviderVtbl;

interface ITransformProvider {
    CONST_VTBL ITransformProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITransformProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITransformProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITransformProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITransformProvider methods ***/
#define ITransformProvider_Move(This,x,y) (This)->lpVtbl->Move(This,x,y)
#define ITransformProvider_Resize(This,width,height) (This)->lpVtbl->Resize(This,width,height)
#define ITransformProvider_Rotate(This,degrees) (This)->lpVtbl->Rotate(This,degrees)
#define ITransformProvider_get_CanMove(This,pRetVal) (This)->lpVtbl->get_CanMove(This,pRetVal)
#define ITransformProvider_get_CanResize(This,pRetVal) (This)->lpVtbl->get_CanResize(This,pRetVal)
#define ITransformProvider_get_CanRotate(This,pRetVal) (This)->lpVtbl->get_CanRotate(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITransformProvider_QueryInterface(ITransformProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITransformProvider_AddRef(ITransformProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITransformProvider_Release(ITransformProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITransformProvider methods ***/
static inline HRESULT ITransformProvider_Move(ITransformProvider* This,double x,double y) {
    return This->lpVtbl->Move(This,x,y);
}
static inline HRESULT ITransformProvider_Resize(ITransformProvider* This,double width,double height) {
    return This->lpVtbl->Resize(This,width,height);
}
static inline HRESULT ITransformProvider_Rotate(ITransformProvider* This,double degrees) {
    return This->lpVtbl->Rotate(This,degrees);
}
static inline HRESULT ITransformProvider_get_CanMove(ITransformProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanMove(This,pRetVal);
}
static inline HRESULT ITransformProvider_get_CanResize(ITransformProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanResize(This,pRetVal);
}
static inline HRESULT ITransformProvider_get_CanRotate(ITransformProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanRotate(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITransformProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IValueProvider interface
 */
#ifndef __IValueProvider_INTERFACE_DEFINED__
#define __IValueProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IValueProvider, 0xc7935180, 0x6fb3, 0x4201, 0xb1,0x74, 0x7d,0xf7,0x3a,0xdb,0xf6,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c7935180-6fb3-4201-b174-7df73adbf64a")
IValueProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetValue(
        LPCWSTR val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Value(
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsReadOnly(
        WINBOOL *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IValueProvider, 0xc7935180, 0x6fb3, 0x4201, 0xb1,0x74, 0x7d,0xf7,0x3a,0xdb,0xf6,0x4a)
#endif
#else
typedef struct IValueProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IValueProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IValueProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IValueProvider *This);

    /*** IValueProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IValueProvider *This,
        LPCWSTR val);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        IValueProvider *This,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_IsReadOnly)(
        IValueProvider *This,
        WINBOOL *pRetVal);

    END_INTERFACE
} IValueProviderVtbl;

interface IValueProvider {
    CONST_VTBL IValueProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IValueProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IValueProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IValueProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IValueProvider methods ***/
#define IValueProvider_SetValue(This,val) (This)->lpVtbl->SetValue(This,val)
#define IValueProvider_get_Value(This,pRetVal) (This)->lpVtbl->get_Value(This,pRetVal)
#define IValueProvider_get_IsReadOnly(This,pRetVal) (This)->lpVtbl->get_IsReadOnly(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IValueProvider_QueryInterface(IValueProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IValueProvider_AddRef(IValueProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IValueProvider_Release(IValueProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IValueProvider methods ***/
static inline HRESULT IValueProvider_SetValue(IValueProvider* This,LPCWSTR val) {
    return This->lpVtbl->SetValue(This,val);
}
static inline HRESULT IValueProvider_get_Value(IValueProvider* This,BSTR *pRetVal) {
    return This->lpVtbl->get_Value(This,pRetVal);
}
static inline HRESULT IValueProvider_get_IsReadOnly(IValueProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsReadOnly(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IValueProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWindowProvider interface
 */
#ifndef __IWindowProvider_INTERFACE_DEFINED__
#define __IWindowProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWindowProvider, 0x987df77b, 0xdb06, 0x4d77, 0x8f,0x8a, 0x86,0xa9,0xc3,0xbb,0x90,0xb9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("987df77b-db06-4d77-8f8a-86a9c3bb90b9")
IWindowProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetVisualState(
        enum WindowVisualState state) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitForInputIdle(
        int milliseconds,
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanMaximize(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanMinimize(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsModal(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WindowVisualState(
        enum WindowVisualState *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WindowInteractionState(
        enum WindowInteractionState *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsTopmost(
        WINBOOL *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWindowProvider, 0x987df77b, 0xdb06, 0x4d77, 0x8f,0x8a, 0x86,0xa9,0xc3,0xbb,0x90,0xb9)
#endif
#else
typedef struct IWindowProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWindowProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWindowProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWindowProvider *This);

    /*** IWindowProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetVisualState)(
        IWindowProvider *This,
        enum WindowVisualState state);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IWindowProvider *This);

    HRESULT (STDMETHODCALLTYPE *WaitForInputIdle)(
        IWindowProvider *This,
        int milliseconds,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanMaximize)(
        IWindowProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanMinimize)(
        IWindowProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_IsModal)(
        IWindowProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_WindowVisualState)(
        IWindowProvider *This,
        enum WindowVisualState *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_WindowInteractionState)(
        IWindowProvider *This,
        enum WindowInteractionState *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_IsTopmost)(
        IWindowProvider *This,
        WINBOOL *pRetVal);

    END_INTERFACE
} IWindowProviderVtbl;

interface IWindowProvider {
    CONST_VTBL IWindowProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWindowProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWindowProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWindowProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWindowProvider methods ***/
#define IWindowProvider_SetVisualState(This,state) (This)->lpVtbl->SetVisualState(This,state)
#define IWindowProvider_Close(This) (This)->lpVtbl->Close(This)
#define IWindowProvider_WaitForInputIdle(This,milliseconds,pRetVal) (This)->lpVtbl->WaitForInputIdle(This,milliseconds,pRetVal)
#define IWindowProvider_get_CanMaximize(This,pRetVal) (This)->lpVtbl->get_CanMaximize(This,pRetVal)
#define IWindowProvider_get_CanMinimize(This,pRetVal) (This)->lpVtbl->get_CanMinimize(This,pRetVal)
#define IWindowProvider_get_IsModal(This,pRetVal) (This)->lpVtbl->get_IsModal(This,pRetVal)
#define IWindowProvider_get_WindowVisualState(This,pRetVal) (This)->lpVtbl->get_WindowVisualState(This,pRetVal)
#define IWindowProvider_get_WindowInteractionState(This,pRetVal) (This)->lpVtbl->get_WindowInteractionState(This,pRetVal)
#define IWindowProvider_get_IsTopmost(This,pRetVal) (This)->lpVtbl->get_IsTopmost(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IWindowProvider_QueryInterface(IWindowProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWindowProvider_AddRef(IWindowProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWindowProvider_Release(IWindowProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWindowProvider methods ***/
static inline HRESULT IWindowProvider_SetVisualState(IWindowProvider* This,enum WindowVisualState state) {
    return This->lpVtbl->SetVisualState(This,state);
}
static inline HRESULT IWindowProvider_Close(IWindowProvider* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IWindowProvider_WaitForInputIdle(IWindowProvider* This,int milliseconds,WINBOOL *pRetVal) {
    return This->lpVtbl->WaitForInputIdle(This,milliseconds,pRetVal);
}
static inline HRESULT IWindowProvider_get_CanMaximize(IWindowProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanMaximize(This,pRetVal);
}
static inline HRESULT IWindowProvider_get_CanMinimize(IWindowProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanMinimize(This,pRetVal);
}
static inline HRESULT IWindowProvider_get_IsModal(IWindowProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsModal(This,pRetVal);
}
static inline HRESULT IWindowProvider_get_WindowVisualState(IWindowProvider* This,enum WindowVisualState *pRetVal) {
    return This->lpVtbl->get_WindowVisualState(This,pRetVal);
}
static inline HRESULT IWindowProvider_get_WindowInteractionState(IWindowProvider* This,enum WindowInteractionState *pRetVal) {
    return This->lpVtbl->get_WindowInteractionState(This,pRetVal);
}
static inline HRESULT IWindowProvider_get_IsTopmost(IWindowProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsTopmost(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IWindowProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IItemContainerProvider interface
 */
#ifndef __IItemContainerProvider_INTERFACE_DEFINED__
#define __IItemContainerProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IItemContainerProvider, 0xe747770b, 0x39ce, 0x4382, 0xab,0x30, 0xd8,0xfb,0x3f,0x33,0x6f,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e747770b-39ce-4382-ab30-d8fb3f336f24")
IItemContainerProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FindItemByProperty(
        IRawElementProviderSimple *pStartAfter,
        PROPERTYID propertyId,
        VARIANT value,
        IRawElementProviderSimple **pFound) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IItemContainerProvider, 0xe747770b, 0x39ce, 0x4382, 0xab,0x30, 0xd8,0xfb,0x3f,0x33,0x6f,0x24)
#endif
#else
typedef struct IItemContainerProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IItemContainerProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IItemContainerProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IItemContainerProvider *This);

    /*** IItemContainerProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *FindItemByProperty)(
        IItemContainerProvider *This,
        IRawElementProviderSimple *pStartAfter,
        PROPERTYID propertyId,
        VARIANT value,
        IRawElementProviderSimple **pFound);

    END_INTERFACE
} IItemContainerProviderVtbl;

interface IItemContainerProvider {
    CONST_VTBL IItemContainerProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IItemContainerProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IItemContainerProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IItemContainerProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IItemContainerProvider methods ***/
#define IItemContainerProvider_FindItemByProperty(This,pStartAfter,propertyId,value,pFound) (This)->lpVtbl->FindItemByProperty(This,pStartAfter,propertyId,value,pFound)
#else
/*** IUnknown methods ***/
static inline HRESULT IItemContainerProvider_QueryInterface(IItemContainerProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IItemContainerProvider_AddRef(IItemContainerProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IItemContainerProvider_Release(IItemContainerProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IItemContainerProvider methods ***/
static inline HRESULT IItemContainerProvider_FindItemByProperty(IItemContainerProvider* This,IRawElementProviderSimple *pStartAfter,PROPERTYID propertyId,VARIANT value,IRawElementProviderSimple **pFound) {
    return This->lpVtbl->FindItemByProperty(This,pStartAfter,propertyId,value,pFound);
}
#endif
#endif

#endif


#endif  /* __IItemContainerProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVirtualizedItemProvider interface
 */
#ifndef __IVirtualizedItemProvider_INTERFACE_DEFINED__
#define __IVirtualizedItemProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVirtualizedItemProvider, 0xcb98b665, 0x2d35, 0x4fac, 0xad,0x35, 0xf3,0xc6,0x0d,0x0c,0x0b,0x8b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cb98b665-2d35-4fac-ad35-f3c60d0c0b8b")
IVirtualizedItemProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Realize(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVirtualizedItemProvider, 0xcb98b665, 0x2d35, 0x4fac, 0xad,0x35, 0xf3,0xc6,0x0d,0x0c,0x0b,0x8b)
#endif
#else
typedef struct IVirtualizedItemProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVirtualizedItemProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVirtualizedItemProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVirtualizedItemProvider *This);

    /*** IVirtualizedItemProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Realize)(
        IVirtualizedItemProvider *This);

    END_INTERFACE
} IVirtualizedItemProviderVtbl;

interface IVirtualizedItemProvider {
    CONST_VTBL IVirtualizedItemProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVirtualizedItemProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVirtualizedItemProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVirtualizedItemProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IVirtualizedItemProvider methods ***/
#define IVirtualizedItemProvider_Realize(This) (This)->lpVtbl->Realize(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IVirtualizedItemProvider_QueryInterface(IVirtualizedItemProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVirtualizedItemProvider_AddRef(IVirtualizedItemProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVirtualizedItemProvider_Release(IVirtualizedItemProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IVirtualizedItemProvider methods ***/
static inline HRESULT IVirtualizedItemProvider_Realize(IVirtualizedItemProvider* This) {
    return This->lpVtbl->Realize(This);
}
#endif
#endif

#endif


#endif  /* __IVirtualizedItemProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IObjectModelProvider interface
 */
#ifndef __IObjectModelProvider_INTERFACE_DEFINED__
#define __IObjectModelProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IObjectModelProvider, 0x3ad86ebd, 0xf5ef, 0x483d, 0xbb,0x18, 0xb1,0x04,0x2a,0x47,0x5d,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3ad86ebd-f5ef-483d-bb18-b1042a475d64")
IObjectModelProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetUnderlyingObjectModel(
        IUnknown **ppUnknown) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IObjectModelProvider, 0x3ad86ebd, 0xf5ef, 0x483d, 0xbb,0x18, 0xb1,0x04,0x2a,0x47,0x5d,0x64)
#endif
#else
typedef struct IObjectModelProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IObjectModelProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IObjectModelProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IObjectModelProvider *This);

    /*** IObjectModelProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUnderlyingObjectModel)(
        IObjectModelProvider *This,
        IUnknown **ppUnknown);

    END_INTERFACE
} IObjectModelProviderVtbl;

interface IObjectModelProvider {
    CONST_VTBL IObjectModelProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IObjectModelProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IObjectModelProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IObjectModelProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IObjectModelProvider methods ***/
#define IObjectModelProvider_GetUnderlyingObjectModel(This,ppUnknown) (This)->lpVtbl->GetUnderlyingObjectModel(This,ppUnknown)
#else
/*** IUnknown methods ***/
static inline HRESULT IObjectModelProvider_QueryInterface(IObjectModelProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IObjectModelProvider_AddRef(IObjectModelProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IObjectModelProvider_Release(IObjectModelProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IObjectModelProvider methods ***/
static inline HRESULT IObjectModelProvider_GetUnderlyingObjectModel(IObjectModelProvider* This,IUnknown **ppUnknown) {
    return This->lpVtbl->GetUnderlyingObjectModel(This,ppUnknown);
}
#endif
#endif

#endif


#endif  /* __IObjectModelProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAnnotationProvider interface
 */
#ifndef __IAnnotationProvider_INTERFACE_DEFINED__
#define __IAnnotationProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAnnotationProvider, 0xf95c7e80, 0xbd63, 0x4601, 0x97,0x82, 0x44,0x5e,0xbf,0xf0,0x11,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f95c7e80-bd63-4601-9782-445ebff011fc")
IAnnotationProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_AnnotationTypeId(
        int *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AnnotationTypeName(
        BSTR *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Author(
        BSTR *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DateTime(
        BSTR *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Target(
        IRawElementProviderSimple **retVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAnnotationProvider, 0xf95c7e80, 0xbd63, 0x4601, 0x97,0x82, 0x44,0x5e,0xbf,0xf0,0x11,0xfc)
#endif
#else
typedef struct IAnnotationProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAnnotationProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAnnotationProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAnnotationProvider *This);

    /*** IAnnotationProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AnnotationTypeId)(
        IAnnotationProvider *This,
        int *retVal);

    HRESULT (STDMETHODCALLTYPE *get_AnnotationTypeName)(
        IAnnotationProvider *This,
        BSTR *retVal);

    HRESULT (STDMETHODCALLTYPE *get_Author)(
        IAnnotationProvider *This,
        BSTR *retVal);

    HRESULT (STDMETHODCALLTYPE *get_DateTime)(
        IAnnotationProvider *This,
        BSTR *retVal);

    HRESULT (STDMETHODCALLTYPE *get_Target)(
        IAnnotationProvider *This,
        IRawElementProviderSimple **retVal);

    END_INTERFACE
} IAnnotationProviderVtbl;

interface IAnnotationProvider {
    CONST_VTBL IAnnotationProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAnnotationProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAnnotationProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAnnotationProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IAnnotationProvider methods ***/
#define IAnnotationProvider_get_AnnotationTypeId(This,retVal) (This)->lpVtbl->get_AnnotationTypeId(This,retVal)
#define IAnnotationProvider_get_AnnotationTypeName(This,retVal) (This)->lpVtbl->get_AnnotationTypeName(This,retVal)
#define IAnnotationProvider_get_Author(This,retVal) (This)->lpVtbl->get_Author(This,retVal)
#define IAnnotationProvider_get_DateTime(This,retVal) (This)->lpVtbl->get_DateTime(This,retVal)
#define IAnnotationProvider_get_Target(This,retVal) (This)->lpVtbl->get_Target(This,retVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IAnnotationProvider_QueryInterface(IAnnotationProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAnnotationProvider_AddRef(IAnnotationProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAnnotationProvider_Release(IAnnotationProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IAnnotationProvider methods ***/
static inline HRESULT IAnnotationProvider_get_AnnotationTypeId(IAnnotationProvider* This,int *retVal) {
    return This->lpVtbl->get_AnnotationTypeId(This,retVal);
}
static inline HRESULT IAnnotationProvider_get_AnnotationTypeName(IAnnotationProvider* This,BSTR *retVal) {
    return This->lpVtbl->get_AnnotationTypeName(This,retVal);
}
static inline HRESULT IAnnotationProvider_get_Author(IAnnotationProvider* This,BSTR *retVal) {
    return This->lpVtbl->get_Author(This,retVal);
}
static inline HRESULT IAnnotationProvider_get_DateTime(IAnnotationProvider* This,BSTR *retVal) {
    return This->lpVtbl->get_DateTime(This,retVal);
}
static inline HRESULT IAnnotationProvider_get_Target(IAnnotationProvider* This,IRawElementProviderSimple **retVal) {
    return This->lpVtbl->get_Target(This,retVal);
}
#endif
#endif

#endif


#endif  /* __IAnnotationProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IStylesProvider interface
 */
#ifndef __IStylesProvider_INTERFACE_DEFINED__
#define __IStylesProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IStylesProvider, 0x19b6b649, 0xf5d7, 0x4a6d, 0xbd,0xcb, 0x12,0x92,0x52,0xbe,0x58,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("19b6b649-f5d7-4a6d-bdcb-129252be588a")
IStylesProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_StyleId(
        int *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StyleName(
        BSTR *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FillColor(
        int *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FillPatternStyle(
        BSTR *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Shape(
        BSTR *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FillPatternColor(
        int *retVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExtendedProperties(
        BSTR *retVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IStylesProvider, 0x19b6b649, 0xf5d7, 0x4a6d, 0xbd,0xcb, 0x12,0x92,0x52,0xbe,0x58,0x8a)
#endif
#else
typedef struct IStylesProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IStylesProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IStylesProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IStylesProvider *This);

    /*** IStylesProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_StyleId)(
        IStylesProvider *This,
        int *retVal);

    HRESULT (STDMETHODCALLTYPE *get_StyleName)(
        IStylesProvider *This,
        BSTR *retVal);

    HRESULT (STDMETHODCALLTYPE *get_FillColor)(
        IStylesProvider *This,
        int *retVal);

    HRESULT (STDMETHODCALLTYPE *get_FillPatternStyle)(
        IStylesProvider *This,
        BSTR *retVal);

    HRESULT (STDMETHODCALLTYPE *get_Shape)(
        IStylesProvider *This,
        BSTR *retVal);

    HRESULT (STDMETHODCALLTYPE *get_FillPatternColor)(
        IStylesProvider *This,
        int *retVal);

    HRESULT (STDMETHODCALLTYPE *get_ExtendedProperties)(
        IStylesProvider *This,
        BSTR *retVal);

    END_INTERFACE
} IStylesProviderVtbl;

interface IStylesProvider {
    CONST_VTBL IStylesProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IStylesProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStylesProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStylesProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IStylesProvider methods ***/
#define IStylesProvider_get_StyleId(This,retVal) (This)->lpVtbl->get_StyleId(This,retVal)
#define IStylesProvider_get_StyleName(This,retVal) (This)->lpVtbl->get_StyleName(This,retVal)
#define IStylesProvider_get_FillColor(This,retVal) (This)->lpVtbl->get_FillColor(This,retVal)
#define IStylesProvider_get_FillPatternStyle(This,retVal) (This)->lpVtbl->get_FillPatternStyle(This,retVal)
#define IStylesProvider_get_Shape(This,retVal) (This)->lpVtbl->get_Shape(This,retVal)
#define IStylesProvider_get_FillPatternColor(This,retVal) (This)->lpVtbl->get_FillPatternColor(This,retVal)
#define IStylesProvider_get_ExtendedProperties(This,retVal) (This)->lpVtbl->get_ExtendedProperties(This,retVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IStylesProvider_QueryInterface(IStylesProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IStylesProvider_AddRef(IStylesProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IStylesProvider_Release(IStylesProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IStylesProvider methods ***/
static inline HRESULT IStylesProvider_get_StyleId(IStylesProvider* This,int *retVal) {
    return This->lpVtbl->get_StyleId(This,retVal);
}
static inline HRESULT IStylesProvider_get_StyleName(IStylesProvider* This,BSTR *retVal) {
    return This->lpVtbl->get_StyleName(This,retVal);
}
static inline HRESULT IStylesProvider_get_FillColor(IStylesProvider* This,int *retVal) {
    return This->lpVtbl->get_FillColor(This,retVal);
}
static inline HRESULT IStylesProvider_get_FillPatternStyle(IStylesProvider* This,BSTR *retVal) {
    return This->lpVtbl->get_FillPatternStyle(This,retVal);
}
static inline HRESULT IStylesProvider_get_Shape(IStylesProvider* This,BSTR *retVal) {
    return This->lpVtbl->get_Shape(This,retVal);
}
static inline HRESULT IStylesProvider_get_FillPatternColor(IStylesProvider* This,int *retVal) {
    return This->lpVtbl->get_FillPatternColor(This,retVal);
}
static inline HRESULT IStylesProvider_get_ExtendedProperties(IStylesProvider* This,BSTR *retVal) {
    return This->lpVtbl->get_ExtendedProperties(This,retVal);
}
#endif
#endif

#endif


#endif  /* __IStylesProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISpreadsheetProvider interface
 */
#ifndef __ISpreadsheetProvider_INTERFACE_DEFINED__
#define __ISpreadsheetProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISpreadsheetProvider, 0x6f6b5d35, 0x5525, 0x4f80, 0xb7,0x58, 0x85,0x47,0x38,0x32,0xff,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6f6b5d35-5525-4f80-b758-85473832ffc7")
ISpreadsheetProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetItemByName(
        LPCWSTR name,
        IRawElementProviderSimple **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpreadsheetProvider, 0x6f6b5d35, 0x5525, 0x4f80, 0xb7,0x58, 0x85,0x47,0x38,0x32,0xff,0xc7)
#endif
#else
typedef struct ISpreadsheetProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpreadsheetProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpreadsheetProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpreadsheetProvider *This);

    /*** ISpreadsheetProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItemByName)(
        ISpreadsheetProvider *This,
        LPCWSTR name,
        IRawElementProviderSimple **pRetVal);

    END_INTERFACE
} ISpreadsheetProviderVtbl;

interface ISpreadsheetProvider {
    CONST_VTBL ISpreadsheetProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpreadsheetProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpreadsheetProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpreadsheetProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ISpreadsheetProvider methods ***/
#define ISpreadsheetProvider_GetItemByName(This,name,pRetVal) (This)->lpVtbl->GetItemByName(This,name,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpreadsheetProvider_QueryInterface(ISpreadsheetProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpreadsheetProvider_AddRef(ISpreadsheetProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpreadsheetProvider_Release(ISpreadsheetProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpreadsheetProvider methods ***/
static inline HRESULT ISpreadsheetProvider_GetItemByName(ISpreadsheetProvider* This,LPCWSTR name,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->GetItemByName(This,name,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ISpreadsheetProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISpreadsheetItemProvider interface
 */
#ifndef __ISpreadsheetItemProvider_INTERFACE_DEFINED__
#define __ISpreadsheetItemProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISpreadsheetItemProvider, 0xeaed4660, 0x7b3d, 0x4879, 0xa2,0xe6, 0x36,0x5c,0xe6,0x03,0xf3,0xd0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eaed4660-7b3d-4879-a2e6-365ce603f3d0")
ISpreadsheetItemProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Formula(
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAnnotationObjects(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAnnotationTypes(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpreadsheetItemProvider, 0xeaed4660, 0x7b3d, 0x4879, 0xa2,0xe6, 0x36,0x5c,0xe6,0x03,0xf3,0xd0)
#endif
#else
typedef struct ISpreadsheetItemProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpreadsheetItemProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpreadsheetItemProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpreadsheetItemProvider *This);

    /*** ISpreadsheetItemProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Formula)(
        ISpreadsheetItemProvider *This,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetAnnotationObjects)(
        ISpreadsheetItemProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetAnnotationTypes)(
        ISpreadsheetItemProvider *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} ISpreadsheetItemProviderVtbl;

interface ISpreadsheetItemProvider {
    CONST_VTBL ISpreadsheetItemProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpreadsheetItemProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpreadsheetItemProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpreadsheetItemProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ISpreadsheetItemProvider methods ***/
#define ISpreadsheetItemProvider_get_Formula(This,pRetVal) (This)->lpVtbl->get_Formula(This,pRetVal)
#define ISpreadsheetItemProvider_GetAnnotationObjects(This,pRetVal) (This)->lpVtbl->GetAnnotationObjects(This,pRetVal)
#define ISpreadsheetItemProvider_GetAnnotationTypes(This,pRetVal) (This)->lpVtbl->GetAnnotationTypes(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpreadsheetItemProvider_QueryInterface(ISpreadsheetItemProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpreadsheetItemProvider_AddRef(ISpreadsheetItemProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpreadsheetItemProvider_Release(ISpreadsheetItemProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpreadsheetItemProvider methods ***/
static inline HRESULT ISpreadsheetItemProvider_get_Formula(ISpreadsheetItemProvider* This,BSTR *pRetVal) {
    return This->lpVtbl->get_Formula(This,pRetVal);
}
static inline HRESULT ISpreadsheetItemProvider_GetAnnotationObjects(ISpreadsheetItemProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetAnnotationObjects(This,pRetVal);
}
static inline HRESULT ISpreadsheetItemProvider_GetAnnotationTypes(ISpreadsheetItemProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetAnnotationTypes(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ISpreadsheetItemProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITransformProvider2 interface
 */
#ifndef __ITransformProvider2_INTERFACE_DEFINED__
#define __ITransformProvider2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITransformProvider2, 0x4758742f, 0x7ac2, 0x460c, 0xbc,0x48, 0x09,0xfc,0x09,0x30,0x8a,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4758742f-7ac2-460c-bc48-09fc09308a93")
ITransformProvider2 : public ITransformProvider
{
    virtual HRESULT STDMETHODCALLTYPE Zoom(
        double zoom) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanZoom(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ZoomLevel(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ZoomMinimum(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ZoomMaximum(
        double *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE ZoomByUnit(
        enum ZoomUnit zoomUnit) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITransformProvider2, 0x4758742f, 0x7ac2, 0x460c, 0xbc,0x48, 0x09,0xfc,0x09,0x30,0x8a,0x93)
#endif
#else
typedef struct ITransformProvider2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITransformProvider2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITransformProvider2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITransformProvider2 *This);

    /*** ITransformProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Move)(
        ITransformProvider2 *This,
        double x,
        double y);

    HRESULT (STDMETHODCALLTYPE *Resize)(
        ITransformProvider2 *This,
        double width,
        double height);

    HRESULT (STDMETHODCALLTYPE *Rotate)(
        ITransformProvider2 *This,
        double degrees);

    HRESULT (STDMETHODCALLTYPE *get_CanMove)(
        ITransformProvider2 *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanResize)(
        ITransformProvider2 *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_CanRotate)(
        ITransformProvider2 *This,
        WINBOOL *pRetVal);

    /*** ITransformProvider2 methods ***/
    HRESULT (STDMETHODCALLTYPE *Zoom)(
        ITransformProvider2 *This,
        double zoom);

    HRESULT (STDMETHODCALLTYPE *get_CanZoom)(
        ITransformProvider2 *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_ZoomLevel)(
        ITransformProvider2 *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_ZoomMinimum)(
        ITransformProvider2 *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_ZoomMaximum)(
        ITransformProvider2 *This,
        double *pRetVal);

    HRESULT (STDMETHODCALLTYPE *ZoomByUnit)(
        ITransformProvider2 *This,
        enum ZoomUnit zoomUnit);

    END_INTERFACE
} ITransformProvider2Vtbl;

interface ITransformProvider2 {
    CONST_VTBL ITransformProvider2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITransformProvider2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITransformProvider2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITransformProvider2_Release(This) (This)->lpVtbl->Release(This)
/*** ITransformProvider methods ***/
#define ITransformProvider2_Move(This,x,y) (This)->lpVtbl->Move(This,x,y)
#define ITransformProvider2_Resize(This,width,height) (This)->lpVtbl->Resize(This,width,height)
#define ITransformProvider2_Rotate(This,degrees) (This)->lpVtbl->Rotate(This,degrees)
#define ITransformProvider2_get_CanMove(This,pRetVal) (This)->lpVtbl->get_CanMove(This,pRetVal)
#define ITransformProvider2_get_CanResize(This,pRetVal) (This)->lpVtbl->get_CanResize(This,pRetVal)
#define ITransformProvider2_get_CanRotate(This,pRetVal) (This)->lpVtbl->get_CanRotate(This,pRetVal)
/*** ITransformProvider2 methods ***/
#define ITransformProvider2_Zoom(This,zoom) (This)->lpVtbl->Zoom(This,zoom)
#define ITransformProvider2_get_CanZoom(This,pRetVal) (This)->lpVtbl->get_CanZoom(This,pRetVal)
#define ITransformProvider2_get_ZoomLevel(This,pRetVal) (This)->lpVtbl->get_ZoomLevel(This,pRetVal)
#define ITransformProvider2_get_ZoomMinimum(This,pRetVal) (This)->lpVtbl->get_ZoomMinimum(This,pRetVal)
#define ITransformProvider2_get_ZoomMaximum(This,pRetVal) (This)->lpVtbl->get_ZoomMaximum(This,pRetVal)
#define ITransformProvider2_ZoomByUnit(This,zoomUnit) (This)->lpVtbl->ZoomByUnit(This,zoomUnit)
#else
/*** IUnknown methods ***/
static inline HRESULT ITransformProvider2_QueryInterface(ITransformProvider2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITransformProvider2_AddRef(ITransformProvider2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITransformProvider2_Release(ITransformProvider2* This) {
    return This->lpVtbl->Release(This);
}
/*** ITransformProvider methods ***/
static inline HRESULT ITransformProvider2_Move(ITransformProvider2* This,double x,double y) {
    return This->lpVtbl->Move(This,x,y);
}
static inline HRESULT ITransformProvider2_Resize(ITransformProvider2* This,double width,double height) {
    return This->lpVtbl->Resize(This,width,height);
}
static inline HRESULT ITransformProvider2_Rotate(ITransformProvider2* This,double degrees) {
    return This->lpVtbl->Rotate(This,degrees);
}
static inline HRESULT ITransformProvider2_get_CanMove(ITransformProvider2* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanMove(This,pRetVal);
}
static inline HRESULT ITransformProvider2_get_CanResize(ITransformProvider2* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanResize(This,pRetVal);
}
static inline HRESULT ITransformProvider2_get_CanRotate(ITransformProvider2* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanRotate(This,pRetVal);
}
/*** ITransformProvider2 methods ***/
static inline HRESULT ITransformProvider2_Zoom(ITransformProvider2* This,double zoom) {
    return This->lpVtbl->Zoom(This,zoom);
}
static inline HRESULT ITransformProvider2_get_CanZoom(ITransformProvider2* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_CanZoom(This,pRetVal);
}
static inline HRESULT ITransformProvider2_get_ZoomLevel(ITransformProvider2* This,double *pRetVal) {
    return This->lpVtbl->get_ZoomLevel(This,pRetVal);
}
static inline HRESULT ITransformProvider2_get_ZoomMinimum(ITransformProvider2* This,double *pRetVal) {
    return This->lpVtbl->get_ZoomMinimum(This,pRetVal);
}
static inline HRESULT ITransformProvider2_get_ZoomMaximum(ITransformProvider2* This,double *pRetVal) {
    return This->lpVtbl->get_ZoomMaximum(This,pRetVal);
}
static inline HRESULT ITransformProvider2_ZoomByUnit(ITransformProvider2* This,enum ZoomUnit zoomUnit) {
    return This->lpVtbl->ZoomByUnit(This,zoomUnit);
}
#endif
#endif

#endif


#endif  /* __ITransformProvider2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDragProvider interface
 */
#ifndef __IDragProvider_INTERFACE_DEFINED__
#define __IDragProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDragProvider, 0x6aa7bbbb, 0x7ff9, 0x497d, 0x90,0x4f, 0xd2,0x0b,0x89,0x79,0x29,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6aa7bbbb-7ff9-497d-904f-d20b897929d8")
IDragProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_IsGrabbed(
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DropEffect(
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DropEffects(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGrabbedItems(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDragProvider, 0x6aa7bbbb, 0x7ff9, 0x497d, 0x90,0x4f, 0xd2,0x0b,0x89,0x79,0x29,0xd8)
#endif
#else
typedef struct IDragProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDragProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDragProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDragProvider *This);

    /*** IDragProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsGrabbed)(
        IDragProvider *This,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_DropEffect)(
        IDragProvider *This,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_DropEffects)(
        IDragProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetGrabbedItems)(
        IDragProvider *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} IDragProviderVtbl;

interface IDragProvider {
    CONST_VTBL IDragProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDragProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDragProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDragProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IDragProvider methods ***/
#define IDragProvider_get_IsGrabbed(This,pRetVal) (This)->lpVtbl->get_IsGrabbed(This,pRetVal)
#define IDragProvider_get_DropEffect(This,pRetVal) (This)->lpVtbl->get_DropEffect(This,pRetVal)
#define IDragProvider_get_DropEffects(This,pRetVal) (This)->lpVtbl->get_DropEffects(This,pRetVal)
#define IDragProvider_GetGrabbedItems(This,pRetVal) (This)->lpVtbl->GetGrabbedItems(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IDragProvider_QueryInterface(IDragProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDragProvider_AddRef(IDragProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDragProvider_Release(IDragProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IDragProvider methods ***/
static inline HRESULT IDragProvider_get_IsGrabbed(IDragProvider* This,WINBOOL *pRetVal) {
    return This->lpVtbl->get_IsGrabbed(This,pRetVal);
}
static inline HRESULT IDragProvider_get_DropEffect(IDragProvider* This,BSTR *pRetVal) {
    return This->lpVtbl->get_DropEffect(This,pRetVal);
}
static inline HRESULT IDragProvider_get_DropEffects(IDragProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->get_DropEffects(This,pRetVal);
}
static inline HRESULT IDragProvider_GetGrabbedItems(IDragProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetGrabbedItems(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IDragProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDropTargetProvider interface
 */
#ifndef __IDropTargetProvider_INTERFACE_DEFINED__
#define __IDropTargetProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDropTargetProvider, 0xbae82bfd, 0x358a, 0x481c, 0x85,0xa0, 0xd8,0xb4,0xd9,0x0a,0x5d,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bae82bfd-358a-481c-85a0-d8b4d90a5d61")
IDropTargetProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_DropTargetEffect(
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DropTargetEffects(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDropTargetProvider, 0xbae82bfd, 0x358a, 0x481c, 0x85,0xa0, 0xd8,0xb4,0xd9,0x0a,0x5d,0x61)
#endif
#else
typedef struct IDropTargetProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDropTargetProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDropTargetProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDropTargetProvider *This);

    /*** IDropTargetProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DropTargetEffect)(
        IDropTargetProvider *This,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_DropTargetEffects)(
        IDropTargetProvider *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} IDropTargetProviderVtbl;

interface IDropTargetProvider {
    CONST_VTBL IDropTargetProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDropTargetProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDropTargetProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDropTargetProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IDropTargetProvider methods ***/
#define IDropTargetProvider_get_DropTargetEffect(This,pRetVal) (This)->lpVtbl->get_DropTargetEffect(This,pRetVal)
#define IDropTargetProvider_get_DropTargetEffects(This,pRetVal) (This)->lpVtbl->get_DropTargetEffects(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IDropTargetProvider_QueryInterface(IDropTargetProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDropTargetProvider_AddRef(IDropTargetProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDropTargetProvider_Release(IDropTargetProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IDropTargetProvider methods ***/
static inline HRESULT IDropTargetProvider_get_DropTargetEffect(IDropTargetProvider* This,BSTR *pRetVal) {
    return This->lpVtbl->get_DropTargetEffect(This,pRetVal);
}
static inline HRESULT IDropTargetProvider_get_DropTargetEffects(IDropTargetProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->get_DropTargetEffects(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IDropTargetProvider_INTERFACE_DEFINED__ */

#ifndef __ITextRangeProvider_FWD_DEFINED__
#define __ITextRangeProvider_FWD_DEFINED__
typedef interface ITextRangeProvider ITextRangeProvider;
#ifdef __cplusplus
interface ITextRangeProvider;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ITextProvider interface
 */
#ifndef __ITextProvider_INTERFACE_DEFINED__
#define __ITextProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITextProvider, 0x3589c92c, 0x63f3, 0x4367, 0x99,0xbb, 0xad,0xa6,0x53,0xb7,0x7c,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3589c92c-63f3-4367-99bb-ada653b77cf2")
ITextProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSelection(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVisibleRanges(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE RangeFromChild(
        IRawElementProviderSimple *childElement,
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE RangeFromPoint(
        struct UiaPoint point,
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DocumentRange(
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SupportedTextSelection(
        enum SupportedTextSelection *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITextProvider, 0x3589c92c, 0x63f3, 0x4367, 0x99,0xbb, 0xad,0xa6,0x53,0xb7,0x7c,0xf2)
#endif
#else
typedef struct ITextProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITextProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITextProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITextProvider *This);

    /*** ITextProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        ITextProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetVisibleRanges)(
        ITextProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *RangeFromChild)(
        ITextProvider *This,
        IRawElementProviderSimple *childElement,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *RangeFromPoint)(
        ITextProvider *This,
        struct UiaPoint point,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_DocumentRange)(
        ITextProvider *This,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_SupportedTextSelection)(
        ITextProvider *This,
        enum SupportedTextSelection *pRetVal);

    END_INTERFACE
} ITextProviderVtbl;

interface ITextProvider {
    CONST_VTBL ITextProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITextProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITextProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITextProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITextProvider methods ***/
#define ITextProvider_GetSelection(This,pRetVal) (This)->lpVtbl->GetSelection(This,pRetVal)
#define ITextProvider_GetVisibleRanges(This,pRetVal) (This)->lpVtbl->GetVisibleRanges(This,pRetVal)
#define ITextProvider_RangeFromChild(This,childElement,pRetVal) (This)->lpVtbl->RangeFromChild(This,childElement,pRetVal)
#define ITextProvider_RangeFromPoint(This,point,pRetVal) (This)->lpVtbl->RangeFromPoint(This,point,pRetVal)
#define ITextProvider_get_DocumentRange(This,pRetVal) (This)->lpVtbl->get_DocumentRange(This,pRetVal)
#define ITextProvider_get_SupportedTextSelection(This,pRetVal) (This)->lpVtbl->get_SupportedTextSelection(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITextProvider_QueryInterface(ITextProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITextProvider_AddRef(ITextProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITextProvider_Release(ITextProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITextProvider methods ***/
static inline HRESULT ITextProvider_GetSelection(ITextProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetSelection(This,pRetVal);
}
static inline HRESULT ITextProvider_GetVisibleRanges(ITextProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetVisibleRanges(This,pRetVal);
}
static inline HRESULT ITextProvider_RangeFromChild(ITextProvider* This,IRawElementProviderSimple *childElement,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->RangeFromChild(This,childElement,pRetVal);
}
static inline HRESULT ITextProvider_RangeFromPoint(ITextProvider* This,struct UiaPoint point,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->RangeFromPoint(This,point,pRetVal);
}
static inline HRESULT ITextProvider_get_DocumentRange(ITextProvider* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->get_DocumentRange(This,pRetVal);
}
static inline HRESULT ITextProvider_get_SupportedTextSelection(ITextProvider* This,enum SupportedTextSelection *pRetVal) {
    return This->lpVtbl->get_SupportedTextSelection(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITextProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITextProvider2 interface
 */
#ifndef __ITextProvider2_INTERFACE_DEFINED__
#define __ITextProvider2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITextProvider2, 0x0dc5e6ed, 0x3e16, 0x4bf1, 0x8f,0x9a, 0xa9,0x79,0x87,0x8b,0xc1,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0dc5e6ed-3e16-4bf1-8f9a-a979878bc195")
ITextProvider2 : public ITextProvider
{
    virtual HRESULT STDMETHODCALLTYPE RangeFromAnnotation(
        IRawElementProviderSimple *annotationElement,
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCaretRange(
        WINBOOL *isActive,
        ITextRangeProvider **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITextProvider2, 0x0dc5e6ed, 0x3e16, 0x4bf1, 0x8f,0x9a, 0xa9,0x79,0x87,0x8b,0xc1,0x95)
#endif
#else
typedef struct ITextProvider2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITextProvider2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITextProvider2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITextProvider2 *This);

    /*** ITextProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        ITextProvider2 *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetVisibleRanges)(
        ITextProvider2 *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *RangeFromChild)(
        ITextProvider2 *This,
        IRawElementProviderSimple *childElement,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *RangeFromPoint)(
        ITextProvider2 *This,
        struct UiaPoint point,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_DocumentRange)(
        ITextProvider2 *This,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_SupportedTextSelection)(
        ITextProvider2 *This,
        enum SupportedTextSelection *pRetVal);

    /*** ITextProvider2 methods ***/
    HRESULT (STDMETHODCALLTYPE *RangeFromAnnotation)(
        ITextProvider2 *This,
        IRawElementProviderSimple *annotationElement,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetCaretRange)(
        ITextProvider2 *This,
        WINBOOL *isActive,
        ITextRangeProvider **pRetVal);

    END_INTERFACE
} ITextProvider2Vtbl;

interface ITextProvider2 {
    CONST_VTBL ITextProvider2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITextProvider2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITextProvider2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITextProvider2_Release(This) (This)->lpVtbl->Release(This)
/*** ITextProvider methods ***/
#define ITextProvider2_GetSelection(This,pRetVal) (This)->lpVtbl->GetSelection(This,pRetVal)
#define ITextProvider2_GetVisibleRanges(This,pRetVal) (This)->lpVtbl->GetVisibleRanges(This,pRetVal)
#define ITextProvider2_RangeFromChild(This,childElement,pRetVal) (This)->lpVtbl->RangeFromChild(This,childElement,pRetVal)
#define ITextProvider2_RangeFromPoint(This,point,pRetVal) (This)->lpVtbl->RangeFromPoint(This,point,pRetVal)
#define ITextProvider2_get_DocumentRange(This,pRetVal) (This)->lpVtbl->get_DocumentRange(This,pRetVal)
#define ITextProvider2_get_SupportedTextSelection(This,pRetVal) (This)->lpVtbl->get_SupportedTextSelection(This,pRetVal)
/*** ITextProvider2 methods ***/
#define ITextProvider2_RangeFromAnnotation(This,annotationElement,pRetVal) (This)->lpVtbl->RangeFromAnnotation(This,annotationElement,pRetVal)
#define ITextProvider2_GetCaretRange(This,isActive,pRetVal) (This)->lpVtbl->GetCaretRange(This,isActive,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITextProvider2_QueryInterface(ITextProvider2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITextProvider2_AddRef(ITextProvider2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITextProvider2_Release(ITextProvider2* This) {
    return This->lpVtbl->Release(This);
}
/*** ITextProvider methods ***/
static inline HRESULT ITextProvider2_GetSelection(ITextProvider2* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetSelection(This,pRetVal);
}
static inline HRESULT ITextProvider2_GetVisibleRanges(ITextProvider2* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetVisibleRanges(This,pRetVal);
}
static inline HRESULT ITextProvider2_RangeFromChild(ITextProvider2* This,IRawElementProviderSimple *childElement,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->RangeFromChild(This,childElement,pRetVal);
}
static inline HRESULT ITextProvider2_RangeFromPoint(ITextProvider2* This,struct UiaPoint point,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->RangeFromPoint(This,point,pRetVal);
}
static inline HRESULT ITextProvider2_get_DocumentRange(ITextProvider2* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->get_DocumentRange(This,pRetVal);
}
static inline HRESULT ITextProvider2_get_SupportedTextSelection(ITextProvider2* This,enum SupportedTextSelection *pRetVal) {
    return This->lpVtbl->get_SupportedTextSelection(This,pRetVal);
}
/*** ITextProvider2 methods ***/
static inline HRESULT ITextProvider2_RangeFromAnnotation(ITextProvider2* This,IRawElementProviderSimple *annotationElement,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->RangeFromAnnotation(This,annotationElement,pRetVal);
}
static inline HRESULT ITextProvider2_GetCaretRange(ITextProvider2* This,WINBOOL *isActive,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->GetCaretRange(This,isActive,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITextProvider2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITextEditProvider interface
 */
#ifndef __ITextEditProvider_INTERFACE_DEFINED__
#define __ITextEditProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITextEditProvider, 0xea3605b4, 0x3a05, 0x400e, 0xb5,0xf9, 0x4e,0x91,0xb4,0x0f,0x61,0x76);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea3605b4-3a05-400e-b5f9-4e91b40f6176")
ITextEditProvider : public ITextProvider
{
    virtual HRESULT STDMETHODCALLTYPE GetActiveComposition(
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConversionTarget(
        ITextRangeProvider **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITextEditProvider, 0xea3605b4, 0x3a05, 0x400e, 0xb5,0xf9, 0x4e,0x91,0xb4,0x0f,0x61,0x76)
#endif
#else
typedef struct ITextEditProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITextEditProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITextEditProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITextEditProvider *This);

    /*** ITextProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        ITextEditProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetVisibleRanges)(
        ITextEditProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *RangeFromChild)(
        ITextEditProvider *This,
        IRawElementProviderSimple *childElement,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *RangeFromPoint)(
        ITextEditProvider *This,
        struct UiaPoint point,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_DocumentRange)(
        ITextEditProvider *This,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_SupportedTextSelection)(
        ITextEditProvider *This,
        enum SupportedTextSelection *pRetVal);

    /*** ITextEditProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetActiveComposition)(
        ITextEditProvider *This,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetConversionTarget)(
        ITextEditProvider *This,
        ITextRangeProvider **pRetVal);

    END_INTERFACE
} ITextEditProviderVtbl;

interface ITextEditProvider {
    CONST_VTBL ITextEditProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITextEditProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITextEditProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITextEditProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITextProvider methods ***/
#define ITextEditProvider_GetSelection(This,pRetVal) (This)->lpVtbl->GetSelection(This,pRetVal)
#define ITextEditProvider_GetVisibleRanges(This,pRetVal) (This)->lpVtbl->GetVisibleRanges(This,pRetVal)
#define ITextEditProvider_RangeFromChild(This,childElement,pRetVal) (This)->lpVtbl->RangeFromChild(This,childElement,pRetVal)
#define ITextEditProvider_RangeFromPoint(This,point,pRetVal) (This)->lpVtbl->RangeFromPoint(This,point,pRetVal)
#define ITextEditProvider_get_DocumentRange(This,pRetVal) (This)->lpVtbl->get_DocumentRange(This,pRetVal)
#define ITextEditProvider_get_SupportedTextSelection(This,pRetVal) (This)->lpVtbl->get_SupportedTextSelection(This,pRetVal)
/*** ITextEditProvider methods ***/
#define ITextEditProvider_GetActiveComposition(This,pRetVal) (This)->lpVtbl->GetActiveComposition(This,pRetVal)
#define ITextEditProvider_GetConversionTarget(This,pRetVal) (This)->lpVtbl->GetConversionTarget(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITextEditProvider_QueryInterface(ITextEditProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITextEditProvider_AddRef(ITextEditProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITextEditProvider_Release(ITextEditProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITextProvider methods ***/
static inline HRESULT ITextEditProvider_GetSelection(ITextEditProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetSelection(This,pRetVal);
}
static inline HRESULT ITextEditProvider_GetVisibleRanges(ITextEditProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetVisibleRanges(This,pRetVal);
}
static inline HRESULT ITextEditProvider_RangeFromChild(ITextEditProvider* This,IRawElementProviderSimple *childElement,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->RangeFromChild(This,childElement,pRetVal);
}
static inline HRESULT ITextEditProvider_RangeFromPoint(ITextEditProvider* This,struct UiaPoint point,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->RangeFromPoint(This,point,pRetVal);
}
static inline HRESULT ITextEditProvider_get_DocumentRange(ITextEditProvider* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->get_DocumentRange(This,pRetVal);
}
static inline HRESULT ITextEditProvider_get_SupportedTextSelection(ITextEditProvider* This,enum SupportedTextSelection *pRetVal) {
    return This->lpVtbl->get_SupportedTextSelection(This,pRetVal);
}
/*** ITextEditProvider methods ***/
static inline HRESULT ITextEditProvider_GetActiveComposition(ITextEditProvider* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->GetActiveComposition(This,pRetVal);
}
static inline HRESULT ITextEditProvider_GetConversionTarget(ITextEditProvider* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->GetConversionTarget(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITextEditProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITextRangeProvider interface
 */
#ifndef __ITextRangeProvider_INTERFACE_DEFINED__
#define __ITextRangeProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITextRangeProvider, 0x5347ad7b, 0xc355, 0x46f8, 0xaf,0xf5, 0x90,0x90,0x33,0x58,0x2f,0x63);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5347ad7b-c355-46f8-aff5-909033582f63")
ITextRangeProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE Compare(
        ITextRangeProvider *range,
        WINBOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompareEndpoints(
        enum TextPatternRangeEndpoint endpoint,
        ITextRangeProvider *targetRange,
        enum TextPatternRangeEndpoint targetEndpoint,
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExpandToEnclosingUnit(
        enum TextUnit unit) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindAttribute(
        TEXTATTRIBUTEID attributeId,
        VARIANT val,
        WINBOOL backward,
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindText(
        BSTR text,
        WINBOOL backward,
        WINBOOL ignoreCase,
        ITextRangeProvider **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAttributeValue(
        TEXTATTRIBUTEID attributeId,
        VARIANT *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoundingRectangles(
        SAFEARRAY **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnclosingElement(
        IRawElementProviderSimple **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetText(
        int maxLength,
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE Move(
        enum TextUnit unit,
        int count,
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveEndpointByUnit(
        enum TextPatternRangeEndpoint endpoint,
        enum TextUnit unit,
        int count,
        int *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveEndpointByRange(
        enum TextPatternRangeEndpoint endpoint,
        ITextRangeProvider *targetRange,
        enum TextPatternRangeEndpoint targetEndpoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE Select(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddToSelection(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveFromSelection(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScrollIntoView(
        WINBOOL alignToTop) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChildren(
        SAFEARRAY **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITextRangeProvider, 0x5347ad7b, 0xc355, 0x46f8, 0xaf,0xf5, 0x90,0x90,0x33,0x58,0x2f,0x63)
#endif
#else
typedef struct ITextRangeProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITextRangeProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITextRangeProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITextRangeProvider *This);

    /*** ITextRangeProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        ITextRangeProvider *This,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        ITextRangeProvider *This,
        ITextRangeProvider *range,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *CompareEndpoints)(
        ITextRangeProvider *This,
        enum TextPatternRangeEndpoint endpoint,
        ITextRangeProvider *targetRange,
        enum TextPatternRangeEndpoint targetEndpoint,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *ExpandToEnclosingUnit)(
        ITextRangeProvider *This,
        enum TextUnit unit);

    HRESULT (STDMETHODCALLTYPE *FindAttribute)(
        ITextRangeProvider *This,
        TEXTATTRIBUTEID attributeId,
        VARIANT val,
        WINBOOL backward,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *FindText)(
        ITextRangeProvider *This,
        BSTR text,
        WINBOOL backward,
        WINBOOL ignoreCase,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetAttributeValue)(
        ITextRangeProvider *This,
        TEXTATTRIBUTEID attributeId,
        VARIANT *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetBoundingRectangles)(
        ITextRangeProvider *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetEnclosingElement)(
        ITextRangeProvider *This,
        IRawElementProviderSimple **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetText)(
        ITextRangeProvider *This,
        int maxLength,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *Move)(
        ITextRangeProvider *This,
        enum TextUnit unit,
        int count,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *MoveEndpointByUnit)(
        ITextRangeProvider *This,
        enum TextPatternRangeEndpoint endpoint,
        enum TextUnit unit,
        int count,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *MoveEndpointByRange)(
        ITextRangeProvider *This,
        enum TextPatternRangeEndpoint endpoint,
        ITextRangeProvider *targetRange,
        enum TextPatternRangeEndpoint targetEndpoint);

    HRESULT (STDMETHODCALLTYPE *Select)(
        ITextRangeProvider *This);

    HRESULT (STDMETHODCALLTYPE *AddToSelection)(
        ITextRangeProvider *This);

    HRESULT (STDMETHODCALLTYPE *RemoveFromSelection)(
        ITextRangeProvider *This);

    HRESULT (STDMETHODCALLTYPE *ScrollIntoView)(
        ITextRangeProvider *This,
        WINBOOL alignToTop);

    HRESULT (STDMETHODCALLTYPE *GetChildren)(
        ITextRangeProvider *This,
        SAFEARRAY **pRetVal);

    END_INTERFACE
} ITextRangeProviderVtbl;

interface ITextRangeProvider {
    CONST_VTBL ITextRangeProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITextRangeProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITextRangeProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITextRangeProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITextRangeProvider methods ***/
#define ITextRangeProvider_Clone(This,pRetVal) (This)->lpVtbl->Clone(This,pRetVal)
#define ITextRangeProvider_Compare(This,range,pRetVal) (This)->lpVtbl->Compare(This,range,pRetVal)
#define ITextRangeProvider_CompareEndpoints(This,endpoint,targetRange,targetEndpoint,pRetVal) (This)->lpVtbl->CompareEndpoints(This,endpoint,targetRange,targetEndpoint,pRetVal)
#define ITextRangeProvider_ExpandToEnclosingUnit(This,unit) (This)->lpVtbl->ExpandToEnclosingUnit(This,unit)
#define ITextRangeProvider_FindAttribute(This,attributeId,val,backward,pRetVal) (This)->lpVtbl->FindAttribute(This,attributeId,val,backward,pRetVal)
#define ITextRangeProvider_FindText(This,text,backward,ignoreCase,pRetVal) (This)->lpVtbl->FindText(This,text,backward,ignoreCase,pRetVal)
#define ITextRangeProvider_GetAttributeValue(This,attributeId,pRetVal) (This)->lpVtbl->GetAttributeValue(This,attributeId,pRetVal)
#define ITextRangeProvider_GetBoundingRectangles(This,pRetVal) (This)->lpVtbl->GetBoundingRectangles(This,pRetVal)
#define ITextRangeProvider_GetEnclosingElement(This,pRetVal) (This)->lpVtbl->GetEnclosingElement(This,pRetVal)
#define ITextRangeProvider_GetText(This,maxLength,pRetVal) (This)->lpVtbl->GetText(This,maxLength,pRetVal)
#define ITextRangeProvider_Move(This,unit,count,pRetVal) (This)->lpVtbl->Move(This,unit,count,pRetVal)
#define ITextRangeProvider_MoveEndpointByUnit(This,endpoint,unit,count,pRetVal) (This)->lpVtbl->MoveEndpointByUnit(This,endpoint,unit,count,pRetVal)
#define ITextRangeProvider_MoveEndpointByRange(This,endpoint,targetRange,targetEndpoint) (This)->lpVtbl->MoveEndpointByRange(This,endpoint,targetRange,targetEndpoint)
#define ITextRangeProvider_Select(This) (This)->lpVtbl->Select(This)
#define ITextRangeProvider_AddToSelection(This) (This)->lpVtbl->AddToSelection(This)
#define ITextRangeProvider_RemoveFromSelection(This) (This)->lpVtbl->RemoveFromSelection(This)
#define ITextRangeProvider_ScrollIntoView(This,alignToTop) (This)->lpVtbl->ScrollIntoView(This,alignToTop)
#define ITextRangeProvider_GetChildren(This,pRetVal) (This)->lpVtbl->GetChildren(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITextRangeProvider_QueryInterface(ITextRangeProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITextRangeProvider_AddRef(ITextRangeProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITextRangeProvider_Release(ITextRangeProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITextRangeProvider methods ***/
static inline HRESULT ITextRangeProvider_Clone(ITextRangeProvider* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->Clone(This,pRetVal);
}
static inline HRESULT ITextRangeProvider_Compare(ITextRangeProvider* This,ITextRangeProvider *range,WINBOOL *pRetVal) {
    return This->lpVtbl->Compare(This,range,pRetVal);
}
static inline HRESULT ITextRangeProvider_CompareEndpoints(ITextRangeProvider* This,enum TextPatternRangeEndpoint endpoint,ITextRangeProvider *targetRange,enum TextPatternRangeEndpoint targetEndpoint,int *pRetVal) {
    return This->lpVtbl->CompareEndpoints(This,endpoint,targetRange,targetEndpoint,pRetVal);
}
static inline HRESULT ITextRangeProvider_ExpandToEnclosingUnit(ITextRangeProvider* This,enum TextUnit unit) {
    return This->lpVtbl->ExpandToEnclosingUnit(This,unit);
}
static inline HRESULT ITextRangeProvider_FindAttribute(ITextRangeProvider* This,TEXTATTRIBUTEID attributeId,VARIANT val,WINBOOL backward,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->FindAttribute(This,attributeId,val,backward,pRetVal);
}
static inline HRESULT ITextRangeProvider_FindText(ITextRangeProvider* This,BSTR text,WINBOOL backward,WINBOOL ignoreCase,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->FindText(This,text,backward,ignoreCase,pRetVal);
}
static inline HRESULT ITextRangeProvider_GetAttributeValue(ITextRangeProvider* This,TEXTATTRIBUTEID attributeId,VARIANT *pRetVal) {
    return This->lpVtbl->GetAttributeValue(This,attributeId,pRetVal);
}
static inline HRESULT ITextRangeProvider_GetBoundingRectangles(ITextRangeProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetBoundingRectangles(This,pRetVal);
}
static inline HRESULT ITextRangeProvider_GetEnclosingElement(ITextRangeProvider* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->GetEnclosingElement(This,pRetVal);
}
static inline HRESULT ITextRangeProvider_GetText(ITextRangeProvider* This,int maxLength,BSTR *pRetVal) {
    return This->lpVtbl->GetText(This,maxLength,pRetVal);
}
static inline HRESULT ITextRangeProvider_Move(ITextRangeProvider* This,enum TextUnit unit,int count,int *pRetVal) {
    return This->lpVtbl->Move(This,unit,count,pRetVal);
}
static inline HRESULT ITextRangeProvider_MoveEndpointByUnit(ITextRangeProvider* This,enum TextPatternRangeEndpoint endpoint,enum TextUnit unit,int count,int *pRetVal) {
    return This->lpVtbl->MoveEndpointByUnit(This,endpoint,unit,count,pRetVal);
}
static inline HRESULT ITextRangeProvider_MoveEndpointByRange(ITextRangeProvider* This,enum TextPatternRangeEndpoint endpoint,ITextRangeProvider *targetRange,enum TextPatternRangeEndpoint targetEndpoint) {
    return This->lpVtbl->MoveEndpointByRange(This,endpoint,targetRange,targetEndpoint);
}
static inline HRESULT ITextRangeProvider_Select(ITextRangeProvider* This) {
    return This->lpVtbl->Select(This);
}
static inline HRESULT ITextRangeProvider_AddToSelection(ITextRangeProvider* This) {
    return This->lpVtbl->AddToSelection(This);
}
static inline HRESULT ITextRangeProvider_RemoveFromSelection(ITextRangeProvider* This) {
    return This->lpVtbl->RemoveFromSelection(This);
}
static inline HRESULT ITextRangeProvider_ScrollIntoView(ITextRangeProvider* This,WINBOOL alignToTop) {
    return This->lpVtbl->ScrollIntoView(This,alignToTop);
}
static inline HRESULT ITextRangeProvider_GetChildren(ITextRangeProvider* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetChildren(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITextRangeProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITextRangeProvider2 interface
 */
#ifndef __ITextRangeProvider2_INTERFACE_DEFINED__
#define __ITextRangeProvider2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITextRangeProvider2, 0x9bbce42c, 0x1921, 0x4f18, 0x89,0xca, 0xdb,0xa1,0x91,0x0a,0x03,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9bbce42c-1921-4f18-89ca-dba1910a0386")
ITextRangeProvider2 : public ITextRangeProvider
{
    virtual HRESULT STDMETHODCALLTYPE ShowContextMenu(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITextRangeProvider2, 0x9bbce42c, 0x1921, 0x4f18, 0x89,0xca, 0xdb,0xa1,0x91,0x0a,0x03,0x86)
#endif
#else
typedef struct ITextRangeProvider2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITextRangeProvider2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITextRangeProvider2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITextRangeProvider2 *This);

    /*** ITextRangeProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        ITextRangeProvider2 *This,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        ITextRangeProvider2 *This,
        ITextRangeProvider *range,
        WINBOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *CompareEndpoints)(
        ITextRangeProvider2 *This,
        enum TextPatternRangeEndpoint endpoint,
        ITextRangeProvider *targetRange,
        enum TextPatternRangeEndpoint targetEndpoint,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *ExpandToEnclosingUnit)(
        ITextRangeProvider2 *This,
        enum TextUnit unit);

    HRESULT (STDMETHODCALLTYPE *FindAttribute)(
        ITextRangeProvider2 *This,
        TEXTATTRIBUTEID attributeId,
        VARIANT val,
        WINBOOL backward,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *FindText)(
        ITextRangeProvider2 *This,
        BSTR text,
        WINBOOL backward,
        WINBOOL ignoreCase,
        ITextRangeProvider **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetAttributeValue)(
        ITextRangeProvider2 *This,
        TEXTATTRIBUTEID attributeId,
        VARIANT *pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetBoundingRectangles)(
        ITextRangeProvider2 *This,
        SAFEARRAY **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetEnclosingElement)(
        ITextRangeProvider2 *This,
        IRawElementProviderSimple **pRetVal);

    HRESULT (STDMETHODCALLTYPE *GetText)(
        ITextRangeProvider2 *This,
        int maxLength,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *Move)(
        ITextRangeProvider2 *This,
        enum TextUnit unit,
        int count,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *MoveEndpointByUnit)(
        ITextRangeProvider2 *This,
        enum TextPatternRangeEndpoint endpoint,
        enum TextUnit unit,
        int count,
        int *pRetVal);

    HRESULT (STDMETHODCALLTYPE *MoveEndpointByRange)(
        ITextRangeProvider2 *This,
        enum TextPatternRangeEndpoint endpoint,
        ITextRangeProvider *targetRange,
        enum TextPatternRangeEndpoint targetEndpoint);

    HRESULT (STDMETHODCALLTYPE *Select)(
        ITextRangeProvider2 *This);

    HRESULT (STDMETHODCALLTYPE *AddToSelection)(
        ITextRangeProvider2 *This);

    HRESULT (STDMETHODCALLTYPE *RemoveFromSelection)(
        ITextRangeProvider2 *This);

    HRESULT (STDMETHODCALLTYPE *ScrollIntoView)(
        ITextRangeProvider2 *This,
        WINBOOL alignToTop);

    HRESULT (STDMETHODCALLTYPE *GetChildren)(
        ITextRangeProvider2 *This,
        SAFEARRAY **pRetVal);

    /*** ITextRangeProvider2 methods ***/
    HRESULT (STDMETHODCALLTYPE *ShowContextMenu)(
        ITextRangeProvider2 *This);

    END_INTERFACE
} ITextRangeProvider2Vtbl;

interface ITextRangeProvider2 {
    CONST_VTBL ITextRangeProvider2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITextRangeProvider2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITextRangeProvider2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITextRangeProvider2_Release(This) (This)->lpVtbl->Release(This)
/*** ITextRangeProvider methods ***/
#define ITextRangeProvider2_Clone(This,pRetVal) (This)->lpVtbl->Clone(This,pRetVal)
#define ITextRangeProvider2_Compare(This,range,pRetVal) (This)->lpVtbl->Compare(This,range,pRetVal)
#define ITextRangeProvider2_CompareEndpoints(This,endpoint,targetRange,targetEndpoint,pRetVal) (This)->lpVtbl->CompareEndpoints(This,endpoint,targetRange,targetEndpoint,pRetVal)
#define ITextRangeProvider2_ExpandToEnclosingUnit(This,unit) (This)->lpVtbl->ExpandToEnclosingUnit(This,unit)
#define ITextRangeProvider2_FindAttribute(This,attributeId,val,backward,pRetVal) (This)->lpVtbl->FindAttribute(This,attributeId,val,backward,pRetVal)
#define ITextRangeProvider2_FindText(This,text,backward,ignoreCase,pRetVal) (This)->lpVtbl->FindText(This,text,backward,ignoreCase,pRetVal)
#define ITextRangeProvider2_GetAttributeValue(This,attributeId,pRetVal) (This)->lpVtbl->GetAttributeValue(This,attributeId,pRetVal)
#define ITextRangeProvider2_GetBoundingRectangles(This,pRetVal) (This)->lpVtbl->GetBoundingRectangles(This,pRetVal)
#define ITextRangeProvider2_GetEnclosingElement(This,pRetVal) (This)->lpVtbl->GetEnclosingElement(This,pRetVal)
#define ITextRangeProvider2_GetText(This,maxLength,pRetVal) (This)->lpVtbl->GetText(This,maxLength,pRetVal)
#define ITextRangeProvider2_Move(This,unit,count,pRetVal) (This)->lpVtbl->Move(This,unit,count,pRetVal)
#define ITextRangeProvider2_MoveEndpointByUnit(This,endpoint,unit,count,pRetVal) (This)->lpVtbl->MoveEndpointByUnit(This,endpoint,unit,count,pRetVal)
#define ITextRangeProvider2_MoveEndpointByRange(This,endpoint,targetRange,targetEndpoint) (This)->lpVtbl->MoveEndpointByRange(This,endpoint,targetRange,targetEndpoint)
#define ITextRangeProvider2_Select(This) (This)->lpVtbl->Select(This)
#define ITextRangeProvider2_AddToSelection(This) (This)->lpVtbl->AddToSelection(This)
#define ITextRangeProvider2_RemoveFromSelection(This) (This)->lpVtbl->RemoveFromSelection(This)
#define ITextRangeProvider2_ScrollIntoView(This,alignToTop) (This)->lpVtbl->ScrollIntoView(This,alignToTop)
#define ITextRangeProvider2_GetChildren(This,pRetVal) (This)->lpVtbl->GetChildren(This,pRetVal)
/*** ITextRangeProvider2 methods ***/
#define ITextRangeProvider2_ShowContextMenu(This) (This)->lpVtbl->ShowContextMenu(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ITextRangeProvider2_QueryInterface(ITextRangeProvider2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITextRangeProvider2_AddRef(ITextRangeProvider2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITextRangeProvider2_Release(ITextRangeProvider2* This) {
    return This->lpVtbl->Release(This);
}
/*** ITextRangeProvider methods ***/
static inline HRESULT ITextRangeProvider2_Clone(ITextRangeProvider2* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->Clone(This,pRetVal);
}
static inline HRESULT ITextRangeProvider2_Compare(ITextRangeProvider2* This,ITextRangeProvider *range,WINBOOL *pRetVal) {
    return This->lpVtbl->Compare(This,range,pRetVal);
}
static inline HRESULT ITextRangeProvider2_CompareEndpoints(ITextRangeProvider2* This,enum TextPatternRangeEndpoint endpoint,ITextRangeProvider *targetRange,enum TextPatternRangeEndpoint targetEndpoint,int *pRetVal) {
    return This->lpVtbl->CompareEndpoints(This,endpoint,targetRange,targetEndpoint,pRetVal);
}
static inline HRESULT ITextRangeProvider2_ExpandToEnclosingUnit(ITextRangeProvider2* This,enum TextUnit unit) {
    return This->lpVtbl->ExpandToEnclosingUnit(This,unit);
}
static inline HRESULT ITextRangeProvider2_FindAttribute(ITextRangeProvider2* This,TEXTATTRIBUTEID attributeId,VARIANT val,WINBOOL backward,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->FindAttribute(This,attributeId,val,backward,pRetVal);
}
static inline HRESULT ITextRangeProvider2_FindText(ITextRangeProvider2* This,BSTR text,WINBOOL backward,WINBOOL ignoreCase,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->FindText(This,text,backward,ignoreCase,pRetVal);
}
static inline HRESULT ITextRangeProvider2_GetAttributeValue(ITextRangeProvider2* This,TEXTATTRIBUTEID attributeId,VARIANT *pRetVal) {
    return This->lpVtbl->GetAttributeValue(This,attributeId,pRetVal);
}
static inline HRESULT ITextRangeProvider2_GetBoundingRectangles(ITextRangeProvider2* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetBoundingRectangles(This,pRetVal);
}
static inline HRESULT ITextRangeProvider2_GetEnclosingElement(ITextRangeProvider2* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->GetEnclosingElement(This,pRetVal);
}
static inline HRESULT ITextRangeProvider2_GetText(ITextRangeProvider2* This,int maxLength,BSTR *pRetVal) {
    return This->lpVtbl->GetText(This,maxLength,pRetVal);
}
static inline HRESULT ITextRangeProvider2_Move(ITextRangeProvider2* This,enum TextUnit unit,int count,int *pRetVal) {
    return This->lpVtbl->Move(This,unit,count,pRetVal);
}
static inline HRESULT ITextRangeProvider2_MoveEndpointByUnit(ITextRangeProvider2* This,enum TextPatternRangeEndpoint endpoint,enum TextUnit unit,int count,int *pRetVal) {
    return This->lpVtbl->MoveEndpointByUnit(This,endpoint,unit,count,pRetVal);
}
static inline HRESULT ITextRangeProvider2_MoveEndpointByRange(ITextRangeProvider2* This,enum TextPatternRangeEndpoint endpoint,ITextRangeProvider *targetRange,enum TextPatternRangeEndpoint targetEndpoint) {
    return This->lpVtbl->MoveEndpointByRange(This,endpoint,targetRange,targetEndpoint);
}
static inline HRESULT ITextRangeProvider2_Select(ITextRangeProvider2* This) {
    return This->lpVtbl->Select(This);
}
static inline HRESULT ITextRangeProvider2_AddToSelection(ITextRangeProvider2* This) {
    return This->lpVtbl->AddToSelection(This);
}
static inline HRESULT ITextRangeProvider2_RemoveFromSelection(ITextRangeProvider2* This) {
    return This->lpVtbl->RemoveFromSelection(This);
}
static inline HRESULT ITextRangeProvider2_ScrollIntoView(ITextRangeProvider2* This,WINBOOL alignToTop) {
    return This->lpVtbl->ScrollIntoView(This,alignToTop);
}
static inline HRESULT ITextRangeProvider2_GetChildren(ITextRangeProvider2* This,SAFEARRAY **pRetVal) {
    return This->lpVtbl->GetChildren(This,pRetVal);
}
/*** ITextRangeProvider2 methods ***/
static inline HRESULT ITextRangeProvider2_ShowContextMenu(ITextRangeProvider2* This) {
    return This->lpVtbl->ShowContextMenu(This);
}
#endif
#endif

#endif


#endif  /* __ITextRangeProvider2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITextChildProvider interface
 */
#ifndef __ITextChildProvider_INTERFACE_DEFINED__
#define __ITextChildProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITextChildProvider, 0x4c2de2b9, 0xc88f, 0x4f88, 0xa1,0x11, 0xf1,0xd3,0x36,0xb7,0xd1,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4c2de2b9-c88f-4f88-a111-f1d336b7d1a9")
ITextChildProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_TextContainer(
        IRawElementProviderSimple **pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TextRange(
        ITextRangeProvider **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITextChildProvider, 0x4c2de2b9, 0xc88f, 0x4f88, 0xa1,0x11, 0xf1,0xd3,0x36,0xb7,0xd1,0xa9)
#endif
#else
typedef struct ITextChildProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITextChildProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITextChildProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITextChildProvider *This);

    /*** ITextChildProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TextContainer)(
        ITextChildProvider *This,
        IRawElementProviderSimple **pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_TextRange)(
        ITextChildProvider *This,
        ITextRangeProvider **pRetVal);

    END_INTERFACE
} ITextChildProviderVtbl;

interface ITextChildProvider {
    CONST_VTBL ITextChildProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITextChildProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITextChildProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITextChildProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITextChildProvider methods ***/
#define ITextChildProvider_get_TextContainer(This,pRetVal) (This)->lpVtbl->get_TextContainer(This,pRetVal)
#define ITextChildProvider_get_TextRange(This,pRetVal) (This)->lpVtbl->get_TextRange(This,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ITextChildProvider_QueryInterface(ITextChildProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITextChildProvider_AddRef(ITextChildProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITextChildProvider_Release(ITextChildProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITextChildProvider methods ***/
static inline HRESULT ITextChildProvider_get_TextContainer(ITextChildProvider* This,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->get_TextContainer(This,pRetVal);
}
static inline HRESULT ITextChildProvider_get_TextRange(ITextChildProvider* This,ITextRangeProvider **pRetVal) {
    return This->lpVtbl->get_TextRange(This,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ITextChildProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICustomNavigationProvider interface
 */
#ifndef __ICustomNavigationProvider_INTERFACE_DEFINED__
#define __ICustomNavigationProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICustomNavigationProvider, 0x2062a28a, 0x8c07, 0x4b94, 0x8e,0x12, 0x70,0x37,0xc6,0x22,0xae,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2062a28a-8c07-4b94-8e12-7037c622aeb8")
ICustomNavigationProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Navigate(
        enum NavigateDirection direction,
        IRawElementProviderSimple **pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICustomNavigationProvider, 0x2062a28a, 0x8c07, 0x4b94, 0x8e,0x12, 0x70,0x37,0xc6,0x22,0xae,0xb8)
#endif
#else
typedef struct ICustomNavigationProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICustomNavigationProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICustomNavigationProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICustomNavigationProvider *This);

    /*** ICustomNavigationProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *Navigate)(
        ICustomNavigationProvider *This,
        enum NavigateDirection direction,
        IRawElementProviderSimple **pRetVal);

    END_INTERFACE
} ICustomNavigationProviderVtbl;

interface ICustomNavigationProvider {
    CONST_VTBL ICustomNavigationProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICustomNavigationProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICustomNavigationProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICustomNavigationProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ICustomNavigationProvider methods ***/
#define ICustomNavigationProvider_Navigate(This,direction,pRetVal) (This)->lpVtbl->Navigate(This,direction,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT ICustomNavigationProvider_QueryInterface(ICustomNavigationProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICustomNavigationProvider_AddRef(ICustomNavigationProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICustomNavigationProvider_Release(ICustomNavigationProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ICustomNavigationProvider methods ***/
static inline HRESULT ICustomNavigationProvider_Navigate(ICustomNavigationProvider* This,enum NavigateDirection direction,IRawElementProviderSimple **pRetVal) {
    return This->lpVtbl->Navigate(This,direction,pRetVal);
}
#endif
#endif

#endif


#endif  /* __ICustomNavigationProvider_INTERFACE_DEFINED__ */

enum UIAutomationType {
    UIAutomationType_Int = 0x1,
    UIAutomationType_Bool = 0x2,
    UIAutomationType_String = 0x3,
    UIAutomationType_Double = 0x4,
    UIAutomationType_Point = 0x5,
    UIAutomationType_Rect = 0x6,
    UIAutomationType_Element = 0x7,
    UIAutomationType_Array = 0x10000,
    UIAutomationType_Out = 0x20000,
    UIAutomationType_IntArray = UIAutomationType_Int | UIAutomationType_Array,
    UIAutomationType_BoolArray = UIAutomationType_Bool | UIAutomationType_Array,
    UIAutomationType_StringArray = UIAutomationType_String | UIAutomationType_Array,
    UIAutomationType_DoubleArray = UIAutomationType_Double | UIAutomationType_Array,
    UIAutomationType_PointArray = UIAutomationType_Point | UIAutomationType_Array,
    UIAutomationType_RectArray = UIAutomationType_Rect | UIAutomationType_Array,
    UIAutomationType_ElementArray = UIAutomationType_Element | UIAutomationType_Array,
    UIAutomationType_OutInt = UIAutomationType_Int | UIAutomationType_Out,
    UIAutomationType_OutBool = UIAutomationType_Bool | UIAutomationType_Out,
    UIAutomationType_OutString = UIAutomationType_String | UIAutomationType_Out,
    UIAutomationType_OutDouble = UIAutomationType_Double | UIAutomationType_Out,
    UIAutomationType_OutPoint = UIAutomationType_Point | UIAutomationType_Out,
    UIAutomationType_OutRect = UIAutomationType_Rect | UIAutomationType_Out,
    UIAutomationType_OutElement = UIAutomationType_Element | UIAutomationType_Out,
    UIAutomationType_OutIntArray = (UIAutomationType_Int | UIAutomationType_Array) | UIAutomationType_Out,
    UIAutomationType_OutBoolArray = (UIAutomationType_Bool | UIAutomationType_Array) | UIAutomationType_Out,
    UIAutomationType_OutStringArray = (UIAutomationType_String | UIAutomationType_Array) | UIAutomationType_Out,
    UIAutomationType_OutDoubleArray = (UIAutomationType_Double | UIAutomationType_Array) | UIAutomationType_Out,
    UIAutomationType_OutPointArray = (UIAutomationType_Point | UIAutomationType_Array) | UIAutomationType_Out,
    UIAutomationType_OutRectArray = (UIAutomationType_Rect | UIAutomationType_Array) | UIAutomationType_Out,
    UIAutomationType_OutElementArray = (UIAutomationType_Element | UIAutomationType_Array) | UIAutomationType_Out
};
DEFINE_ENUM_FLAG_OPERATORS(UIAutomationType)
struct UIAutomationParameter {
    enum UIAutomationType type;
    void *pData;
};
struct UIAutomationPropertyInfo {
    GUID guid;
    LPCWSTR pProgrammaticName;
    enum UIAutomationType type;
};
struct UIAutomationEventInfo {
    GUID guid;
    LPCWSTR pProgrammaticName;
};
struct UIAutomationMethodInfo {
    LPCWSTR pProgrammaticName;
    WINBOOL doSetFocus;
    UINT cInParameters;
    UINT cOutParameters;
    enum UIAutomationType *pParameterTypes;
    LPCWSTR *pParameterNames;
};
/*****************************************************************************
 * IUIAutomationPatternInstance interface
 */
#ifndef __IUIAutomationPatternInstance_INTERFACE_DEFINED__
#define __IUIAutomationPatternInstance_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAutomationPatternInstance, 0xc03a7fe4, 0x9431, 0x409f, 0xbe,0xd8, 0xae,0x7c,0x22,0x99,0xbc,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c03a7fe4-9431-409f-bed8-ae7c2299bc8d")
IUIAutomationPatternInstance : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        UINT index,
        WINBOOL cached,
        enum UIAutomationType type,
        void *pPtr) = 0;

    virtual HRESULT STDMETHODCALLTYPE CallMethod(
        UINT index,
        const struct UIAutomationParameter *pParams,
        UINT cParams) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAutomationPatternInstance, 0xc03a7fe4, 0x9431, 0x409f, 0xbe,0xd8, 0xae,0x7c,0x22,0x99,0xbc,0x8d)
#endif
#else
typedef struct IUIAutomationPatternInstanceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAutomationPatternInstance *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAutomationPatternInstance *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAutomationPatternInstance *This);

    /*** IUIAutomationPatternInstance methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IUIAutomationPatternInstance *This,
        UINT index,
        WINBOOL cached,
        enum UIAutomationType type,
        void *pPtr);

    HRESULT (STDMETHODCALLTYPE *CallMethod)(
        IUIAutomationPatternInstance *This,
        UINT index,
        const struct UIAutomationParameter *pParams,
        UINT cParams);

    END_INTERFACE
} IUIAutomationPatternInstanceVtbl;

interface IUIAutomationPatternInstance {
    CONST_VTBL IUIAutomationPatternInstanceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAutomationPatternInstance_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAutomationPatternInstance_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAutomationPatternInstance_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAutomationPatternInstance methods ***/
#define IUIAutomationPatternInstance_GetProperty(This,index,cached,type,pPtr) (This)->lpVtbl->GetProperty(This,index,cached,type,pPtr)
#define IUIAutomationPatternInstance_CallMethod(This,index,pParams,cParams) (This)->lpVtbl->CallMethod(This,index,pParams,cParams)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAutomationPatternInstance_QueryInterface(IUIAutomationPatternInstance* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAutomationPatternInstance_AddRef(IUIAutomationPatternInstance* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAutomationPatternInstance_Release(IUIAutomationPatternInstance* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAutomationPatternInstance methods ***/
static inline HRESULT IUIAutomationPatternInstance_GetProperty(IUIAutomationPatternInstance* This,UINT index,WINBOOL cached,enum UIAutomationType type,void *pPtr) {
    return This->lpVtbl->GetProperty(This,index,cached,type,pPtr);
}
static inline HRESULT IUIAutomationPatternInstance_CallMethod(IUIAutomationPatternInstance* This,UINT index,const struct UIAutomationParameter *pParams,UINT cParams) {
    return This->lpVtbl->CallMethod(This,index,pParams,cParams);
}
#endif
#endif

#endif


#endif  /* __IUIAutomationPatternInstance_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAutomationPatternHandler interface
 */
#ifndef __IUIAutomationPatternHandler_INTERFACE_DEFINED__
#define __IUIAutomationPatternHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAutomationPatternHandler, 0xd97022f3, 0xa947, 0x465e, 0x8b,0x2a, 0xac,0x43,0x15,0xfa,0x54,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d97022f3-a947-465e-8b2a-ac4315fa54e8")
IUIAutomationPatternHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateClientWrapper(
        IUIAutomationPatternInstance *pPatternInstance,
        IUnknown **pClientWrapper) = 0;

    virtual HRESULT STDMETHODCALLTYPE Dispatch(
        IUnknown *pTarget,
        UINT index,
        const struct UIAutomationParameter *pParams,
        UINT cParams) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAutomationPatternHandler, 0xd97022f3, 0xa947, 0x465e, 0x8b,0x2a, 0xac,0x43,0x15,0xfa,0x54,0xe8)
#endif
#else
typedef struct IUIAutomationPatternHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAutomationPatternHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAutomationPatternHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAutomationPatternHandler *This);

    /*** IUIAutomationPatternHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateClientWrapper)(
        IUIAutomationPatternHandler *This,
        IUIAutomationPatternInstance *pPatternInstance,
        IUnknown **pClientWrapper);

    HRESULT (STDMETHODCALLTYPE *Dispatch)(
        IUIAutomationPatternHandler *This,
        IUnknown *pTarget,
        UINT index,
        const struct UIAutomationParameter *pParams,
        UINT cParams);

    END_INTERFACE
} IUIAutomationPatternHandlerVtbl;

interface IUIAutomationPatternHandler {
    CONST_VTBL IUIAutomationPatternHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAutomationPatternHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAutomationPatternHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAutomationPatternHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAutomationPatternHandler methods ***/
#define IUIAutomationPatternHandler_CreateClientWrapper(This,pPatternInstance,pClientWrapper) (This)->lpVtbl->CreateClientWrapper(This,pPatternInstance,pClientWrapper)
#define IUIAutomationPatternHandler_Dispatch(This,pTarget,index,pParams,cParams) (This)->lpVtbl->Dispatch(This,pTarget,index,pParams,cParams)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAutomationPatternHandler_QueryInterface(IUIAutomationPatternHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAutomationPatternHandler_AddRef(IUIAutomationPatternHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAutomationPatternHandler_Release(IUIAutomationPatternHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAutomationPatternHandler methods ***/
static inline HRESULT IUIAutomationPatternHandler_CreateClientWrapper(IUIAutomationPatternHandler* This,IUIAutomationPatternInstance *pPatternInstance,IUnknown **pClientWrapper) {
    return This->lpVtbl->CreateClientWrapper(This,pPatternInstance,pClientWrapper);
}
static inline HRESULT IUIAutomationPatternHandler_Dispatch(IUIAutomationPatternHandler* This,IUnknown *pTarget,UINT index,const struct UIAutomationParameter *pParams,UINT cParams) {
    return This->lpVtbl->Dispatch(This,pTarget,index,pParams,cParams);
}
#endif
#endif

#endif


#endif  /* __IUIAutomationPatternHandler_INTERFACE_DEFINED__ */

struct UIAutomationPatternInfo {
    GUID guid;
    LPCWSTR pProgrammaticName;
    GUID providerInterfaceId;
    GUID clientInterfaceId;
    UINT cProperties;
    struct UIAutomationPropertyInfo *pProperties;
    UINT cMethods;
    struct UIAutomationMethodInfo *pMethods;
    UINT cEvents;
    struct UIAutomationEventInfo *pEvents;
    IUIAutomationPatternHandler *pPatternHandler;
};
/*****************************************************************************
 * IUIAutomationRegistrar interface
 */
#ifndef __IUIAutomationRegistrar_INTERFACE_DEFINED__
#define __IUIAutomationRegistrar_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAutomationRegistrar, 0x8609c4ec, 0x4a1a, 0x4d88, 0xa3,0x57, 0x5a,0x66,0xe0,0x60,0xe1,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8609c4ec-4a1a-4d88-a357-5a66e060e1cf")
IUIAutomationRegistrar : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterProperty(
        const struct UIAutomationPropertyInfo *property,
        PROPERTYID *propertyId) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterEvent(
        const struct UIAutomationEventInfo *event,
        EVENTID *eventId) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterPattern(
        const struct UIAutomationPatternInfo *pattern,
        PATTERNID *pPatternId,
        PROPERTYID *pPatternAvailablePropertyId,
        UINT propertyIdCount,
        PROPERTYID *pPropertyIds,
        UINT eventIdCount,
        EVENTID *pEventIds) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAutomationRegistrar, 0x8609c4ec, 0x4a1a, 0x4d88, 0xa3,0x57, 0x5a,0x66,0xe0,0x60,0xe1,0xcf)
#endif
#else
typedef struct IUIAutomationRegistrarVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAutomationRegistrar *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAutomationRegistrar *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAutomationRegistrar *This);

    /*** IUIAutomationRegistrar methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterProperty)(
        IUIAutomationRegistrar *This,
        const struct UIAutomationPropertyInfo *property,
        PROPERTYID *propertyId);

    HRESULT (STDMETHODCALLTYPE *RegisterEvent)(
        IUIAutomationRegistrar *This,
        const struct UIAutomationEventInfo *event,
        EVENTID *eventId);

    HRESULT (STDMETHODCALLTYPE *RegisterPattern)(
        IUIAutomationRegistrar *This,
        const struct UIAutomationPatternInfo *pattern,
        PATTERNID *pPatternId,
        PROPERTYID *pPatternAvailablePropertyId,
        UINT propertyIdCount,
        PROPERTYID *pPropertyIds,
        UINT eventIdCount,
        EVENTID *pEventIds);

    END_INTERFACE
} IUIAutomationRegistrarVtbl;

interface IUIAutomationRegistrar {
    CONST_VTBL IUIAutomationRegistrarVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAutomationRegistrar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAutomationRegistrar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAutomationRegistrar_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAutomationRegistrar methods ***/
#define IUIAutomationRegistrar_RegisterProperty(This,property,propertyId) (This)->lpVtbl->RegisterProperty(This,property,propertyId)
#define IUIAutomationRegistrar_RegisterEvent(This,event,eventId) (This)->lpVtbl->RegisterEvent(This,event,eventId)
#define IUIAutomationRegistrar_RegisterPattern(This,pattern,pPatternId,pPatternAvailablePropertyId,propertyIdCount,pPropertyIds,eventIdCount,pEventIds) (This)->lpVtbl->RegisterPattern(This,pattern,pPatternId,pPatternAvailablePropertyId,propertyIdCount,pPropertyIds,eventIdCount,pEventIds)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAutomationRegistrar_QueryInterface(IUIAutomationRegistrar* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAutomationRegistrar_AddRef(IUIAutomationRegistrar* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAutomationRegistrar_Release(IUIAutomationRegistrar* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAutomationRegistrar methods ***/
static inline HRESULT IUIAutomationRegistrar_RegisterProperty(IUIAutomationRegistrar* This,const struct UIAutomationPropertyInfo *property,PROPERTYID *propertyId) {
    return This->lpVtbl->RegisterProperty(This,property,propertyId);
}
static inline HRESULT IUIAutomationRegistrar_RegisterEvent(IUIAutomationRegistrar* This,const struct UIAutomationEventInfo *event,EVENTID *eventId) {
    return This->lpVtbl->RegisterEvent(This,event,eventId);
}
static inline HRESULT IUIAutomationRegistrar_RegisterPattern(IUIAutomationRegistrar* This,const struct UIAutomationPatternInfo *pattern,PATTERNID *pPatternId,PROPERTYID *pPatternAvailablePropertyId,UINT propertyIdCount,PROPERTYID *pPropertyIds,UINT eventIdCount,EVENTID *pEventIds) {
    return This->lpVtbl->RegisterPattern(This,pattern,pPatternId,pPatternAvailablePropertyId,propertyIdCount,pPropertyIds,eventIdCount,pEventIds);
}
#endif
#endif

#endif


#endif  /* __IUIAutomationRegistrar_INTERFACE_DEFINED__ */

/*****************************************************************************
 * CUIAutomationRegistrar coclass
 */

DEFINE_GUID(CLSID_CUIAutomationRegistrar, 0x6e29fabf, 0x9977, 0x42d1, 0x8d,0x0e, 0xca,0x7e,0x61,0xad,0x87,0xe6);

#ifdef __cplusplus
class DECLSPEC_UUID("6e29fabf-9977-42d1-8d0e-ca7e61ad87e6") CUIAutomationRegistrar;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CUIAutomationRegistrar, 0x6e29fabf, 0x9977, 0x42d1, 0x8d,0x0e, 0xca,0x7e,0x61,0xad,0x87,0xe6)
#endif
#endif

#endif /* __UIA_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __uiautomationcore_h__ */
