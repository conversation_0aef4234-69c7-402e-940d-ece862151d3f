/*** Autogenerated by WIDL 10.12 from include/windows.networking.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_networking_h__
#define __windows_networking_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CNetworking_CIEndpointPair_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIEndpointPair_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIEndpointPair __x_ABI_CWindows_CNetworking_CIEndpointPair;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIEndpointPair ABI::Windows::Networking::IEndpointPair
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IEndpointPair;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIEndpointPairFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIEndpointPairFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIEndpointPairFactory __x_ABI_CWindows_CNetworking_CIEndpointPairFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory ABI::Windows::Networking::IEndpointPairFactory
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IEndpointPairFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIHostName_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostName_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIHostName __x_ABI_CWindows_CNetworking_CIHostName;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIHostName ABI::Windows::Networking::IHostName
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IHostName;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIHostNameFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostNameFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIHostNameFactory __x_ABI_CWindows_CNetworking_CIHostNameFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory ABI::Windows::Networking::IHostNameFactory
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IHostNameFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIHostNameStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostNameStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIHostNameStatics __x_ABI_CWindows_CNetworking_CIHostNameStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics ABI::Windows::Networking::IHostNameStatics
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IHostNameStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CEndpointPair_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CEndpointPair_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            class EndpointPair;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CEndpointPair __x_ABI_CWindows_CNetworking_CEndpointPair;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CEndpointPair_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CHostName_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CHostName_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            class HostName;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CHostName __x_ABI_CWindows_CNetworking_CHostName;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CHostName_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CNetworking__CEndpointPair __FIIterable_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CNetworking__CHostName __FIIterable_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIIterator_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CNetworking__CEndpointPair __FIIterator_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIIterator_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CNetworking__CHostName __FIIterator_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CEndpointPair __FIVectorView_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CHostName __FIVectorView_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIVector_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CNetworking__CHostName __FIVector_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIVector_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.networking.connectivity.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CDomainNameType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CDomainNameType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            enum DomainNameType {
                DomainNameType_Suffix = 0,
                DomainNameType_FullyQualified = 1
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CDomainNameType {
    DomainNameType_Suffix = 0,
    DomainNameType_FullyQualified = 1
};
#ifdef WIDL_using_Windows_Networking
#define DomainNameType __x_ABI_CWindows_CNetworking_CDomainNameType
#endif /* WIDL_using_Windows_Networking */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CDomainNameType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CDomainNameType __x_ABI_CWindows_CNetworking_CDomainNameType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CHostNameSortOptions_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CHostNameSortOptions_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            enum HostNameSortOptions {
                HostNameSortOptions_None = 0x0,
                HostNameSortOptions_OptimizeForLongConnections = 0x2
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CHostNameSortOptions {
    HostNameSortOptions_None = 0x0,
    HostNameSortOptions_OptimizeForLongConnections = 0x2
};
#ifdef WIDL_using_Windows_Networking
#define HostNameSortOptions __x_ABI_CWindows_CNetworking_CHostNameSortOptions
#endif /* WIDL_using_Windows_Networking */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CHostNameSortOptions_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CHostNameSortOptions __x_ABI_CWindows_CNetworking_CHostNameSortOptions;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CHostNameType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CHostNameType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            enum HostNameType {
                HostNameType_DomainName = 0,
                HostNameType_Ipv4 = 1,
                HostNameType_Ipv6 = 2,
                HostNameType_Bluetooth = 3
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CHostNameType {
    HostNameType_DomainName = 0,
    HostNameType_Ipv4 = 1,
    HostNameType_Ipv6 = 2,
    HostNameType_Bluetooth = 3
};
#ifdef WIDL_using_Windows_Networking
#define HostNameType __x_ABI_CWindows_CNetworking_CHostNameType
#endif /* WIDL_using_Windows_Networking */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CHostNameType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CHostNameType __x_ABI_CWindows_CNetworking_CHostNameType;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CNetworking_CIEndpointPair_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIEndpointPair_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIEndpointPair __x_ABI_CWindows_CNetworking_CIEndpointPair;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIEndpointPair ABI::Windows::Networking::IEndpointPair
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IEndpointPair;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIEndpointPairFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIEndpointPairFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIEndpointPairFactory __x_ABI_CWindows_CNetworking_CIEndpointPairFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory ABI::Windows::Networking::IEndpointPairFactory
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IEndpointPairFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIHostName_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostName_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIHostName __x_ABI_CWindows_CNetworking_CIHostName;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIHostName ABI::Windows::Networking::IHostName
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IHostName;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIHostNameFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostNameFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIHostNameFactory __x_ABI_CWindows_CNetworking_CIHostNameFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory ABI::Windows::Networking::IHostNameFactory
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IHostNameFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CIHostNameStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostNameStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CIHostNameStatics __x_ABI_CWindows_CNetworking_CIHostNameStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics ABI::Windows::Networking::IHostNameStatics
namespace ABI {
    namespace Windows {
        namespace Networking {
            interface IHostNameStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CNetworking__CEndpointPair __FIIterable_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CNetworking__CHostName __FIIterable_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIIterator_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CNetworking__CEndpointPair __FIIterator_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIIterator_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CNetworking__CHostName __FIIterator_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CEndpointPair __FIVectorView_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CHostName __FIVectorView_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIVector_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CNetworking__CHostName __FIVector_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIVector_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IEndpointPair interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CIEndpointPair_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CIEndpointPair, 0x33a0aa36, 0xf8fa, 0x4b30, 0xb8,0x56, 0x76,0x51,0x7c,0x3b,0xd0,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            MIDL_INTERFACE("33a0aa36-f8fa-4b30-b856-76517c3bd06d")
            IEndpointPair : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_LocalHostName(
                    ABI::Windows::Networking::IHostName **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_LocalHostName(
                    ABI::Windows::Networking::IHostName *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LocalServiceName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_LocalServiceName(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RemoteHostName(
                    ABI::Windows::Networking::IHostName **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_RemoteHostName(
                    ABI::Windows::Networking::IHostName *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RemoteServiceName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_RemoteServiceName(
                    HSTRING value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CIEndpointPair, 0x33a0aa36, 0xf8fa, 0x4b30, 0xb8,0x56, 0x76,0x51,0x7c,0x3b,0xd0,0x6d)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CIEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        TrustLevel *trustLevel);

    /*** IEndpointPair methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LocalHostName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *put_LocalHostName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        __x_ABI_CWindows_CNetworking_CIHostName *value);

    HRESULT (STDMETHODCALLTYPE *get_LocalServiceName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_LocalServiceName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_RemoteHostName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *put_RemoteHostName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        __x_ABI_CWindows_CNetworking_CIHostName *value);

    HRESULT (STDMETHODCALLTYPE *get_RemoteServiceName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_RemoteServiceName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPair *This,
        HSTRING value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CIEndpointPairVtbl;

interface __x_ABI_CWindows_CNetworking_CIEndpointPair {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CIEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IEndpointPair methods ***/
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_get_LocalHostName(This,value) (This)->lpVtbl->get_LocalHostName(This,value)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_put_LocalHostName(This,value) (This)->lpVtbl->put_LocalHostName(This,value)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_get_LocalServiceName(This,value) (This)->lpVtbl->get_LocalServiceName(This,value)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_put_LocalServiceName(This,value) (This)->lpVtbl->put_LocalServiceName(This,value)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_get_RemoteHostName(This,value) (This)->lpVtbl->get_RemoteHostName(This,value)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_put_RemoteHostName(This,value) (This)->lpVtbl->put_RemoteHostName(This,value)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_get_RemoteServiceName(This,value) (This)->lpVtbl->get_RemoteServiceName(This,value)
#define __x_ABI_CWindows_CNetworking_CIEndpointPair_put_RemoteServiceName(This,value) (This)->lpVtbl->put_RemoteServiceName(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_QueryInterface(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIEndpointPair_AddRef(__x_ABI_CWindows_CNetworking_CIEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIEndpointPair_Release(__x_ABI_CWindows_CNetworking_CIEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_GetIids(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_GetTrustLevel(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IEndpointPair methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_get_LocalHostName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->get_LocalHostName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_put_LocalHostName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,__x_ABI_CWindows_CNetworking_CIHostName *value) {
    return This->lpVtbl->put_LocalHostName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_get_LocalServiceName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,HSTRING *value) {
    return This->lpVtbl->get_LocalServiceName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_put_LocalServiceName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,HSTRING value) {
    return This->lpVtbl->put_LocalServiceName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_get_RemoteHostName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->get_RemoteHostName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_put_RemoteHostName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,__x_ABI_CWindows_CNetworking_CIHostName *value) {
    return This->lpVtbl->put_RemoteHostName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_get_RemoteServiceName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,HSTRING *value) {
    return This->lpVtbl->get_RemoteServiceName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPair_put_RemoteServiceName(__x_ABI_CWindows_CNetworking_CIEndpointPair* This,HSTRING value) {
    return This->lpVtbl->put_RemoteServiceName(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking
#define IID_IEndpointPair IID___x_ABI_CWindows_CNetworking_CIEndpointPair
#define IEndpointPairVtbl __x_ABI_CWindows_CNetworking_CIEndpointPairVtbl
#define IEndpointPair __x_ABI_CWindows_CNetworking_CIEndpointPair
#define IEndpointPair_QueryInterface __x_ABI_CWindows_CNetworking_CIEndpointPair_QueryInterface
#define IEndpointPair_AddRef __x_ABI_CWindows_CNetworking_CIEndpointPair_AddRef
#define IEndpointPair_Release __x_ABI_CWindows_CNetworking_CIEndpointPair_Release
#define IEndpointPair_GetIids __x_ABI_CWindows_CNetworking_CIEndpointPair_GetIids
#define IEndpointPair_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CIEndpointPair_GetRuntimeClassName
#define IEndpointPair_GetTrustLevel __x_ABI_CWindows_CNetworking_CIEndpointPair_GetTrustLevel
#define IEndpointPair_get_LocalHostName __x_ABI_CWindows_CNetworking_CIEndpointPair_get_LocalHostName
#define IEndpointPair_put_LocalHostName __x_ABI_CWindows_CNetworking_CIEndpointPair_put_LocalHostName
#define IEndpointPair_get_LocalServiceName __x_ABI_CWindows_CNetworking_CIEndpointPair_get_LocalServiceName
#define IEndpointPair_put_LocalServiceName __x_ABI_CWindows_CNetworking_CIEndpointPair_put_LocalServiceName
#define IEndpointPair_get_RemoteHostName __x_ABI_CWindows_CNetworking_CIEndpointPair_get_RemoteHostName
#define IEndpointPair_put_RemoteHostName __x_ABI_CWindows_CNetworking_CIEndpointPair_put_RemoteHostName
#define IEndpointPair_get_RemoteServiceName __x_ABI_CWindows_CNetworking_CIEndpointPair_get_RemoteServiceName
#define IEndpointPair_put_RemoteServiceName __x_ABI_CWindows_CNetworking_CIEndpointPair_put_RemoteServiceName
#endif /* WIDL_using_Windows_Networking */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CIEndpointPair_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IEndpointPairFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CIEndpointPairFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIEndpointPairFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CIEndpointPairFactory, 0xb609d971, 0x64e0, 0x442b, 0xaa,0x6f, 0xcc,0x8c,0x8f,0x18,0x1f,0x78);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            MIDL_INTERFACE("b609d971-64e0-442b-aa6f-cc8c8f181f78")
            IEndpointPairFactory : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateEndpointPair(
                    ABI::Windows::Networking::IHostName *host,
                    HSTRING service,
                    ABI::Windows::Networking::IHostName *remote_host,
                    HSTRING remote_service,
                    ABI::Windows::Networking::IEndpointPair **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory, 0xb609d971, 0x64e0, 0x442b, 0xaa,0x6f, 0xcc,0x8c,0x8f,0x18,0x1f,0x78)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CIEndpointPairFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CIEndpointPairFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CIEndpointPairFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CIEndpointPairFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CIEndpointPairFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CIEndpointPairFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CIEndpointPairFactory *This,
        TrustLevel *trustLevel);

    /*** IEndpointPairFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateEndpointPair)(
        __x_ABI_CWindows_CNetworking_CIEndpointPairFactory *This,
        __x_ABI_CWindows_CNetworking_CIHostName *host,
        HSTRING service,
        __x_ABI_CWindows_CNetworking_CIHostName *remote_host,
        HSTRING remote_service,
        __x_ABI_CWindows_CNetworking_CIEndpointPair **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CIEndpointPairFactoryVtbl;

interface __x_ABI_CWindows_CNetworking_CIEndpointPairFactory {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CIEndpointPairFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IEndpointPairFactory methods ***/
#define __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_CreateEndpointPair(This,host,service,remote_host,remote_service,value) (This)->lpVtbl->CreateEndpointPair(This,host,service,remote_host,remote_service,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_QueryInterface(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_AddRef(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_Release(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetIids(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetTrustLevel(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IEndpointPairFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_CreateEndpointPair(__x_ABI_CWindows_CNetworking_CIEndpointPairFactory* This,__x_ABI_CWindows_CNetworking_CIHostName *host,HSTRING service,__x_ABI_CWindows_CNetworking_CIHostName *remote_host,HSTRING remote_service,__x_ABI_CWindows_CNetworking_CIEndpointPair **value) {
    return This->lpVtbl->CreateEndpointPair(This,host,service,remote_host,remote_service,value);
}
#endif
#ifdef WIDL_using_Windows_Networking
#define IID_IEndpointPairFactory IID___x_ABI_CWindows_CNetworking_CIEndpointPairFactory
#define IEndpointPairFactoryVtbl __x_ABI_CWindows_CNetworking_CIEndpointPairFactoryVtbl
#define IEndpointPairFactory __x_ABI_CWindows_CNetworking_CIEndpointPairFactory
#define IEndpointPairFactory_QueryInterface __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_QueryInterface
#define IEndpointPairFactory_AddRef __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_AddRef
#define IEndpointPairFactory_Release __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_Release
#define IEndpointPairFactory_GetIids __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetIids
#define IEndpointPairFactory_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetRuntimeClassName
#define IEndpointPairFactory_GetTrustLevel __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_GetTrustLevel
#define IEndpointPairFactory_CreateEndpointPair __x_ABI_CWindows_CNetworking_CIEndpointPairFactory_CreateEndpointPair
#endif /* WIDL_using_Windows_Networking */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CIEndpointPairFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IHostName interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CIHostName_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostName_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CIHostName, 0xbf8ecaad, 0xed96, 0x49a7, 0x90,0x84, 0xd4,0x16,0xca,0xe8,0x8d,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            MIDL_INTERFACE("bf8ecaad-ed96-49a7-9084-d416cae88dcb")
            IHostName : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_IPInformation(
                    ABI::Windows::Networking::Connectivity::IIPInformation **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RawName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CanonicalName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Type(
                    ABI::Windows::Networking::HostNameType *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE IsEqual(
                    ABI::Windows::Networking::IHostName *name,
                    boolean *equal) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CIHostName, 0xbf8ecaad, 0xed96, 0x49a7, 0x90,0x84, 0xd4,0x16,0xca,0xe8,0x8d,0xcb)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CIHostNameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CIHostName *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CIHostName *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        TrustLevel *trustLevel);

    /*** IHostName methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IPInformation)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation **value);

    HRESULT (STDMETHODCALLTYPE *get_RawName)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_CanonicalName)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        __x_ABI_CWindows_CNetworking_CHostNameType *value);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        __x_ABI_CWindows_CNetworking_CIHostName *This,
        __x_ABI_CWindows_CNetworking_CIHostName *name,
        boolean *equal);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CIHostNameVtbl;

interface __x_ABI_CWindows_CNetworking_CIHostName {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CIHostNameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostName_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CIHostName_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CIHostName_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostName_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CIHostName_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CIHostName_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHostName methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostName_get_IPInformation(This,value) (This)->lpVtbl->get_IPInformation(This,value)
#define __x_ABI_CWindows_CNetworking_CIHostName_get_RawName(This,value) (This)->lpVtbl->get_RawName(This,value)
#define __x_ABI_CWindows_CNetworking_CIHostName_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#define __x_ABI_CWindows_CNetworking_CIHostName_get_CanonicalName(This,value) (This)->lpVtbl->get_CanonicalName(This,value)
#define __x_ABI_CWindows_CNetworking_CIHostName_get_Type(This,value) (This)->lpVtbl->get_Type(This,value)
#define __x_ABI_CWindows_CNetworking_CIHostName_IsEqual(This,name,equal) (This)->lpVtbl->IsEqual(This,name,equal)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_QueryInterface(__x_ABI_CWindows_CNetworking_CIHostName* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIHostName_AddRef(__x_ABI_CWindows_CNetworking_CIHostName* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIHostName_Release(__x_ABI_CWindows_CNetworking_CIHostName* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_GetIids(__x_ABI_CWindows_CNetworking_CIHostName* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CIHostName* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_GetTrustLevel(__x_ABI_CWindows_CNetworking_CIHostName* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHostName methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_get_IPInformation(__x_ABI_CWindows_CNetworking_CIHostName* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation **value) {
    return This->lpVtbl->get_IPInformation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_get_RawName(__x_ABI_CWindows_CNetworking_CIHostName* This,HSTRING *value) {
    return This->lpVtbl->get_RawName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_get_DisplayName(__x_ABI_CWindows_CNetworking_CIHostName* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_get_CanonicalName(__x_ABI_CWindows_CNetworking_CIHostName* This,HSTRING *value) {
    return This->lpVtbl->get_CanonicalName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_get_Type(__x_ABI_CWindows_CNetworking_CIHostName* This,__x_ABI_CWindows_CNetworking_CHostNameType *value) {
    return This->lpVtbl->get_Type(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostName_IsEqual(__x_ABI_CWindows_CNetworking_CIHostName* This,__x_ABI_CWindows_CNetworking_CIHostName *name,boolean *equal) {
    return This->lpVtbl->IsEqual(This,name,equal);
}
#endif
#ifdef WIDL_using_Windows_Networking
#define IID_IHostName IID___x_ABI_CWindows_CNetworking_CIHostName
#define IHostNameVtbl __x_ABI_CWindows_CNetworking_CIHostNameVtbl
#define IHostName __x_ABI_CWindows_CNetworking_CIHostName
#define IHostName_QueryInterface __x_ABI_CWindows_CNetworking_CIHostName_QueryInterface
#define IHostName_AddRef __x_ABI_CWindows_CNetworking_CIHostName_AddRef
#define IHostName_Release __x_ABI_CWindows_CNetworking_CIHostName_Release
#define IHostName_GetIids __x_ABI_CWindows_CNetworking_CIHostName_GetIids
#define IHostName_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CIHostName_GetRuntimeClassName
#define IHostName_GetTrustLevel __x_ABI_CWindows_CNetworking_CIHostName_GetTrustLevel
#define IHostName_get_IPInformation __x_ABI_CWindows_CNetworking_CIHostName_get_IPInformation
#define IHostName_get_RawName __x_ABI_CWindows_CNetworking_CIHostName_get_RawName
#define IHostName_get_DisplayName __x_ABI_CWindows_CNetworking_CIHostName_get_DisplayName
#define IHostName_get_CanonicalName __x_ABI_CWindows_CNetworking_CIHostName_get_CanonicalName
#define IHostName_get_Type __x_ABI_CWindows_CNetworking_CIHostName_get_Type
#define IHostName_IsEqual __x_ABI_CWindows_CNetworking_CIHostName_IsEqual
#endif /* WIDL_using_Windows_Networking */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CIHostName_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IHostNameFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CIHostNameFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostNameFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CIHostNameFactory, 0x458c23ed, 0x712f, 0x4576, 0xad,0xf1, 0xc2,0x0b,0x2c,0x64,0x35,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            MIDL_INTERFACE("458c23ed-712f-4576-adf1-c20b2c643558")
            IHostNameFactory : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateHostName(
                    HSTRING name,
                    ABI::Windows::Networking::IHostName **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CIHostNameFactory, 0x458c23ed, 0x712f, 0x4576, 0xad,0xf1, 0xc2,0x0b,0x2c,0x64,0x35,0x58)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CIHostNameFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CIHostNameFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CIHostNameFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CIHostNameFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CIHostNameFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CIHostNameFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CIHostNameFactory *This,
        TrustLevel *trustLevel);

    /*** IHostNameFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateHostName)(
        __x_ABI_CWindows_CNetworking_CIHostNameFactory *This,
        HSTRING name,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CIHostNameFactoryVtbl;

interface __x_ABI_CWindows_CNetworking_CIHostNameFactory {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CIHostNameFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHostNameFactory methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostNameFactory_CreateHostName(This,name,value) (This)->lpVtbl->CreateHostName(This,name,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameFactory_QueryInterface(__x_ABI_CWindows_CNetworking_CIHostNameFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIHostNameFactory_AddRef(__x_ABI_CWindows_CNetworking_CIHostNameFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIHostNameFactory_Release(__x_ABI_CWindows_CNetworking_CIHostNameFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetIids(__x_ABI_CWindows_CNetworking_CIHostNameFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CIHostNameFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetTrustLevel(__x_ABI_CWindows_CNetworking_CIHostNameFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHostNameFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameFactory_CreateHostName(__x_ABI_CWindows_CNetworking_CIHostNameFactory* This,HSTRING name,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->CreateHostName(This,name,value);
}
#endif
#ifdef WIDL_using_Windows_Networking
#define IID_IHostNameFactory IID___x_ABI_CWindows_CNetworking_CIHostNameFactory
#define IHostNameFactoryVtbl __x_ABI_CWindows_CNetworking_CIHostNameFactoryVtbl
#define IHostNameFactory __x_ABI_CWindows_CNetworking_CIHostNameFactory
#define IHostNameFactory_QueryInterface __x_ABI_CWindows_CNetworking_CIHostNameFactory_QueryInterface
#define IHostNameFactory_AddRef __x_ABI_CWindows_CNetworking_CIHostNameFactory_AddRef
#define IHostNameFactory_Release __x_ABI_CWindows_CNetworking_CIHostNameFactory_Release
#define IHostNameFactory_GetIids __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetIids
#define IHostNameFactory_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetRuntimeClassName
#define IHostNameFactory_GetTrustLevel __x_ABI_CWindows_CNetworking_CIHostNameFactory_GetTrustLevel
#define IHostNameFactory_CreateHostName __x_ABI_CWindows_CNetworking_CIHostNameFactory_CreateHostName
#endif /* WIDL_using_Windows_Networking */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CIHostNameFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IHostNameStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CIHostNameStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CIHostNameStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CIHostNameStatics, 0xf68cd4bf, 0xa388, 0x4e8b, 0x91,0xea, 0x54,0xdd,0x6d,0xd9,0x01,0xc0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            MIDL_INTERFACE("f68cd4bf-a388-4e8b-91ea-54dd6dd901c0")
            IHostNameStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE Compare(
                    HSTRING value1,
                    HSTRING value2,
                    INT32 *result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CIHostNameStatics, 0xf68cd4bf, 0xa388, 0x4e8b, 0x91,0xea, 0x54,0xdd,0x6d,0xd9,0x01,0xc0)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CIHostNameStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CIHostNameStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CIHostNameStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CIHostNameStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CIHostNameStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CIHostNameStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CIHostNameStatics *This,
        TrustLevel *trustLevel);

    /*** IHostNameStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *Compare)(
        __x_ABI_CWindows_CNetworking_CIHostNameStatics *This,
        HSTRING value1,
        HSTRING value2,
        INT32 *result);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CIHostNameStaticsVtbl;

interface __x_ABI_CWindows_CNetworking_CIHostNameStatics {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CIHostNameStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHostNameStatics methods ***/
#define __x_ABI_CWindows_CNetworking_CIHostNameStatics_Compare(This,value1,value2,result) (This)->lpVtbl->Compare(This,value1,value2,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameStatics_QueryInterface(__x_ABI_CWindows_CNetworking_CIHostNameStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIHostNameStatics_AddRef(__x_ABI_CWindows_CNetworking_CIHostNameStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CIHostNameStatics_Release(__x_ABI_CWindows_CNetworking_CIHostNameStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetIids(__x_ABI_CWindows_CNetworking_CIHostNameStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CIHostNameStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetTrustLevel(__x_ABI_CWindows_CNetworking_CIHostNameStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHostNameStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CIHostNameStatics_Compare(__x_ABI_CWindows_CNetworking_CIHostNameStatics* This,HSTRING value1,HSTRING value2,INT32 *result) {
    return This->lpVtbl->Compare(This,value1,value2,result);
}
#endif
#ifdef WIDL_using_Windows_Networking
#define IID_IHostNameStatics IID___x_ABI_CWindows_CNetworking_CIHostNameStatics
#define IHostNameStaticsVtbl __x_ABI_CWindows_CNetworking_CIHostNameStaticsVtbl
#define IHostNameStatics __x_ABI_CWindows_CNetworking_CIHostNameStatics
#define IHostNameStatics_QueryInterface __x_ABI_CWindows_CNetworking_CIHostNameStatics_QueryInterface
#define IHostNameStatics_AddRef __x_ABI_CWindows_CNetworking_CIHostNameStatics_AddRef
#define IHostNameStatics_Release __x_ABI_CWindows_CNetworking_CIHostNameStatics_Release
#define IHostNameStatics_GetIids __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetIids
#define IHostNameStatics_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetRuntimeClassName
#define IHostNameStatics_GetTrustLevel __x_ABI_CWindows_CNetworking_CIHostNameStatics_GetTrustLevel
#define IHostNameStatics_Compare __x_ABI_CWindows_CNetworking_CIHostNameStatics_Compare
#endif /* WIDL_using_Windows_Networking */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CIHostNameStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.EndpointPair
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_EndpointPair_DEFINED
#define RUNTIMECLASS_Windows_Networking_EndpointPair_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_EndpointPair[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','E','n','d','p','o','i','n','t','P','a','i','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_EndpointPair[] = L"Windows.Networking.EndpointPair";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_EndpointPair[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','E','n','d','p','o','i','n','t','P','a','i','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_EndpointPair_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.HostName
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_HostName_DEFINED
#define RUNTIMECLASS_Windows_Networking_HostName_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_HostName[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','H','o','s','t','N','a','m','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_HostName[] = L"Windows.Networking.HostName";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_HostName[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','H','o','s','t','N','a','m','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_HostName_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Networking::EndpointPair* > interface
 */
#ifndef ____FIIterable_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CNetworking__CEndpointPair, 0xd7ec83c4, 0xa17b, 0x51bf, 0x89,0x97, 0xaa,0x33,0xb9,0x10,0x2d,0xc9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("d7ec83c4-a17b-51bf-8997-aa33b9102dc9")
                IIterable<ABI::Windows::Networking::EndpointPair* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::EndpointPair*, ABI::Windows::Networking::IEndpointPair* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CNetworking__CEndpointPair, 0xd7ec83c4, 0xa17b, 0x51bf, 0x89,0x97, 0xaa,0x33,0xb9,0x10,0x2d,0xc9)
#endif
#else
typedef struct __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Networking::EndpointPair* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        __FIIterator_1_Windows__CNetworking__CEndpointPair **value);

    END_INTERFACE
} __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl;

interface __FIIterable_1_Windows__CNetworking__CEndpointPair {
    CONST_VTBL __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Networking::EndpointPair* > methods ***/
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_QueryInterface(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CNetworking__CEndpointPair_AddRef(__FIIterable_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CNetworking__CEndpointPair_Release(__FIIterable_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_GetIids(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Networking::EndpointPair* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_First(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,__FIIterator_1_Windows__CNetworking__CEndpointPair **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_EndpointPair IID___FIIterable_1_Windows__CNetworking__CEndpointPair
#define IIterable_EndpointPairVtbl __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl
#define IIterable_EndpointPair __FIIterable_1_Windows__CNetworking__CEndpointPair
#define IIterable_EndpointPair_QueryInterface __FIIterable_1_Windows__CNetworking__CEndpointPair_QueryInterface
#define IIterable_EndpointPair_AddRef __FIIterable_1_Windows__CNetworking__CEndpointPair_AddRef
#define IIterable_EndpointPair_Release __FIIterable_1_Windows__CNetworking__CEndpointPair_Release
#define IIterable_EndpointPair_GetIids __FIIterable_1_Windows__CNetworking__CEndpointPair_GetIids
#define IIterable_EndpointPair_GetRuntimeClassName __FIIterable_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName
#define IIterable_EndpointPair_GetTrustLevel __FIIterable_1_Windows__CNetworking__CEndpointPair_GetTrustLevel
#define IIterable_EndpointPair_First __FIIterable_1_Windows__CNetworking__CEndpointPair_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Networking::HostName* > interface
 */
#ifndef ____FIIterable_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CNetworking__CHostName, 0x9e5f3ed0, 0xcf1c, 0x5d38, 0x83,0x2c, 0xac,0xea,0x61,0x64,0xbf,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("9e5f3ed0-cf1c-5d38-832c-acea6164bf5c")
                IIterable<ABI::Windows::Networking::HostName* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::HostName*, ABI::Windows::Networking::IHostName* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CNetworking__CHostName, 0x9e5f3ed0, 0xcf1c, 0x5d38, 0x83,0x2c, 0xac,0xea,0x61,0x64,0xbf,0x5c)
#endif
#else
typedef struct __FIIterable_1_Windows__CNetworking__CHostNameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CNetworking__CHostName *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CNetworking__CHostName *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CNetworking__CHostName *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CNetworking__CHostName *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CNetworking__CHostName *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CNetworking__CHostName *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Networking::HostName* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CNetworking__CHostName *This,
        __FIIterator_1_Windows__CNetworking__CHostName **value);

    END_INTERFACE
} __FIIterable_1_Windows__CNetworking__CHostNameVtbl;

interface __FIIterable_1_Windows__CNetworking__CHostName {
    CONST_VTBL __FIIterable_1_Windows__CNetworking__CHostNameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CNetworking__CHostName_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CNetworking__CHostName_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CNetworking__CHostName_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CNetworking__CHostName_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CNetworking__CHostName_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CNetworking__CHostName_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Networking::HostName* > methods ***/
#define __FIIterable_1_Windows__CNetworking__CHostName_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CHostName_QueryInterface(__FIIterable_1_Windows__CNetworking__CHostName* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CNetworking__CHostName_AddRef(__FIIterable_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CNetworking__CHostName_Release(__FIIterable_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CHostName_GetIids(__FIIterable_1_Windows__CNetworking__CHostName* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CNetworking__CHostName_GetRuntimeClassName(__FIIterable_1_Windows__CNetworking__CHostName* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CNetworking__CHostName_GetTrustLevel(__FIIterable_1_Windows__CNetworking__CHostName* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Networking::HostName* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CHostName_First(__FIIterable_1_Windows__CNetworking__CHostName* This,__FIIterator_1_Windows__CNetworking__CHostName **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_HostName IID___FIIterable_1_Windows__CNetworking__CHostName
#define IIterable_HostNameVtbl __FIIterable_1_Windows__CNetworking__CHostNameVtbl
#define IIterable_HostName __FIIterable_1_Windows__CNetworking__CHostName
#define IIterable_HostName_QueryInterface __FIIterable_1_Windows__CNetworking__CHostName_QueryInterface
#define IIterable_HostName_AddRef __FIIterable_1_Windows__CNetworking__CHostName_AddRef
#define IIterable_HostName_Release __FIIterable_1_Windows__CNetworking__CHostName_Release
#define IIterable_HostName_GetIids __FIIterable_1_Windows__CNetworking__CHostName_GetIids
#define IIterable_HostName_GetRuntimeClassName __FIIterable_1_Windows__CNetworking__CHostName_GetRuntimeClassName
#define IIterable_HostName_GetTrustLevel __FIIterable_1_Windows__CNetworking__CHostName_GetTrustLevel
#define IIterable_HostName_First __FIIterable_1_Windows__CNetworking__CHostName_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Networking::EndpointPair* > interface
 */
#ifndef ____FIIterator_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CNetworking__CEndpointPair, 0xc899ff9f, 0xe6f5, 0x5673, 0x81,0x0c, 0x04,0xe2,0xff,0x98,0x70,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("c899ff9f-e6f5-5673-810c-04e2ff98704f")
                IIterator<ABI::Windows::Networking::EndpointPair* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::EndpointPair*, ABI::Windows::Networking::IEndpointPair* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CNetworking__CEndpointPair, 0xc899ff9f, 0xe6f5, 0x5673, 0x81,0x0c, 0x04,0xe2,0xff,0x98,0x70,0x4f)
#endif
#else
typedef struct __FIIterator_1_Windows__CNetworking__CEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Networking::EndpointPair* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        __x_ABI_CWindows_CNetworking_CIEndpointPair **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CNetworking__CEndpointPair *This,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CIEndpointPair **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CNetworking__CEndpointPairVtbl;

interface __FIIterator_1_Windows__CNetworking__CEndpointPair {
    CONST_VTBL __FIIterator_1_Windows__CNetworking__CEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Networking::EndpointPair* > methods ***/
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CNetworking__CEndpointPair_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_QueryInterface(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CNetworking__CEndpointPair_AddRef(__FIIterator_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CNetworking__CEndpointPair_Release(__FIIterator_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_GetIids(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Networking::EndpointPair* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_get_Current(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,__x_ABI_CWindows_CNetworking_CIEndpointPair **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_get_HasCurrent(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_MoveNext(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CEndpointPair_GetMany(__FIIterator_1_Windows__CNetworking__CEndpointPair* This,UINT32 items_size,__x_ABI_CWindows_CNetworking_CIEndpointPair **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_EndpointPair IID___FIIterator_1_Windows__CNetworking__CEndpointPair
#define IIterator_EndpointPairVtbl __FIIterator_1_Windows__CNetworking__CEndpointPairVtbl
#define IIterator_EndpointPair __FIIterator_1_Windows__CNetworking__CEndpointPair
#define IIterator_EndpointPair_QueryInterface __FIIterator_1_Windows__CNetworking__CEndpointPair_QueryInterface
#define IIterator_EndpointPair_AddRef __FIIterator_1_Windows__CNetworking__CEndpointPair_AddRef
#define IIterator_EndpointPair_Release __FIIterator_1_Windows__CNetworking__CEndpointPair_Release
#define IIterator_EndpointPair_GetIids __FIIterator_1_Windows__CNetworking__CEndpointPair_GetIids
#define IIterator_EndpointPair_GetRuntimeClassName __FIIterator_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName
#define IIterator_EndpointPair_GetTrustLevel __FIIterator_1_Windows__CNetworking__CEndpointPair_GetTrustLevel
#define IIterator_EndpointPair_get_Current __FIIterator_1_Windows__CNetworking__CEndpointPair_get_Current
#define IIterator_EndpointPair_get_HasCurrent __FIIterator_1_Windows__CNetworking__CEndpointPair_get_HasCurrent
#define IIterator_EndpointPair_MoveNext __FIIterator_1_Windows__CNetworking__CEndpointPair_MoveNext
#define IIterator_EndpointPair_GetMany __FIIterator_1_Windows__CNetworking__CEndpointPair_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Networking::HostName* > interface
 */
#ifndef ____FIIterator_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CNetworking__CHostName, 0x557bf83c, 0xa428, 0x5dbd, 0xa0,0xfe, 0x05,0xf6,0xee,0x54,0x3d,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("557bf83c-a428-5dbd-a0fe-05f6ee543d45")
                IIterator<ABI::Windows::Networking::HostName* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::HostName*, ABI::Windows::Networking::IHostName* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CNetworking__CHostName, 0x557bf83c, 0xa428, 0x5dbd, 0xa0,0xfe, 0x05,0xf6,0xee,0x54,0x3d,0x45)
#endif
#else
typedef struct __FIIterator_1_Windows__CNetworking__CHostNameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CNetworking__CHostName *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CNetworking__CHostName *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Networking::HostName* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CNetworking__CHostName *This,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CIHostName **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CNetworking__CHostNameVtbl;

interface __FIIterator_1_Windows__CNetworking__CHostName {
    CONST_VTBL __FIIterator_1_Windows__CNetworking__CHostNameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CNetworking__CHostName_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CNetworking__CHostName_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CNetworking__CHostName_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CNetworking__CHostName_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CNetworking__CHostName_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CNetworking__CHostName_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Networking::HostName* > methods ***/
#define __FIIterator_1_Windows__CNetworking__CHostName_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CNetworking__CHostName_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CNetworking__CHostName_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CNetworking__CHostName_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_QueryInterface(__FIIterator_1_Windows__CNetworking__CHostName* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CNetworking__CHostName_AddRef(__FIIterator_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CNetworking__CHostName_Release(__FIIterator_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_GetIids(__FIIterator_1_Windows__CNetworking__CHostName* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_GetRuntimeClassName(__FIIterator_1_Windows__CNetworking__CHostName* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_GetTrustLevel(__FIIterator_1_Windows__CNetworking__CHostName* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Networking::HostName* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_get_Current(__FIIterator_1_Windows__CNetworking__CHostName* This,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_get_HasCurrent(__FIIterator_1_Windows__CNetworking__CHostName* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_MoveNext(__FIIterator_1_Windows__CNetworking__CHostName* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CNetworking__CHostName_GetMany(__FIIterator_1_Windows__CNetworking__CHostName* This,UINT32 items_size,__x_ABI_CWindows_CNetworking_CIHostName **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_HostName IID___FIIterator_1_Windows__CNetworking__CHostName
#define IIterator_HostNameVtbl __FIIterator_1_Windows__CNetworking__CHostNameVtbl
#define IIterator_HostName __FIIterator_1_Windows__CNetworking__CHostName
#define IIterator_HostName_QueryInterface __FIIterator_1_Windows__CNetworking__CHostName_QueryInterface
#define IIterator_HostName_AddRef __FIIterator_1_Windows__CNetworking__CHostName_AddRef
#define IIterator_HostName_Release __FIIterator_1_Windows__CNetworking__CHostName_Release
#define IIterator_HostName_GetIids __FIIterator_1_Windows__CNetworking__CHostName_GetIids
#define IIterator_HostName_GetRuntimeClassName __FIIterator_1_Windows__CNetworking__CHostName_GetRuntimeClassName
#define IIterator_HostName_GetTrustLevel __FIIterator_1_Windows__CNetworking__CHostName_GetTrustLevel
#define IIterator_HostName_get_Current __FIIterator_1_Windows__CNetworking__CHostName_get_Current
#define IIterator_HostName_get_HasCurrent __FIIterator_1_Windows__CNetworking__CHostName_get_HasCurrent
#define IIterator_HostName_MoveNext __FIIterator_1_Windows__CNetworking__CHostName_MoveNext
#define IIterator_HostName_GetMany __FIIterator_1_Windows__CNetworking__CHostName_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::EndpointPair* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CEndpointPair, 0x8780a851, 0x6d48, 0x5006, 0x92,0x88, 0x81,0xf3,0xd7,0x04,0x5a,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("8780a851-6d48-5006-9288-81f3d7045a96")
                IVectorView<ABI::Windows::Networking::EndpointPair* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::EndpointPair*, ABI::Windows::Networking::IEndpointPair* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CEndpointPair, 0x8780a851, 0x6d48, 0x5006, 0x92,0x88, 0x81,0xf3,0xd7,0x04,0x5a,0x96)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::EndpointPair* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CIEndpointPair **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        __x_ABI_CWindows_CNetworking_CIEndpointPair *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CIEndpointPair **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl;

interface __FIVectorView_1_Windows__CNetworking__CEndpointPair {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::EndpointPair* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::EndpointPair* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetAt(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,UINT32 index,__x_ABI_CWindows_CNetworking_CIEndpointPair **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Size(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_IndexOf(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,__x_ABI_CWindows_CNetworking_CIEndpointPair *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetMany(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CIEndpointPair **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_EndpointPair IID___FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IVectorView_EndpointPairVtbl __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl
#define IVectorView_EndpointPair __FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IVectorView_EndpointPair_QueryInterface __FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface
#define IVectorView_EndpointPair_AddRef __FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef
#define IVectorView_EndpointPair_Release __FIVectorView_1_Windows__CNetworking__CEndpointPair_Release
#define IVectorView_EndpointPair_GetIids __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids
#define IVectorView_EndpointPair_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName
#define IVectorView_EndpointPair_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel
#define IVectorView_EndpointPair_GetAt __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetAt
#define IVectorView_EndpointPair_get_Size __FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Size
#define IVectorView_EndpointPair_IndexOf __FIVectorView_1_Windows__CNetworking__CEndpointPair_IndexOf
#define IVectorView_EndpointPair_GetMany __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::HostName* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CHostName, 0xf4706ab1, 0x55a3, 0x5270, 0xaf,0xb2, 0x73,0x29,0x88,0xfe,0x82,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f4706ab1-55a3-5270-afb2-732988fe8227")
                IVectorView<ABI::Windows::Networking::HostName* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::HostName*, ABI::Windows::Networking::IHostName* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CHostName, 0xf4706ab1, 0x55a3, 0x5270, 0xaf,0xb2, 0x73,0x29,0x88,0xfe,0x82,0x27)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CHostNameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::HostName* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        __x_ABI_CWindows_CNetworking_CIHostName *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CIHostName **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CHostNameVtbl;

interface __FIVectorView_1_Windows__CNetworking__CHostName {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CHostNameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CHostName_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CHostName_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CHostName_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::HostName* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CHostName_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CHostName_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_QueryInterface(__FIVectorView_1_Windows__CNetworking__CHostName* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CHostName_AddRef(__FIVectorView_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CHostName_Release(__FIVectorView_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetIids(__FIVectorView_1_Windows__CNetworking__CHostName* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CHostName* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CHostName* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::HostName* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetAt(__FIVectorView_1_Windows__CNetworking__CHostName* This,UINT32 index,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_get_Size(__FIVectorView_1_Windows__CNetworking__CHostName* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_IndexOf(__FIVectorView_1_Windows__CNetworking__CHostName* This,__x_ABI_CWindows_CNetworking_CIHostName *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetMany(__FIVectorView_1_Windows__CNetworking__CHostName* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CIHostName **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_HostName IID___FIVectorView_1_Windows__CNetworking__CHostName
#define IVectorView_HostNameVtbl __FIVectorView_1_Windows__CNetworking__CHostNameVtbl
#define IVectorView_HostName __FIVectorView_1_Windows__CNetworking__CHostName
#define IVectorView_HostName_QueryInterface __FIVectorView_1_Windows__CNetworking__CHostName_QueryInterface
#define IVectorView_HostName_AddRef __FIVectorView_1_Windows__CNetworking__CHostName_AddRef
#define IVectorView_HostName_Release __FIVectorView_1_Windows__CNetworking__CHostName_Release
#define IVectorView_HostName_GetIids __FIVectorView_1_Windows__CNetworking__CHostName_GetIids
#define IVectorView_HostName_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CHostName_GetRuntimeClassName
#define IVectorView_HostName_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CHostName_GetTrustLevel
#define IVectorView_HostName_GetAt __FIVectorView_1_Windows__CNetworking__CHostName_GetAt
#define IVectorView_HostName_get_Size __FIVectorView_1_Windows__CNetworking__CHostName_get_Size
#define IVectorView_HostName_IndexOf __FIVectorView_1_Windows__CNetworking__CHostName_IndexOf
#define IVectorView_HostName_GetMany __FIVectorView_1_Windows__CNetworking__CHostName_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Networking::HostName* > interface
 */
#ifndef ____FIVector_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CNetworking__CHostName, 0x90c71c29, 0xa9b5, 0x5267, 0xa5,0xad, 0x8b,0x75,0x67,0x36,0x31,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("90c71c29-a9b5-5267-a5ad-8b756736317c")
                IVector<ABI::Windows::Networking::HostName* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::HostName*, ABI::Windows::Networking::IHostName* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CNetworking__CHostName, 0x90c71c29, 0xa9b5, 0x5267, 0xa5,0xad, 0x8b,0x75,0x67,0x36,0x31,0x7c)
#endif
#else
typedef struct __FIVector_1_Windows__CNetworking__CHostNameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CNetworking__CHostName *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CNetworking__CHostName *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Networking::HostName* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        __FIVectorView_1_Windows__CNetworking__CHostName **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        __x_ABI_CWindows_CNetworking_CIHostName *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CIHostName *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CIHostName *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        __x_ABI_CWindows_CNetworking_CIHostName *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CNetworking__CHostName *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CNetworking__CHostName *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CIHostName **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CNetworking__CHostName *This,
        UINT32 count,
        __x_ABI_CWindows_CNetworking_CIHostName **items);

    END_INTERFACE
} __FIVector_1_Windows__CNetworking__CHostNameVtbl;

interface __FIVector_1_Windows__CNetworking__CHostName {
    CONST_VTBL __FIVector_1_Windows__CNetworking__CHostNameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CNetworking__CHostName_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CNetworking__CHostName_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CNetworking__CHostName_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CNetworking__CHostName_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CNetworking__CHostName_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CNetworking__CHostName_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Networking::HostName* > methods ***/
#define __FIVector_1_Windows__CNetworking__CHostName_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CNetworking__CHostName_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CNetworking__CHostName_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CNetworking__CHostName_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CNetworking__CHostName_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CNetworking__CHostName_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CNetworking__CHostName_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CNetworking__CHostName_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CNetworking__CHostName_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CNetworking__CHostName_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CNetworking__CHostName_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CNetworking__CHostName_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_QueryInterface(__FIVector_1_Windows__CNetworking__CHostName* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CNetworking__CHostName_AddRef(__FIVector_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CNetworking__CHostName_Release(__FIVector_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_GetIids(__FIVector_1_Windows__CNetworking__CHostName* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_GetRuntimeClassName(__FIVector_1_Windows__CNetworking__CHostName* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_GetTrustLevel(__FIVector_1_Windows__CNetworking__CHostName* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Networking::HostName* > methods ***/
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_GetAt(__FIVector_1_Windows__CNetworking__CHostName* This,UINT32 index,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_get_Size(__FIVector_1_Windows__CNetworking__CHostName* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_GetView(__FIVector_1_Windows__CNetworking__CHostName* This,__FIVectorView_1_Windows__CNetworking__CHostName **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_IndexOf(__FIVector_1_Windows__CNetworking__CHostName* This,__x_ABI_CWindows_CNetworking_CIHostName *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_SetAt(__FIVector_1_Windows__CNetworking__CHostName* This,UINT32 index,__x_ABI_CWindows_CNetworking_CIHostName *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_InsertAt(__FIVector_1_Windows__CNetworking__CHostName* This,UINT32 index,__x_ABI_CWindows_CNetworking_CIHostName *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_RemoveAt(__FIVector_1_Windows__CNetworking__CHostName* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_Append(__FIVector_1_Windows__CNetworking__CHostName* This,__x_ABI_CWindows_CNetworking_CIHostName *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_RemoveAtEnd(__FIVector_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_Clear(__FIVector_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_GetMany(__FIVector_1_Windows__CNetworking__CHostName* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CIHostName **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CNetworking__CHostName_ReplaceAll(__FIVector_1_Windows__CNetworking__CHostName* This,UINT32 count,__x_ABI_CWindows_CNetworking_CIHostName **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_HostName IID___FIVector_1_Windows__CNetworking__CHostName
#define IVector_HostNameVtbl __FIVector_1_Windows__CNetworking__CHostNameVtbl
#define IVector_HostName __FIVector_1_Windows__CNetworking__CHostName
#define IVector_HostName_QueryInterface __FIVector_1_Windows__CNetworking__CHostName_QueryInterface
#define IVector_HostName_AddRef __FIVector_1_Windows__CNetworking__CHostName_AddRef
#define IVector_HostName_Release __FIVector_1_Windows__CNetworking__CHostName_Release
#define IVector_HostName_GetIids __FIVector_1_Windows__CNetworking__CHostName_GetIids
#define IVector_HostName_GetRuntimeClassName __FIVector_1_Windows__CNetworking__CHostName_GetRuntimeClassName
#define IVector_HostName_GetTrustLevel __FIVector_1_Windows__CNetworking__CHostName_GetTrustLevel
#define IVector_HostName_GetAt __FIVector_1_Windows__CNetworking__CHostName_GetAt
#define IVector_HostName_get_Size __FIVector_1_Windows__CNetworking__CHostName_get_Size
#define IVector_HostName_GetView __FIVector_1_Windows__CNetworking__CHostName_GetView
#define IVector_HostName_IndexOf __FIVector_1_Windows__CNetworking__CHostName_IndexOf
#define IVector_HostName_SetAt __FIVector_1_Windows__CNetworking__CHostName_SetAt
#define IVector_HostName_InsertAt __FIVector_1_Windows__CNetworking__CHostName_InsertAt
#define IVector_HostName_RemoveAt __FIVector_1_Windows__CNetworking__CHostName_RemoveAt
#define IVector_HostName_Append __FIVector_1_Windows__CNetworking__CHostName_Append
#define IVector_HostName_RemoveAtEnd __FIVector_1_Windows__CNetworking__CHostName_RemoveAtEnd
#define IVector_HostName_Clear __FIVector_1_Windows__CNetworking__CHostName_Clear
#define IVector_HostName_GetMany __FIVector_1_Windows__CNetworking__CHostName_GetMany
#define IVector_HostName_ReplaceAll __FIVector_1_Windows__CNetworking__CHostName_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair, 0x20d6faab, 0x3b8e, 0x5a1f, 0x83,0x97, 0xb0,0x1c,0xb2,0x19,0xa1,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("20d6faab-3b8e-5a1f-8397-b01cb219a18d")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair, 0x20d6faab, 0x3b8e, 0x5a1f, 0x83,0x97, 0xb0,0x1c,0xb2,0x19,0xa1,0x8d)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_EndpointPair IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IAsyncOperationCompletedHandler_IVectorView_EndpointPairVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl
#define IAsyncOperationCompletedHandler_IVectorView_EndpointPair __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IAsyncOperationCompletedHandler_IVectorView_EndpointPair_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_EndpointPair_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_EndpointPair_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Release
#define IAsyncOperationCompletedHandler_IVectorView_EndpointPair_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair, 0xafc2ff8e, 0xe393, 0x566a, 0x89,0xc4, 0xd0,0x43,0xe9,0x40,0x05,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("afc2ff8e-e393-566a-89c4-d043e940050d")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair, 0xafc2ff8e, 0xe393, 0x566a, 0x89,0xc4, 0xd0,0x43,0xe9,0x40,0x05,0x0d)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        __FIVectorView_1_Windows__CNetworking__CEndpointPair **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CEndpointPair **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair* This,__FIVectorView_1_Windows__CNetworking__CEndpointPair **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_EndpointPair IID___FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IAsyncOperation_IVectorView_EndpointPairVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl
#define IAsyncOperation_IVectorView_EndpointPair __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IAsyncOperation_IVectorView_EndpointPair_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface
#define IAsyncOperation_IVectorView_EndpointPair_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef
#define IAsyncOperation_IVectorView_EndpointPair_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_Release
#define IAsyncOperation_IVectorView_EndpointPair_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids
#define IAsyncOperation_IVectorView_EndpointPair_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName
#define IAsyncOperation_IVectorView_EndpointPair_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel
#define IAsyncOperation_IVectorView_EndpointPair_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_put_Completed
#define IAsyncOperation_IVectorView_EndpointPair_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Completed
#define IAsyncOperation_IVectorView_EndpointPair_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_networking_h__ */
