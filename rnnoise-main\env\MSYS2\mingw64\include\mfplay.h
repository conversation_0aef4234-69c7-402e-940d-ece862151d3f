/*** Autogenerated by WIDL 10.12 from include/mfplay.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mfplay_h__
#define __mfplay_h__

/* Forward declarations */

#ifndef __IMFPMediaPlayer_FWD_DEFINED__
#define __IMFPMediaPlayer_FWD_DEFINED__
typedef interface IMFPMediaPlayer IMFPMediaPlayer;
#ifdef __cplusplus
interface IMFPMediaPlayer;
#endif /* __cplusplus */
#endif

#ifndef __IMFPMediaItem_FWD_DEFINED__
#define __IMFPMediaItem_FWD_DEFINED__
typedef interface IMFPMediaItem IMFPMediaItem;
#ifdef __cplusplus
interface IMFPMediaItem;
#endif /* __cplusplus */
#endif

#ifndef __IMFPMediaPlayerCallback_FWD_DEFINED__
#define __IMFPMediaPlayerCallback_FWD_DEFINED__
typedef interface IMFPMediaPlayerCallback IMFPMediaPlayerCallback;
#ifdef __cplusplus
interface IMFPMediaPlayerCallback;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <propsys.h>
#include <mfidl.h>
#include <evr.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#if (WINVER >= _WIN32_WINNT_WIN7)
#ifndef __IMFPMediaPlayer_FWD_DEFINED__
#define __IMFPMediaPlayer_FWD_DEFINED__
typedef interface IMFPMediaPlayer IMFPMediaPlayer;
#ifdef __cplusplus
interface IMFPMediaPlayer;
#endif /* __cplusplus */
#endif

#ifndef __IMFPMediaItem_FWD_DEFINED__
#define __IMFPMediaItem_FWD_DEFINED__
typedef interface IMFPMediaItem IMFPMediaItem;
#ifdef __cplusplus
interface IMFPMediaItem;
#endif /* __cplusplus */
#endif

#ifndef __IMFPMediaPlayerCallback_FWD_DEFINED__
#define __IMFPMediaPlayerCallback_FWD_DEFINED__
typedef interface IMFPMediaPlayerCallback IMFPMediaPlayerCallback;
#ifdef __cplusplus
interface IMFPMediaPlayerCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEvent_FWD_DEFINED__
#define __IMFMediaEvent_FWD_DEFINED__
typedef interface IMFMediaEvent IMFMediaEvent;
#ifdef __cplusplus
interface IMFMediaEvent;
#endif /* __cplusplus */
#endif

#ifndef __IMFNetCredential_FWD_DEFINED__
#define __IMFNetCredential_FWD_DEFINED__
typedef interface IMFNetCredential IMFNetCredential;
#ifdef __cplusplus
interface IMFNetCredential;
#endif /* __cplusplus */
#endif


interface IMFMediaEvent;
interface IMFNetCredential;

typedef UINT32 MFP_CREATION_OPTIONS;

typedef enum _MFP_CREATION_OPTIONS {
    MFP_OPTION_NONE = 0x0,
    MFP_OPTION_FREE_THREADED_CALLBACK = 0x1,
    MFP_OPTION_NO_MMCSS = 0x2,
    MFP_OPTION_NO_REMOTE_DESKTOP_OPTIMIZATION = 0x4
} _MFP_CREATION_OPTIONS;

typedef enum MFP_MEDIAPLAYER_STATE {
    MFP_MEDIAPLAYER_STATE_EMPTY = 0x0,
    MFP_MEDIAPLAYER_STATE_STOPPED = 0x1,
    MFP_MEDIAPLAYER_STATE_PLAYING = 0x2,
    MFP_MEDIAPLAYER_STATE_PAUSED = 0x3,
    MFP_MEDIAPLAYER_STATE_SHUTDOWN = 0x4
} MFP_MEDIAPLAYER_STATE;

typedef UINT32 MFP_MEDIAITEM_CHARACTERISTICS;

typedef enum _MFP_MEDIAITEM_CHARACTERISTICS {
    MFP_MEDIAITEM_IS_LIVE = 0x1,
    MFP_MEDIAITEM_CAN_SEEK = 0x2,
    MFP_MEDIAITEM_CAN_PAUSE = 0x4,
    MFP_MEDIAITEM_HAS_SLOW_SEEK = 0x8
} _MFP_MEDIAITEM_CHARACTERISTICS;

typedef UINT32 MFP_CREDENTIAL_FLAGS;

typedef enum _MFP_CREDENTIAL_FLAGS {
    MFP_CREDENTIAL_PROMPT = 0x1,
    MFP_CREDENTIAL_SAVE = 0x2,
    MFP_CREDENTIAL_DO_NOT_CACHE = 0x4,
    MFP_CREDENTIAL_CLEAR_TEXT = 0x8,
    MFP_CREDENTIAL_PROXY = 0x10,
    MFP_CREDENTIAL_LOGGED_ON_USER = 0x20
} _MFP_CREDENTIAL_FLAGS;
STDAPI MFPCreateMediaPlayer(LPCWSTR pwszURL, BOOL fStartPlayback, MFP_CREATION_OPTIONS creationOptions, IMFPMediaPlayerCallback * pCallback, HWND hWnd, IMFPMediaPlayer ** ppMediaPlayer);

/*****************************************************************************
 * IMFPMediaPlayer interface
 */
#ifndef __IMFPMediaPlayer_INTERFACE_DEFINED__
#define __IMFPMediaPlayer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPMediaPlayer, 0xa714590a, 0x58af, 0x430a, 0x85,0xbf, 0x44,0xf5,0xec,0x83,0x8d,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a714590a-58af-430a-85bf-44f5ec838d85")
IMFPMediaPlayer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Play(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE FrameStep(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPosition(
        REFGUID guidPositionType,
        const PROPVARIANT *pvPositionValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPosition(
        REFGUID guidPositionType,
        PROPVARIANT *pvPositionValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration(
        REFGUID guidPositionType,
        PROPVARIANT *pvDurationValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRate(
        float flRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRate(
        float *pflRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedRates(
        WINBOOL fForwardDirection,
        float *pflSlowestRate,
        float *pflFastestRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        MFP_MEDIAPLAYER_STATE *peState) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMediaItemFromURL(
        LPCWSTR pwszURL,
        WINBOOL fSync,
        DWORD_PTR dwUserData,
        IMFPMediaItem **ppMediaItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMediaItemFromObject(
        IUnknown *pIUnknownObj,
        WINBOOL fSync,
        DWORD_PTR dwUserData,
        IMFPMediaItem **ppMediaItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaItem(
        IMFPMediaItem *pIMFPMediaItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearMediaItem(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaItem(
        IMFPMediaItem **ppIMFPMediaItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVolume(
        float *pflVolume) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVolume(
        float flVolume) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBalance(
        float *pflBalance) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBalance(
        float flBalance) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMute(
        WINBOOL *pfMute) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMute(
        WINBOOL fMute) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNativeVideoSize(
        SIZE *pszVideo,
        SIZE *pszARVideo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIdealVideoSize(
        SIZE *pszMin,
        SIZE *pszMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVideoSourceRect(
        const MFVideoNormalizedRect *pnrcSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoSourceRect(
        MFVideoNormalizedRect *pnrcSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAspectRatioMode(
        DWORD dwAspectRatioMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAspectRatioMode(
        DWORD *pdwAspectRatioMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoWindow(
        HWND *phwndVideo) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateVideo(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBorderColor(
        COLORREF Clr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBorderColor(
        COLORREF *pClr) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertEffect(
        IUnknown *pEffect,
        WINBOOL fOptional) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveEffect(
        IUnknown *pEffect) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllEffects(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPMediaPlayer, 0xa714590a, 0x58af, 0x430a, 0x85,0xbf, 0x44,0xf5,0xec,0x83,0x8d,0x85)
#endif
#else
typedef struct IMFPMediaPlayerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPMediaPlayer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPMediaPlayer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPMediaPlayer *This);

    /*** IMFPMediaPlayer methods ***/
    HRESULT (STDMETHODCALLTYPE *Play)(
        IMFPMediaPlayer *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFPMediaPlayer *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IMFPMediaPlayer *This);

    HRESULT (STDMETHODCALLTYPE *FrameStep)(
        IMFPMediaPlayer *This);

    HRESULT (STDMETHODCALLTYPE *SetPosition)(
        IMFPMediaPlayer *This,
        REFGUID guidPositionType,
        const PROPVARIANT *pvPositionValue);

    HRESULT (STDMETHODCALLTYPE *GetPosition)(
        IMFPMediaPlayer *This,
        REFGUID guidPositionType,
        PROPVARIANT *pvPositionValue);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IMFPMediaPlayer *This,
        REFGUID guidPositionType,
        PROPVARIANT *pvDurationValue);

    HRESULT (STDMETHODCALLTYPE *SetRate)(
        IMFPMediaPlayer *This,
        float flRate);

    HRESULT (STDMETHODCALLTYPE *GetRate)(
        IMFPMediaPlayer *This,
        float *pflRate);

    HRESULT (STDMETHODCALLTYPE *GetSupportedRates)(
        IMFPMediaPlayer *This,
        WINBOOL fForwardDirection,
        float *pflSlowestRate,
        float *pflFastestRate);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IMFPMediaPlayer *This,
        MFP_MEDIAPLAYER_STATE *peState);

    HRESULT (STDMETHODCALLTYPE *CreateMediaItemFromURL)(
        IMFPMediaPlayer *This,
        LPCWSTR pwszURL,
        WINBOOL fSync,
        DWORD_PTR dwUserData,
        IMFPMediaItem **ppMediaItem);

    HRESULT (STDMETHODCALLTYPE *CreateMediaItemFromObject)(
        IMFPMediaPlayer *This,
        IUnknown *pIUnknownObj,
        WINBOOL fSync,
        DWORD_PTR dwUserData,
        IMFPMediaItem **ppMediaItem);

    HRESULT (STDMETHODCALLTYPE *SetMediaItem)(
        IMFPMediaPlayer *This,
        IMFPMediaItem *pIMFPMediaItem);

    HRESULT (STDMETHODCALLTYPE *ClearMediaItem)(
        IMFPMediaPlayer *This);

    HRESULT (STDMETHODCALLTYPE *GetMediaItem)(
        IMFPMediaPlayer *This,
        IMFPMediaItem **ppIMFPMediaItem);

    HRESULT (STDMETHODCALLTYPE *GetVolume)(
        IMFPMediaPlayer *This,
        float *pflVolume);

    HRESULT (STDMETHODCALLTYPE *SetVolume)(
        IMFPMediaPlayer *This,
        float flVolume);

    HRESULT (STDMETHODCALLTYPE *GetBalance)(
        IMFPMediaPlayer *This,
        float *pflBalance);

    HRESULT (STDMETHODCALLTYPE *SetBalance)(
        IMFPMediaPlayer *This,
        float flBalance);

    HRESULT (STDMETHODCALLTYPE *GetMute)(
        IMFPMediaPlayer *This,
        WINBOOL *pfMute);

    HRESULT (STDMETHODCALLTYPE *SetMute)(
        IMFPMediaPlayer *This,
        WINBOOL fMute);

    HRESULT (STDMETHODCALLTYPE *GetNativeVideoSize)(
        IMFPMediaPlayer *This,
        SIZE *pszVideo,
        SIZE *pszARVideo);

    HRESULT (STDMETHODCALLTYPE *GetIdealVideoSize)(
        IMFPMediaPlayer *This,
        SIZE *pszMin,
        SIZE *pszMax);

    HRESULT (STDMETHODCALLTYPE *SetVideoSourceRect)(
        IMFPMediaPlayer *This,
        const MFVideoNormalizedRect *pnrcSource);

    HRESULT (STDMETHODCALLTYPE *GetVideoSourceRect)(
        IMFPMediaPlayer *This,
        MFVideoNormalizedRect *pnrcSource);

    HRESULT (STDMETHODCALLTYPE *SetAspectRatioMode)(
        IMFPMediaPlayer *This,
        DWORD dwAspectRatioMode);

    HRESULT (STDMETHODCALLTYPE *GetAspectRatioMode)(
        IMFPMediaPlayer *This,
        DWORD *pdwAspectRatioMode);

    HRESULT (STDMETHODCALLTYPE *GetVideoWindow)(
        IMFPMediaPlayer *This,
        HWND *phwndVideo);

    HRESULT (STDMETHODCALLTYPE *UpdateVideo)(
        IMFPMediaPlayer *This);

    HRESULT (STDMETHODCALLTYPE *SetBorderColor)(
        IMFPMediaPlayer *This,
        COLORREF Clr);

    HRESULT (STDMETHODCALLTYPE *GetBorderColor)(
        IMFPMediaPlayer *This,
        COLORREF *pClr);

    HRESULT (STDMETHODCALLTYPE *InsertEffect)(
        IMFPMediaPlayer *This,
        IUnknown *pEffect,
        WINBOOL fOptional);

    HRESULT (STDMETHODCALLTYPE *RemoveEffect)(
        IMFPMediaPlayer *This,
        IUnknown *pEffect);

    HRESULT (STDMETHODCALLTYPE *RemoveAllEffects)(
        IMFPMediaPlayer *This);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFPMediaPlayer *This);

    END_INTERFACE
} IMFPMediaPlayerVtbl;

interface IMFPMediaPlayer {
    CONST_VTBL IMFPMediaPlayerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPMediaPlayer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPMediaPlayer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPMediaPlayer_Release(This) (This)->lpVtbl->Release(This)
/*** IMFPMediaPlayer methods ***/
#define IMFPMediaPlayer_Play(This) (This)->lpVtbl->Play(This)
#define IMFPMediaPlayer_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFPMediaPlayer_Stop(This) (This)->lpVtbl->Stop(This)
#define IMFPMediaPlayer_FrameStep(This) (This)->lpVtbl->FrameStep(This)
#define IMFPMediaPlayer_SetPosition(This,guidPositionType,pvPositionValue) (This)->lpVtbl->SetPosition(This,guidPositionType,pvPositionValue)
#define IMFPMediaPlayer_GetPosition(This,guidPositionType,pvPositionValue) (This)->lpVtbl->GetPosition(This,guidPositionType,pvPositionValue)
#define IMFPMediaPlayer_GetDuration(This,guidPositionType,pvDurationValue) (This)->lpVtbl->GetDuration(This,guidPositionType,pvDurationValue)
#define IMFPMediaPlayer_SetRate(This,flRate) (This)->lpVtbl->SetRate(This,flRate)
#define IMFPMediaPlayer_GetRate(This,pflRate) (This)->lpVtbl->GetRate(This,pflRate)
#define IMFPMediaPlayer_GetSupportedRates(This,fForwardDirection,pflSlowestRate,pflFastestRate) (This)->lpVtbl->GetSupportedRates(This,fForwardDirection,pflSlowestRate,pflFastestRate)
#define IMFPMediaPlayer_GetState(This,peState) (This)->lpVtbl->GetState(This,peState)
#define IMFPMediaPlayer_CreateMediaItemFromURL(This,pwszURL,fSync,dwUserData,ppMediaItem) (This)->lpVtbl->CreateMediaItemFromURL(This,pwszURL,fSync,dwUserData,ppMediaItem)
#define IMFPMediaPlayer_CreateMediaItemFromObject(This,pIUnknownObj,fSync,dwUserData,ppMediaItem) (This)->lpVtbl->CreateMediaItemFromObject(This,pIUnknownObj,fSync,dwUserData,ppMediaItem)
#define IMFPMediaPlayer_SetMediaItem(This,pIMFPMediaItem) (This)->lpVtbl->SetMediaItem(This,pIMFPMediaItem)
#define IMFPMediaPlayer_ClearMediaItem(This) (This)->lpVtbl->ClearMediaItem(This)
#define IMFPMediaPlayer_GetMediaItem(This,ppIMFPMediaItem) (This)->lpVtbl->GetMediaItem(This,ppIMFPMediaItem)
#define IMFPMediaPlayer_GetVolume(This,pflVolume) (This)->lpVtbl->GetVolume(This,pflVolume)
#define IMFPMediaPlayer_SetVolume(This,flVolume) (This)->lpVtbl->SetVolume(This,flVolume)
#define IMFPMediaPlayer_GetBalance(This,pflBalance) (This)->lpVtbl->GetBalance(This,pflBalance)
#define IMFPMediaPlayer_SetBalance(This,flBalance) (This)->lpVtbl->SetBalance(This,flBalance)
#define IMFPMediaPlayer_GetMute(This,pfMute) (This)->lpVtbl->GetMute(This,pfMute)
#define IMFPMediaPlayer_SetMute(This,fMute) (This)->lpVtbl->SetMute(This,fMute)
#define IMFPMediaPlayer_GetNativeVideoSize(This,pszVideo,pszARVideo) (This)->lpVtbl->GetNativeVideoSize(This,pszVideo,pszARVideo)
#define IMFPMediaPlayer_GetIdealVideoSize(This,pszMin,pszMax) (This)->lpVtbl->GetIdealVideoSize(This,pszMin,pszMax)
#define IMFPMediaPlayer_SetVideoSourceRect(This,pnrcSource) (This)->lpVtbl->SetVideoSourceRect(This,pnrcSource)
#define IMFPMediaPlayer_GetVideoSourceRect(This,pnrcSource) (This)->lpVtbl->GetVideoSourceRect(This,pnrcSource)
#define IMFPMediaPlayer_SetAspectRatioMode(This,dwAspectRatioMode) (This)->lpVtbl->SetAspectRatioMode(This,dwAspectRatioMode)
#define IMFPMediaPlayer_GetAspectRatioMode(This,pdwAspectRatioMode) (This)->lpVtbl->GetAspectRatioMode(This,pdwAspectRatioMode)
#define IMFPMediaPlayer_GetVideoWindow(This,phwndVideo) (This)->lpVtbl->GetVideoWindow(This,phwndVideo)
#define IMFPMediaPlayer_UpdateVideo(This) (This)->lpVtbl->UpdateVideo(This)
#define IMFPMediaPlayer_SetBorderColor(This,Clr) (This)->lpVtbl->SetBorderColor(This,Clr)
#define IMFPMediaPlayer_GetBorderColor(This,pClr) (This)->lpVtbl->GetBorderColor(This,pClr)
#define IMFPMediaPlayer_InsertEffect(This,pEffect,fOptional) (This)->lpVtbl->InsertEffect(This,pEffect,fOptional)
#define IMFPMediaPlayer_RemoveEffect(This,pEffect) (This)->lpVtbl->RemoveEffect(This,pEffect)
#define IMFPMediaPlayer_RemoveAllEffects(This) (This)->lpVtbl->RemoveAllEffects(This)
#define IMFPMediaPlayer_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPMediaPlayer_QueryInterface(IMFPMediaPlayer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPMediaPlayer_AddRef(IMFPMediaPlayer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPMediaPlayer_Release(IMFPMediaPlayer* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFPMediaPlayer methods ***/
static inline HRESULT IMFPMediaPlayer_Play(IMFPMediaPlayer* This) {
    return This->lpVtbl->Play(This);
}
static inline HRESULT IMFPMediaPlayer_Pause(IMFPMediaPlayer* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IMFPMediaPlayer_Stop(IMFPMediaPlayer* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IMFPMediaPlayer_FrameStep(IMFPMediaPlayer* This) {
    return This->lpVtbl->FrameStep(This);
}
static inline HRESULT IMFPMediaPlayer_SetPosition(IMFPMediaPlayer* This,REFGUID guidPositionType,const PROPVARIANT *pvPositionValue) {
    return This->lpVtbl->SetPosition(This,guidPositionType,pvPositionValue);
}
static inline HRESULT IMFPMediaPlayer_GetPosition(IMFPMediaPlayer* This,REFGUID guidPositionType,PROPVARIANT *pvPositionValue) {
    return This->lpVtbl->GetPosition(This,guidPositionType,pvPositionValue);
}
static inline HRESULT IMFPMediaPlayer_GetDuration(IMFPMediaPlayer* This,REFGUID guidPositionType,PROPVARIANT *pvDurationValue) {
    return This->lpVtbl->GetDuration(This,guidPositionType,pvDurationValue);
}
static inline HRESULT IMFPMediaPlayer_SetRate(IMFPMediaPlayer* This,float flRate) {
    return This->lpVtbl->SetRate(This,flRate);
}
static inline HRESULT IMFPMediaPlayer_GetRate(IMFPMediaPlayer* This,float *pflRate) {
    return This->lpVtbl->GetRate(This,pflRate);
}
static inline HRESULT IMFPMediaPlayer_GetSupportedRates(IMFPMediaPlayer* This,WINBOOL fForwardDirection,float *pflSlowestRate,float *pflFastestRate) {
    return This->lpVtbl->GetSupportedRates(This,fForwardDirection,pflSlowestRate,pflFastestRate);
}
static inline HRESULT IMFPMediaPlayer_GetState(IMFPMediaPlayer* This,MFP_MEDIAPLAYER_STATE *peState) {
    return This->lpVtbl->GetState(This,peState);
}
static inline HRESULT IMFPMediaPlayer_CreateMediaItemFromURL(IMFPMediaPlayer* This,LPCWSTR pwszURL,WINBOOL fSync,DWORD_PTR dwUserData,IMFPMediaItem **ppMediaItem) {
    return This->lpVtbl->CreateMediaItemFromURL(This,pwszURL,fSync,dwUserData,ppMediaItem);
}
static inline HRESULT IMFPMediaPlayer_CreateMediaItemFromObject(IMFPMediaPlayer* This,IUnknown *pIUnknownObj,WINBOOL fSync,DWORD_PTR dwUserData,IMFPMediaItem **ppMediaItem) {
    return This->lpVtbl->CreateMediaItemFromObject(This,pIUnknownObj,fSync,dwUserData,ppMediaItem);
}
static inline HRESULT IMFPMediaPlayer_SetMediaItem(IMFPMediaPlayer* This,IMFPMediaItem *pIMFPMediaItem) {
    return This->lpVtbl->SetMediaItem(This,pIMFPMediaItem);
}
static inline HRESULT IMFPMediaPlayer_ClearMediaItem(IMFPMediaPlayer* This) {
    return This->lpVtbl->ClearMediaItem(This);
}
static inline HRESULT IMFPMediaPlayer_GetMediaItem(IMFPMediaPlayer* This,IMFPMediaItem **ppIMFPMediaItem) {
    return This->lpVtbl->GetMediaItem(This,ppIMFPMediaItem);
}
static inline HRESULT IMFPMediaPlayer_GetVolume(IMFPMediaPlayer* This,float *pflVolume) {
    return This->lpVtbl->GetVolume(This,pflVolume);
}
static inline HRESULT IMFPMediaPlayer_SetVolume(IMFPMediaPlayer* This,float flVolume) {
    return This->lpVtbl->SetVolume(This,flVolume);
}
static inline HRESULT IMFPMediaPlayer_GetBalance(IMFPMediaPlayer* This,float *pflBalance) {
    return This->lpVtbl->GetBalance(This,pflBalance);
}
static inline HRESULT IMFPMediaPlayer_SetBalance(IMFPMediaPlayer* This,float flBalance) {
    return This->lpVtbl->SetBalance(This,flBalance);
}
static inline HRESULT IMFPMediaPlayer_GetMute(IMFPMediaPlayer* This,WINBOOL *pfMute) {
    return This->lpVtbl->GetMute(This,pfMute);
}
static inline HRESULT IMFPMediaPlayer_SetMute(IMFPMediaPlayer* This,WINBOOL fMute) {
    return This->lpVtbl->SetMute(This,fMute);
}
static inline HRESULT IMFPMediaPlayer_GetNativeVideoSize(IMFPMediaPlayer* This,SIZE *pszVideo,SIZE *pszARVideo) {
    return This->lpVtbl->GetNativeVideoSize(This,pszVideo,pszARVideo);
}
static inline HRESULT IMFPMediaPlayer_GetIdealVideoSize(IMFPMediaPlayer* This,SIZE *pszMin,SIZE *pszMax) {
    return This->lpVtbl->GetIdealVideoSize(This,pszMin,pszMax);
}
static inline HRESULT IMFPMediaPlayer_SetVideoSourceRect(IMFPMediaPlayer* This,const MFVideoNormalizedRect *pnrcSource) {
    return This->lpVtbl->SetVideoSourceRect(This,pnrcSource);
}
static inline HRESULT IMFPMediaPlayer_GetVideoSourceRect(IMFPMediaPlayer* This,MFVideoNormalizedRect *pnrcSource) {
    return This->lpVtbl->GetVideoSourceRect(This,pnrcSource);
}
static inline HRESULT IMFPMediaPlayer_SetAspectRatioMode(IMFPMediaPlayer* This,DWORD dwAspectRatioMode) {
    return This->lpVtbl->SetAspectRatioMode(This,dwAspectRatioMode);
}
static inline HRESULT IMFPMediaPlayer_GetAspectRatioMode(IMFPMediaPlayer* This,DWORD *pdwAspectRatioMode) {
    return This->lpVtbl->GetAspectRatioMode(This,pdwAspectRatioMode);
}
static inline HRESULT IMFPMediaPlayer_GetVideoWindow(IMFPMediaPlayer* This,HWND *phwndVideo) {
    return This->lpVtbl->GetVideoWindow(This,phwndVideo);
}
static inline HRESULT IMFPMediaPlayer_UpdateVideo(IMFPMediaPlayer* This) {
    return This->lpVtbl->UpdateVideo(This);
}
static inline HRESULT IMFPMediaPlayer_SetBorderColor(IMFPMediaPlayer* This,COLORREF Clr) {
    return This->lpVtbl->SetBorderColor(This,Clr);
}
static inline HRESULT IMFPMediaPlayer_GetBorderColor(IMFPMediaPlayer* This,COLORREF *pClr) {
    return This->lpVtbl->GetBorderColor(This,pClr);
}
static inline HRESULT IMFPMediaPlayer_InsertEffect(IMFPMediaPlayer* This,IUnknown *pEffect,WINBOOL fOptional) {
    return This->lpVtbl->InsertEffect(This,pEffect,fOptional);
}
static inline HRESULT IMFPMediaPlayer_RemoveEffect(IMFPMediaPlayer* This,IUnknown *pEffect) {
    return This->lpVtbl->RemoveEffect(This,pEffect);
}
static inline HRESULT IMFPMediaPlayer_RemoveAllEffects(IMFPMediaPlayer* This) {
    return This->lpVtbl->RemoveAllEffects(This);
}
static inline HRESULT IMFPMediaPlayer_Shutdown(IMFPMediaPlayer* This) {
    return This->lpVtbl->Shutdown(This);
}
#endif
#endif

#endif


#endif  /* __IMFPMediaPlayer_INTERFACE_DEFINED__ */

EXTERN_GUID( MFP_POSITIONTYPE_100NS, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 );

/*****************************************************************************
 * IMFPMediaItem interface
 */
#ifndef __IMFPMediaItem_INTERFACE_DEFINED__
#define __IMFPMediaItem_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPMediaItem, 0x90eb3e6b, 0xecbf, 0x45cc, 0xb1,0xda, 0xc6,0xfe,0x3e,0xa7,0x0d,0x57);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("90eb3e6b-ecbf-45cc-b1da-c6fe3ea70d57")
IMFPMediaItem : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMediaPlayer(
        IMFPMediaPlayer **ppMediaPlayer) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetURL(
        LPWSTR *ppwszURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObject(
        IUnknown **ppIUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserData(
        DWORD_PTR *pdwUserData) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUserData(
        DWORD_PTR dwUserData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStartStopPosition(
        GUID *pguidStartPositionType,
        PROPVARIANT *pvStartValue,
        GUID *pguidStopPositionType,
        PROPVARIANT *pvStopValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStartStopPosition(
        const GUID *pguidStartPositionType,
        const PROPVARIANT *pvStartValue,
        const GUID *pguidStopPositionType,
        const PROPVARIANT *pvStopValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasVideo(
        WINBOOL *pfHasVideo,
        WINBOOL *pfSelected) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasAudio(
        WINBOOL *pfHasAudio,
        WINBOOL *pfSelected) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsProtected(
        WINBOOL *pfProtected) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration(
        REFGUID guidPositionType,
        PROPVARIANT *pvDurationValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberOfStreams(
        DWORD *pdwStreamCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamSelection(
        DWORD dwStreamIndex,
        WINBOOL *pfEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamSelection(
        DWORD dwStreamIndex,
        WINBOOL fEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamAttribute(
        DWORD dwStreamIndex,
        REFGUID guidMFAttribute,
        PROPVARIANT *pvValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPresentationAttribute(
        REFGUID guidMFAttribute,
        PROPVARIANT *pvValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCharacteristics(
        MFP_MEDIAITEM_CHARACTERISTICS *pCharacteristics) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamSink(
        DWORD dwStreamIndex,
        IUnknown *pMediaSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadata(
        IPropertyStore **ppMetadataStore) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPMediaItem, 0x90eb3e6b, 0xecbf, 0x45cc, 0xb1,0xda, 0xc6,0xfe,0x3e,0xa7,0x0d,0x57)
#endif
#else
typedef struct IMFPMediaItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPMediaItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPMediaItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPMediaItem *This);

    /*** IMFPMediaItem methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMediaPlayer)(
        IMFPMediaItem *This,
        IMFPMediaPlayer **ppMediaPlayer);

    HRESULT (STDMETHODCALLTYPE *GetURL)(
        IMFPMediaItem *This,
        LPWSTR *ppwszURL);

    HRESULT (STDMETHODCALLTYPE *GetObject)(
        IMFPMediaItem *This,
        IUnknown **ppIUnknown);

    HRESULT (STDMETHODCALLTYPE *GetUserData)(
        IMFPMediaItem *This,
        DWORD_PTR *pdwUserData);

    HRESULT (STDMETHODCALLTYPE *SetUserData)(
        IMFPMediaItem *This,
        DWORD_PTR dwUserData);

    HRESULT (STDMETHODCALLTYPE *GetStartStopPosition)(
        IMFPMediaItem *This,
        GUID *pguidStartPositionType,
        PROPVARIANT *pvStartValue,
        GUID *pguidStopPositionType,
        PROPVARIANT *pvStopValue);

    HRESULT (STDMETHODCALLTYPE *SetStartStopPosition)(
        IMFPMediaItem *This,
        const GUID *pguidStartPositionType,
        const PROPVARIANT *pvStartValue,
        const GUID *pguidStopPositionType,
        const PROPVARIANT *pvStopValue);

    HRESULT (STDMETHODCALLTYPE *HasVideo)(
        IMFPMediaItem *This,
        WINBOOL *pfHasVideo,
        WINBOOL *pfSelected);

    HRESULT (STDMETHODCALLTYPE *HasAudio)(
        IMFPMediaItem *This,
        WINBOOL *pfHasAudio,
        WINBOOL *pfSelected);

    HRESULT (STDMETHODCALLTYPE *IsProtected)(
        IMFPMediaItem *This,
        WINBOOL *pfProtected);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IMFPMediaItem *This,
        REFGUID guidPositionType,
        PROPVARIANT *pvDurationValue);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfStreams)(
        IMFPMediaItem *This,
        DWORD *pdwStreamCount);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelection)(
        IMFPMediaItem *This,
        DWORD dwStreamIndex,
        WINBOOL *pfEnabled);

    HRESULT (STDMETHODCALLTYPE *SetStreamSelection)(
        IMFPMediaItem *This,
        DWORD dwStreamIndex,
        WINBOOL fEnabled);

    HRESULT (STDMETHODCALLTYPE *GetStreamAttribute)(
        IMFPMediaItem *This,
        DWORD dwStreamIndex,
        REFGUID guidMFAttribute,
        PROPVARIANT *pvValue);

    HRESULT (STDMETHODCALLTYPE *GetPresentationAttribute)(
        IMFPMediaItem *This,
        REFGUID guidMFAttribute,
        PROPVARIANT *pvValue);

    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        IMFPMediaItem *This,
        MFP_MEDIAITEM_CHARACTERISTICS *pCharacteristics);

    HRESULT (STDMETHODCALLTYPE *SetStreamSink)(
        IMFPMediaItem *This,
        DWORD dwStreamIndex,
        IUnknown *pMediaSink);

    HRESULT (STDMETHODCALLTYPE *GetMetadata)(
        IMFPMediaItem *This,
        IPropertyStore **ppMetadataStore);

    END_INTERFACE
} IMFPMediaItemVtbl;

interface IMFPMediaItem {
    CONST_VTBL IMFPMediaItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPMediaItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPMediaItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPMediaItem_Release(This) (This)->lpVtbl->Release(This)
/*** IMFPMediaItem methods ***/
#define IMFPMediaItem_GetMediaPlayer(This,ppMediaPlayer) (This)->lpVtbl->GetMediaPlayer(This,ppMediaPlayer)
#define IMFPMediaItem_GetURL(This,ppwszURL) (This)->lpVtbl->GetURL(This,ppwszURL)
#define IMFPMediaItem_GetObject(This,ppIUnknown) (This)->lpVtbl->GetObject(This,ppIUnknown)
#define IMFPMediaItem_GetUserData(This,pdwUserData) (This)->lpVtbl->GetUserData(This,pdwUserData)
#define IMFPMediaItem_SetUserData(This,dwUserData) (This)->lpVtbl->SetUserData(This,dwUserData)
#define IMFPMediaItem_GetStartStopPosition(This,pguidStartPositionType,pvStartValue,pguidStopPositionType,pvStopValue) (This)->lpVtbl->GetStartStopPosition(This,pguidStartPositionType,pvStartValue,pguidStopPositionType,pvStopValue)
#define IMFPMediaItem_SetStartStopPosition(This,pguidStartPositionType,pvStartValue,pguidStopPositionType,pvStopValue) (This)->lpVtbl->SetStartStopPosition(This,pguidStartPositionType,pvStartValue,pguidStopPositionType,pvStopValue)
#define IMFPMediaItem_HasVideo(This,pfHasVideo,pfSelected) (This)->lpVtbl->HasVideo(This,pfHasVideo,pfSelected)
#define IMFPMediaItem_HasAudio(This,pfHasAudio,pfSelected) (This)->lpVtbl->HasAudio(This,pfHasAudio,pfSelected)
#define IMFPMediaItem_IsProtected(This,pfProtected) (This)->lpVtbl->IsProtected(This,pfProtected)
#define IMFPMediaItem_GetDuration(This,guidPositionType,pvDurationValue) (This)->lpVtbl->GetDuration(This,guidPositionType,pvDurationValue)
#define IMFPMediaItem_GetNumberOfStreams(This,pdwStreamCount) (This)->lpVtbl->GetNumberOfStreams(This,pdwStreamCount)
#define IMFPMediaItem_GetStreamSelection(This,dwStreamIndex,pfEnabled) (This)->lpVtbl->GetStreamSelection(This,dwStreamIndex,pfEnabled)
#define IMFPMediaItem_SetStreamSelection(This,dwStreamIndex,fEnabled) (This)->lpVtbl->SetStreamSelection(This,dwStreamIndex,fEnabled)
#define IMFPMediaItem_GetStreamAttribute(This,dwStreamIndex,guidMFAttribute,pvValue) (This)->lpVtbl->GetStreamAttribute(This,dwStreamIndex,guidMFAttribute,pvValue)
#define IMFPMediaItem_GetPresentationAttribute(This,guidMFAttribute,pvValue) (This)->lpVtbl->GetPresentationAttribute(This,guidMFAttribute,pvValue)
#define IMFPMediaItem_GetCharacteristics(This,pCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pCharacteristics)
#define IMFPMediaItem_SetStreamSink(This,dwStreamIndex,pMediaSink) (This)->lpVtbl->SetStreamSink(This,dwStreamIndex,pMediaSink)
#define IMFPMediaItem_GetMetadata(This,ppMetadataStore) (This)->lpVtbl->GetMetadata(This,ppMetadataStore)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPMediaItem_QueryInterface(IMFPMediaItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPMediaItem_AddRef(IMFPMediaItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPMediaItem_Release(IMFPMediaItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFPMediaItem methods ***/
static inline HRESULT IMFPMediaItem_GetMediaPlayer(IMFPMediaItem* This,IMFPMediaPlayer **ppMediaPlayer) {
    return This->lpVtbl->GetMediaPlayer(This,ppMediaPlayer);
}
static inline HRESULT IMFPMediaItem_GetURL(IMFPMediaItem* This,LPWSTR *ppwszURL) {
    return This->lpVtbl->GetURL(This,ppwszURL);
}
static inline HRESULT IMFPMediaItem_GetObject(IMFPMediaItem* This,IUnknown **ppIUnknown) {
    return This->lpVtbl->GetObject(This,ppIUnknown);
}
static inline HRESULT IMFPMediaItem_GetUserData(IMFPMediaItem* This,DWORD_PTR *pdwUserData) {
    return This->lpVtbl->GetUserData(This,pdwUserData);
}
static inline HRESULT IMFPMediaItem_SetUserData(IMFPMediaItem* This,DWORD_PTR dwUserData) {
    return This->lpVtbl->SetUserData(This,dwUserData);
}
static inline HRESULT IMFPMediaItem_GetStartStopPosition(IMFPMediaItem* This,GUID *pguidStartPositionType,PROPVARIANT *pvStartValue,GUID *pguidStopPositionType,PROPVARIANT *pvStopValue) {
    return This->lpVtbl->GetStartStopPosition(This,pguidStartPositionType,pvStartValue,pguidStopPositionType,pvStopValue);
}
static inline HRESULT IMFPMediaItem_SetStartStopPosition(IMFPMediaItem* This,const GUID *pguidStartPositionType,const PROPVARIANT *pvStartValue,const GUID *pguidStopPositionType,const PROPVARIANT *pvStopValue) {
    return This->lpVtbl->SetStartStopPosition(This,pguidStartPositionType,pvStartValue,pguidStopPositionType,pvStopValue);
}
static inline HRESULT IMFPMediaItem_HasVideo(IMFPMediaItem* This,WINBOOL *pfHasVideo,WINBOOL *pfSelected) {
    return This->lpVtbl->HasVideo(This,pfHasVideo,pfSelected);
}
static inline HRESULT IMFPMediaItem_HasAudio(IMFPMediaItem* This,WINBOOL *pfHasAudio,WINBOOL *pfSelected) {
    return This->lpVtbl->HasAudio(This,pfHasAudio,pfSelected);
}
static inline HRESULT IMFPMediaItem_IsProtected(IMFPMediaItem* This,WINBOOL *pfProtected) {
    return This->lpVtbl->IsProtected(This,pfProtected);
}
static inline HRESULT IMFPMediaItem_GetDuration(IMFPMediaItem* This,REFGUID guidPositionType,PROPVARIANT *pvDurationValue) {
    return This->lpVtbl->GetDuration(This,guidPositionType,pvDurationValue);
}
static inline HRESULT IMFPMediaItem_GetNumberOfStreams(IMFPMediaItem* This,DWORD *pdwStreamCount) {
    return This->lpVtbl->GetNumberOfStreams(This,pdwStreamCount);
}
static inline HRESULT IMFPMediaItem_GetStreamSelection(IMFPMediaItem* This,DWORD dwStreamIndex,WINBOOL *pfEnabled) {
    return This->lpVtbl->GetStreamSelection(This,dwStreamIndex,pfEnabled);
}
static inline HRESULT IMFPMediaItem_SetStreamSelection(IMFPMediaItem* This,DWORD dwStreamIndex,WINBOOL fEnabled) {
    return This->lpVtbl->SetStreamSelection(This,dwStreamIndex,fEnabled);
}
static inline HRESULT IMFPMediaItem_GetStreamAttribute(IMFPMediaItem* This,DWORD dwStreamIndex,REFGUID guidMFAttribute,PROPVARIANT *pvValue) {
    return This->lpVtbl->GetStreamAttribute(This,dwStreamIndex,guidMFAttribute,pvValue);
}
static inline HRESULT IMFPMediaItem_GetPresentationAttribute(IMFPMediaItem* This,REFGUID guidMFAttribute,PROPVARIANT *pvValue) {
    return This->lpVtbl->GetPresentationAttribute(This,guidMFAttribute,pvValue);
}
static inline HRESULT IMFPMediaItem_GetCharacteristics(IMFPMediaItem* This,MFP_MEDIAITEM_CHARACTERISTICS *pCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pCharacteristics);
}
static inline HRESULT IMFPMediaItem_SetStreamSink(IMFPMediaItem* This,DWORD dwStreamIndex,IUnknown *pMediaSink) {
    return This->lpVtbl->SetStreamSink(This,dwStreamIndex,pMediaSink);
}
static inline HRESULT IMFPMediaItem_GetMetadata(IMFPMediaItem* This,IPropertyStore **ppMetadataStore) {
    return This->lpVtbl->GetMetadata(This,ppMetadataStore);
}
#endif
#endif

#endif


#endif  /* __IMFPMediaItem_INTERFACE_DEFINED__ */


typedef enum MFP_EVENT_TYPE {
    MFP_EVENT_TYPE_PLAY = 0,
    MFP_EVENT_TYPE_PAUSE = 1,
    MFP_EVENT_TYPE_STOP = 2,
    MFP_EVENT_TYPE_POSITION_SET = 3,
    MFP_EVENT_TYPE_RATE_SET = 4,
    MFP_EVENT_TYPE_MEDIAITEM_CREATED = 5,
    MFP_EVENT_TYPE_MEDIAITEM_SET = 6,
    MFP_EVENT_TYPE_FRAME_STEP = 7,
    MFP_EVENT_TYPE_MEDIAITEM_CLEARED = 8,
    MFP_EVENT_TYPE_MF = 9,
    MFP_EVENT_TYPE_ERROR = 10,
    MFP_EVENT_TYPE_PLAYBACK_ENDED = 11,
    MFP_EVENT_TYPE_ACQUIRE_USER_CREDENTIAL = 12
} MFP_EVENT_TYPE;

typedef struct MFP_EVENT_HEADER {
    MFP_EVENT_TYPE eEventType;
    HRESULT hrEvent;
    IMFPMediaPlayer *pMediaPlayer;
    MFP_MEDIAPLAYER_STATE eState;
    IPropertyStore *pPropertyStore;
} MFP_EVENT_HEADER;

typedef struct MFP_PLAY_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_PLAY_EVENT;

typedef struct MFP_PAUSE_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_PAUSE_EVENT;

typedef struct MFP_STOP_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_STOP_EVENT;

typedef struct MFP_POSITION_SET_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_POSITION_SET_EVENT;

typedef struct MFP_RATE_SET_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
    float flRate;
} MFP_RATE_SET_EVENT;

typedef struct MFP_MEDIAITEM_CREATED_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
    DWORD_PTR dwUserData;
} MFP_MEDIAITEM_CREATED_EVENT;

typedef struct MFP_MEDIAITEM_SET_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_MEDIAITEM_SET_EVENT;

typedef struct MFP_FRAME_STEP_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_FRAME_STEP_EVENT;

typedef struct MFP_MEDIAITEM_CLEARED_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_MEDIAITEM_CLEARED_EVENT;

typedef struct MFP_MF_EVENT {
    MFP_EVENT_HEADER header;
    MediaEventType MFEventType;
    IMFMediaEvent *pMFMediaEvent;
    IMFPMediaItem *pMediaItem;
} MFP_MF_EVENT;

typedef struct MFP_ERROR_EVENT {
    MFP_EVENT_HEADER header;
} MFP_ERROR_EVENT;

typedef struct MFP_PLAYBACK_ENDED_EVENT {
    MFP_EVENT_HEADER header;
    IMFPMediaItem *pMediaItem;
} MFP_PLAYBACK_ENDED_EVENT;

typedef struct MFP_ACQUIRE_USER_CREDENTIAL_EVENT {
    MFP_EVENT_HEADER header;
    DWORD_PTR dwUserData;
    WINBOOL fProceedWithAuthentication;
    HRESULT hrAuthenticationStatus;
    LPCWSTR pwszURL;
    LPCWSTR pwszSite;
    LPCWSTR pwszRealm;
    LPCWSTR pwszPackage;
    LONG nRetries;
    MFP_CREDENTIAL_FLAGS flags;
    IMFNetCredential *pCredential;
} MFP_ACQUIRE_USER_CREDENTIAL_EVENT;
EXTERN_C const DECLSPEC_SELECTANY PROPERTYKEY MFP_PKEY_StreamIndex = { { 0xa7cf9740, 0xe8d9, 0x4a87, { 0xbd, 0x8e, 0x29, 0x67, 0x0, 0x1f, 0xd3, 0xad } }, 0x00 };
EXTERN_C const DECLSPEC_SELECTANY PROPERTYKEY MFP_PKEY_StreamRenderingResults = { { 0xa7cf9740, 0xe8d9, 0x4a87, { 0xbd, 0x8e, 0x29, 0x67, 0x0, 0x1f, 0xd3, 0xad } }, 0x01 };
#define __MFP_CAST_EVENT( pHdr, Tag ) (((pHdr)->eEventType == MFP_EVENT_TYPE_##Tag ) ? (MFP_##Tag##_EVENT*)(pHdr) : NULL)
#define MFP_GET_PLAY_EVENT( pHdr )                     __MFP_CAST_EVENT( pHdr, PLAY )
#define MFP_GET_PAUSE_EVENT( pHdr )                    __MFP_CAST_EVENT( pHdr, PAUSE )
#define MFP_GET_STOP_EVENT( pHdr )                     __MFP_CAST_EVENT( pHdr, STOP )
#define MFP_GET_POSITION_SET_EVENT( pHdr )             __MFP_CAST_EVENT( pHdr, POSITION_SET )
#define MFP_GET_RATE_SET_EVENT( pHdr )                 __MFP_CAST_EVENT( pHdr, RATE_SET )
#define MFP_GET_MEDIAITEM_CREATED_EVENT( pHdr )        __MFP_CAST_EVENT( pHdr, MEDIAITEM_CREATED )
#define MFP_GET_MEDIAITEM_SET_EVENT( pHdr )            __MFP_CAST_EVENT( pHdr, MEDIAITEM_SET )
#define MFP_GET_FRAME_STEP_EVENT( pHdr )               __MFP_CAST_EVENT( pHdr, FRAME_STEP )
#define MFP_GET_MEDIAITEM_CLEARED_EVENT( pHdr )        __MFP_CAST_EVENT( pHdr, MEDIAITEM_CLEARED )
#define MFP_GET_MF_EVENT( pHdr )                       __MFP_CAST_EVENT( pHdr, MF )
#define MFP_GET_ERROR_EVENT( pHdr )                    __MFP_CAST_EVENT( pHdr, ERROR )
#define MFP_GET_PLAYBACK_ENDED_EVENT( pHdr )           __MFP_CAST_EVENT( pHdr, PLAYBACK_ENDED )
#define MFP_GET_ACQUIRE_USER_CREDENTIAL_EVENT( pHdr )  __MFP_CAST_EVENT( pHdr, ACQUIRE_USER_CREDENTIAL )

/*****************************************************************************
 * IMFPMediaPlayerCallback interface
 */
#ifndef __IMFPMediaPlayerCallback_INTERFACE_DEFINED__
#define __IMFPMediaPlayerCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPMediaPlayerCallback, 0x766c8ffb, 0x5fdb, 0x4fea, 0xa2,0x8d, 0xb9,0x12,0x99,0x6f,0x51,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("766c8ffb-5fdb-4fea-a28d-b912996f51bd")
IMFPMediaPlayerCallback : public IUnknown
{
    virtual void STDMETHODCALLTYPE OnMediaPlayerEvent(
        MFP_EVENT_HEADER *pEventHeader) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPMediaPlayerCallback, 0x766c8ffb, 0x5fdb, 0x4fea, 0xa2,0x8d, 0xb9,0x12,0x99,0x6f,0x51,0xbd)
#endif
#else
typedef struct IMFPMediaPlayerCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPMediaPlayerCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPMediaPlayerCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPMediaPlayerCallback *This);

    /*** IMFPMediaPlayerCallback methods ***/
    void (STDMETHODCALLTYPE *OnMediaPlayerEvent)(
        IMFPMediaPlayerCallback *This,
        MFP_EVENT_HEADER *pEventHeader);

    END_INTERFACE
} IMFPMediaPlayerCallbackVtbl;

interface IMFPMediaPlayerCallback {
    CONST_VTBL IMFPMediaPlayerCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPMediaPlayerCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPMediaPlayerCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPMediaPlayerCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFPMediaPlayerCallback methods ***/
#define IMFPMediaPlayerCallback_OnMediaPlayerEvent(This,pEventHeader) (This)->lpVtbl->OnMediaPlayerEvent(This,pEventHeader)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPMediaPlayerCallback_QueryInterface(IMFPMediaPlayerCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPMediaPlayerCallback_AddRef(IMFPMediaPlayerCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPMediaPlayerCallback_Release(IMFPMediaPlayerCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFPMediaPlayerCallback methods ***/
static inline void IMFPMediaPlayerCallback_OnMediaPlayerEvent(IMFPMediaPlayerCallback* This,MFP_EVENT_HEADER *pEventHeader) {
    This->lpVtbl->OnMediaPlayerEvent(This,pEventHeader);
}
#endif
#endif

#endif


#endif  /* __IMFPMediaPlayerCallback_INTERFACE_DEFINED__ */

#endif // (WINVER >= _WIN32_WINNT_WIN7)
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mfplay_h__ */
