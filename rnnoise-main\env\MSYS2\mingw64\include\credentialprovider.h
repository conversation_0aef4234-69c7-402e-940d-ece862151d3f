/*** Autogenerated by WIDL 10.12 from include/credentialprovider.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __credentialprovider_h__
#define __credentialprovider_h__

/* Forward declarations */

#ifndef __ICredentialProviderCredential_FWD_DEFINED__
#define __ICredentialProviderCredential_FWD_DEFINED__
typedef interface ICredentialProviderCredential ICredentialProviderCredential;
#ifdef __cplusplus
interface ICredentialProviderCredential;
#endif /* __cplusplus */
#endif

#ifndef __IQueryContinueWithStatus_FWD_DEFINED__
#define __IQueryContinueWithStatus_FWD_DEFINED__
typedef interface IQueryContinueWithStatus IQueryContinueWithStatus;
#ifdef __cplusplus
interface IQueryContinueWithStatus;
#endif /* __cplusplus */
#endif

#ifndef __IConnectableCredentialProviderCredential_FWD_DEFINED__
#define __IConnectableCredentialProviderCredential_FWD_DEFINED__
typedef interface IConnectableCredentialProviderCredential IConnectableCredentialProviderCredential;
#ifdef __cplusplus
interface IConnectableCredentialProviderCredential;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderCredentialEvents_FWD_DEFINED__
#define __ICredentialProviderCredentialEvents_FWD_DEFINED__
typedef interface ICredentialProviderCredentialEvents ICredentialProviderCredentialEvents;
#ifdef __cplusplus
interface ICredentialProviderCredentialEvents;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProvider_FWD_DEFINED__
#define __ICredentialProvider_FWD_DEFINED__
typedef interface ICredentialProvider ICredentialProvider;
#ifdef __cplusplus
interface ICredentialProvider;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderEvents_FWD_DEFINED__
#define __ICredentialProviderEvents_FWD_DEFINED__
typedef interface ICredentialProviderEvents ICredentialProviderEvents;
#ifdef __cplusplus
interface ICredentialProviderEvents;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderFilter_FWD_DEFINED__
#define __ICredentialProviderFilter_FWD_DEFINED__
typedef interface ICredentialProviderFilter ICredentialProviderFilter;
#ifdef __cplusplus
interface ICredentialProviderFilter;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderCredential2_FWD_DEFINED__
#define __ICredentialProviderCredential2_FWD_DEFINED__
typedef interface ICredentialProviderCredential2 ICredentialProviderCredential2;
#ifdef __cplusplus
interface ICredentialProviderCredential2;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderCredentialWithFieldOptions_FWD_DEFINED__
#define __ICredentialProviderCredentialWithFieldOptions_FWD_DEFINED__
typedef interface ICredentialProviderCredentialWithFieldOptions ICredentialProviderCredentialWithFieldOptions;
#ifdef __cplusplus
interface ICredentialProviderCredentialWithFieldOptions;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderCredentialEvents2_FWD_DEFINED__
#define __ICredentialProviderCredentialEvents2_FWD_DEFINED__
typedef interface ICredentialProviderCredentialEvents2 ICredentialProviderCredentialEvents2;
#ifdef __cplusplus
interface ICredentialProviderCredentialEvents2;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderUser_FWD_DEFINED__
#define __ICredentialProviderUser_FWD_DEFINED__
typedef interface ICredentialProviderUser ICredentialProviderUser;
#ifdef __cplusplus
interface ICredentialProviderUser;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderUserArray_FWD_DEFINED__
#define __ICredentialProviderUserArray_FWD_DEFINED__
typedef interface ICredentialProviderUserArray ICredentialProviderUserArray;
#ifdef __cplusplus
interface ICredentialProviderUserArray;
#endif /* __cplusplus */
#endif

#ifndef __ICredentialProviderSetUserArray_FWD_DEFINED__
#define __ICredentialProviderSetUserArray_FWD_DEFINED__
typedef interface ICredentialProviderSetUserArray ICredentialProviderSetUserArray;
#ifdef __cplusplus
interface ICredentialProviderSetUserArray;
#endif /* __cplusplus */
#endif

#ifndef __PasswordCredentialProvider_FWD_DEFINED__
#define __PasswordCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class PasswordCredentialProvider PasswordCredentialProvider;
#else
typedef struct PasswordCredentialProvider PasswordCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __PasswordCredentialProvider_FWD_DEFINED__ */

#ifndef __V1PasswordCredentialProvider_FWD_DEFINED__
#define __V1PasswordCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class V1PasswordCredentialProvider V1PasswordCredentialProvider;
#else
typedef struct V1PasswordCredentialProvider V1PasswordCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __V1PasswordCredentialProvider_FWD_DEFINED__ */

#ifndef __PINLogonCredentialProvider_FWD_DEFINED__
#define __PINLogonCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class PINLogonCredentialProvider PINLogonCredentialProvider;
#else
typedef struct PINLogonCredentialProvider PINLogonCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __PINLogonCredentialProvider_FWD_DEFINED__ */

#ifndef __NPCredentialProvider_FWD_DEFINED__
#define __NPCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class NPCredentialProvider NPCredentialProvider;
#else
typedef struct NPCredentialProvider NPCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __NPCredentialProvider_FWD_DEFINED__ */

#ifndef __SmartcardCredentialProvider_FWD_DEFINED__
#define __SmartcardCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class SmartcardCredentialProvider SmartcardCredentialProvider;
#else
typedef struct SmartcardCredentialProvider SmartcardCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __SmartcardCredentialProvider_FWD_DEFINED__ */

#ifndef __V1SmartcardCredentialProvider_FWD_DEFINED__
#define __V1SmartcardCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class V1SmartcardCredentialProvider V1SmartcardCredentialProvider;
#else
typedef struct V1SmartcardCredentialProvider V1SmartcardCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __V1SmartcardCredentialProvider_FWD_DEFINED__ */

#ifndef __SmartcardPinProvider_FWD_DEFINED__
#define __SmartcardPinProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class SmartcardPinProvider SmartcardPinProvider;
#else
typedef struct SmartcardPinProvider SmartcardPinProvider;
#endif /* defined __cplusplus */
#endif /* defined __SmartcardPinProvider_FWD_DEFINED__ */

#ifndef __SmartcardReaderSelectionProvider_FWD_DEFINED__
#define __SmartcardReaderSelectionProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class SmartcardReaderSelectionProvider SmartcardReaderSelectionProvider;
#else
typedef struct SmartcardReaderSelectionProvider SmartcardReaderSelectionProvider;
#endif /* defined __cplusplus */
#endif /* defined __SmartcardReaderSelectionProvider_FWD_DEFINED__ */

#ifndef __SmartcardWinRTProvider_FWD_DEFINED__
#define __SmartcardWinRTProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class SmartcardWinRTProvider SmartcardWinRTProvider;
#else
typedef struct SmartcardWinRTProvider SmartcardWinRTProvider;
#endif /* defined __cplusplus */
#endif /* defined __SmartcardWinRTProvider_FWD_DEFINED__ */

#ifndef __GenericCredentialProvider_FWD_DEFINED__
#define __GenericCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class GenericCredentialProvider GenericCredentialProvider;
#else
typedef struct GenericCredentialProvider GenericCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __GenericCredentialProvider_FWD_DEFINED__ */

#ifndef __RASProvider_FWD_DEFINED__
#define __RASProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class RASProvider RASProvider;
#else
typedef struct RASProvider RASProvider;
#endif /* defined __cplusplus */
#endif /* defined __RASProvider_FWD_DEFINED__ */

#ifndef __OnexCredentialProvider_FWD_DEFINED__
#define __OnexCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class OnexCredentialProvider OnexCredentialProvider;
#else
typedef struct OnexCredentialProvider OnexCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __OnexCredentialProvider_FWD_DEFINED__ */

#ifndef __OnexPlapSmartcardCredentialProvider_FWD_DEFINED__
#define __OnexPlapSmartcardCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class OnexPlapSmartcardCredentialProvider OnexPlapSmartcardCredentialProvider;
#else
typedef struct OnexPlapSmartcardCredentialProvider OnexPlapSmartcardCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __OnexPlapSmartcardCredentialProvider_FWD_DEFINED__ */

#ifndef __VaultProvider_FWD_DEFINED__
#define __VaultProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class VaultProvider VaultProvider;
#else
typedef struct VaultProvider VaultProvider;
#endif /* defined __cplusplus */
#endif /* defined __VaultProvider_FWD_DEFINED__ */

#ifndef __WinBioCredentialProvider_FWD_DEFINED__
#define __WinBioCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class WinBioCredentialProvider WinBioCredentialProvider;
#else
typedef struct WinBioCredentialProvider WinBioCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __WinBioCredentialProvider_FWD_DEFINED__ */

#ifndef __V1WinBioCredentialProvider_FWD_DEFINED__
#define __V1WinBioCredentialProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class V1WinBioCredentialProvider V1WinBioCredentialProvider;
#else
typedef struct V1WinBioCredentialProvider V1WinBioCredentialProvider;
#endif /* defined __cplusplus */
#endif /* defined __V1WinBioCredentialProvider_FWD_DEFINED__ */

/* Headers for imported files */

#include <wtypes.h>
#include <shobjidl.h>
#include <propsys.h>
#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum _CREDENTIAL_PROVIDER_USAGE_SCENARIO {
    CPUS_INVALID = 0,
    CPUS_LOGON = 1,
    CPUS_UNLOCK_WORKSTATION = 2,
    CPUS_CHANGE_PASSWORD = 3,
    CPUS_CREDUI = 4,
    CPUS_PLAP = 5
} CREDENTIAL_PROVIDER_USAGE_SCENARIO;
typedef enum _CREDENTIAL_PROVIDER_FIELD_TYPE {
    CPFT_INVALID = 0,
    CPFT_LARGE_TEXT = 1,
    CPFT_SMALL_TEXT = 2,
    CPFT_COMMAND_LINK = 3,
    CPFT_EDIT_TEXT = 4,
    CPFT_PASSWORD_TEXT = 5,
    CPFT_TILE_IMAGE = 6,
    CPFT_CHECKBOX = 7,
    CPFT_COMBOBOX = 8,
    CPFT_SUBMIT_BUTTON = 9
} CREDENTIAL_PROVIDER_FIELD_TYPE;
typedef enum _CREDENTIAL_PROVIDER_FIELD_STATE {
    CPFS_HIDDEN = 0,
    CPFS_DISPLAY_IN_SELECTED_TILE = 1,
    CPFS_DISPLAY_IN_DESELECTED_TILE = 2,
    CPFS_DISPLAY_IN_BOTH = 3
} CREDENTIAL_PROVIDER_FIELD_STATE;
typedef enum _CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE {
    CPFIS_NONE = 0,
    CPFIS_READONLY = 1,
    CPFIS_DISABLED = 2,
    CPFIS_FOCUSED = 3
} CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE;
typedef struct _CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR {
    DWORD dwFieldID;
    CREDENTIAL_PROVIDER_FIELD_TYPE cpft;
    LPWSTR pszLabel;
    GUID guidFieldType;
} CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR;
typedef enum _CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE {
    CPGSR_NO_CREDENTIAL_NOT_FINISHED = 0,
    CPGSR_NO_CREDENTIAL_FINISHED = 1,
    CPGSR_RETURN_CREDENTIAL_FINISHED = 2,
    CPGSR_RETURN_NO_CREDENTIAL_FINISHED = 3
} CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE;
typedef enum _CREDENTIAL_PROVIDER_STATUS_ICON {
    CPSI_NONE = 0,
    CPSI_ERROR = 1,
    CPSI_WARNING = 2,
    CPSI_SUCCESS = 3
} CREDENTIAL_PROVIDER_STATUS_ICON;
typedef struct _CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION {
    ULONG ulAuthenticationPackage;
    GUID clsidCredentialProvider;
    ULONG cbSerialization;
    byte *rgbSerialization;
} CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION;
#if (NTDDI_VERSION >= NTDDI_WIN8)
typedef enum CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS {
    CPAO_NONE = 0x0,
    CPAO_EMPTY_LOCAL = 0x1,
    CPAO_EMPTY_CONNECTED = 0x2
} CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS;
DEFINE_ENUM_FLAG_OPERATORS(CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS)
typedef enum CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS {
    CPCFO_NONE = 0x0,
    CPCFO_ENABLE_PASSWORD_REVEAL = 0x1,
    CPCFO_IS_EMAIL_ADDRESS = 0x2,
    CPCFO_ENABLE_TOUCH_KEYBOARD_AUTO_INVOKE = 0x4,
    CPCFO_NUMBERS_ONLY = 0x8,
    CPCFO_SHOW_ENGLISH_KEYBOARD = 0x10
} CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS;
DEFINE_ENUM_FLAG_OPERATORS(CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS)
#endif /* (NTDDI_VERSION >= NTDDI_WIN8) */
#ifdef __WIDL__
typedef LONG NTSTATUS;
#else
#ifndef NTSTATUS
typedef LONG NTSTATUS;
#endif
#endif
#define CREDENTIAL_PROVIDER_NO_DEFAULT ((DWORD)-1)
#ifndef __ICredentialProviderCredentialEvents_FWD_DEFINED__
#define __ICredentialProviderCredentialEvents_FWD_DEFINED__
typedef interface ICredentialProviderCredentialEvents ICredentialProviderCredentialEvents;
#ifdef __cplusplus
interface ICredentialProviderCredentialEvents;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ICredentialProviderCredential interface
 */
#ifndef __ICredentialProviderCredential_INTERFACE_DEFINED__
#define __ICredentialProviderCredential_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderCredential, 0x63913a93, 0x40c1, 0x481a, 0x81,0x8d, 0x40,0x72,0xff,0x8c,0x70,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("63913a93-40c1-481a-818d-4072ff8c70cc")
ICredentialProviderCredential : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Advise(
        ICredentialProviderCredentialEvents *pcpce) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnAdvise(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSelected(
        WINBOOL *pbAutoLogon) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDeselected(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFieldState(
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_STATE *pcpfs,
        CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE *pcpfis) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringValue(
        DWORD dwFieldID,
        LPWSTR *ppsz) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBitmapValue(
        DWORD dwFieldID,
        HBITMAP *phbmp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCheckboxValue(
        DWORD dwFieldID,
        WINBOOL *pbChecked,
        LPWSTR *ppszLabel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubmitButtonValue(
        DWORD dwFieldID,
        DWORD *pdwAdjacentTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComboBoxValueCount(
        DWORD dwFieldID,
        DWORD *pcItems,
        DWORD *pdwSelectedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComboBoxValueAt(
        DWORD dwFieldID,
        DWORD dwItem,
        LPWSTR *ppszItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStringValue(
        DWORD dwFieldID,
        LPCWSTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCheckboxValue(
        DWORD dwFieldID,
        WINBOOL bChecked) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetComboBoxSelectedValue(
        DWORD dwFieldID,
        DWORD dwSelectedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommandLinkClicked(
        DWORD dwFieldID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSerialization(
        CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE *pcpgsr,
        CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReportResult(
        NTSTATUS ntsStatus,
        NTSTATUS ntsSubstatus,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderCredential, 0x63913a93, 0x40c1, 0x481a, 0x81,0x8d, 0x40,0x72,0xff,0x8c,0x70,0xcc)
#endif
#else
typedef struct ICredentialProviderCredentialVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderCredential *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderCredential *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderCredential *This);

    /*** ICredentialProviderCredential methods ***/
    HRESULT (STDMETHODCALLTYPE *Advise)(
        ICredentialProviderCredential *This,
        ICredentialProviderCredentialEvents *pcpce);

    HRESULT (STDMETHODCALLTYPE *UnAdvise)(
        ICredentialProviderCredential *This);

    HRESULT (STDMETHODCALLTYPE *SetSelected)(
        ICredentialProviderCredential *This,
        WINBOOL *pbAutoLogon);

    HRESULT (STDMETHODCALLTYPE *SetDeselected)(
        ICredentialProviderCredential *This);

    HRESULT (STDMETHODCALLTYPE *GetFieldState)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_STATE *pcpfs,
        CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE *pcpfis);

    HRESULT (STDMETHODCALLTYPE *GetStringValue)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        LPWSTR *ppsz);

    HRESULT (STDMETHODCALLTYPE *GetBitmapValue)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        HBITMAP *phbmp);

    HRESULT (STDMETHODCALLTYPE *GetCheckboxValue)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        WINBOOL *pbChecked,
        LPWSTR *ppszLabel);

    HRESULT (STDMETHODCALLTYPE *GetSubmitButtonValue)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD *pdwAdjacentTo);

    HRESULT (STDMETHODCALLTYPE *GetComboBoxValueCount)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD *pcItems,
        DWORD *pdwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *GetComboBoxValueAt)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD dwItem,
        LPWSTR *ppszItem);

    HRESULT (STDMETHODCALLTYPE *SetStringValue)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        LPCWSTR psz);

    HRESULT (STDMETHODCALLTYPE *SetCheckboxValue)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        WINBOOL bChecked);

    HRESULT (STDMETHODCALLTYPE *SetComboBoxSelectedValue)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD dwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *CommandLinkClicked)(
        ICredentialProviderCredential *This,
        DWORD dwFieldID);

    HRESULT (STDMETHODCALLTYPE *GetSerialization)(
        ICredentialProviderCredential *This,
        CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE *pcpgsr,
        CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon);

    HRESULT (STDMETHODCALLTYPE *ReportResult)(
        ICredentialProviderCredential *This,
        NTSTATUS ntsStatus,
        NTSTATUS ntsSubstatus,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon);

    END_INTERFACE
} ICredentialProviderCredentialVtbl;

interface ICredentialProviderCredential {
    CONST_VTBL ICredentialProviderCredentialVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderCredential_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderCredential_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderCredential_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderCredential methods ***/
#define ICredentialProviderCredential_Advise(This,pcpce) (This)->lpVtbl->Advise(This,pcpce)
#define ICredentialProviderCredential_UnAdvise(This) (This)->lpVtbl->UnAdvise(This)
#define ICredentialProviderCredential_SetSelected(This,pbAutoLogon) (This)->lpVtbl->SetSelected(This,pbAutoLogon)
#define ICredentialProviderCredential_SetDeselected(This) (This)->lpVtbl->SetDeselected(This)
#define ICredentialProviderCredential_GetFieldState(This,dwFieldID,pcpfs,pcpfis) (This)->lpVtbl->GetFieldState(This,dwFieldID,pcpfs,pcpfis)
#define ICredentialProviderCredential_GetStringValue(This,dwFieldID,ppsz) (This)->lpVtbl->GetStringValue(This,dwFieldID,ppsz)
#define ICredentialProviderCredential_GetBitmapValue(This,dwFieldID,phbmp) (This)->lpVtbl->GetBitmapValue(This,dwFieldID,phbmp)
#define ICredentialProviderCredential_GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel) (This)->lpVtbl->GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel)
#define ICredentialProviderCredential_GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo) (This)->lpVtbl->GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo)
#define ICredentialProviderCredential_GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem) (This)->lpVtbl->GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem)
#define ICredentialProviderCredential_GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem) (This)->lpVtbl->GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem)
#define ICredentialProviderCredential_SetStringValue(This,dwFieldID,psz) (This)->lpVtbl->SetStringValue(This,dwFieldID,psz)
#define ICredentialProviderCredential_SetCheckboxValue(This,dwFieldID,bChecked) (This)->lpVtbl->SetCheckboxValue(This,dwFieldID,bChecked)
#define ICredentialProviderCredential_SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem) (This)->lpVtbl->SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem)
#define ICredentialProviderCredential_CommandLinkClicked(This,dwFieldID) (This)->lpVtbl->CommandLinkClicked(This,dwFieldID)
#define ICredentialProviderCredential_GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon) (This)->lpVtbl->GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon)
#define ICredentialProviderCredential_ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon) (This)->lpVtbl->ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderCredential_QueryInterface(ICredentialProviderCredential* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderCredential_AddRef(ICredentialProviderCredential* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderCredential_Release(ICredentialProviderCredential* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderCredential methods ***/
static inline HRESULT ICredentialProviderCredential_Advise(ICredentialProviderCredential* This,ICredentialProviderCredentialEvents *pcpce) {
    return This->lpVtbl->Advise(This,pcpce);
}
static inline HRESULT ICredentialProviderCredential_UnAdvise(ICredentialProviderCredential* This) {
    return This->lpVtbl->UnAdvise(This);
}
static inline HRESULT ICredentialProviderCredential_SetSelected(ICredentialProviderCredential* This,WINBOOL *pbAutoLogon) {
    return This->lpVtbl->SetSelected(This,pbAutoLogon);
}
static inline HRESULT ICredentialProviderCredential_SetDeselected(ICredentialProviderCredential* This) {
    return This->lpVtbl->SetDeselected(This);
}
static inline HRESULT ICredentialProviderCredential_GetFieldState(ICredentialProviderCredential* This,DWORD dwFieldID,CREDENTIAL_PROVIDER_FIELD_STATE *pcpfs,CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE *pcpfis) {
    return This->lpVtbl->GetFieldState(This,dwFieldID,pcpfs,pcpfis);
}
static inline HRESULT ICredentialProviderCredential_GetStringValue(ICredentialProviderCredential* This,DWORD dwFieldID,LPWSTR *ppsz) {
    return This->lpVtbl->GetStringValue(This,dwFieldID,ppsz);
}
static inline HRESULT ICredentialProviderCredential_GetBitmapValue(ICredentialProviderCredential* This,DWORD dwFieldID,HBITMAP *phbmp) {
    return This->lpVtbl->GetBitmapValue(This,dwFieldID,phbmp);
}
static inline HRESULT ICredentialProviderCredential_GetCheckboxValue(ICredentialProviderCredential* This,DWORD dwFieldID,WINBOOL *pbChecked,LPWSTR *ppszLabel) {
    return This->lpVtbl->GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel);
}
static inline HRESULT ICredentialProviderCredential_GetSubmitButtonValue(ICredentialProviderCredential* This,DWORD dwFieldID,DWORD *pdwAdjacentTo) {
    return This->lpVtbl->GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo);
}
static inline HRESULT ICredentialProviderCredential_GetComboBoxValueCount(ICredentialProviderCredential* This,DWORD dwFieldID,DWORD *pcItems,DWORD *pdwSelectedItem) {
    return This->lpVtbl->GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem);
}
static inline HRESULT ICredentialProviderCredential_GetComboBoxValueAt(ICredentialProviderCredential* This,DWORD dwFieldID,DWORD dwItem,LPWSTR *ppszItem) {
    return This->lpVtbl->GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem);
}
static inline HRESULT ICredentialProviderCredential_SetStringValue(ICredentialProviderCredential* This,DWORD dwFieldID,LPCWSTR psz) {
    return This->lpVtbl->SetStringValue(This,dwFieldID,psz);
}
static inline HRESULT ICredentialProviderCredential_SetCheckboxValue(ICredentialProviderCredential* This,DWORD dwFieldID,WINBOOL bChecked) {
    return This->lpVtbl->SetCheckboxValue(This,dwFieldID,bChecked);
}
static inline HRESULT ICredentialProviderCredential_SetComboBoxSelectedValue(ICredentialProviderCredential* This,DWORD dwFieldID,DWORD dwSelectedItem) {
    return This->lpVtbl->SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem);
}
static inline HRESULT ICredentialProviderCredential_CommandLinkClicked(ICredentialProviderCredential* This,DWORD dwFieldID) {
    return This->lpVtbl->CommandLinkClicked(This,dwFieldID);
}
static inline HRESULT ICredentialProviderCredential_GetSerialization(ICredentialProviderCredential* This,CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE *pcpgsr,CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs,LPWSTR *ppszOptionalStatusText,CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) {
    return This->lpVtbl->GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon);
}
static inline HRESULT ICredentialProviderCredential_ReportResult(ICredentialProviderCredential* This,NTSTATUS ntsStatus,NTSTATUS ntsSubstatus,LPWSTR *ppszOptionalStatusText,CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) {
    return This->lpVtbl->ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderCredential_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IQueryContinueWithStatus interface
 */
#ifndef __IQueryContinueWithStatus_INTERFACE_DEFINED__
#define __IQueryContinueWithStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID_IQueryContinueWithStatus, 0x9090be5b, 0x502b, 0x41fb, 0xbc,0xcc, 0x00,0x49,0xa6,0xc7,0x25,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9090be5b-502b-41fb-bccc-0049a6c7254b")
IQueryContinueWithStatus : public IQueryContinue
{
    virtual HRESULT STDMETHODCALLTYPE SetStatusMessage(
        LPCWSTR psz) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IQueryContinueWithStatus, 0x9090be5b, 0x502b, 0x41fb, 0xbc,0xcc, 0x00,0x49,0xa6,0xc7,0x25,0x4b)
#endif
#else
typedef struct IQueryContinueWithStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IQueryContinueWithStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IQueryContinueWithStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IQueryContinueWithStatus *This);

    /*** IQueryContinue methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryContinue)(
        IQueryContinueWithStatus *This);

    /*** IQueryContinueWithStatus methods ***/
    HRESULT (STDMETHODCALLTYPE *SetStatusMessage)(
        IQueryContinueWithStatus *This,
        LPCWSTR psz);

    END_INTERFACE
} IQueryContinueWithStatusVtbl;

interface IQueryContinueWithStatus {
    CONST_VTBL IQueryContinueWithStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IQueryContinueWithStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IQueryContinueWithStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IQueryContinueWithStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IQueryContinue methods ***/
#define IQueryContinueWithStatus_QueryContinue(This) (This)->lpVtbl->QueryContinue(This)
/*** IQueryContinueWithStatus methods ***/
#define IQueryContinueWithStatus_SetStatusMessage(This,psz) (This)->lpVtbl->SetStatusMessage(This,psz)
#else
/*** IUnknown methods ***/
static inline HRESULT IQueryContinueWithStatus_QueryInterface(IQueryContinueWithStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IQueryContinueWithStatus_AddRef(IQueryContinueWithStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IQueryContinueWithStatus_Release(IQueryContinueWithStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IQueryContinue methods ***/
static inline HRESULT IQueryContinueWithStatus_QueryContinue(IQueryContinueWithStatus* This) {
    return This->lpVtbl->QueryContinue(This);
}
/*** IQueryContinueWithStatus methods ***/
static inline HRESULT IQueryContinueWithStatus_SetStatusMessage(IQueryContinueWithStatus* This,LPCWSTR psz) {
    return This->lpVtbl->SetStatusMessage(This,psz);
}
#endif
#endif

#endif


#endif  /* __IQueryContinueWithStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IConnectableCredentialProviderCredential interface
 */
#ifndef __IConnectableCredentialProviderCredential_INTERFACE_DEFINED__
#define __IConnectableCredentialProviderCredential_INTERFACE_DEFINED__

DEFINE_GUID(IID_IConnectableCredentialProviderCredential, 0x9387928b, 0xac75, 0x4bf9, 0x8a,0xb2, 0x2b,0x93,0xc4,0xa5,0x52,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9387928b-ac75-4bf9-8ab2-2b93c4a55290")
IConnectableCredentialProviderCredential : public ICredentialProviderCredential
{
    virtual HRESULT STDMETHODCALLTYPE Connect(
        IQueryContinueWithStatus *pqcws) = 0;

    virtual HRESULT STDMETHODCALLTYPE Disconnect(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IConnectableCredentialProviderCredential, 0x9387928b, 0xac75, 0x4bf9, 0x8a,0xb2, 0x2b,0x93,0xc4,0xa5,0x52,0x90)
#endif
#else
typedef struct IConnectableCredentialProviderCredentialVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IConnectableCredentialProviderCredential *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IConnectableCredentialProviderCredential *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IConnectableCredentialProviderCredential *This);

    /*** ICredentialProviderCredential methods ***/
    HRESULT (STDMETHODCALLTYPE *Advise)(
        IConnectableCredentialProviderCredential *This,
        ICredentialProviderCredentialEvents *pcpce);

    HRESULT (STDMETHODCALLTYPE *UnAdvise)(
        IConnectableCredentialProviderCredential *This);

    HRESULT (STDMETHODCALLTYPE *SetSelected)(
        IConnectableCredentialProviderCredential *This,
        WINBOOL *pbAutoLogon);

    HRESULT (STDMETHODCALLTYPE *SetDeselected)(
        IConnectableCredentialProviderCredential *This);

    HRESULT (STDMETHODCALLTYPE *GetFieldState)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_STATE *pcpfs,
        CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE *pcpfis);

    HRESULT (STDMETHODCALLTYPE *GetStringValue)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        LPWSTR *ppsz);

    HRESULT (STDMETHODCALLTYPE *GetBitmapValue)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        HBITMAP *phbmp);

    HRESULT (STDMETHODCALLTYPE *GetCheckboxValue)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        WINBOOL *pbChecked,
        LPWSTR *ppszLabel);

    HRESULT (STDMETHODCALLTYPE *GetSubmitButtonValue)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD *pdwAdjacentTo);

    HRESULT (STDMETHODCALLTYPE *GetComboBoxValueCount)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD *pcItems,
        DWORD *pdwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *GetComboBoxValueAt)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD dwItem,
        LPWSTR *ppszItem);

    HRESULT (STDMETHODCALLTYPE *SetStringValue)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        LPCWSTR psz);

    HRESULT (STDMETHODCALLTYPE *SetCheckboxValue)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        WINBOOL bChecked);

    HRESULT (STDMETHODCALLTYPE *SetComboBoxSelectedValue)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID,
        DWORD dwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *CommandLinkClicked)(
        IConnectableCredentialProviderCredential *This,
        DWORD dwFieldID);

    HRESULT (STDMETHODCALLTYPE *GetSerialization)(
        IConnectableCredentialProviderCredential *This,
        CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE *pcpgsr,
        CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon);

    HRESULT (STDMETHODCALLTYPE *ReportResult)(
        IConnectableCredentialProviderCredential *This,
        NTSTATUS ntsStatus,
        NTSTATUS ntsSubstatus,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon);

    /*** IConnectableCredentialProviderCredential methods ***/
    HRESULT (STDMETHODCALLTYPE *Connect)(
        IConnectableCredentialProviderCredential *This,
        IQueryContinueWithStatus *pqcws);

    HRESULT (STDMETHODCALLTYPE *Disconnect)(
        IConnectableCredentialProviderCredential *This);

    END_INTERFACE
} IConnectableCredentialProviderCredentialVtbl;

interface IConnectableCredentialProviderCredential {
    CONST_VTBL IConnectableCredentialProviderCredentialVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IConnectableCredentialProviderCredential_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConnectableCredentialProviderCredential_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConnectableCredentialProviderCredential_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderCredential methods ***/
#define IConnectableCredentialProviderCredential_Advise(This,pcpce) (This)->lpVtbl->Advise(This,pcpce)
#define IConnectableCredentialProviderCredential_UnAdvise(This) (This)->lpVtbl->UnAdvise(This)
#define IConnectableCredentialProviderCredential_SetSelected(This,pbAutoLogon) (This)->lpVtbl->SetSelected(This,pbAutoLogon)
#define IConnectableCredentialProviderCredential_SetDeselected(This) (This)->lpVtbl->SetDeselected(This)
#define IConnectableCredentialProviderCredential_GetFieldState(This,dwFieldID,pcpfs,pcpfis) (This)->lpVtbl->GetFieldState(This,dwFieldID,pcpfs,pcpfis)
#define IConnectableCredentialProviderCredential_GetStringValue(This,dwFieldID,ppsz) (This)->lpVtbl->GetStringValue(This,dwFieldID,ppsz)
#define IConnectableCredentialProviderCredential_GetBitmapValue(This,dwFieldID,phbmp) (This)->lpVtbl->GetBitmapValue(This,dwFieldID,phbmp)
#define IConnectableCredentialProviderCredential_GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel) (This)->lpVtbl->GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel)
#define IConnectableCredentialProviderCredential_GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo) (This)->lpVtbl->GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo)
#define IConnectableCredentialProviderCredential_GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem) (This)->lpVtbl->GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem)
#define IConnectableCredentialProviderCredential_GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem) (This)->lpVtbl->GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem)
#define IConnectableCredentialProviderCredential_SetStringValue(This,dwFieldID,psz) (This)->lpVtbl->SetStringValue(This,dwFieldID,psz)
#define IConnectableCredentialProviderCredential_SetCheckboxValue(This,dwFieldID,bChecked) (This)->lpVtbl->SetCheckboxValue(This,dwFieldID,bChecked)
#define IConnectableCredentialProviderCredential_SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem) (This)->lpVtbl->SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem)
#define IConnectableCredentialProviderCredential_CommandLinkClicked(This,dwFieldID) (This)->lpVtbl->CommandLinkClicked(This,dwFieldID)
#define IConnectableCredentialProviderCredential_GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon) (This)->lpVtbl->GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon)
#define IConnectableCredentialProviderCredential_ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon) (This)->lpVtbl->ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon)
/*** IConnectableCredentialProviderCredential methods ***/
#define IConnectableCredentialProviderCredential_Connect(This,pqcws) (This)->lpVtbl->Connect(This,pqcws)
#define IConnectableCredentialProviderCredential_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IConnectableCredentialProviderCredential_QueryInterface(IConnectableCredentialProviderCredential* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IConnectableCredentialProviderCredential_AddRef(IConnectableCredentialProviderCredential* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IConnectableCredentialProviderCredential_Release(IConnectableCredentialProviderCredential* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderCredential methods ***/
static inline HRESULT IConnectableCredentialProviderCredential_Advise(IConnectableCredentialProviderCredential* This,ICredentialProviderCredentialEvents *pcpce) {
    return This->lpVtbl->Advise(This,pcpce);
}
static inline HRESULT IConnectableCredentialProviderCredential_UnAdvise(IConnectableCredentialProviderCredential* This) {
    return This->lpVtbl->UnAdvise(This);
}
static inline HRESULT IConnectableCredentialProviderCredential_SetSelected(IConnectableCredentialProviderCredential* This,WINBOOL *pbAutoLogon) {
    return This->lpVtbl->SetSelected(This,pbAutoLogon);
}
static inline HRESULT IConnectableCredentialProviderCredential_SetDeselected(IConnectableCredentialProviderCredential* This) {
    return This->lpVtbl->SetDeselected(This);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetFieldState(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,CREDENTIAL_PROVIDER_FIELD_STATE *pcpfs,CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE *pcpfis) {
    return This->lpVtbl->GetFieldState(This,dwFieldID,pcpfs,pcpfis);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetStringValue(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,LPWSTR *ppsz) {
    return This->lpVtbl->GetStringValue(This,dwFieldID,ppsz);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetBitmapValue(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,HBITMAP *phbmp) {
    return This->lpVtbl->GetBitmapValue(This,dwFieldID,phbmp);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetCheckboxValue(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,WINBOOL *pbChecked,LPWSTR *ppszLabel) {
    return This->lpVtbl->GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetSubmitButtonValue(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,DWORD *pdwAdjacentTo) {
    return This->lpVtbl->GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetComboBoxValueCount(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,DWORD *pcItems,DWORD *pdwSelectedItem) {
    return This->lpVtbl->GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetComboBoxValueAt(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,DWORD dwItem,LPWSTR *ppszItem) {
    return This->lpVtbl->GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem);
}
static inline HRESULT IConnectableCredentialProviderCredential_SetStringValue(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,LPCWSTR psz) {
    return This->lpVtbl->SetStringValue(This,dwFieldID,psz);
}
static inline HRESULT IConnectableCredentialProviderCredential_SetCheckboxValue(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,WINBOOL bChecked) {
    return This->lpVtbl->SetCheckboxValue(This,dwFieldID,bChecked);
}
static inline HRESULT IConnectableCredentialProviderCredential_SetComboBoxSelectedValue(IConnectableCredentialProviderCredential* This,DWORD dwFieldID,DWORD dwSelectedItem) {
    return This->lpVtbl->SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem);
}
static inline HRESULT IConnectableCredentialProviderCredential_CommandLinkClicked(IConnectableCredentialProviderCredential* This,DWORD dwFieldID) {
    return This->lpVtbl->CommandLinkClicked(This,dwFieldID);
}
static inline HRESULT IConnectableCredentialProviderCredential_GetSerialization(IConnectableCredentialProviderCredential* This,CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE *pcpgsr,CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs,LPWSTR *ppszOptionalStatusText,CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) {
    return This->lpVtbl->GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon);
}
static inline HRESULT IConnectableCredentialProviderCredential_ReportResult(IConnectableCredentialProviderCredential* This,NTSTATUS ntsStatus,NTSTATUS ntsSubstatus,LPWSTR *ppszOptionalStatusText,CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) {
    return This->lpVtbl->ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon);
}
/*** IConnectableCredentialProviderCredential methods ***/
static inline HRESULT IConnectableCredentialProviderCredential_Connect(IConnectableCredentialProviderCredential* This,IQueryContinueWithStatus *pqcws) {
    return This->lpVtbl->Connect(This,pqcws);
}
static inline HRESULT IConnectableCredentialProviderCredential_Disconnect(IConnectableCredentialProviderCredential* This) {
    return This->lpVtbl->Disconnect(This);
}
#endif
#endif

#endif


#endif  /* __IConnectableCredentialProviderCredential_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICredentialProviderCredentialEvents interface
 */
#ifndef __ICredentialProviderCredentialEvents_INTERFACE_DEFINED__
#define __ICredentialProviderCredentialEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderCredentialEvents, 0xfa6fa76b, 0x66b7, 0x4b11, 0x95,0xf1, 0x86,0x17,0x11,0x18,0xe8,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa6fa76b-66b7-4b11-95f1-86171118e816")
ICredentialProviderCredentialEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetFieldState(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_STATE cpfs) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFieldInteractiveState(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE cpfis) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFieldString(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        LPCWSTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFieldCheckbox(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        WINBOOL bChecked,
        LPCWSTR pszLabel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFieldBitmap(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        HBITMAP hbmp) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFieldComboBoxSelectedItem(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwSelectedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteFieldComboBoxItem(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE AppendFieldComboBoxItem(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        LPCWSTR pszItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFieldSubmitButton(
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwAdjacentTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnCreatingWindow(
        HWND *phwndOwner) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderCredentialEvents, 0xfa6fa76b, 0x66b7, 0x4b11, 0x95,0xf1, 0x86,0x17,0x11,0x18,0xe8,0x16)
#endif
#else
typedef struct ICredentialProviderCredentialEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderCredentialEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderCredentialEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderCredentialEvents *This);

    /*** ICredentialProviderCredentialEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFieldState)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_STATE cpfs);

    HRESULT (STDMETHODCALLTYPE *SetFieldInteractiveState)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE cpfis);

    HRESULT (STDMETHODCALLTYPE *SetFieldString)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        LPCWSTR psz);

    HRESULT (STDMETHODCALLTYPE *SetFieldCheckbox)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        WINBOOL bChecked,
        LPCWSTR pszLabel);

    HRESULT (STDMETHODCALLTYPE *SetFieldBitmap)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        HBITMAP hbmp);

    HRESULT (STDMETHODCALLTYPE *SetFieldComboBoxSelectedItem)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *DeleteFieldComboBoxItem)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwItem);

    HRESULT (STDMETHODCALLTYPE *AppendFieldComboBoxItem)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        LPCWSTR pszItem);

    HRESULT (STDMETHODCALLTYPE *SetFieldSubmitButton)(
        ICredentialProviderCredentialEvents *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwAdjacentTo);

    HRESULT (STDMETHODCALLTYPE *OnCreatingWindow)(
        ICredentialProviderCredentialEvents *This,
        HWND *phwndOwner);

    END_INTERFACE
} ICredentialProviderCredentialEventsVtbl;

interface ICredentialProviderCredentialEvents {
    CONST_VTBL ICredentialProviderCredentialEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderCredentialEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderCredentialEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderCredentialEvents_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderCredentialEvents methods ***/
#define ICredentialProviderCredentialEvents_SetFieldState(This,pcpc,dwFieldID,cpfs) (This)->lpVtbl->SetFieldState(This,pcpc,dwFieldID,cpfs)
#define ICredentialProviderCredentialEvents_SetFieldInteractiveState(This,pcpc,dwFieldID,cpfis) (This)->lpVtbl->SetFieldInteractiveState(This,pcpc,dwFieldID,cpfis)
#define ICredentialProviderCredentialEvents_SetFieldString(This,pcpc,dwFieldID,psz) (This)->lpVtbl->SetFieldString(This,pcpc,dwFieldID,psz)
#define ICredentialProviderCredentialEvents_SetFieldCheckbox(This,pcpc,dwFieldID,bChecked,pszLabel) (This)->lpVtbl->SetFieldCheckbox(This,pcpc,dwFieldID,bChecked,pszLabel)
#define ICredentialProviderCredentialEvents_SetFieldBitmap(This,pcpc,dwFieldID,hbmp) (This)->lpVtbl->SetFieldBitmap(This,pcpc,dwFieldID,hbmp)
#define ICredentialProviderCredentialEvents_SetFieldComboBoxSelectedItem(This,pcpc,dwFieldID,dwSelectedItem) (This)->lpVtbl->SetFieldComboBoxSelectedItem(This,pcpc,dwFieldID,dwSelectedItem)
#define ICredentialProviderCredentialEvents_DeleteFieldComboBoxItem(This,pcpc,dwFieldID,dwItem) (This)->lpVtbl->DeleteFieldComboBoxItem(This,pcpc,dwFieldID,dwItem)
#define ICredentialProviderCredentialEvents_AppendFieldComboBoxItem(This,pcpc,dwFieldID,pszItem) (This)->lpVtbl->AppendFieldComboBoxItem(This,pcpc,dwFieldID,pszItem)
#define ICredentialProviderCredentialEvents_SetFieldSubmitButton(This,pcpc,dwFieldID,dwAdjacentTo) (This)->lpVtbl->SetFieldSubmitButton(This,pcpc,dwFieldID,dwAdjacentTo)
#define ICredentialProviderCredentialEvents_OnCreatingWindow(This,phwndOwner) (This)->lpVtbl->OnCreatingWindow(This,phwndOwner)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderCredentialEvents_QueryInterface(ICredentialProviderCredentialEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderCredentialEvents_AddRef(ICredentialProviderCredentialEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderCredentialEvents_Release(ICredentialProviderCredentialEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderCredentialEvents methods ***/
static inline HRESULT ICredentialProviderCredentialEvents_SetFieldState(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,CREDENTIAL_PROVIDER_FIELD_STATE cpfs) {
    return This->lpVtbl->SetFieldState(This,pcpc,dwFieldID,cpfs);
}
static inline HRESULT ICredentialProviderCredentialEvents_SetFieldInteractiveState(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE cpfis) {
    return This->lpVtbl->SetFieldInteractiveState(This,pcpc,dwFieldID,cpfis);
}
static inline HRESULT ICredentialProviderCredentialEvents_SetFieldString(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,LPCWSTR psz) {
    return This->lpVtbl->SetFieldString(This,pcpc,dwFieldID,psz);
}
static inline HRESULT ICredentialProviderCredentialEvents_SetFieldCheckbox(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,WINBOOL bChecked,LPCWSTR pszLabel) {
    return This->lpVtbl->SetFieldCheckbox(This,pcpc,dwFieldID,bChecked,pszLabel);
}
static inline HRESULT ICredentialProviderCredentialEvents_SetFieldBitmap(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,HBITMAP hbmp) {
    return This->lpVtbl->SetFieldBitmap(This,pcpc,dwFieldID,hbmp);
}
static inline HRESULT ICredentialProviderCredentialEvents_SetFieldComboBoxSelectedItem(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,DWORD dwSelectedItem) {
    return This->lpVtbl->SetFieldComboBoxSelectedItem(This,pcpc,dwFieldID,dwSelectedItem);
}
static inline HRESULT ICredentialProviderCredentialEvents_DeleteFieldComboBoxItem(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,DWORD dwItem) {
    return This->lpVtbl->DeleteFieldComboBoxItem(This,pcpc,dwFieldID,dwItem);
}
static inline HRESULT ICredentialProviderCredentialEvents_AppendFieldComboBoxItem(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,LPCWSTR pszItem) {
    return This->lpVtbl->AppendFieldComboBoxItem(This,pcpc,dwFieldID,pszItem);
}
static inline HRESULT ICredentialProviderCredentialEvents_SetFieldSubmitButton(ICredentialProviderCredentialEvents* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,DWORD dwAdjacentTo) {
    return This->lpVtbl->SetFieldSubmitButton(This,pcpc,dwFieldID,dwAdjacentTo);
}
static inline HRESULT ICredentialProviderCredentialEvents_OnCreatingWindow(ICredentialProviderCredentialEvents* This,HWND *phwndOwner) {
    return This->lpVtbl->OnCreatingWindow(This,phwndOwner);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderCredentialEvents_INTERFACE_DEFINED__ */

#ifndef __ICredentialProviderEvents_FWD_DEFINED__
#define __ICredentialProviderEvents_FWD_DEFINED__
typedef interface ICredentialProviderEvents ICredentialProviderEvents;
#ifdef __cplusplus
interface ICredentialProviderEvents;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ICredentialProvider interface
 */
#ifndef __ICredentialProvider_INTERFACE_DEFINED__
#define __ICredentialProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProvider, 0xd27c3481, 0x5a1c, 0x45b2, 0x8a,0xaa, 0xc2,0x0e,0xbb,0xe8,0x22,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d27c3481-5a1c-45b2-8aaa-c20ebbe8229e")
ICredentialProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetUsageScenario(
        CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSerialization(
        const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs) = 0;

    virtual HRESULT STDMETHODCALLTYPE Advise(
        ICredentialProviderEvents *pcpe,
        UINT_PTR upAdviseContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnAdvise(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFieldDescriptorCount(
        DWORD *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFieldDescriptorAt(
        DWORD dwIndex,
        CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR **ppcpfd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCredentialCount(
        DWORD *pdwCount,
        DWORD *pdwDefault,
        WINBOOL *pbAutoLogonWithDefault) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCredentialAt(
        DWORD dwIndex,
        ICredentialProviderCredential **ppcpc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProvider, 0xd27c3481, 0x5a1c, 0x45b2, 0x8a,0xaa, 0xc2,0x0e,0xbb,0xe8,0x22,0x9e)
#endif
#else
typedef struct ICredentialProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProvider *This);

    /*** ICredentialProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUsageScenario)(
        ICredentialProvider *This,
        CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *SetSerialization)(
        ICredentialProvider *This,
        const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs);

    HRESULT (STDMETHODCALLTYPE *Advise)(
        ICredentialProvider *This,
        ICredentialProviderEvents *pcpe,
        UINT_PTR upAdviseContext);

    HRESULT (STDMETHODCALLTYPE *UnAdvise)(
        ICredentialProvider *This);

    HRESULT (STDMETHODCALLTYPE *GetFieldDescriptorCount)(
        ICredentialProvider *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *GetFieldDescriptorAt)(
        ICredentialProvider *This,
        DWORD dwIndex,
        CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR **ppcpfd);

    HRESULT (STDMETHODCALLTYPE *GetCredentialCount)(
        ICredentialProvider *This,
        DWORD *pdwCount,
        DWORD *pdwDefault,
        WINBOOL *pbAutoLogonWithDefault);

    HRESULT (STDMETHODCALLTYPE *GetCredentialAt)(
        ICredentialProvider *This,
        DWORD dwIndex,
        ICredentialProviderCredential **ppcpc);

    END_INTERFACE
} ICredentialProviderVtbl;

interface ICredentialProvider {
    CONST_VTBL ICredentialProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProvider methods ***/
#define ICredentialProvider_SetUsageScenario(This,cpus,dwFlags) (This)->lpVtbl->SetUsageScenario(This,cpus,dwFlags)
#define ICredentialProvider_SetSerialization(This,pcpcs) (This)->lpVtbl->SetSerialization(This,pcpcs)
#define ICredentialProvider_Advise(This,pcpe,upAdviseContext) (This)->lpVtbl->Advise(This,pcpe,upAdviseContext)
#define ICredentialProvider_UnAdvise(This) (This)->lpVtbl->UnAdvise(This)
#define ICredentialProvider_GetFieldDescriptorCount(This,pdwCount) (This)->lpVtbl->GetFieldDescriptorCount(This,pdwCount)
#define ICredentialProvider_GetFieldDescriptorAt(This,dwIndex,ppcpfd) (This)->lpVtbl->GetFieldDescriptorAt(This,dwIndex,ppcpfd)
#define ICredentialProvider_GetCredentialCount(This,pdwCount,pdwDefault,pbAutoLogonWithDefault) (This)->lpVtbl->GetCredentialCount(This,pdwCount,pdwDefault,pbAutoLogonWithDefault)
#define ICredentialProvider_GetCredentialAt(This,dwIndex,ppcpc) (This)->lpVtbl->GetCredentialAt(This,dwIndex,ppcpc)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProvider_QueryInterface(ICredentialProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProvider_AddRef(ICredentialProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProvider_Release(ICredentialProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProvider methods ***/
static inline HRESULT ICredentialProvider_SetUsageScenario(ICredentialProvider* This,CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,DWORD dwFlags) {
    return This->lpVtbl->SetUsageScenario(This,cpus,dwFlags);
}
static inline HRESULT ICredentialProvider_SetSerialization(ICredentialProvider* This,const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs) {
    return This->lpVtbl->SetSerialization(This,pcpcs);
}
static inline HRESULT ICredentialProvider_Advise(ICredentialProvider* This,ICredentialProviderEvents *pcpe,UINT_PTR upAdviseContext) {
    return This->lpVtbl->Advise(This,pcpe,upAdviseContext);
}
static inline HRESULT ICredentialProvider_UnAdvise(ICredentialProvider* This) {
    return This->lpVtbl->UnAdvise(This);
}
static inline HRESULT ICredentialProvider_GetFieldDescriptorCount(ICredentialProvider* This,DWORD *pdwCount) {
    return This->lpVtbl->GetFieldDescriptorCount(This,pdwCount);
}
static inline HRESULT ICredentialProvider_GetFieldDescriptorAt(ICredentialProvider* This,DWORD dwIndex,CREDENTIAL_PROVIDER_FIELD_DESCRIPTOR **ppcpfd) {
    return This->lpVtbl->GetFieldDescriptorAt(This,dwIndex,ppcpfd);
}
static inline HRESULT ICredentialProvider_GetCredentialCount(ICredentialProvider* This,DWORD *pdwCount,DWORD *pdwDefault,WINBOOL *pbAutoLogonWithDefault) {
    return This->lpVtbl->GetCredentialCount(This,pdwCount,pdwDefault,pbAutoLogonWithDefault);
}
static inline HRESULT ICredentialProvider_GetCredentialAt(ICredentialProvider* This,DWORD dwIndex,ICredentialProviderCredential **ppcpc) {
    return This->lpVtbl->GetCredentialAt(This,dwIndex,ppcpc);
}
#endif
#endif

#endif


#endif  /* __ICredentialProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICredentialProviderEvents interface
 */
#ifndef __ICredentialProviderEvents_INTERFACE_DEFINED__
#define __ICredentialProviderEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderEvents, 0x34201e5a, 0xa787, 0x41a3, 0xa5,0xa4, 0xbd,0x6d,0xcf,0x2a,0x85,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("34201e5a-a787-41a3-a5a4-bd6dcf2a854e")
ICredentialProviderEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CredentialsChanged(
        UINT_PTR upAdviseContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderEvents, 0x34201e5a, 0xa787, 0x41a3, 0xa5,0xa4, 0xbd,0x6d,0xcf,0x2a,0x85,0x4e)
#endif
#else
typedef struct ICredentialProviderEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderEvents *This);

    /*** ICredentialProviderEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *CredentialsChanged)(
        ICredentialProviderEvents *This,
        UINT_PTR upAdviseContext);

    END_INTERFACE
} ICredentialProviderEventsVtbl;

interface ICredentialProviderEvents {
    CONST_VTBL ICredentialProviderEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderEvents_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderEvents methods ***/
#define ICredentialProviderEvents_CredentialsChanged(This,upAdviseContext) (This)->lpVtbl->CredentialsChanged(This,upAdviseContext)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderEvents_QueryInterface(ICredentialProviderEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderEvents_AddRef(ICredentialProviderEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderEvents_Release(ICredentialProviderEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderEvents methods ***/
static inline HRESULT ICredentialProviderEvents_CredentialsChanged(ICredentialProviderEvents* This,UINT_PTR upAdviseContext) {
    return This->lpVtbl->CredentialsChanged(This,upAdviseContext);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICredentialProviderFilter interface
 */
#ifndef __ICredentialProviderFilter_INTERFACE_DEFINED__
#define __ICredentialProviderFilter_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderFilter, 0xa5da53f9, 0xd475, 0x4080, 0xa1,0x20, 0x91,0x0c,0x4a,0x73,0x98,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a5da53f9-d475-4080-a120-910c4a739880")
ICredentialProviderFilter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Filter(
        CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,
        DWORD dwFlags,
        GUID *rgclsidProviders,
        WINBOOL *rgbAllow,
        DWORD cProviders) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateRemoteCredential(
        const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcsIn,
        CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcsOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderFilter, 0xa5da53f9, 0xd475, 0x4080, 0xa1,0x20, 0x91,0x0c,0x4a,0x73,0x98,0x80)
#endif
#else
typedef struct ICredentialProviderFilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderFilter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderFilter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderFilter *This);

    /*** ICredentialProviderFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *Filter)(
        ICredentialProviderFilter *This,
        CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,
        DWORD dwFlags,
        GUID *rgclsidProviders,
        WINBOOL *rgbAllow,
        DWORD cProviders);

    HRESULT (STDMETHODCALLTYPE *UpdateRemoteCredential)(
        ICredentialProviderFilter *This,
        const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcsIn,
        CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcsOut);

    END_INTERFACE
} ICredentialProviderFilterVtbl;

interface ICredentialProviderFilter {
    CONST_VTBL ICredentialProviderFilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderFilter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderFilter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderFilter_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderFilter methods ***/
#define ICredentialProviderFilter_Filter(This,cpus,dwFlags,rgclsidProviders,rgbAllow,cProviders) (This)->lpVtbl->Filter(This,cpus,dwFlags,rgclsidProviders,rgbAllow,cProviders)
#define ICredentialProviderFilter_UpdateRemoteCredential(This,pcpcsIn,pcpcsOut) (This)->lpVtbl->UpdateRemoteCredential(This,pcpcsIn,pcpcsOut)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderFilter_QueryInterface(ICredentialProviderFilter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderFilter_AddRef(ICredentialProviderFilter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderFilter_Release(ICredentialProviderFilter* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderFilter methods ***/
static inline HRESULT ICredentialProviderFilter_Filter(ICredentialProviderFilter* This,CREDENTIAL_PROVIDER_USAGE_SCENARIO cpus,DWORD dwFlags,GUID *rgclsidProviders,WINBOOL *rgbAllow,DWORD cProviders) {
    return This->lpVtbl->Filter(This,cpus,dwFlags,rgclsidProviders,rgbAllow,cProviders);
}
static inline HRESULT ICredentialProviderFilter_UpdateRemoteCredential(ICredentialProviderFilter* This,const CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcsIn,CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcsOut) {
    return This->lpVtbl->UpdateRemoteCredential(This,pcpcsIn,pcpcsOut);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderFilter_INTERFACE_DEFINED__ */

#if (NTDDI_VERSION >= NTDDI_WIN8)
/*****************************************************************************
 * ICredentialProviderCredential2 interface
 */
#ifndef __ICredentialProviderCredential2_INTERFACE_DEFINED__
#define __ICredentialProviderCredential2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderCredential2, 0xfd672c54, 0x40ea, 0x4d6e, 0x9b,0x49, 0xcf,0xb1,0xa7,0x50,0x7b,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fd672c54-40ea-4d6e-9b49-cfb1a7507bd7")
ICredentialProviderCredential2 : public ICredentialProviderCredential
{
    virtual HRESULT STDMETHODCALLTYPE GetUserSid(
        LPWSTR *sid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderCredential2, 0xfd672c54, 0x40ea, 0x4d6e, 0x9b,0x49, 0xcf,0xb1,0xa7,0x50,0x7b,0xd7)
#endif
#else
typedef struct ICredentialProviderCredential2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderCredential2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderCredential2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderCredential2 *This);

    /*** ICredentialProviderCredential methods ***/
    HRESULT (STDMETHODCALLTYPE *Advise)(
        ICredentialProviderCredential2 *This,
        ICredentialProviderCredentialEvents *pcpce);

    HRESULT (STDMETHODCALLTYPE *UnAdvise)(
        ICredentialProviderCredential2 *This);

    HRESULT (STDMETHODCALLTYPE *SetSelected)(
        ICredentialProviderCredential2 *This,
        WINBOOL *pbAutoLogon);

    HRESULT (STDMETHODCALLTYPE *SetDeselected)(
        ICredentialProviderCredential2 *This);

    HRESULT (STDMETHODCALLTYPE *GetFieldState)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_STATE *pcpfs,
        CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE *pcpfis);

    HRESULT (STDMETHODCALLTYPE *GetStringValue)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        LPWSTR *ppsz);

    HRESULT (STDMETHODCALLTYPE *GetBitmapValue)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        HBITMAP *phbmp);

    HRESULT (STDMETHODCALLTYPE *GetCheckboxValue)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        WINBOOL *pbChecked,
        LPWSTR *ppszLabel);

    HRESULT (STDMETHODCALLTYPE *GetSubmitButtonValue)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        DWORD *pdwAdjacentTo);

    HRESULT (STDMETHODCALLTYPE *GetComboBoxValueCount)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        DWORD *pcItems,
        DWORD *pdwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *GetComboBoxValueAt)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        DWORD dwItem,
        LPWSTR *ppszItem);

    HRESULT (STDMETHODCALLTYPE *SetStringValue)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        LPCWSTR psz);

    HRESULT (STDMETHODCALLTYPE *SetCheckboxValue)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        WINBOOL bChecked);

    HRESULT (STDMETHODCALLTYPE *SetComboBoxSelectedValue)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID,
        DWORD dwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *CommandLinkClicked)(
        ICredentialProviderCredential2 *This,
        DWORD dwFieldID);

    HRESULT (STDMETHODCALLTYPE *GetSerialization)(
        ICredentialProviderCredential2 *This,
        CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE *pcpgsr,
        CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon);

    HRESULT (STDMETHODCALLTYPE *ReportResult)(
        ICredentialProviderCredential2 *This,
        NTSTATUS ntsStatus,
        NTSTATUS ntsSubstatus,
        LPWSTR *ppszOptionalStatusText,
        CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon);

    /*** ICredentialProviderCredential2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUserSid)(
        ICredentialProviderCredential2 *This,
        LPWSTR *sid);

    END_INTERFACE
} ICredentialProviderCredential2Vtbl;

interface ICredentialProviderCredential2 {
    CONST_VTBL ICredentialProviderCredential2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderCredential2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderCredential2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderCredential2_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderCredential methods ***/
#define ICredentialProviderCredential2_Advise(This,pcpce) (This)->lpVtbl->Advise(This,pcpce)
#define ICredentialProviderCredential2_UnAdvise(This) (This)->lpVtbl->UnAdvise(This)
#define ICredentialProviderCredential2_SetSelected(This,pbAutoLogon) (This)->lpVtbl->SetSelected(This,pbAutoLogon)
#define ICredentialProviderCredential2_SetDeselected(This) (This)->lpVtbl->SetDeselected(This)
#define ICredentialProviderCredential2_GetFieldState(This,dwFieldID,pcpfs,pcpfis) (This)->lpVtbl->GetFieldState(This,dwFieldID,pcpfs,pcpfis)
#define ICredentialProviderCredential2_GetStringValue(This,dwFieldID,ppsz) (This)->lpVtbl->GetStringValue(This,dwFieldID,ppsz)
#define ICredentialProviderCredential2_GetBitmapValue(This,dwFieldID,phbmp) (This)->lpVtbl->GetBitmapValue(This,dwFieldID,phbmp)
#define ICredentialProviderCredential2_GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel) (This)->lpVtbl->GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel)
#define ICredentialProviderCredential2_GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo) (This)->lpVtbl->GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo)
#define ICredentialProviderCredential2_GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem) (This)->lpVtbl->GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem)
#define ICredentialProviderCredential2_GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem) (This)->lpVtbl->GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem)
#define ICredentialProviderCredential2_SetStringValue(This,dwFieldID,psz) (This)->lpVtbl->SetStringValue(This,dwFieldID,psz)
#define ICredentialProviderCredential2_SetCheckboxValue(This,dwFieldID,bChecked) (This)->lpVtbl->SetCheckboxValue(This,dwFieldID,bChecked)
#define ICredentialProviderCredential2_SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem) (This)->lpVtbl->SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem)
#define ICredentialProviderCredential2_CommandLinkClicked(This,dwFieldID) (This)->lpVtbl->CommandLinkClicked(This,dwFieldID)
#define ICredentialProviderCredential2_GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon) (This)->lpVtbl->GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon)
#define ICredentialProviderCredential2_ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon) (This)->lpVtbl->ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon)
/*** ICredentialProviderCredential2 methods ***/
#define ICredentialProviderCredential2_GetUserSid(This,sid) (This)->lpVtbl->GetUserSid(This,sid)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderCredential2_QueryInterface(ICredentialProviderCredential2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderCredential2_AddRef(ICredentialProviderCredential2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderCredential2_Release(ICredentialProviderCredential2* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderCredential methods ***/
static inline HRESULT ICredentialProviderCredential2_Advise(ICredentialProviderCredential2* This,ICredentialProviderCredentialEvents *pcpce) {
    return This->lpVtbl->Advise(This,pcpce);
}
static inline HRESULT ICredentialProviderCredential2_UnAdvise(ICredentialProviderCredential2* This) {
    return This->lpVtbl->UnAdvise(This);
}
static inline HRESULT ICredentialProviderCredential2_SetSelected(ICredentialProviderCredential2* This,WINBOOL *pbAutoLogon) {
    return This->lpVtbl->SetSelected(This,pbAutoLogon);
}
static inline HRESULT ICredentialProviderCredential2_SetDeselected(ICredentialProviderCredential2* This) {
    return This->lpVtbl->SetDeselected(This);
}
static inline HRESULT ICredentialProviderCredential2_GetFieldState(ICredentialProviderCredential2* This,DWORD dwFieldID,CREDENTIAL_PROVIDER_FIELD_STATE *pcpfs,CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE *pcpfis) {
    return This->lpVtbl->GetFieldState(This,dwFieldID,pcpfs,pcpfis);
}
static inline HRESULT ICredentialProviderCredential2_GetStringValue(ICredentialProviderCredential2* This,DWORD dwFieldID,LPWSTR *ppsz) {
    return This->lpVtbl->GetStringValue(This,dwFieldID,ppsz);
}
static inline HRESULT ICredentialProviderCredential2_GetBitmapValue(ICredentialProviderCredential2* This,DWORD dwFieldID,HBITMAP *phbmp) {
    return This->lpVtbl->GetBitmapValue(This,dwFieldID,phbmp);
}
static inline HRESULT ICredentialProviderCredential2_GetCheckboxValue(ICredentialProviderCredential2* This,DWORD dwFieldID,WINBOOL *pbChecked,LPWSTR *ppszLabel) {
    return This->lpVtbl->GetCheckboxValue(This,dwFieldID,pbChecked,ppszLabel);
}
static inline HRESULT ICredentialProviderCredential2_GetSubmitButtonValue(ICredentialProviderCredential2* This,DWORD dwFieldID,DWORD *pdwAdjacentTo) {
    return This->lpVtbl->GetSubmitButtonValue(This,dwFieldID,pdwAdjacentTo);
}
static inline HRESULT ICredentialProviderCredential2_GetComboBoxValueCount(ICredentialProviderCredential2* This,DWORD dwFieldID,DWORD *pcItems,DWORD *pdwSelectedItem) {
    return This->lpVtbl->GetComboBoxValueCount(This,dwFieldID,pcItems,pdwSelectedItem);
}
static inline HRESULT ICredentialProviderCredential2_GetComboBoxValueAt(ICredentialProviderCredential2* This,DWORD dwFieldID,DWORD dwItem,LPWSTR *ppszItem) {
    return This->lpVtbl->GetComboBoxValueAt(This,dwFieldID,dwItem,ppszItem);
}
static inline HRESULT ICredentialProviderCredential2_SetStringValue(ICredentialProviderCredential2* This,DWORD dwFieldID,LPCWSTR psz) {
    return This->lpVtbl->SetStringValue(This,dwFieldID,psz);
}
static inline HRESULT ICredentialProviderCredential2_SetCheckboxValue(ICredentialProviderCredential2* This,DWORD dwFieldID,WINBOOL bChecked) {
    return This->lpVtbl->SetCheckboxValue(This,dwFieldID,bChecked);
}
static inline HRESULT ICredentialProviderCredential2_SetComboBoxSelectedValue(ICredentialProviderCredential2* This,DWORD dwFieldID,DWORD dwSelectedItem) {
    return This->lpVtbl->SetComboBoxSelectedValue(This,dwFieldID,dwSelectedItem);
}
static inline HRESULT ICredentialProviderCredential2_CommandLinkClicked(ICredentialProviderCredential2* This,DWORD dwFieldID) {
    return This->lpVtbl->CommandLinkClicked(This,dwFieldID);
}
static inline HRESULT ICredentialProviderCredential2_GetSerialization(ICredentialProviderCredential2* This,CREDENTIAL_PROVIDER_GET_SERIALIZATION_RESPONSE *pcpgsr,CREDENTIAL_PROVIDER_CREDENTIAL_SERIALIZATION *pcpcs,LPWSTR *ppszOptionalStatusText,CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) {
    return This->lpVtbl->GetSerialization(This,pcpgsr,pcpcs,ppszOptionalStatusText,pcpsiOptionalStatusIcon);
}
static inline HRESULT ICredentialProviderCredential2_ReportResult(ICredentialProviderCredential2* This,NTSTATUS ntsStatus,NTSTATUS ntsSubstatus,LPWSTR *ppszOptionalStatusText,CREDENTIAL_PROVIDER_STATUS_ICON *pcpsiOptionalStatusIcon) {
    return This->lpVtbl->ReportResult(This,ntsStatus,ntsSubstatus,ppszOptionalStatusText,pcpsiOptionalStatusIcon);
}
/*** ICredentialProviderCredential2 methods ***/
static inline HRESULT ICredentialProviderCredential2_GetUserSid(ICredentialProviderCredential2* This,LPWSTR *sid) {
    return This->lpVtbl->GetUserSid(This,sid);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderCredential2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICredentialProviderCredentialWithFieldOptions interface
 */
#ifndef __ICredentialProviderCredentialWithFieldOptions_INTERFACE_DEFINED__
#define __ICredentialProviderCredentialWithFieldOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderCredentialWithFieldOptions, 0xdbc6fb30, 0xc843, 0x49e3, 0xa6,0x45, 0x57,0x3e,0x6f,0x39,0x44,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dbc6fb30-c843-49e3-a645-573e6f39446a")
ICredentialProviderCredentialWithFieldOptions : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetFieldOptions(
        DWORD fieldID,
        CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS *options) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderCredentialWithFieldOptions, 0xdbc6fb30, 0xc843, 0x49e3, 0xa6,0x45, 0x57,0x3e,0x6f,0x39,0x44,0x6a)
#endif
#else
typedef struct ICredentialProviderCredentialWithFieldOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderCredentialWithFieldOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderCredentialWithFieldOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderCredentialWithFieldOptions *This);

    /*** ICredentialProviderCredentialWithFieldOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFieldOptions)(
        ICredentialProviderCredentialWithFieldOptions *This,
        DWORD fieldID,
        CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS *options);

    END_INTERFACE
} ICredentialProviderCredentialWithFieldOptionsVtbl;

interface ICredentialProviderCredentialWithFieldOptions {
    CONST_VTBL ICredentialProviderCredentialWithFieldOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderCredentialWithFieldOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderCredentialWithFieldOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderCredentialWithFieldOptions_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderCredentialWithFieldOptions methods ***/
#define ICredentialProviderCredentialWithFieldOptions_GetFieldOptions(This,fieldID,options) (This)->lpVtbl->GetFieldOptions(This,fieldID,options)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderCredentialWithFieldOptions_QueryInterface(ICredentialProviderCredentialWithFieldOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderCredentialWithFieldOptions_AddRef(ICredentialProviderCredentialWithFieldOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderCredentialWithFieldOptions_Release(ICredentialProviderCredentialWithFieldOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderCredentialWithFieldOptions methods ***/
static inline HRESULT ICredentialProviderCredentialWithFieldOptions_GetFieldOptions(ICredentialProviderCredentialWithFieldOptions* This,DWORD fieldID,CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS *options) {
    return This->lpVtbl->GetFieldOptions(This,fieldID,options);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderCredentialWithFieldOptions_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICredentialProviderCredentialEvents2 interface
 */
#ifndef __ICredentialProviderCredentialEvents2_INTERFACE_DEFINED__
#define __ICredentialProviderCredentialEvents2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderCredentialEvents2, 0xb53c00b6, 0x9922, 0x4b78, 0xb1,0xf4, 0xdd,0xfe,0x77,0x4d,0xc3,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b53c00b6-9922-4b78-b1f4-ddfe774dc39b")
ICredentialProviderCredentialEvents2 : public ICredentialProviderCredentialEvents
{
    virtual HRESULT STDMETHODCALLTYPE BeginFieldUpdates(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndFieldUpdates(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFieldOptions(
        ICredentialProviderCredential *credential,
        DWORD fieldID,
        CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS options) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderCredentialEvents2, 0xb53c00b6, 0x9922, 0x4b78, 0xb1,0xf4, 0xdd,0xfe,0x77,0x4d,0xc3,0x9b)
#endif
#else
typedef struct ICredentialProviderCredentialEvents2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderCredentialEvents2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderCredentialEvents2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderCredentialEvents2 *This);

    /*** ICredentialProviderCredentialEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFieldState)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_STATE cpfs);

    HRESULT (STDMETHODCALLTYPE *SetFieldInteractiveState)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE cpfis);

    HRESULT (STDMETHODCALLTYPE *SetFieldString)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        LPCWSTR psz);

    HRESULT (STDMETHODCALLTYPE *SetFieldCheckbox)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        WINBOOL bChecked,
        LPCWSTR pszLabel);

    HRESULT (STDMETHODCALLTYPE *SetFieldBitmap)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        HBITMAP hbmp);

    HRESULT (STDMETHODCALLTYPE *SetFieldComboBoxSelectedItem)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwSelectedItem);

    HRESULT (STDMETHODCALLTYPE *DeleteFieldComboBoxItem)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwItem);

    HRESULT (STDMETHODCALLTYPE *AppendFieldComboBoxItem)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        LPCWSTR pszItem);

    HRESULT (STDMETHODCALLTYPE *SetFieldSubmitButton)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *pcpc,
        DWORD dwFieldID,
        DWORD dwAdjacentTo);

    HRESULT (STDMETHODCALLTYPE *OnCreatingWindow)(
        ICredentialProviderCredentialEvents2 *This,
        HWND *phwndOwner);

    /*** ICredentialProviderCredentialEvents2 methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginFieldUpdates)(
        ICredentialProviderCredentialEvents2 *This);

    HRESULT (STDMETHODCALLTYPE *EndFieldUpdates)(
        ICredentialProviderCredentialEvents2 *This);

    HRESULT (STDMETHODCALLTYPE *SetFieldOptions)(
        ICredentialProviderCredentialEvents2 *This,
        ICredentialProviderCredential *credential,
        DWORD fieldID,
        CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS options);

    END_INTERFACE
} ICredentialProviderCredentialEvents2Vtbl;

interface ICredentialProviderCredentialEvents2 {
    CONST_VTBL ICredentialProviderCredentialEvents2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderCredentialEvents2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderCredentialEvents2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderCredentialEvents2_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderCredentialEvents methods ***/
#define ICredentialProviderCredentialEvents2_SetFieldState(This,pcpc,dwFieldID,cpfs) (This)->lpVtbl->SetFieldState(This,pcpc,dwFieldID,cpfs)
#define ICredentialProviderCredentialEvents2_SetFieldInteractiveState(This,pcpc,dwFieldID,cpfis) (This)->lpVtbl->SetFieldInteractiveState(This,pcpc,dwFieldID,cpfis)
#define ICredentialProviderCredentialEvents2_SetFieldString(This,pcpc,dwFieldID,psz) (This)->lpVtbl->SetFieldString(This,pcpc,dwFieldID,psz)
#define ICredentialProviderCredentialEvents2_SetFieldCheckbox(This,pcpc,dwFieldID,bChecked,pszLabel) (This)->lpVtbl->SetFieldCheckbox(This,pcpc,dwFieldID,bChecked,pszLabel)
#define ICredentialProviderCredentialEvents2_SetFieldBitmap(This,pcpc,dwFieldID,hbmp) (This)->lpVtbl->SetFieldBitmap(This,pcpc,dwFieldID,hbmp)
#define ICredentialProviderCredentialEvents2_SetFieldComboBoxSelectedItem(This,pcpc,dwFieldID,dwSelectedItem) (This)->lpVtbl->SetFieldComboBoxSelectedItem(This,pcpc,dwFieldID,dwSelectedItem)
#define ICredentialProviderCredentialEvents2_DeleteFieldComboBoxItem(This,pcpc,dwFieldID,dwItem) (This)->lpVtbl->DeleteFieldComboBoxItem(This,pcpc,dwFieldID,dwItem)
#define ICredentialProviderCredentialEvents2_AppendFieldComboBoxItem(This,pcpc,dwFieldID,pszItem) (This)->lpVtbl->AppendFieldComboBoxItem(This,pcpc,dwFieldID,pszItem)
#define ICredentialProviderCredentialEvents2_SetFieldSubmitButton(This,pcpc,dwFieldID,dwAdjacentTo) (This)->lpVtbl->SetFieldSubmitButton(This,pcpc,dwFieldID,dwAdjacentTo)
#define ICredentialProviderCredentialEvents2_OnCreatingWindow(This,phwndOwner) (This)->lpVtbl->OnCreatingWindow(This,phwndOwner)
/*** ICredentialProviderCredentialEvents2 methods ***/
#define ICredentialProviderCredentialEvents2_BeginFieldUpdates(This) (This)->lpVtbl->BeginFieldUpdates(This)
#define ICredentialProviderCredentialEvents2_EndFieldUpdates(This) (This)->lpVtbl->EndFieldUpdates(This)
#define ICredentialProviderCredentialEvents2_SetFieldOptions(This,credential,fieldID,options) (This)->lpVtbl->SetFieldOptions(This,credential,fieldID,options)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderCredentialEvents2_QueryInterface(ICredentialProviderCredentialEvents2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderCredentialEvents2_AddRef(ICredentialProviderCredentialEvents2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderCredentialEvents2_Release(ICredentialProviderCredentialEvents2* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderCredentialEvents methods ***/
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldState(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,CREDENTIAL_PROVIDER_FIELD_STATE cpfs) {
    return This->lpVtbl->SetFieldState(This,pcpc,dwFieldID,cpfs);
}
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldInteractiveState(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,CREDENTIAL_PROVIDER_FIELD_INTERACTIVE_STATE cpfis) {
    return This->lpVtbl->SetFieldInteractiveState(This,pcpc,dwFieldID,cpfis);
}
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldString(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,LPCWSTR psz) {
    return This->lpVtbl->SetFieldString(This,pcpc,dwFieldID,psz);
}
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldCheckbox(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,WINBOOL bChecked,LPCWSTR pszLabel) {
    return This->lpVtbl->SetFieldCheckbox(This,pcpc,dwFieldID,bChecked,pszLabel);
}
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldBitmap(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,HBITMAP hbmp) {
    return This->lpVtbl->SetFieldBitmap(This,pcpc,dwFieldID,hbmp);
}
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldComboBoxSelectedItem(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,DWORD dwSelectedItem) {
    return This->lpVtbl->SetFieldComboBoxSelectedItem(This,pcpc,dwFieldID,dwSelectedItem);
}
static inline HRESULT ICredentialProviderCredentialEvents2_DeleteFieldComboBoxItem(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,DWORD dwItem) {
    return This->lpVtbl->DeleteFieldComboBoxItem(This,pcpc,dwFieldID,dwItem);
}
static inline HRESULT ICredentialProviderCredentialEvents2_AppendFieldComboBoxItem(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,LPCWSTR pszItem) {
    return This->lpVtbl->AppendFieldComboBoxItem(This,pcpc,dwFieldID,pszItem);
}
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldSubmitButton(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *pcpc,DWORD dwFieldID,DWORD dwAdjacentTo) {
    return This->lpVtbl->SetFieldSubmitButton(This,pcpc,dwFieldID,dwAdjacentTo);
}
static inline HRESULT ICredentialProviderCredentialEvents2_OnCreatingWindow(ICredentialProviderCredentialEvents2* This,HWND *phwndOwner) {
    return This->lpVtbl->OnCreatingWindow(This,phwndOwner);
}
/*** ICredentialProviderCredentialEvents2 methods ***/
static inline HRESULT ICredentialProviderCredentialEvents2_BeginFieldUpdates(ICredentialProviderCredentialEvents2* This) {
    return This->lpVtbl->BeginFieldUpdates(This);
}
static inline HRESULT ICredentialProviderCredentialEvents2_EndFieldUpdates(ICredentialProviderCredentialEvents2* This) {
    return This->lpVtbl->EndFieldUpdates(This);
}
static inline HRESULT ICredentialProviderCredentialEvents2_SetFieldOptions(ICredentialProviderCredentialEvents2* This,ICredentialProviderCredential *credential,DWORD fieldID,CREDENTIAL_PROVIDER_CREDENTIAL_FIELD_OPTIONS options) {
    return This->lpVtbl->SetFieldOptions(This,credential,fieldID,options);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderCredentialEvents2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICredentialProviderUser interface
 */
#ifndef __ICredentialProviderUser_INTERFACE_DEFINED__
#define __ICredentialProviderUser_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderUser, 0x13793285, 0x3ea6, 0x40fd, 0xb4,0x20, 0x15,0xf4,0x7d,0xa4,0x1f,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("13793285-3ea6-40fd-b420-15f47da41fbb")
ICredentialProviderUser : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSid(
        LPWSTR *sid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProviderID(
        GUID *providerID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringValue(
        REFPROPERTYKEY key,
        LPWSTR *stringValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        REFPROPERTYKEY key,
        PROPVARIANT *value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderUser, 0x13793285, 0x3ea6, 0x40fd, 0xb4,0x20, 0x15,0xf4,0x7d,0xa4,0x1f,0xbb)
#endif
#else
typedef struct ICredentialProviderUserVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderUser *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderUser *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderUser *This);

    /*** ICredentialProviderUser methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSid)(
        ICredentialProviderUser *This,
        LPWSTR *sid);

    HRESULT (STDMETHODCALLTYPE *GetProviderID)(
        ICredentialProviderUser *This,
        GUID *providerID);

    HRESULT (STDMETHODCALLTYPE *GetStringValue)(
        ICredentialProviderUser *This,
        REFPROPERTYKEY key,
        LPWSTR *stringValue);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        ICredentialProviderUser *This,
        REFPROPERTYKEY key,
        PROPVARIANT *value);

    END_INTERFACE
} ICredentialProviderUserVtbl;

interface ICredentialProviderUser {
    CONST_VTBL ICredentialProviderUserVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderUser_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderUser_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderUser_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderUser methods ***/
#define ICredentialProviderUser_GetSid(This,sid) (This)->lpVtbl->GetSid(This,sid)
#define ICredentialProviderUser_GetProviderID(This,providerID) (This)->lpVtbl->GetProviderID(This,providerID)
#define ICredentialProviderUser_GetStringValue(This,key,stringValue) (This)->lpVtbl->GetStringValue(This,key,stringValue)
#define ICredentialProviderUser_GetValue(This,key,value) (This)->lpVtbl->GetValue(This,key,value)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderUser_QueryInterface(ICredentialProviderUser* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderUser_AddRef(ICredentialProviderUser* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderUser_Release(ICredentialProviderUser* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderUser methods ***/
static inline HRESULT ICredentialProviderUser_GetSid(ICredentialProviderUser* This,LPWSTR *sid) {
    return This->lpVtbl->GetSid(This,sid);
}
static inline HRESULT ICredentialProviderUser_GetProviderID(ICredentialProviderUser* This,GUID *providerID) {
    return This->lpVtbl->GetProviderID(This,providerID);
}
static inline HRESULT ICredentialProviderUser_GetStringValue(ICredentialProviderUser* This,REFPROPERTYKEY key,LPWSTR *stringValue) {
    return This->lpVtbl->GetStringValue(This,key,stringValue);
}
static inline HRESULT ICredentialProviderUser_GetValue(ICredentialProviderUser* This,REFPROPERTYKEY key,PROPVARIANT *value) {
    return This->lpVtbl->GetValue(This,key,value);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderUser_INTERFACE_DEFINED__ */

DEFINE_GUID(Identity_LocalUserProvider, 0xA198529B, 0x730F, 0x4089, 0xB6, 0x46, 0xA1, 0x25, 0x57, 0xF5, 0x66, 0x5E);
/*****************************************************************************
 * ICredentialProviderUserArray interface
 */
#ifndef __ICredentialProviderUserArray_INTERFACE_DEFINED__
#define __ICredentialProviderUserArray_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderUserArray, 0x90c119ae, 0x0f18, 0x4520, 0xa1,0xf1, 0x11,0x43,0x66,0xa4,0x0f,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("90c119ae-0f18-4520-a1f1-114366a40fe8")
ICredentialProviderUserArray : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetProviderFilter(
        REFGUID guidProviderToFilterTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAccountOptions(
        CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS *credentialProviderAccountOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *userCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        DWORD userIndex,
        ICredentialProviderUser **user) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderUserArray, 0x90c119ae, 0x0f18, 0x4520, 0xa1,0xf1, 0x11,0x43,0x66,0xa4,0x0f,0xe8)
#endif
#else
typedef struct ICredentialProviderUserArrayVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderUserArray *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderUserArray *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderUserArray *This);

    /*** ICredentialProviderUserArray methods ***/
    HRESULT (STDMETHODCALLTYPE *SetProviderFilter)(
        ICredentialProviderUserArray *This,
        REFGUID guidProviderToFilterTo);

    HRESULT (STDMETHODCALLTYPE *GetAccountOptions)(
        ICredentialProviderUserArray *This,
        CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS *credentialProviderAccountOptions);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        ICredentialProviderUserArray *This,
        DWORD *userCount);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        ICredentialProviderUserArray *This,
        DWORD userIndex,
        ICredentialProviderUser **user);

    END_INTERFACE
} ICredentialProviderUserArrayVtbl;

interface ICredentialProviderUserArray {
    CONST_VTBL ICredentialProviderUserArrayVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderUserArray_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderUserArray_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderUserArray_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderUserArray methods ***/
#define ICredentialProviderUserArray_SetProviderFilter(This,guidProviderToFilterTo) (This)->lpVtbl->SetProviderFilter(This,guidProviderToFilterTo)
#define ICredentialProviderUserArray_GetAccountOptions(This,credentialProviderAccountOptions) (This)->lpVtbl->GetAccountOptions(This,credentialProviderAccountOptions)
#define ICredentialProviderUserArray_GetCount(This,userCount) (This)->lpVtbl->GetCount(This,userCount)
#define ICredentialProviderUserArray_GetAt(This,userIndex,user) (This)->lpVtbl->GetAt(This,userIndex,user)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderUserArray_QueryInterface(ICredentialProviderUserArray* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderUserArray_AddRef(ICredentialProviderUserArray* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderUserArray_Release(ICredentialProviderUserArray* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderUserArray methods ***/
static inline HRESULT ICredentialProviderUserArray_SetProviderFilter(ICredentialProviderUserArray* This,REFGUID guidProviderToFilterTo) {
    return This->lpVtbl->SetProviderFilter(This,guidProviderToFilterTo);
}
static inline HRESULT ICredentialProviderUserArray_GetAccountOptions(ICredentialProviderUserArray* This,CREDENTIAL_PROVIDER_ACCOUNT_OPTIONS *credentialProviderAccountOptions) {
    return This->lpVtbl->GetAccountOptions(This,credentialProviderAccountOptions);
}
static inline HRESULT ICredentialProviderUserArray_GetCount(ICredentialProviderUserArray* This,DWORD *userCount) {
    return This->lpVtbl->GetCount(This,userCount);
}
static inline HRESULT ICredentialProviderUserArray_GetAt(ICredentialProviderUserArray* This,DWORD userIndex,ICredentialProviderUser **user) {
    return This->lpVtbl->GetAt(This,userIndex,user);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderUserArray_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICredentialProviderSetUserArray interface
 */
#ifndef __ICredentialProviderSetUserArray_INTERFACE_DEFINED__
#define __ICredentialProviderSetUserArray_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICredentialProviderSetUserArray, 0x095c1484, 0x1c0c, 0x4388, 0x9c,0x6d, 0x50,0x0e,0x61,0xbf,0x84,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("095c1484-1c0c-4388-9c6d-500e61bf84bd")
ICredentialProviderSetUserArray : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetUserArray(
        ICredentialProviderUserArray *users) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICredentialProviderSetUserArray, 0x095c1484, 0x1c0c, 0x4388, 0x9c,0x6d, 0x50,0x0e,0x61,0xbf,0x84,0xbd)
#endif
#else
typedef struct ICredentialProviderSetUserArrayVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICredentialProviderSetUserArray *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICredentialProviderSetUserArray *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICredentialProviderSetUserArray *This);

    /*** ICredentialProviderSetUserArray methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUserArray)(
        ICredentialProviderSetUserArray *This,
        ICredentialProviderUserArray *users);

    END_INTERFACE
} ICredentialProviderSetUserArrayVtbl;

interface ICredentialProviderSetUserArray {
    CONST_VTBL ICredentialProviderSetUserArrayVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICredentialProviderSetUserArray_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICredentialProviderSetUserArray_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICredentialProviderSetUserArray_Release(This) (This)->lpVtbl->Release(This)
/*** ICredentialProviderSetUserArray methods ***/
#define ICredentialProviderSetUserArray_SetUserArray(This,users) (This)->lpVtbl->SetUserArray(This,users)
#else
/*** IUnknown methods ***/
static inline HRESULT ICredentialProviderSetUserArray_QueryInterface(ICredentialProviderSetUserArray* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICredentialProviderSetUserArray_AddRef(ICredentialProviderSetUserArray* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICredentialProviderSetUserArray_Release(ICredentialProviderSetUserArray* This) {
    return This->lpVtbl->Release(This);
}
/*** ICredentialProviderSetUserArray methods ***/
static inline HRESULT ICredentialProviderSetUserArray_SetUserArray(ICredentialProviderSetUserArray* This,ICredentialProviderUserArray *users) {
    return This->lpVtbl->SetUserArray(This,users);
}
#endif
#endif

#endif


#endif  /* __ICredentialProviderSetUserArray_INTERFACE_DEFINED__ */

#endif /* (NTDDI_VERSION >= NTDDI_WIN8) */
#ifndef __CredentialProviders_LIBRARY_DEFINED__
#define __CredentialProviders_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_CredentialProviders, 0xd545db01, 0xe522, 0x4a63, 0xaf,0x83, 0xd8,0xdd,0xf9,0x54,0x00,0x4f);

/*****************************************************************************
 * PasswordCredentialProvider coclass
 */

DEFINE_GUID(CLSID_PasswordCredentialProvider, 0x60b78e88, 0xead8, 0x445c, 0x9c,0xfd, 0x0b,0x87,0xf7,0x4e,0xa6,0xcd);

#ifdef __cplusplus
class DECLSPEC_UUID("60b78e88-ead8-445c-9cfd-0b87f74ea6cd") PasswordCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PasswordCredentialProvider, 0x60b78e88, 0xead8, 0x445c, 0x9c,0xfd, 0x0b,0x87,0xf7,0x4e,0xa6,0xcd)
#endif
#endif

/*****************************************************************************
 * V1PasswordCredentialProvider coclass
 */

DEFINE_GUID(CLSID_V1PasswordCredentialProvider, 0x6f45dc1e, 0x5384, 0x457a, 0xbc,0x13, 0x2c,0xd8,0x1b,0x0d,0x28,0xed);

#ifdef __cplusplus
class DECLSPEC_UUID("6f45dc1e-5384-457a-bc13-2cd81b0d28ed") V1PasswordCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(V1PasswordCredentialProvider, 0x6f45dc1e, 0x5384, 0x457a, 0xbc,0x13, 0x2c,0xd8,0x1b,0x0d,0x28,0xed)
#endif
#endif

/*****************************************************************************
 * PINLogonCredentialProvider coclass
 */

DEFINE_GUID(CLSID_PINLogonCredentialProvider, 0xcb82ea12, 0x9f71, 0x446d, 0x89,0xe1, 0x8d,0x09,0x24,0xe1,0x25,0x6e);

#ifdef __cplusplus
class DECLSPEC_UUID("cb82ea12-9f71-446d-89e1-8d0924e1256e") PINLogonCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PINLogonCredentialProvider, 0xcb82ea12, 0x9f71, 0x446d, 0x89,0xe1, 0x8d,0x09,0x24,0xe1,0x25,0x6e)
#endif
#endif

/*****************************************************************************
 * NPCredentialProvider coclass
 */

DEFINE_GUID(CLSID_NPCredentialProvider, 0x3dd6bec0, 0x8193, 0x4ffe, 0xae,0x25, 0xe0,0x8e,0x39,0xea,0x40,0x63);

#ifdef __cplusplus
class DECLSPEC_UUID("3dd6bec0-8193-4ffe-ae25-e08e39ea4063") NPCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NPCredentialProvider, 0x3dd6bec0, 0x8193, 0x4ffe, 0xae,0x25, 0xe0,0x8e,0x39,0xea,0x40,0x63)
#endif
#endif

/*****************************************************************************
 * SmartcardCredentialProvider coclass
 */

DEFINE_GUID(CLSID_SmartcardCredentialProvider, 0x8fd7e19c, 0x3bf7, 0x489b, 0xa7,0x2c, 0x84,0x6a,0xb3,0x67,0x8c,0x96);

#ifdef __cplusplus
class DECLSPEC_UUID("8fd7e19c-3bf7-489b-a72c-846ab3678c96") SmartcardCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SmartcardCredentialProvider, 0x8fd7e19c, 0x3bf7, 0x489b, 0xa7,0x2c, 0x84,0x6a,0xb3,0x67,0x8c,0x96)
#endif
#endif

/*****************************************************************************
 * V1SmartcardCredentialProvider coclass
 */

DEFINE_GUID(CLSID_V1SmartcardCredentialProvider, 0x8bf9a910, 0xa8ff, 0x457f, 0x99,0x9f, 0xa5,0xca,0x10,0xb4,0xa8,0x85);

#ifdef __cplusplus
class DECLSPEC_UUID("8bf9a910-a8ff-457f-999f-a5ca10b4a885") V1SmartcardCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(V1SmartcardCredentialProvider, 0x8bf9a910, 0xa8ff, 0x457f, 0x99,0x9f, 0xa5,0xca,0x10,0xb4,0xa8,0x85)
#endif
#endif

/*****************************************************************************
 * SmartcardPinProvider coclass
 */

DEFINE_GUID(CLSID_SmartcardPinProvider, 0x94596c7e, 0x3744, 0x41ce, 0x89,0x3e, 0xbb,0xf0,0x91,0x22,0xf7,0x6a);

#ifdef __cplusplus
class DECLSPEC_UUID("94596c7e-3744-41ce-893e-bbf09122f76a") SmartcardPinProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SmartcardPinProvider, 0x94596c7e, 0x3744, 0x41ce, 0x89,0x3e, 0xbb,0xf0,0x91,0x22,0xf7,0x6a)
#endif
#endif

/*****************************************************************************
 * SmartcardReaderSelectionProvider coclass
 */

DEFINE_GUID(CLSID_SmartcardReaderSelectionProvider, 0x1b283861, 0x754f, 0x4022, 0xad,0x47, 0xa5,0xea,0xaa,0x61,0x88,0x94);

#ifdef __cplusplus
class DECLSPEC_UUID("1b283861-754f-4022-ad47-a5eaaa618894") SmartcardReaderSelectionProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SmartcardReaderSelectionProvider, 0x1b283861, 0x754f, 0x4022, 0xad,0x47, 0xa5,0xea,0xaa,0x61,0x88,0x94)
#endif
#endif

/*****************************************************************************
 * SmartcardWinRTProvider coclass
 */

DEFINE_GUID(CLSID_SmartcardWinRTProvider, 0x1ee7337f, 0x85ac, 0x45e2, 0xa2,0x3c, 0x37,0xc7,0x53,0x20,0x97,0x69);

#ifdef __cplusplus
class DECLSPEC_UUID("1ee7337f-85ac-45e2-a23c-37c753209769") SmartcardWinRTProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SmartcardWinRTProvider, 0x1ee7337f, 0x85ac, 0x45e2, 0xa2,0x3c, 0x37,0xc7,0x53,0x20,0x97,0x69)
#endif
#endif

/*****************************************************************************
 * GenericCredentialProvider coclass
 */

DEFINE_GUID(CLSID_GenericCredentialProvider, 0x25cbb996, 0x92ed, 0x457e, 0xb2,0x8c, 0x47,0x74,0x08,0x4b,0xd5,0x62);

#ifdef __cplusplus
class DECLSPEC_UUID("25cbb996-92ed-457e-b28c-4774084bd562") GenericCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(GenericCredentialProvider, 0x25cbb996, 0x92ed, 0x457e, 0xb2,0x8c, 0x47,0x74,0x08,0x4b,0xd5,0x62)
#endif
#endif

/*****************************************************************************
 * RASProvider coclass
 */

DEFINE_GUID(CLSID_RASProvider, 0x5537e283, 0xb1e7, 0x4ef8, 0x9c,0x6e, 0x7a,0xb0,0xaf,0xe5,0x05,0x6d);

#ifdef __cplusplus
class DECLSPEC_UUID("5537e283-b1e7-4ef8-9c6e-7ab0afe5056d") RASProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RASProvider, 0x5537e283, 0xb1e7, 0x4ef8, 0x9c,0x6e, 0x7a,0xb0,0xaf,0xe5,0x05,0x6d)
#endif
#endif

/*****************************************************************************
 * OnexCredentialProvider coclass
 */

DEFINE_GUID(CLSID_OnexCredentialProvider, 0x07aa0886, 0xcc8d, 0x4e19, 0xa4,0x10, 0x1c,0x75,0xaf,0x68,0x6e,0x62);

#ifdef __cplusplus
class DECLSPEC_UUID("07aa0886-cc8d-4e19-a410-1c75af686e62") OnexCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(OnexCredentialProvider, 0x07aa0886, 0xcc8d, 0x4e19, 0xa4,0x10, 0x1c,0x75,0xaf,0x68,0x6e,0x62)
#endif
#endif

/*****************************************************************************
 * OnexPlapSmartcardCredentialProvider coclass
 */

DEFINE_GUID(CLSID_OnexPlapSmartcardCredentialProvider, 0x33c86cd6, 0x705f, 0x4ba1, 0x9a,0xdb, 0x67,0x07,0x0b,0x83,0x77,0x75);

#ifdef __cplusplus
class DECLSPEC_UUID("33c86cd6-705f-4ba1-9adb-67070b837775") OnexPlapSmartcardCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(OnexPlapSmartcardCredentialProvider, 0x33c86cd6, 0x705f, 0x4ba1, 0x9a,0xdb, 0x67,0x07,0x0b,0x83,0x77,0x75)
#endif
#endif

/*****************************************************************************
 * VaultProvider coclass
 */

DEFINE_GUID(CLSID_VaultProvider, 0x503739d0, 0x4c5e, 0x4cfd, 0xb3,0xba, 0xd8,0x81,0x33,0x4f,0x0d,0xf2);

#ifdef __cplusplus
class DECLSPEC_UUID("503739d0-4c5e-4cfd-b3ba-d881334f0df2") VaultProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(VaultProvider, 0x503739d0, 0x4c5e, 0x4cfd, 0xb3,0xba, 0xd8,0x81,0x33,0x4f,0x0d,0xf2)
#endif
#endif

/*****************************************************************************
 * WinBioCredentialProvider coclass
 */

DEFINE_GUID(CLSID_WinBioCredentialProvider, 0xbec09223, 0xb018, 0x416d, 0xa0,0xac, 0x52,0x39,0x71,0xb6,0x39,0xf5);

#ifdef __cplusplus
class DECLSPEC_UUID("bec09223-b018-416d-a0ac-523971b639f5") WinBioCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WinBioCredentialProvider, 0xbec09223, 0xb018, 0x416d, 0xa0,0xac, 0x52,0x39,0x71,0xb6,0x39,0xf5)
#endif
#endif

/*****************************************************************************
 * V1WinBioCredentialProvider coclass
 */

DEFINE_GUID(CLSID_V1WinBioCredentialProvider, 0xac3ac249, 0xe820, 0x4343, 0xa6,0x5b, 0x37,0x7a,0xc6,0x34,0xdc,0x09);

#ifdef __cplusplus
class DECLSPEC_UUID("ac3ac249-e820-4343-a65b-377ac634dc09") V1WinBioCredentialProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(V1WinBioCredentialProvider, 0xac3ac249, 0xe820, 0x4343, 0xa6,0x5b, 0x37,0x7a,0xc6,0x34,0xdc,0x09)
#endif
#endif

#endif /* __CredentialProviders_LIBRARY_DEFINED__ */
#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HBITMAP_UserSize     (ULONG *, ULONG, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserMarshal  (ULONG *, unsigned char *, HBITMAP *);
unsigned char * __RPC_USER HBITMAP_UserUnmarshal(ULONG *, unsigned char *, HBITMAP *);
void            __RPC_USER HBITMAP_UserFree     (ULONG *, HBITMAP *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __credentialprovider_h__ */
