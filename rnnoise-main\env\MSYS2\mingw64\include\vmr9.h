/*** Autogenerated by WIDL 10.12 from include/vmr9.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __vmr9_h__
#define __vmr9_h__

/* Forward declarations */

#ifndef __IVMRImagePresenter9_FWD_DEFINED__
#define __IVMRImagePresenter9_FWD_DEFINED__
typedef interface IVMRImagePresenter9 IVMRImagePresenter9;
#ifdef __cplusplus
interface IVMRImagePresenter9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRSurfaceAllocator9_FWD_DEFINED__
#define __IVMRSurfaceAllocator9_FWD_DEFINED__
typedef interface IVMRSurfaceAllocator9 IVMRSurfaceAllocator9;
#ifdef __cplusplus
interface IVMRSurfaceAllocator9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRSurfaceAllocatorEx9_FWD_DEFINED__
#define __IVMRSurfaceAllocatorEx9_FWD_DEFINED__
typedef interface IVMRSurfaceAllocatorEx9 IVMRSurfaceAllocatorEx9;
#ifdef __cplusplus
interface IVMRSurfaceAllocatorEx9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRSurfaceAllocatorNotify9_FWD_DEFINED__
#define __IVMRSurfaceAllocatorNotify9_FWD_DEFINED__
typedef interface IVMRSurfaceAllocatorNotify9 IVMRSurfaceAllocatorNotify9;
#ifdef __cplusplus
interface IVMRSurfaceAllocatorNotify9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRWindowlessControl9_FWD_DEFINED__
#define __IVMRWindowlessControl9_FWD_DEFINED__
typedef interface IVMRWindowlessControl9 IVMRWindowlessControl9;
#ifdef __cplusplus
interface IVMRWindowlessControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRMixerControl9_FWD_DEFINED__
#define __IVMRMixerControl9_FWD_DEFINED__
typedef interface IVMRMixerControl9 IVMRMixerControl9;
#ifdef __cplusplus
interface IVMRMixerControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRMixerBitmap9_FWD_DEFINED__
#define __IVMRMixerBitmap9_FWD_DEFINED__
typedef interface IVMRMixerBitmap9 IVMRMixerBitmap9;
#ifdef __cplusplus
interface IVMRMixerBitmap9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRSurface9_FWD_DEFINED__
#define __IVMRSurface9_FWD_DEFINED__
typedef interface IVMRSurface9 IVMRSurface9;
#ifdef __cplusplus
interface IVMRSurface9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRImagePresenterConfig9_FWD_DEFINED__
#define __IVMRImagePresenterConfig9_FWD_DEFINED__
typedef interface IVMRImagePresenterConfig9 IVMRImagePresenterConfig9;
#ifdef __cplusplus
interface IVMRImagePresenterConfig9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRVideoStreamControl9_FWD_DEFINED__
#define __IVMRVideoStreamControl9_FWD_DEFINED__
typedef interface IVMRVideoStreamControl9 IVMRVideoStreamControl9;
#ifdef __cplusplus
interface IVMRVideoStreamControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRFilterConfig9_FWD_DEFINED__
#define __IVMRFilterConfig9_FWD_DEFINED__
typedef interface IVMRFilterConfig9 IVMRFilterConfig9;
#ifdef __cplusplus
interface IVMRFilterConfig9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRAspectRatioControl9_FWD_DEFINED__
#define __IVMRAspectRatioControl9_FWD_DEFINED__
typedef interface IVMRAspectRatioControl9 IVMRAspectRatioControl9;
#ifdef __cplusplus
interface IVMRAspectRatioControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRMonitorConfig9_FWD_DEFINED__
#define __IVMRMonitorConfig9_FWD_DEFINED__
typedef interface IVMRMonitorConfig9 IVMRMonitorConfig9;
#ifdef __cplusplus
interface IVMRMonitorConfig9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRDeinterlaceControl9_FWD_DEFINED__
#define __IVMRDeinterlaceControl9_FWD_DEFINED__
typedef interface IVMRDeinterlaceControl9 IVMRDeinterlaceControl9;
#ifdef __cplusplus
interface IVMRDeinterlaceControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRImageCompositor9_FWD_DEFINED__
#define __IVMRImageCompositor9_FWD_DEFINED__
typedef interface IVMRImageCompositor9 IVMRImageCompositor9;
#ifdef __cplusplus
interface IVMRImageCompositor9;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

#if 0
#ifndef __IDirect3DSurface9_FWD_DEFINED__
#define __IDirect3DSurface9_FWD_DEFINED__
typedef interface IDirect3DSurface9 IDirect3DSurface9;
#ifdef __cplusplus
interface IDirect3DSurface9;
#endif /* __cplusplus */
#endif

#ifndef __IDirect3DDevice9_FWD_DEFINED__
#define __IDirect3DDevice9_FWD_DEFINED__
typedef interface IDirect3DDevice9 IDirect3DDevice9;
#ifdef __cplusplus
interface IDirect3DDevice9;
#endif /* __cplusplus */
#endif

typedef LONGLONG REFERENCE_TIME;
typedef DWORD D3DFORMAT;
typedef DWORD D3DPOOL;
typedef HANDLE HMONITOR;
typedef struct __WIDL_vmr9_generated_name_0000000C {
    char dummy;
} AM_MEDIA_TYPE;
typedef struct __WIDL_vmr9_generated_name_0000000D {
    char dummy;
} D3DCOLOR;
#endif
#ifndef __IVMRSurface9_FWD_DEFINED__
#define __IVMRSurface9_FWD_DEFINED__
typedef interface IVMRSurface9 IVMRSurface9;
#ifdef __cplusplus
interface IVMRSurface9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRSurfaceAllocator9_FWD_DEFINED__
#define __IVMRSurfaceAllocator9_FWD_DEFINED__
typedef interface IVMRSurfaceAllocator9 IVMRSurfaceAllocator9;
#ifdef __cplusplus
interface IVMRSurfaceAllocator9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRSurfaceAllocatorEx9_FWD_DEFINED__
#define __IVMRSurfaceAllocatorEx9_FWD_DEFINED__
typedef interface IVMRSurfaceAllocatorEx9 IVMRSurfaceAllocatorEx9;
#ifdef __cplusplus
interface IVMRSurfaceAllocatorEx9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRSurfaceAllocatorNotify9_FWD_DEFINED__
#define __IVMRSurfaceAllocatorNotify9_FWD_DEFINED__
typedef interface IVMRSurfaceAllocatorNotify9 IVMRSurfaceAllocatorNotify9;
#ifdef __cplusplus
interface IVMRSurfaceAllocatorNotify9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRImagePresenter9_FWD_DEFINED__
#define __IVMRImagePresenter9_FWD_DEFINED__
typedef interface IVMRImagePresenter9 IVMRImagePresenter9;
#ifdef __cplusplus
interface IVMRImagePresenter9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRImagePresenterConfig9_FWD_DEFINED__
#define __IVMRImagePresenterConfig9_FWD_DEFINED__
typedef interface IVMRImagePresenterConfig9 IVMRImagePresenterConfig9;
#ifdef __cplusplus
interface IVMRImagePresenterConfig9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRMonitorConfig9_FWD_DEFINED__
#define __IVMRMonitorConfig9_FWD_DEFINED__
typedef interface IVMRMonitorConfig9 IVMRMonitorConfig9;
#ifdef __cplusplus
interface IVMRMonitorConfig9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRWindowlessControl9_FWD_DEFINED__
#define __IVMRWindowlessControl9_FWD_DEFINED__
typedef interface IVMRWindowlessControl9 IVMRWindowlessControl9;
#ifdef __cplusplus
interface IVMRWindowlessControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRMixerControl9_FWD_DEFINED__
#define __IVMRMixerControl9_FWD_DEFINED__
typedef interface IVMRMixerControl9 IVMRMixerControl9;
#ifdef __cplusplus
interface IVMRMixerControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRImageCompositor9_FWD_DEFINED__
#define __IVMRImageCompositor9_FWD_DEFINED__
typedef interface IVMRImageCompositor9 IVMRImageCompositor9;
#ifdef __cplusplus
interface IVMRImageCompositor9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRMixerBitmap9_FWD_DEFINED__
#define __IVMRMixerBitmap9_FWD_DEFINED__
typedef interface IVMRMixerBitmap9 IVMRMixerBitmap9;
#ifdef __cplusplus
interface IVMRMixerBitmap9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRFilterConfig9_FWD_DEFINED__
#define __IVMRFilterConfig9_FWD_DEFINED__
typedef interface IVMRFilterConfig9 IVMRFilterConfig9;
#ifdef __cplusplus
interface IVMRFilterConfig9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRAspectRatioControl9_FWD_DEFINED__
#define __IVMRAspectRatioControl9_FWD_DEFINED__
typedef interface IVMRAspectRatioControl9 IVMRAspectRatioControl9;
#ifdef __cplusplus
interface IVMRAspectRatioControl9;
#endif /* __cplusplus */
#endif

#ifndef __IVMRVideoStreamControl9_FWD_DEFINED__
#define __IVMRVideoStreamControl9_FWD_DEFINED__
typedef interface IVMRVideoStreamControl9 IVMRVideoStreamControl9;
#ifdef __cplusplus
interface IVMRVideoStreamControl9;
#endif /* __cplusplus */
#endif

typedef enum _VMR9PresentationFlags {
    VMR9Sample_SyncPoint = 0x1,
    VMR9Sample_Preroll = 0x2,
    VMR9Sample_Discontinuity = 0x4,
    VMR9Sample_TimeValid = 0x8,
    VMR9Sample_SrcDstRectsValid = 0x10
} VMR9PresentationFlags;
typedef struct _VMR9PresentationInfo {
    DWORD dwFlags;
    IDirect3DSurface9 *lpSurf;
    REFERENCE_TIME rtStart;
    REFERENCE_TIME rtEnd;
    SIZE szAspectRatio;
    RECT rcSrc;
    RECT rcDst;
    DWORD dwReserved1;
    DWORD dwReserved2;
} VMR9PresentationInfo;
/*****************************************************************************
 * IVMRImagePresenter9 interface
 */
#ifndef __IVMRImagePresenter9_INTERFACE_DEFINED__
#define __IVMRImagePresenter9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRImagePresenter9, 0x69188c61, 0x12a3, 0x40f0, 0x8f,0xfc, 0x34,0x2e,0x7b,0x43,0x3f,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("69188c61-12a3-40f0-8ffc-342e7b433fd7")
IVMRImagePresenter9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartPresenting(
        DWORD_PTR id) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopPresenting(
        DWORD_PTR id) = 0;

    virtual HRESULT STDMETHODCALLTYPE PresentImage(
        DWORD_PTR id,
        VMR9PresentationInfo *info) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRImagePresenter9, 0x69188c61, 0x12a3, 0x40f0, 0x8f,0xfc, 0x34,0x2e,0x7b,0x43,0x3f,0xd7)
#endif
#else
typedef struct IVMRImagePresenter9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRImagePresenter9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRImagePresenter9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRImagePresenter9 *This);

    /*** IVMRImagePresenter9 methods ***/
    HRESULT (STDMETHODCALLTYPE *StartPresenting)(
        IVMRImagePresenter9 *This,
        DWORD_PTR id);

    HRESULT (STDMETHODCALLTYPE *StopPresenting)(
        IVMRImagePresenter9 *This,
        DWORD_PTR id);

    HRESULT (STDMETHODCALLTYPE *PresentImage)(
        IVMRImagePresenter9 *This,
        DWORD_PTR id,
        VMR9PresentationInfo *info);

    END_INTERFACE
} IVMRImagePresenter9Vtbl;

interface IVMRImagePresenter9 {
    CONST_VTBL IVMRImagePresenter9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRImagePresenter9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRImagePresenter9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRImagePresenter9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRImagePresenter9 methods ***/
#define IVMRImagePresenter9_StartPresenting(This,id) (This)->lpVtbl->StartPresenting(This,id)
#define IVMRImagePresenter9_StopPresenting(This,id) (This)->lpVtbl->StopPresenting(This,id)
#define IVMRImagePresenter9_PresentImage(This,id,info) (This)->lpVtbl->PresentImage(This,id,info)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRImagePresenter9_QueryInterface(IVMRImagePresenter9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRImagePresenter9_AddRef(IVMRImagePresenter9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRImagePresenter9_Release(IVMRImagePresenter9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRImagePresenter9 methods ***/
static inline HRESULT IVMRImagePresenter9_StartPresenting(IVMRImagePresenter9* This,DWORD_PTR id) {
    return This->lpVtbl->StartPresenting(This,id);
}
static inline HRESULT IVMRImagePresenter9_StopPresenting(IVMRImagePresenter9* This,DWORD_PTR id) {
    return This->lpVtbl->StopPresenting(This,id);
}
static inline HRESULT IVMRImagePresenter9_PresentImage(IVMRImagePresenter9* This,DWORD_PTR id,VMR9PresentationInfo *info) {
    return This->lpVtbl->PresentImage(This,id,info);
}
#endif
#endif

#endif


#endif  /* __IVMRImagePresenter9_INTERFACE_DEFINED__ */

typedef enum _VMR9SurfaceAllocationFlags {
    VMR9AllocFlag_3DRenderTarget = 0x1,
    VMR9AllocFlag_DXVATarget = 0x2,
    VMR9AllocFlag_TextureSurface = 0x4,
    VMR9AllocFlag_OffscreenSurface = 0x8,
    VMR9AllocFlag_RGBDynamicSwitch = 0x10,
    VMR9AllocFlag_UsageReserved = 0xe0,
    VMR9AllocFlag_UsageMask = 0xff
} VMR9SurfaceAllocationFlags;
typedef struct _VMR9AllocationInfo {
    DWORD dwFlags;
    DWORD dwWidth;
    DWORD dwHeight;
    D3DFORMAT Format;
    D3DPOOL Pool;
    DWORD MinBuffers;
    SIZE szAspectRatio;
    SIZE szNativeSize;
} VMR9AllocationInfo;
/*****************************************************************************
 * IVMRSurfaceAllocator9 interface
 */
#ifndef __IVMRSurfaceAllocator9_INTERFACE_DEFINED__
#define __IVMRSurfaceAllocator9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRSurfaceAllocator9, 0x8d5148ea, 0x3f5d, 0x46cf, 0x9d,0xf1, 0xd1,0xb8,0x96,0xee,0xdb,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8d5148ea-3f5d-46cf-9df1-d1b896eedb1f")
IVMRSurfaceAllocator9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitializeDevice(
        DWORD_PTR id,
        VMR9AllocationInfo *allocinfo,
        DWORD *numbuffers) = 0;

    virtual HRESULT STDMETHODCALLTYPE TerminateDevice(
        DWORD_PTR id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSurface(
        DWORD_PTR id,
        DWORD surfaceindex,
        DWORD flags,
        IDirect3DSurface9 **surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE AdviseNotify(
        IVMRSurfaceAllocatorNotify9 *allocnotify) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRSurfaceAllocator9, 0x8d5148ea, 0x3f5d, 0x46cf, 0x9d,0xf1, 0xd1,0xb8,0x96,0xee,0xdb,0x1f)
#endif
#else
typedef struct IVMRSurfaceAllocator9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRSurfaceAllocator9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRSurfaceAllocator9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRSurfaceAllocator9 *This);

    /*** IVMRSurfaceAllocator9 methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializeDevice)(
        IVMRSurfaceAllocator9 *This,
        DWORD_PTR id,
        VMR9AllocationInfo *allocinfo,
        DWORD *numbuffers);

    HRESULT (STDMETHODCALLTYPE *TerminateDevice)(
        IVMRSurfaceAllocator9 *This,
        DWORD_PTR id);

    HRESULT (STDMETHODCALLTYPE *GetSurface)(
        IVMRSurfaceAllocator9 *This,
        DWORD_PTR id,
        DWORD surfaceindex,
        DWORD flags,
        IDirect3DSurface9 **surface);

    HRESULT (STDMETHODCALLTYPE *AdviseNotify)(
        IVMRSurfaceAllocator9 *This,
        IVMRSurfaceAllocatorNotify9 *allocnotify);

    END_INTERFACE
} IVMRSurfaceAllocator9Vtbl;

interface IVMRSurfaceAllocator9 {
    CONST_VTBL IVMRSurfaceAllocator9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRSurfaceAllocator9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRSurfaceAllocator9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRSurfaceAllocator9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRSurfaceAllocator9 methods ***/
#define IVMRSurfaceAllocator9_InitializeDevice(This,id,allocinfo,numbuffers) (This)->lpVtbl->InitializeDevice(This,id,allocinfo,numbuffers)
#define IVMRSurfaceAllocator9_TerminateDevice(This,id) (This)->lpVtbl->TerminateDevice(This,id)
#define IVMRSurfaceAllocator9_GetSurface(This,id,surfaceindex,flags,surface) (This)->lpVtbl->GetSurface(This,id,surfaceindex,flags,surface)
#define IVMRSurfaceAllocator9_AdviseNotify(This,allocnotify) (This)->lpVtbl->AdviseNotify(This,allocnotify)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRSurfaceAllocator9_QueryInterface(IVMRSurfaceAllocator9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRSurfaceAllocator9_AddRef(IVMRSurfaceAllocator9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRSurfaceAllocator9_Release(IVMRSurfaceAllocator9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRSurfaceAllocator9 methods ***/
static inline HRESULT IVMRSurfaceAllocator9_InitializeDevice(IVMRSurfaceAllocator9* This,DWORD_PTR id,VMR9AllocationInfo *allocinfo,DWORD *numbuffers) {
    return This->lpVtbl->InitializeDevice(This,id,allocinfo,numbuffers);
}
static inline HRESULT IVMRSurfaceAllocator9_TerminateDevice(IVMRSurfaceAllocator9* This,DWORD_PTR id) {
    return This->lpVtbl->TerminateDevice(This,id);
}
static inline HRESULT IVMRSurfaceAllocator9_GetSurface(IVMRSurfaceAllocator9* This,DWORD_PTR id,DWORD surfaceindex,DWORD flags,IDirect3DSurface9 **surface) {
    return This->lpVtbl->GetSurface(This,id,surfaceindex,flags,surface);
}
static inline HRESULT IVMRSurfaceAllocator9_AdviseNotify(IVMRSurfaceAllocator9* This,IVMRSurfaceAllocatorNotify9 *allocnotify) {
    return This->lpVtbl->AdviseNotify(This,allocnotify);
}
#endif
#endif

#endif


#endif  /* __IVMRSurfaceAllocator9_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVMRSurfaceAllocatorEx9 interface
 */
#ifndef __IVMRSurfaceAllocatorEx9_INTERFACE_DEFINED__
#define __IVMRSurfaceAllocatorEx9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRSurfaceAllocatorEx9, 0x6de9a68a, 0xa928, 0x4522, 0xbf,0x57, 0x65,0x5a,0xe3,0x86,0x64,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6de9a68a-a928-4522-bf57-655ae3866456")
IVMRSurfaceAllocatorEx9 : public IVMRSurfaceAllocator9
{
    virtual HRESULT STDMETHODCALLTYPE GetSurfaceEx(
        DWORD_PTR id,
        DWORD surfaceindex,
        DWORD flags,
        IDirect3DSurface9 **surface,
        RECT *dest) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRSurfaceAllocatorEx9, 0x6de9a68a, 0xa928, 0x4522, 0xbf,0x57, 0x65,0x5a,0xe3,0x86,0x64,0x56)
#endif
#else
typedef struct IVMRSurfaceAllocatorEx9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRSurfaceAllocatorEx9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRSurfaceAllocatorEx9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRSurfaceAllocatorEx9 *This);

    /*** IVMRSurfaceAllocator9 methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializeDevice)(
        IVMRSurfaceAllocatorEx9 *This,
        DWORD_PTR id,
        VMR9AllocationInfo *allocinfo,
        DWORD *numbuffers);

    HRESULT (STDMETHODCALLTYPE *TerminateDevice)(
        IVMRSurfaceAllocatorEx9 *This,
        DWORD_PTR id);

    HRESULT (STDMETHODCALLTYPE *GetSurface)(
        IVMRSurfaceAllocatorEx9 *This,
        DWORD_PTR id,
        DWORD surfaceindex,
        DWORD flags,
        IDirect3DSurface9 **surface);

    HRESULT (STDMETHODCALLTYPE *AdviseNotify)(
        IVMRSurfaceAllocatorEx9 *This,
        IVMRSurfaceAllocatorNotify9 *allocnotify);

    /*** IVMRSurfaceAllocatorEx9 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSurfaceEx)(
        IVMRSurfaceAllocatorEx9 *This,
        DWORD_PTR id,
        DWORD surfaceindex,
        DWORD flags,
        IDirect3DSurface9 **surface,
        RECT *dest);

    END_INTERFACE
} IVMRSurfaceAllocatorEx9Vtbl;

interface IVMRSurfaceAllocatorEx9 {
    CONST_VTBL IVMRSurfaceAllocatorEx9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRSurfaceAllocatorEx9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRSurfaceAllocatorEx9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRSurfaceAllocatorEx9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRSurfaceAllocator9 methods ***/
#define IVMRSurfaceAllocatorEx9_InitializeDevice(This,id,allocinfo,numbuffers) (This)->lpVtbl->InitializeDevice(This,id,allocinfo,numbuffers)
#define IVMRSurfaceAllocatorEx9_TerminateDevice(This,id) (This)->lpVtbl->TerminateDevice(This,id)
#define IVMRSurfaceAllocatorEx9_GetSurface(This,id,surfaceindex,flags,surface) (This)->lpVtbl->GetSurface(This,id,surfaceindex,flags,surface)
#define IVMRSurfaceAllocatorEx9_AdviseNotify(This,allocnotify) (This)->lpVtbl->AdviseNotify(This,allocnotify)
/*** IVMRSurfaceAllocatorEx9 methods ***/
#define IVMRSurfaceAllocatorEx9_GetSurfaceEx(This,id,surfaceindex,flags,surface,dest) (This)->lpVtbl->GetSurfaceEx(This,id,surfaceindex,flags,surface,dest)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRSurfaceAllocatorEx9_QueryInterface(IVMRSurfaceAllocatorEx9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRSurfaceAllocatorEx9_AddRef(IVMRSurfaceAllocatorEx9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRSurfaceAllocatorEx9_Release(IVMRSurfaceAllocatorEx9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRSurfaceAllocator9 methods ***/
static inline HRESULT IVMRSurfaceAllocatorEx9_InitializeDevice(IVMRSurfaceAllocatorEx9* This,DWORD_PTR id,VMR9AllocationInfo *allocinfo,DWORD *numbuffers) {
    return This->lpVtbl->InitializeDevice(This,id,allocinfo,numbuffers);
}
static inline HRESULT IVMRSurfaceAllocatorEx9_TerminateDevice(IVMRSurfaceAllocatorEx9* This,DWORD_PTR id) {
    return This->lpVtbl->TerminateDevice(This,id);
}
static inline HRESULT IVMRSurfaceAllocatorEx9_GetSurface(IVMRSurfaceAllocatorEx9* This,DWORD_PTR id,DWORD surfaceindex,DWORD flags,IDirect3DSurface9 **surface) {
    return This->lpVtbl->GetSurface(This,id,surfaceindex,flags,surface);
}
static inline HRESULT IVMRSurfaceAllocatorEx9_AdviseNotify(IVMRSurfaceAllocatorEx9* This,IVMRSurfaceAllocatorNotify9 *allocnotify) {
    return This->lpVtbl->AdviseNotify(This,allocnotify);
}
/*** IVMRSurfaceAllocatorEx9 methods ***/
static inline HRESULT IVMRSurfaceAllocatorEx9_GetSurfaceEx(IVMRSurfaceAllocatorEx9* This,DWORD_PTR id,DWORD surfaceindex,DWORD flags,IDirect3DSurface9 **surface,RECT *dest) {
    return This->lpVtbl->GetSurfaceEx(This,id,surfaceindex,flags,surface,dest);
}
#endif
#endif

#endif


#endif  /* __IVMRSurfaceAllocatorEx9_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVMRSurfaceAllocatorNotify9 interface
 */
#ifndef __IVMRSurfaceAllocatorNotify9_INTERFACE_DEFINED__
#define __IVMRSurfaceAllocatorNotify9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRSurfaceAllocatorNotify9, 0xdca3f5df, 0xbb3a, 0x4d03, 0xbd,0x81, 0x84,0x61,0x4b,0xfb,0xfa,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dca3f5df-bb3a-4d03-bd81-84614bfbfa0c")
IVMRSurfaceAllocatorNotify9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseSurfaceAllocator(
        DWORD_PTR id,
        IVMRSurfaceAllocator9 *alloc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetD3DDevice(
        IDirect3DDevice9 *device,
        HMONITOR monitor) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChangeD3DDevice(
        IDirect3DDevice9 *device,
        HMONITOR monitor) = 0;

    virtual HRESULT STDMETHODCALLTYPE AllocateSurfaceHelper(
        VMR9AllocationInfo *allocinfo,
        DWORD *numbuffers,
        IDirect3DSurface9 **surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyEvent(
        LONG code,
        LONG_PTR param1,
        LONG_PTR param2) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRSurfaceAllocatorNotify9, 0xdca3f5df, 0xbb3a, 0x4d03, 0xbd,0x81, 0x84,0x61,0x4b,0xfb,0xfa,0x0c)
#endif
#else
typedef struct IVMRSurfaceAllocatorNotify9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRSurfaceAllocatorNotify9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRSurfaceAllocatorNotify9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRSurfaceAllocatorNotify9 *This);

    /*** IVMRSurfaceAllocatorNotify9 methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseSurfaceAllocator)(
        IVMRSurfaceAllocatorNotify9 *This,
        DWORD_PTR id,
        IVMRSurfaceAllocator9 *alloc);

    HRESULT (STDMETHODCALLTYPE *SetD3DDevice)(
        IVMRSurfaceAllocatorNotify9 *This,
        IDirect3DDevice9 *device,
        HMONITOR monitor);

    HRESULT (STDMETHODCALLTYPE *ChangeD3DDevice)(
        IVMRSurfaceAllocatorNotify9 *This,
        IDirect3DDevice9 *device,
        HMONITOR monitor);

    HRESULT (STDMETHODCALLTYPE *AllocateSurfaceHelper)(
        IVMRSurfaceAllocatorNotify9 *This,
        VMR9AllocationInfo *allocinfo,
        DWORD *numbuffers,
        IDirect3DSurface9 **surface);

    HRESULT (STDMETHODCALLTYPE *NotifyEvent)(
        IVMRSurfaceAllocatorNotify9 *This,
        LONG code,
        LONG_PTR param1,
        LONG_PTR param2);

    END_INTERFACE
} IVMRSurfaceAllocatorNotify9Vtbl;

interface IVMRSurfaceAllocatorNotify9 {
    CONST_VTBL IVMRSurfaceAllocatorNotify9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRSurfaceAllocatorNotify9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRSurfaceAllocatorNotify9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRSurfaceAllocatorNotify9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRSurfaceAllocatorNotify9 methods ***/
#define IVMRSurfaceAllocatorNotify9_AdviseSurfaceAllocator(This,id,alloc) (This)->lpVtbl->AdviseSurfaceAllocator(This,id,alloc)
#define IVMRSurfaceAllocatorNotify9_SetD3DDevice(This,device,monitor) (This)->lpVtbl->SetD3DDevice(This,device,monitor)
#define IVMRSurfaceAllocatorNotify9_ChangeD3DDevice(This,device,monitor) (This)->lpVtbl->ChangeD3DDevice(This,device,monitor)
#define IVMRSurfaceAllocatorNotify9_AllocateSurfaceHelper(This,allocinfo,numbuffers,surface) (This)->lpVtbl->AllocateSurfaceHelper(This,allocinfo,numbuffers,surface)
#define IVMRSurfaceAllocatorNotify9_NotifyEvent(This,code,param1,param2) (This)->lpVtbl->NotifyEvent(This,code,param1,param2)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRSurfaceAllocatorNotify9_QueryInterface(IVMRSurfaceAllocatorNotify9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRSurfaceAllocatorNotify9_AddRef(IVMRSurfaceAllocatorNotify9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRSurfaceAllocatorNotify9_Release(IVMRSurfaceAllocatorNotify9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRSurfaceAllocatorNotify9 methods ***/
static inline HRESULT IVMRSurfaceAllocatorNotify9_AdviseSurfaceAllocator(IVMRSurfaceAllocatorNotify9* This,DWORD_PTR id,IVMRSurfaceAllocator9 *alloc) {
    return This->lpVtbl->AdviseSurfaceAllocator(This,id,alloc);
}
static inline HRESULT IVMRSurfaceAllocatorNotify9_SetD3DDevice(IVMRSurfaceAllocatorNotify9* This,IDirect3DDevice9 *device,HMONITOR monitor) {
    return This->lpVtbl->SetD3DDevice(This,device,monitor);
}
static inline HRESULT IVMRSurfaceAllocatorNotify9_ChangeD3DDevice(IVMRSurfaceAllocatorNotify9* This,IDirect3DDevice9 *device,HMONITOR monitor) {
    return This->lpVtbl->ChangeD3DDevice(This,device,monitor);
}
static inline HRESULT IVMRSurfaceAllocatorNotify9_AllocateSurfaceHelper(IVMRSurfaceAllocatorNotify9* This,VMR9AllocationInfo *allocinfo,DWORD *numbuffers,IDirect3DSurface9 **surface) {
    return This->lpVtbl->AllocateSurfaceHelper(This,allocinfo,numbuffers,surface);
}
static inline HRESULT IVMRSurfaceAllocatorNotify9_NotifyEvent(IVMRSurfaceAllocatorNotify9* This,LONG code,LONG_PTR param1,LONG_PTR param2) {
    return This->lpVtbl->NotifyEvent(This,code,param1,param2);
}
#endif
#endif

#endif


#endif  /* __IVMRSurfaceAllocatorNotify9_INTERFACE_DEFINED__ */

typedef enum _VMR9AspectRatioMode {
    VMR9ARMode_None = 0,
    VMR9ARMode_LetterBox = 1
} VMR9AspectRatioMode;
/*****************************************************************************
 * IVMRWindowlessControl9 interface
 */
#ifndef __IVMRWindowlessControl9_INTERFACE_DEFINED__
#define __IVMRWindowlessControl9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRWindowlessControl9, 0x8f537d09, 0xf85e, 0x4414, 0xb2,0x3b, 0x50,0x2e,0x54,0xc7,0x99,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f537d09-f85e-4414-b23b-502e54c79927")
IVMRWindowlessControl9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNativeVideoSize(
        LONG *width,
        LONG *height,
        LONG *arwidth,
        LONG *arheight) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMinIdealVideoSize(
        LONG *width,
        LONG *height) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxIdealVideoSize(
        LONG *width,
        LONG *height) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVideoPosition(
        const RECT *source,
        const RECT *dest) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoPosition(
        RECT *source,
        RECT *dest) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAspectRatioMode(
        DWORD *mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAspectRatioMode(
        DWORD mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVideoClippingWindow(
        HWND hwnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE RepaintVideo(
        HWND hwnd,
        HDC hdc) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisplayModeChanged(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentImage(
        BYTE **dib) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBorderColor(
        COLORREF color) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBorderColor(
        COLORREF *color) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRWindowlessControl9, 0x8f537d09, 0xf85e, 0x4414, 0xb2,0x3b, 0x50,0x2e,0x54,0xc7,0x99,0x27)
#endif
#else
typedef struct IVMRWindowlessControl9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRWindowlessControl9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRWindowlessControl9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRWindowlessControl9 *This);

    /*** IVMRWindowlessControl9 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNativeVideoSize)(
        IVMRWindowlessControl9 *This,
        LONG *width,
        LONG *height,
        LONG *arwidth,
        LONG *arheight);

    HRESULT (STDMETHODCALLTYPE *GetMinIdealVideoSize)(
        IVMRWindowlessControl9 *This,
        LONG *width,
        LONG *height);

    HRESULT (STDMETHODCALLTYPE *GetMaxIdealVideoSize)(
        IVMRWindowlessControl9 *This,
        LONG *width,
        LONG *height);

    HRESULT (STDMETHODCALLTYPE *SetVideoPosition)(
        IVMRWindowlessControl9 *This,
        const RECT *source,
        const RECT *dest);

    HRESULT (STDMETHODCALLTYPE *GetVideoPosition)(
        IVMRWindowlessControl9 *This,
        RECT *source,
        RECT *dest);

    HRESULT (STDMETHODCALLTYPE *GetAspectRatioMode)(
        IVMRWindowlessControl9 *This,
        DWORD *mode);

    HRESULT (STDMETHODCALLTYPE *SetAspectRatioMode)(
        IVMRWindowlessControl9 *This,
        DWORD mode);

    HRESULT (STDMETHODCALLTYPE *SetVideoClippingWindow)(
        IVMRWindowlessControl9 *This,
        HWND hwnd);

    HRESULT (STDMETHODCALLTYPE *RepaintVideo)(
        IVMRWindowlessControl9 *This,
        HWND hwnd,
        HDC hdc);

    HRESULT (STDMETHODCALLTYPE *DisplayModeChanged)(
        IVMRWindowlessControl9 *This);

    HRESULT (STDMETHODCALLTYPE *GetCurrentImage)(
        IVMRWindowlessControl9 *This,
        BYTE **dib);

    HRESULT (STDMETHODCALLTYPE *SetBorderColor)(
        IVMRWindowlessControl9 *This,
        COLORREF color);

    HRESULT (STDMETHODCALLTYPE *GetBorderColor)(
        IVMRWindowlessControl9 *This,
        COLORREF *color);

    END_INTERFACE
} IVMRWindowlessControl9Vtbl;

interface IVMRWindowlessControl9 {
    CONST_VTBL IVMRWindowlessControl9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRWindowlessControl9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRWindowlessControl9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRWindowlessControl9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRWindowlessControl9 methods ***/
#define IVMRWindowlessControl9_GetNativeVideoSize(This,width,height,arwidth,arheight) (This)->lpVtbl->GetNativeVideoSize(This,width,height,arwidth,arheight)
#define IVMRWindowlessControl9_GetMinIdealVideoSize(This,width,height) (This)->lpVtbl->GetMinIdealVideoSize(This,width,height)
#define IVMRWindowlessControl9_GetMaxIdealVideoSize(This,width,height) (This)->lpVtbl->GetMaxIdealVideoSize(This,width,height)
#define IVMRWindowlessControl9_SetVideoPosition(This,source,dest) (This)->lpVtbl->SetVideoPosition(This,source,dest)
#define IVMRWindowlessControl9_GetVideoPosition(This,source,dest) (This)->lpVtbl->GetVideoPosition(This,source,dest)
#define IVMRWindowlessControl9_GetAspectRatioMode(This,mode) (This)->lpVtbl->GetAspectRatioMode(This,mode)
#define IVMRWindowlessControl9_SetAspectRatioMode(This,mode) (This)->lpVtbl->SetAspectRatioMode(This,mode)
#define IVMRWindowlessControl9_SetVideoClippingWindow(This,hwnd) (This)->lpVtbl->SetVideoClippingWindow(This,hwnd)
#define IVMRWindowlessControl9_RepaintVideo(This,hwnd,hdc) (This)->lpVtbl->RepaintVideo(This,hwnd,hdc)
#define IVMRWindowlessControl9_DisplayModeChanged(This) (This)->lpVtbl->DisplayModeChanged(This)
#define IVMRWindowlessControl9_GetCurrentImage(This,dib) (This)->lpVtbl->GetCurrentImage(This,dib)
#define IVMRWindowlessControl9_SetBorderColor(This,color) (This)->lpVtbl->SetBorderColor(This,color)
#define IVMRWindowlessControl9_GetBorderColor(This,color) (This)->lpVtbl->GetBorderColor(This,color)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRWindowlessControl9_QueryInterface(IVMRWindowlessControl9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRWindowlessControl9_AddRef(IVMRWindowlessControl9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRWindowlessControl9_Release(IVMRWindowlessControl9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRWindowlessControl9 methods ***/
static inline HRESULT IVMRWindowlessControl9_GetNativeVideoSize(IVMRWindowlessControl9* This,LONG *width,LONG *height,LONG *arwidth,LONG *arheight) {
    return This->lpVtbl->GetNativeVideoSize(This,width,height,arwidth,arheight);
}
static inline HRESULT IVMRWindowlessControl9_GetMinIdealVideoSize(IVMRWindowlessControl9* This,LONG *width,LONG *height) {
    return This->lpVtbl->GetMinIdealVideoSize(This,width,height);
}
static inline HRESULT IVMRWindowlessControl9_GetMaxIdealVideoSize(IVMRWindowlessControl9* This,LONG *width,LONG *height) {
    return This->lpVtbl->GetMaxIdealVideoSize(This,width,height);
}
static inline HRESULT IVMRWindowlessControl9_SetVideoPosition(IVMRWindowlessControl9* This,const RECT *source,const RECT *dest) {
    return This->lpVtbl->SetVideoPosition(This,source,dest);
}
static inline HRESULT IVMRWindowlessControl9_GetVideoPosition(IVMRWindowlessControl9* This,RECT *source,RECT *dest) {
    return This->lpVtbl->GetVideoPosition(This,source,dest);
}
static inline HRESULT IVMRWindowlessControl9_GetAspectRatioMode(IVMRWindowlessControl9* This,DWORD *mode) {
    return This->lpVtbl->GetAspectRatioMode(This,mode);
}
static inline HRESULT IVMRWindowlessControl9_SetAspectRatioMode(IVMRWindowlessControl9* This,DWORD mode) {
    return This->lpVtbl->SetAspectRatioMode(This,mode);
}
static inline HRESULT IVMRWindowlessControl9_SetVideoClippingWindow(IVMRWindowlessControl9* This,HWND hwnd) {
    return This->lpVtbl->SetVideoClippingWindow(This,hwnd);
}
static inline HRESULT IVMRWindowlessControl9_RepaintVideo(IVMRWindowlessControl9* This,HWND hwnd,HDC hdc) {
    return This->lpVtbl->RepaintVideo(This,hwnd,hdc);
}
static inline HRESULT IVMRWindowlessControl9_DisplayModeChanged(IVMRWindowlessControl9* This) {
    return This->lpVtbl->DisplayModeChanged(This);
}
static inline HRESULT IVMRWindowlessControl9_GetCurrentImage(IVMRWindowlessControl9* This,BYTE **dib) {
    return This->lpVtbl->GetCurrentImage(This,dib);
}
static inline HRESULT IVMRWindowlessControl9_SetBorderColor(IVMRWindowlessControl9* This,COLORREF color) {
    return This->lpVtbl->SetBorderColor(This,color);
}
static inline HRESULT IVMRWindowlessControl9_GetBorderColor(IVMRWindowlessControl9* This,COLORREF *color) {
    return This->lpVtbl->GetBorderColor(This,color);
}
#endif
#endif

#endif


#endif  /* __IVMRWindowlessControl9_INTERFACE_DEFINED__ */

typedef enum _VMR9MixerPrefs {
    MixerPref9_NoDecimation = 0x1,
    MixerPref9_DecimateOutput = 0x2,
    MixerPref9_ARAdjustXorY = 0x4,
    MixerPref9_NonSquareMixing = 0x8,
    MixerPref9_DecimateMask = 0xf,
    MixerPref9_BiLinearFiltering = 0x10,
    MixerPref9_PointFiltering = 0x20,
    MixerPref9_AnisotropicFiltering = 0x40,
    MixerPref9_PyramidalQuadFiltering = 0x80,
    MixerPref9_GaussianQuadFiltering = 0x100,
    MixerPref9_FilteringReserved = 0xe00,
    MixerPref9_FilteringMask = 0xff0,
    MixerPref9_RenderTargetRGB = 0x1000,
    MixerPref9_RenderTargetYUV = 0x2000,
    MixerPref9_RenderTargetReserved = 0xfc000,
    MixerPref9_DynamicSwitchToBOB = 0x100000,
    MixerPref9_DynamicDecimateBy2 = 0x200000,
    MixerPref9_DynamicReserved = 0xc00000,
    MixerPref9_DynamicMask = 0xf00000
} VMR9MixerPrefs;
typedef struct _VMR9NormalizedRect {
    FLOAT left;
    FLOAT top;
    FLOAT right;
    FLOAT bottom;
} VMR9NormalizedRect;
typedef enum _VMR9ProcAmpControlFlags {
    ProcAmpControl9_Brightness = 0x1,
    ProcAmpControl9_Contrast = 0x2,
    ProcAmpControl9_Hue = 0x4,
    ProcAmpControl9_Saturation = 0x8,
    ProcAmpControl9_Mask = 0xf
} VMR9ProcAmpControlFlags;
typedef struct _VMR9ProcAmpControl {
    DWORD dwSize;
    DWORD dwFlags;
    FLOAT Brightness;
    FLOAT Contrast;
    FLOAT Hue;
    FLOAT Saturation;
} VMR9ProcAmpControl;
typedef struct _VMR9ProcAmpControlRange {
    DWORD dwSize;
    VMR9ProcAmpControlFlags dwProperty;
    FLOAT MinValue;
    FLOAT MaxValue;
    FLOAT DefaultValue;
    FLOAT StepSize;
} VMR9ProcAmpControlRange;
/*****************************************************************************
 * IVMRMixerControl9 interface
 */
#ifndef __IVMRMixerControl9_INTERFACE_DEFINED__
#define __IVMRMixerControl9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRMixerControl9, 0x1a777eaa, 0x47c8, 0x4930, 0xb2,0xc9, 0x8f,0xee,0x1c,0x1b,0x0f,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1a777eaa-47c8-4930-b2c9-8fee1c1b0f3b")
IVMRMixerControl9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetAlpha(
        DWORD streamid,
        FLOAT alpha) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlpha(
        DWORD streamid,
        FLOAT *alpha) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetZOrder(
        DWORD streamid,
        DWORD zorder) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetZOrder(
        DWORD streamid,
        DWORD *zorder) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputRect(
        DWORD streamid,
        const VMR9NormalizedRect *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputRect(
        DWORD streamid,
        VMR9NormalizedRect *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackgroundClr(
        COLORREF back) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackgroundClr(
        COLORREF *back) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMixingPrefs(
        DWORD mixingprefs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMixingPrefs(
        DWORD *mixingprefs) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProcAmpControl(
        DWORD streamid,
        VMR9ProcAmpControl *control) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProcAmpControl(
        DWORD streamid,
        VMR9ProcAmpControl *control) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProcAmpControlRange(
        DWORD streamid,
        VMR9ProcAmpControlRange *controlrange) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRMixerControl9, 0x1a777eaa, 0x47c8, 0x4930, 0xb2,0xc9, 0x8f,0xee,0x1c,0x1b,0x0f,0x3b)
#endif
#else
typedef struct IVMRMixerControl9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRMixerControl9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRMixerControl9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRMixerControl9 *This);

    /*** IVMRMixerControl9 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAlpha)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        FLOAT alpha);

    HRESULT (STDMETHODCALLTYPE *GetAlpha)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        FLOAT *alpha);

    HRESULT (STDMETHODCALLTYPE *SetZOrder)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        DWORD zorder);

    HRESULT (STDMETHODCALLTYPE *GetZOrder)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        DWORD *zorder);

    HRESULT (STDMETHODCALLTYPE *SetOutputRect)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        const VMR9NormalizedRect *rect);

    HRESULT (STDMETHODCALLTYPE *GetOutputRect)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        VMR9NormalizedRect *rect);

    HRESULT (STDMETHODCALLTYPE *SetBackgroundClr)(
        IVMRMixerControl9 *This,
        COLORREF back);

    HRESULT (STDMETHODCALLTYPE *GetBackgroundClr)(
        IVMRMixerControl9 *This,
        COLORREF *back);

    HRESULT (STDMETHODCALLTYPE *SetMixingPrefs)(
        IVMRMixerControl9 *This,
        DWORD mixingprefs);

    HRESULT (STDMETHODCALLTYPE *GetMixingPrefs)(
        IVMRMixerControl9 *This,
        DWORD *mixingprefs);

    HRESULT (STDMETHODCALLTYPE *SetProcAmpControl)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        VMR9ProcAmpControl *control);

    HRESULT (STDMETHODCALLTYPE *GetProcAmpControl)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        VMR9ProcAmpControl *control);

    HRESULT (STDMETHODCALLTYPE *GetProcAmpControlRange)(
        IVMRMixerControl9 *This,
        DWORD streamid,
        VMR9ProcAmpControlRange *controlrange);

    END_INTERFACE
} IVMRMixerControl9Vtbl;

interface IVMRMixerControl9 {
    CONST_VTBL IVMRMixerControl9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRMixerControl9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRMixerControl9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRMixerControl9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRMixerControl9 methods ***/
#define IVMRMixerControl9_SetAlpha(This,streamid,alpha) (This)->lpVtbl->SetAlpha(This,streamid,alpha)
#define IVMRMixerControl9_GetAlpha(This,streamid,alpha) (This)->lpVtbl->GetAlpha(This,streamid,alpha)
#define IVMRMixerControl9_SetZOrder(This,streamid,zorder) (This)->lpVtbl->SetZOrder(This,streamid,zorder)
#define IVMRMixerControl9_GetZOrder(This,streamid,zorder) (This)->lpVtbl->GetZOrder(This,streamid,zorder)
#define IVMRMixerControl9_SetOutputRect(This,streamid,rect) (This)->lpVtbl->SetOutputRect(This,streamid,rect)
#define IVMRMixerControl9_GetOutputRect(This,streamid,rect) (This)->lpVtbl->GetOutputRect(This,streamid,rect)
#define IVMRMixerControl9_SetBackgroundClr(This,back) (This)->lpVtbl->SetBackgroundClr(This,back)
#define IVMRMixerControl9_GetBackgroundClr(This,back) (This)->lpVtbl->GetBackgroundClr(This,back)
#define IVMRMixerControl9_SetMixingPrefs(This,mixingprefs) (This)->lpVtbl->SetMixingPrefs(This,mixingprefs)
#define IVMRMixerControl9_GetMixingPrefs(This,mixingprefs) (This)->lpVtbl->GetMixingPrefs(This,mixingprefs)
#define IVMRMixerControl9_SetProcAmpControl(This,streamid,control) (This)->lpVtbl->SetProcAmpControl(This,streamid,control)
#define IVMRMixerControl9_GetProcAmpControl(This,streamid,control) (This)->lpVtbl->GetProcAmpControl(This,streamid,control)
#define IVMRMixerControl9_GetProcAmpControlRange(This,streamid,controlrange) (This)->lpVtbl->GetProcAmpControlRange(This,streamid,controlrange)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRMixerControl9_QueryInterface(IVMRMixerControl9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRMixerControl9_AddRef(IVMRMixerControl9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRMixerControl9_Release(IVMRMixerControl9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRMixerControl9 methods ***/
static inline HRESULT IVMRMixerControl9_SetAlpha(IVMRMixerControl9* This,DWORD streamid,FLOAT alpha) {
    return This->lpVtbl->SetAlpha(This,streamid,alpha);
}
static inline HRESULT IVMRMixerControl9_GetAlpha(IVMRMixerControl9* This,DWORD streamid,FLOAT *alpha) {
    return This->lpVtbl->GetAlpha(This,streamid,alpha);
}
static inline HRESULT IVMRMixerControl9_SetZOrder(IVMRMixerControl9* This,DWORD streamid,DWORD zorder) {
    return This->lpVtbl->SetZOrder(This,streamid,zorder);
}
static inline HRESULT IVMRMixerControl9_GetZOrder(IVMRMixerControl9* This,DWORD streamid,DWORD *zorder) {
    return This->lpVtbl->GetZOrder(This,streamid,zorder);
}
static inline HRESULT IVMRMixerControl9_SetOutputRect(IVMRMixerControl9* This,DWORD streamid,const VMR9NormalizedRect *rect) {
    return This->lpVtbl->SetOutputRect(This,streamid,rect);
}
static inline HRESULT IVMRMixerControl9_GetOutputRect(IVMRMixerControl9* This,DWORD streamid,VMR9NormalizedRect *rect) {
    return This->lpVtbl->GetOutputRect(This,streamid,rect);
}
static inline HRESULT IVMRMixerControl9_SetBackgroundClr(IVMRMixerControl9* This,COLORREF back) {
    return This->lpVtbl->SetBackgroundClr(This,back);
}
static inline HRESULT IVMRMixerControl9_GetBackgroundClr(IVMRMixerControl9* This,COLORREF *back) {
    return This->lpVtbl->GetBackgroundClr(This,back);
}
static inline HRESULT IVMRMixerControl9_SetMixingPrefs(IVMRMixerControl9* This,DWORD mixingprefs) {
    return This->lpVtbl->SetMixingPrefs(This,mixingprefs);
}
static inline HRESULT IVMRMixerControl9_GetMixingPrefs(IVMRMixerControl9* This,DWORD *mixingprefs) {
    return This->lpVtbl->GetMixingPrefs(This,mixingprefs);
}
static inline HRESULT IVMRMixerControl9_SetProcAmpControl(IVMRMixerControl9* This,DWORD streamid,VMR9ProcAmpControl *control) {
    return This->lpVtbl->SetProcAmpControl(This,streamid,control);
}
static inline HRESULT IVMRMixerControl9_GetProcAmpControl(IVMRMixerControl9* This,DWORD streamid,VMR9ProcAmpControl *control) {
    return This->lpVtbl->GetProcAmpControl(This,streamid,control);
}
static inline HRESULT IVMRMixerControl9_GetProcAmpControlRange(IVMRMixerControl9* This,DWORD streamid,VMR9ProcAmpControlRange *controlrange) {
    return This->lpVtbl->GetProcAmpControlRange(This,streamid,controlrange);
}
#endif
#endif

#endif


#endif  /* __IVMRMixerControl9_INTERFACE_DEFINED__ */

typedef struct _VMR9AlphaBitmap {
    DWORD dwFlags;
    HDC hdc;
    IDirect3DSurface9 *pDDS;
    RECT rSrc;
    VMR9NormalizedRect rDest;
    FLOAT fAlpha;
    COLORREF clrSrcKey;
    DWORD dwFilterMode;
} VMR9AlphaBitmap;
typedef enum _VMR9AlphaBitmapFlags {
    VMR9AlphaBitmap_Disable = 0x1,
    VMR9AlphaBitmap_hDC = 0x2,
    VMR9AlphaBitmap_EntireDDS = 0x4,
    VMR9AlphaBitmap_SrcColorKey = 0x8,
    VMR9AlphaBitmap_SrcRect = 0x10,
    VMR9AlphaBitmap_FilterMode = 0x20
} VMR9AlphaBitmapFlags;
/*****************************************************************************
 * IVMRMixerBitmap9 interface
 */
#ifndef __IVMRMixerBitmap9_INTERFACE_DEFINED__
#define __IVMRMixerBitmap9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRMixerBitmap9, 0xced175e5, 0x1935, 0x4820, 0x81,0xbd, 0xff,0x6a,0xd0,0x0c,0x91,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ced175e5-1935-4820-81bd-ff6ad00c9108")
IVMRMixerBitmap9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetAlphaBitmap(
        const VMR9AlphaBitmap *bitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateAlphaBitmapParameters(
        const VMR9AlphaBitmap *bitmap) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlphaBitmapParameters(
        VMR9AlphaBitmap *bitmap) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRMixerBitmap9, 0xced175e5, 0x1935, 0x4820, 0x81,0xbd, 0xff,0x6a,0xd0,0x0c,0x91,0x08)
#endif
#else
typedef struct IVMRMixerBitmap9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRMixerBitmap9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRMixerBitmap9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRMixerBitmap9 *This);

    /*** IVMRMixerBitmap9 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAlphaBitmap)(
        IVMRMixerBitmap9 *This,
        const VMR9AlphaBitmap *bitmap);

    HRESULT (STDMETHODCALLTYPE *UpdateAlphaBitmapParameters)(
        IVMRMixerBitmap9 *This,
        const VMR9AlphaBitmap *bitmap);

    HRESULT (STDMETHODCALLTYPE *GetAlphaBitmapParameters)(
        IVMRMixerBitmap9 *This,
        VMR9AlphaBitmap *bitmap);

    END_INTERFACE
} IVMRMixerBitmap9Vtbl;

interface IVMRMixerBitmap9 {
    CONST_VTBL IVMRMixerBitmap9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRMixerBitmap9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRMixerBitmap9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRMixerBitmap9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRMixerBitmap9 methods ***/
#define IVMRMixerBitmap9_SetAlphaBitmap(This,bitmap) (This)->lpVtbl->SetAlphaBitmap(This,bitmap)
#define IVMRMixerBitmap9_UpdateAlphaBitmapParameters(This,bitmap) (This)->lpVtbl->UpdateAlphaBitmapParameters(This,bitmap)
#define IVMRMixerBitmap9_GetAlphaBitmapParameters(This,bitmap) (This)->lpVtbl->GetAlphaBitmapParameters(This,bitmap)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRMixerBitmap9_QueryInterface(IVMRMixerBitmap9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRMixerBitmap9_AddRef(IVMRMixerBitmap9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRMixerBitmap9_Release(IVMRMixerBitmap9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRMixerBitmap9 methods ***/
static inline HRESULT IVMRMixerBitmap9_SetAlphaBitmap(IVMRMixerBitmap9* This,const VMR9AlphaBitmap *bitmap) {
    return This->lpVtbl->SetAlphaBitmap(This,bitmap);
}
static inline HRESULT IVMRMixerBitmap9_UpdateAlphaBitmapParameters(IVMRMixerBitmap9* This,const VMR9AlphaBitmap *bitmap) {
    return This->lpVtbl->UpdateAlphaBitmapParameters(This,bitmap);
}
static inline HRESULT IVMRMixerBitmap9_GetAlphaBitmapParameters(IVMRMixerBitmap9* This,VMR9AlphaBitmap *bitmap) {
    return This->lpVtbl->GetAlphaBitmapParameters(This,bitmap);
}
#endif
#endif

#endif


#endif  /* __IVMRMixerBitmap9_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVMRSurface9 interface
 */
#ifndef __IVMRSurface9_INTERFACE_DEFINED__
#define __IVMRSurface9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRSurface9, 0xdfc581a1, 0x6e1f, 0x4c3a, 0x8d,0x0a, 0x5e,0x97,0x92,0xea,0x2a,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dfc581a1-6e1f-4c3a-8d0a-5e9792ea2afc")
IVMRSurface9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsSurfaceLocked(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockSurface(
        BYTE **surface) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockSurface(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSurface(
        IDirect3DSurface9 **surface) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRSurface9, 0xdfc581a1, 0x6e1f, 0x4c3a, 0x8d,0x0a, 0x5e,0x97,0x92,0xea,0x2a,0xfc)
#endif
#else
typedef struct IVMRSurface9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRSurface9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRSurface9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRSurface9 *This);

    /*** IVMRSurface9 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsSurfaceLocked)(
        IVMRSurface9 *This);

    HRESULT (STDMETHODCALLTYPE *LockSurface)(
        IVMRSurface9 *This,
        BYTE **surface);

    HRESULT (STDMETHODCALLTYPE *UnlockSurface)(
        IVMRSurface9 *This);

    HRESULT (STDMETHODCALLTYPE *GetSurface)(
        IVMRSurface9 *This,
        IDirect3DSurface9 **surface);

    END_INTERFACE
} IVMRSurface9Vtbl;

interface IVMRSurface9 {
    CONST_VTBL IVMRSurface9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRSurface9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRSurface9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRSurface9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRSurface9 methods ***/
#define IVMRSurface9_IsSurfaceLocked(This) (This)->lpVtbl->IsSurfaceLocked(This)
#define IVMRSurface9_LockSurface(This,surface) (This)->lpVtbl->LockSurface(This,surface)
#define IVMRSurface9_UnlockSurface(This) (This)->lpVtbl->UnlockSurface(This)
#define IVMRSurface9_GetSurface(This,surface) (This)->lpVtbl->GetSurface(This,surface)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRSurface9_QueryInterface(IVMRSurface9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRSurface9_AddRef(IVMRSurface9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRSurface9_Release(IVMRSurface9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRSurface9 methods ***/
static inline HRESULT IVMRSurface9_IsSurfaceLocked(IVMRSurface9* This) {
    return This->lpVtbl->IsSurfaceLocked(This);
}
static inline HRESULT IVMRSurface9_LockSurface(IVMRSurface9* This,BYTE **surface) {
    return This->lpVtbl->LockSurface(This,surface);
}
static inline HRESULT IVMRSurface9_UnlockSurface(IVMRSurface9* This) {
    return This->lpVtbl->UnlockSurface(This);
}
static inline HRESULT IVMRSurface9_GetSurface(IVMRSurface9* This,IDirect3DSurface9 **surface) {
    return This->lpVtbl->GetSurface(This,surface);
}
#endif
#endif

#endif


#endif  /* __IVMRSurface9_INTERFACE_DEFINED__ */

typedef enum _VMR9RenderPrefs {
    RenderPrefs9_DoNotRenderBorder = 0x1,
    RenderPrefs9_Mask = 0x1
} VMR9RenderPrefs;
/*****************************************************************************
 * IVMRImagePresenterConfig9 interface
 */
#ifndef __IVMRImagePresenterConfig9_INTERFACE_DEFINED__
#define __IVMRImagePresenterConfig9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRImagePresenterConfig9, 0x45c15cab, 0x6e22, 0x420a, 0x80,0x43, 0xae,0x1f,0x0a,0xc0,0x2c,0x7d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("45c15cab-6e22-420a-8043-ae1f0ac02c7d")
IVMRImagePresenterConfig9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetRenderingPrefs(
        DWORD renderflags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRenderingPrefs(
        DWORD *renderflags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRImagePresenterConfig9, 0x45c15cab, 0x6e22, 0x420a, 0x80,0x43, 0xae,0x1f,0x0a,0xc0,0x2c,0x7d)
#endif
#else
typedef struct IVMRImagePresenterConfig9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRImagePresenterConfig9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRImagePresenterConfig9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRImagePresenterConfig9 *This);

    /*** IVMRImagePresenterConfig9 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRenderingPrefs)(
        IVMRImagePresenterConfig9 *This,
        DWORD renderflags);

    HRESULT (STDMETHODCALLTYPE *GetRenderingPrefs)(
        IVMRImagePresenterConfig9 *This,
        DWORD *renderflags);

    END_INTERFACE
} IVMRImagePresenterConfig9Vtbl;

interface IVMRImagePresenterConfig9 {
    CONST_VTBL IVMRImagePresenterConfig9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRImagePresenterConfig9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRImagePresenterConfig9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRImagePresenterConfig9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRImagePresenterConfig9 methods ***/
#define IVMRImagePresenterConfig9_SetRenderingPrefs(This,renderflags) (This)->lpVtbl->SetRenderingPrefs(This,renderflags)
#define IVMRImagePresenterConfig9_GetRenderingPrefs(This,renderflags) (This)->lpVtbl->GetRenderingPrefs(This,renderflags)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRImagePresenterConfig9_QueryInterface(IVMRImagePresenterConfig9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRImagePresenterConfig9_AddRef(IVMRImagePresenterConfig9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRImagePresenterConfig9_Release(IVMRImagePresenterConfig9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRImagePresenterConfig9 methods ***/
static inline HRESULT IVMRImagePresenterConfig9_SetRenderingPrefs(IVMRImagePresenterConfig9* This,DWORD renderflags) {
    return This->lpVtbl->SetRenderingPrefs(This,renderflags);
}
static inline HRESULT IVMRImagePresenterConfig9_GetRenderingPrefs(IVMRImagePresenterConfig9* This,DWORD *renderflags) {
    return This->lpVtbl->GetRenderingPrefs(This,renderflags);
}
#endif
#endif

#endif


#endif  /* __IVMRImagePresenterConfig9_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVMRVideoStreamControl9 interface
 */
#ifndef __IVMRVideoStreamControl9_INTERFACE_DEFINED__
#define __IVMRVideoStreamControl9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRVideoStreamControl9, 0xd0cfe38b, 0x93e7, 0x4772, 0x89,0x57, 0x04,0x00,0xc4,0x9a,0x44,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d0cfe38b-93e7-4772-8957-0400c49a4485")
IVMRVideoStreamControl9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetStreamActiveState(
        WINBOOL active) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamActiveState(
        WINBOOL *active) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRVideoStreamControl9, 0xd0cfe38b, 0x93e7, 0x4772, 0x89,0x57, 0x04,0x00,0xc4,0x9a,0x44,0x85)
#endif
#else
typedef struct IVMRVideoStreamControl9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRVideoStreamControl9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRVideoStreamControl9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRVideoStreamControl9 *This);

    /*** IVMRVideoStreamControl9 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetStreamActiveState)(
        IVMRVideoStreamControl9 *This,
        WINBOOL active);

    HRESULT (STDMETHODCALLTYPE *GetStreamActiveState)(
        IVMRVideoStreamControl9 *This,
        WINBOOL *active);

    END_INTERFACE
} IVMRVideoStreamControl9Vtbl;

interface IVMRVideoStreamControl9 {
    CONST_VTBL IVMRVideoStreamControl9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRVideoStreamControl9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRVideoStreamControl9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRVideoStreamControl9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRVideoStreamControl9 methods ***/
#define IVMRVideoStreamControl9_SetStreamActiveState(This,active) (This)->lpVtbl->SetStreamActiveState(This,active)
#define IVMRVideoStreamControl9_GetStreamActiveState(This,active) (This)->lpVtbl->GetStreamActiveState(This,active)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRVideoStreamControl9_QueryInterface(IVMRVideoStreamControl9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRVideoStreamControl9_AddRef(IVMRVideoStreamControl9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRVideoStreamControl9_Release(IVMRVideoStreamControl9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRVideoStreamControl9 methods ***/
static inline HRESULT IVMRVideoStreamControl9_SetStreamActiveState(IVMRVideoStreamControl9* This,WINBOOL active) {
    return This->lpVtbl->SetStreamActiveState(This,active);
}
static inline HRESULT IVMRVideoStreamControl9_GetStreamActiveState(IVMRVideoStreamControl9* This,WINBOOL *active) {
    return This->lpVtbl->GetStreamActiveState(This,active);
}
#endif
#endif

#endif


#endif  /* __IVMRVideoStreamControl9_INTERFACE_DEFINED__ */

typedef enum _VMR9Mode {
    VMR9Mode_Windowed = 0x1,
    VMR9Mode_Windowless = 0x2,
    VMR9Mode_Renderless = 0x4,
    VMR9Mode_Mask = 0x7
} VMR9Mode;
/*****************************************************************************
 * IVMRFilterConfig9 interface
 */
#ifndef __IVMRFilterConfig9_INTERFACE_DEFINED__
#define __IVMRFilterConfig9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRFilterConfig9, 0x5a804648, 0x4f66, 0x4867, 0x9c,0x43, 0x4f,0x5c,0x82,0x2c,0xf1,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5a804648-4f66-4867-9c43-4f5c822cf1b8")
IVMRFilterConfig9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetImageCompositor(
        IVMRImageCompositor9 *compositor) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNumberOfStreams(
        DWORD max) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberOfStreams(
        DWORD *max) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRenderingPrefs(
        DWORD renderflags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRenderingPrefs(
        DWORD *renderflags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRenderingMode(
        DWORD mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRenderingMode(
        DWORD *mode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRFilterConfig9, 0x5a804648, 0x4f66, 0x4867, 0x9c,0x43, 0x4f,0x5c,0x82,0x2c,0xf1,0xb8)
#endif
#else
typedef struct IVMRFilterConfig9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRFilterConfig9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRFilterConfig9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRFilterConfig9 *This);

    /*** IVMRFilterConfig9 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetImageCompositor)(
        IVMRFilterConfig9 *This,
        IVMRImageCompositor9 *compositor);

    HRESULT (STDMETHODCALLTYPE *SetNumberOfStreams)(
        IVMRFilterConfig9 *This,
        DWORD max);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfStreams)(
        IVMRFilterConfig9 *This,
        DWORD *max);

    HRESULT (STDMETHODCALLTYPE *SetRenderingPrefs)(
        IVMRFilterConfig9 *This,
        DWORD renderflags);

    HRESULT (STDMETHODCALLTYPE *GetRenderingPrefs)(
        IVMRFilterConfig9 *This,
        DWORD *renderflags);

    HRESULT (STDMETHODCALLTYPE *SetRenderingMode)(
        IVMRFilterConfig9 *This,
        DWORD mode);

    HRESULT (STDMETHODCALLTYPE *GetRenderingMode)(
        IVMRFilterConfig9 *This,
        DWORD *mode);

    END_INTERFACE
} IVMRFilterConfig9Vtbl;

interface IVMRFilterConfig9 {
    CONST_VTBL IVMRFilterConfig9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRFilterConfig9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRFilterConfig9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRFilterConfig9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRFilterConfig9 methods ***/
#define IVMRFilterConfig9_SetImageCompositor(This,compositor) (This)->lpVtbl->SetImageCompositor(This,compositor)
#define IVMRFilterConfig9_SetNumberOfStreams(This,max) (This)->lpVtbl->SetNumberOfStreams(This,max)
#define IVMRFilterConfig9_GetNumberOfStreams(This,max) (This)->lpVtbl->GetNumberOfStreams(This,max)
#define IVMRFilterConfig9_SetRenderingPrefs(This,renderflags) (This)->lpVtbl->SetRenderingPrefs(This,renderflags)
#define IVMRFilterConfig9_GetRenderingPrefs(This,renderflags) (This)->lpVtbl->GetRenderingPrefs(This,renderflags)
#define IVMRFilterConfig9_SetRenderingMode(This,mode) (This)->lpVtbl->SetRenderingMode(This,mode)
#define IVMRFilterConfig9_GetRenderingMode(This,mode) (This)->lpVtbl->GetRenderingMode(This,mode)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRFilterConfig9_QueryInterface(IVMRFilterConfig9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRFilterConfig9_AddRef(IVMRFilterConfig9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRFilterConfig9_Release(IVMRFilterConfig9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRFilterConfig9 methods ***/
static inline HRESULT IVMRFilterConfig9_SetImageCompositor(IVMRFilterConfig9* This,IVMRImageCompositor9 *compositor) {
    return This->lpVtbl->SetImageCompositor(This,compositor);
}
static inline HRESULT IVMRFilterConfig9_SetNumberOfStreams(IVMRFilterConfig9* This,DWORD max) {
    return This->lpVtbl->SetNumberOfStreams(This,max);
}
static inline HRESULT IVMRFilterConfig9_GetNumberOfStreams(IVMRFilterConfig9* This,DWORD *max) {
    return This->lpVtbl->GetNumberOfStreams(This,max);
}
static inline HRESULT IVMRFilterConfig9_SetRenderingPrefs(IVMRFilterConfig9* This,DWORD renderflags) {
    return This->lpVtbl->SetRenderingPrefs(This,renderflags);
}
static inline HRESULT IVMRFilterConfig9_GetRenderingPrefs(IVMRFilterConfig9* This,DWORD *renderflags) {
    return This->lpVtbl->GetRenderingPrefs(This,renderflags);
}
static inline HRESULT IVMRFilterConfig9_SetRenderingMode(IVMRFilterConfig9* This,DWORD mode) {
    return This->lpVtbl->SetRenderingMode(This,mode);
}
static inline HRESULT IVMRFilterConfig9_GetRenderingMode(IVMRFilterConfig9* This,DWORD *mode) {
    return This->lpVtbl->GetRenderingMode(This,mode);
}
#endif
#endif

#endif


#endif  /* __IVMRFilterConfig9_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVMRAspectRatioControl9 interface
 */
#ifndef __IVMRAspectRatioControl9_INTERFACE_DEFINED__
#define __IVMRAspectRatioControl9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRAspectRatioControl9, 0x00d96c29, 0xbbde, 0x4efc, 0x99,0x01, 0xbb,0x50,0x36,0x39,0x21,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00d96c29-bbde-4efc-9901-bb5036392146")
IVMRAspectRatioControl9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetAspectRatioMode(
        DWORD *mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAspectRatioMode(
        DWORD mode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRAspectRatioControl9, 0x00d96c29, 0xbbde, 0x4efc, 0x99,0x01, 0xbb,0x50,0x36,0x39,0x21,0x46)
#endif
#else
typedef struct IVMRAspectRatioControl9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRAspectRatioControl9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRAspectRatioControl9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRAspectRatioControl9 *This);

    /*** IVMRAspectRatioControl9 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAspectRatioMode)(
        IVMRAspectRatioControl9 *This,
        DWORD *mode);

    HRESULT (STDMETHODCALLTYPE *SetAspectRatioMode)(
        IVMRAspectRatioControl9 *This,
        DWORD mode);

    END_INTERFACE
} IVMRAspectRatioControl9Vtbl;

interface IVMRAspectRatioControl9 {
    CONST_VTBL IVMRAspectRatioControl9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRAspectRatioControl9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRAspectRatioControl9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRAspectRatioControl9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRAspectRatioControl9 methods ***/
#define IVMRAspectRatioControl9_GetAspectRatioMode(This,mode) (This)->lpVtbl->GetAspectRatioMode(This,mode)
#define IVMRAspectRatioControl9_SetAspectRatioMode(This,mode) (This)->lpVtbl->SetAspectRatioMode(This,mode)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRAspectRatioControl9_QueryInterface(IVMRAspectRatioControl9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRAspectRatioControl9_AddRef(IVMRAspectRatioControl9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRAspectRatioControl9_Release(IVMRAspectRatioControl9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRAspectRatioControl9 methods ***/
static inline HRESULT IVMRAspectRatioControl9_GetAspectRatioMode(IVMRAspectRatioControl9* This,DWORD *mode) {
    return This->lpVtbl->GetAspectRatioMode(This,mode);
}
static inline HRESULT IVMRAspectRatioControl9_SetAspectRatioMode(IVMRAspectRatioControl9* This,DWORD mode) {
    return This->lpVtbl->SetAspectRatioMode(This,mode);
}
#endif
#endif

#endif


#endif  /* __IVMRAspectRatioControl9_INTERFACE_DEFINED__ */

typedef struct _VMR9MonitorInfo {
    UINT uDevID;
    RECT rcMonitor;
    HMONITOR hMon;
    DWORD dwFlags;
    WCHAR szDevice[32];
    WCHAR szDescription[512];
    LARGE_INTEGER liDriverVersion;
    DWORD dwVendorId;
    DWORD dwDeviceId;
    DWORD dwSubSysId;
    DWORD dwRevision;
} VMR9MonitorInfo;
/*****************************************************************************
 * IVMRMonitorConfig9 interface
 */
#ifndef __IVMRMonitorConfig9_INTERFACE_DEFINED__
#define __IVMRMonitorConfig9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRMonitorConfig9, 0x46c2e457, 0x8ba0, 0x4eef, 0xb8,0x0b, 0x06,0x80,0xf0,0x97,0x87,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("46c2e457-8ba0-4eef-b80b-0680f0978749")
IVMRMonitorConfig9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetMonitor(
        UINT uDev) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMonitor(
        UINT *uDev) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultMonitor(
        UINT uDev) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultMonitor(
        UINT *uDev) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAvailableMonitors(
        VMR9MonitorInfo *info,
        DWORD arraysize,
        DWORD *numdev) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRMonitorConfig9, 0x46c2e457, 0x8ba0, 0x4eef, 0xb8,0x0b, 0x06,0x80,0xf0,0x97,0x87,0x49)
#endif
#else
typedef struct IVMRMonitorConfig9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRMonitorConfig9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRMonitorConfig9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRMonitorConfig9 *This);

    /*** IVMRMonitorConfig9 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMonitor)(
        IVMRMonitorConfig9 *This,
        UINT uDev);

    HRESULT (STDMETHODCALLTYPE *GetMonitor)(
        IVMRMonitorConfig9 *This,
        UINT *uDev);

    HRESULT (STDMETHODCALLTYPE *SetDefaultMonitor)(
        IVMRMonitorConfig9 *This,
        UINT uDev);

    HRESULT (STDMETHODCALLTYPE *GetDefaultMonitor)(
        IVMRMonitorConfig9 *This,
        UINT *uDev);

    HRESULT (STDMETHODCALLTYPE *GetAvailableMonitors)(
        IVMRMonitorConfig9 *This,
        VMR9MonitorInfo *info,
        DWORD arraysize,
        DWORD *numdev);

    END_INTERFACE
} IVMRMonitorConfig9Vtbl;

interface IVMRMonitorConfig9 {
    CONST_VTBL IVMRMonitorConfig9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRMonitorConfig9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRMonitorConfig9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRMonitorConfig9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRMonitorConfig9 methods ***/
#define IVMRMonitorConfig9_SetMonitor(This,uDev) (This)->lpVtbl->SetMonitor(This,uDev)
#define IVMRMonitorConfig9_GetMonitor(This,uDev) (This)->lpVtbl->GetMonitor(This,uDev)
#define IVMRMonitorConfig9_SetDefaultMonitor(This,uDev) (This)->lpVtbl->SetDefaultMonitor(This,uDev)
#define IVMRMonitorConfig9_GetDefaultMonitor(This,uDev) (This)->lpVtbl->GetDefaultMonitor(This,uDev)
#define IVMRMonitorConfig9_GetAvailableMonitors(This,info,arraysize,numdev) (This)->lpVtbl->GetAvailableMonitors(This,info,arraysize,numdev)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRMonitorConfig9_QueryInterface(IVMRMonitorConfig9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRMonitorConfig9_AddRef(IVMRMonitorConfig9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRMonitorConfig9_Release(IVMRMonitorConfig9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRMonitorConfig9 methods ***/
static inline HRESULT IVMRMonitorConfig9_SetMonitor(IVMRMonitorConfig9* This,UINT uDev) {
    return This->lpVtbl->SetMonitor(This,uDev);
}
static inline HRESULT IVMRMonitorConfig9_GetMonitor(IVMRMonitorConfig9* This,UINT *uDev) {
    return This->lpVtbl->GetMonitor(This,uDev);
}
static inline HRESULT IVMRMonitorConfig9_SetDefaultMonitor(IVMRMonitorConfig9* This,UINT uDev) {
    return This->lpVtbl->SetDefaultMonitor(This,uDev);
}
static inline HRESULT IVMRMonitorConfig9_GetDefaultMonitor(IVMRMonitorConfig9* This,UINT *uDev) {
    return This->lpVtbl->GetDefaultMonitor(This,uDev);
}
static inline HRESULT IVMRMonitorConfig9_GetAvailableMonitors(IVMRMonitorConfig9* This,VMR9MonitorInfo *info,DWORD arraysize,DWORD *numdev) {
    return This->lpVtbl->GetAvailableMonitors(This,info,arraysize,numdev);
}
#endif
#endif

#endif


#endif  /* __IVMRMonitorConfig9_INTERFACE_DEFINED__ */

typedef enum _VMR9DeinterlacePrefs {
    DeinterlacePref9_NextBest = 0x1,
    DeinterlacePref9_BOB = 0x2,
    DeinterlacePref9_Weave = 0x4,
    DeinterlacePref9_Mask = 0x7
} VMR9DeinterlacePrefs;
typedef enum _VMR9DeinterlaceTech {
    DeinterlaceTech9_Unknown = 0,
    DeinterlaceTech9_BOBLineReplicate = 0x1,
    DeinterlaceTech9_BOBVerticalStretch = 0x2,
    DeinterlaceTech9_MedianFiltering = 0x4,
    DeinterlaceTech9_EdgeFiltering = 0x10,
    DeinterlaceTech9_FieldAdaptive = 0x20,
    DeinterlaceTech9_PixelAdaptive = 0x40,
    DeinterlaceTech9_MotionVectorSteered = 0x80
} VMR9DeinterlaceTech;
typedef struct _VMR9Frequency {
    DWORD dwNumerator;
    DWORD dwDenominator;
} VMR9Frequency;
typedef enum _VMR9_SampleFormat {
    VMR9_SampleReserved = 1,
    VMR9_SampleProgressiveFrame = 2,
    VMR9_SampleFieldInterleavedEvenFirst = 3,
    VMR9_SampleFieldInterleavedOddFirst = 4,
    VMR9_SampleFieldSingleEven = 5,
    VMR9_SampleFieldSingleOdd = 6
} VMR9_SampleFormat;
typedef struct _VMR9VideoDesc {
    DWORD dwSize;
    DWORD dwSampleWidth;
    DWORD dwSampleHeight;
    VMR9_SampleFormat SampleFormat;
    DWORD dwFourCC;
    VMR9Frequency InputSampleFreq;
    VMR9Frequency OutputFrameFreq;
} VMR9VideoDesc;
typedef struct _VMR9DeinterlaceCaps {
    DWORD dwSize;
    DWORD dwNumPreviousOutputFrames;
    DWORD dwNumForwardRefSamples;
    DWORD dwNumBackwardRefSamples;
    VMR9DeinterlaceTech DeinterlaceTechnology;
} VMR9DeinterlaceCaps;
/*****************************************************************************
 * IVMRDeinterlaceControl9 interface
 */
#ifndef __IVMRDeinterlaceControl9_INTERFACE_DEFINED__
#define __IVMRDeinterlaceControl9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRDeinterlaceControl9, 0xa215fb8d, 0x13c2, 0x4f7f, 0x99,0x3c, 0x00,0x3d,0x62,0x71,0xa4,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a215fb8d-13c2-4f7f-993c-003d6271a459")
IVMRDeinterlaceControl9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNumberOfDeinterlaceModes(
        VMR9VideoDesc *desc,
        DWORD *nummodes,
        GUID *modes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeinterlaceModeCaps(
        GUID *mode,
        VMR9VideoDesc *desc,
        VMR9DeinterlaceCaps *caps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeinterlaceMode(
        DWORD streamid,
        GUID *mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDeinterlaceMode(
        DWORD streamid,
        GUID *mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeinterlacePrefs(
        DWORD *prefs) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDeinterlacePrefs(
        DWORD prefs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetActualDeinterlaceMode(
        DWORD streamid,
        GUID *mode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRDeinterlaceControl9, 0xa215fb8d, 0x13c2, 0x4f7f, 0x99,0x3c, 0x00,0x3d,0x62,0x71,0xa4,0x59)
#endif
#else
typedef struct IVMRDeinterlaceControl9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRDeinterlaceControl9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRDeinterlaceControl9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRDeinterlaceControl9 *This);

    /*** IVMRDeinterlaceControl9 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNumberOfDeinterlaceModes)(
        IVMRDeinterlaceControl9 *This,
        VMR9VideoDesc *desc,
        DWORD *nummodes,
        GUID *modes);

    HRESULT (STDMETHODCALLTYPE *GetDeinterlaceModeCaps)(
        IVMRDeinterlaceControl9 *This,
        GUID *mode,
        VMR9VideoDesc *desc,
        VMR9DeinterlaceCaps *caps);

    HRESULT (STDMETHODCALLTYPE *GetDeinterlaceMode)(
        IVMRDeinterlaceControl9 *This,
        DWORD streamid,
        GUID *mode);

    HRESULT (STDMETHODCALLTYPE *SetDeinterlaceMode)(
        IVMRDeinterlaceControl9 *This,
        DWORD streamid,
        GUID *mode);

    HRESULT (STDMETHODCALLTYPE *GetDeinterlacePrefs)(
        IVMRDeinterlaceControl9 *This,
        DWORD *prefs);

    HRESULT (STDMETHODCALLTYPE *SetDeinterlacePrefs)(
        IVMRDeinterlaceControl9 *This,
        DWORD prefs);

    HRESULT (STDMETHODCALLTYPE *GetActualDeinterlaceMode)(
        IVMRDeinterlaceControl9 *This,
        DWORD streamid,
        GUID *mode);

    END_INTERFACE
} IVMRDeinterlaceControl9Vtbl;

interface IVMRDeinterlaceControl9 {
    CONST_VTBL IVMRDeinterlaceControl9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRDeinterlaceControl9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRDeinterlaceControl9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRDeinterlaceControl9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRDeinterlaceControl9 methods ***/
#define IVMRDeinterlaceControl9_GetNumberOfDeinterlaceModes(This,desc,nummodes,modes) (This)->lpVtbl->GetNumberOfDeinterlaceModes(This,desc,nummodes,modes)
#define IVMRDeinterlaceControl9_GetDeinterlaceModeCaps(This,mode,desc,caps) (This)->lpVtbl->GetDeinterlaceModeCaps(This,mode,desc,caps)
#define IVMRDeinterlaceControl9_GetDeinterlaceMode(This,streamid,mode) (This)->lpVtbl->GetDeinterlaceMode(This,streamid,mode)
#define IVMRDeinterlaceControl9_SetDeinterlaceMode(This,streamid,mode) (This)->lpVtbl->SetDeinterlaceMode(This,streamid,mode)
#define IVMRDeinterlaceControl9_GetDeinterlacePrefs(This,prefs) (This)->lpVtbl->GetDeinterlacePrefs(This,prefs)
#define IVMRDeinterlaceControl9_SetDeinterlacePrefs(This,prefs) (This)->lpVtbl->SetDeinterlacePrefs(This,prefs)
#define IVMRDeinterlaceControl9_GetActualDeinterlaceMode(This,streamid,mode) (This)->lpVtbl->GetActualDeinterlaceMode(This,streamid,mode)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRDeinterlaceControl9_QueryInterface(IVMRDeinterlaceControl9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRDeinterlaceControl9_AddRef(IVMRDeinterlaceControl9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRDeinterlaceControl9_Release(IVMRDeinterlaceControl9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRDeinterlaceControl9 methods ***/
static inline HRESULT IVMRDeinterlaceControl9_GetNumberOfDeinterlaceModes(IVMRDeinterlaceControl9* This,VMR9VideoDesc *desc,DWORD *nummodes,GUID *modes) {
    return This->lpVtbl->GetNumberOfDeinterlaceModes(This,desc,nummodes,modes);
}
static inline HRESULT IVMRDeinterlaceControl9_GetDeinterlaceModeCaps(IVMRDeinterlaceControl9* This,GUID *mode,VMR9VideoDesc *desc,VMR9DeinterlaceCaps *caps) {
    return This->lpVtbl->GetDeinterlaceModeCaps(This,mode,desc,caps);
}
static inline HRESULT IVMRDeinterlaceControl9_GetDeinterlaceMode(IVMRDeinterlaceControl9* This,DWORD streamid,GUID *mode) {
    return This->lpVtbl->GetDeinterlaceMode(This,streamid,mode);
}
static inline HRESULT IVMRDeinterlaceControl9_SetDeinterlaceMode(IVMRDeinterlaceControl9* This,DWORD streamid,GUID *mode) {
    return This->lpVtbl->SetDeinterlaceMode(This,streamid,mode);
}
static inline HRESULT IVMRDeinterlaceControl9_GetDeinterlacePrefs(IVMRDeinterlaceControl9* This,DWORD *prefs) {
    return This->lpVtbl->GetDeinterlacePrefs(This,prefs);
}
static inline HRESULT IVMRDeinterlaceControl9_SetDeinterlacePrefs(IVMRDeinterlaceControl9* This,DWORD prefs) {
    return This->lpVtbl->SetDeinterlacePrefs(This,prefs);
}
static inline HRESULT IVMRDeinterlaceControl9_GetActualDeinterlaceMode(IVMRDeinterlaceControl9* This,DWORD streamid,GUID *mode) {
    return This->lpVtbl->GetActualDeinterlaceMode(This,streamid,mode);
}
#endif
#endif

#endif


#endif  /* __IVMRDeinterlaceControl9_INTERFACE_DEFINED__ */

typedef struct _VMR9VideoStreamInfo {
    IDirect3DSurface9 *pddsVideoSurface;
    DWORD dwWidth;
    DWORD dwHeight;
    DWORD dwStrmID;
    FLOAT fAlpha;
    VMR9NormalizedRect rNormal;
    REFERENCE_TIME rtStart;
    REFERENCE_TIME rtEnd;
    VMR9_SampleFormat SampleFormat;
} VMR9VideoStreamInfo;
/*****************************************************************************
 * IVMRImageCompositor9 interface
 */
#ifndef __IVMRImageCompositor9_INTERFACE_DEFINED__
#define __IVMRImageCompositor9_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVMRImageCompositor9, 0x4a5c89eb, 0xdf51, 0x4654, 0xac,0x2a, 0xe4,0x8e,0x02,0xbb,0xab,0xf6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4a5c89eb-df51-4654-ac2a-e48e02bbabf6")
IVMRImageCompositor9 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitCompositionDevice(
        IUnknown *d3ddev) = 0;

    virtual HRESULT STDMETHODCALLTYPE TermCompositionDevice(
        IUnknown *d3ddev) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamMediaType(
        DWORD stream,
        AM_MEDIA_TYPE *mt,
        WINBOOL texture) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompositeImage(
        IUnknown *d3ddev,
        IDirect3DSurface9 *d3dtarget,
        AM_MEDIA_TYPE *mttarget,
        REFERENCE_TIME start,
        REFERENCE_TIME stop,
        D3DCOLOR back,
        VMR9VideoStreamInfo *info,
        UINT streams) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVMRImageCompositor9, 0x4a5c89eb, 0xdf51, 0x4654, 0xac,0x2a, 0xe4,0x8e,0x02,0xbb,0xab,0xf6)
#endif
#else
typedef struct IVMRImageCompositor9Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVMRImageCompositor9 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVMRImageCompositor9 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVMRImageCompositor9 *This);

    /*** IVMRImageCompositor9 methods ***/
    HRESULT (STDMETHODCALLTYPE *InitCompositionDevice)(
        IVMRImageCompositor9 *This,
        IUnknown *d3ddev);

    HRESULT (STDMETHODCALLTYPE *TermCompositionDevice)(
        IVMRImageCompositor9 *This,
        IUnknown *d3ddev);

    HRESULT (STDMETHODCALLTYPE *SetStreamMediaType)(
        IVMRImageCompositor9 *This,
        DWORD stream,
        AM_MEDIA_TYPE *mt,
        WINBOOL texture);

    HRESULT (STDMETHODCALLTYPE *CompositeImage)(
        IVMRImageCompositor9 *This,
        IUnknown *d3ddev,
        IDirect3DSurface9 *d3dtarget,
        AM_MEDIA_TYPE *mttarget,
        REFERENCE_TIME start,
        REFERENCE_TIME stop,
        D3DCOLOR back,
        VMR9VideoStreamInfo *info,
        UINT streams);

    END_INTERFACE
} IVMRImageCompositor9Vtbl;

interface IVMRImageCompositor9 {
    CONST_VTBL IVMRImageCompositor9Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVMRImageCompositor9_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVMRImageCompositor9_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVMRImageCompositor9_Release(This) (This)->lpVtbl->Release(This)
/*** IVMRImageCompositor9 methods ***/
#define IVMRImageCompositor9_InitCompositionDevice(This,d3ddev) (This)->lpVtbl->InitCompositionDevice(This,d3ddev)
#define IVMRImageCompositor9_TermCompositionDevice(This,d3ddev) (This)->lpVtbl->TermCompositionDevice(This,d3ddev)
#define IVMRImageCompositor9_SetStreamMediaType(This,stream,mt,texture) (This)->lpVtbl->SetStreamMediaType(This,stream,mt,texture)
#define IVMRImageCompositor9_CompositeImage(This,d3ddev,d3dtarget,mttarget,start,stop,back,info,streams) (This)->lpVtbl->CompositeImage(This,d3ddev,d3dtarget,mttarget,start,stop,back,info,streams)
#else
/*** IUnknown methods ***/
static inline HRESULT IVMRImageCompositor9_QueryInterface(IVMRImageCompositor9* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVMRImageCompositor9_AddRef(IVMRImageCompositor9* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVMRImageCompositor9_Release(IVMRImageCompositor9* This) {
    return This->lpVtbl->Release(This);
}
/*** IVMRImageCompositor9 methods ***/
static inline HRESULT IVMRImageCompositor9_InitCompositionDevice(IVMRImageCompositor9* This,IUnknown *d3ddev) {
    return This->lpVtbl->InitCompositionDevice(This,d3ddev);
}
static inline HRESULT IVMRImageCompositor9_TermCompositionDevice(IVMRImageCompositor9* This,IUnknown *d3ddev) {
    return This->lpVtbl->TermCompositionDevice(This,d3ddev);
}
static inline HRESULT IVMRImageCompositor9_SetStreamMediaType(IVMRImageCompositor9* This,DWORD stream,AM_MEDIA_TYPE *mt,WINBOOL texture) {
    return This->lpVtbl->SetStreamMediaType(This,stream,mt,texture);
}
static inline HRESULT IVMRImageCompositor9_CompositeImage(IVMRImageCompositor9* This,IUnknown *d3ddev,IDirect3DSurface9 *d3dtarget,AM_MEDIA_TYPE *mttarget,REFERENCE_TIME start,REFERENCE_TIME stop,D3DCOLOR back,VMR9VideoStreamInfo *info,UINT streams) {
    return This->lpVtbl->CompositeImage(This,d3ddev,d3dtarget,mttarget,start,stop,back,info,streams);
}
#endif
#endif

#endif


#endif  /* __IVMRImageCompositor9_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __vmr9_h__ */
