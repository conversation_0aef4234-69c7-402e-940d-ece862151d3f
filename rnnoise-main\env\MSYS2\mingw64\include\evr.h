/*** Autogenerated by WIDL 10.12 from include/evr.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __evr_h__
#define __evr_h__

/* Forward declarations */

#ifndef __IMFVideoPositionMapper_FWD_DEFINED__
#define __IMFVideoPositionMapper_FWD_DEFINED__
typedef interface IMFVideoPositionMapper IMFVideoPositionMapper;
#ifdef __cplusplus
interface IMFVideoPositionMapper;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoDeviceID_FWD_DEFINED__
#define __IMFVideoDeviceID_FWD_DEFINED__
typedef interface IMFVideoDeviceID IMFVideoDeviceID;
#ifdef __cplusplus
interface IMFVideoDeviceID;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoDisplayControl_FWD_DEFINED__
#define __IMFVideoDisplayControl_FWD_DEFINED__
typedef interface IMFVideoDisplayControl IMFVideoDisplayControl;
#ifdef __cplusplus
interface IMFVideoDisplayControl;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoPresenter_FWD_DEFINED__
#define __IMFVideoPresenter_FWD_DEFINED__
typedef interface IMFVideoPresenter IMFVideoPresenter;
#ifdef __cplusplus
interface IMFVideoPresenter;
#endif /* __cplusplus */
#endif

#ifndef __IMFDesiredSample_FWD_DEFINED__
#define __IMFDesiredSample_FWD_DEFINED__
typedef interface IMFDesiredSample IMFDesiredSample;
#ifdef __cplusplus
interface IMFDesiredSample;
#endif /* __cplusplus */
#endif

#ifndef __IMFTrackedSample_FWD_DEFINED__
#define __IMFTrackedSample_FWD_DEFINED__
typedef interface IMFTrackedSample IMFTrackedSample;
#ifdef __cplusplus
interface IMFTrackedSample;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoMixerControl_FWD_DEFINED__
#define __IMFVideoMixerControl_FWD_DEFINED__
typedef interface IMFVideoMixerControl IMFVideoMixerControl;
#ifdef __cplusplus
interface IMFVideoMixerControl;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoMixerControl2_FWD_DEFINED__
#define __IMFVideoMixerControl2_FWD_DEFINED__
typedef interface IMFVideoMixerControl2 IMFVideoMixerControl2;
#ifdef __cplusplus
interface IMFVideoMixerControl2;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoRenderer_FWD_DEFINED__
#define __IMFVideoRenderer_FWD_DEFINED__
typedef interface IMFVideoRenderer IMFVideoRenderer;
#ifdef __cplusplus
interface IMFVideoRenderer;
#endif /* __cplusplus */
#endif

#ifndef __IEVRFilterConfig_FWD_DEFINED__
#define __IEVRFilterConfig_FWD_DEFINED__
typedef interface IEVRFilterConfig IEVRFilterConfig;
#ifdef __cplusplus
interface IEVRFilterConfig;
#endif /* __cplusplus */
#endif

#ifndef __IEVRFilterConfigEx_FWD_DEFINED__
#define __IEVRFilterConfigEx_FWD_DEFINED__
typedef interface IEVRFilterConfigEx IEVRFilterConfigEx;
#ifdef __cplusplus
interface IEVRFilterConfigEx;
#endif /* __cplusplus */
#endif

#ifndef __IMFTopologyServiceLookup_FWD_DEFINED__
#define __IMFTopologyServiceLookup_FWD_DEFINED__
typedef interface IMFTopologyServiceLookup IMFTopologyServiceLookup;
#ifdef __cplusplus
interface IMFTopologyServiceLookup;
#endif /* __cplusplus */
#endif

#ifndef __IMFTopologyServiceLookupClient_FWD_DEFINED__
#define __IMFTopologyServiceLookupClient_FWD_DEFINED__
typedef interface IMFTopologyServiceLookupClient IMFTopologyServiceLookupClient;
#ifdef __cplusplus
interface IMFTopologyServiceLookupClient;
#endif /* __cplusplus */
#endif

#ifndef __IEVRTrustedVideoPlugin_FWD_DEFINED__
#define __IEVRTrustedVideoPlugin_FWD_DEFINED__
typedef interface IEVRTrustedVideoPlugin IEVRTrustedVideoPlugin;
#ifdef __cplusplus
interface IEVRTrustedVideoPlugin;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <propidl.h>
#include <mfidl.h>
#include <mftransform.h>
#include <mediaobj.h>
#include <strmif.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IMFTrackedSample_FWD_DEFINED__
#define __IMFTrackedSample_FWD_DEFINED__
typedef interface IMFTrackedSample IMFTrackedSample;
#ifdef __cplusplus
interface IMFTrackedSample;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoDisplayControl_FWD_DEFINED__
#define __IMFVideoDisplayControl_FWD_DEFINED__
typedef interface IMFVideoDisplayControl IMFVideoDisplayControl;
#ifdef __cplusplus
interface IMFVideoDisplayControl;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoPresenter_FWD_DEFINED__
#define __IMFVideoPresenter_FWD_DEFINED__
typedef interface IMFVideoPresenter IMFVideoPresenter;
#ifdef __cplusplus
interface IMFVideoPresenter;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoPositionMapper_FWD_DEFINED__
#define __IMFVideoPositionMapper_FWD_DEFINED__
typedef interface IMFVideoPositionMapper IMFVideoPositionMapper;
#ifdef __cplusplus
interface IMFVideoPositionMapper;
#endif /* __cplusplus */
#endif

#ifndef __IMFDesiredSample_FWD_DEFINED__
#define __IMFDesiredSample_FWD_DEFINED__
typedef interface IMFDesiredSample IMFDesiredSample;
#ifdef __cplusplus
interface IMFDesiredSample;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoMixerControl_FWD_DEFINED__
#define __IMFVideoMixerControl_FWD_DEFINED__
typedef interface IMFVideoMixerControl IMFVideoMixerControl;
#ifdef __cplusplus
interface IMFVideoMixerControl;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoRenderer_FWD_DEFINED__
#define __IMFVideoRenderer_FWD_DEFINED__
typedef interface IMFVideoRenderer IMFVideoRenderer;
#ifdef __cplusplus
interface IMFVideoRenderer;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoDeviceID_FWD_DEFINED__
#define __IMFVideoDeviceID_FWD_DEFINED__
typedef interface IMFVideoDeviceID IMFVideoDeviceID;
#ifdef __cplusplus
interface IMFVideoDeviceID;
#endif /* __cplusplus */
#endif

#ifndef __IEVRFilterConfig_FWD_DEFINED__
#define __IEVRFilterConfig_FWD_DEFINED__
typedef interface IEVRFilterConfig IEVRFilterConfig;
#ifdef __cplusplus
interface IEVRFilterConfig;
#endif /* __cplusplus */
#endif

#if defined(__midl)

typedef enum _D3DFORMAT {
    D3DFMT_UNKNOWN = 0,
    D3DFMT_R8G8B8 = 20,
    D3DFMT_A8R8G8B8 = 21,
    D3DFMT_X8R8G8B8 = 22,
    D3DFMT_R5G6B5 = 23,
    D3DFMT_X1R5G5B5 = 24,
    D3DFMT_A1R5G5B5 = 25,
    D3DFMT_A4R4G4B4 = 26,
    D3DFMT_R3G3B2 = 27,
    D3DFMT_A8 = 28,
    D3DFMT_A8R3G3B2 = 29,
    D3DFMT_X4R4G4B4 = 30,
    D3DFMT_A2B10G10R10 = 31,
    D3DFMT_G16R16 = 34,
    D3DFMT_A8P8 = 40,
    D3DFMT_P8 = 41,
    D3DFMT_L8 = 50,
    D3DFMT_A8L8 = 51,
    D3DFMT_A4L4 = 52,
    D3DFMT_V8U8 = 60,
    D3DFMT_L6V5U5 = 61,
    D3DFMT_X8L8V8U8 = 62,
    D3DFMT_Q8W8V8U8 = 63,
    D3DFMT_V16U16 = 64,
    D3DFMT_W11V11U10 = 65,
    D3DFMT_A2W10V10U10 = 67,
    D3DFMT_D16_LOCKABLE = 70,
    D3DFMT_D32 = 71,
    D3DFMT_D15S1 = 73,
    D3DFMT_D24S8 = 75,
    D3DFMT_D16 = 80,
    D3DFMT_D24X8 = 77,
    D3DFMT_D24X4S4 = 79,
    D3DFMT_VERTEXDATA = 100,
    D3DFMT_INDEX16 = 101,
    D3DFMT_INDEX32 = 102,
    D3DFMT_FORCE_DWORD = 0x7fffffff
} D3DFORMAT;
#endif

typedef enum MFVideoAspectRatioMode {
    MFVideoARMode_None = 0x0,
    MFVideoARMode_PreservePicture = 0x1,
    MFVideoARMode_PreservePixel = 0x2,
    MFVideoARMode_NonLinearStretch = 0x4,
    MFVideoARMode_Mask = 0x7
} MFVideoAspectRatioMode;

typedef enum MFVideoRenderPrefs {
    MFVideoRenderPrefs_DoNotRenderBorder = 0x1,
    MFVideoRenderPrefs_DoNotClipToDevice = 0x2,
    MFVideoRenderPrefs_AllowOutputThrottling = 0x4,
    MFVideoRenderPrefs_ForceOutputThrottling = 0x8,
    MFVideoRenderPrefs_ForceBatching = 0x10,
    MFVideoRenderPrefs_AllowBatching = 0x20,
    MFVideoRenderPrefs_ForceScaling = 0x40,
    MFVideoRenderPrefs_AllowScaling = 0x80,
    MFVideoRenderPrefs_DoNotRepaintOnStop = 0x100,
    MFVideoRenderPrefs_Mask = 0x1ff
} MFVideoRenderPrefs;

#ifndef _MFVideoNormalizedRect_
#define _MFVideoNormalizedRect_
typedef struct MFVideoNormalizedRect {
    float left;
    float top;
    float right;
    float bottom;
} MFVideoNormalizedRect;
#endif

typedef enum MFVP_MESSAGE_TYPE {
    MFVP_MESSAGE_FLUSH = 0x0,
    MFVP_MESSAGE_INVALIDATEMEDIATYPE = 0x1,
    MFVP_MESSAGE_PROCESSINPUTNOTIFY = 0x2,
    MFVP_MESSAGE_BEGINSTREAMING = 0x3,
    MFVP_MESSAGE_ENDSTREAMING = 0x4,
    MFVP_MESSAGE_ENDOFSTREAM = 0x5,
    MFVP_MESSAGE_STEP = 0x6,
    MFVP_MESSAGE_CANCELSTEP = 0x7
} MFVP_MESSAGE_TYPE;

typedef enum _MFVideoMixPrefs {
    MFVideoMixPrefs_ForceHalfInterlace = 0x1,
    MFVideoMixPrefs_AllowDropToHalfInterlace = 0x2,
    MFVideoMixPrefs_AllowDropToBob = 0x4,
    MFVideoMixPrefs_ForceBob = 0x8,
    MFVideoMixPrefs_EnableRotation = 0x10,
    MFVideoMixPrefs_Mask = 0x1f
} MFVideoMixPrefs;

typedef enum _EVRFilterConfig_Prefs {
    EVRFilterConfigPrefs_EnableQoS = 0x1,
    EVRFilterConfigPrefs_Mask = 0x1
} EVRFilterConfigPrefs;

typedef enum _MF_SERVICE_LOOKUP_TYPE {
    MF_SERVICE_LOOKUP_UPSTREAM = 0,
    MF_SERVICE_LOOKUP_UPSTREAM_DIRECT = 1,
    MF_SERVICE_LOOKUP_DOWNSTREAM = 2,
    MF_SERVICE_LOOKUP_DOWNSTREAM_DIRECT = 3,
    MF_SERVICE_LOOKUP_ALL = 4,
    MF_SERVICE_LOOKUP_GLOBAL = 5
} MF_SERVICE_LOOKUP_TYPE;

DEFINE_GUID(MR_VIDEO_RENDER_SERVICE, 0x1092a86c, 0xab1a, 0x459a, 0xa3, 0x36, 0x83, 0x1f, 0xbc, 0x4d, 0x11, 0xff);
DEFINE_GUID(MR_VIDEO_MIXER_SERVICE, 0x73cd2fc, 0x6cf4, 0x40b7, 0x88, 0x59, 0xe8, 0x95, 0x52, 0xc8, 0x41, 0xf8);
DEFINE_GUID(MR_VIDEO_ACCELERATION_SERVICE, 0xefef5175, 0x5c7d, 0x4ce2, 0xbb, 0xbd, 0x34, 0xff, 0x8b, 0xca, 0x65, 0x54);
DEFINE_GUID(MR_BUFFER_SERVICE, 0xa562248c, 0x9ac6, 0x4ffc, 0x9f, 0xba, 0x3a, 0xf8, 0xf8, 0xad, 0x1a, 0x4d);
DEFINE_GUID(VIDEO_ZOOM_RECT, 0x7aaa1638, 0x1b7f, 0x4c93, 0xbd, 0x89, 0x5b, 0x9c, 0x9f, 0xb6, 0xfc, 0xf0);

/*****************************************************************************
 * IMFVideoPositionMapper interface
 */
#ifndef __IMFVideoPositionMapper_INTERFACE_DEFINED__
#define __IMFVideoPositionMapper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoPositionMapper, 0x1f6a9f17, 0xe70b, 0x4e24, 0x8a,0xe4, 0x0b,0x2c,0x3b,0xa7,0xa4,0xae);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1f6a9f17-e70b-4e24-8ae4-0b2c3ba7a4ae")
IMFVideoPositionMapper : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MapOutputCoordinateToInputStream(
        float xOut,
        float yOut,
        DWORD dwOutputStreamIndex,
        DWORD dwInputStreamIndex,
        float *pxIn,
        float *pyIn) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoPositionMapper, 0x1f6a9f17, 0xe70b, 0x4e24, 0x8a,0xe4, 0x0b,0x2c,0x3b,0xa7,0xa4,0xae)
#endif
#else
typedef struct IMFVideoPositionMapperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoPositionMapper *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoPositionMapper *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoPositionMapper *This);

    /*** IMFVideoPositionMapper methods ***/
    HRESULT (STDMETHODCALLTYPE *MapOutputCoordinateToInputStream)(
        IMFVideoPositionMapper *This,
        float xOut,
        float yOut,
        DWORD dwOutputStreamIndex,
        DWORD dwInputStreamIndex,
        float *pxIn,
        float *pyIn);

    END_INTERFACE
} IMFVideoPositionMapperVtbl;

interface IMFVideoPositionMapper {
    CONST_VTBL IMFVideoPositionMapperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoPositionMapper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoPositionMapper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoPositionMapper_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoPositionMapper methods ***/
#define IMFVideoPositionMapper_MapOutputCoordinateToInputStream(This,xOut,yOut,dwOutputStreamIndex,dwInputStreamIndex,pxIn,pyIn) (This)->lpVtbl->MapOutputCoordinateToInputStream(This,xOut,yOut,dwOutputStreamIndex,dwInputStreamIndex,pxIn,pyIn)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoPositionMapper_QueryInterface(IMFVideoPositionMapper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoPositionMapper_AddRef(IMFVideoPositionMapper* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoPositionMapper_Release(IMFVideoPositionMapper* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoPositionMapper methods ***/
static inline HRESULT IMFVideoPositionMapper_MapOutputCoordinateToInputStream(IMFVideoPositionMapper* This,float xOut,float yOut,DWORD dwOutputStreamIndex,DWORD dwInputStreamIndex,float *pxIn,float *pyIn) {
    return This->lpVtbl->MapOutputCoordinateToInputStream(This,xOut,yOut,dwOutputStreamIndex,dwInputStreamIndex,pxIn,pyIn);
}
#endif
#endif

#endif


#endif  /* __IMFVideoPositionMapper_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFVideoDeviceID interface
 */
#ifndef __IMFVideoDeviceID_INTERFACE_DEFINED__
#define __IMFVideoDeviceID_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoDeviceID, 0xa38d9567, 0x5a9c, 0x4f3c, 0xb2,0x93, 0x8e,0xb4,0x15,0xb2,0x79,0xba);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a38d9567-5a9c-4f3c-b293-8eb415b279ba")
IMFVideoDeviceID : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDeviceID(
        IID *pDeviceID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoDeviceID, 0xa38d9567, 0x5a9c, 0x4f3c, 0xb2,0x93, 0x8e,0xb4,0x15,0xb2,0x79,0xba)
#endif
#else
typedef struct IMFVideoDeviceIDVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoDeviceID *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoDeviceID *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoDeviceID *This);

    /*** IMFVideoDeviceID methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceID)(
        IMFVideoDeviceID *This,
        IID *pDeviceID);

    END_INTERFACE
} IMFVideoDeviceIDVtbl;

interface IMFVideoDeviceID {
    CONST_VTBL IMFVideoDeviceIDVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoDeviceID_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoDeviceID_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoDeviceID_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoDeviceID methods ***/
#define IMFVideoDeviceID_GetDeviceID(This,pDeviceID) (This)->lpVtbl->GetDeviceID(This,pDeviceID)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoDeviceID_QueryInterface(IMFVideoDeviceID* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoDeviceID_AddRef(IMFVideoDeviceID* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoDeviceID_Release(IMFVideoDeviceID* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoDeviceID methods ***/
static inline HRESULT IMFVideoDeviceID_GetDeviceID(IMFVideoDeviceID* This,IID *pDeviceID) {
    return This->lpVtbl->GetDeviceID(This,pDeviceID);
}
#endif
#endif

#endif


#endif  /* __IMFVideoDeviceID_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFVideoDisplayControl interface
 */
#ifndef __IMFVideoDisplayControl_INTERFACE_DEFINED__
#define __IMFVideoDisplayControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoDisplayControl, 0xa490b1e4, 0xab84, 0x4d31, 0xa1,0xb2, 0x18,0x1e,0x03,0xb1,0x07,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a490b1e4-ab84-4d31-a1b2-181e03b1077a")
IMFVideoDisplayControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNativeVideoSize(
        SIZE *pszVideo,
        SIZE *pszARVideo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIdealVideoSize(
        SIZE *pszMin,
        SIZE *pszMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVideoPosition(
        const MFVideoNormalizedRect *pnrcSource,
        const LPRECT prcDest) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoPosition(
        MFVideoNormalizedRect *pnrcSource,
        LPRECT prcDest) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAspectRatioMode(
        DWORD dwAspectRatioMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAspectRatioMode(
        DWORD *pdwAspectRatioMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVideoWindow(
        HWND hwndVideo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoWindow(
        HWND *phwndVideo) = 0;

    virtual HRESULT STDMETHODCALLTYPE RepaintVideo(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentImage(
        BITMAPINFOHEADER *pBih,
        BYTE **pDib,
        DWORD *pcbDib,
        LONGLONG *pTimeStamp) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBorderColor(
        COLORREF Clr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBorderColor(
        COLORREF *pClr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRenderingPrefs(
        DWORD dwRenderFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRenderingPrefs(
        DWORD *pdwRenderFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFullscreen(
        WINBOOL fFullscreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFullscreen(
        WINBOOL *pfFullscreen) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoDisplayControl, 0xa490b1e4, 0xab84, 0x4d31, 0xa1,0xb2, 0x18,0x1e,0x03,0xb1,0x07,0x7a)
#endif
#else
typedef struct IMFVideoDisplayControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoDisplayControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoDisplayControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoDisplayControl *This);

    /*** IMFVideoDisplayControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNativeVideoSize)(
        IMFVideoDisplayControl *This,
        SIZE *pszVideo,
        SIZE *pszARVideo);

    HRESULT (STDMETHODCALLTYPE *GetIdealVideoSize)(
        IMFVideoDisplayControl *This,
        SIZE *pszMin,
        SIZE *pszMax);

    HRESULT (STDMETHODCALLTYPE *SetVideoPosition)(
        IMFVideoDisplayControl *This,
        const MFVideoNormalizedRect *pnrcSource,
        const LPRECT prcDest);

    HRESULT (STDMETHODCALLTYPE *GetVideoPosition)(
        IMFVideoDisplayControl *This,
        MFVideoNormalizedRect *pnrcSource,
        LPRECT prcDest);

    HRESULT (STDMETHODCALLTYPE *SetAspectRatioMode)(
        IMFVideoDisplayControl *This,
        DWORD dwAspectRatioMode);

    HRESULT (STDMETHODCALLTYPE *GetAspectRatioMode)(
        IMFVideoDisplayControl *This,
        DWORD *pdwAspectRatioMode);

    HRESULT (STDMETHODCALLTYPE *SetVideoWindow)(
        IMFVideoDisplayControl *This,
        HWND hwndVideo);

    HRESULT (STDMETHODCALLTYPE *GetVideoWindow)(
        IMFVideoDisplayControl *This,
        HWND *phwndVideo);

    HRESULT (STDMETHODCALLTYPE *RepaintVideo)(
        IMFVideoDisplayControl *This);

    HRESULT (STDMETHODCALLTYPE *GetCurrentImage)(
        IMFVideoDisplayControl *This,
        BITMAPINFOHEADER *pBih,
        BYTE **pDib,
        DWORD *pcbDib,
        LONGLONG *pTimeStamp);

    HRESULT (STDMETHODCALLTYPE *SetBorderColor)(
        IMFVideoDisplayControl *This,
        COLORREF Clr);

    HRESULT (STDMETHODCALLTYPE *GetBorderColor)(
        IMFVideoDisplayControl *This,
        COLORREF *pClr);

    HRESULT (STDMETHODCALLTYPE *SetRenderingPrefs)(
        IMFVideoDisplayControl *This,
        DWORD dwRenderFlags);

    HRESULT (STDMETHODCALLTYPE *GetRenderingPrefs)(
        IMFVideoDisplayControl *This,
        DWORD *pdwRenderFlags);

    HRESULT (STDMETHODCALLTYPE *SetFullscreen)(
        IMFVideoDisplayControl *This,
        WINBOOL fFullscreen);

    HRESULT (STDMETHODCALLTYPE *GetFullscreen)(
        IMFVideoDisplayControl *This,
        WINBOOL *pfFullscreen);

    END_INTERFACE
} IMFVideoDisplayControlVtbl;

interface IMFVideoDisplayControl {
    CONST_VTBL IMFVideoDisplayControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoDisplayControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoDisplayControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoDisplayControl_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoDisplayControl methods ***/
#define IMFVideoDisplayControl_GetNativeVideoSize(This,pszVideo,pszARVideo) (This)->lpVtbl->GetNativeVideoSize(This,pszVideo,pszARVideo)
#define IMFVideoDisplayControl_GetIdealVideoSize(This,pszMin,pszMax) (This)->lpVtbl->GetIdealVideoSize(This,pszMin,pszMax)
#define IMFVideoDisplayControl_SetVideoPosition(This,pnrcSource,prcDest) (This)->lpVtbl->SetVideoPosition(This,pnrcSource,prcDest)
#define IMFVideoDisplayControl_GetVideoPosition(This,pnrcSource,prcDest) (This)->lpVtbl->GetVideoPosition(This,pnrcSource,prcDest)
#define IMFVideoDisplayControl_SetAspectRatioMode(This,dwAspectRatioMode) (This)->lpVtbl->SetAspectRatioMode(This,dwAspectRatioMode)
#define IMFVideoDisplayControl_GetAspectRatioMode(This,pdwAspectRatioMode) (This)->lpVtbl->GetAspectRatioMode(This,pdwAspectRatioMode)
#define IMFVideoDisplayControl_SetVideoWindow(This,hwndVideo) (This)->lpVtbl->SetVideoWindow(This,hwndVideo)
#define IMFVideoDisplayControl_GetVideoWindow(This,phwndVideo) (This)->lpVtbl->GetVideoWindow(This,phwndVideo)
#define IMFVideoDisplayControl_RepaintVideo(This) (This)->lpVtbl->RepaintVideo(This)
#define IMFVideoDisplayControl_GetCurrentImage(This,pBih,pDib,pcbDib,pTimeStamp) (This)->lpVtbl->GetCurrentImage(This,pBih,pDib,pcbDib,pTimeStamp)
#define IMFVideoDisplayControl_SetBorderColor(This,Clr) (This)->lpVtbl->SetBorderColor(This,Clr)
#define IMFVideoDisplayControl_GetBorderColor(This,pClr) (This)->lpVtbl->GetBorderColor(This,pClr)
#define IMFVideoDisplayControl_SetRenderingPrefs(This,dwRenderFlags) (This)->lpVtbl->SetRenderingPrefs(This,dwRenderFlags)
#define IMFVideoDisplayControl_GetRenderingPrefs(This,pdwRenderFlags) (This)->lpVtbl->GetRenderingPrefs(This,pdwRenderFlags)
#define IMFVideoDisplayControl_SetFullscreen(This,fFullscreen) (This)->lpVtbl->SetFullscreen(This,fFullscreen)
#define IMFVideoDisplayControl_GetFullscreen(This,pfFullscreen) (This)->lpVtbl->GetFullscreen(This,pfFullscreen)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoDisplayControl_QueryInterface(IMFVideoDisplayControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoDisplayControl_AddRef(IMFVideoDisplayControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoDisplayControl_Release(IMFVideoDisplayControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoDisplayControl methods ***/
static inline HRESULT IMFVideoDisplayControl_GetNativeVideoSize(IMFVideoDisplayControl* This,SIZE *pszVideo,SIZE *pszARVideo) {
    return This->lpVtbl->GetNativeVideoSize(This,pszVideo,pszARVideo);
}
static inline HRESULT IMFVideoDisplayControl_GetIdealVideoSize(IMFVideoDisplayControl* This,SIZE *pszMin,SIZE *pszMax) {
    return This->lpVtbl->GetIdealVideoSize(This,pszMin,pszMax);
}
static inline HRESULT IMFVideoDisplayControl_SetVideoPosition(IMFVideoDisplayControl* This,const MFVideoNormalizedRect *pnrcSource,const LPRECT prcDest) {
    return This->lpVtbl->SetVideoPosition(This,pnrcSource,prcDest);
}
static inline HRESULT IMFVideoDisplayControl_GetVideoPosition(IMFVideoDisplayControl* This,MFVideoNormalizedRect *pnrcSource,LPRECT prcDest) {
    return This->lpVtbl->GetVideoPosition(This,pnrcSource,prcDest);
}
static inline HRESULT IMFVideoDisplayControl_SetAspectRatioMode(IMFVideoDisplayControl* This,DWORD dwAspectRatioMode) {
    return This->lpVtbl->SetAspectRatioMode(This,dwAspectRatioMode);
}
static inline HRESULT IMFVideoDisplayControl_GetAspectRatioMode(IMFVideoDisplayControl* This,DWORD *pdwAspectRatioMode) {
    return This->lpVtbl->GetAspectRatioMode(This,pdwAspectRatioMode);
}
static inline HRESULT IMFVideoDisplayControl_SetVideoWindow(IMFVideoDisplayControl* This,HWND hwndVideo) {
    return This->lpVtbl->SetVideoWindow(This,hwndVideo);
}
static inline HRESULT IMFVideoDisplayControl_GetVideoWindow(IMFVideoDisplayControl* This,HWND *phwndVideo) {
    return This->lpVtbl->GetVideoWindow(This,phwndVideo);
}
static inline HRESULT IMFVideoDisplayControl_RepaintVideo(IMFVideoDisplayControl* This) {
    return This->lpVtbl->RepaintVideo(This);
}
static inline HRESULT IMFVideoDisplayControl_GetCurrentImage(IMFVideoDisplayControl* This,BITMAPINFOHEADER *pBih,BYTE **pDib,DWORD *pcbDib,LONGLONG *pTimeStamp) {
    return This->lpVtbl->GetCurrentImage(This,pBih,pDib,pcbDib,pTimeStamp);
}
static inline HRESULT IMFVideoDisplayControl_SetBorderColor(IMFVideoDisplayControl* This,COLORREF Clr) {
    return This->lpVtbl->SetBorderColor(This,Clr);
}
static inline HRESULT IMFVideoDisplayControl_GetBorderColor(IMFVideoDisplayControl* This,COLORREF *pClr) {
    return This->lpVtbl->GetBorderColor(This,pClr);
}
static inline HRESULT IMFVideoDisplayControl_SetRenderingPrefs(IMFVideoDisplayControl* This,DWORD dwRenderFlags) {
    return This->lpVtbl->SetRenderingPrefs(This,dwRenderFlags);
}
static inline HRESULT IMFVideoDisplayControl_GetRenderingPrefs(IMFVideoDisplayControl* This,DWORD *pdwRenderFlags) {
    return This->lpVtbl->GetRenderingPrefs(This,pdwRenderFlags);
}
static inline HRESULT IMFVideoDisplayControl_SetFullscreen(IMFVideoDisplayControl* This,WINBOOL fFullscreen) {
    return This->lpVtbl->SetFullscreen(This,fFullscreen);
}
static inline HRESULT IMFVideoDisplayControl_GetFullscreen(IMFVideoDisplayControl* This,WINBOOL *pfFullscreen) {
    return This->lpVtbl->GetFullscreen(This,pfFullscreen);
}
#endif
#endif

#endif


#endif  /* __IMFVideoDisplayControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFVideoPresenter interface
 */
#ifndef __IMFVideoPresenter_INTERFACE_DEFINED__
#define __IMFVideoPresenter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoPresenter, 0x29aff080, 0x182a, 0x4a5d, 0xaf,0x3b, 0x44,0x8f,0x3a,0x63,0x46,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("29aff080-182a-4a5d-af3b-448f3a6346cb")
IMFVideoPresenter : public IMFClockStateSink
{
    virtual HRESULT STDMETHODCALLTYPE ProcessMessage(
        MFVP_MESSAGE_TYPE eMessage,
        ULONG_PTR ulParam) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentMediaType(
        IMFVideoMediaType **ppMediaType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoPresenter, 0x29aff080, 0x182a, 0x4a5d, 0xaf,0x3b, 0x44,0x8f,0x3a,0x63,0x46,0xcb)
#endif
#else
typedef struct IMFVideoPresenterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoPresenter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoPresenter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoPresenter *This);

    /*** IMFClockStateSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnClockStart)(
        IMFVideoPresenter *This,
        MFTIME hnsSystemTime,
        LONGLONG llClockStartOffset);

    HRESULT (STDMETHODCALLTYPE *OnClockStop)(
        IMFVideoPresenter *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockPause)(
        IMFVideoPresenter *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockRestart)(
        IMFVideoPresenter *This,
        MFTIME hnsSystemTime);

    HRESULT (STDMETHODCALLTYPE *OnClockSetRate)(
        IMFVideoPresenter *This,
        MFTIME hnsSystemTime,
        float flRate);

    /*** IMFVideoPresenter methods ***/
    HRESULT (STDMETHODCALLTYPE *ProcessMessage)(
        IMFVideoPresenter *This,
        MFVP_MESSAGE_TYPE eMessage,
        ULONG_PTR ulParam);

    HRESULT (STDMETHODCALLTYPE *GetCurrentMediaType)(
        IMFVideoPresenter *This,
        IMFVideoMediaType **ppMediaType);

    END_INTERFACE
} IMFVideoPresenterVtbl;

interface IMFVideoPresenter {
    CONST_VTBL IMFVideoPresenterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoPresenter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoPresenter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoPresenter_Release(This) (This)->lpVtbl->Release(This)
/*** IMFClockStateSink methods ***/
#define IMFVideoPresenter_OnClockStart(This,hnsSystemTime,llClockStartOffset) (This)->lpVtbl->OnClockStart(This,hnsSystemTime,llClockStartOffset)
#define IMFVideoPresenter_OnClockStop(This,hnsSystemTime) (This)->lpVtbl->OnClockStop(This,hnsSystemTime)
#define IMFVideoPresenter_OnClockPause(This,hnsSystemTime) (This)->lpVtbl->OnClockPause(This,hnsSystemTime)
#define IMFVideoPresenter_OnClockRestart(This,hnsSystemTime) (This)->lpVtbl->OnClockRestart(This,hnsSystemTime)
#define IMFVideoPresenter_OnClockSetRate(This,hnsSystemTime,flRate) (This)->lpVtbl->OnClockSetRate(This,hnsSystemTime,flRate)
/*** IMFVideoPresenter methods ***/
#define IMFVideoPresenter_ProcessMessage(This,eMessage,ulParam) (This)->lpVtbl->ProcessMessage(This,eMessage,ulParam)
#define IMFVideoPresenter_GetCurrentMediaType(This,ppMediaType) (This)->lpVtbl->GetCurrentMediaType(This,ppMediaType)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoPresenter_QueryInterface(IMFVideoPresenter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoPresenter_AddRef(IMFVideoPresenter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoPresenter_Release(IMFVideoPresenter* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFClockStateSink methods ***/
static inline HRESULT IMFVideoPresenter_OnClockStart(IMFVideoPresenter* This,MFTIME hnsSystemTime,LONGLONG llClockStartOffset) {
    return This->lpVtbl->OnClockStart(This,hnsSystemTime,llClockStartOffset);
}
static inline HRESULT IMFVideoPresenter_OnClockStop(IMFVideoPresenter* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockStop(This,hnsSystemTime);
}
static inline HRESULT IMFVideoPresenter_OnClockPause(IMFVideoPresenter* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockPause(This,hnsSystemTime);
}
static inline HRESULT IMFVideoPresenter_OnClockRestart(IMFVideoPresenter* This,MFTIME hnsSystemTime) {
    return This->lpVtbl->OnClockRestart(This,hnsSystemTime);
}
static inline HRESULT IMFVideoPresenter_OnClockSetRate(IMFVideoPresenter* This,MFTIME hnsSystemTime,float flRate) {
    return This->lpVtbl->OnClockSetRate(This,hnsSystemTime,flRate);
}
/*** IMFVideoPresenter methods ***/
static inline HRESULT IMFVideoPresenter_ProcessMessage(IMFVideoPresenter* This,MFVP_MESSAGE_TYPE eMessage,ULONG_PTR ulParam) {
    return This->lpVtbl->ProcessMessage(This,eMessage,ulParam);
}
static inline HRESULT IMFVideoPresenter_GetCurrentMediaType(IMFVideoPresenter* This,IMFVideoMediaType **ppMediaType) {
    return This->lpVtbl->GetCurrentMediaType(This,ppMediaType);
}
#endif
#endif

#endif


#endif  /* __IMFVideoPresenter_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFDesiredSample interface
 */
#ifndef __IMFDesiredSample_INTERFACE_DEFINED__
#define __IMFDesiredSample_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFDesiredSample, 0x56c294d0, 0x753e, 0x4260, 0x8d,0x61, 0xa3,0xd8,0x82,0x0b,0x1d,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56c294d0-753e-4260-8d61-a3d8820b1d54")
IMFDesiredSample : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDesiredSampleTimeAndDuration(
        LONGLONG *phnsSampleTime,
        LONGLONG *phnsSampleDuration) = 0;

    virtual void STDMETHODCALLTYPE SetDesiredSampleTimeAndDuration(
        LONGLONG hnsSampleTime,
        LONGLONG hnsSampleDuration) = 0;

    virtual void STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFDesiredSample, 0x56c294d0, 0x753e, 0x4260, 0x8d,0x61, 0xa3,0xd8,0x82,0x0b,0x1d,0x54)
#endif
#else
typedef struct IMFDesiredSampleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFDesiredSample *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFDesiredSample *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFDesiredSample *This);

    /*** IMFDesiredSample methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesiredSampleTimeAndDuration)(
        IMFDesiredSample *This,
        LONGLONG *phnsSampleTime,
        LONGLONG *phnsSampleDuration);

    void (STDMETHODCALLTYPE *SetDesiredSampleTimeAndDuration)(
        IMFDesiredSample *This,
        LONGLONG hnsSampleTime,
        LONGLONG hnsSampleDuration);

    void (STDMETHODCALLTYPE *Clear)(
        IMFDesiredSample *This);

    END_INTERFACE
} IMFDesiredSampleVtbl;

interface IMFDesiredSample {
    CONST_VTBL IMFDesiredSampleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFDesiredSample_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFDesiredSample_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFDesiredSample_Release(This) (This)->lpVtbl->Release(This)
/*** IMFDesiredSample methods ***/
#define IMFDesiredSample_GetDesiredSampleTimeAndDuration(This,phnsSampleTime,phnsSampleDuration) (This)->lpVtbl->GetDesiredSampleTimeAndDuration(This,phnsSampleTime,phnsSampleDuration)
#define IMFDesiredSample_SetDesiredSampleTimeAndDuration(This,hnsSampleTime,hnsSampleDuration) (This)->lpVtbl->SetDesiredSampleTimeAndDuration(This,hnsSampleTime,hnsSampleDuration)
#define IMFDesiredSample_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFDesiredSample_QueryInterface(IMFDesiredSample* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFDesiredSample_AddRef(IMFDesiredSample* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFDesiredSample_Release(IMFDesiredSample* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFDesiredSample methods ***/
static inline HRESULT IMFDesiredSample_GetDesiredSampleTimeAndDuration(IMFDesiredSample* This,LONGLONG *phnsSampleTime,LONGLONG *phnsSampleDuration) {
    return This->lpVtbl->GetDesiredSampleTimeAndDuration(This,phnsSampleTime,phnsSampleDuration);
}
static inline void IMFDesiredSample_SetDesiredSampleTimeAndDuration(IMFDesiredSample* This,LONGLONG hnsSampleTime,LONGLONG hnsSampleDuration) {
    This->lpVtbl->SetDesiredSampleTimeAndDuration(This,hnsSampleTime,hnsSampleDuration);
}
static inline void IMFDesiredSample_Clear(IMFDesiredSample* This) {
    This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __IMFDesiredSample_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFTrackedSample interface
 */
#ifndef __IMFTrackedSample_INTERFACE_DEFINED__
#define __IMFTrackedSample_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTrackedSample, 0x245bf8e9, 0x0755, 0x40f7, 0x88,0xa5, 0xae,0x0f,0x18,0xd5,0x5e,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("245bf8e9-0755-40f7-88a5-ae0f18d55e17")
IMFTrackedSample : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetAllocator(
        IMFAsyncCallback *pSampleAllocator,
        IUnknown *pUnkState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTrackedSample, 0x245bf8e9, 0x0755, 0x40f7, 0x88,0xa5, 0xae,0x0f,0x18,0xd5,0x5e,0x17)
#endif
#else
typedef struct IMFTrackedSampleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTrackedSample *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTrackedSample *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTrackedSample *This);

    /*** IMFTrackedSample methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAllocator)(
        IMFTrackedSample *This,
        IMFAsyncCallback *pSampleAllocator,
        IUnknown *pUnkState);

    END_INTERFACE
} IMFTrackedSampleVtbl;

interface IMFTrackedSample {
    CONST_VTBL IMFTrackedSampleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTrackedSample_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTrackedSample_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTrackedSample_Release(This) (This)->lpVtbl->Release(This)
/*** IMFTrackedSample methods ***/
#define IMFTrackedSample_SetAllocator(This,pSampleAllocator,pUnkState) (This)->lpVtbl->SetAllocator(This,pSampleAllocator,pUnkState)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFTrackedSample_QueryInterface(IMFTrackedSample* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFTrackedSample_AddRef(IMFTrackedSample* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFTrackedSample_Release(IMFTrackedSample* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFTrackedSample methods ***/
static inline HRESULT IMFTrackedSample_SetAllocator(IMFTrackedSample* This,IMFAsyncCallback *pSampleAllocator,IUnknown *pUnkState) {
    return This->lpVtbl->SetAllocator(This,pSampleAllocator,pUnkState);
}
#endif
#endif

#endif


#endif  /* __IMFTrackedSample_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFVideoMixerControl interface
 */
#ifndef __IMFVideoMixerControl_INTERFACE_DEFINED__
#define __IMFVideoMixerControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoMixerControl, 0xa5c6c53f, 0xc202, 0x4aa5, 0x96,0x95, 0x17,0x5b,0xa8,0xc5,0x08,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a5c6c53f-c202-4aa5-9695-175ba8c508a5")
IMFVideoMixerControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetStreamZOrder(
        DWORD dwStreamID,
        DWORD dwZ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamZOrder(
        DWORD dwStreamID,
        DWORD *pdwZ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamOutputRect(
        DWORD dwStreamID,
        const MFVideoNormalizedRect *pnrcOutput) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamOutputRect(
        DWORD dwStreamID,
        MFVideoNormalizedRect *pnrcOutput) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoMixerControl, 0xa5c6c53f, 0xc202, 0x4aa5, 0x96,0x95, 0x17,0x5b,0xa8,0xc5,0x08,0xa5)
#endif
#else
typedef struct IMFVideoMixerControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoMixerControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoMixerControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoMixerControl *This);

    /*** IMFVideoMixerControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SetStreamZOrder)(
        IMFVideoMixerControl *This,
        DWORD dwStreamID,
        DWORD dwZ);

    HRESULT (STDMETHODCALLTYPE *GetStreamZOrder)(
        IMFVideoMixerControl *This,
        DWORD dwStreamID,
        DWORD *pdwZ);

    HRESULT (STDMETHODCALLTYPE *SetStreamOutputRect)(
        IMFVideoMixerControl *This,
        DWORD dwStreamID,
        const MFVideoNormalizedRect *pnrcOutput);

    HRESULT (STDMETHODCALLTYPE *GetStreamOutputRect)(
        IMFVideoMixerControl *This,
        DWORD dwStreamID,
        MFVideoNormalizedRect *pnrcOutput);

    END_INTERFACE
} IMFVideoMixerControlVtbl;

interface IMFVideoMixerControl {
    CONST_VTBL IMFVideoMixerControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoMixerControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoMixerControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoMixerControl_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoMixerControl methods ***/
#define IMFVideoMixerControl_SetStreamZOrder(This,dwStreamID,dwZ) (This)->lpVtbl->SetStreamZOrder(This,dwStreamID,dwZ)
#define IMFVideoMixerControl_GetStreamZOrder(This,dwStreamID,pdwZ) (This)->lpVtbl->GetStreamZOrder(This,dwStreamID,pdwZ)
#define IMFVideoMixerControl_SetStreamOutputRect(This,dwStreamID,pnrcOutput) (This)->lpVtbl->SetStreamOutputRect(This,dwStreamID,pnrcOutput)
#define IMFVideoMixerControl_GetStreamOutputRect(This,dwStreamID,pnrcOutput) (This)->lpVtbl->GetStreamOutputRect(This,dwStreamID,pnrcOutput)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoMixerControl_QueryInterface(IMFVideoMixerControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoMixerControl_AddRef(IMFVideoMixerControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoMixerControl_Release(IMFVideoMixerControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoMixerControl methods ***/
static inline HRESULT IMFVideoMixerControl_SetStreamZOrder(IMFVideoMixerControl* This,DWORD dwStreamID,DWORD dwZ) {
    return This->lpVtbl->SetStreamZOrder(This,dwStreamID,dwZ);
}
static inline HRESULT IMFVideoMixerControl_GetStreamZOrder(IMFVideoMixerControl* This,DWORD dwStreamID,DWORD *pdwZ) {
    return This->lpVtbl->GetStreamZOrder(This,dwStreamID,pdwZ);
}
static inline HRESULT IMFVideoMixerControl_SetStreamOutputRect(IMFVideoMixerControl* This,DWORD dwStreamID,const MFVideoNormalizedRect *pnrcOutput) {
    return This->lpVtbl->SetStreamOutputRect(This,dwStreamID,pnrcOutput);
}
static inline HRESULT IMFVideoMixerControl_GetStreamOutputRect(IMFVideoMixerControl* This,DWORD dwStreamID,MFVideoNormalizedRect *pnrcOutput) {
    return This->lpVtbl->GetStreamOutputRect(This,dwStreamID,pnrcOutput);
}
#endif
#endif

#endif


#endif  /* __IMFVideoMixerControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFVideoMixerControl2 interface
 */
#ifndef __IMFVideoMixerControl2_INTERFACE_DEFINED__
#define __IMFVideoMixerControl2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoMixerControl2, 0x8459616d, 0x966e, 0x4930, 0xb6,0x58, 0x54,0xfa,0x7e,0x5a,0x16,0xd3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8459616d-966e-4930-b658-54fa7e5a16d3")
IMFVideoMixerControl2 : public IMFVideoMixerControl
{
    virtual HRESULT STDMETHODCALLTYPE SetMixingPrefs(
        DWORD dwMixFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMixingPrefs(
        DWORD *pdwMixFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoMixerControl2, 0x8459616d, 0x966e, 0x4930, 0xb6,0x58, 0x54,0xfa,0x7e,0x5a,0x16,0xd3)
#endif
#else
typedef struct IMFVideoMixerControl2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoMixerControl2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoMixerControl2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoMixerControl2 *This);

    /*** IMFVideoMixerControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SetStreamZOrder)(
        IMFVideoMixerControl2 *This,
        DWORD dwStreamID,
        DWORD dwZ);

    HRESULT (STDMETHODCALLTYPE *GetStreamZOrder)(
        IMFVideoMixerControl2 *This,
        DWORD dwStreamID,
        DWORD *pdwZ);

    HRESULT (STDMETHODCALLTYPE *SetStreamOutputRect)(
        IMFVideoMixerControl2 *This,
        DWORD dwStreamID,
        const MFVideoNormalizedRect *pnrcOutput);

    HRESULT (STDMETHODCALLTYPE *GetStreamOutputRect)(
        IMFVideoMixerControl2 *This,
        DWORD dwStreamID,
        MFVideoNormalizedRect *pnrcOutput);

    /*** IMFVideoMixerControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMixingPrefs)(
        IMFVideoMixerControl2 *This,
        DWORD dwMixFlags);

    HRESULT (STDMETHODCALLTYPE *GetMixingPrefs)(
        IMFVideoMixerControl2 *This,
        DWORD *pdwMixFlags);

    END_INTERFACE
} IMFVideoMixerControl2Vtbl;

interface IMFVideoMixerControl2 {
    CONST_VTBL IMFVideoMixerControl2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoMixerControl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoMixerControl2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoMixerControl2_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoMixerControl methods ***/
#define IMFVideoMixerControl2_SetStreamZOrder(This,dwStreamID,dwZ) (This)->lpVtbl->SetStreamZOrder(This,dwStreamID,dwZ)
#define IMFVideoMixerControl2_GetStreamZOrder(This,dwStreamID,pdwZ) (This)->lpVtbl->GetStreamZOrder(This,dwStreamID,pdwZ)
#define IMFVideoMixerControl2_SetStreamOutputRect(This,dwStreamID,pnrcOutput) (This)->lpVtbl->SetStreamOutputRect(This,dwStreamID,pnrcOutput)
#define IMFVideoMixerControl2_GetStreamOutputRect(This,dwStreamID,pnrcOutput) (This)->lpVtbl->GetStreamOutputRect(This,dwStreamID,pnrcOutput)
/*** IMFVideoMixerControl2 methods ***/
#define IMFVideoMixerControl2_SetMixingPrefs(This,dwMixFlags) (This)->lpVtbl->SetMixingPrefs(This,dwMixFlags)
#define IMFVideoMixerControl2_GetMixingPrefs(This,pdwMixFlags) (This)->lpVtbl->GetMixingPrefs(This,pdwMixFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoMixerControl2_QueryInterface(IMFVideoMixerControl2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoMixerControl2_AddRef(IMFVideoMixerControl2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoMixerControl2_Release(IMFVideoMixerControl2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoMixerControl methods ***/
static inline HRESULT IMFVideoMixerControl2_SetStreamZOrder(IMFVideoMixerControl2* This,DWORD dwStreamID,DWORD dwZ) {
    return This->lpVtbl->SetStreamZOrder(This,dwStreamID,dwZ);
}
static inline HRESULT IMFVideoMixerControl2_GetStreamZOrder(IMFVideoMixerControl2* This,DWORD dwStreamID,DWORD *pdwZ) {
    return This->lpVtbl->GetStreamZOrder(This,dwStreamID,pdwZ);
}
static inline HRESULT IMFVideoMixerControl2_SetStreamOutputRect(IMFVideoMixerControl2* This,DWORD dwStreamID,const MFVideoNormalizedRect *pnrcOutput) {
    return This->lpVtbl->SetStreamOutputRect(This,dwStreamID,pnrcOutput);
}
static inline HRESULT IMFVideoMixerControl2_GetStreamOutputRect(IMFVideoMixerControl2* This,DWORD dwStreamID,MFVideoNormalizedRect *pnrcOutput) {
    return This->lpVtbl->GetStreamOutputRect(This,dwStreamID,pnrcOutput);
}
/*** IMFVideoMixerControl2 methods ***/
static inline HRESULT IMFVideoMixerControl2_SetMixingPrefs(IMFVideoMixerControl2* This,DWORD dwMixFlags) {
    return This->lpVtbl->SetMixingPrefs(This,dwMixFlags);
}
static inline HRESULT IMFVideoMixerControl2_GetMixingPrefs(IMFVideoMixerControl2* This,DWORD *pdwMixFlags) {
    return This->lpVtbl->GetMixingPrefs(This,pdwMixFlags);
}
#endif
#endif

#endif


#endif  /* __IMFVideoMixerControl2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFVideoRenderer interface
 */
#ifndef __IMFVideoRenderer_INTERFACE_DEFINED__
#define __IMFVideoRenderer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoRenderer, 0xdfdfd197, 0xa9ca, 0x43d8, 0xb3,0x41, 0x6a,0xf3,0x50,0x37,0x92,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dfdfd197-a9ca-43d8-b341-6af3503792cd")
IMFVideoRenderer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitializeRenderer(
        IMFTransform *pVideoMixer,
        IMFVideoPresenter *pVideoPresenter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoRenderer, 0xdfdfd197, 0xa9ca, 0x43d8, 0xb3,0x41, 0x6a,0xf3,0x50,0x37,0x92,0xcd)
#endif
#else
typedef struct IMFVideoRendererVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoRenderer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoRenderer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoRenderer *This);

    /*** IMFVideoRenderer methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializeRenderer)(
        IMFVideoRenderer *This,
        IMFTransform *pVideoMixer,
        IMFVideoPresenter *pVideoPresenter);

    END_INTERFACE
} IMFVideoRendererVtbl;

interface IMFVideoRenderer {
    CONST_VTBL IMFVideoRendererVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoRenderer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoRenderer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoRenderer_Release(This) (This)->lpVtbl->Release(This)
/*** IMFVideoRenderer methods ***/
#define IMFVideoRenderer_InitializeRenderer(This,pVideoMixer,pVideoPresenter) (This)->lpVtbl->InitializeRenderer(This,pVideoMixer,pVideoPresenter)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoRenderer_QueryInterface(IMFVideoRenderer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoRenderer_AddRef(IMFVideoRenderer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoRenderer_Release(IMFVideoRenderer* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFVideoRenderer methods ***/
static inline HRESULT IMFVideoRenderer_InitializeRenderer(IMFVideoRenderer* This,IMFTransform *pVideoMixer,IMFVideoPresenter *pVideoPresenter) {
    return This->lpVtbl->InitializeRenderer(This,pVideoMixer,pVideoPresenter);
}
#endif
#endif

#endif


#endif  /* __IMFVideoRenderer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEVRFilterConfig interface
 */
#ifndef __IEVRFilterConfig_INTERFACE_DEFINED__
#define __IEVRFilterConfig_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEVRFilterConfig, 0x83e91e85, 0x82c1, 0x4ea7, 0x80,0x1d, 0x85,0xdc,0x50,0xb7,0x50,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83e91e85-82c1-4ea7-801d-85dc50b75086")
IEVRFilterConfig : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetNumberOfStreams(
        DWORD dwMaxStreams) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberOfStreams(
        DWORD *pdwMaxStreams) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEVRFilterConfig, 0x83e91e85, 0x82c1, 0x4ea7, 0x80,0x1d, 0x85,0xdc,0x50,0xb7,0x50,0x86)
#endif
#else
typedef struct IEVRFilterConfigVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEVRFilterConfig *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEVRFilterConfig *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEVRFilterConfig *This);

    /*** IEVRFilterConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *SetNumberOfStreams)(
        IEVRFilterConfig *This,
        DWORD dwMaxStreams);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfStreams)(
        IEVRFilterConfig *This,
        DWORD *pdwMaxStreams);

    END_INTERFACE
} IEVRFilterConfigVtbl;

interface IEVRFilterConfig {
    CONST_VTBL IEVRFilterConfigVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEVRFilterConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEVRFilterConfig_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEVRFilterConfig_Release(This) (This)->lpVtbl->Release(This)
/*** IEVRFilterConfig methods ***/
#define IEVRFilterConfig_SetNumberOfStreams(This,dwMaxStreams) (This)->lpVtbl->SetNumberOfStreams(This,dwMaxStreams)
#define IEVRFilterConfig_GetNumberOfStreams(This,pdwMaxStreams) (This)->lpVtbl->GetNumberOfStreams(This,pdwMaxStreams)
#else
/*** IUnknown methods ***/
static inline HRESULT IEVRFilterConfig_QueryInterface(IEVRFilterConfig* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEVRFilterConfig_AddRef(IEVRFilterConfig* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEVRFilterConfig_Release(IEVRFilterConfig* This) {
    return This->lpVtbl->Release(This);
}
/*** IEVRFilterConfig methods ***/
static inline HRESULT IEVRFilterConfig_SetNumberOfStreams(IEVRFilterConfig* This,DWORD dwMaxStreams) {
    return This->lpVtbl->SetNumberOfStreams(This,dwMaxStreams);
}
static inline HRESULT IEVRFilterConfig_GetNumberOfStreams(IEVRFilterConfig* This,DWORD *pdwMaxStreams) {
    return This->lpVtbl->GetNumberOfStreams(This,pdwMaxStreams);
}
#endif
#endif

#endif


#endif  /* __IEVRFilterConfig_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEVRFilterConfigEx interface
 */
#ifndef __IEVRFilterConfigEx_INTERFACE_DEFINED__
#define __IEVRFilterConfigEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEVRFilterConfigEx, 0xaea36028, 0x796d, 0x454f, 0xbe,0xee, 0xb4,0x80,0x71,0xe2,0x43,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aea36028-796d-454f-beee-b48071e24304")
IEVRFilterConfigEx : public IEVRFilterConfig
{
    virtual HRESULT STDMETHODCALLTYPE SetConfigPrefs(
        DWORD dwConfigFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConfigPrefs(
        DWORD *pdwConfigFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEVRFilterConfigEx, 0xaea36028, 0x796d, 0x454f, 0xbe,0xee, 0xb4,0x80,0x71,0xe2,0x43,0x04)
#endif
#else
typedef struct IEVRFilterConfigExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEVRFilterConfigEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEVRFilterConfigEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEVRFilterConfigEx *This);

    /*** IEVRFilterConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *SetNumberOfStreams)(
        IEVRFilterConfigEx *This,
        DWORD dwMaxStreams);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfStreams)(
        IEVRFilterConfigEx *This,
        DWORD *pdwMaxStreams);

    /*** IEVRFilterConfigEx methods ***/
    HRESULT (STDMETHODCALLTYPE *SetConfigPrefs)(
        IEVRFilterConfigEx *This,
        DWORD dwConfigFlags);

    HRESULT (STDMETHODCALLTYPE *GetConfigPrefs)(
        IEVRFilterConfigEx *This,
        DWORD *pdwConfigFlags);

    END_INTERFACE
} IEVRFilterConfigExVtbl;

interface IEVRFilterConfigEx {
    CONST_VTBL IEVRFilterConfigExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEVRFilterConfigEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEVRFilterConfigEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEVRFilterConfigEx_Release(This) (This)->lpVtbl->Release(This)
/*** IEVRFilterConfig methods ***/
#define IEVRFilterConfigEx_SetNumberOfStreams(This,dwMaxStreams) (This)->lpVtbl->SetNumberOfStreams(This,dwMaxStreams)
#define IEVRFilterConfigEx_GetNumberOfStreams(This,pdwMaxStreams) (This)->lpVtbl->GetNumberOfStreams(This,pdwMaxStreams)
/*** IEVRFilterConfigEx methods ***/
#define IEVRFilterConfigEx_SetConfigPrefs(This,dwConfigFlags) (This)->lpVtbl->SetConfigPrefs(This,dwConfigFlags)
#define IEVRFilterConfigEx_GetConfigPrefs(This,pdwConfigFlags) (This)->lpVtbl->GetConfigPrefs(This,pdwConfigFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IEVRFilterConfigEx_QueryInterface(IEVRFilterConfigEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEVRFilterConfigEx_AddRef(IEVRFilterConfigEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEVRFilterConfigEx_Release(IEVRFilterConfigEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IEVRFilterConfig methods ***/
static inline HRESULT IEVRFilterConfigEx_SetNumberOfStreams(IEVRFilterConfigEx* This,DWORD dwMaxStreams) {
    return This->lpVtbl->SetNumberOfStreams(This,dwMaxStreams);
}
static inline HRESULT IEVRFilterConfigEx_GetNumberOfStreams(IEVRFilterConfigEx* This,DWORD *pdwMaxStreams) {
    return This->lpVtbl->GetNumberOfStreams(This,pdwMaxStreams);
}
/*** IEVRFilterConfigEx methods ***/
static inline HRESULT IEVRFilterConfigEx_SetConfigPrefs(IEVRFilterConfigEx* This,DWORD dwConfigFlags) {
    return This->lpVtbl->SetConfigPrefs(This,dwConfigFlags);
}
static inline HRESULT IEVRFilterConfigEx_GetConfigPrefs(IEVRFilterConfigEx* This,DWORD *pdwConfigFlags) {
    return This->lpVtbl->GetConfigPrefs(This,pdwConfigFlags);
}
#endif
#endif

#endif


#endif  /* __IEVRFilterConfigEx_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFTopologyServiceLookup interface
 */
#ifndef __IMFTopologyServiceLookup_INTERFACE_DEFINED__
#define __IMFTopologyServiceLookup_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTopologyServiceLookup, 0xfa993889, 0x4383, 0x415a, 0xa9,0x30, 0xdd,0x47,0x2a,0x8c,0xf6,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa993889-4383-415a-a930-dd472a8cf6f7")
IMFTopologyServiceLookup : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LookupService(
        MF_SERVICE_LOOKUP_TYPE Type,
        DWORD dwIndex,
        REFGUID guidService,
        REFIID riid,
        LPVOID *ppvObjects,
        DWORD *pnObjects) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTopologyServiceLookup, 0xfa993889, 0x4383, 0x415a, 0xa9,0x30, 0xdd,0x47,0x2a,0x8c,0xf6,0xf7)
#endif
#else
typedef struct IMFTopologyServiceLookupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTopologyServiceLookup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTopologyServiceLookup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTopologyServiceLookup *This);

    /*** IMFTopologyServiceLookup methods ***/
    HRESULT (STDMETHODCALLTYPE *LookupService)(
        IMFTopologyServiceLookup *This,
        MF_SERVICE_LOOKUP_TYPE Type,
        DWORD dwIndex,
        REFGUID guidService,
        REFIID riid,
        LPVOID *ppvObjects,
        DWORD *pnObjects);

    END_INTERFACE
} IMFTopologyServiceLookupVtbl;

interface IMFTopologyServiceLookup {
    CONST_VTBL IMFTopologyServiceLookupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTopologyServiceLookup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTopologyServiceLookup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTopologyServiceLookup_Release(This) (This)->lpVtbl->Release(This)
/*** IMFTopologyServiceLookup methods ***/
#define IMFTopologyServiceLookup_LookupService(This,Type,dwIndex,guidService,riid,ppvObjects,pnObjects) (This)->lpVtbl->LookupService(This,Type,dwIndex,guidService,riid,ppvObjects,pnObjects)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFTopologyServiceLookup_QueryInterface(IMFTopologyServiceLookup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFTopologyServiceLookup_AddRef(IMFTopologyServiceLookup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFTopologyServiceLookup_Release(IMFTopologyServiceLookup* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFTopologyServiceLookup methods ***/
static inline HRESULT IMFTopologyServiceLookup_LookupService(IMFTopologyServiceLookup* This,MF_SERVICE_LOOKUP_TYPE Type,DWORD dwIndex,REFGUID guidService,REFIID riid,LPVOID *ppvObjects,DWORD *pnObjects) {
    return This->lpVtbl->LookupService(This,Type,dwIndex,guidService,riid,ppvObjects,pnObjects);
}
#endif
#endif

#endif


#endif  /* __IMFTopologyServiceLookup_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFTopologyServiceLookupClient interface
 */
#ifndef __IMFTopologyServiceLookupClient_INTERFACE_DEFINED__
#define __IMFTopologyServiceLookupClient_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFTopologyServiceLookupClient, 0xfa99388a, 0x4383, 0x415a, 0xa9,0x30, 0xdd,0x47,0x2a,0x8c,0xf6,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa99388a-4383-415a-a930-dd472a8cf6f7")
IMFTopologyServiceLookupClient : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitServicePointers(
        IMFTopologyServiceLookup *pLookup) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseServicePointers(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFTopologyServiceLookupClient, 0xfa99388a, 0x4383, 0x415a, 0xa9,0x30, 0xdd,0x47,0x2a,0x8c,0xf6,0xf7)
#endif
#else
typedef struct IMFTopologyServiceLookupClientVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFTopologyServiceLookupClient *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFTopologyServiceLookupClient *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFTopologyServiceLookupClient *This);

    /*** IMFTopologyServiceLookupClient methods ***/
    HRESULT (STDMETHODCALLTYPE *InitServicePointers)(
        IMFTopologyServiceLookupClient *This,
        IMFTopologyServiceLookup *pLookup);

    HRESULT (STDMETHODCALLTYPE *ReleaseServicePointers)(
        IMFTopologyServiceLookupClient *This);

    END_INTERFACE
} IMFTopologyServiceLookupClientVtbl;

interface IMFTopologyServiceLookupClient {
    CONST_VTBL IMFTopologyServiceLookupClientVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFTopologyServiceLookupClient_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFTopologyServiceLookupClient_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFTopologyServiceLookupClient_Release(This) (This)->lpVtbl->Release(This)
/*** IMFTopologyServiceLookupClient methods ***/
#define IMFTopologyServiceLookupClient_InitServicePointers(This,pLookup) (This)->lpVtbl->InitServicePointers(This,pLookup)
#define IMFTopologyServiceLookupClient_ReleaseServicePointers(This) (This)->lpVtbl->ReleaseServicePointers(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFTopologyServiceLookupClient_QueryInterface(IMFTopologyServiceLookupClient* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFTopologyServiceLookupClient_AddRef(IMFTopologyServiceLookupClient* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFTopologyServiceLookupClient_Release(IMFTopologyServiceLookupClient* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFTopologyServiceLookupClient methods ***/
static inline HRESULT IMFTopologyServiceLookupClient_InitServicePointers(IMFTopologyServiceLookupClient* This,IMFTopologyServiceLookup *pLookup) {
    return This->lpVtbl->InitServicePointers(This,pLookup);
}
static inline HRESULT IMFTopologyServiceLookupClient_ReleaseServicePointers(IMFTopologyServiceLookupClient* This) {
    return This->lpVtbl->ReleaseServicePointers(This);
}
#endif
#endif

#endif


#endif  /* __IMFTopologyServiceLookupClient_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEVRTrustedVideoPlugin interface
 */
#ifndef __IEVRTrustedVideoPlugin_INTERFACE_DEFINED__
#define __IEVRTrustedVideoPlugin_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEVRTrustedVideoPlugin, 0x83a4ce40, 0x7710, 0x494b, 0xa8,0x93, 0xa4,0x72,0x04,0x9a,0xf6,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83a4ce40-7710-494b-a893-a472049af630")
IEVRTrustedVideoPlugin : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsInTrustedVideoMode(
        WINBOOL *pYes) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanConstrict(
        WINBOOL *pYes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConstriction(
        DWORD dwKPix) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableImageExport(
        WINBOOL bDisable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEVRTrustedVideoPlugin, 0x83a4ce40, 0x7710, 0x494b, 0xa8,0x93, 0xa4,0x72,0x04,0x9a,0xf6,0x30)
#endif
#else
typedef struct IEVRTrustedVideoPluginVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEVRTrustedVideoPlugin *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEVRTrustedVideoPlugin *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEVRTrustedVideoPlugin *This);

    /*** IEVRTrustedVideoPlugin methods ***/
    HRESULT (STDMETHODCALLTYPE *IsInTrustedVideoMode)(
        IEVRTrustedVideoPlugin *This,
        WINBOOL *pYes);

    HRESULT (STDMETHODCALLTYPE *CanConstrict)(
        IEVRTrustedVideoPlugin *This,
        WINBOOL *pYes);

    HRESULT (STDMETHODCALLTYPE *SetConstriction)(
        IEVRTrustedVideoPlugin *This,
        DWORD dwKPix);

    HRESULT (STDMETHODCALLTYPE *DisableImageExport)(
        IEVRTrustedVideoPlugin *This,
        WINBOOL bDisable);

    END_INTERFACE
} IEVRTrustedVideoPluginVtbl;

interface IEVRTrustedVideoPlugin {
    CONST_VTBL IEVRTrustedVideoPluginVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEVRTrustedVideoPlugin_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEVRTrustedVideoPlugin_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEVRTrustedVideoPlugin_Release(This) (This)->lpVtbl->Release(This)
/*** IEVRTrustedVideoPlugin methods ***/
#define IEVRTrustedVideoPlugin_IsInTrustedVideoMode(This,pYes) (This)->lpVtbl->IsInTrustedVideoMode(This,pYes)
#define IEVRTrustedVideoPlugin_CanConstrict(This,pYes) (This)->lpVtbl->CanConstrict(This,pYes)
#define IEVRTrustedVideoPlugin_SetConstriction(This,dwKPix) (This)->lpVtbl->SetConstriction(This,dwKPix)
#define IEVRTrustedVideoPlugin_DisableImageExport(This,bDisable) (This)->lpVtbl->DisableImageExport(This,bDisable)
#else
/*** IUnknown methods ***/
static inline HRESULT IEVRTrustedVideoPlugin_QueryInterface(IEVRTrustedVideoPlugin* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEVRTrustedVideoPlugin_AddRef(IEVRTrustedVideoPlugin* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEVRTrustedVideoPlugin_Release(IEVRTrustedVideoPlugin* This) {
    return This->lpVtbl->Release(This);
}
/*** IEVRTrustedVideoPlugin methods ***/
static inline HRESULT IEVRTrustedVideoPlugin_IsInTrustedVideoMode(IEVRTrustedVideoPlugin* This,WINBOOL *pYes) {
    return This->lpVtbl->IsInTrustedVideoMode(This,pYes);
}
static inline HRESULT IEVRTrustedVideoPlugin_CanConstrict(IEVRTrustedVideoPlugin* This,WINBOOL *pYes) {
    return This->lpVtbl->CanConstrict(This,pYes);
}
static inline HRESULT IEVRTrustedVideoPlugin_SetConstriction(IEVRTrustedVideoPlugin* This,DWORD dwKPix) {
    return This->lpVtbl->SetConstriction(This,dwKPix);
}
static inline HRESULT IEVRTrustedVideoPlugin_DisableImageExport(IEVRTrustedVideoPlugin* This,WINBOOL bDisable) {
    return This->lpVtbl->DisableImageExport(This,bDisable);
}
#endif
#endif

#endif


#endif  /* __IEVRTrustedVideoPlugin_INTERFACE_DEFINED__ */


HRESULT WINAPI MFCreateVideoPresenter(IUnknown *pOwner, REFIID riidDevice, REFIID riid, void **ppVideoPresenter);
HRESULT WINAPI MFCreateVideoMixer(IUnknown *pOwner, REFIID riidDevice, REFIID riid, void **ppv);
HRESULT WINAPI MFCreateVideoMixerAndPresenter(IUnknown *pMixerOwner, IUnknown *pPresenterOwner, REFIID riidMixer, void **ppvVideoMixer, REFIID riidPresenter, void **ppvVideoPresenter);
HRESULT WINAPI MFCreateVideoRenderer( REFIID riidRenderer, void **ppVideoRenderer );
HRESULT WINAPI MFCreateVideoSampleFromSurface(IUnknown *pUnkSurface, IMFSample **ppSample);
HRESULT WINAPI MFCreateVideoSampleAllocator(REFIID riid, void **ppSampleAllocator);
#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __evr_h__ */
